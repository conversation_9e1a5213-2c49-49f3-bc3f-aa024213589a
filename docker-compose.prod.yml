version: '3.1'
services:
  upay-gateway:
    build: .
    image: jfrog.wosai-inc.com/docker-virtual-staging/upay-gateway:$tag
    restart: always
    network_mode: host
    container_name: upay-gateway
    hostname: "{{ inventory_hostname }}-gateway"
    environment:
      - JAVA_OPTIONS=-Xms4g -Xmx9g -XX:+UnlockExperimentalVMOptions -XX:+UseZGC -XX:ConcGCThreads=4 -XX:ParallelGCThreads=8 -XX:ZCollectionInterval=30 -XX:ZAllocationSpikeTolerance=5 -XX:+UnlockDiagnosticVMOptions -XX:-ZProactive -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=/var/lib/jetty/logs/gc.log:time -XX:+ParallelRefProcEnabled -Djetty.port=19801 -Djavax.net.ssl.sessionCacheSize=1000 {% if not inventory_hostname.startswith('sz')  %} -Dshouqianba.flavor=prod -Dshouqianba.region=hangzhou {% else %}  -Dshouqianba.flavor=prodsz -Dshouqianba.region=shenzhen {% endif %} -Denv=PRO -Djetty.threadPool.maxThreads=500 -Djetty.http.acceptors=4 -Djetty.http.selectors=8 -Djetty.http.idleTimeout=3000 --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.net.util=ALL-UNNAMED -Dhera.meta.host_name=hera-meta.vpc.shouqianba.com -Dhera.meta.http_port=80 -Dhera.meta.grpc_port=8082 -Dhera.jaeger.collector_endpoint=http://hera-otel-collector.vpc.shouqianba.com/api/traces -javaagent:/app/hera-agent/agent/hera-agent.jar=agent.service_name=upay-gateway -Dskywalking.plugin.kafka.enable_inject_kafka_header=false -Dhera.jaeger.baggage_items=fake -DlogDir=/var/lib/jetty/logs
    extra_hosts:
      - "rmq.internal.shouqianba.com:***********"
      - "public-confluent-001:*************"
      - "public-confluent-002:*************"
      - "public-confluent-003:*************"
      - "public-confluent-004:************"
      - "public-confluent-005:************"

    {% if inventory_hostname.startswith('sz')  %}

      - "zipkin-kafka-001.shouqianba.com:************"
      - "zipkin-kafka-002.shouqianba.com:**************"
      - "zipkin-kafka-003.shouqianba.com:*************"
      - "grandet.internal.shouqianba.com:**************"

    {% endif %}

    volumes:
      - /app/log/upay-gateway:/var/lib/jetty/logs
      - /etc/resolv.conf:/etc/resolv.conf
      - /opt/data:/opt/data
      - /opt/settings:/opt/settings