<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <property resource="logback-${shouqianba.flavor:-default}.properties"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <turboFilter class="com.wosai.upay.helper.TraceLogFilter"/>

    <!-- To enable JMX Management -->
    <jmxConfigurator/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder  class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
        <encoder  class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <target>System.err</target>
    </appender>

    <!-- ROOT appender -->
    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir:-.}/upay-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <immediateFlush>false</immediateFlush>
        </encoder>
    </appender>


    <appender name="workflowFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir:-.}/workflow-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <immediateFlush>false</immediateFlush>
        </encoder>
    </appender>
    
    <appender name="asyncFileAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="fileAppender" />
        <queueSize>163840</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
        <maxFlushTime>5000</maxFlushTime>
        <neverBlock>false</neverBlock>
    </appender>

    <appender name="asyncWorkflowFileAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="workflowFileAppender" />
        <queueSize>163840</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
        <maxFlushTime>5000</maxFlushTime>
        <neverBlock>false</neverBlock>
    </appender>

    
    <appender name="amqpFacadeKafkaLogAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir:-.}/kafka-facade-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder  class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <immediateFlush>false</immediateFlush>
        </encoder>
    </appender>

    <appender name="notifyClientLogAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir:-.}/notify-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder  class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <immediateFlush>false</immediateFlush>
        </encoder>
    </appender>



    <root level="info">
        <appender-ref ref="${rootAppender}"/>
    </root>

    <logger name="com.wosai" level="trace"/>

    <logger name="com.wosai.springmvc.support.NullableRequestResponseBodyMethodProcessor" level="info"/>
    <logger name="com.wosai.upay.helper.UpayServiceMethodInterceptor" additivity="false" level="trace">
        <appender-ref ref="fileAppender"/>
    </logger>
    
    <logger name="com.wosai.upay.service.AmqpFacade.Kafka" additivity="false" level="trace">
    	<appender-ref ref="amqpFacadeKafkaLogAppender"/>
    </logger>

    <logger name="com.wosai.upay.service.ClientNotifier" additivity="false" level="trace">
        <appender-ref ref="notifyClientLogAppender"/>
    </logger>

    <logger name="com.wosai.upay.workflow" additivity="false" level="trace">
        <appender-ref ref="asyncWorkflowFileAppender"/>
    </logger>
    
    <logger name="com.wosai.mpay.api" additivity="false" level="trace">
        <appender-ref ref="asyncWorkflowFileAppender"/>
    </logger>
    
    <logger name="com.wosai.mpay.util" additivity="false" level="trace">
        <appender-ref ref="asyncWorkflowFileAppender"/>
    </logger>
    
    <logger name="com.wosai.upay.util.UpayScheduledExecutorService" additivity="false" level="trace">
        <appender-ref ref="asyncWorkflowFileAppender"/>
    </logger>
</configuration>
