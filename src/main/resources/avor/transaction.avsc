{"name": "Transaction", "type": "record", "doc": "Auto-generated Avro schema for transaction. Generated at Mar 20, 2018 08:25:13 PM CST", "namespace": "com.wosai.upay.model.kafka", "meta": "网关消息格式", "fields": [{"name": "id", "type": ["null", "string"], "meta": ""}, {"name": "tsn", "type": ["null", "string"], "meta": "交易流水号。"}, {"name": "client_tsn", "type": ["null", "string"], "meta": "商户的流水号。支付流水的client_tsn等于订单的client_sn。退款流水的client_tsn等于client_sn加商户的退款请求编号。"}, {"name": "type", "type": ["null", "int"], "meta": "交易类型\n30: 付款\n10: 取消\n11: 退款\n"}, {"name": "subject", "type": ["null", "string"], "meta": "标题"}, {"name": "body", "type": ["null", "string"], "meta": ""}, {"name": "status", "type": ["null", "int"], "meta": "状态 CREATED, IN_PROG, SUCESS, UNKNOWN, ERROR_RECOVERY, ERROR_CANCELED, ERROR_UNKNOWN"}, {"name": "effective_amount", "type": ["null", "long"], "meta": "向支付通道请求的金额 BIGINT"}, {"name": "original_amount", "type": ["null", "long"], "meta": ""}, {"name": "paid_amount", "type": ["null", "long"], "meta": "消费者实际支付金额"}, {"name": "received_amount", "type": ["null", "long"], "meta": "商户实际收款金额"}, {"name": "items", "type": ["null", "bytes"], "meta": "如果type是支付，这个字段记录购买的商品明细。如果type是退款，这个字段记录退掉的商品明细。这个字段可以用于统计一段时间内商品的净销量。"}, {"name": "buyer_uid", "type": ["null", "string"], "meta": "付款人在支付服务商的用户ID"}, {"name": "buyer_login", "type": ["null", "string"], "meta": "付款人在支付服务商的登录账号"}, {"name": "merchant_id", "type": ["null", "string"], "meta": ""}, {"name": "store_id", "type": ["null", "string"], "meta": "商户ID 商户记录UUID"}, {"name": "terminal_id", "type": ["null", "string"], "meta": "终端ID 终端记录UUID"}, {"name": "operator", "type": ["null", "string"], "meta": "操作员姓名或其它自定义ID"}, {"name": "order_sn", "type": ["null", "string"], "meta": "原始订单号"}, {"name": "order_id", "type": ["null", "string"], "meta": "原始订单ID（uuid）"}, {"name": "provider", "type": ["null", "int"], "meta": "支付通道 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉"}, {"name": "payway", "type": ["null", "int"], "meta": "支付服务商\n1: alipay\n3: <PERSON><PERSON><PERSON>\n4: b<PERSON><PERSON><PERSON>\n5: jd<PERSON>et\n"}, {"name": "sub_payway", "type": ["null", "int"], "meta": "支付方式\n1: BARCODE\n2: QRCODE\n3: WAP"}, {"name": "trade_no", "type": ["null", "string"], "meta": "服务商返回的交易凭证号"}, {"name": "product_flag", "type": ["null", "string"], "meta": "产品来源：APP、SDK、POS"}, {"name": "extra_params", "type": ["null", "bytes"], "meta": "可选参数，包括\noperator － 操作员\npoi － 发生地点\nnotify_url － 回调URL\nremark － 备注\nbarcode - 条码\n"}, {"name": "extra_out_fields", "type": ["null", "bytes"], "meta": "可选的交易流水的返回字段（例如qrcode)"}, {"name": "extended_params", "type": ["null", "bytes"], "meta": "透传到支付通道的参数(operator, goods_details)，由商户和支付通道约定，我们不做解析"}, {"name": "reflect", "type": ["null", "bytes"], "meta": "商户上传的附加字段，保存在订单中。终端查询的时候原样返回。"}, {"name": "config_snapshot", "type": ["null", "bytes"], "meta": "配置参数快照，包括\nfee_rate 费率\nliquidation_next_day 是否二清\nweixin_trade_params\n     sub_mch_id 微信子商户号\n     goods_tag 参与优惠活动标识\nalipay_v1_trade_params\n     partner 收款商户\n     app_key\nalipay_v2_trade_params\n     app_id 收款商户\n     private_key\n     auth_token\nalipay_wap_tr /* comment truncated */ /*ade_params\n     partner\n     app_key\n     app_id\n     private_key\n     app_auth_token\nweixin_wap_trade_params\n*/"}, {"name": "finish_time", "type": ["null", "long"], "meta": "交易完成时间（收钱吧系统时间）"}, {"name": "channel_finish_time", "type": ["null", "long"], "meta": "交易实际完成时间（从支付通道获得）"}, {"name": "biz_error_code", "type": ["null", "bytes"], "meta": "业务错误码。收钱吧统一定义的业务错误码。"}, {"name": "provider_error_info", "type": ["null", "bytes"], "meta": "调用支付通道返回的错误信息\nprotocol_error_code  接入错误码\nsystem_error_code 系统错误码\nnetwork_error_message 网络异常消息文本\nbiz_error_code 业务错误码\nbiz_error_message 业务错误消息文本\n"}, {"name": "ctime", "type": ["null", "long"], "meta": "记录创建时间"}, {"name": "mtime", "type": ["null", "long"], "meta": "最近修改时间"}, {"name": "deleted", "type": ["boolean", "null"], "meta": "软删除标志"}, {"name": "version", "type": ["null", "long"], "meta": "版本号"}, {"name": "nfc_card", "type": ["null", "string"], "meta": "nfc交易，银行卡号"}]}