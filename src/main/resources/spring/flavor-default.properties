#部署区域
shouqianba.region=

#upay jdbc config
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=**************************************************************************************************************************************************************************************
jdbc.username=st_core_20ahg7yw
jdbc.password=iBtj)C2$X^hc*RfOJ7N5FgAVu@43
jdbc.connection.eviction.interval=60000


#ticket jdbc  config
jdbc1.driverClassName=com.mysql.jdbc.Driver
jdbc1.url=********************************************************************************************************************************************************************
jdbc1.username=st_core_gbk6tioh
jdbc1.password=kStsfWdnvTU7e)%EuNIj6w14c$Bg
jdbc1.connection.eviction.interval=60000

#orderSn prefix config
ordersn.prefix=78942,78943


#notify host
notify.host=https://upay-gateway.iwosai.com
psbc.notify.host=https://upay-gateway.iwosai.com

#专线网络的回调地址映射 {host : notifyHost}
notify.private.network.hosts={}

# gateways

######################################## gateway config start ###################################################

provider.alipay.intl.gateway.all=https://isupergw.alipaydev.com
provider.alipay.overseas.gateway.all=https://intlmapi.alipay.com/gateway.do
provider.alipay.v1.gateway.all=https://mapi.alipay.com/gateway.do
provider.alipay.v2.gateway.all=https://openapi.alipay.com/gateway.do
provider.alipay.wap.gateway.all=https://mapi.alipay.com/gateway.do
provider.alipay.wap.v2.gateway.all=https://openapi.alipay.com/gateway.do
provider.alipay.fitness.gateway.all=https://apigw.alipay-eco.com
provider.bestpay.gateway.all=https://webpaywg.bestpay.com.cn
provider.bestpay.v2.gateway.all=https://mapi.bestpay.com.cn
provider.chinaums.gateway.all=http://***********:29015
provider.chinaums.v1.gateway.all=https://api-mop.chinaums.com
provider.chinaums.epay.gateway.all=https://api-mop.chinaums.com
provider.cibbank.gateway.all=https://pay.swiftpass.cn/pay/gateway
provider.citicbank.gateway.all=https://pay.swiftpass.cn/pay/gateway
provider.cmcc.gateway.all=http://ipos.10086.cn/cps/cmpayService
provider.direct.unionpay.alipay.v2.gateway.all=https://apay.95516.com/ali/aligateway
provider.direct.unionpay.alipay.wap.v2.gateway.all=https://apay.95516.com/ali/aligateway
provider.direct.unionpay.weixin.gateway.all=https://tpay.95516.com
provider.direct.unionpay.weixin.wapOrMini.gateway.all=https://tpay.95516.com
provider.lkl.unionpay.alipay.v2.gateway.all=http://union-at-lakala-proxy-alipay.beta.iwosai.com/ali/aligateway
provider.lkl.unionpay.alipay.wap.v2.gateway.all=http://union-at-lakala-proxy-alipay.beta.iwosai.com/ali/aligateway
provider.lkl.unionpay.weixin.gateway.all=http://union-at-lakala-proxy-weixin.beta.iwosai.com
provider.lkl.unionpay.weixin.wapOrMini.gateway.all=http://union-at-lakala-proxy-weixin.beta.iwosai.com
provider.gift.card.gateway.all=http://giftcard-openapi.test.shouqianba.com/gateway.do
provider.lakala.gateway.all=https://api.lakala.com
provider.lakala.open.gateway.all=https://s2.lakala.com/labs/txn
provider.lakala.openv3.gateway.all=https://test.wsmsd.cn/sit/api/v3
provider.lakala.openv3.gateway.union.userId.query=https://test.wsmsd.cn/sit/api/v2/saas/query/wx_openid_query
provider.nucc.alipay.v2.gateway.all=https://***********:8213/gateway/alipay
provider.nucc.bestpay.gateway.all=https://***********:8213/gateway/standard
provider.nucc.weixin.gateway.all=http://***********:8213
provider.nucc.weixin.wapOrMini.gateway.all=http://***********:8213
provider.sodexo.gateway.all=https://uat-mt.sdxpass.com
provider.sodexo.gateway.oauth=https://uat-mt.sdxpass.com:8001
#sodexo
provider.sodexo.wap.gateway.all=https://uat-mt.sdxpass.com
provider.sodexo.wap.gateway.oauth=https://uat-mt.sdxpass.com:8001
provider.swiftpass.gateway.all=https://pay.swiftpass.cn/pay/gateway
provider.unionpay.alipay.v2.gateway.all=https://openapi.alipay.com/gateway.do
provider.unionpay.alipay.wap.v2.gateway.all=https://openapi.alipay.com/gateway.do
provider.unionpay.online.gateway.all=https://gateway.95516.com
provider.unionpay.open.gateway.all=https://partner.95516.com
provider.unionpay.weixin.gateway.all=https://tpay.test.95516.com
provider.unionpay.weixin.wapOrMini.gateway.all=https://tpay.test.95516.com
provider.weixin.gateway.all=https://api.mch.weixin.qq.com
provider.weixin.gateway.payFaceAuthInfo=https://payapp.weixin.qq.com/face/get_wxpayface_authinfo
provider.weixin.hk.gateway.all=https://mchapi.wechatpay.com.hk
provider.weixin.wapOrMini.gateway.all=https://api.mch.weixin.qq.com
provider.tl.unionpay.alipay.v2.gateway.all=https://isv-test.allinpay.com/trxapi/alipaytrx/pay
provider.tl.unionpay.alipay.wap.v2.gateway.all=https://isv-test.allinpay.com/trxapi/alipaytrx/pay
provider.tl.unionpay.weixin.gateway.all=https://isv-test.allinpay.com/trxapi/wxtrx
provider.tl.unionpay.weixin.wapOrMini.gateway.all=https://isv-test.allinpay.com/trxapi/wxtrx
provider.tl.unionpay.union.qrcode.gateway.all=https://isv-test.allinpay.com/trxapi/unionpaytrx/trx
provider.weixin.wapOrMini.v3.gateway.all=https://api.mch.weixin.qq.com/v3/payscore/serviceorder
provider.weixin.wapOrMini.v3.palm.gateway.all=https://api.mch.weixin.qq.com/v3/palmservice/service-order
provider.weixin.wapOrMini.v3.partner.gateway.all=https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder

provider.uepay.gateway.all=https://fat.uepay.mo/payment/gateway
provider.cmb.gateway.all=https://api.cmburl.cn:8065/polypay/v1.0/mchorders
provider.psbcbank.weixin.gateway.all=https://**************:8055/trans/intermgr/online
provider.psbcbank.alipay.gateway.all=https://**************:8055/trans/intermgr/online
provider.psbcbank.unionpay.gateway.all=https://**************:8055/trans/intermgr/online
provider.foxconn.gateway.all=http://**************:31001/epp-gateway/api/entry.do
provider.cgbbank.gateway.all=http://************:18090/gateway/API
provider.hxbank.gateway.all=https://paytest.95577.com.cn/trans
provider.ccb.gateway.all=https://smartpos.ccb.com/mis/
provider.ccb.wap.alipay.gateway.all=https://mmerchant.ccb.com/NCCB/MMER00GatePTReqServlet
provider.ccb.wap.weixin.gateway.all=https://mmerchant.ccb.com/NCCB/MMER00GatePTReqServlet
provider.ccb.wap.unionpay.gateway.all=https://mmerchant.ccb.com/NCCB/MMER00GatePTReqServlet
provider.ccb.precreate.gateway.all=https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_PTJ
provider.grabpay.gateway.all=https://partner-api.stg-myteksi.com/grabpay/partner/v1/terminal/transaction
provider.grabpay.moca.gateway.all=https://stg-paysi.moca.vn/mocapay/partner/v1/terminal/transaction
provider.icbcbank.gateway.all=https://apipcs3.dccnet.com.cn/api
provider.cmbapp.gateway.pay=https://az2openserviceccuat.gm.cmburl.cn/AccessGateway/transIn/{funcName}.json
provider.cmbapp.gateway.all=https://sandbox.cdcc.cmbchina.com/AccessGateway/transIn/{funcName}.json
provider.weixin.hkv3.gateway.all=https://api.mch.weixin.qq.com
provider.weixin.hkv3.wapOrMini.gateway.all=https://api.mch.weixin.qq.com
provider.tl.syb.gateway.all=https://syb-test.allinpay.com/apiweb
provider.tl.syb.bank.gateway.all=https://syb-test.allinpay.com/apiweb
provider.ccb.giftcard.gateway.all=http://*************:8090/CCBIS/B2CMainPlat_00_ZHST
provider.haike.unionpay.gateway.all=http://*************:8080/front-api/pay
provider.haike.unionpay.alipay.v2.gateway.all=https://sqb-front.icardpay.com/ali/aligateway
provider.haike.unionpay.alipay.wap.v2.gateway.all=https://sqb-front.icardpay.com/ali/aligateway
provider.haike.unionpay.weixin.gateway.all=https://sqb-front.icardpay.com
provider.haike.unionpay.weixin.wapOrMini.gateway.all=https://sqb-front.icardpay.com
provider.haike.unionpay.union.qrcode.gateway.all=https://sqb-front-test.icardpay.com/qrc/api/merBackTransReq.do
provider.fuyou.gateway.all=https://fundwx.fuiou.com
provider.fuyou.bank.gateway.all=https://tpayapi-cloud.fuioupay.com
provider.fuyou.bank.query.gateway.all=https://tradequery.fuioupay.com

provider.bocom.gateway.all=https://***************:9443/api/pmssMpng
provider.abc.gateway.all=https://bjuat.echase.cn/gateway/bmpapi/postrans
provider.pab.gateway.all=https://pos-ccard.pingan.com.cn:21721
provider.entpay.gateway.all=https://api.businesspay.qq.com

#provider.zjtlcb.gateway.all=https://dev.zjtlcb.com/api/SQB/
provider.zjtlcb.gateway.all=http://**************:22383/api/SQB/

provider.fjnx.gateway.all=https://epay.fjnx.com.cn/
provider.spdb.gateway.all=https://etest4.spdb.com.cn/spdb/uat/
provider.cmbcbank.gateway.all=https://wxpay.cmbc.com.cn/mobilePlatform
provider.jycard.gateway.all=http://weixin.ykt.jzmu.edu.cn:8081/onecard/uap/gateway.action
provider.jsb.gateway.all=https://epaytest.jsbchina.cn:9999/eis/merchant/merchantServices.htm
provider.lzccb.gateway.all=https://lzshhglpttet.lzccb.cn/
provider.weixin.B2b.gateway.all=https://api.weixin.qq.com/retail/B2b
provider.ztkx.gateway.all=http://test.umbpay.com.cn:12080/cashier/transV2/service.do
provider.tl.s2p.gateway.all=https://cnp-test.allinpay.com/
provider.weixin.wapOrMini.v3.partner.transactions.gateway.all=https://api.mch.weixin.qq.com
provider.yop.gateway.all=https://openapi.yeepay.com/yop-center/
provider.psbc.gateway.all=http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/
provider.pkx.airport.gateway.all=https://pay.bdia.com.cn
provider.macaupass.gateway.all=https://uatopenapi.macaupay.com.mo/masl/umpg/gateway
provider.xzx.gateway.all=https://ykt.jzmu.edu.cn/api/v2/gateway
provider.hopeedu.gateway.all=http://*************:16009
provider.guotong.gateway.all=https://yyfsvxm.postar.cn
provider.airwallex.gateway.all=https://api-demo.airwallex.com
provider.wecard.gateway.all=https://p.wecard.tencent.com
provider.weixin.cycle.v2.gateway.all=https://api.mch.weixin.qq.com

######################################## gateway config end   ###################################################
######################################## fake gateway config start ###################################################
provider.alipay.intl.fake.gateway.all=http://grandet.qa.shouqianba.com
provider.alipay.overseas.fake.gateway.all=http://grandet.qa.shouqianba.com/alipayoversea/gateway.do
provider.alipay.v1.fake.gateway.all=http://grandet.qa.shouqianba.com/alipayv1/gateway.do
provider.alipay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/alipay/gateway.do
provider.alipay.wap.fake.gateway.all=http://grandet.qa.shouqianba.com/alipayv1/gateway.do
provider.alipay.wap.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/alipay/gateway.do
provider.alipay.fitness.fake.gateway.all=http://grandet.qa.shouqianba.com/alipay/gateway.do
provider.bestpay.fake.gateway.all=http://grandet.qa.shouqianba.com/bestpay
provider.bestpay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/bestpayV2
provider.chinaums.fake.gateway.all=http://grandet.qa.shouqianba.com/chinaums
provider.chinaums.v1.fake.gateway.all=http://grandet.qa.shouqianba.com/chinaums
provider.chinaums.epay.fake.gateway.all=http://grandet.qa.shouqianba.com/chinaums
provider.cibbank.fake.gateway.all=http://grandet.qa.shouqianba.com/wft/pay/gateway
provider.citicbank.fake.gateway.all=http://grandet.qa.shouqianba.com/wft/pay/gateway
provider.cmcc.fake.gateway.all=http://grandet.qa.shouqianba.com/cps/cmpayService
provider.direct.unionpay.alipay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.direct.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.direct.unionpay.weixin.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.direct.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.lkl.unionpay.alipay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.lkl.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.lkl.unionpay.weixin.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.lkl.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.gift.card.fake.gateway.all=http://grandet.qa.shouqianba.com/giftCard/gateway.do
provider.lakala.fake.gateway.all=http://grandet.qa.shouqianba.com/lkl
provider.lakala.open.fake.gateway.all=http://grandet.qa.shouqianba.com/lkl
provider.lakala.openv3.fake.gateway.all=http://grandet.qa.shouqianba.com/lkl
provider.lakala.openv3.fake.gateway.union.userId.query=http://grandet.qa.shouqianba.com/lkl/api/v2/saas/query/wx_openid_query
provider.nucc.alipay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/nucc/trade
provider.nucc.bestpay.fake.gateway.all=http://grandet.qa.shouqianba.com/nucc/bestpay/standard
provider.nucc.weixin.fake.gateway.all=http://grandet.qa.shouqianba.com/nucc
provider.nucc.weixin.wapOrMini.fake.gateway.all=http://grandet.qa.shouqianba.com/nucc
provider.sodexo.fake.gateway.all=http://grandet.qa.shouqianba.com/sodexo
provider.sodexo.fake.gateway.oauth=http://grandet.qa.shouqianba.com/sodexo
provider.sodexo.wap.fake.gateway.all=http://grandet.qa.shouqianba.com/sodexo
provider.sodexo.wap.fake.gateway.oauth=http://grandet.qa.shouqianba.com/sodexo
provider.swiftpass.fake.gateway.all=http://grandet.qa.shouqianba.com/wft/pay/gateway
provider.unionpay.alipay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.unionpay.online.fake.gateway.all=http://grandet.qa.shouqianba.com/unionOnline
provider.unionpay.open.fake.gateway.all=http://grandet.qa.shouqianba.com/unionOpen
provider.unionpay.weixin.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.weixin.fake.gateway.all=http://grandet.qa.shouqianba.com/wechat
provider.weixin.hk.fake.gateway.all=http://grandet.qa.shouqianba.com/wechat
provider.weixin.wapOrMini.fake.gateway.all=http://grandet.qa.shouqianba.com/wechat
provider.tl.unionpay.alipay.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.tl.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay/trade
provider.tl.unionpay.weixin.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.tl.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.tl.unionpay.union.qrcode.fake.gateway.all=http://grandet.qa.shouqianba.com/unionpay
provider.weixin.wapOrMini.v3.fake.gateway.all=http://grandet.qa.shouqianba.com/
provider.weixin.wapOrMini.v3.partner.fake.gateway.all=http://grandet.qa.shouqianba.com/

provider.weixin.wapOrMini.v3.palm.fake.gateway.all=http://grandet.beta.iwosai.com/
provider.uepay.fake.gateway.all=http://grandet/uepay/payment/gateway
provider.cmb.fake.gateway.all=http://grandet/polypay/v1.0/mchorders
provider.psbcbank.weixin.fake.gateway.all=https://**************:8055/trans/intermgr/online
provider.psbcbank.alipay.fake.gateway.all=https://**************:8055/trans/intermgr/online
provider.psbcbank.unionpay.fake.gateway.all=https://**************:8055/trans/intermgr/online
provider.foxconn.fake.gateway.all=http://grandet.beta.iwosai.com/epp-gateway/api/entry.do
provider.cgbbank.fake.gateway.all=http://************:18090/gateway/API
provider.ccb.fake.gateway.all=http://grandet.beta.iwosai.com/trans
provider.ccb.wap.alipay.fake.gateway.all=http://grandet.beta.iwosai.com/trans
provider.ccb.wap.weixin.fake.gateway.all=http://grandet.beta.iwosai.com/trans
provider.ccb.wap.unionpay.fake.gateway.all=http://grandet.beta.iwosai.com/trans
provider.ccb.precreate.fake.gateway.all=http://grandet.beta.iwosai.com/trans
provider.icbcbank.fake.gateway.all=http://grandet.beta.iwosai.com/api
provider.cmbapp.fake.gateway.pay=http://grandet.beta.iwosai.com/AccessGateway/transIn/{funcName}.json
provider.cmbapp.fake.gateway.all=http://grandet.beta.iwosai.com/AccessGateway/transIn/{funcName}.json
provider.weixin.hkv3.fake.gateway.all=http://grandet.beta.iwosai.com/wechat
provider.weixin.hkv3.wapOrMini.fake.gateway.all=http://grandet.beta.iwosai.com/wechat
provider.tl.syb.fake.gateway.all=http://grandet.beta.iwosai.com/apiweb
provider.ccb.giftcard.fake.gateway.all=http://grandet.beta.iwosai.com/CCBIS/B2CMainPlat_00_ZHST
provider.haike.unionpay.fake.gateway.all=http://grandet.beta.iwosai.com/front-api/pay
provider.haike.unionpay.alipay.v2.fake.gateway.all=http://grandet.beta.iwosai.com/unionpay/trade
provider.haike.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.beta.iwosai.com/unionpay/trade
provider.haike.unionpay.weixin.fake.gateway.all=http://grandet.beta.iwosai.com/unionpay
provider.haike.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.beta.iwosai.com/unionpay
provider.haike.unionpay.union.qrcode.fake.gateway.all=http://grandet.beta.iwosai.com/unionpay
provider.hxbank.fake.gateway.all=http://grandet.beta.iwosai.com/trans
provider.fuyou.fake.gateway.all=https://grandet.beta.iwosai.com
provider.bocom.fake.gateway.all=https://grandet.beta.iwosai.com/api/pmssMpng
provider.abc.fake.gateway.all=http://bjuat.echase.cn/gateway/bmpapi/postrans
provider.pab.fake.gateway.all=https://pos-ccard.pingan.com.cn:21721
provider.entpay.fake.gateway.all=http://grandet.beta.iwosai.com

provider.zjtlcb.fake.gateway.all=https://dev.zjtlcb.com/api/SQB/

provider.fjnx.fake.gateway.all=https://nepayuat.fjnx.com.cn/
provider.spdb.fake.gateway.all=http://grandet.beta.iwosai.com/spdb/uat/
provider.cmbcbank.fake.gateway.all=http://grandet.beta.iwosai.com
provider.jycard.fake.gateway.all=http://grandet.beta.iwosai.com
provider.jsb.fake.gateway.all=http://grandet.beta.iwosai.com
provider.lzccb.fake.gateway.all=http://grandet.beta.iwosai.com
provider.weixin.B2b.fake.gateway.all=http://grandet.beta.iwosai.com/retail/B2b
provider.tl.s2p.fake.gateway.all=http://grandet.beta.iwosai.com/
provider.ztkx.fake.gateway.all=http://grandet.beta.iwosai.com
provider.yop.fake.gateway.all=http://grandet.beta.iwosai.com
provider.pkx.airport.fake.gateway.all=http://grandet.beta.iwosai.com
provider.macaupass.fake.gateway.all=http://grandet.beta.iwosai.com
provider.xzx.fake.gateway.all=http://grandet.beta.iwosai.com
provider.hopeedu.fake.gateway.all=http://grandet.beta.iwosai.com
provider.psbc.fake.gateway.all=http://grandet.beta.iwosai.com
provider.guotong.fake.gateway.all=http://grandet.beta.iwosai.com
provider.airwallex.fake.gateway.all=https://grandet.beta.iwosai.com
provider.wecard.fake.gateway.all=http://grandet.beta.iwosai.com

provider.weixin.wapOrMini.v3.partner.transactions.fake.gateway.all=http://grandet.beta.shouqianba.com
provider.weixin.cycle.v2.fake.gateway.all=http://grandet.beta.iwosai.com


######################################## fake gateway config end   ###################################################


#redis
redis.url=r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
redis.port=6379
redis.database=3
redis.password=roFXzHwXPY3RnI%5





#workflow driver concurrency
workflow.eventbus.concurrency=1
workflow.eventbus.maxQueueSizeForWarning=10
workflow.action.executor.concurrency=8
workflow.action.alipayExecutor.concurrency=8
workflow.action.weixinExecutor.concurrency=8
workflow.action.indirect.concurrency=8
workflow.action.nuccBestPayExecutor.concurrency=8
workflow.action.lklUnionpayWeixinExecutor.concurrency=4
workflow.action.lklUnionpayAlipayExecutor.concurrency=4
workflow.action.giftCard.concurrency=8
workflow.action.unionpayOpenExecutor.concurrency=8
workflow.action.chinaums.alipay.concurrency=8
workflow.action.chinaums.weixin.concurrency=8
workflow.action.chinaumsExecutor.concurrency=8
workflow.action.unionpayOnlineExecutor.concurrency=8
workflow.action.psbcBank.concurrency=8
workflow.action.tlsyb.alipay.concurrency=8
workflow.action.tlsyb.weixin.concurrency=8
workflow.action.haike.alipay.concurrency=8
workflow.action.haike.weixin.concurrency=8
workflow.action.hxbank.concurrency=8
workflow.action.cmbcbank.concurrency=8

async.concurrency=8
clientnotify.concurrency=8
confirmPay.concurrency=8

workflow.action.largeDelayTime=1
workflow.action.discard.threshold=5000


#amqp facede
message.send.wait.max.queue.size=20
message.send.executor.concurrency=10

#kafka facede
message.send.topic=trade
message.send.brokers=**************:9092,**************:9092,**************:9092
message.send.registry.url=http://**************:8081,http://**************:8081,http://**************:8081
message.send.batch.size=100
message.send.acks=all
message.send.linger.ms=500
message.send.max.block.ms=3000
message.send.transaction.partitions=1
message.send.enable.idempotence=true
#core-business
jsonrpc.core-business.server=http://core-business.beta.iwosai.com/
#jsonrpc.core-business.server=http://localhost:8080/



#upay-activity
jsonrpc.upay-activity.server=http://upay-activity.beta.iwosai.com/

#user-service
jsonrpc.user-service.server=http://user-service.beta.iwosai.com/

#qrcode imaging
qrcode.imaging.server=http://upay.test.shouqianba.com

#upay-wallet
jsonrpc.upay-wallet.server=http://upay-wallet.beta.iwosai.com/

#fake-upay-wallet
jsonrpc.fake-upay-wallet.server=http://upay-wallet-fake.beta.iwosai.com/

#short-url
jsonrpc.short-url.server=http://short-url.beta.iwosai.com/

#profit-sharing
jsonrpc.profit-sharing.server=http://profit-sharing.beta.iwosai.com/

#profit-sharing-proxy
jsonrpc.profit-sharing-proxy.server=http://profit-sharing-proxy.beta.iwosai.com/


#upay-transaction
jsonrpc.upay-transaction.server=http://upay-transaction.beta.iwosai.com/

tran-es-sync.server=http://tran-es-sync.beta.iwosai.com/

#upay-prepaid-card
jsonrpc.upay-prepaid-card.server=http://upay-prepaid-card.beta.iwosai.com/

#upay-prepaid-card fake
jsonrpc.fake-upay-prepaid-card.server=http://upay-prepaid-card-fake.beta.iwosai.com/

#trade-manage
jsonrpc.trade-manage.server=http://trade-manage-service.beta.iwosai.com/

#signature-proxy
jsonrpc.signature-proxy.server=http://signature-proxy.beta.iwosai.com/

# ????
jsonrpc.customer-user.server=http://customer-user.beta.iwosai.com/

jsonrpc.mini-apps-open.server=http://mini-apps-open.beta.iwosai.com/

#cmcc connection config
cmcc.connection.readTimeout=5000
cmcc.connection.connectionTimeout=3000

#nuccbestpay connection config
nuccbestpay.connection.readTimeout=5000
nuccbestpay.connection.connectionTimeout=3000

#lakala connection config
lakala.connection.readTimeout=5000
lakala.connection.connectionTimeout=3000

#lakala open connection config
lakala.open.connection.readTimeout=50000
lakala.open.connection.connectionTimeout=30000

#cibbank connection config
cibbank.connection.readTimeout=5000
cibbank.connection.connectionTimeout=3000

#weixin connection config
weixin.connection.readTimeout=5000
weixin.connection.connectionTimeout=2000

#weixin hk connection config
weixinhk.connection.readTimeout=5000
weixinhk.connection.connectionTimeout=2000

#alipay intl connection config
alipay.intl.connection.readTimeout=10000
alipay.intl.connection.connectionTimeout=3000

#unionpay open connection config
unionpayopen.connection.readTimeout=5000
unionpayopen.connection.connectionTimeout=1000

#chinaums connection config
chinaums.connection.readTimeout=5000
chinaums.connection.connectionTimeout=2000

#unionpay online connection config
unionpayonline.connection.readTimeout=10000
unionpayonline.connection.connectionTimeout=1000

#abc connection config
abc.connection.readTimeout=10000
abc.connection.connectionTimeout=5000



#zjtl connection config
zjtl.connection.readTimeout=10000
zjtl.connection.connectionTimeout=5000


#sodexo connection config
sodexo.connection.readTimeout=5000
sodexo.connection.connectionTimeout=2000

#tl unionpay connection config
tl.union.qrcode.connection.readTimeout=10000
tl.union.qrcode.connection.connectionTimeout=1000

#uepay connection config
uepay.connection.readTimeout=5000
uepay.connection.connectionTimeout=1000

#cmb connection config
cmb.connection.readTimeout=5000
cmb.connection.connectionTimeout=2000
#cmb app connection config
cmb.app.connection.readTimeout=5000
cmb.app.connection.connectionTimeout=2000
#psbcbank connection config
psbcbank.connection.readTimeout=10000
psbcbank.connection.connectionTimeout=1000

#foxconn connection config
foxconn.connection.readTimeout=5000
foxconn.connection.connectionTimeout=2000


#cgbbank connection config
cgbbank.connection.readTimeout=8000
cgbbank.connection.connectionTimeout=3000

#hxbank connection config
hxbank.connection.readTimeout=5000
hxbank.connection.connectionTimeout=1000

#cmb connection config
ccb.connection.readTimeout=10000
ccb.connection.connectionTimeout=5000

#grabpay connection config
grabpay.connection.readTimeout=10000
grabpay.connection.connectionTimeout=5000

#icbc connection config
icbc.connection.readTimeout=10000
icbc.connection.connectionTimeout=5000

#syb connection config
tl.syb.connection.readTimeout=5000
tl.syb.connection.connectionTimeout=3000

#ccbis connection config
ccb.giftcard.connection.readTimeout=5000
ccb.giftcard.connection.connectionTimeout=3000

#bocom connection config
bocom.connection.readTimeout=10000
bocom.connection.connectionTimeout=5000

#entpay connection config
entpay.connection.readTimeout=10000
entpay.connection.connectionTimeout=5000

#ztkx connection config
ztkx.connection.readTimeout=10000
ztkx.connection.connectionTimeout=5000

#hopeedu connection config
hopeedu.connection.readTimeout=10000
hopeedu.connection.connectionTimeout=5000

#cmbc connection config
cmbc.connection.readTimeout=10000
cmbc.connection.connectionTimeout=5000

#jycard connection config
jycard.connection.readTimeout=10000
jycard.connection.connectionTimeout=5000

#jsb connection config
jsb.connection.readTimeout=10000
jsb.connection.connectionTimeout=5000

#weixin B2b connection config
weixin.B2b.connection.readTimeout=5000
weixin.B2b.connection.connectionTimeout=3000

#techtrans connection config
techtrans.connection.readTimeout=5000
techtrans.connection.connectionTimeout=3000


#macaupass connection config
macaupass.connection.readTimeout=5000
macaupass.connection.connectionTimeout=3000

#xzx connection config
xzx.connection.readTimeout=5000
xzx.connection.connectionTimeout=3000


#upay-activity  connection config
upay-activity.connection.readTimeout=5000
upay-activity.connection.connectionTimeout=3000

#upay-activity B2C connection config
upay-activity.b2c.connection.readTimeout=5000
upay-activity.b2c.connection.connectionTimeout=3000

#upay-activity long time connection config
upay-activity.longTime.connection.readTimeout=5000
upay-activity.longTime.connection.connectionTimeout=3000

#db config
db.maxActive=5
db.minIdel=1

spring.application.name=upay-gateway
spring.application.env=test
spring.application.rate=1.0f

lark.webhook=https://open.feishu.cn/open-apis/bot/v2/hook/e832b5c8-2b5e-4fa4-8d3c-e6f938bb44c3

qr.host=https://qr-wap-pay.iwosai.com/gateway
qr.return.host=https://qr-wap-pay.iwosai.com/qr/csb_result

#depoist rsa key id
deposit.reflect.rsa_key.id=f017ec9f-8776-4300-833b-626f4af681a6
jsonrpc.brand-settle.server=http://brand-settle.beta.iwosai.com/
spring.scene-manage-service.url=http://scene-manage-service.beta.iwosai.com/
spring.scene-manage-service.project_name=upay-gateway
spring.scene-manage-service.synonym_map={\"payway\":[[1,2]]}

jdbt.mini.redirectUrlFormat=https://jdpaycert.jd.com/scan/unionPay/degrade?merchantSecondNo=%s&qrCode=%s

# test