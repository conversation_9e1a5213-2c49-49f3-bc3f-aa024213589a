<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
 
    <!-- ========================= RESOURCE DEFINITIONS ========================= -->

    <!-- import the dataSource definition -->
    <import resource="datasource-config.xml"/>



    <!-- Transaction manager for a single JDBC DataSource (alternative to JTA) -->
    <bean id="transactionManager"
        class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
        p:dataSource-ref="datasource" />
    <bean id="ticketTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
          p:dataSource-ref="orderSnDatasource" />
    <tx:annotation-driven transaction-manager="transactionManager"/>

    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="datasource" />
    </bean>

    <bean id="orderSnJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="orderSnDatasource" />
    </bean>

    <bean id="orderDao" class="com.wosai.data.dao.common.TimedDaoBase" >
        <constructor-arg>
            <bean class="com.wosai.upay.dao.JsonBlobAwareDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.upay.dao.FakeRequestDao">
                        <constructor-arg index="0">
                            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                                <constructor-arg index="0" value="order"/>
                                <constructor-arg index="1" ref="jdbcTemplate"/>
                                <constructor-arg index="2">
                                    <list>
                                        <value>merchant_id</value>
                                    </list>
                                </constructor-arg>
                                <constructor-arg index="3" value="true"></constructor-arg>
                            </bean>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                                <constructor-arg index="0" value="order_fake"/>
                                <constructor-arg index="1" ref="jdbcTemplate"/>
                                <constructor-arg index="2">
                                    <list>
                                        <value>merchant_id</value>
                                    </list>
                                </constructor-arg>
                                <constructor-arg index="3" value="true"></constructor-arg>
                            </bean>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <set>
                        <value>items</value>
                        <value>net_items</value>
                        <value>reflect</value>
                    </set>
                </constructor-arg>
            </bean>

        </constructor-arg>
    </bean>

    <bean id="transactionDao" class="com.wosai.data.dao.common.TimedDaoBase" >
        <constructor-arg>
            <bean class="com.wosai.upay.dao.JsonBlobAwareDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.upay.dao.FakeRequestDao">
                        <constructor-arg index="0">
                            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                                <constructor-arg index="0" value="transaction"/>
                                <constructor-arg index="1" ref="jdbcTemplate"/>
                                <constructor-arg index="2">
                                    <list>
                                        <value>merchant_id</value>
                                    </list>
                                </constructor-arg>
                                <constructor-arg index="3" value="true"></constructor-arg>
                            </bean>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                                <constructor-arg index="0" value="transaction_fake"/>
                                <constructor-arg index="1" ref="jdbcTemplate"/>
                                <constructor-arg index="2">
                                    <list>
                                        <value>merchant_id</value>
                                    </list>
                                </constructor-arg>
                                <constructor-arg index="3" value="true"></constructor-arg>
                            </bean>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <set>
                        <value>items</value>
                        <value>extra_params</value>
                        <value>extra_out_fields</value>
                        <value>extended_params</value>
                        <value>reflect</value>
                        <value>config_snapshot</value>
                        <value>biz_error_code</value>
                        <value>provider_error_info</value>
                    </set>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>



    <bean id="logDao" class="com.wosai.upay.dao.JsonBlobAwareDao">
    	<constructor-arg index="0">
            <bean class="com.wosai.data.dao.common.TimedDaoBase">
                <constructor-arg>
                    <bean class="com.wosai.upay.dao.FakeRequestDao">
                        <constructor-arg index="0">
                            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                                <constructor-arg index="0" value="log" />
                                <constructor-arg index="1" ref="jdbcTemplate" />
                                <constructor-arg index="2">
                                    <list>
                                        <value>merchant_id</value>
                                    </list>
                                </constructor-arg>
                                <constructor-arg index="3" value="true"></constructor-arg>
                            </bean>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                                <constructor-arg index="0" value="log_fake" />
                                <constructor-arg index="1" ref="jdbcTemplate" />
                                <constructor-arg index="2">
                                    <list>
                                        <value>merchant_id</value>
                                    </list>
                                </constructor-arg>
                                <constructor-arg index="3" value="true"></constructor-arg>
                            </bean>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
        </constructor-arg>
        <constructor-arg index="1">
            <set>
                <value>payload</value>
            </set>
        </constructor-arg>
    </bean>

	<bean id="walletDao" class="com.wosai.data.dao.common.TimedDaoBase">
		<constructor-arg>
            <bean class="com.wosai.upay.dao.FakeRequestDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                        <constructor-arg index="0" value="wallet" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                        <constructor-arg index="2"><list></list></constructor-arg>
                        <constructor-arg index="3" value="true"></constructor-arg>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="com.wosai.data.dao.jdbc.DrdsJdbcDaoBase">
                        <constructor-arg index="0" value="wallet_fake" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                        <constructor-arg index="2"><list></list></constructor-arg>
                        <constructor-arg index="3" value="true"></constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
		</constructor-arg>
	</bean>

    <bean id="systemConfigDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.dao.FakeRequestDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="system_config" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="system_config_fake" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                    </bean>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>
    
    <bean id="nonSqbOrderDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.dao.FakeRequestDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="non_sqb_order" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="non_sqb_order_fake" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                    </bean>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>

    <bean id="preCancelOrderDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.dao.FakeRequestDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="pre_cancel" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="pre_cancel_fake" />
                        <constructor-arg index="1" ref="jdbcTemplate" />
                    </bean>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>

    <bean class="com.wosai.upay.repository.DataRepository">
        <property name="orderDao" ref="orderDao"/>
        <property name="transactionDao" ref="transactionDao"/>
        <property name="walletDao" ref="walletDao"/>
        <property name="logDao" ref="logDao"/>
        <property name="systemConfigDao" ref="systemConfigDao"/>
        <property name="nonSqbOrderDao" ref="nonSqbOrderDao"/>
        <property name="preCancelOrderDao" ref="preCancelOrderDao"/>
    </bean>








</beans>