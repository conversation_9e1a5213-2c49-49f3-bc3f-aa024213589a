#部署区域
shouqianba.region=zhangjiakou

#upay jdbc config
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=********************************************************************************************************************************
jdbc.username=st_core_20ahg7yw
jdbc.password=iBtj)C2$X^hc*RfOJ7N5FgAVu@43
jdbc.connection.eviction.interval=60000

#ticket jdbc  config
jdbc1.driverClassName=com.mysql.jdbc.Driver
jdbc1.url=**************************************************************************************************************
jdbc1.username=st_core_gbk6tioh
jdbc1.password=kStsfWdnvTU7e)%EuNIj6w14c$Bg
jdbc1.connection.eviction.interval=60000

#orderSn prefix config
ordersn.prefix=20002,20012,20022,20032,20042
#notify host
notify.host=https://upay-gateway-mock.iwosai.com
psbc.notify.host=https://upay-gateway-mock.iwosai.com

#专线网络的回调地址映射
notify.private.network.hosts={}
# gateways
######################################## gateway config start ###################################################
provider.alipay.intl.gateway.all=http://grandet
provider.alipay.overseas.gateway.all=http://grandet/alipayoversea/gateway.do
provider.alipay.v1.gateway.all=http://grandet/alipayv1/gateway.do
provider.alipay.v2.gateway.all=http://grandet/alipay/gateway.do
provider.alipay.wap.gateway.all=http://grandet/alipayv1/gateway.do
provider.alipay.wap.v2.gateway.all=http://grandet/alipay/gateway.do
provider.alipay.fitness.gateway.all=http://grandet/alipay/gateway.do
provider.bestpay.gateway.all=http://grandet/bestpay
provider.bestpay.v2.gateway.all=http://grandet/bestpayV2
provider.chinaums.gateway.all=http://grandet/chinaums
provider.chinaums.v1.gateway.all=http://grandet/chinaums
provider.chinaums.epay.gateway.all=http://grandet/chinaums
provider.cibbank.gateway.all=http://grandet/wft/pay/gateway
provider.citicbank.gateway.all=http://grandet/wft/pay/gateway
provider.cmcc.gateway.all=http://grandet/cps/cmpayService
provider.direct.unionpay.alipay.v2.gateway.all=http://grandet/unionpay/trade
provider.direct.unionpay.alipay.wap.v2.gateway.all=http://grandet/unionpay/trade
provider.direct.unionpay.weixin.gateway.all=http://grandet/unionpay
provider.direct.unionpay.weixin.wapOrMini.gateway.all=http://grandet/unionpay
provider.lkl.unionpay.alipay.v2.gateway.all=http://grandet/unionpay/trade
provider.lkl.unionpay.alipay.wap.v2.gateway.all=http://grandet/unionpay/trade
provider.lkl.unionpay.weixin.gateway.all=http://grandet/unionpay
provider.lkl.unionpay.weixin.wapOrMini.gateway.all=http://grandet/unionpay
provider.gift.card.gateway.all=http://grandet/giftCard/gateway.do
provider.lakala.gateway.all=http://grandet/lkl
provider.lakala.open.gateway.all=http://grandet/labs/txn
provider.lakala.openv3.gateway.all=http://grandet/sit/api/v3
provider.lakala.openv3.gateway.union.userId.query=http://grandet/sit/api/v2/saas/query/wx_openid_query
provider.nucc.alipay.v2.gateway.all=http://grandet/nucc/trade
provider.nucc.bestpay.gateway.all=http://grandet/nucc/bestpay/standard
provider.nucc.weixin.gateway.all=http://grandet/nucc
provider.nucc.weixin.wapOrMini.gateway.all=http://grandet/nucc
provider.sodexo.gateway.all=http://grandet/sodexo
provider.sodexo.gateway.oauth=http://grandet/sodexo
#sodexo wap
provider.sodexo.wap.gateway.all=http://grandet/sodexo
provider.sodexo.wap.gateway.oauth=http://grandet/sodexo

provider.swiftpass.gateway.all=http://grandet/wft/pay/gateway
provider.unionpay.alipay.v2.gateway.all=http://grandet/unionpay/trade
provider.unionpay.alipay.wap.v2.gateway.all=http://grandet/unionpay/trade
provider.unionpay.online.gateway.all=http://grandet/unionOnline
provider.unionpay.open.gateway.all=http://grandet/unionOpen
provider.unionpay.weixin.gateway.all=http://grandet/unionpay
provider.unionpay.weixin.wapOrMini.gateway.all=http://grandet/unionpay
provider.weixin.gateway.all=http://grandet/wechat
provider.weixin.gateway.payFaceAuthInfo=https://payapp.weixin.qq.com/face/get_wxpayface_authinfo
provider.weixin.hk.gateway.all=http://grandet/wechat
provider.weixin.wapOrMini.gateway.all=http://grandet/wechat
provider.tl.unionpay.alipay.v2.gateway.all=http://grandet/trxapi/alipaytrx/pay
provider.tl.unionpay.alipay.wap.v2.gateway.all=http://grandet/trxapi/alipaytrx/pay
provider.tl.unionpay.weixin.gateway.all=http://grandet/trxapi/wxtrx
provider.tl.unionpay.weixin.wapOrMini.gateway.all=http://grandet/trxapi/wxtrx
provider.tl.unionpay.union.qrcode.gateway.all=http://grandet/trxapi/unionpaytrx/trx
provider.weixin.wapOrMini.v3.gateway.all=http://grandet/v3/payscore/serviceorder
provider.weixin.wapOrMini.v3.palm.gateway.all=http://grandet/v3/palmservice/service-order
provider.weixin.wapOrMini.v3.partner.gateway.all=http://grandet/v3/payscore/partner/serviceorder
provider.uepay.gateway.all=http://grandet/uepay/payment/gateway
provider.cmb.gateway.all=http://grandet/polypay/v1.0/mchorders
provider.psbcbank.weixin.gateway.all=http://grandet/postal/payment/gateway
provider.psbcbank.alipay.gateway.all=http://grandet/postal/payment/gateway
provider.psbcbank.unionpay.gateway.all=http://grandet/postal/payment/gateway
provider.foxconn.gateway.all=http://grandet/epp-gateway/api/entry.do
provider.cgbbank.gateway.all=http://grandet/gateway/API
provider.hxbank.gateway.all=http://grandet/trans
provider.ccb.gateway.all=https://grandet/trans
provider.ccb.wap.alipay.gateway.all=https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_PTJ
provider.ccb.wap.weixin.gateway.all=https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_PTJ
provider.ccb.wap.unionpay.gateway.all=https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_PTJ
provider.ccb.precreate.gateway.all=https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_PTJ
provider.grabpay.gateway.all=http://grandet/grabpay/partner/v1/terminal/transaction
provider.grabpay.moca.gateway.all=http://grandet/mocapay/partner/v1/terminal/transaction
provider.icbcbank.gateway.all=http://grandet/api
provider.cmbapp.gateway.pay=http://grandet/AccessGateway/transIn/{funcName}.json
provider.cmbapp.gateway.all=http://grandet/AccessGateway/transIn/{funcName}.json
provider.weixin.hkv3.gateway.all=http://grandet
provider.weixin.hkv3.wapOrMini.gateway.all=http://grandet
provider.tl.syb.gateway.all=http://grandet/apiweb
provider.tl.syb.bank.gateway.all=http://grandet/apiweb
provider.ccb.giftcard.gateway.all=http://grandet/CCBIS/B2CMainPlat_00_ZHST
provider.haike.unionpay.gateway.all=http://grandet/front-api
provider.haike.unionpay.gateway.refund=http://grandet/front-api/refund
provider.haike.unionpay.alipay.v2.gateway.all=http://grandet/front-api/trade
provider.haike.unionpay.alipay.wap.v2.gateway.all=http://grandet/front-api/trade
provider.haike.unionpay.weixin.gateway.all=http://grandet/front-api
provider.haike.unionpay.weixin.wapOrMini.gateway.all=http://grandet/front-api
provider.haike.unionpay.union.qrcode.gateway.all=http://grandet/front-api
provider.fuyou.gateway.all=http://grandet/fuyou
provider.pab.gateway.all=http://grandet
provider.bocom.gateway.all=http://grandet/api/pmssMpng
provider.abc.gateway.all=http://bjuat.echase.cn/gateway/bmpapi/postrans
provider.entpay.gateway.all=http://grandet
provider.fuyou.bank.gateway.all=http://grandet/fuyou
provider.fuyou.bank.query.gateway.all=http://grandet/fuyou
provider.zjtlcb.gateway.all=http://grandet/api/SQB/
provider.fjnx.gateway.all=http://grandet
provider.spdb.gateway.all=http://grandet/spdb/uat/
provider.cmbcbank.gateway.all=http://grandet
provider.jycard.gateway.all=http://grandet
provider.jsb.gateway.all=http://grandet
provider.lzccb.gateway.all=http://grandet
provider.weixin.B2b.gateway.all=http://grandet/retail/B2b
provider.ztkx.gateway.all=http://grandet
provider.tl.s2p.gateway.all=http://grandet
provider.yop.gateway.all=http://grandet
provider.pkx.airport.gateway.all=http://grandet
provider.macaupass.gateway.all=http://grandet
provider.xzx.gateway.all=https://grandet
provider.hopeedu.gateway.all=https://grandet
provider.weixin.wapOrMini.v3.partner.transactions.gateway.all=https://api.mch.weixin.qq.com
provider.guotong.fake.gateway.all=http://grandet
provider.psbc.gateway.all=http://grandet
provider.airwallex.gateway.all=http://grandet
provider.wecard.fake.gateway.all=http://grandet
provider.weixin.cycle.v2.gateway.all=http://grandet

######################################## gateway config end   ###################################################
######################################## fake gateway config start ###################################################
provider.alipay.intl.fake.gateway.all=http://grandet
provider.alipay.overseas.fake.gateway.all=http://grandet/alipayoversea/gateway.do
provider.alipay.v1.fake.gateway.all=http://grandet/alipayv1/gateway.do
provider.alipay.v2.fake.gateway.all=http://grandet/alipay/gateway.do
provider.alipay.wap.fake.gateway.all=http://grandet/alipayv1/gateway.do
provider.alipay.wap.v2.fake.gateway.all=http://grandet/alipay/gateway.do
provider.alipay.fitness.fake.gateway.all=http://grandet/alipay/gateway.do
provider.bestpay.fake.gateway.all=http://grandet/bestpay
provider.bestpay.v2.fake.gateway.all=http://grandet/bestpayV2
provider.chinaums.fake.gateway.all=http://grandet/chinaums
provider.chinaums.v1.fake.gateway.all=http://grandet/chinaums
provider.chinaums.epay.fake.gateway.all=http://grandet/chinaums
provider.cibbank.fake.gateway.all=http://grandet/wft/pay/gateway
provider.citicbank.fake.gateway.all=http://grandet/wft/pay/gateway
provider.cmcc.fake.gateway.all=http://grandet/cps/cmpayService
provider.direct.unionpay.alipay.v2.fake.gateway.all=http://grandet/unionpay/trade
provider.direct.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet/unionpay/trade
provider.direct.unionpay.weixin.fake.gateway.all=http://grandet/unionpay
provider.direct.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet/unionpay
provider.lkl.unionpay.alipay.v2.fake.gateway.all=http://grandet/unionpay/trade
provider.lkl.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet/unionpay/trade
provider.lkl.unionpay.weixin.fake.gateway.all=http://grandet/unionpay
provider.lkl.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet/unionpay
provider.gift.card.fake.gateway.all=http://grandet/giftCard/gateway.do
provider.lakala.fake.gateway.all=http://grandet/lkl
provider.lakala.open.fake.gateway.all=http://grandet/lklopen
provider.lakala.openv3.fake.gateway.all=http://grandet/sit/api/v3
provider.lakala.openv3.fake.gateway.union.userId.query=http://grandet/sit/api/v2/saas/query/wx_openid_query
provider.nucc.alipay.v2.fake.gateway.all=http://grandet/nucc/trade
provider.nucc.bestpay.fake.gateway.all=http://grandet/nucc/bestpay/standard
provider.nucc.weixin.fake.gateway.all=http://grandet/nucc
provider.nucc.weixin.wapOrMini.fake.gateway.all=http://grandet/nucc
provider.sodexo.fake.gateway.all=http://grandet/sodexo
provider.sodexo.fake.gateway.oauth=http://grandet/sodexo
provider.sodexo.wap.fake.gateway.all=http://grandet/sodexo
provider.sodexo.wap.fake.gateway.oauth=http://grandet/sodexo
provider.swiftpass.fake.gateway.all=http://grandet/wft/pay/gateway
provider.unionpay.alipay.v2.fake.gateway.all=http://grandet/unionpay/trade
provider.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet/unionpay/trade
provider.unionpay.online.fake.gateway.all=http://grandet/unionOnline
provider.unionpay.open.fake.gateway.all=http://grandet/unionOpen
provider.unionpay.weixin.fake.gateway.all=http://grandet/unionpay
provider.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet/unionpay
provider.weixin.fake.gateway.all=http://grandet/wechat
provider.weixin.hk.fake.gateway.all=http://grandet/wechat
provider.weixin.wapOrMini.fake.gateway.all=http://grandet/wechat
provider.tl.unionpay.alipay.v2.fake.gateway.all=http://grandet/trxapi/alipaytrx/pay
provider.tl.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet/trxapi/alipaytrx/pay
provider.tl.unionpay.weixin.fake.gateway.all=http://grandet/trxapi/wxtrx
provider.tl.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet/trxapi/wxtrx
provider.tl.unionpay.union.qrcode.fake.gateway.all=http://grandet/trxapi/unionpaytrx/trx
provider.weixin.wapOrMini.v3.fake.gateway.all=http://grandet/v3/payscore/serviceorder
provider.weixin.wapOrMini.v3.palm.fake.gateway.all=http://grandet/v3/palmservice/service-order
provider.weixin.wapOrMini.v3.partner.fake.gateway.all=http://grandet/v3/payscore/partner/serviceorder
provider.uepay.fake.gateway.all=http://grandet/uepay/payment/gateway
provider.cmb.fake.gateway.all=http://grandet/polypay/v1.0/mchorders
provider.psbcbank.weixin.fake.gateway.all=http://grandet/postal/payment/gateway
provider.psbcbank.alipay.fake.gateway.all=http://grandet/postal/payment/gateway
provider.psbcbank.unionpay.fake.gateway.all=http://grandet/postal/payment/gateway
provider.foxconn.fake.gateway.all=http://grandet/epp-gateway/api/entry.do
provider.cgbbank.fake.gateway.all=http://grandet/gateway/API
provider.hxbank.fake.gateway.all=https://grandet/trans
provider.ccb.fake.gateway.all=http://grandet/trans
provider.ccb.wap.alipay.fake.gateway.all=http://grandet/trans
provider.ccb.wap.weixin.fake.gateway.all=http://grandet/trans
provider.ccb.wap.unionpay.fake.gateway.all=http://grandet/trans
provider.ccb.precreate.fake.gateway.all=http://grandet/trans
provider.grabpay.fake.gateway.all=http://grandet/grabpay/partner/v1/terminal/transaction
provider.grabpay.moca.fake.gateway.all=http://grandet/mocapay/partner/v1/terminal/transaction
provider.icbcbank.fake.gateway.all=http://grandet/api
provider.cmbapp.fake.gateway.pay=http://grandet/AccessGateway/transIn/{funcName}.json
provider.cmbapp.fake.gateway.all=http://grandet/AccessGateway/transIn/{funcName}.json
provider.weixin.hkv3.fake.gateway.all=http://grandet
provider.weixin.hkv3.wapOrMini.fake.gateway.all=http://grandet
provider.tl.syb.fake.gateway.all=http://grandet/apiweb
provider.ccb.giftcard.fake.gateway.all=http://grandet/CCBIS/B2CMainPlat_00_ZHST
provider.haike.unionpay.fake.gateway.all=http://grandet/front-api
provider.haike.unionpay.fake.gateway.refund=http://grandet/front-api/refund
provider.haike.unionpay.alipay.v2.fake.gateway.all=http://grandet/front-api/trade
provider.haike.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet/front-api/trade
provider.haike.unionpay.weixin.fake.gateway.all=http://grandet/front-api
provider.haike.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet/front-api
provider.haike.unionpay.union.qrcode.fake.gateway.all=http://grandet/front-api
provider.fuyou.fake.gateway.all=http://grandet/fuyou
provider.bocom.fake.gateway.all=http://grandet/api/pmssMpng
provider.abc.fake.gateway.all=http://bjuat.echase.cn/gateway/bmpapi/postrans
provider.pab.fake.gateway.all=http://grandet
provider.entpay.fake.gateway.all=http://grandet
provider.zjtlcb.fake.gateway.all=http://grandet/api/SQB/
provider.fjnx.fake.gateway.all=http://grandet
provider.spdb.fake.gateway.all=http://grandet/spdb/uat/
provider.cmbcbank.fake.gateway.all=http://grandet
provider.jycard.fake.gateway.all=http://grandet
provider.jsb.fake.gateway.all=http://grandet
provider.lzccb.fake.gateway.all=http://grandet
provider.weixin.B2b.fake.gateway.all=http://grandet/retail/B2b
provider.ztkx.fake.gateway.all=http://grandet
provider.tl.s2p.fake.gateway.all=http://grandet
provider.yop.fake.gateway.all=http://grandet
provider.pkx.airport.fake.gateway.all=http://grandet
provider.macaupass.fake.gateway.all=http://grandet
provider.xzx.fake.gateway.all=http://grandet
provider.hopeedu.fake.gateway.all=http://grandet
provider.weixin.wapOrMini.v3.partner.transactions.fake.gateway.all=http://grandet
provider.guotong.gateway.all=https://yyfsvxm.postar.cn
provider.wecard.gateway.all=http://grandet

provider.airwallex.fake.gateway.all=http://grandet
provider.psbc.fake.gateway.all=http://grandet
provider.weixin.cycle.v2.fake.gateway.all=http://grandet

######################################## fake gateway config end   ###################################################

#redis
redis.url=redis-beta
redis.port=6379
redis.database=3
redis.password=roFXzHwXPY3RnI%5


#workflow driver concurrency
workflow.eventbus.concurrency=8
workflow.eventbus.maxQueueSizeForWarning=10
workflow.action.executor.concurrency=12
workflow.action.alipayExecutor.concurrency=12
workflow.action.weixinExecutor.concurrency=12
workflow.action.indirect.concurrency=12
workflow.action.nuccBestPayExecutor.concurrency=8
workflow.action.lklUnionpayWeixinExecutor.concurrency=4
workflow.action.lklUnionpayAlipayExecutor.concurrency=4
workflow.action.giftCard.concurrency=8
workflow.action.unionpayOpenExecutor.concurrency=8
workflow.action.chinaums.alipay.concurrency=8
workflow.action.chinaums.weixin.concurrency=8
workflow.action.chinaumsExecutor.concurrency=8
workflow.action.unionpayOnlineExecutor.concurrency=8
workflow.action.psbcBank.concurrency=8
workflow.action.tlsyb.alipay.concurrency=8
workflow.action.tlsyb.weixin.concurrency=8
workflow.action.haike.alipay.concurrency=8
workflow.action.haike.weixin.concurrency=8
workflow.action.hxbank.concurrency=8
workflow.action.cmbcbank.concurrency=8

async.concurrency=8
clientnotify.concurrency=8
confirmPay.concurrency=8
workflow.action.largeDelayTime=500
workflow.action.discard.threshold=5000
#amqp facede
message.send.wait.max.queue.size=20
message.send.executor.concurrency=10
#kafka facede
message.send.topic=trade
message.send.brokers=kafka-beta1.base:9092,kafka-beta2.base:9092,kafka-beta3.base:9092
message.send.registry.url=http://kafka-beta1.base:8081,http://kafka-beta2.base:8081,http://kafka-beta3.base:8081
message.send.batch.size=10
message.send.acks=all
message.send.linger.ms=1000
message.send.max.block.ms=3000
message.send.transaction.partitions=1
message.send.enable.idempotence=true

#core-business
jsonrpc.core-business.server=http://core-business/
#upay-activity
jsonrpc.upay-activity.server=http://upay-activity/
#user-service
jsonrpc.user-service.server=http://user-service/
#qrcode imaging
qrcode.imaging.server=https://upay-gateway.iwosai.com/
#upay-wallet
jsonrpc.upay-wallet.server=http://upay-wallet/

#upay-wallet
jsonrpc.fake-upay-wallet.server=http://grandet/upayWallet
#short-url
jsonrpc.short-url.server=http://short-url/
#profit-sharing
jsonrpc.profit-sharing.server=http://profit-sharing/
#profit-sharing-proxy
jsonrpc.profit-sharing-proxy.server=http://profit-sharing-proxy/

#upay-transaction
jsonrpc.upay-transaction.server=http://upay-transaction/

tran-es-sync.server=http://tran-es-sync/

#upay-prepaid-card
jsonrpc.upay-prepaid-card.server=http://upay-prepaid-card/


#upay-prepaid-card fake
jsonrpc.fake-upay-prepaid-card.server=http://grandet/upayPrePaidCard

#trade-manage
jsonrpc.trade-manage.server=http://trade-manage-service/

#signature-proxy
jsonrpc.signature-proxy.server=http://signature-proxy/

jsonrpc.customer-user.server=http://customer-user/

jsonrpc.brand-settle.server=http://brand-settle/

jsonrpc.mini-apps-open.server=http://mini-apps-open/

#cmcc connection config
cmcc.connection.readTimeout=1000
cmcc.connection.connectionTimeout=1000
#nuccbestpay connection config
nuccbestpay.connection.readTimeout=5000
nuccbestpay.connection.connectionTimeout=3000
#lakala connection config
lakala.connection.readTimeout=5000
lakala.connection.connectionTimeout=3000
#lakala open connection config
lakala.open.connection.readTimeout=5000
lakala.open.connection.connectionTimeout=3000
#cibbank connection config
cibbank.connection.readTimeout=5000
cibbank.connection.connectionTimeout=3000
#weixin connection config
weixin.connection.readTimeout=5000
weixin.connection.connectionTimeout=2000
#weixin hk connection config
weixinhk.connection.readTimeout=5000
weixinhk.connection.connectionTimeout=2000
#alipay intl connection config
alipay.intl.connection.readTimeout=5000
alipay.intl.connection.connectionTimeout=2000
#unionpay open connection config
unionpayopen.connection.readTimeout=5000
unionpayopen.connection.connectionTimeout=1000
#chinaums connection config
chinaums.connection.readTimeout=10000
chinaums.connection.connectionTimeout=10000
#unionpay online connection config
unionpayonline.connection.readTimeout=10000
unionpayonline.connection.connectionTimeout=1000
#sodexo connection config
sodexo.connection.readTimeout=5000
sodexo.connection.connectionTimeout=2000
#tl unionpay connection config
tl.union.qrcode.connection.readTimeout=10000
tl.union.qrcode.connection.connectionTimeout=1000
#uepay connection config
uepay.connection.readTimeout=5000
uepay.connection.connectionTimeout=1000
#cmb connection config
cmb.connection.readTimeout=10000
cmb.connection.connectionTimeout=5000
#cmb app connection config
cmb.app.connection.readTimeout=5000
cmb.app.connection.connectionTimeout=2000
#psbcbank connection config
psbcbank.connection.readTimeout=5000
psbcbank.connection.connectionTimeout=1000
#foxconn connection config
foxconn.connection.readTimeout=5000
foxconn.connection.connectionTimeout=1000
#cgbbank connection config
cgbbank.connection.readTimeout=5000
cgbbank.connection.connectionTimeout=3000
#hxbank connection config
hxbank.connection.readTimeout=5000
hxbank.connection.connectionTimeout=1000
#cmb connection config
ccb.connection.readTimeout=10000
ccb.connection.connectionTimeout=5000
#grabpay connection config
grabpay.connection.readTimeout=10000
grabpay.connection.connectionTimeout=5000
#icbc connection config
icbc.connection.readTimeout=10000
icbc.connection.connectionTimeout=5000

#syb connection config
tl.syb.connection.readTimeout=5000
tl.syb.connection.connectionTimeout=3000

#ccbis connection config
ccb.giftcard.connection.readTimeout=5000
ccb.giftcard.connection.connectionTimeout=3000

#bocom connection config
bocom.connection.readTimeout=10000
bocom.connection.connectionTimeout=5000

#entpay connection config
entpay.connection.readTimeout=10000
entpay.connection.connectionTimeout=5000

#jycard connection config
jycard.connection.readTimeout=5000
jycard.connection.connectionTimeout=3000

#jsb connection config
jsb.connection.readTimeout=5000
jsb.connection.connectionTimeout=3000

#upay-activity  connection config
upay-activity.connection.readTimeout=5000
upay-activity.connection.connectionTimeout=3000

#upay-activity B2C connection config
upay-activity.b2c.connection.readTimeout=5000
upay-activity.b2c.connection.connectionTimeout=3000

#upay-activity long time connection config
upay-activity.longTime.connection.readTimeout=5000
upay-activity.longTime.connection.connectionTimeout=3000

#abc connection config
abc.connection.readTimeout=10000
abc.connection.connectionTimeout=5000

#cmbc connection config
cmbc.connection.readTimeout=10000
cmbc.connection.connectionTimeout=5000

#weixin B2b connection config
weixin.B2b.connection.readTimeout=5000
weixin.B2b.connection.connectionTimeout=3000

#techtrans connection config
techtrans.connection.readTimeout=5000
techtrans.connection.connectionTimeout=3000

#macaupass connection config
macaupass.connection.readTimeout=5000
macaupass.connection.connectionTimeout=3000

#xzx connection config
xzx.connection.readTimeout=5000
xzx.connection.connectionTimeout=3000

#hopeedu connection config
hopeedu.connection.readTimeout=5000
hopeedu.connection.connectionTimeout=3000

#db config
db.maxActive=30
db.minIdel=1
spring.application.name=upay-gateway
spring.application.env=beta
spring.application.rate=1.0f

lark.webhook=https://open.feishu.cn/open-apis/bot/v2/hook/e832b5c8-2b5e-4fa4-8d3c-e6f938bb44c3

qr.host=https://qr-wap-pay.iwosai.com/gateway
qr.return.host=https://qr-wap-pay.iwosai.com/qr/csb_result
#depoist rsa key id
deposit.reflect.rsa_key.id=f017ec9f-8776-4300-833b-626f4af681a6

spring.scene-manage-service.url=http://scene-manage-service.beta.iwosai.com/
spring.scene-manage-service.project_name=upay-gateway
spring.scene-manage-service.synonym_map={\"payway\":[[1,2]]}

jdbt.mini.redirectUrlFormat=https://jdpaycert.jd.com/scan/unionPay/degrade?merchantSecondNo=%s&qrCode=%s