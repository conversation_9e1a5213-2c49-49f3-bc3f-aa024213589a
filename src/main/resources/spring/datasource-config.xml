<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context
         http://www.springframework.org/schema/context/spring-context.xsd
         http://www.springframework.org/schema/jdbc
         http://www.springframework.org/schema/jdbc/spring-jdbc.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<import resource="classpath:vault-datasource-manager.xml"/>

	<bean id="databaseVault" class="com.wosai.middleware.vault.Vault" factory-method="autoload"/>

	<bean id="dataSourceTranslateImpls"
		  class="com.wosai.database.instrumentation.spring.translate.impl.DruidDataSourceTranslate">
		<constructor-arg ref="dataSourceManager"/>
	</bean>

	<!-- ========================= DATASOURCE DEFINITION ========================= -->

	<!-- DataSource configuration for druid. -->
	<bean id="datasource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<property name="driverClassName" value="${jdbc.driverClassName}" />
		<property name="url" value="${jdbc.url}" />
		<property name="username" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<!-- 配置初始化大小、最小、最大 -->
		<property name="initialSize" value="${db.minIdel}" />
		<property name="minIdle" value="${db.minIdel}" />
		<property name="maxActive" value="${db.maxActive}" />
		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="3000" />
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="${jdbc.connection.eviction.interval}" />
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000" />
		<property name="testWhileIdle" value="true" />
		<!-- 这里建议配置为TRUE，防止取到的连接不可用 -->
		<property name="testOnBorrow" value="true" />
		<property name="testOnReturn" value="false" />
		<!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
		<property name="poolPreparedStatements" value="true" />
		<property name="maxPoolPreparedStatementPerConnectionSize" value="20" />
		<!-- 这里配置提交方式，默认就是TRUE，可以不用配置 -->
		<property name="defaultAutoCommit" value="true" />
		<!-- 验证连接有效与否的SQL，不同的数据配置不同 -->
		<property name="validationQuery" value="select 1 " />
		<property name="filters" value="stat" />
		<property name="proxyFilters">
			<list>
				<ref bean="logFilter" />
			</list>
		</property>
	</bean>

	<bean id="orderSnDatasource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
		<property name="driverClassName" value="${jdbc1.driverClassName}" />
		<property name="url" value="${jdbc1.url}" />
		<property name="username" value="${jdbc1.username:}" />
		<property name="password" value="${jdbc1.password:}" />
		<!-- 配置初始化大小、最小、最大 -->
		<property name="initialSize" value="${db.minIdel}" />
		<property name="minIdle" value="${db.minIdel}" />
		<property name="maxActive" value="${db.maxActive}" />
		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="3000" />
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="${jdbc1.connection.eviction.interval}" />
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000" />
		<property name="testWhileIdle" value="true" />
		<!-- 这里建议配置为TRUE，防止取到的连接不可用 -->
		<property name="testOnBorrow" value="true" />
		<property name="testOnReturn" value="false" />
		<!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
		<property name="poolPreparedStatements" value="true" />
		<property name="maxPoolPreparedStatementPerConnectionSize" value="20" />
		<!-- 这里配置提交方式，默认就是TRUE，可以不用配置 -->
		<property name="defaultAutoCommit" value="true" />
		<!-- 验证连接有效与否的SQL，不同的数据配置不同 -->
		<property name="validationQuery" value="select 1 " />
		<property name="filters" value="stat" />
		<property name="proxyFilters">
			<list>
				<ref bean="logFilter" />
			</list>
		</property>
	</bean>

	<bean id="logFilter" class="com.alibaba.druid.filter.logging.Slf4jLogFilter">
		<property name="statementExecutableSqlLogEnable" value="false" />
	</bean>

</beans>