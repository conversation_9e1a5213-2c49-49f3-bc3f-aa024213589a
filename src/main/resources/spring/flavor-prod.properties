#部署区域
shouqianba.region=hangzhou

#upay jdbc config
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=*******************************************************************************************************************************************************
jdbc.username=upay
jdbc.password=c98511e6_ae297cd32ac33438W
jdbc.connection.eviction.interval=60000


#ticket jdbc  config
# rds4r0j3egp846751a6y458.mysql.rds.aliyuncs.com
jdbc1.driverClassName=com.mysql.jdbc.Driver
jdbc1.url=pk-upay-gateway-ticket-2871?socketTimeout=5000
jdbc1.connection.eviction.interval=60000

#orderSn prefix config
ordersn.prefix=78952,78950


#notify host
notify.host =http://gateway.shouqianba.com
psbc.notify.host=https://gateway.shouqianba.com
#专线网络的回调地址映射
notify.private.network.hosts={}

# gateways



######################################## gateway config start ###################################################

provider.alipay.intl.gateway.all=https://open-na.alipay.com
provider.alipay.overseas.gateway.all=https://intlmapi.alipay.com/gateway.do
provider.alipay.v1.gateway.all=https://mapi.alipay.com/gateway.do
provider.alipay.v2.gateway.all=https://openapi.alipay.com/gateway.do
provider.alipay.wap.gateway.all=https://mapi.alipay.com/gateway.do
provider.alipay.wap.v2.gateway.all=https://openapi.alipay.com/gateway.do
provider.alipay.fitness.gateway.all=https://apigw.alipay-eco.com
provider.bestpay.gateway.all=https://webpaywg.bestpay.com.cn
provider.bestpay.v2.gateway.all=https://mapi.bestpay.com.cn
provider.chinaums.gateway.all=https://api-mop.chinaums.com
provider.chinaums.v1.gateway.all=https://api-mop.chinaums.com
provider.chinaums.epay.gateway.all=https://api-mop.chinaums.com
provider.cibbank.gateway.all=https://pay.swiftpass.cn/pay/gateway
provider.citicbank.gateway.all=https://pay.swiftpass.cn/pay/gateway
provider.swiftpass.gateway.all=https://pay.swiftpass.cn/pay/gateway
provider.cmcc.gateway.all=http://ipos.10086.cn/cps/cmpayService
provider.direct.unionpay.alipay.v2.gateway.all=https://apay2.95516.com/ali/aligateway
provider.direct.unionpay.alipay.wap.v2.gateway.all=https://apay2.95516.com/ali/aligateway
provider.direct.unionpay.weixin.gateway.all=https://tpay2.95516.com
provider.direct.unionpay.weixin.wapOrMini.gateway.all=https://tpay2.95516.com
provider.lkl.unionpay.alipay.v2.gateway.all=http://upatproxy-alipay.vpc.shouqianba.com/ali/aligateway
provider.lkl.unionpay.alipay.wap.v2.gateway.all=http://upatproxy-alipay.vpc.shouqianba.com/ali/aligateway
provider.lkl.unionpay.weixin.gateway.all=http://upatproxy-wx.vpc.shouqianba.com
provider.lkl.unionpay.weixin.wapOrMini.gateway.all=http://upatproxy-wx.vpc.shouqianba.com
provider.gift.card.gateway.all=https://giftcard-openapi.shouqianba.com/gateway.do
provider.lakala.gateway.all=https://sips.lakala.com
provider.lakala.open.gateway.all=https://s2.lakala.com/labs/txn
provider.lakala.openv3.gateway.all=https://s2.lakala.com/api/v3
provider.lakala.openv3.gateway.union.userId.query=https://s2.lakala.com/api/v2/saas/query/wx_openid_query
provider.nucc.alipay.v2.gateway.all=http://*************:8213/gateway/alipay
provider.nucc.bestpay.gateway.all=http://*************:7213/gateway/standard
provider.nucc.weixin.gateway.all=http://*************:8213
provider.nucc.weixin.wapOrMini.gateway.all=http://*************:8213
provider.sodexo.gateway.all=https://mt.sdxpass.com:8003
provider.sodexo.gateway.oauth=https://mt.sdxpass.com:8002
#sodexo wap
provider.sodexo.wap.gateway.all=https://mt.sdxpass.com:8003
provider.sodexo.wap.gateway.oauth=https://mt.sdxpass.com:8002

provider.unionpay.alipay.v2.gateway.all=?
provider.unionpay.alipay.wap.v2.gateway.all=?
provider.unionpay.online.gateway.all=https://gateway.95516.com
provider.unionpay.open.gateway.all=https://partner.95516.com
provider.unionpay.weixin.gateway.all=http://**************
provider.unionpay.weixin.wapOrMini.gateway.all=http://**************
provider.weixin.gateway.all=https://api.mch.weixin.qq.com
provider.weixin.gateway.payFaceAuthInfo=https://payapp.weixin.qq.com/face/get_wxpayface_authinfo
provider.weixin.hk.gateway.all=https://mchapi.wechatpay.com.hk
provider.weixin.wapOrMini.gateway.all=https://api.mch.weixin.qq.com
provider.tl.unionpay.alipay.v2.gateway.all=https://isv-api.allinpay.com/trxapi/alipaytrx/pay
provider.tl.unionpay.alipay.wap.v2.gateway.all=https://isv-api.allinpay.com/trxapi/alipaytrx/pay
provider.tl.unionpay.weixin.gateway.all=https://isv-api.allinpay.com/trxapi/wxtrx
provider.tl.unionpay.weixin.wapOrMini.gateway.all=https://isv-api.allinpay.com/trxapi/wxtrx
provider.tl.unionpay.union.qrcode.gateway.all=https://isv-api.allinpay.com/trxapi/unionpaytrx/trx
provider.weixin.wapOrMini.v3.gateway.all=https://api.mch.weixin.qq.com/v3/payscore/serviceorder
provider.weixin.wapOrMini.v3.palm.gateway.all=https://api.mch.weixin.qq.com/v3/palmservice/service-order
provider.weixin.wapOrMini.v3.partner.gateway.all=https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder
provider.uepay.gateway.all=https://openapi.uepay.mo/payment/gateway
provider.cmb.gateway.all=https://api.cmbchina.com/polypay/v1.0/mchorders
provider.psbcbank.weixin.gateway.all=https://nap.psbc.com/trans/intermgr/online
provider.psbcbank.alipay.gateway.all=https://nap.psbc.com/trans/intermgr/online
provider.psbcbank.unionpay.gateway.all=https://nap.psbc.com/trans/intermgr/online
provider.foxconn.gateway.all=https://eppgateway.yuanqq.net/epp-gateway/api/entry.do
provider.cgbbank.gateway.all=https://open.cgbchina.com.cn/gateway/API
provider.hxbank.gateway.all=https://pay.hxb.com.cn/trans
# 注意建行 provider.ccb.gateway.all 后面必须带 / , 不然交易会报错
provider.ccb.gateway.all=https://smartpos.ccb.com/mis/
provider.ccb.wap.alipay.gateway.all=https://mmerchant.ccb.com/NCCB/MMER00GatePTReqServlet
provider.ccb.wap.weixin.gateway.all=https://mmerchant.ccb.com/NCCB/MMER00GatePTReqServlet
provider.ccb.wap.unionpay.gateway.all=https://mmerchant.ccb.com/NCCB/MMER00GatePTReqServlet
provider.ccb.precreate.gateway.all=https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_PTJ
provider.grabpay.gateway.all=https://partner-api.grab.com/grabpay/partner/v1/terminal/transaction
provider.grabpay.moca.gateway.all=https://partner-gw.moca.vn/mocapay/partners/v1/terminal/transaction
provider.icbcbank.gateway.all=https://gw.open.icbc.com.cn/api
provider.cmbapp.gateway.pay=https://openserviceccgm.bas.cmbchina.com/AccessGateway/transIn/{funcName}.json
provider.cmbapp.gateway.all=https://open.cmbchina.com/AccessGateway/transIn/{funcName}.json
provider.weixin.hkv3.gateway.all=https://api.mch.weixin.qq.com
provider.weixin.hkv3.wapOrMini.gateway.all=https://api.mch.weixin.qq.com
provider.tl.syb.gateway.all=https://vsp.allinpay.com/apiweb
provider.tl.syb.bank.gateway.all=https://vsp.allinpay.com/apiweb
provider.ccb.giftcard.gateway.all=https://dining.icenter.ccb.com/CCBIS/B2CMainPlat_00_ZHST
provider.haike.unionpay.gateway.all=https://saas-front.hkrt.cn/front-api/pay
provider.haike.unionpay.alipay.v2.gateway.all=https://sqb-front.icardpay.com/ali/aligateway
provider.haike.unionpay.alipay.wap.v2.gateway.all=https://sqb-front.icardpay.com/ali/aligateway
provider.haike.unionpay.weixin.gateway.all=https://sqb-front.icardpay.com
provider.haike.unionpay.weixin.wapOrMini.gateway.all=https://sqb-front.icardpay.com
provider.haike.unionpay.union.qrcode.gateway.all=https://sqb-front.icardpay.com/qrc/api/merBackTransReq.do
provider.fuyou.gateway.all=https://spay-cloud.fuioupay.com
provider.bocom.gateway.all=https://open.bankcomm.com/api/pmssMpng
provider.abc.gateway.all=https://bjpay.echase.cn/gateway/bmpapi/postrans
provider.pab.gateway.all=https://pos-ccard.pingan.com.cn:21721
provider.entpay.gateway.all=https://api.businesspay.qq.com
provider.fuyou.bank.gateway.all=https://tpayapi-cloud.fuioupay.com
provider.fuyou.bank.query.gateway.all=https://tradequery.fuioupay.com
provider.zjtlcb.gateway.all=https://fcloud.zjtlcb.com/api/SQB/
provider.fjnx.gateway.all=https://epay.fjnx.com.cn/
provider.spdb.gateway.all=https://api.spdb.com.cn/spdb/prd/
provider.cmbcbank.gateway.all=https://epay.cmbc.com.cn/appweb
provider.jycard.gateway.all=http://weixin.ykt.jzmu.edu.cn:8081/onecard/uap/gateway.action
provider.jsb.gateway.all=https://mybank.jsbchina.cn:577/eis/merchant/merchantServices.htm
provider.lzccb.gateway.all=https://lzqwsd.lzccb.cn/
provider.weixin.B2b.gateway.all=https://api.weixin.qq.com/retail/B2b
provider.tl.s2p.gateway.all=https://cnp.allinpay.com
provider.ztkx.gateway.all=https://www.umbpay.cn/cashier/transV2/service.do
provider.yop.gateway.all=https://openapi.yeepay.com/yop-center/
provider.airwallex.gateway.all=https://api.airwallex.com
provider.pkx.airport.gateway.all=https://pay.bdia.com.cn
provider.macaupass.gateway.all=https://openapi.macaupay.com.mo/masl/umpg/gateway
provider.xzx.gateway.all=https://ykt.jzmu.edu.cn/api/v2/gateway
provider.hopeedu.gateway.all=http://ci.hopeedu.com
provider.weixin.wapOrMini.v3.partner.transactions.gateway.all=https://api.mch.weixin.qq.com
provider.psbc.gateway.all=https://open.psbc.com/gateway/biz/unionpay/

provider.guotong.gateway.all=https://yyfsvxm.postar.cn
provider.wecard.gateway.all=https://p.wecard.tencent.com
provider.weixin.cycle.v2.gateway.all=https://api.mch.weixin.qq.com

######################################## gateway config end   ###################################################

######################################## fake gateway config start ###################################################
provider.alipay.intl.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.alipay.overseas.fake.gateway.all=http://grandet.internal.shouqianba.com/alipayoversea/gateway.do
provider.alipay.v1.fake.gateway.all=http://grandet.internal.shouqianba.com/alipayv1/gateway.do
provider.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/alipay/gateway.do
provider.alipay.wap.fake.gateway.all=http://grandet.internal.shouqianba.com/alipayv1/gateway.do
provider.alipay.wap.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/alipay/gateway.do
provider.alipay.fitness.fake.gateway.all=http://grandet.internal.shouqianba.com/alipay/gateway.do
provider.bestpay.fake.gateway.all=http://grandet.internal.shouqianba.com/bestpay
provider.bestpay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/bestpayV2
provider.chinaums.fake.gateway.all=http://grandet.internal.shouqianba.com/chinaums
provider.chinaums.v1.fake.gateway.all=http://grandet.internal.shouqianba.com/chinaums
provider.chinaums.epay.fake.gateway.all=http://grandet.internal.shouqianba.com/chinaums
provider.cibbank.fake.gateway.all=http://grandet.internal.shouqianba.com/wft/pay/gateway
provider.citicbank.fake.gateway.all=http://grandet.internal.shouqianba.com/wft/pay/gateway
provider.cmcc.fake.gateway.all=http://grandet.internal.shouqianba.com/cps/cmpayService
provider.direct.unionpay.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay/trade
provider.direct.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay/trade
provider.direct.unionpay.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay
provider.direct.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay
provider.lkl.unionpay.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay/trade
provider.lkl.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay/trade
provider.lkl.unionpay.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay
provider.lkl.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay
provider.gift.card.fake.gateway.all=http://grandet.internal.shouqianba.com/giftCard/gateway.do
provider.lakala.fake.gateway.all=http://grandet.internal.shouqianba.com/lkl
provider.lakala.open.fake.gateway.all=http://grandet.internal.shouqianba.com/labs/txn
provider.lakala.openv3.fake.gateway.all=http://grandet.internal.shouqianba.com/sit/api/v3
provider.lakala.openv3.fake.gateway.union.userId.query=http://grandet.internal.shouqianba.com/sit/api/v2/saas/query/wx_openid_query
provider.nucc.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/nucc/trade
provider.nucc.bestpay.fake.gateway.all=http://grandet.internal.shouqianba.com/nucc/bestpay/standard
provider.nucc.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/nucc
provider.nucc.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/nucc
provider.sodexo.fake.gateway.all=http://grandet.internal.shouqianba.com/sodexo
provider.sodexo.fake.gateway.oauth=http://grandet.internal.shouqianba.com/sodexo
provider.sodexo.wap.fake.gateway.all=http://grandet.internal.shouqianba.com/sodexo
provider.sodexo.wap.fake.gateway.oauth=http://grandet.internal.shouqianba.com/sodexo
provider.swiftpass.fake.gateway.all=http://grandet.internal.shouqianba.com/wft/pay/gateway
provider.unionpay.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay/trade
provider.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay/trade
provider.unionpay.online.fake.gateway.all=http://grandet.internal.shouqianba.com/unionOnline
provider.unionpay.open.fake.gateway.all=http://grandet.internal.shouqianba.com/unionOpen
provider.unionpay.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay
provider.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/unionpay
provider.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/wechat
provider.weixin.hk.fake.gateway.all=http://grandet.internal.shouqianba.com/wechat
provider.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/wechat
provider.tl.unionpay.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/trxapi/alipaytrx/pay
provider.tl.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/trxapi/alipaytrx/pay
provider.tl.unionpay.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/trxapi/wxtrx
provider.tl.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/trxapi/wxtrx
provider.tl.unionpay.union.qrcode.fake.gateway.all=http://grandet.internal.shouqianba.com/trxapi/unionpaytrx/trx
provider.weixin.wapOrMini.v3.fake.gateway.all=http://grandet.internal.shouqianba.com/v3/payscore/serviceorder
provider.weixin.wapOrMini.v3.palm.fake.gateway.all=http://grandet.internal.shouqianba.com/v3/palmservice/service-order
provider.weixin.wapOrMini.v3.partner.fake.gateway.all=http://grandet.internal.shouqianba.com/v3/payscore/partner/serviceorder
provider.uepay.fake.gateway.all=http://grandet.internal.shouqianba.com/uepay/payment/gateway
provider.cmb.fake.gateway.all=http://grandet.internal.shouqianba.com/polypay/v1.0/mchorders
provider.psbcbank.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/postal/payment/gateway
provider.psbcbank.alipay.fake.gateway.all=http://grandet.internal.shouqianba.com/postal/payment/gateway
provider.psbcbank.unionpay.fake.gateway.all=http://grandet.internal.shouqianba.com/postal/payment/gateway
provider.foxconn.fake.gateway.all=http://grandet.internal.shouqianba.com/epp-gateway/api/entry.do
provider.cgbbank.fake.gateway.all=http://grandet.internal.shouqianba.com/gateway/API
provider.hxbank.fake.gateway.all=http://grandet.internal.shouqianba.com/trans
provider.ccb.fake.gateway.all=http://grandet.internal.shouqianba.com/trans
provider.ccb.wap.alipay.fake.gateway.all=http://grandet.internal.shouqianba.com/trans
provider.ccb.wap.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/trans
provider.ccb.wap.unionpay.fake.gateway.all=http://grandet.internal.shouqianba.com/trans
provider.ccb.precreate.fake.gateway.all=http://grandet.internal.shouqianba.com/trans
provider.icbcbank.fake.gateway.all=http://grandet.internal.shouqianba.com/api
provider.cmbapp.fake.gateway.pay=http://grandet.internal.shouqianba.com/AccessGateway/transIn/{funcName}.json
provider.cmbapp.fake.gateway.all=http://grandet.internal.shouqianba.com/AccessGateway/transIn/{funcName}.json
provider.weixin.hkv3.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.weixin.hkv3.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.tl.syb.fake.gateway.all=http://grandet.internal.shouqianba.com/apiweb
provider.ccb.giftcard.fake.gateway.all=http://grandet.internal.shouqianba.com/CCBIS/B2CMainPlat_00_ZHST
provider.haike.unionpay.fake.gateway.all=http://grandet.internal.shouqianba.com/front-api
provider.haike.unionpay.alipay.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/front-api/trade
provider.haike.unionpay.alipay.wap.v2.fake.gateway.all=http://grandet.internal.shouqianba.com/front-api/trade
provider.haike.unionpay.weixin.fake.gateway.all=http://grandet.internal.shouqianba.com/front-api
provider.haike.unionpay.weixin.wapOrMini.fake.gateway.all=http://grandet.internal.shouqianba.com/front-api
provider.haike.unionpay.union.qrcode.fake.gateway.all=http://grandet.internal.shouqianba.com/front-api
provider.haike.unionpay.fake.gateway.refund=http://grandet.internal.shouqianba.com/refund
provider.fuyou.fake.gateway.all=http://grandet.internal.shouqianba.com/fuyou
provider.bocom.fake.gateway.all=http://grandet.internal.shouqianba.com/api/pmssMpng
provider.abc.fake.gateway.all=http://grandet.internal.shouqianba.com/gateway/bmpapi/postrans
provider.pab.fake.gateway.all=http://grandet.internal.shouqianba.com/pab
provider.fuyou.bank.fake.gateway.all=http://grandet.internal.shouqianba.com/fuyou
provider.fuyou.bank.query.fake.gateway.all=http://grandet.internal.shouqianba.com/fuyou
provider.entpay.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.zjtlcb.fake.gateway.all=http://grandet.internal.shouqianba.com/api/SQB/
provider.fjnx.fake.gateway.all=http://grandet.internal.shouqianba.com/fjnx
provider.spdb.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.cmbcbank.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.jycard.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.jsb.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.lzccb.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.weixin.B2b.fake.gateway.all=http://grandet.internal.shouqianba.com/retail/B2b
provider.ztkx.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.tl.s2p.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.weixin.wapOrMini.v3.partner.transactions.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.yop.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.airwallex.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.pkx.airport.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.macaupass.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.xzx.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.hopeedu.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.guotong.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.psbc.fake.gateway.all=http://grandet.internal.shouqianba.com
provider.wecard.fake.gateway.all=http://grandet.internal.shouqianba.com

provider.weixin.cycle.v2.fake.gateway.all=http://grandet.internal.shouqianba.com

######################################## fake gateway config end   ###################################################

#redis
redis.url=r-bp19619d6a62d824389.redis.rds.aliyuncs.com
redis.port=6379
redis.database=2
redis.password=VEjJhGo7f34YUqEjDHA00OtUcGGs

#workflow driver concurrency
workflow.eventbus.concurrency=320
workflow.eventbus.maxQueueSizeForWarning=10
workflow.action.executor.concurrency=200
workflow.action.alipayExecutor.concurrency=160
workflow.action.weixinExecutor.concurrency=200
workflow.action.indirect.concurrency=80
workflow.action.nuccBestPayExecutor.concurrency=30
workflow.action.lklUnionpayWeixinExecutor.concurrency=500
workflow.action.lklUnionpayAlipayExecutor.concurrency=100
workflow.action.giftCard.concurrency=5
workflow.action.unionpayOpenExecutor.concurrency=10
workflow.action.chinaums.alipay.concurrency=10
workflow.action.chinaums.weixin.concurrency=30
workflow.action.chinaumsExecutor.concurrency=10
workflow.action.unionpayOnlineExecutor.concurrency=10
workflow.action.psbcBank.concurrency=30
workflow.action.tlsyb.alipay.concurrency=25
workflow.action.tlsyb.weixin.concurrency=65
workflow.action.haike.alipay.concurrency=40
workflow.action.haike.weixin.concurrency=150
workflow.action.hxbank.concurrency=80
workflow.action.cmbcbank.concurrency=30

async.concurrency=50
clientnotify.concurrency=50
confirmPay.concurrency=50

workflow.action.largeDelayTime=500
workflow.action.discard.threshold=5000

#amqp facede
message.send.wait.max.queue.size=10000
message.send.executor.concurrency=30

#kafka facede
message.send.topic=events.upay.trade
message.send.brokers=************:9092,************:9092,*************:9092,*************:9092,*************:9092
message.send.registry.url=http://************:8081,http://************:8081,http://*************:8081,http://*************:8081,http://*************:8081
message.send.batch.size=16384
message.send.acks=all
message.send.linger.ms=100
message.send.max.block.ms=1000
message.send.transaction.partitions=10
message.send.enable.idempotence=true

#core-business
jsonrpc.core-business.server=http://core-business-internal.shouqianba.com:9900/

#upay-activity
jsonrpc.upay-activity.server=http://upay-activity-internal.shouqianba.com/

#user-service
jsonrpc.user-service.server=http://user-service.internal.shouqianba.com/

#qrcode imaging
qrcode.imaging.server=https://api.shouqianba.com

#upay-wallet
jsonrpc.upay-wallet.server=http://upay-wallet.vpc.shouqianba.com/

#upay-wallet
jsonrpc.fake-upay-wallet.server=http://grandet.internal.shouqianba.com/upayWallet

#short-url
jsonrpc.short-url.server=http://short-url.internal.shouqianba.com/

#profit-sharing
jsonrpc.profit-sharing.server=http://profit-sharing.internal.shouqianba.com/

#profit-sharing-proxy
jsonrpc.profit-sharing-proxy.server=http://profit-sharing-proxy.vpc.shouqianba.com/


#upay-transaction
jsonrpc.upay-transaction.server=http://upay-transaction-query.internal.shouqianba.com/

#tran-es-sync
tran-es-sync.server=http://tran-es-sync.vpc.shouqianba.com/

#upay-prepaid-card
jsonrpc.upay-prepaid-card.server=http://upay-prepaid-card.vpc.shouqianba.com/

#upay-prepaid-card fake
jsonrpc.fake-upay-prepaid-card.server=http://grandet.internal.shouqianba.com/upayPrePaidCard

#trade-manage
jsonrpc.trade-manage.server=http://trade-manage-service.internal.shouqianba.com/

#signature-proxy
jsonrpc.signature-proxy.server=http://signature-proxy.shouqianba.com/

# ????
jsonrpc.customer-user.server=http://customer-user.internal.shouqianba.com/
qr.host=https://qr.shouqianba.com/gateway
qr.return.host=https://qr.shouqianba.com/qr/csb_result

jsonrpc.brand-settle.server=http://brand-settle.vpc.shouqianba.com/

jsonrpc.mini-apps-open.server=http://mini-apps-open.vpc.shouqianba.com/

#cmcc connection config
cmcc.connection.readTimeout=5000
cmcc.connection.connectionTimeout=3000

#nuccbestpay connection config
nuccbestpay.connection.readTimeout=5000
nuccbestpay.connection.connectionTimeout=3000

#lakala connection config
lakala.connection.readTimeout=5000
lakala.connection.connectionTimeout=3000

#lakala open connection config
lakala.open.connection.readTimeout=5000
lakala.open.connection.connectionTimeout=3000

#cibbank connection config
cibbank.connection.readTimeout=5000
cibbank.connection.connectionTimeout=3000
#weixin connection config
weixin.connection.readTimeout=12000
weixin.connection.connectionTimeout=2000

#weixin hk connection config
weixinhk.connection.readTimeout=5000
weixinhk.connection.connectionTimeout=2000

#alipay intl connection config
alipay.intl.connection.readTimeout=5000
alipay.intl.connection.connectionTimeout=2000

#unionpay open connection config
unionpayopen.connection.readTimeout=8000
unionpayopen.connection.connectionTimeout=1000

#chinaums connection config
chinaums.connection.readTimeout=5000
chinaums.connection.connectionTimeout=2000

#unionpay online connection config
unionpayonline.connection.readTimeout=5000
unionpayonline.connection.connectionTimeout=1000

#sodexo connection config
sodexo.connection.readTimeout=5000
sodexo.connection.connectionTimeout=2000

#tl unionpay connection config
tl.union.qrcode.connection.readTimeout=10000
tl.union.qrcode.connection.connectionTimeout=1000

#uepay connection config
uepay.connection.readTimeout=5000
uepay.connection.connectionTimeout=2000

#cmb connection config
cmb.connection.readTimeout=5000
cmb.connection.connectionTimeout=2000

#cmb app connection config
cmb.app.connection.readTimeout=5000
cmb.app.connection.connectionTimeout=2000

#psbcbank connection config
psbcbank.connection.readTimeout=5000
psbcbank.connection.connectionTimeout=2000

#foxconn connection config
foxconn.connection.readTimeout=5000
foxconn.connection.connectionTimeout=2000

#cgbbank connection config
cgbbank.connection.readTimeout=5000
cgbbank.connection.connectionTimeout=2000

#hxbank connection config
hxbank.connection.readTimeout=5000
hxbank.connection.connectionTimeout=2000

#cmb connection config
ccb.connection.readTimeout=5000
ccb.connection.connectionTimeout=2000

#grabpay connection config
grabpay.connection.readTimeout=5000
grabpay.connection.connectionTimeout=2000

#icbc connection config
icbc.connection.readTimeout=5000
icbc.connection.connectionTimeout=2000

#syb connection config
tl.syb.connection.readTimeout=5000
tl.syb.connection.connectionTimeout=3000

#ccbis connection config
ccb.giftcard.connection.readTimeout=5000
ccb.giftcard.connection.connectionTimeout=3000

#bocom connection config
bocom.connection.readTimeout=5000
bocom.connection.connectionTimeout=3000

#entpay connection config
entpay.connection.readTimeout=5000
entpay.connection.connectionTimeout=3000

#jycard connection config
jycard.connection.readTimeout=5000
jycard.connection.connectionTimeout=3000

#jsb connection config
jsb.connection.readTimeout=5000
jsb.connection.connectionTimeout=3000

#upay-activity  connection config
upay-activity.connection.readTimeout=500
upay-activity.connection.connectionTimeout=500

#upay-activity B2C connection config
upay-activity.b2c.connection.readTimeout=1000
upay-activity.b2c.connection.connectionTimeout=1000

#upay-activity long time connection config
upay-activity.longTime.connection.readTimeout=3000
upay-activity.longTime.connection.connectionTimeout=1000

#abc connection config
abc.connection.readTimeout=10000
abc.connection.connectionTimeout=5000

#cmbc connection config
cmbc.connection.readTimeout=10000
cmbc.connection.connectionTimeout=5000

#weixin B2b connection config
weixin.B2b.connection.readTimeout=5000
weixin.B2b.connection.connectionTimeout=3000

#techtrans connection config
techtrans.connection.readTimeout=5000
techtrans.connection.connectionTimeout=1000

#macaupass connection config
macaupass.connection.readTimeout=5000
macaupass.connection.connectionTimeout=3000

#xzx connection config
xzx.connection.readTimeout=5000
xzx.connection.connectionTimeout=1000

#hopeedu connection config
hopeedu.connection.readTimeout=5000
hopeedu.connection.connectionTimeout=1000

#db config
db.maxActive=200
db.minIdel=8

spring.application.name=upay-gateway
spring.application.env=prod
spring.application.rate=1.0f

lark.webhook=https://open.feishu.cn/open-apis/bot/v2/hook/c90f8106-08a0-4cdb-8f48-fa603306ed8b

#depoist rsa key id
deposit.reflect.rsa_key.id=f017ec9f-8776-4300-833b-626f4af681a6

spring.scene-manage-service.url=http://scene-manage-service.vpc.shouqianba.com/
spring.scene-manage-service.project_name=upay-gateway
spring.scene-manage-service.synonym_map={\"payway\":[[1,2]]}

jdbt.mini.redirectUrlFormat=https://jdpaycert.jd.com/scan/unionPay/degrade?merchantSecondNo=%s&qrCode=%s
