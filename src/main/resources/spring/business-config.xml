<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
            http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
            http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <import resource="classpath*:/wosai-tracing.xml"/>
    <context:component-scan
            base-package="com.wosai.upay.service"/>
    <task:annotation-driven/>
    <bean class="com.wosai.upay.util.ApolloConfigurationCenterUtil">
        <constructor-arg name="defaultGateway">
            <props>
                <prop key="provider.alipay.intl.gateway.all">${provider.alipay.intl.gateway.all}</prop>
                <prop key="provider.alipay.overseas.gateway.all">${provider.alipay.overseas.gateway.all}</prop>
                <prop key="provider.alipay.v1.gateway.all">${provider.alipay.v1.gateway.all}</prop>
                <prop key="provider.alipay.v2.gateway.all">${provider.alipay.v2.gateway.all}</prop>
                <prop key="provider.alipay.wap.gateway.all">${provider.alipay.wap.gateway.all}</prop>
                <prop key="provider.alipay.wap.v2.gateway.all">${provider.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.alipay.fitness.gateway.all">${provider.alipay.fitness.gateway.all}</prop>
                <prop key="provider.bestpay.gateway.all">${provider.bestpay.gateway.all}</prop>
                <prop key="provider.bestpay.v2.gateway.all">${provider.bestpay.v2.gateway.all}</prop>
                <prop key="provider.chinaums.gateway.all">${provider.chinaums.gateway.all}</prop>
                <prop key="provider.chinaums.v1.gateway.all">${provider.chinaums.v1.gateway.all}</prop>
                <prop key="provider.chinaums.epay.gateway.all">${provider.chinaums.epay.gateway.all}</prop>
                <prop key="provider.cibbank.gateway.all">${provider.cibbank.gateway.all}</prop>
                <prop key="provider.citicbank.gateway.all">${provider.citicbank.gateway.all}</prop>
                <prop key="provider.cmcc.gateway.all">${provider.cmcc.gateway.all}</prop>
                <prop key="provider.direct.unionpay.alipay.v2.gateway.all">${provider.direct.unionpay.alipay.v2.gateway.all}</prop>
                <prop key="provider.direct.unionpay.alipay.wap.v2.gateway.all">${provider.direct.unionpay.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.direct.unionpay.weixin.gateway.all">${provider.direct.unionpay.weixin.gateway.all}</prop>
                <prop key="provider.direct.unionpay.weixin.wapOrMini.gateway.all">${provider.direct.unionpay.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.alipay.v2.gateway.all">${provider.lkl.unionpay.alipay.v2.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.alipay.wap.v2.gateway.all">${provider.lkl.unionpay.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.weixin.gateway.all">${provider.lkl.unionpay.weixin.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.weixin.wapOrMini.gateway.all">${provider.lkl.unionpay.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.gift.card.gateway.all">${provider.gift.card.gateway.all}</prop>
                <prop key="provider.lakala.gateway.all">${provider.lakala.gateway.all}</prop>
                <prop key="provider.nucc.alipay.v2.gateway.all">${provider.nucc.alipay.v2.gateway.all}</prop>
                <prop key="provider.nucc.bestpay.gateway.all">${provider.nucc.bestpay.gateway.all}</prop>
                <prop key="provider.nucc.weixin.gateway.all">${provider.nucc.weixin.gateway.all}</prop>
                <prop key="provider.nucc.weixin.wapOrMini.gateway.all">${provider.nucc.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.sodexo.gateway.all">${provider.sodexo.gateway.all}</prop>
                <prop key="provider.sodexo.gateway.oauth">${provider.sodexo.gateway.oauth}</prop>
                <prop key="provider.sodexo.wap.gateway.all">${provider.sodexo.wap.gateway.all}</prop>
                <prop key="provider.sodexo.wap.gateway.oauth">${provider.sodexo.wap.gateway.oauth}</prop>
                <prop key="provider.swiftpass.gateway.all">${provider.swiftpass.gateway.all}</prop>
                <prop key="provider.unionpay.alipay.v2.gateway.all">${provider.unionpay.alipay.v2.gateway.all}</prop>
                <prop key="provider.unionpay.alipay.wap.v2.gateway.all">${provider.unionpay.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.unionpay.online.gateway.all">${provider.unionpay.online.gateway.all}</prop>
                <prop key="provider.unionpay.open.gateway.all">${provider.unionpay.open.gateway.all}</prop>
                <prop key="provider.unionpay.weixin.gateway.all">${provider.unionpay.weixin.gateway.all}</prop>
                <prop key="provider.unionpay.weixin.wapOrMini.gateway.all">${provider.unionpay.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.weixin.gateway.all">${provider.weixin.gateway.all}</prop>
                <prop key="provider.weixin.gateway.payFaceAuthInfo">${provider.weixin.gateway.payFaceAuthInfo}</prop>
                <prop key="provider.weixin.hk.gateway.all">${provider.weixin.hk.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.gateway.all">${provider.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.tl.unionpay.alipay.v2.gateway.all">${provider.tl.unionpay.alipay.v2.gateway.all}</prop>
                <prop key="provider.tl.unionpay.alipay.wap.v2.gateway.all">${provider.tl.unionpay.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.tl.unionpay.weixin.gateway.all">${provider.tl.unionpay.weixin.gateway.all}</prop>
                <prop key="provider.tl.unionpay.weixin.wapOrMini.gateway.all">${provider.tl.unionpay.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.tl.unionpay.union.qrcode.gateway.all">${provider.tl.unionpay.union.qrcode.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.gateway.all">${provider.weixin.wapOrMini.v3.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.palm.gateway.all">${provider.weixin.wapOrMini.v3.palm.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.partner.gateway.all">${provider.weixin.wapOrMini.v3.partner.gateway.all}</prop>
                <prop key="provider.uepay.gateway.all">${provider.uepay.gateway.all}</prop>
                <prop key="provider.cmb.gateway.all">${provider.cmb.gateway.all}</prop>
                <prop key="provider.lakala.open.gateway.all">${provider.lakala.open.gateway.all}</prop>
                <prop key="provider.lakala.openv3.gateway.all">${provider.lakala.openv3.gateway.all}</prop>
                <prop key="provider.lakala.openv3.bankcard.gateway.all">${provider.lakala.openv3.gateway.all}</prop>
                <prop key="provider.lakala.openv3.phone.pos.gateway.all">${provider.lakala.openv3.gateway.all}</prop>
                <prop key="provider.psbcbank.weixin.gateway.all">${provider.psbcbank.weixin.gateway.all}</prop>
                <prop key="provider.psbcbank.alipay.gateway.all">${provider.psbcbank.alipay.gateway.all}</prop>
                <prop key="provider.psbcbank.unionpay.gateway.all">${provider.psbcbank.unionpay.gateway.all}</prop>
                <prop key="provider.foxconn.gateway.all">${provider.foxconn.gateway.all}</prop>
                <prop key="provider.cgbbank.gateway.all">${provider.cgbbank.gateway.all}</prop>
                <prop key="provider.hxbank.gateway.all">${provider.hxbank.gateway.all}</prop>
                <prop key="provider.ccb.gateway.all">${provider.ccb.gateway.all}</prop>
                <prop key="provider.ccb.wap.alipay.gateway.all">${provider.ccb.wap.alipay.gateway.all}</prop>
                <prop key="provider.ccb.wap.weixin.gateway.all">${provider.ccb.wap.weixin.gateway.all}</prop>
                <prop key="provider.ccb.wap.unionpay.gateway.all">${provider.ccb.wap.unionpay.gateway.all}</prop>
                <prop key="provider.ccb.precreate.gateway.all">${provider.ccb.precreate.gateway.all}</prop>
                <prop key="provider.grabpay.gateway.all">${provider.grabpay.gateway.all}</prop>
                <prop key="provider.grabpay.moca.gateway.all">${provider.grabpay.moca.gateway.all}</prop>
                <prop key="provider.icbcbank.gateway.all">${provider.icbcbank.gateway.all}</prop>
                <prop key="provider.cmbapp.gateway.pay">${provider.cmbapp.gateway.pay}</prop>
                <prop key="provider.cmbapp.gateway.all">${provider.cmbapp.gateway.all}</prop>
                <prop key="provider.weixin.hkv3.gateway.all">${provider.weixin.hkv3.gateway.all}</prop>
                <prop key="provider.weixin.hkv3.wapOrMini.gateway.all">${provider.weixin.hkv3.wapOrMini.gateway.all}</prop>
                <prop key="provider.tl.syb.gateway.all">${provider.tl.syb.gateway.all}</prop>
                <prop key="provider.tl.syb.bank.gateway.all">${provider.tl.syb.bank.gateway.all}</prop>
                <prop key="provider.ccb.giftcard.gateway.all">${provider.ccb.giftcard.gateway.all}</prop>
                <prop key="provider.haike.unionpay.gateway.all">${provider.haike.unionpay.gateway.all}</prop>
                <prop key="provider.haike.unionpay.alipay.v2.gateway.all">${provider.haike.unionpay.alipay.v2.gateway.all}</prop>
                <prop key="provider.haike.unionpay.alipay.wap.v2.gateway.all">${provider.haike.unionpay.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.haike.unionpay.weixin.gateway.all">${provider.haike.unionpay.weixin.gateway.all}</prop>
                <prop key="provider.haike.unionpay.weixin.wapOrMini.gateway.all">${provider.haike.unionpay.weixin.wapOrMini.gateway.all}</prop>
                <prop key="provider.haike.unionpay.union.qrcode.gateway.all">${provider.haike.unionpay.union.qrcode.gateway.all}</prop>
                <prop key="provider.alipay.pre.deposit.gateway.all">${provider.alipay.wap.v2.gateway.all}</prop>
                <prop key="provider.fuyou.gateway.all">${provider.fuyou.gateway.all}</prop>
                <prop key="provider.fuyou.bank.gateway.all">${provider.fuyou.bank.gateway.all}</prop>
                <prop key="provider.fuyou.bank.query.gateway.all">${provider.fuyou.bank.query.gateway.all}</prop>
                <prop key="provider.bocom.gateway.all">${provider.bocom.gateway.all}</prop>
                <prop key="provider.lakala.openv3.gateway.union.userId.query">${provider.lakala.openv3.gateway.union.userId.query}</prop>
                <prop key="provider.abc.gateway.all">${provider.abc.gateway.all}</prop>
                <prop key="provider.pab.gateway.all">${provider.pab.gateway.all}</prop>
                <prop key="provider.entpay.gateway.all">${provider.entpay.gateway.all}</prop>
                <prop key="provider.zjtlcb.gateway.all">${provider.zjtlcb.gateway.all}</prop>
                <prop key="provider.fjnx.gateway.all">${provider.fjnx.gateway.all}</prop>
                <prop key="provider.spdb.gateway.all">${provider.spdb.gateway.all}</prop>
                <prop key="provider.cmbcbank.gateway.all">${provider.cmbcbank.gateway.all}</prop>
                <prop key="provider.jycard.gateway.all">${provider.jycard.gateway.all}</prop>
                <prop key="provider.jsb.gateway.all">${provider.jsb.gateway.all}</prop>
                <prop key="provider.lzccb.gateway.all">${provider.lzccb.gateway.all}</prop>
                <prop key="provider.weixin.mini.B2b.gateway.all">${provider.weixin.B2b.gateway.all}</prop>
                <prop key="provider.tls2p.gateway.all">${provider.tl.s2p.gateway.all}</prop>
                <prop key="provider.ztkx.gateway.all">${provider.ztkx.gateway.all}</prop>
                <prop key="provider.yop.gateway.all">${provider.yop.gateway.all}</prop>
                <prop key="provider.pkx.airport.gateway.all">${provider.pkx.airport.gateway.all}</prop>
                <prop key="provider.xzx.gateway.all">${provider.xzx.gateway.all}</prop>
                <prop key="provider.hopeedu.gateway.all">${provider.hopeedu.gateway.all}</prop>
                <prop key="provider.psbc.gateway.all">${provider.psbc.gateway.all}</prop>
                <prop key="provider.guotong.gateway.all">${provider.guotong.gateway.all}</prop>
                <prop key="provider.wecard.gateway.all">${provider.wecard.gateway.all}</prop>
                <prop key="provider.airwallex.gateway.all">${provider.airwallex.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.gateway.all">${provider.weixin.cycle.v2.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.merchant.gateway.all">${provider.weixin.cycle.v2.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.partner.auth.gateway.all">${provider.weixin.cycle.v2.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.merchant.auth.gateway.all">${provider.weixin.cycle.v2.gateway.all}</prop>

                <prop key="provider.macaupass.gateway.all">${provider.macaupass.gateway.all}</prop>

                <prop key="provider.weixin.wapOrMini.v3.partner.transactions.gateway.all">${provider.weixin.wapOrMini.v3.partner.transactions.gateway.all}</prop>



                <prop key="provider.alipay.intl.fake.gateway.all">${provider.alipay.intl.fake.gateway.all}</prop>
                <prop key="provider.alipay.overseas.fake.gateway.all">${provider.alipay.overseas.fake.gateway.all}</prop>
                <prop key="provider.alipay.v1.fake.gateway.all">${provider.alipay.v1.fake.gateway.all}</prop>
                <prop key="provider.alipay.v2.fake.gateway.all">${provider.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.alipay.wap.fake.gateway.all">${provider.alipay.wap.fake.gateway.all}</prop>
                <prop key="provider.alipay.wap.v2.fake.gateway.all">${provider.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.alipay.fitness.fake.gateway.all">${provider.alipay.fitness.fake.gateway.all}</prop>
                <prop key="provider.bestpay.fake.gateway.all">${provider.bestpay.fake.gateway.all}</prop>
                <prop key="provider.bestpay.v2.fake.gateway.all">${provider.bestpay.v2.fake.gateway.all}</prop>
                <prop key="provider.chinaums.fake.gateway.all">${provider.chinaums.fake.gateway.all}</prop>
                <prop key="provider.chinaums.v1.fake.gateway.all">${provider.chinaums.v1.fake.gateway.all}</prop>
                <prop key="provider.chinaums.epay.fake.gateway.all">${provider.chinaums.epay.fake.gateway.all}</prop>
                <prop key="provider.cibbank.fake.gateway.all">${provider.cibbank.fake.gateway.all}</prop>
                <prop key="provider.citicbank.fake.gateway.all">${provider.citicbank.fake.gateway.all}</prop>
                <prop key="provider.cmcc.fake.gateway.all">${provider.cmcc.fake.gateway.all}</prop>
                <prop key="provider.direct.unionpay.alipay.v2.fake.gateway.all">${provider.direct.unionpay.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.direct.unionpay.alipay.wap.v2.fake.gateway.all">${provider.direct.unionpay.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.direct.unionpay.weixin.fake.gateway.all">${provider.direct.unionpay.weixin.fake.gateway.all}</prop>
                <prop key="provider.direct.unionpay.weixin.wapOrMini.fake.gateway.all">${provider.direct.unionpay.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.alipay.v2.fake.gateway.all">${provider.lkl.unionpay.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.alipay.wap.v2.fake.gateway.all">${provider.lkl.unionpay.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.weixin.fake.gateway.all">${provider.lkl.unionpay.weixin.fake.gateway.all}</prop>
                <prop key="provider.lkl.unionpay.weixin.wapOrMini.fake.gateway.all">${provider.lkl.unionpay.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.gift.card.fake.gateway.all">${provider.gift.card.fake.gateway.all}</prop>
                <prop key="provider.lakala.fake.gateway.all">${provider.lakala.fake.gateway.all}</prop>
                <prop key="provider.nucc.alipay.v2.fake.gateway.all">${provider.nucc.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.nucc.bestpay.fake.gateway.all">${provider.nucc.bestpay.fake.gateway.all}</prop>
                <prop key="provider.nucc.weixin.fake.gateway.all">${provider.nucc.weixin.fake.gateway.all}</prop>
                <prop key="provider.nucc.weixin.wapOrMini.fake.gateway.all">${provider.nucc.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.sodexo.fake.gateway.all">${provider.sodexo.fake.gateway.all}</prop>
                <prop key="provider.sodexo.fake.gateway.oauth">${provider.sodexo.fake.gateway.oauth}</prop>
                <prop key="provider.sodexo.wap.fake.gateway.all">${provider.sodexo.wap.fake.gateway.all}</prop>
                <prop key="provider.sodexo.wap.fake.gateway.oauth">${provider.sodexo.wap.fake.gateway.oauth}</prop>
                <prop key="provider.swiftpass.fake.gateway.all">${provider.swiftpass.fake.gateway.all}</prop>
                <prop key="provider.unionpay.alipay.v2.fake.gateway.all">${provider.unionpay.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.unionpay.alipay.wap.v2.fake.gateway.all">${provider.unionpay.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.unionpay.online.fake.gateway.all">${provider.unionpay.online.fake.gateway.all}</prop>
                <prop key="provider.unionpay.open.fake.gateway.all">${provider.unionpay.open.fake.gateway.all}</prop>
                <prop key="provider.unionpay.weixin.fake.gateway.all">${provider.unionpay.weixin.fake.gateway.all}</prop>
                <prop key="provider.unionpay.weixin.wapOrMini.fake.gateway.all">${provider.unionpay.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.weixin.fake.gateway.all">${provider.weixin.fake.gateway.all}</prop>
                <prop key="provider.weixin.hk.fake.gateway.all">${provider.weixin.hk.fake.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.fake.gateway.all">${provider.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.tl.unionpay.alipay.v2.fake.gateway.all">${provider.tl.unionpay.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.tl.unionpay.alipay.wap.v2.fake.gateway.all">${provider.tl.unionpay.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.tl.unionpay.weixin.fake.gateway.all">${provider.tl.unionpay.weixin.fake.gateway.all}</prop>
                <prop key="provider.tl.unionpay.weixin.wapOrMini.fake.gateway.all">${provider.tl.unionpay.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.tl.unionpay.union.qrcode.fake.gateway.all">${provider.tl.unionpay.union.qrcode.fake.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.fake.gateway.all">${provider.weixin.wapOrMini.v3.fake.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.palm.fake.gateway.all">${provider.weixin.wapOrMini.v3.palm.fake.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.partner.fake.gateway.all">${provider.weixin.wapOrMini.v3.partner.fake.gateway.all}</prop>
                <prop key="provider.uepay.fake.gateway.all">${provider.uepay.fake.gateway.all}</prop>
                <prop key="provider.cmb.fake.gateway.all">${provider.cmb.fake.gateway.all}</prop>
                <prop key="provider.lakala.open.fake.gateway.all">${provider.lakala.open.fake.gateway.all}</prop>
                <prop key="provider.lakala.openv3.bankcard.fake.gateway.all">${provider.lakala.openv3.gateway.all}</prop>
                <prop key="provider.lakala.openv3.phone.pos.fake.gateway.all">${provider.lakala.openv3.gateway.all}</prop>
                <prop key="provider.lakala.openv3.fake.gateway.all">${provider.lakala.openv3.fake.gateway.all}</prop>
                <prop key="provider.psbcbank.weixin.fake.gateway.all">${provider.psbcbank.weixin.fake.gateway.all}</prop>
                <prop key="provider.psbcbank.alipay.fake.gateway.all">${provider.psbcbank.alipay.fake.gateway.all}</prop>
                <prop key="provider.foxconn.fake.gateway.all">${provider.foxconn.fake.gateway.all}</prop>
                <prop key="provider.cgbbank.fake.gateway.all">${provider.cgbbank.fake.gateway.all}</prop>
                <prop key="provider.hxbank.fake.gateway.all">${provider.hxbank.fake.gateway.all}</prop>
                <prop key="provider.ccb.fake.gateway.all">${provider.ccb.fake.gateway.all}</prop>
                <prop key="provider.ccb.wap.alipay.fake.gateway.all">${provider.ccb.wap.alipay.fake.gateway.all}</prop>
                <prop key="provider.ccb.wap.weixin.fake.gateway.all">${provider.ccb.wap.weixin.fake.gateway.all}</prop>
                <prop key="provider.ccb.wap.unionpay.fake.gateway.all">${provider.ccb.wap.unionpay.fake.gateway.all}</prop>
                <prop key="provider.ccb.precreate.fake.gateway.all">${provider.ccb.precreate.fake.gateway.all}</prop>
                <prop key="provider.icbcbank.fake.gateway.all">${provider.icbcbank.fake.gateway.all}</prop>
                <prop key="provider.cmbapp.fake.gateway.pay">${provider.cmbapp.fake.gateway.pay}</prop>
                <prop key="provider.cmbapp.fake.gateway.all">${provider.cmbapp.fake.gateway.all}</prop>
                <prop key="provider.tl.syb.fake.gateway.all">${provider.tl.syb.fake.gateway.all}</prop>
                <prop key="provider.ccb.giftcard.fake.gateway.all">${provider.ccb.giftcard.fake.gateway.all}</prop>
                <prop key="provider.weixin.hkv3.fake.gateway.all">${provider.weixin.hkv3.fake.gateway.all}</prop>
                <prop key="provider.weixin.hkv3.wapOrMini.fake.gateway.all">${provider.weixin.hkv3.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.haike.unionpay.fake.gateway.all">${provider.haike.unionpay.fake.gateway.all}</prop>
                <prop key="provider.haike.unionpay.alipay.v2.fake.gateway.all">${provider.haike.unionpay.alipay.v2.fake.gateway.all}</prop>
                <prop key="provider.haike.unionpay.alipay.wap.v2.fake.gateway.all">${provider.haike.unionpay.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.haike.unionpay.weixin.fake.gateway.all">${provider.haike.unionpay.weixin.fake.gateway.all}</prop>
                <prop key="provider.haike.unionpay.weixin.wapOrMini.fake.gateway.all">${provider.haike.unionpay.weixin.wapOrMini.fake.gateway.all}</prop>
                <prop key="provider.haike.unionpay.union.qrcode.fake.gateway.all">${provider.haike.unionpay.union.qrcode.fake.gateway.all}</prop>
                <prop key="provider.alipay.pre.deposit.fake.gateway.all">${provider.alipay.wap.v2.fake.gateway.all}</prop>
                <prop key="provider.fuyou.fake.gateway.all">${provider.fuyou.fake.gateway.all}</prop>
                <prop key="provider.pab.fake.gateway.all">${provider.pab.gateway.all}</prop>
                <prop key="provider.bocom.fake.gateway.all">${provider.bocom.fake.gateway.all}</prop>
                <prop key="provider.abc.fake.gateway.all">${provider.abc.fake.gateway.all}</prop>
                <prop key="provider.lakala.openv3.fake.gateway.union.userId.query">${provider.lakala.openv3.fake.gateway.union.userId.query}</prop>
                <prop key="provider.entpay.fake.gateway.all">${provider.entpay.fake.gateway.all}</prop>
                <prop key="provider.zjtlcb.fake.gateway.all">${provider.zjtlcb.fake.gateway.all}</prop>
                <prop key="provider.fjnx.fake.gateway.all">${provider.fjnx.fake.gateway.all}</prop>
                <prop key="provider.spdb.fake.gateway.all">${provider.spdb.fake.gateway.all}</prop>
                <prop key="provider.cmbcbank.fake.gateway.all">${provider.cmbcbank.fake.gateway.all}</prop>
                <prop key="provider.jycard.fake.gateway.all">${provider.jycard.fake.gateway.all}</prop>
                <prop key="provider.jsb.fake.gateway.all">${provider.jsb.fake.gateway.all}</prop>
                <prop key="provider.lzccb.fake.gateway.all">${provider.lzccb.fake.gateway.all}</prop>
                <prop key="provider.weixin.mini.B2b.fake.gateway.all">${provider.weixin.B2b.fake.gateway.all}</prop>
                <prop key="provider.tls2p.fake.gateway.all">${provider.tl.s2p.fake.gateway.all}</prop>
                <prop key="provider.weixin.wapOrMini.v3.partner.transactions.fake.gateway.all">${provider.weixin.wapOrMini.v3.partner.transactions.fake.gateway.all}</prop>

                <prop key="provider.ztkx.fake.gateway.all">${provider.ztkx.fake.gateway.all}</prop>
                <prop key="provider.yop.fake.gateway.all">${provider.yop.fake.gateway.all}</prop>
                <prop key="provider.airwallex.fake.gateway.all">${provider.airwallex.fake.gateway.all}</prop>
                <prop key="provider.pkx.airport.fake.gateway.all">${provider.pkx.airport.fake.gateway.all}</prop>
                <prop key="provider.xzx.fake.gateway.all">${provider.xzx.fake.gateway.all}</prop>
                <prop key="provider.hopeedu.fake.gateway.all">${provider.hopeedu.fake.gateway.all}</prop>
                <prop key="provider.guotong.fake.gateway.all">${provider.guotong.fake.gateway.all}</prop>
                <prop key="provider.psbc.fake.gateway.all">${provider.psbc.fake.gateway.all}</prop>
                <prop key="provider.wecard.fake.gateway.all">${provider.wecard.fake.gateway.all}</prop>
                <prop key="provider.macaupass.fake.gateway.all">${provider.macaupass.fake.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.fake.gateway.all">${provider.weixin.cycle.v2.fake.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.merchant.fake.gateway.all">${provider.weixin.cycle.v2.fake.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.partner.auth.fake.gateway.all">${provider.weixin.cycle.v2.fake.gateway.all}</prop>
                <prop key="provider.weixin.cycle.v2.merchant.auth.fake.gateway.all">${provider.weixin.cycle.v2.fake.gateway.all}</prop>
            </props>
        </constructor-arg>

        <constructor-arg name="defaultThreadPoolConfig">
            <props>
                <prop key=":2.concurrency">${workflow.action.alipayExecutor.concurrency}</prop>
                <prop key=":3.concurrency">${workflow.action.weixinExecutor.concurrency}</prop>
                <prop key="indirect:.concurrency">${workflow.action.indirect.concurrency}</prop>
                <prop key="1013:18.concurrency">${workflow.action.nuccBestPayExecutor.concurrency}</prop>
                <prop key="1033:2.concurrency">${workflow.action.lklUnionpayAlipayExecutor.concurrency}</prop>
                <prop key="1033:3.concurrency">${workflow.action.lklUnionpayWeixinExecutor.concurrency}</prop>
                <prop key=":101.concurrency">${workflow.action.giftCard.concurrency}</prop>
                <prop key="1017:.concurrency">${workflow.action.unionpayOpenExecutor.concurrency}</prop>
                <prop key="1018:2.concurrency">${workflow.action.chinaums.alipay.concurrency}</prop>
                <prop key="1018:3.concurrency">${workflow.action.chinaums.weixin.concurrency}</prop>
                <prop key="1018:.concurrency">${workflow.action.chinaumsExecutor.concurrency}</prop>
                <prop key="1019:.concurrency">${workflow.action.unionpayOnlineExecutor.concurrency}</prop>
                <prop key="1023:.concurrency">${workflow.action.psbcBank.concurrency}</prop>
                <prop key="1028:.concurrency">${workflow.action.hxbank.concurrency}</prop>
                <prop key="1035:2.concurrency">${workflow.action.tlsyb.alipay.concurrency}</prop>
                <prop key="1035:3.concurrency">${workflow.action.tlsyb.weixin.concurrency}</prop>
                <prop key="1037:2.concurrency">${workflow.action.haike.alipay.concurrency}</prop>
                <prop key="1037:3.concurrency">${workflow.action.haike.weixin.concurrency}</prop>
                <prop key="1010:.concurrency">${workflow.action.cmbcbank.concurrency}</prop>
            </props>
        </constructor-arg>
        <constructor-arg name="notifyPrivateNetworkConfigStr" value="${notify.private.network.hosts}"></constructor-arg>
        <constructor-arg name="region" value="${shouqianba.region}"></constructor-arg>
    </bean>

    <bean class="com.wosai.upay.helper.UpayServiceMethodInterceptor"/>
    <bean class="com.wosai.upay.helper.UpayMethodValidationPostProcessor"/>
    <bean class="com.wosai.upay.helper.UpayServicePostProcessor"/>
    <bean class="com.wosai.upay.service.QrcodeImaging">
        <property name="imagingServer" value="${qrcode.imaging.server:#{null}}"/>
    </bean>
    <bean class="com.wosai.upay.service.TimeBasedGenerator"/>
    <bean class="com.wosai.upay.service.AmqpFacade">
        <constructor-arg name="messageSendWaitMaxQueueSize" value="${message.send.wait.max.queue.size}"></constructor-arg>
        <constructor-arg name="messageSendExecutorConcurrency" value="${message.send.executor.concurrency}"></constructor-arg>
        <constructor-arg name="topic" value="${message.send.topic}"></constructor-arg>
        <constructor-arg name="tradePartitions" value="${message.send.transaction.partitions}"></constructor-arg>
        <constructor-arg name="kafkaProps">
            <props>
                <prop key="bootstrap.servers">${message.send.brokers}</prop>
                <prop key="batch.size">${message.send.batch.size}</prop>
                <prop key="acks">${message.send.acks}</prop>
                <prop key="linger.ms">${message.send.linger.ms}</prop>
                <prop key="max.block.ms">${message.send.max.block.ms}</prop>
                <prop key="schema.registry.url">${message.send.registry.url}</prop>
                <prop key="key.serializer">io.confluent.kafka.serializers.KafkaAvroSerializer</prop>
                <prop key="value.serializer">io.confluent.kafka.serializers.KafkaAvroSerializer</prop>
                <prop key="enable.idempotence">${message.send.enable.idempotence}</prop>
            </props>
        </constructor-arg>
    </bean>
    <bean class="com.wosai.upay.service.SimpleTsnGenerator">
        <property name="jdbcTemplate" ref="orderSnJdbcTemplate"></property>
        <property name="prefix" value="${ordersn.prefix}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.WorkflowManager"/>
    <bean class="com.wosai.upay.workflow.WorkflowDriver"/>
    <bean class="com.wosai.upay.workflow.UpayThreadPoolManager">
        <property name="largeDelayTime" value="${workflow.action.largeDelayTime}"/>
    </bean>

    <bean class="com.wosai.upay.service.AdminService"></bean>
    <bean class="com.wosai.mpay.api.weixin.WeixinClient">
        <property name="connectTimeout" value="${weixin.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${weixin.connection.readTimeout:5000}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.weixin.hk.WeixinHKClient">
        <property name="connectTimeout" value="${weixinhk.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${weixinhk.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.weixin.WeixinV3Client">
        <property name="connectTimeout" value="${weixinV3.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${weixinV3.connection.readTimeout:5000}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.cibbank.CIBBankClient">
        <property name="connectTimeout" value="${cibbank.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${cibbank.connection.readTimeout}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.lakala.LakalaClient">
        <property name="connectTimeout" value="${lakala.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${lakala.connection.readTimeout}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.cmcc.CMCCWalletClient">
        <property name="connectTimeout" value="${cmcc.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${cmcc.connection.readTimeout}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.NuccBestPay.NuccBestPayClient">
        <property name="connectTimeout" value="${nuccbestpay.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${nuccbestpay.connection.readTimeout}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.alipay.AlipayV1Client">
        <constructor-arg index="0" value="GBK"/>
    </bean>
    <bean class="com.wosai.mpay.api.alipay.AlipayV2NewClient">
    </bean>
    <bean class="com.wosai.mpay.api.alipay.overseas.AlipayOverseasClient">
        <property name="charset" value="GBK"/>
    </bean>
    <bean class="com.wosai.mpay.api.bestpay.BestpayClient"></bean>
    <bean class="com.wosai.mpay.api.bestpay.BestpayV2Client"></bean>

    <bean class="com.wosai.mpay.api.alipay.intl.AlipayIntlClient">
        <property name="connectTimeout" value="${alipay.intl.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${alipay.intl.connection.readTimeout}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.giftCard.GiftCardClient"/>
    <bean class="com.wosai.mpay.api.unionpayopen.UnionPayOpenClient">
        <property name="connectTimeout" value="${unionpayopen.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${unionpayopen.connection.readTimeout}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.chinaums.ChinaumsClient">
        <property name="connectTimeout" value="${chinaums.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${chinaums.connection.readTimeout}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.unionqrcode.UnionPayQrCodeClient">
        <property name="connectTimeout" value="${tl.union.qrcode.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${tl.union.qrcode.connection.readTimeout}"></property>
    </bean>

    <bean id="providerDefaultThreadPool" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="${workflow.action.executor.concurrency}"/>
    </bean>

    <bean class="com.wosai.mpay.api.unionpayonline.UnionPayOnlineClient">
        <property name="connectTimeout" value="${unionpayonline.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${unionpayonline.connection.readTimeout}"></property>
    </bean>

    <bean id="sodexoClient" class="com.wosai.mpay.api.sodexo.SodexoClient">
        <property name="connectTimeout" value="${sodexo.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${sodexo.connection.readTimeout}"></property>
    </bean>

    <bean id="uepayClient" class="com.wosai.mpay.api.uepay.UepayClient">
        <property name="connectTimeout" value="${uepay.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${uepay.connection.readTimeout}"></property>
    </bean>

    <bean id="psbcBankClient" class="com.wosai.mpay.api.psbcbank.PSBCBankClient">
        <property name="connectTimeout" value="${psbcbank.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${psbcbank.connection.readTimeout}"></property>
    </bean>

    <bean id="cgbBankClient" class="com.wosai.mpay.api.cgbbank.CGBBankClient">
        <property name="connectTimeout" value="${cgbbank.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${cgbbank.connection.readTimeout}"></property>
    </bean>

    <bean id="cmbClient" class="com.wosai.mpay.api.cmb.CmbClient">
        <property name="connectTimeout" value="${cmb.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${cmb.connection.readTimeout}"></property>
    </bean>

    <bean id="cmbAppClient" class="com.wosai.mpay.api.cmbapp.CmbAppClient">
        <property name="connectTimeout" value="${cmb.app.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${cmb.app.connection.readTimeout}"></property>
    </bean>

    <bean id="lakalaOpenClient" class="com.wosai.mpay.api.lakala.open.LakalaOpenClient">
        <property name="connectTimeout" value="${lakala.open.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${lakala.open.connection.readTimeout}"></property>
    </bean>

    <bean id="tlSybClient" class="com.wosai.mpay.api.tl.syb.TlSybClient">
        <property name="connectTimeout" value="${tl.syb.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${tl.syb.connection.readTimeout}"></property>
    </bean>

    <bean id="pabClient" class="com.wosai.mpay.api.pab.PabClient"></bean>

    <bean id="zjtlcbClient" class="com.wosai.mpay.api.zjtlcb.ZJTLCBClient"></bean>


    <bean id="fjnxClient" class="com.wosai.mpay.api.fjnx.FJNXClient"></bean>


    <bean id="spdbClient" class="com.wosai.mpay.api.spdb.SPDBClient"></bean>
    <bean id="lzccbClient" class="com.wosai.mpay.api.lzccb.LZCCBClient"></bean>
    <bean id="lzccbTokenCache" class="com.wosai.mpay.api.lzccb.LZCCBTokenCache"></bean>

    <bean id="ccbGiftCardClient" class="com.wosai.mpay.api.ccb.giftcard.CcbGiftCardClient">
        <property name="connectTimeout" value="${ccb.giftcard.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${ccb.giftcard.connection.readTimeout}"></property>
    </bean>

    <bean id="cmbcClient" class="com.wosai.mpay.api.cmbcbank.CMBCBankClient">
        <property name="connectTimeout" value="${cmbc.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${cmbc.connection.connectionTimeout}"></property>
    </bean>

    <bean id="yopClient" class="com.wosai.mpay.api.yop.YopClient">
        <property name="connectTimeout" value="${yop.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${yop.connection.readTimeout: 5000}"></property>
    </bean>

    <bean id="airwallexClient" class="com.wosai.mpay.api.airwallex.AirwallexClient">
        <property name="connectTimeout" value="${airwallex.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${airwallex.connection.readTimeout: 5000}"></property>
    </bean>

    <bean id="techTransClient" class="com.wosai.mpay.api.techtrans.TechTransClient">
        <property name="connectTimeout" value="${techtrans.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${techtrans.connection.readTimeout: 5000}"></property>
    </bean>

    <bean id="macauPassClient" class="com.wosai.mpay.api.macaupass.MacauPassClient">
        <property name="connectTimeout" value="${techtrans.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${techtrans.connection.readTimeout: 5000}"></property>
    </bean>
    <bean id="xzxClient" class="com.wosai.mpay.api.xzx.XZXClient">
        <property name="connectTimeout" value="${xzx.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${xzx.connection.readTimeout: 5000}"></property>
    </bean>

    <bean id="hopeEduClient" class="com.wosai.mpay.api.hopeedu.HopeEduClient">
        <property name="connectTimeout" value="${xzx.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${xzx.connection.readTimeout: 5000}"></property>
    </bean>
    <bean id="psbcClient" class="com.wosai.mpay.api.psbc.PSBCClient">
        <property name="connectTimeout" value="${psbc.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${psbc.connection.readTimeout: 5000}"></property>
    </bean>

    <bean id="guotongClient" class="com.wosai.mpay.api.guotong.GuotongClient">
        <property name="connectTimeout" value="${yop.connection.connectionTimeout: 1000}"></property>
        <property name="readTimeout" value="${yop.connection.readTimeout: 5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.foxconn.FoxconnClient">
        <property name="connectTimeout" value="${foxconn.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${foxconn.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.hxbank.HXBankClient">
        <property name="connectTimeout" value="${hxbank.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${hxbank.connection.readTimeout}"></property>
    </bean>

    <bean id="ccbClient" class="com.wosai.mpay.api.ccb.CcbClient">
        <property name="connectTimeout" value="${ccb.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${ccb.connection.readTimeout}"></property>
    </bean>

    <bean id="grabpayClient" class="com.wosai.mpay.api.grabpay.GrabPayClient">
        <property name="connectTimeout" value="${grabpay.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${grabpay.connection.readTimeout}"></property>
    </bean>

    <bean id="icbcClient" class="com.wosai.mpay.api.icbc.ICBCClient">
        <property name="connectTimeout" value="${icbc.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${icbc.connection.readTimeout}"></property>
    </bean>

    <bean id="abcClient" class="com.wosai.mpay.api.abc.ABCClient">
        <property name="connectTimeout" value="${abc.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${abc.connection.readTimeout}"></property>
    </bean>
    <bean class="com.wosai.mpay.api.weixin.hkv3.WeixinHKV3Client">
        <property name="connectTimeout" value="${weixinV3.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${weixinV3.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.haike.HaikeClient">
        <property name="connectTimeout" value="${haike.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${haike.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.fuyou.FuyouClient">
        <property name="connectTimeout" value="${fuyou.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${fuyou.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.fuyou.bank.FuyouBankClient">
        <property name="connectTimeout" value="${fuyoubank.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${fuyoubank.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.bocom.BOCOMClient">
        <property name="connectTimeout" value="${bocom.connection.connectionTimeout}"></property>
        <property name="readTimeout" value="${bocom.connection.readTimeout}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.entpay.EntPayClient">
        <property name="connectTimeout" value="${entpay.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${entpay.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.jycard.JyCardClient">
        <property name="connectTimeout" value="${jycard.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${jycard.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.jsbank.JSBClient">
        <property name="connectTimeout" value="${jsb.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${jsb.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.ztkx.ZTKXClient">
        <property name="connectTimeout" value="${ztkx.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${ztkx.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.mpay.api.tl.s2p.S2PClient">
        <property name="connectTimeout" value="${tl.s2p.connection.connectionTimeout:1000}"></property>
        <property name="readTimeout" value="${tl.s2p.connection.readTimeout:5000}"></property>
    </bean>

    <bean id="WecardClient" class="com.wosai.mpay.api.wecard.WecardPayClient">
        <property name="connectTimeout" value="${wecard.connection.connectionTimeout:1000}"></property>
        <property name="readTimeout" value="${wecard.connection.readTimeout:5000}"></property>
    </bean>

    <bean id="fakeClient" class="com.wosai.mpay.api.fake.FakeClient"></bean>

    <bean id="actionExecutor" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="${workflow.action.executor.concurrency}"/>
    </bean>

    <bean id="asyncThreadPool" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="${async.concurrency}"/>
    </bean>

    <bean id="clientNotifyThreadPool" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="${clientnotify.concurrency}"/>
    </bean>

    <bean id="confirmPayThreadPool" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="${confirmPay.concurrency}"/>
    </bean>

    <bean class="com.wosai.eventbus.EventBus">
        <constructor-arg name="concurrency" value="${workflow.eventbus.concurrency}"/>
        <constructor-arg name="maxQueueSizeForWarning" value="${workflow.eventbus.maxQueueSizeForWarning}"></constructor-arg>
    </bean>

    <!-- 注意：此处按照线上业务流量排序，或使用WorkflowPriority提升优先级 -->
    <bean class="com.wosai.upay.workflow.QRCodeWorkflow"/>
    <bean class="com.wosai.upay.workflow.BarcodeWorkflow"/>
    <bean class="com.wosai.upay.workflow.QRCodeCIBBankWorkflow"/>
    <bean class="com.wosai.upay.workflow.DepositBarcodeWorkflow"/>
    <bean class="com.wosai.upay.workflow.DepositQRCodeWorkflow"/>
    <bean class="com.wosai.upay.workflow.RefundOrderWorkflow"/>
    <bean class="com.wosai.upay.workflow.CancelOrderWorkflow"/>
    <bean class="com.wosai.upay.workflow.DepositConsumeOrderWorkflow"/>
    <bean class="com.wosai.upay.workflow.DepositCancelOrderWorkflow"/>
    <bean class="com.wosai.upay.workflow.BankcardRefundOrderWorkflow"/>
    <bean class="com.wosai.upay.workflow.BankcardDepositCancelWorkflow"/>
    <bean class="com.wosai.upay.workflow.BankcardDepositComsumeWorkflow"/>
    <bean class="com.wosai.upay.workflow.RefundOrderRevokeWorkflow"/>
    <bean class="com.wosai.upay.workflow.HaikeRefundOrderWorkflow"/>

    <bean class="com.wosai.upay.workflow.AlipayV1ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.AlipayWapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.DirectAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectAlipayV2PreDepositServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.NuccAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.NuccAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.UnionPayAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.UnionPayAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.AlipayOverseasServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinV2CycleServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinV2CycleMerchantServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinV2CyclePartnerAuthServiceProvider"></bean>
    <bean class="com.wosai.upay.workflow.DirectWeixinV2CycleMerchantAuthServiceProvider"></bean>

    <bean class="com.wosai.upay.workflow.NuccBestPayServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.NuccWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>

    </bean>
    <bean class="com.wosai.upay.workflow.UnionPayWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>

    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinV3PreDepositServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinV3PreDepositPalmServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinV3PartnerPreDepositServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>


    <bean class="com.wosai.upay.workflow.NuccWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>

    </bean>

    <bean class="com.wosai.upay.workflow.UnionPayWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.LakalaServiceProvider"/>
    <bean class="com.wosai.upay.workflow.LakalaOpenServiceProvider"/>
    <bean class="com.wosai.upay.workflow.LakalaPhonePosServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.LakalaOpenV3BankCardServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.LakalaOpenV3ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.TLSybServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.TLSybBankServiceProvider">
    </bean>
    <bean class="com.wosai.upay.workflow.CcbGiftCardProvider"/>
    <bean class="com.wosai.upay.workflow.CIBBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.CITICBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DefaultSwiftPassServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.WeixinHKServiceProvider"/>

    <bean class="com.wosai.upay.workflow.CMCCServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.BestpayServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.BestpayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectUnionPayWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectUnionPayWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.LklUnionPayWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.LklUnionPayWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.TLUnionPayWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.TLUnionPayWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.AlipayIntlServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectUnionPayAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectUnionPayAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.LklUnionPayAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.LklUnionPayAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.TLUnionPayAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.TLUnionPayAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.UnionPayOpenServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.ChinaumsServiceProvider"/>
    <bean class="com.wosai.upay.workflow.ChinaumsV1ServiceProvider">
       <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.UnionPayOnlineServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.TLUnionPayUnionQRCodeServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.SodexoServiceProvider"/>

    <bean class="com.wosai.upay.workflow.SodexoWapServiceProvider"/>

    <bean class="com.wosai.upay.workflow.GiftCardProvider"/>

    <bean class="com.wosai.upay.workflow.PrepaidCardProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.UepayServiceProvider"/>

    <bean class="com.wosai.upay.workflow.CmbServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.CmbAppServiceProvider">
    </bean>

    <bean class="com.wosai.upay.workflow.PSBCBankAlipayServiceProvider">
        <property name="notifyHost" value="${psbc.notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.PSBCBankWeixinServiceProvider">
        <property name="notifyHost" value="${psbc.notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.PSBCBankUnionpayServiceProvider">
        <property name="notifyHost" value="${psbc.notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.HXBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.CGBBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.GrabPayServiceProvider">
    </bean>

    <bean class="com.wosai.upay.workflow.ICBCBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.CcbServiceProvider"/>
    <bean class="com.wosai.upay.workflow.CcbAlipayWapServiceProvider"/>
    <bean class="com.wosai.upay.workflow.CcbWeixinWapServiceProvider"/>
    <bean class="com.wosai.upay.workflow.CcbUnionpayWapServiceProvider"/>
    <bean class="com.wosai.upay.workflow.ChinaumsEPayServiceProvider"/>
    <bean class="com.wosai.upay.workflow.DepositProvider"/>
    <bean class="com.wosai.upay.workflow.SqbDepositProvider"/>
    <bean class="com.wosai.upay.workflow.BOCOMServiceProvider"/>
    <bean class="com.wosai.upay.workflow.AbcBankServiceProvider"/>


    <bean class="com.wosai.upay.workflow.PabServiceProvider"/>

    <bean class="com.wosai.upay.workflow.ZJTLCBServiceProvider"/>
    <bean class="com.wosai.upay.workflow.TlS2PServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>
    <bean class="com.wosai.upay.workflow.YopServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>
    <bean class="com.wosai.upay.workflow.PkxAirportMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>
    <bean class="com.wosai.upay.workflow.XZXServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.HopeEduServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.GuotongServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.MacauPassServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>
    <bean class="com.wosai.upay.workflow.PSBCServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.SPDBServiceProvider"/>

    <bean class="com.wosai.upay.workflow.LZCCBServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <bean class="com.wosai.upay.workflow.FJNXServiceProvider"/>

    <bean class="com.wosai.upay.workflow.CMBCBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.AirwallexServiceProvider">
    </bean>

    <bean class="com.wosai.mpay.api.zjtlcb.TLCBTokenCache">
    </bean>


    <bean class="com.fasterxml.jackson.databind.ObjectMapper"/>

    <bean class="com.wosai.upay.service.SystemConfigServiceImpl"/>

    <bean class="com.lark.chatbot.LarkChatbotClient"/>

    <bean class="com.wosai.mpay.api.sodexo.SodexoTokenCache">
        <constructor-arg name="sodexoClient" ref="sodexoClient"/>
    </bean>

    <bean class="com.wosai.mpay.api.airwallex.AirwallexAccessTokenCache">
        <constructor-arg name="airwallexClient" ref="airwallexClient"/>
    </bean>

    <bean class="com.wosai.upay.workflow.FoxconnServiceProvider"/>
    <bean class="com.wosai.upay.workflow.WeixinHKV3ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>
    <bean class="com.wosai.upay.workflow.WeixinHKV3WapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.HaikeUnionPayServiceProvider"/>

    <bean class="com.wosai.upay.workflow.HaikeUnionPayAlipayV2ServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.HaikeUnionPayAlipayV2WapServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.HaikeUnionWeixinServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.HaikeUnionPayWeixinWapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.HaikeUnionPayUnionQRCodeServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.FuyouServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.EntPayServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.FuyouBankServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.ZTKXProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>


    <bean class="com.wosai.mpay.api.alipay.AlipayApiClient">
    </bean>

    <bean class="com.wosai.upay.scene.config.SceneManageConfig">
    </bean>
    <bean class="com.wosai.upay.workflow.AlipayFitnessServiceProvider">
    </bean>

    <bean class="com.wosai.upay.workflow.JyCardProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.JSBBankProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.PartnerWeixinV3WapOrMiniServiceProvider">
        <property name="notifyHost" value="${notify.host}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.DirectWeixinB2bServiceProvider">
    </bean>

    <bean class="com.wosai.mpay.api.weixin.B2b.WeixinB2bClient">
        <property name="connectTimeout" value="${weixin.B2b.connection.connectionTimeout:2000}"></property>
        <property name="readTimeout" value="${weixin.B2b.connection.readTimeout:5000}"></property>
    </bean>

    <bean class="com.wosai.upay.workflow.WecardServiceProvider">
        <property name="notifyHost" value="${notify.host}"/>
    </bean>

    <context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:default}.properties"/>

    <import resource="jdbc-dao-config.xml" />
    <import resource="redis-config.xml" />

    <bean id="upayPrepaidExceptionResolver" class="com.wosai.upay.prepaid.api.exception.UpayPrepaidExceptionResolver"/>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/support"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.SupportService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="3000"></property>
        <property name="serverName" value="core-business"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/common"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="3000"></property>
        <property name="serverName" value="core-business"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/terminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="2000"></property>
        <property name="serverName" value="core-business"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="2000"></property>
        <property name="serverName" value="core-business"/>
    </bean>

    <bean id="merchantService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/merchant" />
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService" />
        <property name="connectionTimeoutMillis" value="1000" />
        <property name="readTimeoutMillis" value="2000" />
        <property name="serverName" value="core-business"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.short-url.server}/rpc/shortUrl"></property>
        <property name="serviceInterface" value="com.wosai.shorturl.api.ShortUrlService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="2000"></property>
        <property name="serverName" value="core-business"/>
    </bean>

    <bean id="tradeConfigService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/tradeConfig" />
        <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService" />
        <property name="connectionTimeoutMillis" value="1000" />
        <property name="readTimeoutMillis" value="2000" />
        <property name="serverName" value="core-business"/>
    </bean>

    <bean id="metaService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/meta" />
        <property name="serviceInterface" value="com.wosai.upay.core.service.MetaService" />
        <property name="connectionTimeoutMillis" value="1000" />
        <property name="readTimeoutMillis" value="4000" />
        <property name="serverName" value="core-business"/>
    </bean>


    <bean id="activityUpayService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-activity.server}/rpc/activityUpay" />
        <property name="serviceInterface" value="com.wosai.upay.activity.service.ActivityUpayService" />
        <property name="connectionTimeoutMillis" value="${upay-activity.connection.connectionTimeout}"></property>
        <property name="readTimeoutMillis" value="${upay-activity.connection.readTimeout}"></property>
        <property name="serverName" value="upay-activity"/>
    </bean>

    <bean id="activityUpayForB2CService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-activity.server}/rpc/activityUpay" />
        <property name="serviceInterface" value="com.wosai.upay.activity.service.ActivityUpayService" />
        <property name="connectionTimeoutMillis" value="${upay-activity.b2c.connection.connectionTimeout}"></property>
        <property name="readTimeoutMillis" value="${upay-activity.b2c.connection.readTimeout}"></property>
        <property name="serverName" value="upay-activity"/>
    </bean>

    <bean id="activityUpayForLongTimeService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-activity.server}/rpc/activityUpay" />
        <property name="serviceInterface" value="com.wosai.upay.activity.service.ActivityUpayService" />
        <property name="connectionTimeoutMillis" value="${upay-activity.longTime.connection.connectionTimeout}"></property>
        <property name="readTimeoutMillis" value="${upay-activity.longTime.connection.readTimeout}"></property>
        <property name="serverName" value="upay-activity"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/group" />
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.GroupService" />
        <property name="connectionTimeoutMillis" value="500"></property>
        <property name="readTimeoutMillis" value="500"></property>
        <property name="serverName" value="user-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.profit-sharing.server}/rpc/sharing" />
        <property name="serviceInterface" value="com.wosai.profit.sharing.service.SharingService" />
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="6000"></property>
        <property name="objectMapper">
            <bean class="com.wosai.upay.util.ProfitSharingJsonRpcUtil" factory-method="profitSharingJsonRpcObjectMapper"></bean>
        </property>
        <property name="exceptionResolver" >
            <bean class="com.wosai.web.rpc.spring.CommonExceptionResolver"></bean>
        </property>
        <property name="serverName" value="profit-sharing"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.profit-sharing-proxy.server}/rpc/proxySharing" />
        <property name="serviceInterface" value="com.wosai.sharing.proxy.service.ProxySharingService" />
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="2000"></property>
        <property name="exceptionResolver" >
            <bean class="com.wosai.web.rpc.spring.CommonExceptionResolver"></bean>
        </property>
        <property name="serverName" value="profit-sharing-proxy"/>
    </bean>


    <bean id="walletService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-wallet.server}/rpc/walletV3" />
        <property name="serviceInterface" value="com.wosai.upay.wallet.service.WalletServiceV3" />
        <property name="serverName" value="upay-wallet"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean id="fakeWalletService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.fake-upay-wallet.server}/rpc/walletV3" />
        <property name="serviceInterface" value="com.wosai.upay.wallet.service.WalletServiceV3" />
        <property name="serverName" value="upay-wallet"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction.server}/rpc/upayorder"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.UpayOrderService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="5000"></property>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction.server}/rpc/gateway-support"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.GatewaySupportService"></property>
        <property name="connectionTimeoutMillis" value="1000"></property>
        <property name="readTimeoutMillis" value="5000"></property>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean id="prepaidVerificationService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-prepaid-card.server}/rpc/verification"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidVerificationService"/>
        <property name="exceptionResolver" ref="upayPrepaidExceptionResolver"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="upay-prepaid-card"/>
    </bean>
    <bean id="prepaidBusinessQueryService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.upay-prepaid-card.server}/rpc/business"/>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidBusinessQueryService"/>
        <property name="exceptionResolver" ref="upayPrepaidExceptionResolver"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="upay-prepaid-card"/>
    </bean>
    <bean id="fakePrepaidVerificationService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.fake-upay-prepaid-card.server}/rpc/verification"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidVerificationService"/>
        <property name="exceptionResolver" ref="upayPrepaidExceptionResolver"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="upay-prepaid-card"/>
    </bean>
    <bean id="fakePrepaidBusinessQueryService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.fake-upay-prepaid-card.server}/rpc/business"/>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidBusinessQueryService"/>
        <property name="exceptionResolver" ref="upayPrepaidExceptionResolver"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="upay-prepaid-card"/>
    </bean>
    <bean id="discountQuotaService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.trade-manage.server}/rpc/discountQuota"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.DiscountQuotaService"/>
        <property name="exceptionResolver" ref="upayPrepaidExceptionResolver"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="500"/>
        <property name="serverName" value="trade-manage"/>
    </bean>

    <bean id="customerSqbMpService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.customer-user.server}/rpc/ucUser"></property>
        <property name="serviceInterface" value="com.wosai.market.user.service.SqbMpService"/>
        <property name="exceptionResolver" ref="upayPrepaidExceptionResolver"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="500"/>
        <property name="serverName" value="customer-user"/>
    </bean>

    <bean id="sceneConfigService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.trade-manage.server}/rpc/sceneConfig"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.SceneConfigService"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="trade-manage"/>
    </bean>

    <bean id="sftSharingService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.brand-settle.server}/rpc/sharing"></property>
        <property name="serviceInterface" value="com.wosai.upay.brandsettle.service.SharingService"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="brand-settle"/>
    </bean>

    <bean id="jyCardService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.signature-proxy.server}/rpc/jycard"></property>
        <property name="serviceInterface" value="com.wosai.upay.signature.service.JyCardService"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="signature-proxy"/>
    </bean>

    <bean id="miniAppsOpenService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.mini-apps-open.server}rpc/miniApps/open" />
        <property name="serviceInterface" value="com.wosai.service.MiniAppsOpenService" />
        <property name="serverName" value="mini-apps-open"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>



    <bean id="xzxService" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.signature-proxy.server}/rpc/xzx"></property>
        <property name="serviceInterface" value="com.wosai.upay.signature.service.XZXService"/>
        <property name="connectionTimeoutMillis" value="1000"/>
        <property name="readTimeoutMillis" value="5000"/>
        <property name="serverName" value="signature-proxy"/>
    </bean>

    <!-- 接入hera后，首次下单请求后，证书加载慢导致接口超时长的问题。在没有彻底解决问题前，不要移除此代码！！！ -->
    <bean class = "com.wosai.config.HeraSm2Config" init-method="loadBouncycastleClass">
    </bean>
</beans>