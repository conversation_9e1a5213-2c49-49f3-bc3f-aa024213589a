<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="false">
    <diskStore path="java.io.tmpdir"/>

    <cache name="RsaKeyData"
           timeToLiveSeconds="0"
           maxElementsInMemory="1000"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="5"
           memoryStoreEvictionPolicy="LRU"/>

    <!-- 10分钟失效 -->
    <cache name="systemConfigContent"
           timeToLiveSeconds="60"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="5"
           memoryStoreEvictionPolicy="LRU"/>

</ehcache>
