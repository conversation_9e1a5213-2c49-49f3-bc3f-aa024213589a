package com.wosai.fsm;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MachineBuilder {
    private static final Logger logger = LoggerFactory.getLogger(MachineBuilder.class);
    private Map<StateLabel, State> states = new HashMap<StateLabel, State>();
    private State current;
    
    public MachineBuilder on(StateLabel label) {
        current = states.get(label);
        if (current == null) {
            current = new State(label, null);
            states.put(label, current);
        }
        return this;
    }

    public MachineBuilder delay(long[] delays, String giveUpResult) {
        current.setDelays(delays);
        current.setGiveUpResult(giveUpResult);
        return this;
    }

    public MachineBuilder invoke(Action action) {
        current.setAction(action);
        return this;
    }
    
    public MachineBuilder transition(String outcome, StateLabel label) {
        current.setTransition(outcome, label);
        return this;
    }
    
    public MachineBuilder inproc() {
        current.setInproc(true);
        return this;
    }
    public MachineBuilder end() {
        current.setEnd(true);
        return this;
    }

    public Machine build() {
        if (validate()) {
            return new Machine(states);
        }else{
            return null;
        }
    }
    
    public boolean validate() {
        Set<StateLabel> unknownStates = new HashSet<StateLabel>();
        for (State state: states.values()) {
            for (StateLabel label: state.targetStateLabels()) {
                if (!states.containsKey(label)) {
                    unknownStates.add(label);
                }
            }
        }
        if (!unknownStates.isEmpty()) {
            logger.error("unknown target states {}", unknownStates);
            return false;
            
        }else{
            return true;
        }
    }
}
