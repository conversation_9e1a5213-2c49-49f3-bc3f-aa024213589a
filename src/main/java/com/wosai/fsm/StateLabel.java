package com.wosai.fsm;

import java.util.HashMap;
import java.util.Map;

public class StateLabel {
    private int id;
    private String name;
    
    public StateLabel(int id, String name) {
        this.id = id;
        this.name = name;
        registry.put(id, this);
    }
    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public String toString() {
        return name + ":" + id;
    }
    
    @Override
    public int hashCode() {
        return new Integer(id).hashCode();
    }
    
    @Override
    public boolean equals(Object o) {
        return (o instanceof StateLabel) && ((StateLabel)o).getId() == id;
    }
    
    private static Map<Integer, StateLabel> registry = new HashMap<Integer, StateLabel>();
    public static StateLabel fromId(int id) {
        return registry.get(id);
    }
}
