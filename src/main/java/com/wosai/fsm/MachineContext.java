package com.wosai.fsm;

import java.util.HashMap;
import java.util.Map;

public class MachineContext {
    private Machine machine;
    private StateLabel currentStateLabel;
    private Map<StateLabel, Integer> stateCount;
    
    public MachineContext(Machine machine, StateLabel currentStateLabel) {
        this.machine = machine;
        this.currentStateLabel = currentStateLabel;
        this.stateCount = new HashMap<StateLabel, Integer>();
    }
    public Machine getMachine() {
        return machine;
    }
    public void setMachine(Machine machine) {
        this.machine = machine;
    }
    public StateLabel getCurrentStateLabel() {
        return currentStateLabel;
    }
    public void setCurrentStateLabel(StateLabel currentStateLabel) {
        this.currentStateLabel = currentStateLabel;
    }
    public State getCurrentState() {
        return machine.getState(currentStateLabel);
    }

    public boolean isDelayed() {
        return getCurrentState().isDelayed();
    }
    public int getStateIterAndInc(StateLabel label) {
        Integer iter = stateCount.get(label);
        if (iter == null) {
            iter = 0;
        }
        stateCount.put(label, iter+1);
        return iter;
    }
    public long getDelay() {
        State currentState = getCurrentState();
        int iter = getStateIterAndInc(currentStateLabel);
        long[] delays = currentState.getDelays();
        if (iter < delays.length) {
            return delays[iter];
        }else{
            return -1;
        }
    }
    
    public boolean isInproc() {
        return getCurrentState().isInproc();
    }
}
