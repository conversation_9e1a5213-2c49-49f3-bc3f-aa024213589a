package com.wosai.fsm;

import java.util.Map;

public class Machine {
    Map<StateLabel, State> states;
    
    public Machine(Map<StateLabel, State> states) {
        this.states = states;
    }

    public State getState(StateLabel label) {
        return states.get(label);
    }
    public State transition(StateLabel currentLabel, String outcome) {
        State currentState = states.get(currentLabel);
        StateLabel nextStateLabel = currentState.nextState(outcome);
        return states.get(nextStateLabel);
    }

}
