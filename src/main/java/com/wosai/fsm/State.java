package com.wosai.fsm;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class State {
    private StateLabel label;
    private boolean end;
    private Map<String, StateLabel> transitionMap = new HashMap<String, StateLabel>();
    private Action action;
    private long[] delays;
    private String giveUpResult;
    private boolean inproc;
    
    
    public State(State other) {
        this.label = other.label;
        this.end = other.end;
        this.transitionMap = other.transitionMap;
        this.action = other.action;
        this.delays = other.delays;
        this.giveUpResult = other.giveUpResult;
        this.inproc = other.inproc;
    }
    public State(StateLabel label, Action action) {
        this(label, action, false);
    }
    public State(StateLabel label, Action action, boolean end) {
        this.label = label;
        this.action = action;
        this.end = end;
    }
    
    public StateLabel getLabel() {
        return label;
    }
    public boolean isEnd() {
        return end;
    }
    public void setEnd(boolean end) {
        this.end = end;
    }
 
    public Map<String, StateLabel> getTransitionMap() {
        return transitionMap;
    }
    public void setTransitionMap(Map<String, StateLabel> transitionMap) {
        this.transitionMap = transitionMap;
    }
    
    public void setTransition(String outcome, StateLabel label) {
        this.transitionMap.put(outcome, label);
    }

    public StateLabel nextState(String outcome) {
        return transitionMap.get(outcome); 
    }

    public Action getAction() {
        return action;
    }
    public void setAction(Action action) {
        this.action = action;
    }
    
    public Collection<StateLabel> targetStateLabels() {
        return transitionMap.values();
        
    }
    public long[] getDelays() {
        return delays;
    }
    public void setDelays(long[] delays) {
        this.delays = delays;
    }
    public String getGiveUpResult() {
        return giveUpResult;
    }
    public void setGiveUpResult(String giveUpResult) {
        this.giveUpResult = giveUpResult;
    }
    public boolean isDelayed () {
        return this.delays != null;
    }
    
    public boolean isInproc() {
        return inproc;
    }
    public void setInproc(boolean inproc) {
        this.inproc = inproc;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("label: ").append(label);
        sb.append(", end: ").append(end);
        sb.append(", inproc: ").append(inproc);
        return sb.toString();
    }
}
