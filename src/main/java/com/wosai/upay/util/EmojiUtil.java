package com.wosai.upay.util;

import com.vdurmont.emoji.EmojiParser;

import java.util.ArrayList;
import java.util.List;

/***
 * @ClassName: EmojiUtil
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/12/5 14:14
 */
public class EmojiUtil {

    public static String filterEmoji(String data) {
        if (data == null) {
            return null;
        }

        return EmojiParser.removeAllEmojis(data);
    }
}
