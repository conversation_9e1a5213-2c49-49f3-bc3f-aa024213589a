package com.wosai.upay.util;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.wosai.constant.UpayConstant;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.net.GatewayUrl;
import com.wosai.net.Peer;
import com.wosai.net.Upstream;
import com.wosai.net.UpstreamPeerContextHolder;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.CsbToWapConfig;
import com.wosai.upay.service.SimpleTsnGenerator;
import com.wosai.upay.workflow.UpayThreadPoolManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


public class ApolloConfigurationCenterUtil {
    private static Logger logger = LoggerFactory.getLogger(ApolloConfigurationCenterUtil.class);
    public static final String CONFIG_NAMESPACE_PROVIDER_GATEWAY = "provider-gateway";
    public static final String CONFIG_PUBLIC_NAMESPACE_CORE_BASE = "core.base";

    public static final String DEFAULT_REGEX_KEY = "-";

    public static final String ERROR_CODE = "error_code";
    public static final String ERROR_MESSAGE = "error_message";
    public static final String ERROR_CODE_STANDARD = "error_code_standard";

    public static final String GATEWAY_OP_WAP = "wap";                                // wap方式支付
    public static final String GATEWAY_OP_REFUND_QUERY = "refund.query";            // 退款查询
    public static final String GATEWAY_OP_BSC_CANCEL = "bsccancel";                    // 主扫撤单
    public static final String GATEWAY_OP_CSB_CANCEL = "csbcancel";                    // 被扫撤单
    public static final String GATEWAY_OP_POSITIVELYQUERY = "positivelyquery";        // 查单
    public static final String GATEWAY_OP_REVOKE = "revoke";                        // nfc撤单
    public static final String GATEWAY_OP_CLOSE = "close";                            // 关单
    public static final String GATEWAY_OP_WAPORMINI = "wapOrMini";                    // wap和小程序支付
    public static final String GATEWAY_OP_OPENID_BARCODE_QUERY = "openid.barcode.query"; // 通过二维码反查用户信息
    public static final String GATEWAY_OP_AUTH = "oauth";                            //授权
    public static final String THREAD_POOL_CONCURRENCY="concurrency";
    public static final String GATEWAY_OP_WXPAYFACEAUTHINFO = "payFaceAuthInfo";     // 获取微信刷脸支付调用凭证
    public static final String GATEWAY_OP_WX_WAP = "wx.wap";
    public static final String GATEWAY_OP_ALIPAY_WAP = "alipay.wap";
	public static final String GATEWAY_OP_UNION_WAP = "union.wap";
    public static final String GATEWAY_OP_HISTORY_QUERY = "history.query";
	public static final String GATEWAY_OP_UNION_USERID_QUERY = "union.userId.query"; // 获取银联userId

    public static final String FAKE_REQUEST_ENABLE = "fake_request_enable";
    public static final String RATE_LIMITER_ENABLE = "rate_limiter_enable";
    public static final String RATE_QPS = "rate_qps";
    public static final String FAKE_DATABASE_FAST_FAIL_ENABLE = "fake_database_fast_fail_enable"; //fake数据快速失败
    public static final String KEY_SERVICE_PROVIDER_LEVEL = "service_provider_level"; // 通道级别配置

    public static final String TRADE_CACHE_ENABLE = "trade_cache_enable"; // 交易缓存开关
    public static final String TRADE_CACHE_TIMEOUT = "trade_cache_timeout"; // 交易缓存时间配置
    public static final long DEFAULT_TRADE_CACHE_TIME = 5 * 60 ; // 默认存储5分钟数据
    public static final String MCH_CHANNEL_COUPON_SUBSIDY_ENABLE = "mch_channel_coupon_subsidy_enable"; // 商户支付通道免充值优惠补贴是否开启
    public static final String UNIONPAY_MANUAL_ACCOUNT_FIX_ENABLE = "unionpay_manual_account_fix_enable";  //云闪付手工修复单边账是否开启
    public static final String NOTIFY_PRIVATE_NETWORK_CONFIG = "notify_private_network_config";  //专线网络的回调地址配置
    public static final String EVENTBUS_MULTIPLES_ENABLE = "eventbus_multiples_enable";  //eventbus分片处理
    public static final String CSB_TO_WAP_CONFIG = "csb_to_wap_config";  //c2b转wap配置

    public static final String PROVIDER_GATEWAY_CONFIG_PREFIX = "provider.";
    public static final String PROVIDER_GATEWAY_AUTO_DISABLE = "provider_gateway_auto_disable"; //是否禁用网关的自动负载均衡, 默认不禁用

    public static final String WORKFLOW_DISCARD_THRESHOLD = "workflow_discard_threshold";//workflow丢弃的的时间阈值
    public static final long DEFAULT_WORKFLOW_DISCARD_THRESHOLD = UpayConstant.MILLISECOND_OF_MINUTES;//workflow丢弃的的时间阈值

    public static final String APPLY_TRADE_COPROCESSOR = "apply_trade_coprocessor"; // 活动服务开关
    public static final String APPLY_TRADE_WOSAI_COPROCESSOR = "apply_trade_wosai_coprocessor"; // 喔噻全量商户优惠活动开关
    public static final String HX_259_PARAMS_COPROCESSOR = "hx_259_params_coprocessor"; //华夏259号文交易参数上送开关
    public static final String HX_B2C_SUB_APP_ID_COPROCESSOR = "hx_b2c_sub_app_id_coprocessor"; //华夏微信b2c subAppId参数上送开关
    public static final String TL_UNIONPAY_259_PARAMS_COPROCESSOR = "tl_unionpay_259_params_coprocessor"; //通联云闪付259参数上送开关
    public static final String CHINAUMS_259_PARAMS_COPROCESSOR = "chinaums_259_params_coprocessor"; //银商259交易参数上送开关

    public static final String MAX_REFUND_COUNT = "max_refund_count"; // 退款笔数限制
    public static final int DEFAULT_MAX_REFUND_COUNT = 100 ; // 退款笔数限制,默认100

    public static final String QUERY_HISTORY_ENABLE = "query_history_enable"; //查询是否查询历史交易

    public static final String  HISTORY_REFUND_INFO_CACHE_TIMEOUT = "history_refund_info_cache_timeout"; //历史退款交易信息缓存时间
    public static final long DEFAULT_HISTORY_REFUND_INFO_CACHE_TIMEOUT = UpayConstant.MILLISECOND_OF_DAY * 1; //历史退款交易信息缓存时间 默认1天

    public static final String HBFQ_SERVICE_CHARGE_FEE = "hb_fq_service_charge_fee"; //花呗分期手续费配置
    public static final String CREDIT_FQ_SERVICE_CHARGE_FEE = "credit_fq_service_charge_fee"; //信用卡分期手续费
    public static final String HBFQ_SHARING_PROFIT_TEMPLATE = "hbfq_sharing_profit_template"; //花呗分期分账配置
    public static final String PSBC_WAP_CODE_FLAG = "psbc_wap_code_flag"; //邮储预下单码牌标识，1-动态买牌  0-静态码牌
    public static final String GEO_UDF_APP_KEY = "geo_udf_app_key"; //高德地图APP_KEY
    public static final String DEPOSIT_CONSUME_FEE_DIFFERENCE_VALUE = "deposit_consume_fee_difference_value"; //预授权完成差异值
    public static final String JJZ_VENDOR_APP_APPID = "jjz_vendor_app_appid"; //智慧门店终端应用id
    public static final String JJZ_FEE_RATE_UP = "jjz_fee_rate_up";      //智慧门店费率上浮值配置
    public static final String DCEP_PATTERN = "dcep_pattern"; // 数字人民币条码规则（因各银行的条码规则不一样，需要动态变更）
    public static final String MPAY_PATTERN = "mpay_pattern"; // 澳门通MPAY条码规则（后期可能会变化，需要动态变更）
    public static final String SEND_WEIXIN_INDIRECT_DETAIL_FIELD = "send_weixin_indirect_detail_field"; // 间连交易是否上送detail字段

    public static final int APOLLO_KEY_MAX_SIZE = 128;      //apollo key max size = 128
    public static final String PROVIDER_QUERY_EXPIRE = "provider_query_expire"; //订单状态为PAY_ERROR时，查单超过指定时间后，状态修改为PAY_CANCELED
    public static final String CUT_ICBC_IPV6 = "CUT_ICBC_IPV6"; //由于工行暂时不支持ipv6,故做127.0.0.1替换
    public static final long DEFAULT_PROVIDER_QUERY_EXPIRE_TIME = 1; // 默认为1小时
    public static final String UNIONPAY_SM2_SIGN_PARAMS = "unionpay_sm2_sign_params"; //银联sm2签名参数

	public static final String H5_OR_APP_HANDLE_CUSTOMIZED_PARAMS_ENABLE = "h5_or_app_handle_customized_params_enable"; //支付宝h5或app交易是否支持定制化参数
	public static final String IS_SEND_MERCHANT_POI = "is_send_merchant_poi";

    public static final String HAI_MA_VENDOR_APP_ID = "hai_ma_vendor_app_id";
    public static final String KEY_WX_APP_H5_NOT_SHARING_MERCHANTS = "wx_app_h5_not_sharing_merchants";
    public static final String FUYOU_FEE_RATE_TEMPLATE = "fuyou_fee_rate_template"; //富友通道手续费扣率模板
    public static final String SCENE_CONFIG_NEED_UPDATE_FLAG = "scene_config_need_update_flag";
    public static final String NOT_CHECK_TRADE_APPS = "not_check_trade_apps"; //不校验trade_app的名单，字符串格式 json格式
    public static final String UNION_OVERSEAS_WALLET = "union_overseas_wallet";
    public static final String BASIC_PAYMENT_TRADE_APPS = "basic_payment_trade_apps";//基础支付业务trade_apps
    public static final String PRODUCT_FLAGS_CONFIG = "product_flags_config"; //product_flag映射配置，{(trade_app| trade_app&sqb_biz_model | trade_app&terminal_category | trade_app&sub_payway) -> product_flag}
    public static final String TRADE_PARAMS_REPLACE = "trade_params_replace"; // 交易参数更换
    public static final String TSN_GENERATOR_BATCH_COUNT = "tsn_generator_batch_count"; // 订单号生成批次单号

    public static final String ZJTL_DECRYPT_PARAMS = "zjtl_decrypt_params"; // 浙江泰隆解密参数

    public static final String ZTKX_WECHAT_SUBAPPID_ENABLE = "ztkx_wechat_subappid_enable"; // 中投科信微信预下单传SubAppid开关

    public static final String SPDB_DECRYPT_PARAMS = "spdb_decrypt_params"; // 浦发银行解密参数


    public static final String PSBC_DECRYPT_PARAMS = "psbc_decrypt_params"; // 邮储银行解密参数


    public static final String KEY_T9_VENDOR_APP_ID_PROVIDER_MAPPING = "t9_vendor_app_id_provider_mapping";

    public static final String KEY_SUPPORT_CHANGE_FEE_FLAG = "support_change_fee_flag"; //是否允许修改手续费

    private static final String KEY_PHONE_POS_TRADE_APP = "phone_pos_trade_app"; //手机pos专用的trade_app 生产跟测试环境不一致
    private static final String KEY_WILD_CARD_TRADE_APP = "wild_card_trade_app";
    private static final String KEY_DOMESTIC_CARD_TRADE_APP = "domestic_card_trade_app";

    public static final String KEY_HAIKE_UNION_B2C_TRADE_DELAY_FOR_QUERY = "haike_union_b2c_trade_delay_for_query"; //是否允许海科云闪付b2c交易延迟3秒走查单
    public static final String KEY_UNION_B2C_TRADE_DELAY_FOR_QUERY = "union_b2c_trade_delay_for_query"; //是否允许云闪付b2c交易延迟走查单
    public static final String KEY_UNION_B2C_TRADE_DELAY_FOR_QUERY_MILLS = "union_b2c_trade_delay_for_query_mills"; //云闪付b2c交易延迟时间，毫秒
    public static final long KEY_DEFAULT_UNION_B2C_TRADE_DELAY_FOR_QUERY_MILLS = 3000L; //默认云闪付b2c交易延迟时间，3秒

    public static final String KEY_IS_EP99_APPEND_PROVIDER_MESSAGE = "IS_EP99_APPEND_PROVIDER_MESSAGE"; //是否允许EP99追加通道侧错误信息

    public static final String KEY_IS_WEIXIN_HX_SCENE_INFO = "IS_WEIXIN_HX_SCENE_INFO"; //微信香港是否上送场景信息
    public static final String KEY_FIX_WITHOUT_PROVIDER_QUERY_ORDER_SN = "fix_without_provider_query_order_sn";

    private static final String KEY_WEIXIN_B2b_NOTIFY_SIGN_TOKEN = "weixin_B2b_notify_sign_token"; //微信B2b通知签名token
    private static final String CMBC_PAY_IN_PROG_DELAY_MS = "cmbc_pay_in_prog_delay_ms"; //民生银行支付中状态延迟毫秒

    public static final String AIRWALLEX_TPK = "airwallex_tpk"; // Aiewallex tpk
    public static final String GRAY_SEND_ATTACH = "gray_send_attach"; // attach上送比例
    public static final String GRAY_SEND_FUND_BILL_LIST = "gray_send_fund_bill_list"; // query_options 上送比例

    public static List<String> skipEnAndDecryptServers = Lists.newArrayList("http://grandet", "https://grandet", "http://eugenie", "https://eugenie", "http://grandet.internal.shouqianba.com", "https://grandet.internal.shouqianba.com");
    public static Set<String> withoutProviderQueryOrderSns = new HashSet<>();

    private static final Map<String, Map<String, Object>> PROVIDER_ERRORMESSAGE_CACHE = new ConcurrentHashMap<String, Map<String, Object>>();
    private static final Map<String,String> KEY_PROVIDER_ERRORMESSAGE = new ConcurrentHashMap<String,String>();
    private static final Map<String, Upstream> upstreamConfigMap = new ConcurrentHashMap<>();
    private static final Map<String, Map<String, Object>> HBFQ_SERVICE_CHARGE_FEE_CACHE = new ConcurrentHashMap<String, Map<String, Object>>();
    private static final Map<String, Map<String, Object>> CREDIT_FQ_CHARGE_FEE_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, Object> HBFQ_SHARING_PROFIT_TEMPLATE_CACHE = new ConcurrentHashMap<String, Object>();
    private static Map<String, Object> queryExpireTimeMap = new ConcurrentHashMap<>();
    private static Map<String, Object> unionpaySm2SignParams = new ConcurrentHashMap<>();
    private static Map<String, Object> fuyouFeeRateTemplateMap = new ConcurrentHashMap<>();
    private static List<String> notCheckTradeApps = new ArrayList<>();

    private static List<String> unionOverseasWallet = new ArrayList<>();
    private static List<String> basicPaymentTradeApps = new ArrayList<>();
    private static Map<String, String> productFlagsConfig = new HashMap<>();
    private static Map<String, List<Map<String, Objects>>> tradeParamsReplaceConfig = new ConcurrentHashMap<>();
    private static Map<String, Map<String, Object>> signKeySwitchMap = new ConcurrentHashMap<>();
    private static Map<String, List<Integer>> vendorT9ProviderMap = new ConcurrentHashMap<>();

    private static Map<String, String> zjtlDecryptParamsMap = new HashMap<>();


    private static Map<String, String> spdbDecryptParamsMap = new HashMap<>();


    private static Map<String, String> psbcDecryptParamsMap = new HashMap<>();



    private static Config config = null;
    private static Config providerGatewayConfig = null;
    private static Config coreBaseConfig = null;
    public static Map<String, String> defaultGateway;
    public static RateLimiter rateLimiter = RateLimiter.create(500);
    public static Map<String, Integer> defaultThreadPoolConfig;
    private static volatile boolean isMchChannelCouponSubsidy;
    private static volatile boolean isSupportUnionpayManualFix;
    private static volatile boolean providerGatewayAutoDisable;
    private static volatile boolean isEventBusMultiples;
    private static volatile boolean isTradeCache;
    private static volatile boolean isApplyTradeCoprocessor;
    private static volatile boolean isApplyTradeWosaiCoprocessor;
    private static volatile boolean isSendHX259Params;
    private static volatile boolean isSendTLUnionPay259Params;
    private static volatile boolean isSendChinaums259Params;
    private static volatile boolean isSupportZtkxWechatSubAppid;

    private static volatile boolean isWeixinHxSceneInfo;

    public static Map<String,String> notifyPrivateNetworkConfig = new HashMap<>(); //专网回调地址配置 {host : notifyHost}
    public static CsbToWapConfig csbToWapConfig = new CsbToWapConfig();
    private static String region = null;

    private static List<String> haiMaVendorAppId = null;
    private static Set<String> wxNotSharingMerchantSet = new HashSet<>();

    private static Map<Integer, Integer> graySendAttach = new HashMap<>();

    public ApolloConfigurationCenterUtil(Map<String, String> defaultGateway, Map<String, Integer> defaultThreadPoolConfig, String notifyPrivateNetworkConfigStr, String region){
        this.defaultGateway = defaultGateway;
        this.defaultThreadPoolConfig = defaultThreadPoolConfig;
        this.region = region;
        config = ConfigService.getAppConfig();
        notifyPrivateNetworkConfigStr = config.getProperty(NOTIFY_PRIVATE_NETWORK_CONFIG, notifyPrivateNetworkConfigStr);
        if(!StringUtil.empty(notifyPrivateNetworkConfigStr)){
            this.notifyPrivateNetworkConfig = JSON.parseObject(notifyPrivateNetworkConfigStr, Map.class);
        }
        providerGatewayConfig = ConfigService.getConfig(CONFIG_NAMESPACE_PROVIDER_GATEWAY);
        coreBaseConfig = ConfigService.getConfig(CONFIG_PUBLIC_NAMESPACE_CORE_BASE);
        rateLimiter = RateLimiter.create(config.getIntProperty(RATE_QPS,500));
        config.addChangeListener(changeEvent -> {
            Set<String> changedKeys = changeEvent.changedKeys();
            changedKeys.forEach(key -> {
                if (RATE_QPS.equalsIgnoreCase(key)) {
                    //目的是在配置中心修改了qps限流后,及时通知到系统
                    rateLimiter = RateLimiter.create(config.getDoubleProperty(RATE_QPS,  500D));
                }
            });
        });
        config.addChangeListener(changeEvent -> {
            Set<String> changedKeys = changeEvent.changedKeys();
            changedKeys.forEach(key -> {
                if (key.contains(THREAD_POOL_CONCURRENCY)) {
                    try {
                        Integer concurrency = config.getIntProperty(key, null);
                        UpayThreadPoolManager.changeThreadPoolSize(key.split("."+THREAD_POOL_CONCURRENCY)[0],concurrency);
                    }catch (Exception ex){
                        logger.error("change thread pool size fail:{}",key, ex);
                    }
                }
            });
        });
        RateLimiterUtil.init();
        isMchChannelCouponSubsidy = config.getBooleanProperty(MCH_CHANNEL_COUPON_SUBSIDY_ENABLE, false);
        config.addChangeListener(changeEvent -> {
            if(changeEvent.changedKeys().contains(MCH_CHANNEL_COUPON_SUBSIDY_ENABLE)){
                isMchChannelCouponSubsidy = config.getBooleanProperty(MCH_CHANNEL_COUPON_SUBSIDY_ENABLE, false);
            }
        });

        isSupportZtkxWechatSubAppid = config.getBooleanProperty(ZTKX_WECHAT_SUBAPPID_ENABLE, true);
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(ZTKX_WECHAT_SUBAPPID_ENABLE)) {
                isSupportZtkxWechatSubAppid = config.getBooleanProperty(ZTKX_WECHAT_SUBAPPID_ENABLE, true);
            }
        });

        isSupportUnionpayManualFix = config.getBooleanProperty(UNIONPAY_MANUAL_ACCOUNT_FIX_ENABLE, false);
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(UNIONPAY_MANUAL_ACCOUNT_FIX_ENABLE)) {
                isSupportUnionpayManualFix = config.getBooleanProperty(UNIONPAY_MANUAL_ACCOUNT_FIX_ENABLE, false);
            }
        });
        providerGatewayConfig.addChangeListener(changeEvent -> {
            if(changeEvent == null){
                return;
            }
            String regionProviderGatewayConfigPrefix = region + ":" + PROVIDER_GATEWAY_CONFIG_PREFIX;
            //remove changed provider gateway config
            for(String key: changeEvent.changedKeys()){
                if(key.startsWith(PROVIDER_GATEWAY_CONFIG_PREFIX) || key.startsWith(regionProviderGatewayConfigPrefix)){
                    //test value is valid
                    String gatewayConfig = providerGatewayConfig.getProperty(key, null);
                    if(gatewayConfig != null && gatewayConfig.startsWith("{")){
                        Upstream upstream = JSON.parseObject(gatewayConfig.replace("servers", "peers"), Upstream.class);
                        if(upstream.getPeers().size() == 0){
                            logger.warn("provider gateway config is not valid, please to check: {}", gatewayConfig.replaceAll("\n", ""));
                            continue;
                        }
                    }
                    // 删除当前分区的特殊配置
                    if(key.startsWith(regionProviderGatewayConfigPrefix)) {
                        key = key.replace(region + ":", "");
                    }
                    upstreamConfigMap.remove(key);
                }
            }
        });
        providerGatewayAutoDisable = providerGatewayConfig.getBooleanProperty(PROVIDER_GATEWAY_AUTO_DISABLE, false);
        providerGatewayConfig.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(PROVIDER_GATEWAY_AUTO_DISABLE)){
                providerGatewayAutoDisable = providerGatewayConfig.getBooleanProperty(PROVIDER_GATEWAY_AUTO_DISABLE, false);
                upstreamConfigMap.clear();
            }
        });

        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(NOTIFY_PRIVATE_NETWORK_CONFIG)){
                String value = config.getProperty(NOTIFY_PRIVATE_NETWORK_CONFIG, null);
                if(!StringUtil.empty(value)){
                    notifyPrivateNetworkConfig = JSON.parseObject(value, Map.class);
                }
            }
        });

        isEventBusMultiples = config.getBooleanProperty(EVENTBUS_MULTIPLES_ENABLE, false);
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.changedKeys().contains(EVENTBUS_MULTIPLES_ENABLE)){
                isEventBusMultiples = config.getBooleanProperty(EVENTBUS_MULTIPLES_ENABLE, false);
            }
        });

        isTradeCache = config.getBooleanProperty(TRADE_CACHE_ENABLE, false);
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.changedKeys().contains(TRADE_CACHE_ENABLE)){
                isTradeCache = config.getBooleanProperty(TRADE_CACHE_ENABLE, false);
            }
        });

        String csbToWapConfigString = config.getProperty(CSB_TO_WAP_CONFIG, null);
        if(!StringUtil.empty(csbToWapConfigString)){
            csbToWapConfig = JSON.parseObject(csbToWapConfigString, CsbToWapConfig.class);
        }
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(CSB_TO_WAP_CONFIG)) {
                csbToWapConfig = JSON.parseObject(config.getProperty(CSB_TO_WAP_CONFIG, null), CsbToWapConfig.class);
            }
        });
        
        isApplyTradeCoprocessor = config.getBooleanProperty(APPLY_TRADE_COPROCESSOR, false);
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(APPLY_TRADE_COPROCESSOR)){
                isApplyTradeCoprocessor = config.getBooleanProperty(APPLY_TRADE_COPROCESSOR, false);
            }
        });
        
        isApplyTradeWosaiCoprocessor = config.getBooleanProperty(APPLY_TRADE_WOSAI_COPROCESSOR, false);
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(APPLY_TRADE_WOSAI_COPROCESSOR)){
                isApplyTradeWosaiCoprocessor = config.getBooleanProperty(APPLY_TRADE_WOSAI_COPROCESSOR, false);
            }
        });

        isSendHX259Params = config.getBooleanProperty(HX_259_PARAMS_COPROCESSOR, true);
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(HX_259_PARAMS_COPROCESSOR)) {
                isSendHX259Params = config.getBooleanProperty(HX_259_PARAMS_COPROCESSOR, true);
            }
        });

        isSendTLUnionPay259Params = config.getBooleanProperty(TL_UNIONPAY_259_PARAMS_COPROCESSOR, true);
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(TL_UNIONPAY_259_PARAMS_COPROCESSOR)) {
                isSendTLUnionPay259Params = config.getBooleanProperty(TL_UNIONPAY_259_PARAMS_COPROCESSOR, true);
            }
        });

        isSendChinaums259Params = config.getBooleanProperty(CHINAUMS_259_PARAMS_COPROCESSOR, true);
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(CHINAUMS_259_PARAMS_COPROCESSOR)) {
                isSendChinaums259Params = config.getBooleanProperty(CHINAUMS_259_PARAMS_COPROCESSOR, true);
            }
        });
        
        loadHbfqServiceChargeFee();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(HBFQ_SERVICE_CHARGE_FEE)) {
                loadHbfqServiceChargeFee();
            }
        });

        loadCreditFqServiceChargeFee();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(CREDIT_FQ_SERVICE_CHARGE_FEE)) {
                loadCreditFqServiceChargeFee();
            }
        });

        loadHbfqProfitSharingTemplate();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(HBFQ_SHARING_PROFIT_TEMPLATE)) {
                loadHbfqProfitSharingTemplate();
            }
        });
        loadDcepPattern();
        config.addChangeListener(changeEvent ->{
            if(changeEvent != null && changeEvent.isChanged(DCEP_PATTERN)) {
                loadDcepPattern();
            }
        });
        loadMPayPattern();
        config.addChangeListener(changeEvent ->{
            if(changeEvent != null && changeEvent.isChanged(MPAY_PATTERN)) {
                loadMPayPattern();
            }
        });
        loadProviderQueryExpireTime();
        config.addChangeListener(changeEvent -> {
            Set<String> changedKeys = changeEvent.changedKeys();
            changedKeys.forEach(key -> {
                if (PROVIDER_QUERY_EXPIRE.equalsIgnoreCase(key)) {
                    loadProviderQueryExpireTime();
                }
            });
        });
        loadUnionpaySm2SignParams();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(UNIONPAY_SM2_SIGN_PARAMS)) {
                loadUnionpaySm2SignParams();
            }
        });
        buildHaiMaAppid();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(HAI_MA_VENDOR_APP_ID)) {
                buildHaiMaAppid();
            }
        });
        loadWxNotSharingMerchantSet();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(KEY_WX_APP_H5_NOT_SHARING_MERCHANTS)) {
                loadWxNotSharingMerchantSet();
            }
        });
        loadNotCheckTradeApps();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(NOT_CHECK_TRADE_APPS)) {
                loadNotCheckTradeApps();
            }
        });
        loadBasicPaymentTradeApps();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(BASIC_PAYMENT_TRADE_APPS)) {
                loadBasicPaymentTradeApps();

            }
        });
        loadProductFlagsConfig();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(PRODUCT_FLAGS_CONFIG)) {
                loadProductFlagsConfig();
            }

        });
        loadUnionOverseasWallet();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(UNION_OVERSEAS_WALLET)) {
                loadUnionOverseasWallet();

            }
        });
        loadTradeParameterReplace();
        coreBaseConfig.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(TRADE_PARAMS_REPLACE)) {
                loadTradeParameterReplace();
            }
        });
        loadFuyouFeeRateTemplate();
        coreBaseConfig.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(FUYOU_FEE_RATE_TEMPLATE)) {
                loadFuyouFeeRateTemplate();
            }
        });
        loadVendorT9ProviderMap();
        coreBaseConfig.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(KEY_T9_VENDOR_APP_ID_PROVIDER_MAPPING)) {
                loadVendorT9ProviderMap();
            }
        });
        loadTsnGeneratorBatchCount();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(TSN_GENERATOR_BATCH_COUNT)) {
                loadTsnGeneratorBatchCount();
            }
        });
        loadZJTLDecryptParams();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(ZJTL_DECRYPT_PARAMS)) {
                loadZJTLDecryptParams();
            }
        });

        loadSPDBDecryptParams();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(SPDB_DECRYPT_PARAMS)) {
                loadSPDBDecryptParams();
            }
        });

        loadPSBCDecryptParams();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(PSBC_DECRYPT_PARAMS)) {
                loadPSBCDecryptParams();
            }
        });

        isWeixinHxSceneInfo = config.getBooleanProperty(KEY_IS_WEIXIN_HX_SCENE_INFO, false);
        config.addChangeListener(changeEvent -> {
            if (changeEvent != null && changeEvent.isChanged(KEY_IS_WEIXIN_HX_SCENE_INFO)) {
                isWeixinHxSceneInfo = config.getBooleanProperty(KEY_IS_WEIXIN_HX_SCENE_INFO, false);
            }
        });

        loadFixWithoutProviderQueryOrderSet();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(KEY_FIX_WITHOUT_PROVIDER_QUERY_ORDER_SN)) {
                loadFixWithoutProviderQueryOrderSet();
            }
        });

        loadGraySendAttach();
        config.addChangeListener(changeEvent -> {
            if(changeEvent != null && changeEvent.isChanged(GRAY_SEND_ATTACH)) {
                loadGraySendAttach();
            }
        });
    }

    private static void loadTsnGeneratorBatchCount() {
        long newCount = config.getLongProperty(TSN_GENERATOR_BATCH_COUNT, SimpleTsnGenerator.DEFAULT_BATCH_COUNT);
        // apollo 配置只能调整为比默认值大，防止误操作
        if (newCount >= SimpleTsnGenerator.DEFAULT_BATCH_COUNT) {
            SimpleTsnGenerator.updateBatchCount(newCount);
        }
    }

    private static void loadZJTLDecryptParams() {
        String zjtlDecryptParamsStr = config.getProperty(ZJTL_DECRYPT_PARAMS, "{}");
        zjtlDecryptParamsMap = JsonUtil.jsonStrToObject(zjtlDecryptParamsStr, Map.class);

    }

    public static Map<String, String> getZJTLDecryptParams() {
        return zjtlDecryptParamsMap;
    }

    private static void loadSPDBDecryptParams() {
        String spdbDecryptParamsStr = config.getProperty(SPDB_DECRYPT_PARAMS, "{}");
        spdbDecryptParamsMap = JsonUtil.jsonStrToObject(spdbDecryptParamsStr, Map.class);

    }


    public static Map<String, String> getPSBCDecryptParams() {
        return psbcDecryptParamsMap;
    }


    private static void loadPSBCDecryptParams() {
        String psbcDecryptParamsStr = config.getProperty(PSBC_DECRYPT_PARAMS, "{}");
        psbcDecryptParamsMap = JsonUtil.jsonStrToObject(psbcDecryptParamsStr, Map.class);
    }



    public static Map<String, String> getSpdbDecryptParamsMap() {
        return spdbDecryptParamsMap;
    }


    private static void loadUnionpaySm2SignParams() {
        String changeValue = config.getProperty(UNIONPAY_SM2_SIGN_PARAMS, "{}");
        unionpaySm2SignParams = JsonUtil.jsonStrToObject(changeValue, Map.class);
    }

    private static void loadDcepPattern() {
        UpayUtil.changeDcepPattern(config.getProperty(DCEP_PATTERN, "01\\d{17}"));
    }

    private static void loadMPayPattern() {
        UpayUtil.changeMPayPattern(config.getProperty(MPAY_PATTERN, "88\\d{18}"));
    }



    /**
     *  获取支付通道配置的网关地址
     * @param providerName
     * @param op
     * @return
     */
    public static GatewayUrl getProviderGatewayUrl(String providerName, String op){
        boolean isFake = FakeRequestUtil.isFakeRequest();
        String key = getUpstreamConfigKey(providerName, isFake, op);
        Upstream upstream = upstreamConfigMap.get(key);
        if(upstream == null){
            synchronized (key.intern()){
                upstream = upstreamConfigMap.get(key);
                if(upstream == null){
                    String gatewayConfig = getProviderGatewayConfig(providerName, op);
                    // 不同区域的配置不同，优先采用区域配置
                    if(!StringUtil.empty(region)) {
                        String regionGatewayConfig = getProviderGatewayConfig(region + ":" + providerName, op);
                        if(!StringUtil.empty(regionGatewayConfig)) {
                            gatewayConfig = regionGatewayConfig;
                        }
                    }
                    //构造Upstream
                    upstream = new Upstream();
                    if(gatewayConfig == null || !gatewayConfig.startsWith("{")){
                        //简单配置
                        Peer peer = new Peer(gatewayConfig, 100);
                        upstream.setPeers(Arrays.asList(peer));
                    }else{
                        //复杂多地址配置，json格式字符串
                        upstream = JSON.parseObject(gatewayConfig.replace("servers", "peers"), Upstream.class);
                    }
                    upstream.getPeers().forEach(item -> {
                        String server = item.getServer();
                        boolean skip = skipEnAndDecryptServers.stream().anyMatch(server::startsWith);
                        item.setSkipEnAndDecrypt(skip);
                    });
                    upstreamConfigMap.put(key, upstream);

                }
            }
        }
        //获取配置的地址或者域名
        UpstreamPeerContextHolder.remove();
        Peer peer = upstream.getPeer();
        if(peer == null){
            throw new RuntimeException("支付通道地址没有配置");
        }
        UpstreamPeerContextHolder.setContextHolder(upstream, peer);
        //获取真正的网关url地址
        return ProviderGatewayUtil.getGatewayUrl(providerName, peer.getServer(), peer.isSkipEnAndDecrypt(), op);
    }

    /**
     *  获取支付通道配置的网关地址
     * @param providerName
     * @param op
     * @return
     */
    public static String getProviderGateway(String providerName, String op){
        GatewayUrl providerGatewayUrl = getProviderGatewayUrl(providerName, op);
        return providerGatewayUrl.getUrl();
    }

    /**
     * @param providerName
     * @param isFake
     * @param op
     * @return
     */
    public static String getUpstreamConfigKey(String providerName, boolean isFake, String op){
        String opKey = providerName + (isFake ? ".fake" : "") + ".gateway." +  op; //like provider.alipay.v2.gateway.all.refund or provider.alipay.v2.fake.gateway.all.refund
        String allKey = providerName + (isFake ? ".fake" : "") + ".gateway.all"; // like provider.alipay.v2.gateway.all  or like provider.alipay.v2.fake.gateway.all
        //特定接口有特殊域名配置的话，那么用独立的配置，如果没有的话，共用同一个
        return defaultGateway.containsKey(opKey) ? opKey : allKey;
    }



    /**
     *  获取支付通道配置的网关地址配置
     * @param providerName 支付通道名称
     * @param op 网关操作  query：查单 refund：退款 pay：支付 precreate：预下单 close:关单
     *
     * @return String 网关地址或者域名
     *
     * @throws Exception
     */
    public static String getProviderGatewayConfig(String providerName, String op){
        String matchKey = getUpstreamConfigKey(providerName, FakeRequestUtil.isFakeRequest(), op);
        String gateway = providerGatewayConfig.getProperty(matchKey, null);
        if(StringUtils.isEmpty(gateway)){
            gateway = defaultGateway.get(matchKey);
        }
        if(gateway != null){
            gateway = gateway.trim();
        }
        return gateway;
    }

    /**
     * 获取回调地址，主要是为了处理支付通道专线接入后需要特定的专线回调地址。
     * @param defaultHost
     * @param payUrl
     * @return
     */
    public static String getNotifyHost(String defaultHost, String payUrl){
        if(payUrl == null){
            return defaultHost;
        }else{
            for(String host: notifyPrivateNetworkConfig.keySet()){
                if(payUrl.contains(host)){
                    return notifyPrivateNetworkConfig.get(host);
                }
            }
            return defaultHost;
        }
    }

    public static boolean getFakeRequestEnable(){
        return config.getBooleanProperty(FAKE_REQUEST_ENABLE, false);
    }

    public static boolean getRateLimiterEnable(){
        return config.getBooleanProperty(RATE_LIMITER_ENABLE, false);
    }

    public static boolean getFakeDatabaseFastFail(){
        return config.getBooleanProperty(FAKE_DATABASE_FAST_FAIL_ENABLE, true);
    }

    public static boolean getQueryHistoryEnable(){
        return config.getBooleanProperty(QUERY_HISTORY_ENABLE, false);
    }

	public static boolean getH5OrAppHandleCustomizedParamsEnable(){
		return config.getBooleanProperty(H5_OR_APP_HANDLE_CUSTOMIZED_PARAMS_ENABLE, true);
	}

    public static boolean isSendMerchantPoi(){
        return config.getBooleanProperty(IS_SEND_MERCHANT_POI, false);
    }

    public static int getCMBCPayInProgDelayMs() {
        return config.getIntProperty(CMBC_PAY_IN_PROG_DELAY_MS, -1);
    }

    public Integer getProviderThreadPoolConfig(String key){
		String matchKey = String.format("%s.concurrency", key);
		String concurrency = config.getProperty(matchKey, null);
		if (!StringUtils.isEmpty(concurrency)){
			try {
				return Integer.parseInt(concurrency);
			}catch (Exception ex){
				return defaultThreadPoolConfig.get(matchKey);
			}
		}
		return defaultThreadPoolConfig.get(matchKey);
	}

    public static boolean isTradeCacheEnable() {
        return isTradeCache;
    }

    public static long getTradeCacheTimeout() {
        return config.getLongProperty(TRADE_CACHE_TIMEOUT, DEFAULT_TRADE_CACHE_TIME);
    }

    public static long getWorkflowDiscardThreshold(){
        return config.getLongProperty(WORKFLOW_DISCARD_THRESHOLD, DEFAULT_WORKFLOW_DISCARD_THRESHOLD);
    }

    public static boolean isMchChannelCouponSubsidy(){
        return isMchChannelCouponSubsidy;
    }

    public static long getHistoryRefundInfoCacheTimeout(){
        return config.getLongProperty(HISTORY_REFUND_INFO_CACHE_TIMEOUT, DEFAULT_HISTORY_REFUND_INFO_CACHE_TIMEOUT);
    }

    public static boolean isProviderGatewayAutoDisable() {
        return providerGatewayAutoDisable;
    }

    public static boolean isSupportUnionpayManualFix() {
        return isSupportUnionpayManualFix;
    }

    public static boolean isSupportZtkxWechatSubAppid() {
        return isSupportZtkxWechatSubAppid;
    }

    public static boolean isEventbusMultiples() {
        return isEventBusMultiples;
    }

    public static CsbToWapConfig getCsbToWapConfig(){
        return csbToWapConfig;
    }

    public static boolean isApplyTradeCoprocessor() {
        return isApplyTradeCoprocessor;
    }

    public static boolean isApplyTradeWosaiCoprocessor() {
        return isApplyTradeWosaiCoprocessor;
    }

    public static boolean isSendHX259Params() {
        return isSendHX259Params;
    }

    public static boolean isSendTLUnionPay259Params() {
        return isSendTLUnionPay259Params;
    }

    public static boolean isSendChinaums259Params() {
        return isSendChinaums259Params;
    }

    public static void setApplyTradeCoprocessor(boolean changeVal) {
        isApplyTradeCoprocessor = changeVal;
    }
    
    public static int getMaxRefundCount() {
        return config.getIntProperty(MAX_REFUND_COUNT, DEFAULT_MAX_REFUND_COUNT);
    }

    public static boolean isSendHXB2cSubAppId() {
        return config.getBooleanProperty(HX_B2C_SUB_APP_ID_COPROCESSOR, true);
    }

    private void loadHbfqServiceChargeFee() {
        String val = config.getProperty(HBFQ_SERVICE_CHARGE_FEE, "{}");
        Map<String, Map<String, Object>> changeVal = JsonUtil.jsonStrToObject(val, Map.class);
        HBFQ_SERVICE_CHARGE_FEE_CACHE.putAll(changeVal);
    }

    private void loadCreditFqServiceChargeFee(){
        String val = config.getProperty(CREDIT_FQ_SERVICE_CHARGE_FEE, "{}");
        Map<String, Map<String, Object>> changeVal = JsonUtil.jsonStrToObject(val, Map.class);
        CREDIT_FQ_CHARGE_FEE_CACHE.putAll(changeVal);
    }

    public static String getBuyerServiceChargeFee(int num, int type) {
        if (type == UpayConstant.USE_HBFQ) {
            Map<String, String> buyerServiceChangeFees = MapUtil.getMap(HBFQ_SERVICE_CHARGE_FEE_CACHE, "buyer", new HashMap<>());
            return Optional.ofNullable(buyerServiceChangeFees.get(num + "")).orElse("0");
        }else if(type == UpayConstant.USE_CREDIT){
            Map<String, String> buyerServiceChangeFees = MapUtil.getMap(CREDIT_FQ_CHARGE_FEE_CACHE, "buyer", new HashMap<>());
            return Optional.ofNullable(buyerServiceChangeFees.get(num + "")).orElse("0");
        }
        return "0";
    }

    public static String getSellerServiceChargeFee(int num, int type) {
        if (type == UpayConstant.USE_HBFQ) {
            Map<String, String> sellerServiceChangeFees = MapUtil.getMap(HBFQ_SERVICE_CHARGE_FEE_CACHE, "seller", new HashMap<>());
            return Optional.ofNullable(sellerServiceChangeFees.get(num + "")).orElse("0");
        }else if(type == UpayConstant.USE_CREDIT){
            Map<String, String> sellerServiceChangeFees = MapUtil.getMap(CREDIT_FQ_CHARGE_FEE_CACHE, "seller", new HashMap<>());
            return Optional.ofNullable(sellerServiceChangeFees.get(num + "")).orElse("0");
        }
        return "0";
    }

    private void loadHbfqProfitSharingTemplate() {
        String val = config.getProperty(HBFQ_SHARING_PROFIT_TEMPLATE, "{}");
        Map<String, Map<String, Object>> changeVal = JsonUtil.jsonStrToObject(val, Map.class);
        HBFQ_SHARING_PROFIT_TEMPLATE_CACHE.putAll(changeVal);
    }

    public static String getPSBCWapCodeFlag() {
        return config.getProperty(PSBC_WAP_CODE_FLAG, "0");
    }

    public static String getGeoUdfAppKey(String defaultKey) {
        return config.getProperty(GEO_UDF_APP_KEY, defaultKey);
    }

    public static Map<String, Object> getHbfqProfitSharingInfoTemplate() {
        return HBFQ_SHARING_PROFIT_TEMPLATE_CACHE;
    }

    public static long getConsumeFeeMaxDifferenceValue(){
        return config.getLongProperty(DEPOSIT_CONSUME_FEE_DIFFERENCE_VALUE, 10L);
    }

    public static String getJjzVendorAppAppid(){
        return config.getProperty(JJZ_VENDOR_APP_APPID, "");
    }

    /**
     * 获取智慧门店费率上浮值, 千22， 返回0.22
     * @return
     */
    public static String getJjzFeeRateUp(){
        return config.getProperty(JJZ_FEE_RATE_UP, "0.22");
    }

    /**
     * 微信间连交易是否上送detail字段
     * @return
     */
    public static boolean sendWeixinIndirectDetailField(){
        return config.getBooleanProperty(SEND_WEIXIN_INDIRECT_DETAIL_FIELD, true);
    }

    /**
     * 订单状态为PAY_ERROR时，查单超过指定时间后，状态修改为PAY_CANCELED, 默认为1小时
     * @return
     */
    public static void loadProviderQueryExpireTime(){
        String val = config.getProperty(PROVIDER_QUERY_EXPIRE, "{}");
        queryExpireTimeMap = JsonUtil.jsonStrToObject(val, Map.class);

    }

    public static long getProviderQueryExpireTime(String providerCode) {
        if (queryExpireTimeMap.containsKey(providerCode)) {
            return MapUtil.getLongValue(queryExpireTimeMap, providerCode) * 60 * 60 * 1000;
        }
        return MapUtil.getLongValue(queryExpireTimeMap, "", DEFAULT_PROVIDER_QUERY_EXPIRE_TIME) * 60 * 60 * 1000;
    }

    public static Map<String, Object> getReplaceSm2SignParams(int provider) {
        Object replaceValue;
        if (unionpaySm2SignParams != null
                && (replaceValue = unionpaySm2SignParams.get(String.valueOf(provider))) != null) {
            return (Map)replaceValue;
        }
        return Collections.emptyMap();
    }

    public static boolean cutICBCIpv6(){
        return config.getBooleanProperty(CUT_ICBC_IPV6, false);
    }

    public static String getDispatcherRegisterKey() {
        return ConfigService.getConfig("core.dispatcher-public").getProperty("register-key", "shouqian8");
    }

    private void buildHaiMaAppid(){
        String str = config.getProperty(HAI_MA_VENDOR_APP_ID, "[]");
        haiMaVendorAppId = JSON.parseArray(str, String.class);
    }

    public static List<String> getHaiMaVendorAppId(){
        return haiMaVendorAppId;
    }

    public static void loadWxNotSharingMerchantSet(){
        String property = config.getProperty(KEY_WX_APP_H5_NOT_SHARING_MERCHANTS, null);
        if(com.wosai.pantheon.util.StringUtil.isEmpty(property)){
            wxNotSharingMerchantSet = new HashSet<>();
        }else {
            wxNotSharingMerchantSet = Arrays.stream(property.split(",")).collect(Collectors.toSet());
        }
    }

    public static Set<String> getWxNotSharingMerchantSet() {
        return wxNotSharingMerchantSet;
    }

    public static void loadFuyouFeeRateTemplate(){
        String property = coreBaseConfig.getProperty(FUYOU_FEE_RATE_TEMPLATE, "{}");
        fuyouFeeRateTemplateMap = JsonUtil.jsonStrToObject(property, Map.class);
    }

    private static void loadNotCheckTradeApps(){
        String property = config.getProperty(NOT_CHECK_TRADE_APPS, "[]");
        notCheckTradeApps = JsonUtil.jsonStrToObject(property, List.class);
    }

    private static void loadUnionOverseasWallet(){
        String property = config.getProperty(UNION_OVERSEAS_WALLET, "[]");
        unionOverseasWallet = JsonUtil.jsonStrToObject(property, List.class);
    }

    public static  List<String> getUnionOverseasWallet(){
        return unionOverseasWallet;
    }


    private static void loadBasicPaymentTradeApps(){
        String property = config.getProperty(BASIC_PAYMENT_TRADE_APPS, "[]");
        basicPaymentTradeApps = JsonUtil.jsonStrToObject(property, List.class);
    }

    private static void loadProductFlagsConfig(){
        String property = config.getProperty(PRODUCT_FLAGS_CONFIG, "{}");
        productFlagsConfig = JsonUtil.jsonStrToObject(property, Map.class);
    }
    private static void loadTradeParameterReplace(){
        String property = coreBaseConfig.getProperty(TRADE_PARAMS_REPLACE, "{}");
        tradeParamsReplaceConfig = JsonUtil.jsonStrToObject(property, Map.class);
    }

    private static void loadVendorT9ProviderMap(){
        String property = coreBaseConfig.getProperty(KEY_T9_VENDOR_APP_ID_PROVIDER_MAPPING, "{}");
        vendorT9ProviderMap = JsonUtil.jsonStrToObject(property, Map.class);
    }

    private static void loadFixWithoutProviderQueryOrderSet(){
        String property = config.getProperty(KEY_FIX_WITHOUT_PROVIDER_QUERY_ORDER_SN, "[]");
        withoutProviderQueryOrderSns = JsonUtil.jsonStrToObject(property, Set.class);
    }

    public static Map<String, List<Integer>> getVendorT9ProviderMap() {
        return vendorT9ProviderMap;
    }

    /**
     * 是否需要校验trade app相关的参数
     * @param tradeApp
     * @return
     */
    public static boolean isCheckTradeApp(String tradeApp){
        if(notCheckTradeApps == null){
            return true;
        }
        return !notCheckTradeApps.contains(tradeApp);
    }

    /**
     * 获取基础支付业务的trade_app配置
     * @return
     */
    public static  List<String> getBasicPaymentTradeApps(){
        return basicPaymentTradeApps;
    }
    /**
     * 获取product_flag映射配置
     * @return
     */
    public static Map<String, String> getProductFlagsConfig(){
        return productFlagsConfig;
    }

    /**
     * 富友手续费设置
     * 
     * @param templateCode
     * @param defaultFeeRate
     * @param amount
     * @return
     */
    public static String getFuyouFeeRate(String templateCode, long amount) {
        Map<String, Object> config = MapUtil.getMap(fuyouFeeRateTemplateMap, templateCode);
        String type = MapUtil.getString(config, "type");
        Object value = MapUtil.getObject(config, "value");
        if (value == null) {
            return null;
        }
        if (MerchantConfig.FEE_RATE_TYPE_FIXED.equals(type)) {
            // 固定费率
            return (String)value;

        } else if (MerchantConfig.FEE_RATE_TYPE_LADDER.equals(type)) {
            // 阶梯费率
            List<Map<String, Object>> ladderFeeRates = (List<Map<String, Object>>)value;
            for (Map<String, Object> ladderFeeRate : ladderFeeRates) {
                String feeRate = MapUtil.getString(ladderFeeRate, TransactionParam.FEE_RATE);
                long min = Math.round(MapUtil.getDouble(ladderFeeRate, TransactionParam.LADDER_FEE_RATE_MIN, 0.00) * 100) ;
                long max = Math.round(MapUtil.getDouble(ladderFeeRate, TransactionParam.LADDER_FEE_RATE_MAX, 0.00) * 100);
                max = (0 == max) ? Long.MAX_VALUE : max;
                if (!StringUtils.isEmpty(feeRate) && amount > min && amount <= max) {
                    return feeRate;
                }
            }
        }
        return null;
    }

    public static void addChangeListenerForSceneConfigError(ConfigChangeListener listener) {
        config.addChangeListener(listener);
    }

    public static Map<String, List<Map<String, Objects>>> getTradeParamsReplaceConfig() {
        return tradeParamsReplaceConfig;
    }

    public static boolean getSupportChangeFeeFlag(){
        return config.getBooleanProperty(KEY_SUPPORT_CHANGE_FEE_FLAG, false);
    }

    public static String getPhonePosTradeAppId(){
        return coreBaseConfig.getProperty(KEY_PHONE_POS_TRADE_APP,"");
    }

    public static String getWildCardTradeApp(String defaultTradeApp) {
        return coreBaseConfig.getProperty(KEY_WILD_CARD_TRADE_APP, defaultTradeApp);
    }

    public static String getDomesticCardTradeApp(String defaultTradeApp) {
        return coreBaseConfig.getProperty(KEY_DOMESTIC_CARD_TRADE_APP, defaultTradeApp);
    }

    /**
     * 是否允许海科云闪付b2c交易延迟3秒走查单
     * @return
     */
    public static boolean getHaikeUnionB2cTradeDelayForQuery() {
        return config.getBooleanProperty(KEY_HAIKE_UNION_B2C_TRADE_DELAY_FOR_QUERY, false);
    }

    /**
     * 是否允许云闪付b2c交易延迟走查单
     * @return
     */
    public static boolean getUnionB2cTradeDelayForQuery() {
        return config.getBooleanProperty(KEY_UNION_B2C_TRADE_DELAY_FOR_QUERY, false);
    }

    /**
     * 默认云闪付b2c交易延迟3秒走查单
     * @return
     */
    public static long getUnionB2cTradeDelayForQueryMills() {
        return config.getLongProperty(KEY_UNION_B2C_TRADE_DELAY_FOR_QUERY_MILLS, KEY_DEFAULT_UNION_B2C_TRADE_DELAY_FOR_QUERY_MILLS);
    }


    /**
     * 是否允许EP99追加通道侧错误信息
     * @return
     */
    public static boolean getIsEP99AppendProviderMessage() {
        return config.getBooleanProperty(KEY_IS_EP99_APPEND_PROVIDER_MESSAGE, false);
    }


    public static boolean getIsWeiXinHXSceneInfo() {
        return isWeixinHxSceneInfo;
    }

    public static Set<String> getWithoutProviderQueryOrderSns() {
        return withoutProviderQueryOrderSns == null ? new HashSet<>() : withoutProviderQueryOrderSns;
    }

    public static String getB2bNotifySignToken() {
        return config.getProperty(KEY_WEIXIN_B2b_NOTIFY_SIGN_TOKEN, "");
    }

    public static String getAirwallexTpk() {
        return config.getProperty(AIRWALLEX_TPK,"");
    }

    private void loadGraySendAttach() {
        String str = config.getProperty(GRAY_SEND_ATTACH, "{}");
        Map<String, Integer> newGraySendAttach = JsonUtil.jsonStrToObject(str, Map.class);
        Map<Integer, Integer> newValue = new HashMap<>();
        if (newGraySendAttach != null){
            newGraySendAttach.forEach((k, v) -> {
                newValue.put(Integer.valueOf(k), v);
            });
        }
        graySendAttach = newValue;
    }

    public static int getGraySendAttachPercent(Integer provider) {
        return MapUtil.getIntValue(graySendAttach, provider, 100);
    }

    public static int getGraySendFundBillListPercent() {
        return config.getIntProperty(GRAY_SEND_FUND_BILL_LIST, 0);
    }
}
