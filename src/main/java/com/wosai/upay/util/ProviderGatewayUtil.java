package com.wosai.upay.util;

import com.wosai.data.util.CollectionUtil;
import com.wosai.net.GatewayUrl;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/9.
 */
public class ProviderGatewayUtil {
    //{providerName: {op: path}} 如果支付通道用的是同一个url地址，那么 providerName 的value可配置为null
    private static Map<String, Map<String, String>> providerPathConfigs = new HashMap<>();

    static {
        providerPathConfigs = CollectionUtil.hashMap(
                "provider.alipay.intl", CollectionUtil.hashMap(
                        "cancel", "/api/alipay/intl/acquiring/common/paycancel.htm",
                        "query", "/api/alipay/intl/acquiring/offline/payquery.htm",
                        "pay", "/api/alipay/intl/acquiring/offline/pay.htm",
                        "refund", "/api/alipay/intl/acquiring/common/grefund.htm"
                ),
                "provider.alipay.overseas", null,
                "provider.alipay.v1", null,
                "provider.alipay.v2", null,
                "provider.alipay.wap", null,
                "provider.alipay.wap.v2", null,
                "provider.alipay.fitness", null,
                "provider.bestpay", CollectionUtil.hashMap(
                        "cancel", "/reverse/reverse",
                        "query", "/query/queryOrder",
                        "wap", "/order.action",
                        "pay", "/barcode/placeOrder",
                        "refund", "/refund/commonRefund"
                ),
                "provider.bestpay.v2", CollectionUtil.hashMap(
                        "query", "/mapi/uniformReceipt/tradeQuery",
                        "refund.query", "/mapi/uniformReceipt/refundOrderQuery",
                        "pay", "/mapi/uniformReceipt/barCodePay",
                        "refund", "/mapi/uniformReceipt/tradeRefund"
                ),
                "provider.chinaums", CollectionUtil.hashMap(
                        "cancel", "/v2/poslink/transaction/voidpayment",
                        "query", "/v2/poslink/transaction/query",
                        "pay", "/v6/poslink/transaction/pay",
                        "refund", "/v2/poslink/transaction/refund"
                ),
                "provider.chinaums.v1", CollectionUtil.hashMap(
                        "wap", "/v1/netpay",
                        "wap.query", "/v1/netpay/query",
                        "wap.cancel", "/v1/netpay/close",
                        "wap.refund", "/v1/netpay/refund",
                        "pay", "/v2/ulink-netpay/pay",
                        "pay.query", "/v1/ulink-netpay/query",
                        "pay.refund", "/v1/ulink-netpay/refund",
                        "pay.cancel", "/v1/ulink-netpay/cancel"
                ),
                "provider.cibbank", null,
                "provider.citicbank", null,
                "provider.cmcc", null,
                "provider.direct.unionpay.alipay.v2", null,
                "provider.direct.unionpay.alipay.wap.v2", null,
                "provider.direct.unionpay.weixin", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.direct.unionpay.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "wap", "/wx/v1/pay/prepay",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.lkl.unionpay.alipay.v2", null,
                "provider.lkl.unionpay.alipay.wap.v2", null,
                "provider.lkl.unionpay.weixin", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.lkl.unionpay.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "wap", "/wx/v1/pay/prepay",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.gift.card", null,
                "provider.lakala", CollectionUtil.hashMap(
                        "bsccancel", "/thirdpartplatform/scancodpay/8002.dor",
                        "csbcancel", "/thirdpartplatform/scancodpay/8012.dor",
                        "wap", "/thirdpartplatform/scancodpay/8011.dor",
                        "query", "/thirdpartplatform/scancodpay/8021.dor",
                        "pay", "/thirdpartplatform/scancodpay/8001.dor",
                        "positivelyquery", "/thirdpartplatform/scancodpay/8022.dor",
                        "refund.query", "/thirdpartplatform/scancodpay/8032.dor",
                        "precreate", "/thirdpartplatform/scancodpay/8011.dor",
                        "refund", "/thirdpartplatform/scancodpay/8031.dor"
                ),
                "provider.nucc.alipay.v2", null,
                "provider.nucc.bestpay", null,
                "provider.nucc.weixin", CollectionUtil.hashMap(
                        "cancel", "/gateway/wechat/order/reverse",
                        "query", "/gateway/wechat/order/query",
                        "pay", "/gateway/wechat/micropay",
                        "refund.query", "/gateway/wechat/refund/query",
                        "close", "/gateway/wechat/order/close",
                        "precreate", "/gateway/wechat/unifiedorder",
                        "refund", "/gateway/wechat/refund"
                ),
                "provider.nucc.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/gateway/wechat/order/reverse",
                        "query", "/gateway/wechat/order/query",
                        "wap", "/gateway/wechat/unifiedorder",
                        "pay", "/gateway/wechat/micropay",
                        "refund.query", "/gateway/wechat/refund/query",
                        "close", "/gateway/wechat/order/close",
                        "precreate", "/gateway/wechat/unifiedorder",
                        "refund", "/gateway/wechat/refund"
                ),
                "provider.qq", CollectionUtil.hashMap(
                        "cancel", "/cgi-bin/scan/code_reverse.cgi",
                        "query", "/cgi-bin/clientv1.0/qpay_order_query.cgi",
                        "pay", "/cgi-bin/scan/code_pay.cgi",
                        "refund.query", "/cgi-bin/scan/code_refund_query.cgi",
                        "close", "/cgi-bin/scan/code_cancel.cgi",
                        "refund", "/cgi-bin/scan/code_refund.cgi"
                ),
                "provider.sodexo", CollectionUtil.hashMap(
                        "oauth", "/OAuth/Token",
                        "pay", "/api/MealCardBarcodeExpense",
                        "cancel", "/api/MealCardPaymentCancel",
                        "refund", "/api/MealCardPaymentRefund",
                        "query", "/api/TransQuery"
                ),
                "provider.sodexo.wap", CollectionUtil.hashMap(
                        "oauth", "/OAuth/Token",
                        "pay", "/api/MealCardBarcodeExpense",
                        "cancel", "/api/MealCardPaymentCancel",
                        "refund", "/api/MealCardPaymentRefund",
                        "query", "/api/TransQuery",
                        "openId", "/api/mobileopenidrequest",       //用户唯一标识申请
                        "payToken", "/api/ExpenseTokenRequest",     //线上支付token申请
                        "queryBalance", "/api/TBalanceQuery",       //余额查询
                        "otherPay", "/api/ExpenseByToken"           //线上消费
                ),
                "provider.swiftpass", null,
                "provider.unionpay.alipay.v2", null,
                "provider.unionpay.alipay.wap.v2", null,
                "provider.unionpay.online", CollectionUtil.hashMap(
                        "query", "/gateway/api/queryTrans.do",
                        "precreate", "/gateway/api/frontTransReq.do",
                        "refund", "/gateway/api/backTransReq.do"
                ),
                "provider.unionpay.open", CollectionUtil.hashMap(
                        "cancel", "/gateway/api/pay/cancelOrder",
                        "query", "/gateway/api/pay/queryOrder",
                        "wap", "/gateway/api/pay/unifiedorder",
                        "pay", "/gateway/api/pay/micropay",
                        "close", "/gateway/api/pay/closeOrder",
                        "precreate", "/gateway/api/pay/qrpay",
                        "refund", "/gateway/api/pay/refund"
                ),
                "provider.unionpay.weixin", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.unionpay.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "wap", "/wx/v1/pay/prepay",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.weixin", CollectionUtil.hashMap(
                        "cancel", "/secapi/pay/reverse",
                        "query", "/pay/orderquery",
                        "pay", "/pay/micropay",
                        "refund.query", "/pay/refundquery",
                        "deposit.cancel", "/deposit/reverse",
                        "precreate", "/pay/unifiedorder",
                        "openid.barcode.query", "/tools/authcodetoopenid",
                        "deposit.consume", "/deposit/consume",
                        "deposit.freeze", "/deposit/micropay",
                        "deposit.refund.query", "/deposit/refundquery",
                        "deposit.refund", "/deposit/refund",
                        "deposit.query", "/deposit/orderquery",
                        "close", "/pay/closeorder",
                        "refund", "/secapi/pay/refund",
                        "refundv2", "/secapi/pay/refundv2"
                ),
                "provider.weixin.hk", CollectionUtil.hashMap(
                        "cancel", "/cgi-bin/api/ia_mch_qr_order_revoke.cgi",
                        "query", "/cgi-bin/ia_mch_qr_order_query.cgi",
                        "pay", "/cgi-bin/ia_mch_qr_auth.cgi",
                        "refund.query", "/cgi-bin/ia_mch_qr_refund_query.cgi",
                        "refund", "/cgi-bin/api/ia_mch_qr_order_refund.cgi"
                ),
                "provider.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/secapi/pay/reverse",
                        "wap", "/pay/unifiedorder",
                        "query", "/pay/orderquery",
                        "pay", "/pay/micropay",
                        "refund.query", "/pay/refundquery",
                        "close", "/pay/closeorder",
                        "precreate", "/pay/unifiedorder",
                        "refund", "/secapi/pay/refund",
                        "refundv2", "/secapi/pay/refundv2",
                        "deposit.refund", "/deposit/refund"
                ),
                "provider.weixin.mini.B2b", CollectionUtil.hashMap(
                        "query", "/getorder",
                        "refund", "/refund",
                        "refund.query", "/getrefund"
                ),
                "provider.weixin.cycle.v2", CollectionUtil.hashMap(
                        "deposit.prefreeze", "/v3/partner-papay/contracts/contract_id/notify",
                        "deposit.consume", "/pay/partner/pappayapply",
                        "query", "/pay/orderquery",
                        "refund", "/secapi/pay/refund",
                        "refund.query", "/pay/refundquery"
                ),
                "provider.weixin.cycle.v2.merchant", CollectionUtil.hashMap(
                        "deposit.prefreeze", "/v3/papay/contracts/contract_id/notify",
                        "deposit.consume", "/pay/pappayapply",
                        "query", "/pay/orderquery",
                        "refund", "/secapi/pay/refund",
                        "refund.query", "/pay/refundquery"
                ),
                "provider.weixin.cycle.v2.partner.auth", CollectionUtil.hashMap(
                        "deposit.auth.apply", "/papay/partner",
                        "deposit.auth.query", "/papay/partner/querycontract",
                        "deposit.auth.terminate", "/papay/deletecontract"
                ),
                "provider.weixin.cycle.v2.merchant.auth", CollectionUtil.hashMap(
                        "deposit.auth.apply", "/papay",
                        "deposit.auth.query", "/papay/querycontract",
                        "deposit.auth.terminate", "/papay/deletecontract"
                ),
                "provider.tl.unionpay.weixin", CollectionUtil.hashMap(
                        "cancel", "/wx_reverse",
                        "query", "/wx_orderqry",
                        "pay", "/wx_micropay",
                        "precreate", "/wx_prepay",
                        "close", "/wx_close",
                        "refund", "/wx_refund"
                ),
                "provider.tl.unionpay.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/wx_reverse",
                        "wap", "/wx_prepay",
                        "query", "/wx_orderqry",
                        "close", "/wx_close",
                        "precreate", "/wx_prepay",
                        "refund", "/wx_refund"
                ),
                "provider.weixin.wapOrMini.v3", null,
                "provider.weixin.wapOrMini.v3.palm", null,
                "provider.weixin.wapOrMini.v3.partner", null,
                "provider.weixin.wapOrMini.v3.partner.transactions", CollectionUtil.hashMap(
                        "precreate", "/v3/pay/partner/transactions/jsapi",
                        "query", "/v3/pay/partner/transactions/out-trade-no/{out_trade_no}",
                        "refund", "/v3/refund/domestic/refunds",
                        "refund.query", "/v3/refund/domestic/refunds/{out_refund_no}",
                        "close", "/v3/pay/partner/transactions/out-trade-no/{out_trade_no}/close",
                        "cancel", "/v3/pay/partner/transactions/out-trade-no/{out_trade_no}/close"
                ),
                "provider.cmb", CollectionUtil.hashMap(
                        "pay", "/pay",
                        "query", "/orderquery",
                        "refund", "/refund",
                        "refund.query", "/refundquery",
                        "cancel", "/cancel",
                        "wx.wap", "/onlinepay",
                        "alipay.wap", "/servpay",
                        "decp", "/ecny/unifiedPayment"
                ),
                "provider.lakala.open", CollectionUtil.hashMap(
                        "pay", "/labs_order_micropay",
                        "cancel", "/labs_order_micropay_reverse",
                        "query", "/labs_order_query",
                        "refund", "/labs_order_refund"
                ),
                "provider.lakala.openv3.bankcard", CollectionUtil.hashMap(
                        "query", "/searcher/base_core/trans_query",
                        "deposit.query", "/searcher/base_core/trans_query",
                        "precreate", "/los/checkout_counter/generate_order",
                        "refund", "/lams/trade/trade_refund",
                        "refund.query", "/lams/trade/trade_refund_query"
                ),
                "provider.lakala.openv3.phone.pos", CollectionUtil.hashMap(
                        "query", "/searcher/base_core/trans_query",
                        "precreate", ""
                ),
                "provider.psbcbank.alipay", CollectionUtil.hashMap(
                        "pay", "/api/platform/outAgency/aliOrderPay",
                        "precreate", "/api/platform/outAgency/aliCreOrder",
                        "refund", "/api/platform/outAgency/aliRefOrder",
                        "refund.query", "/api/platform/outAgency/aliQueRefOrder",
                        "query", "/api/platform/ordQue/aliQueOrder",
                        "close", "/api/platform/outAgency/aliCloOrder"
                ),
                "provider.psbcbank.weixin", CollectionUtil.hashMap(
                        "pay", "/api/platform/outAgency/wxOrderPay",
                        "precreate", "/api/platform/outAgency/wxCreOrder",
                        "refund", "/api/platform/outAgency/wxRefOrder",
                        "refund.query", "/api/platform/outAgency/wxQueRefOrder",
                        "query", "/api/platform/ordQue/wxQueOrder",
                        "close", "/api/platform/outAgency/wxCloOrder"
                ),
                "provider.psbcbank.unionpay", CollectionUtil.hashMap(
                        "pay", "/api/platform/outAgency/unionOrderPay",
                        "close", "/api/platform/outAgency/unUnionCloOrder",
                        "refund", "/api/platform/outAgency/unionRefOrder",
                        "refund.query", "/api/platform/outAgency/unUnionQueRefOrder",
                        "query", "/api/platform/ordQue/unionQueOrder",
                        "union.userId.query", "/api/platform/outAgency/actUnionUserId",
                        "precreate", "/api/platform/outAgency/actUnionCreOrder",
                        "precreate.close", "/api/platform/outAgency/unionCloOrder",
                        "precreate.refund", "/api/platform/outAgency/actUnionRefOrder",
                        "precreate.refund.query", "/api/platform/outAgency/unionQueRefOrder",
                        "precreate.query", "/api/platform/ordQue/actUnionQueOrder"
                ),
                "provider.cgbbank", CollectionUtil.hashMap(
                        "pay", "/e_PayTicket/micropay/1.0.0",
                        "precreate", "/e_PayTicket/createOrder/1.0.0",
                        "refund", "/e_PayTicket/refund/1.0.0",
                        "refund.query", "/e_PayTicket/refundQuery/1.0.0",
                        "query", "/e_PayTicket/queryOrder/1.0.0",
                        "cancel", "/e_PayTicket/reverse/1.0.0"
                ),
                "provider.foxconn", null,
                "provider.hxbank", CollectionUtil.hashMap(
                        "pay", "/OPC103040202000100002500",
                        "wx.wap", "/OPC103040202000100002800",
                        "alipay.wap", "/OPC10304020200**********",
                        "union.userId.query", "/OPC103040202000100004200",
                        "union.wap", "/OPC103040202000100004300",
                        "precreate", "/OPC103040202000100003200",
                        "refund", "/OPC103040202000100002900",
                        "refund.query", "/OPC103040202000100003100",
                        "query", "/OPC103040202000100003100",
                        "cancel", "/OPC103040202000100003000"
                ),
                "provider.ccb", null,
                "provider.ccb.wap.alipay", null,
                "provider.ccb.wap.weixin", null,
                "provider.ccb.wap.unionpay", null,
                "provider.ccb.precreate", null,
                "provider.chinaums.epay", CollectionUtil.hashMap(
                        "union.deposit.consume", "/v1/netpay/uac/entrust-pay",
                        "union.refund", "/v1/netpay/uac/refund",
                        "union.refund.query", "/v1/netpay/uac/refund-query",
                        "union.query", "/v1/netpay/uac/query"
                ),
                "provider.grabpay", CollectionUtil.hashMap(
                        "pay", "/perform",
                        "query", "",
                        "cancel", "",
                        "refund", ""
                ),
                "provider.grabpay.moca", CollectionUtil.hashMap(
                        "pay", "/perform",
                        "query", "",
                        "cancel", "",
                        "refund", ""
                ),
                "provider.icbcbank", CollectionUtil.hashMap(
                        "pay", "/mybank/pay/qrcode/scanned/pay/V1",
                        "query", "/mybank/pay/qrcode/scanned/paystatus/V1",
                        "refund", "/mybank/pay/qrcode/scanned/return/V1",
                        "refund.query", "/mybank/pay/qrcode/scanned/returnstatus/V1",
                        "precreate", "/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1",
                        "precreate.query", "/cardbusiness/aggregatepay/b2c/online/orderqry/V1",
                        "precreate.refund", "/cardbusiness/aggregatepay/b2c/online/merrefund/V1",
                        "precreate.refund.query", "/cardbusiness/aggregatepay/b2c/online/refundqry/V1"
                ),
                "provider.weixin.hkv3", CollectionUtil.hashMap(
                        "pay", "/hk/v3/transactions/micropay",
                        "query", "/hk/v3/transactions/out-trade-no/{out_trade_no}",
                        "refund", "/hk/v3/refunds",
                        "cancel", "/hk/v3/transactions/out-trade-no/{out_trade_no}/reverse",
                        "precreate", "/hk/v3/transactions/native",
                        "close", "/hk/v3/transactions/out-trade-no/{out_trade_no}/close"
                ),
                "provider.weixin.hkv3.wapOrMini", CollectionUtil.hashMap(
                        "wap", "/hk/v3/transactions/jsapi",
                        "query", "/hk/v3/transactions/out-trade-no/{out_trade_no}",
                        "refund", "/hk/v3/refunds",
                        "close", "/hk/v3/transactions/out-trade-no/{out_trade_no}/close"
                ),
                "provider.tl.syb", CollectionUtil.hashMap(
                        "pay", "/unitorder/scanqrpay",
                        "precreate", "/unitorder/pay",
                        "query", "/tranx/query",
                        "refund", "/tranx/refund",
                        "union.userId.query", "/unitorder/authcodetouserid",
                        "refundv2", "/unitorder/isvrefund"
                ),
                "provider.tl.syb.bank", CollectionUtil.hashMap(
                        "query", "/tranx/queryorder",
                        "refund.query", "/tranx/queryorder",
                        "refund", "/posol/refund"
                ),
                "provider.ccb.giftcard", CollectionUtil.hashMap(
                        "pay", "",
                        "query", ""
                ),
                "provider.haike.unionpay", CollectionUtil.hashMap(
                        "refund", "/refund",
                        "refund.query", "/refund-query"
                ),
                "provider.haike.unionpay.alipay.v2", null,
                "provider.haike.unionpay.alipay.wap.v2", null,
                "provider.haike.unionpay.weixin", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.haike.unionpay.weixin.wapOrMini", CollectionUtil.hashMap(
                        "cancel", "/wx/v1/pay/order/reverse",
                        "query", "/wx/v1/pay/order/qry",
                        "wap", "/wx/v1/pay/prepay",
                        "pay", "/wx/v1/pay/micropay",
                        "refund.query", "/wx/v1/pay/refund/qry/single",
                        "close", "/wx/v1/pay/order/close",
                        "precreate", "/wx/v1/pay/prepay",
                        "refund", "/wx/v1/pay/refund"
                ),
                "provider.fuyou", CollectionUtil.hashMap(
                        "pay", "/micropay",
                        "cancel", "/cancelorder",
                        "close", "/closeorder",
                        "query", "/commonQuery",
                        "history.query", "/hisTradeQuery",
                        "refund", "/commonRefund",
                        "refund.query", "/refundQuery",
                        "precreate", "/wxPreCreate",
                        "union.userId.query", "/auth2Openid"
                ),
                "provider.fuyou.bank", CollectionUtil.hashMap(
                        "refund", "/order/insRefund"
                ),
                "provider.fuyou.bank.query", CollectionUtil.hashMap(
                        "query", "/trade/order",
                        "refund.query", "/trade/order"
                ),
                "provider.bocom", CollectionUtil.hashMap(
                        "pay", "/MPNG210002/v1",
                        "query", "/MPNG020702/v1",
                        "refund", "/MPNG020701/v2",
                        "refund.query", "/MPNG020703/v1"
                ),
                "provider.lakala.openv3", CollectionUtil.hashMap(
                        "query", "/labs/query/tradequery",
                        "pay", "/labs/trans/micropay",
                        "precreate", "/labs/trans/preorder",
                        "refund", "/labs/relation/refund",
                        "cancel", "/labs/relation/revoked",
                        "close", "/labs/relation/close"
                ),
                "provider.entpay", CollectionUtil.hashMap(
                        "precreate", "/v3/mse-pay/payments",
                        "redirect", "/v3/mse-pay/redirects",
                        "cancel", "/v3/mse-pay/payments/%s/close",
                        "query", "/v3/mse-pay/payments/out-payment-id/%s",
                        "refund", "/v3/mse-pay/refunds",
                        "refund.query", "/v3/mse-pay/refunds/out-refund-id/%s"
                ),
                "provider.fjnx", CollectionUtil.hashMap(
                        "pay", "/nepay/front/order/ScanPay",
                        "wx.wap", "/nepay/front/order/WxPay",
                        "alipay.wap", "/nepay/front/order/AlPay",
                        "query", "/nepay/front/order/QueryOrder",
                        "refund", "/nepay/front/order/RefundOrder"
                ),
                "provider.spdb", CollectionUtil.hashMap(
                        "pay", "/api/corporateAccounts/payments/orders",
                        "precreate", "/api/corporateAccounts/payments/orders",
                        "query", "/api/corporateAccounts/payments/status",
                        "refund", "/api/corporateAccounts/payments/ordersReturn",
                        "cancel", "/api/corporateAccounts/payments/ordersClose"
                ),
                "provider.cmbcbank", CollectionUtil.hashMap(
                        "pay", "/appserver/lcbpPay.do",
                        "precreate", "/appserver/lcbpPay.do",
                        "query", "/appserver/paymentResultSelectNew.do",
                        "refund", "/appserver/cancelTrans.do",
                        "cancel", "/appserver/customsClearCancel.do"
                ),
                "provider.tls2p", CollectionUtil.hashMap(
                        "precreate", "/gateway/applepay/preProcess",
                        "query", "/gateway/cnp/quickpay",
                        "pay", "/gateway/applepay/authorized",
                        "refund", "/gateway/cnp/quickpay"
                ),
                "provider.lzccb", CollectionUtil.hashMap(
                        "pay", "/payGateway/zlxt/trade/pay",
                        "precreate", "/payGateway/zlxt/trade/pay",
                        "query", "/payGateway/zlxt/trade/query",
                        "refund", "/payGateway/zlxt/trade/refund",
                        "getSm4Key", "/payGateway/payApi/merchant/getSm4Key"
                ),
                "provider.yop", CollectionUtil.hashMap(
                        "pay", "/rest/v1.0/aggpay/pay",
                        "precreate", "/rest/v1.0/aggpay/pre-pay",
                        "close", "/rest/v1.0/trade/order/close",
                        "query", "/rest/v1.0/trade/order/query",
                        "refund", "/rest/v1.0/trade/refund",
                        "refund.query", "/rest/v1.0/trade/refund/query",
                        "union.userId.query", "/rest/v1.0/aggpay/get-user-id"
                ),
                "provider.psbc", CollectionUtil.hashMap(
                        "pay", "",
                        "query", "",
                        "refund", "",
                        "precreate", "",
                        "notify", ""
                ),
                "provider.pkx.airport", CollectionUtil.hashMap(
                        "close", "/API/WxMiniOrderClose",
                        "query", "/API/WxMiniOrderQuery",
                        "refund", "/API/WxMiniOrderRefund",
                        "refund.query", "/API/WxMiniRefundQuery"
                ),
                "provider.macaupass", null,
                "provider.xzx", null,
                "provider.hopeedu", null,
                "provider.guotong", CollectionUtil.hashMap(
                        "pay","/yyfsevr/order/scanByMerchant",
                        "precreate",  "/yyfsevr/order/pay",
                        "query", "/yyfsevr/order/orderQuery",
                        "refund", "/yyfsevr/order/refund",
                        "refund.query", "/yyfsevr/order/refundQuery",
                        "union.userId.query", "/yyfsevr/order/getPaypalTag"
                ),
                "provider.airwallex", CollectionUtil.hashMap(
                        "token","/api/v1/authentication/login",
                        "pay", "/api/v1/pa/payment_intents/create",
                        "query", "/api/v1/pa/payment_intents",
                        "refund", "/api/v1/pa/refunds/create",
                        "refund.query", "/api/v1/pa/refunds"
                ),
                "provider.wecard", CollectionUtil.hashMap(
                        "pay", "/cloudpay/v1/pay/c2b/unified_order",
                        "precreate", "/cloudpay/v1/pay/c2b/unified_order",
                        "query", "/cloudpay/v1/pay/query_order",
                        "refund", "/cloudpay/v1/refundOrder/apply",
                        "refund.query", "/cloudpay/v1/refundOrder/query"
                )
        );
    }


    /**
     *
     * @param providerName
     * @param gateway
     * @param skip
     * @param op
     * @return
     */
    public static GatewayUrl getGatewayUrl(String providerName, String gateway, boolean skip, String op) {
        if (gateway == null) {
            return null;
        }
        Map<String, String> config = providerPathConfigs.get(providerName);
        if (config == null) {
            return new GatewayUrl(gateway, skip);
        }
        String path = config.get(op);
        if (path == null) {
            return null;
        }
        //容错，防止拼接出来的url里面有两个//
        String url = gateway.endsWith("/") ? (gateway.substring(0, gateway.length() - 1) + path) : (gateway + path);
        return new GatewayUrl(url, skip);
    }
}
