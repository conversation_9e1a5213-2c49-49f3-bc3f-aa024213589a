package com.wosai.upay.util;

import com.wosai.middleware.hera.toolkit.trace.TraceContext;

public class FakeRequestUtil {
    private static final String FAKE_KEY = "fake";
    private static final String DEFAULT_VALUE = null;
    private static final String FAKE_TRUE = "1";

    public static boolean isFakeRequest() {
        String fake = TraceContext.getBaggageItem(FAKE_KEY, DEFAULT_VALUE);
        return FAKE_TRUE.equals(fake);
    }
}
