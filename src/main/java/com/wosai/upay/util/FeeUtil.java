package com.wosai.upay.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by jianfree on 7/2/17.
 */
public class FeeUtil {
    public static final Logger logger = LoggerFactory.getLogger(FeeUtil.class);

    //零费率
    protected static final String ZERO_FEE_RATE = "0.00";

    /**
     * 计算退款或者撤单的手续费
     * @param repository
     * @param refundOrCancelTransaction
     * @return
     */
    public static long calculateRefundOrCancelFee(DataRepository repository, Map<String, Object> refundOrCancelTransaction){
        return internalCalculateRefundOrCancelFee(repository, refundOrCancelTransaction);
    }

    /**
     * 计算预授权消费时的手续费
     * @param repository
     * @param refundOrCancelTransaction
     * @return
     */
    public static long calculateDepositConsumeFee(Map<String, Object> consumeTransaction){
        return internalCalculatePayOrPrecreateFee(consumeTransaction);
    }

    /**
     * 计算微信预授权消费时的手续费
     *
     * 预授权完成的手续费 = 冻结金额 * 手续费费率 - （冻结金额 - 完成金额）* 手续费费率，需要重新计算
     *
     * @param freezeTransaction
     * @param consumeTransaction
     * @return
     */
    public static long calculateWeixinDepositConsumeFee(Map<String, Object> freezeTransaction, Map<String, Object> consumeTransaction){
        Map<String, Object> refundConsumeTransaction = new HashMap<>(consumeTransaction);
        refundConsumeTransaction.put(Transaction.ORIGINAL_AMOUNT, MapUtil.getLongValue(freezeTransaction, Transaction.ORIGINAL_AMOUNT) - MapUtil.getLongValue(consumeTransaction, Transaction.ORIGINAL_AMOUNT));
        refundConsumeTransaction.put(Transaction.EFFECTIVE_AMOUNT, MapUtil.getLongValue(freezeTransaction, Transaction.EFFECTIVE_AMOUNT) - MapUtil.getLongValue(consumeTransaction, Transaction.EFFECTIVE_AMOUNT));
        long refundFee = internalCalculatePayOrPrecreateFee(refundConsumeTransaction);
        long freezeFee = getFeeFromTransaction(freezeTransaction);
        return freezeFee - refundFee;
    }

    /**
     * 计算退款或者撤单的手续费
     * @param repository
     * @param refundOrCancelTransaction
     * @return
     */
    public static long calculateHistoryRefundFee(DataRepository repository, Map<String, Object> refundOrCancelTransaction, GatewaySupportService gatewaySupportService, Long orderCtime, boolean useApproximateFee){
        return internalCalculateRefundOrCancelFee(repository, refundOrCancelTransaction, gatewaySupportService, orderCtime, useApproximateFee);
    }

    /**
     * 重新计算退款或者撤单的手续费
     * (当部分退款时， 如果某笔交易是正式的，支付通道免充值的交易，需要重新计算)
     * @param repository
     * @param refundOrCancelTransaction
     * @return
     */
    public static long reCalculateRefundOrCancelFee(DataRepository repository, Map<String,Object> refundOrCancelTransaction, GatewaySupportService gatewaySupportService, Long orderCtime, boolean useApproximateFee) {
        return internalCalculateRefundOrCancelFee(repository, refundOrCancelTransaction, gatewaySupportService, orderCtime, useApproximateFee);
    }

    /**
     * 计算撤单或者退款，退还的手续费
     * @param repository
     * @param refundOrCancelTransaction
     * @return
     */
    public static long internalCalculateRefundOrCancelFee(DataRepository repository, Map<String,Object> refundOrCancelTransaction){
        return internalCalculateRefundOrCancelFee(repository, refundOrCancelTransaction, null, null, false);
    }

    /**
     * 使用指定费率计算撤单或者退款，退还的手续费
     * @param repository
     * @param refundOrCancelTransaction
     * @return
     */
    private static long internalCalculateRefundOrCancelFee(DataRepository repository, Map<String,Object> refundOrCancelTransaction, GatewaySupportService gatewaySupportService, Long orderCtime, boolean useApproximateFee){
        String transactionId = BeanUtil.getPropString(refundOrCancelTransaction, DaoConstants.ID);
        String merchantId = BeanUtil.getPropString(refundOrCancelTransaction, Transaction.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(refundOrCancelTransaction, Transaction.ORDER_SN);
        int payway = BeanUtil.getPropInt(refundOrCancelTransaction, Transaction.PAYWAY);
        long originalAmount = BeanUtil.getPropLong(refundOrCancelTransaction, Transaction.ORIGINAL_AMOUNT);
        Map<String,Object> tradeParams = getTradeParamsFromConfigSnapshot((Map<String, Object>) refundOrCancelTransaction.get(Transaction.CONFIG_SNAPSHOT));
        String feeRate = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE, "0.0");
        List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(refundOrCancelTransaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH);
        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(refundOrCancelTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);

        //新增直连商户退款是否退还手续费的逻辑
        Map extraOutFields = MapUtil.getMap(refundOrCancelTransaction, Transaction.EXTRA_OUT_FIELDS);
        Boolean isNeedRefund = MapUtil.getBoolean(extraOutFields, TransactionParam.IS_NEED_REFUND_FEE_FLAG);
        if (Objects.nonNull(isNeedRefund) && !isNeedRefund
                && MapUtils.isNotEmpty(extraOutFields) && extraOutFields.containsKey(TransactionParam.IS_NEED_REFUND_FEE_FLAG)) {
            return 0L;
        }

        if(originalAmount == 0){
            return 0l;
        }
        long wosaiNotTopUp = 0;
        long channelNotTopUp = 0; //渠道免充值金额

        if(payments != null){
            for(Map<String,Object> payment: payments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long paymentAmountAmount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(Payment.TYPE_HONGBAO_WOSAI_MCH.equals(type) || Payment.TYPE_DISCOUNT_WOSAI_MCH.equals(type)){
                    wosaiNotTopUp = wosaiNotTopUp + paymentAmountAmount;
                }
            }
        }
        if(channelPayments != null){
            for(Map<String,Object> channelPayment: channelPayments){
                String type = BeanUtil.getPropString(channelPayment, Payment.TYPE);
                long paymentAmountTotal = BeanUtil.getPropLong(channelPayment, Payment.AMOUNT);
                if(Payment.TYPE_HONGBAO_CHANNEL_MCH.equals(type) || Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                    channelNotTopUp = channelNotTopUp + paymentAmountTotal;
                }
            }
        }
        long feeFundAmount = originalAmount - wosaiNotTopUp - channelNotTopUp; //计算费率时涉及的金额  等于原始金额减去商户立减金额再减去商户红包金额 减去渠道免充值金额
        long approximateFee = applyRate(feeFundAmount, feeRate);
        int provider = MapUtil.getIntValue(refundOrCancelTransaction, Transaction.PROVIDER);
        //招行通道手续费计算按照普通的 金额*费率 四舍五入计算
        if(provider == Order.PROVIDER_CMB || useApproximateFee){
            return approximateFee;
        }
        List<Map<String,Object>> transactions = null;
        if(null == gatewaySupportService){
            transactions = CollectionUtil.iterator2list(repository.getTransactionDao().filter(
                    Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)
            ).fetchAll());
        }else {
            transactions = gatewaySupportService.getSuccessTransactionList(merchantId, orderSn, orderCtime);
        }
        long paidInFee = 0; //实收手续费
        long originalPaidInFee = 0; //收款交易产生的手续费
        long originalPaidFeeAmount = 0; //收款交易对应的实收金额
        long netFeeFundAmount = 0; //计算费率相关的剩余总金额

        for(Map<String,Object> transaction: transactions) {
            if(BeanUtil.getPropString(transaction, DaoConstants.ID).equals(transactionId)){
                //交易完成后，因为支付通道免充值的问题，可能会重新计算手续费, 容错，不计算此次的流水
                continue;
            }
            int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
            if(type == Transaction.TYPE_DEPOSIT_FREEZE) {
                // 预授权操作只是冻结消费者押金，不计算此次的流水
                continue;
            }
            long localOriginalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            int status = BeanUtil.getPropInt(transaction, Transaction.STATUS);
            int flag = (type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_REFUND_REVOKE || type == Transaction.TYPE_DEPOSIT_CONSUME) ? 1 : -1;

            Long fee = getFeeFromTransaction(transaction);
            if(fee == null){
                return approximateFee;
            }
            List<Map<String,Object>> localPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH);
            List<Map<String,Object>> localChannelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            long localWosaiNotTopUp = 0;
            long localChannelNotTopUp = 0;
            if(localPayments != null && localPayments.size() > 0){
                for(Map<String,Object> payment: localPayments){
                    String paymentType = BeanUtil.getPropString(payment, Payment.TYPE);
                    if(Payment.TYPE_HONGBAO_WOSAI_MCH.equals(paymentType) || Payment.TYPE_DISCOUNT_WOSAI_MCH.equals(paymentType)){
                        long paymentAmount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                        localWosaiNotTopUp = localWosaiNotTopUp + paymentAmount;
                    }
                }
            }
            if(localChannelPayments != null){
                for(Map<String,Object> channelPayment: localChannelPayments){
                    String paymentType = BeanUtil.getPropString(channelPayment, Payment.TYPE);
                    if(Payment.TYPE_HONGBAO_CHANNEL_MCH.equals(paymentType) || Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(paymentType)){
                        long paymentAmountTotal = BeanUtil.getPropLong(channelPayment, Payment.AMOUNT);
                        localChannelNotTopUp = localChannelNotTopUp + paymentAmountTotal;
                    }
                }
            }

            if(status == Transaction.STATUS_SUCCESS){
                paidInFee = paidInFee +  fee * flag;
                netFeeFundAmount = netFeeFundAmount + (localOriginalAmount - localWosaiNotTopUp - localChannelNotTopUp) * flag;
                if(type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_DEPOSIT_CONSUME){
                    originalPaidInFee = fee;
                    originalPaidFeeAmount = localOriginalAmount - localWosaiNotTopUp - localChannelNotTopUp;
                }
            }
        }
        long receiveFee = applyRate((netFeeFundAmount - feeFundAmount), feeRate);//此次退款或者撤单成功后应收的手续费
        long fee = paidInFee - receiveFee;
        //支付宝部分退款的费率计算方式为 退款金额/付款金额*付款手续费金额 然后四舍五入, 如果最后一次退款，则退回全部剩下的手续费 如果用户实付金额为0 手续费退回为0
        if(payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2){
            if(netFeeFundAmount - feeFundAmount == 0){
                return paidInFee;
            }else {
                if(originalPaidFeeAmount == 0 || paidInFee == 0) return 0;
                // 部分退款手续费 原计算方式为： Math.round(feeFundAmount * 1.0 /originalPaidFeeAmount * originalPaidInFee);
                // 但是存在计算不正确的情况 ， 例如：feeFundAmount = 18500， originalPaidFeeAmount = 34000， originalPaidInFee = 102 ，若用上述公式计算 Math.round(18500.0 / 34000 * 102) = 55,实际手续费应该为56；
                // 故修改为BigDecimal 的方式去计算
                return Math.round(new BigDecimal(feeFundAmount).divide(new BigDecimal(originalPaidFeeAmount), 20, RoundingMode.HALF_UP).multiply(new BigDecimal(originalPaidInFee)).doubleValue());
            }
        }
        //微信退款的费率计算方式为 round( 本次退款金额/(订单金额 - 已退款金额)) * （订单手续费 - 已退款手续费)), 如果最后一次退款，则退回全部剩下的手续费
        if(payway == Order.PAYWAY_WEIXIN){
            if(netFeeFundAmount - feeFundAmount == 0){
                return paidInFee;
            }else {
                //可退金额为0的情况下 会有问题 单根据业务逻辑 执行不到此处
                return Math.round(new BigDecimal(feeFundAmount).divide(new BigDecimal(netFeeFundAmount), 20, RoundingMode.HALF_UP).multiply(new BigDecimal(paidInFee)).doubleValue());
            }
        }
        //银行卡退款手续费计算 使用手续费的比例计算
        if (payway == Order.PAYWAY_BANKCARD || payway == Order.PAYWAY_BANKACCOUNT) {
            //该笔流水应该退的手续费 总的手续费 * (退款金额 / 订单总金额)
            long localFee = Math.round(new BigDecimal(feeFundAmount).divide(new BigDecimal(originalPaidFeeAmount), 20, RoundingMode.HALF_UP).multiply(new BigDecimal(originalPaidInFee)).doubleValue());
            if (paidInFee - localFee < 0) {
                //如果剩下的手续费不够扣的话(向上取整) 那就用剩下的手续费
                return paidInFee;
            } else {
                return localFee;
            }
        }
        if(fee < 0){
            logger.warn("calculateFee data incorrect, fee < 0  fee:{}", fee);
            return approximateFee;
        }
        if(Math.abs(approximateFee - fee) > 1){
            logger.warn("calculateFee the gap of two method result is large: {}, {} ", approximateFee, fee);
            return approximateFee;
        }
        return  fee;
    }

    /**
     * 计算付款时或者预下单时候的手续费
     * @param payTransaction
     * @return
     */
    public static long calculatePayOrPrecreateFee(Map<String,Object> payTransaction){
        return internalCalculatePayOrPrecreateFee(payTransaction);
    }

    /**
     * 付款成功后重新计算手续费
     * (支付通道免充值的交易，需要重新计算)
     * @param payTransaction
     * @return
     */
    public static long reCalculatePayOrPrecreateFee(Map<String,Object> payTransaction){
        return internalCalculatePayOrPrecreateFee(payTransaction);
    }

    public static long reCalculatePayOrPrecreateFee(Map<String, Object> payTransaction, String feeRate) {
        return internalCalculatePayOrPrecreateFee(payTransaction, feeRate);
    }


    /**
     * 计算额度减免手续费
     *
     * @param transaction
     * @return
     */
    public static long calculateQuotaFee(Map<String, Object> transaction) {
        //额度包减免/回退手续费
        Long quotaFee = 0L;
        try {

            Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
            String quotaFeeRateTag = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE_TAG);
            String quotaFeeRate = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE);
            if (StringUtils.isEmpty(quotaFeeRateTag) || StringUtils.isEmpty(quotaFeeRate)) {
                return 0L;
            }
            long amount = calculatePayerAmount(transaction);
            quotaFee = applyRate(amount, quotaFeeRate);
        } catch (Exception e) {
            logger.error("计算额度减免手续费异常. tsn={}", BeanUtil.getPropString(transaction, Transaction.TSN), e);
        }
        return quotaFee;
    }

    /**
     * 计算付款时或者预下单时候的手续费
     * @param payTransaction
     * @return
     */
    private static long internalCalculatePayOrPrecreateFee(Map<String,Object> payTransaction){
        Map<String,Object> tradeParams = getTradeParamsFromConfigSnapshot((Map<String, Object>) BeanUtil.getProperty(payTransaction, Transaction.CONFIG_SNAPSHOT));
        return internalCalculatePayOrPrecreateFee(payTransaction, BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE));
    }

    /**
     * 计算付款时或者预下单时候的手续费
     * @param payTransaction
     * @return
     */
    private static long internalCalculatePayOrPrecreateFee(Map<String,Object> payTransaction,String feeRate){
        Map<String,Object> tradeParams = getTradeParamsFromConfigSnapshot((Map<String, Object>) BeanUtil.getProperty(payTransaction, Transaction.CONFIG_SNAPSHOT));
        long amount = calculatePayerAmount(payTransaction);
        if(StringUtils.isEmpty(feeRate)) {
            feeRate = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE);
        }
        return applyRate(amount, feeRate);
    }

    public static String calculateFeeRate(Map<String, Object> payTransaction, long feeVal) {
        long amount = calculatePayerAmount(payTransaction);
        return BigDecimal.valueOf(feeVal * 1.0D * 100 / amount).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString();
    }

    public static long calculatePayerAmount(Map<String,Object> payTransaction){
        long originalTotal = BeanUtil.getPropLong(payTransaction, Transaction.ORIGINAL_AMOUNT);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH);
        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        long hongbaoWosaiMchTotal = 0;
        long discountWosaiMchTotal = 0;
        long channelNotTopUp = 0; //渠道免充值金额
        if(payments != null){
            for(Map<String,Object> payment: payments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long paymentAmountTotal = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (type){
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchTotal = hongbaoWosaiMchTotal + paymentAmountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchTotal = discountWosaiMchTotal + paymentAmountTotal;
                        break;
                    default:
                        break;
                }
            }
        }
        if(channelPayments != null){
            for(Map<String,Object> channelPayment: channelPayments){
                String type = BeanUtil.getPropString(channelPayment, Payment.TYPE);
                long paymentAmountTotal = BeanUtil.getPropLong(channelPayment, Payment.AMOUNT);
                if(Payment.TYPE_HONGBAO_CHANNEL_MCH.equals(type) || Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                    channelNotTopUp = channelNotTopUp + paymentAmountTotal;
                }
            }
        }
        long amount = originalTotal - discountWosaiMchTotal - hongbaoWosaiMchTotal - channelNotTopUp;
        return amount;
    }

    /**
     * 计算此次交易余额变动金额
     * @param payments
     * @param originalAmount
     * @return
     */
    public static long calculateApproximateWalletChangeAmount(Map<String,Object> payTransaction, List<Map<String, Object>> payments,
                                                              long originalAmount,boolean refundAllOrCancel) {
        @SuppressWarnings("unchecked")
        Map<String, Object> configSnapshot = (Map<String, Object>) payTransaction.get(Transaction.CONFIG_SNAPSHOT);
        if (originalAmount == 0) {
            return 0L;
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> tradeParams = UpayUtil.getTradeParamsFromConfigSnapshot(configSnapshot);
        //全额退款
        if (refundAllOrCancel) {
            //结算金额 - 手续费
            return PaymentUtil.calculateSettlementAmountByTransaction(payTransaction) - BeanUtil.getPropLong(tradeParams, TransactionParam.FEE);
        }
        String feeRate = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE, "0.0");
        long hongbaoWosaiMchTotal = 0;
        long discountWosaiMchTotal = 0;
        if (payments != null) {
            for (Map<String, Object> payment : payments) {
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long paymentAmount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchTotal = hongbaoWosaiMchTotal + paymentAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchTotal = discountWosaiMchTotal + paymentAmount;
                        break;
                    default:
                        break;
                }
            }
        }
        long feeFundAmount = originalAmount - discountWosaiMchTotal - hongbaoWosaiMchTotal;
        //手续费此处不太精确
        return feeFundAmount - applyRefundRate(feeFundAmount, feeRate);
    }


    public static long applyRate(long amount, String rate) {
        if (rate == null){
            return 0;
        }
        long cents = StringUtils.yuan2cents(rate);

        return (amount*cents + 5000)/10000;
    }

    //向下取整数 防止退款的时候 少冻结余额
    public static long applyRefundRate(long amount, String rate) {
        if (rate == null){
            return 0;
        }
        long cents = StringUtils.yuan2cents(rate);

        return (amount*cents)/10000;
    }


    /**
     * 获取流水里面的手续费
     * @param transaction
     * @return
     */
    private static Long getFeeFromTransaction(Map<String,Object> transaction){
        long localOriginalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        Map<String,Object> tradeParams = getTradeParamsFromConfigSnapshot((Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT));
        if(tradeParams == null){
            logger.warn("can not find tradeParams, transaction: {}", transaction);
            return null;
        }
        List<Map<String,Object>> localPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH);
        long localHongbaoWosaiMchTotal = 0;
        long localDiscountWosaiMchTotal = 0;
        if(localPayments != null && localPayments.size() > 0){
            for(Map<String,Object> payment: localPayments){
                String paymentType = BeanUtil.getPropString(payment, Payment.TYPE);
                long paymentAmount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (paymentType){
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        localHongbaoWosaiMchTotal = localHongbaoWosaiMchTotal + paymentAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        localDiscountWosaiMchTotal = localDiscountWosaiMchTotal + paymentAmount;
                        break;
                    default:
                        break;
                }
            }
        }
        long fee = tradeParams.containsKey(TransactionParam.FEE) ? BeanUtil.getPropLong(tradeParams, TransactionParam.FEE) :
                applyRate((localOriginalAmount - localHongbaoWosaiMchTotal - localDiscountWosaiMchTotal), BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE));
        return fee;
    }



    /**
     * 取得交易参数
     * @param configSnapshot
     * @return
     */
    private static   Map getTradeParamsFromConfigSnapshot(Map<String, Object> configSnapshot) {
        if(configSnapshot == null){
            return null;
        }
        for(String key: configSnapshot.keySet()){
            Object value = configSnapshot.get(key);
            if(value instanceof  Map){
                if(BeanUtil.getPropBoolean(value, TransactionParam.ACTIVE, false)){
                    return (Map) value;
                }
            }
        }
        return null;
    }

    /**
     * 获取实际的费率
     *
     * @param baseFeeRate  基础费率
     * @param quotaFeeRate 优惠费率
     * @return
     */
    public static String getActualFeeRate(String baseFeeRate, String quotaFeeRate) {
        if(StringUtils.isEmpty(baseFeeRate)) {
            return ZERO_FEE_RATE;
        }
        if (StringUtils.isEmpty(quotaFeeRate)) {
            return baseFeeRate;
        }

        long baseFeeRateCents = StringUtils.yuan2cents(baseFeeRate);
        long quotaFeeRateCents = StringUtils.yuan2cents(quotaFeeRate);

        long actualFeeRateCents = baseFeeRateCents - quotaFeeRateCents;
        if (actualFeeRateCents < 0) {
            actualFeeRateCents = 0;
        }
        return StringUtils.cents2yuan(actualFeeRateCents);
    }

    /**
     * 重置浦发银行通道交易手续费， 正向交易最低1分钱， 逆向交易不退手续费
     * @param transaction
     * @param tradeParams
     */
    public static void resetSPDBTradeFee(Map<String, Object> transaction, Map<String, Object> tradeParams) {

        int provider = MapUtil.getIntValue(transaction, Transaction.PROVIDER);
        if (provider == Provider.SPDB.getCode()) {
            long fee = MapUtil.getLongValue(tradeParams, TransactionParam.FEE);
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            if (Transaction.TYPE_PAYMENT == type && fee < 1) {
                // 浦发银行通道，支付手续费最低1分钱
                tradeParams.put(TransactionParam.FEE, 1);
            } else if (Transaction.TYPE_REFUND == type || Transaction.TYPE_CANCEL == type) {
                // 浦发银行通道，退款｜撤单不退手续费
                tradeParams.put(TransactionParam.FEE, 0);
            }
        }
    }
}
