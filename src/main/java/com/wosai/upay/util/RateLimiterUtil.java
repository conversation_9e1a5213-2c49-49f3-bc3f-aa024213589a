package com.wosai.upay.util;

import java.time.LocalDate;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.RateLimiter;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.exception.RateLimiterError;


public class RateLimiterUtil {
    private static final Map<String, RateLimiter> RATE_LIMITERS = new ConcurrentHashMap<String, RateLimiter>();
    private static final Map<String, Map<String,Object>> PROVIDER_RATE_LIMITERS = new ConcurrentHashMap<String, Map<String,Object>>();

    private static final LoadingCache<LocalDate, Semaphore> DATE_SEMAPHORE_LOADING_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(25, TimeUnit.HOURS).maximumSize(2)
            .build(new CacheLoader<LocalDate, Semaphore>() {
                @Override
                public Semaphore load(LocalDate key) throws Exception {
                    return new Semaphore(rateConfig.getIntProperty(GEN_SN_COUNTER_CONFIG, 0), true);
                }
            });


    private static final String NAMESPACE_RATE = "rate";
    private static final String LIMITER_ENABLE = "limiter_enable";
    private static final String PROVIDER_LIMITER = "provider_limiter";
    private static final String GEN_SN_COUNTER_CONFIG = "gen_sn_per_day";
    private static Config rateConfig = null;
    
    public static void init(){
        // 终端限流配置
        rateConfig = ConfigService.getConfig(NAMESPACE_RATE);
        Set<String> rateKey = rateConfig.getPropertyNames();
        if(null != rateKey && rateKey.size() > 0){
            rateKey.forEach(key -> {
                if(PROVIDER_LIMITER.equals(key)){
                    PROVIDER_RATE_LIMITERS.clear();
                    PROVIDER_RATE_LIMITERS.putAll(JsonUtil.jsonStrToObject(rateConfig.getProperty(key, "{}"), Map.class));
                    return;
                    
                } else if (GEN_SN_COUNTER_CONFIG.equals(key)) {
                    DATE_SEMAPHORE_LOADING_CACHE.put(LocalDate.now()
                            , new Semaphore(rateConfig.getIntProperty(key, 0), true));
                } else if(!LIMITER_ENABLE.equals(key)){
                    String val = rateConfig.getProperty(key, null);
                    if(!StringUtils.isEmpty(val)){
                        try{
                            RATE_LIMITERS.put(key, RateLimiter.create(Double.parseDouble(val)));
                        }catch (Exception e){}
                    }
                }
            });
        }
        
        rateConfig.addChangeListener(changeEvent -> {
            Set<String> changedKeys = changeEvent.changedKeys();
            changedKeys.forEach(key -> {
                if(LIMITER_ENABLE.equals(key)){
                    return;
                }
                
                if(PROVIDER_LIMITER.equals(key)){
                    PROVIDER_RATE_LIMITERS.clear();
                    PROVIDER_RATE_LIMITERS.putAll(JsonUtil.jsonStrToObject(rateConfig.getProperty(key, "{}"), Map.class));
                    return;
                }
                if (GEN_SN_COUNTER_CONFIG.equals(key)) {
                    DATE_SEMAPHORE_LOADING_CACHE.put(LocalDate.now()
                            , new Semaphore(rateConfig.getIntProperty(key, 0), true));
                }
                
                String changeVal = rateConfig.getProperty(key, null);
                if(StringUtil.empty(changeVal) && RATE_LIMITERS.containsKey(key)){
                    RATE_LIMITERS.remove(key);
                }else{
                    try{
                        RATE_LIMITERS.put(key, RateLimiter.create(Double.parseDouble(changeVal)));
                    }catch (Exception e){}
                }
            });
        });
    }
    
    /**
     * 
     * 门店、终端、商户限流
     * 
     * @param sn 门店、终端和商户编号
     * @param op 功能操作 pay:支付；precreate：预下单；query：查单
     */
    public static void verification(String sn, String op) {
        verification(sn, op, Boolean.TRUE);
    }
    
    /**
     * 
     * 门店、终端、商户限流
     * 
     * @param sn 门店、终端和商户编号
     * @param op 功能操作 pay:支付；precreate：预下单；query：查单
     * @param verifiOp 是否验证功能限流
     * 
     */
    public static void verification(String sn, String op, boolean verifiOp) {
        if(rateConfig.getBooleanProperty(LIMITER_ENABLE, Boolean.FALSE) && !StringUtil.empty(sn)){
            Set<String> rateLmiterKeys = CollectionUtil.hashSet(StringUtil.join(ApolloConfigurationCenterUtil.DEFAULT_REGEX_KEY, sn, op), sn);
            if(verifiOp){
                rateLmiterKeys.add(op);
            }
            for (String rateKey : rateLmiterKeys) {
                if(null != RATE_LIMITERS.get(rateKey)){
                    if(!RATE_LIMITERS.get(rateKey).tryAcquire()) {
                        throw new RateLimiterError(UpayErrorScenesConstant.RATE_LIMITER, UpayErrorScenesConstant.RATE_LIMITER_MESSAGE);
                    }
                    return;
                }
            }
        }
    }

    public static Map<String, Map<String, Object>> getProviderRateLimiter() {
        return PROVIDER_RATE_LIMITERS;
    }

    /**
     * 对外开放单号生成接口限流，每日限定总数
     */
    public static void verifyGenSnRateLimit() {
        Semaphore semaphore;
        try {
            semaphore = DATE_SEMAPHORE_LOADING_CACHE.get(LocalDate.now());
        } catch (ExecutionException e) {
            throw new RateLimiterError(UpayErrorScenesConstant.RATE_LIMITER
                    , UpayErrorScenesConstant.RATE_LIMITER_MESSAGE);
        }
        if (!semaphore.tryAcquire()) {
            throw new RateLimiterError(UpayErrorScenesConstant.RATE_LIMITER
                    , UpayErrorScenesConstant.RATE_LIMITER_MESSAGE);
        }
    }

}
