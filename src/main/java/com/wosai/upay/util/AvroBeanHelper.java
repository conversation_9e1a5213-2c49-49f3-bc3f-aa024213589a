package com.wosai.upay.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.model.kafka.Transaction;

import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 21/03/2018.
 */
public class AvroBeanHelper {

    public static final ObjectMapper objectMapper = new ObjectMapper();


    public static Transaction getTransactionBeanFromMap(Map<String,Object> transactionMap) throws JsonProcessingException {
        Transaction transaction = new Transaction();
        transaction.setId(BeanUtil.getPropString(transactionMap, DaoConstants.ID));
        transaction.setTsn(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.TSN));
        transaction.setClientTsn(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.CLIENT_TSN));
        transaction.setType(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.TYPE));
        transaction.setSubject(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.SUBJECT));
        transaction.setBody(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.BODY));
        transaction.setStatus(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.STATUS));
        transaction.setEffectiveAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.EFFECTIVE_AMOUNT));
        transaction.setOriginalAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.ORIGINAL_AMOUNT));
        transaction.setPaidAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.PAID_AMOUNT));
        transaction.setReceivedAmount(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.RECEIVED_AMOUNT));
        transaction.setItems(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.ITEMS))));
        transaction.setBuyerUid(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.BUYER_UID));
        transaction.setBuyerLogin(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.BUYER_LOGIN));
        transaction.setMerchantId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.MERCHANT_ID));
        transaction.setStoreId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.STORE_ID));
        transaction.setTerminalId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.TERMINAL_ID));
        transaction.setOperator(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.OPERATOR));
        transaction.setOrderSn(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.ORDER_SN));
        transaction.setOrderId(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.ORDER_ID));
        transaction.setProvider(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.PROVIDER));
        transaction.setPayway(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.PAYWAY));
        transaction.setSubPayway(BeanUtil.getPropInt(transactionMap, com.wosai.upay.model.dao.Transaction.SUB_PAYWAY));
        transaction.setTradeNo(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.TRADE_NO));
        transaction.setProductFlag(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.PRODUCT_FLAG));
        transaction.setExtraParams(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.EXTRA_PARAMS))));
        transaction.setExtraOutFields(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.EXTRA_OUT_FIELDS))));
        transaction.setExtendedParams(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.EXTENDED_PARAMS))));
        transaction.setReflect(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.REFLECT))));
        transaction.setConfigSnapshot(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.CONFIG_SNAPSHOT))));
        transaction.setFinishTime(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.FINISH_TIME));
        transaction.setChannelFinishTime(BeanUtil.getPropLong(transactionMap, com.wosai.upay.model.dao.Transaction.CHANNEL_FINISH_TIME));
        transaction.setBizErrorCode(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.BIZ_ERROR_CODE))));
        transaction.setProviderErrorInfo(ByteBuffer.wrap(objectMapper.writeValueAsBytes(transactionMap.get(com.wosai.upay.model.dao.Transaction.PROVIDER_ERROR_INFO))));
        transaction.setCtime(BeanUtil.getPropLong(transactionMap, DaoConstants.CTIME));
        transaction.setMtime(BeanUtil.getPropLong(transactionMap, DaoConstants.MTIME));
        transaction.setDeleted(BeanUtil.getPropBoolean(transactionMap, DaoConstants.DELETED));
        transaction.setVersion(BeanUtil.getPropLong(transactionMap, DaoConstants.VERSION));
        transaction.setNfcCard(BeanUtil.getPropString(transactionMap, com.wosai.upay.model.dao.Transaction.NFC_CARD));
        return transaction;
    }


    //fixme implement
    public static Map<String,Object> getTransactionMapFromBean(Transaction transaction){
        Map<String,Object> transactionMap = new ConcurrentHashMap<>();

        return  transactionMap;
    }

}
