package com.wosai.upay.util;

import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/4/1.
 */
public class ProviderUtil {

    private static final Map<String, String> DEFAULT_PAYWAY_TYPE_PAYMENT_TYPE_CONFIG = CollectionUtil.hashMap(
            Order.PAYWAY_ALIPAY + "", Payment.TYPE_WALLET_ALIPAY,
            Order.PAYWAY_ALIPAY2 + "", Payment.TYPE_WALLET_ALIPAY,
            Order.PAYWAY_WEIXIN + "", Payment.TYPE_WALLET_WEIXIN,
            Order.PAYWAY_UNIONPAY + "", Payment.TYPE_BANKCARD
    );

    /**
     * 获取默认的支付类型
     * @param payway
     * @return
     */
    public static String getDefaultPaymentType(String payway){
        String paymentType = DEFAULT_PAYWAY_TYPE_PAYMENT_TYPE_CONFIG.get(payway);
        return paymentType == null ? Payment.TYPE_OTHERS : paymentType;
    }

    /**
     * 获取字符串，如果为空，则返回默认值
     * @param str
     * @param defaultStr
     * @return
     */
    public static String getOrDefault(String str, String defaultStr){
        return StringUtils.isEmpty(str) ? defaultStr : str;
    }

    /**
     * 更新map的值，如果map中不存在该字段，则从response中取值
     * @param map
     * @param field
     * @param response
     * @param responseField
     */
    public static void updateMapIfResponseNotNull(Map<String, Object> map, String field, Map<String, Object> response, String responseField){
        if(response == null){
            return;
        }
        String value = com.wosai.pantheon.util.MapUtil.getString(map, field);
        if(value == null){
            String responseValue = com.wosai.pantheon.util.MapUtil.getString(response, responseField);
            if(!StringUtils.isEmpty(responseValue)){
                map.put(field, responseValue);
            }
        }
    }

    /**
     *  更新map的值，如果map中不存在该字段，则从response中取值， 并使用responseValueConverter转换
     * @param map
     * @param field
     * @param response
     * @param responseField
     * @param responseValueConverter
     */
    public static void updateMapIfResponseNotNull(Map<String, Object> map, String field, Map<String, Object> response, String responseField, Function responseValueConverter){
        if(response == null){
            return;
        }
        String value = com.wosai.pantheon.util.MapUtil.getString(map, field);
        if(value == null){
            String responseValue = com.wosai.pantheon.util.MapUtil.getString(response, responseField);
            if(!StringUtils.isEmpty(responseValue)){
                map.put(field, responseValueConverter.apply(responseValue));
            }
        }
    }

    /**
     *
     * @param map
     * @param field
     * @param response
     * @param responseFields 按照这个顺序进行取值处理， 如果有值，则取第一个不为空的值
     * @param responseValueConverter
     */
    public static void updateMapIfResponseNotNull(Map<String, Object> map, String field, Map<String, Object> response, List<String> responseFields, Function responseValueConverter){
        if(response == null){
            return;
        }
        String value = com.wosai.pantheon.util.MapUtil.getString(map, field);
        if(value == null){
            String responseValue = null;
            for (String responseField : responseFields) {
                responseValue = com.wosai.pantheon.util.MapUtil.getString(response, responseField);
                if(!StringUtils.isEmpty(responseValue)){
                    break;
                }
            }
            if(!StringUtils.isEmpty(responseValue)){
                map.put(field, responseValueConverter.apply(responseValue));
            }
        }
    }

    /**
     *
     * @param map
     * @param field
     * @param response
     * @param concatFields
     */
    public static void updateMapIfResponseNotNull(Map<String, Object> map, String field, Map<String, Object> response, List<String> concatFields){
        if(response == null){
            return;
        }
        String value = com.wosai.pantheon.util.MapUtil.getString(map, field);
        if(value == null){
            String mergeResponseValue = null;
            for(String concatField : concatFields){
                String responseValue = MapUtil.getString(response, concatField);
                if(!StringUtils.isEmpty(responseValue)){
                    mergeResponseValue = mergeResponseValue == null ? responseValue : (mergeResponseValue + " " + responseValue);
                }
            }
            if(!StringUtils.isEmpty(mergeResponseValue)){
                map.put(field, mergeResponseValue);
            }
        }
    }

}
