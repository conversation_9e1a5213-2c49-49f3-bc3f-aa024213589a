package com.wosai.upay.util;

/**
 * Created by jian<PERSON> on 24/5/17.
 */
public class WalletDeductionContextHolder {
    private static ThreadLocal<String> merchantIdThreadLocal = new ThreadLocal<>();
    private static ThreadLocal<String> transactionIdThreadLocal = new ThreadLocal<>();


    public static void setContextHolder(String merchantId, String transactionId){
        merchantIdThreadLocal.set(merchantId);
        transactionIdThreadLocal.set(transactionId);
    }

    public static void remove(){
        merchantIdThreadLocal.remove();
        transactionIdThreadLocal.remove();
    }

    public static String getMerchantId(){
        return merchantIdThreadLocal.get();
    }

    public static String getTransactionId(){
        return transactionIdThreadLocal.get();
    }
}
