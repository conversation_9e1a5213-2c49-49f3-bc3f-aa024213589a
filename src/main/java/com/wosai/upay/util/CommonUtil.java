package com.wosai.upay.util;

import com.wosai.mpay.api.pab.PabConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @Date 2024/7/11、13:53
 **/

public class CommonUtil {

    public static final Logger logger = LoggerFactory.getLogger(CommonUtil.class);




    public static String getTime(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }

    public static String getTime(String format,long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(timeStamp));
    }



    public static String getIPv4Address(String address) {
        try {
            InetAddress inetAddress = InetAddress.getByName(address);
            if (inetAddress instanceof java.net.Inet4Address) {
                // 如果该地址是 IPv4 地址，则直接返回
                return address;
            } else if (inetAddress instanceof java.net.Inet6Address) {
                // 如果该地址是 IPv6 地址，则获取本机的 IPv4 地址
                Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                while (networkInterfaces.hasMoreElements()) {
                    NetworkInterface ni = networkInterfaces.nextElement();
                    Enumeration<InetAddress> ips = ni.getInetAddresses();
                    while (ips.hasMoreElements()) {
                        InetAddress ip = ips.nextElement();
                        if (ip instanceof java.net.Inet4Address) {
                            return ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取ipv4地址失败", e);
        }
        return PabConstant.DEFAULT_IP;
    }



}
