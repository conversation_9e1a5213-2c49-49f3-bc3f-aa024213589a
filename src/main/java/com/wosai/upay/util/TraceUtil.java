package com.wosai.upay.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * Description: This is a description of the class.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/6/21
 */
public class TraceUtil {


    public static final String TRACE_ID = "trace_id";

    public static void createTraceId() {
        try {
            String traceId = MDC.get(TRACE_ID);
            if (StringUtils.isNotEmpty(traceId)) {
                return;
            }
            traceId = UUID.randomUUID().toString().replace("-", "");
            MDC.put(TRACE_ID, traceId);
        } catch (Throwable e) {
        }
    }

    public static void removeTraceId() {
        try {
            MDC.remove(TRACE_ID);
        } catch (Throwable e) {
        }
    }
}
