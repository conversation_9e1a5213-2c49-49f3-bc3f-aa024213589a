package com.wosai.upay.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.wosai.data.jackson.RowDeserializerInstantiator;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/10.
 */
public class ProfitSharingJsonRpcUtil {

    /**
     * 分账服务专用
     */
    public static ObjectMapper objectMapper;
    static {
        objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);  // ignore unknown properties during deserialization
        objectMapper.setHandlerInstantiator(new RowDeserializerInstantiator());  // customized deserialization process from blob to java bean
    }

    public static ObjectMapper profitSharingJsonRpcObjectMapper(){
        return objectMapper;
    }
}
