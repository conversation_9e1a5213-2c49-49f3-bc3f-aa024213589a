package com.wosai.upay.util;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by jian<PERSON> on 19/9/16.
 * SimpleDateFormat 线程不安全。
 * 为了性能考虑，采用ThreadLocal 方案来实现一个只支持部分方法的SimpleDateFormat的替代实现。
 *
 */
public class SafeSimpleDateFormat {
    private String pattern;

    private ThreadLocal<SimpleDateFormat> threadLocal = new ThreadLocal<>();


    public SafeSimpleDateFormat(String pattern){
        this.pattern = pattern;
        getSimpleDateFormat();
    }

    public Date parse(String source) throws ParseException {
        return getSimpleDateFormat().parse(source);
    }

    public String format(Date date) {
        return getSimpleDateFormat().format(date);
    }

    private SimpleDateFormat getSimpleDateFormat(){
        SimpleDateFormat sdf = threadLocal.get();
        if(threadLocal.get() == null){
            sdf = new SimpleDateFormat(this.pattern);
            threadLocal.set(sdf);
        }
        return sdf;
    }

    public void setTimeZone(TimeZone zone){
        getSimpleDateFormat().setTimeZone(zone);
    }
}
