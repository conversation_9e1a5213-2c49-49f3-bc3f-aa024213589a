package com.wosai.upay.util;

import com.google.common.collect.ImmutableList;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.service.ExternalServiceFacade.TcpPayResult.TcpPay;
import com.wosai.upay.service.ExternalServiceFacade.TcpRefundResult.TcpRefund;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by jianfree on 7/3/17.
 */
public class PaymentUtil {

    private static final Logger logger = LoggerFactory.getLogger(PaymentUtil.class);

    public static final String ORDER_PAYMENTS_PATH = Order.ITEMS + "." + Order.PAYMENTS;
    public static final String ORDER_CHANNEL_PAYMENTS_PATH = Order.ITEMS + "." + Order.CHANNEL_PAYMENTS;
    public static final String TRANSACTION_PAYMENTS_PATH = Transaction.ITEMS + "." + Transaction.PAYMENTS;
    public static final String TRANSACTION_CHANNEL_PAYMENTS_PATH = Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYMENTS;
    public static final String TRANSACTION_CHANNEL_DEPOSIT_REFUND_PAYMENTS_PATH = Transaction.EXTRA_OUT_FIELDS + "." + Transaction.DEPOSIT_REFUND_PAYMENTS;
    public static final String TRANSACTION_PAYER_INFO_PATH = Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYER_INFO;

    public static final List<String> MCH_TYPE_LIST = ImmutableList.of(Payment.TYPE_DISCOUNT_CHANNEL_MCH, Payment.TYPE_HONGBAO_CHANNEL_MCH);

    /**
     * @param tcpRefundResult
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> buildTransactionPaymentsForRefundOrCancel(ExternalServiceFacade.TcpRefundResult tcpRefundResult) {
        List<Map<String, Object>> transactionPayments = new ArrayList<>();
        Long hongbaoWosaiTotal = tcpRefundResult.getHongbaoWosaiAmount();
        Long hongbaoWosaiMchTotal = tcpRefundResult.getHongbaoWosaiMchAmount();
        Long discountWosaiTotal = tcpRefundResult.getDiscountWosaiAmount();
        Long discountWosaiMchTotal = tcpRefundResult.getDiscountWosaiMchAmount();
        if (hongbaoWosaiTotal != null && hongbaoWosaiTotal != 0) {
            for (TcpRefund tcpInfo : tcpRefundResult.getHongbaoWosai()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_HONGBAO_WOSAI, tcpInfo));
            }
        }
        if (hongbaoWosaiMchTotal != null && hongbaoWosaiMchTotal != 0) {
            for (TcpRefund tcpInfo : tcpRefundResult.getHongbaoWosaiMch()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_HONGBAO_WOSAI_MCH, tcpInfo));
            }
        }
        if (discountWosaiTotal != null && discountWosaiTotal != 0) {
            for (TcpRefund tcpInfo : tcpRefundResult.getDiscountWosai()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_DISCOUNT_WOSAI, tcpInfo));
            }
        }
        if (discountWosaiMchTotal != null && discountWosaiMchTotal != 0) {
            for (TcpRefund tcpInfo : tcpRefundResult.getDiscountWosaiMch()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_DISCOUNT_WOSAI_MCH, tcpInfo));
            }
        }
        return transactionPayments;
    }
    

    /**
     *  通过活动服务返回的结果构造流水的优惠明细
     * @param tcpPayResult
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> buildTransactionPaymentsForPay(ExternalServiceFacade.TcpPayResult tcpPayResult) {
        Long hongbaoWosaiTotal = tcpPayResult.getHongbaoWosaiAmount();
        Long hongbaoWosaiMchTotal = tcpPayResult.getHongbaoWosaiMchAmount();
        Long discountWosaiTotal = tcpPayResult.getDiscountWosaiAmount();
        Long discountWosaiMchTotal = tcpPayResult.getDiscountWosaiMchAmount();
        List<Map<String, Object>> transactionPayments = new ArrayList<>();
        if (hongbaoWosaiTotal != null && hongbaoWosaiTotal != 0) {
            for (TcpPay tcpInfo : tcpPayResult.getHongbaoWosai()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_HONGBAO_WOSAI, tcpInfo));
            }
        }
        if (hongbaoWosaiMchTotal != null && hongbaoWosaiMchTotal != 0) {
            for (TcpPay tcpInfo : tcpPayResult.getHongbaoWosaiMch()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_HONGBAO_WOSAI_MCH, tcpInfo));
            }
        }
        if (discountWosaiTotal != null && discountWosaiTotal != 0) {
            for (TcpPay tcpInfo : tcpPayResult.getDiscountWosai()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_DISCOUNT_WOSAI, tcpInfo));
            }
        }
        if (discountWosaiMchTotal != null && discountWosaiMchTotal != 0) {
            for (TcpPay tcpInfo : tcpPayResult.getDiscountWosaiMch()) {
                transactionPayments.add(buildTransactionPayment(Payment.TYPE_DISCOUNT_WOSAI_MCH, tcpInfo));
            }
        }
        return transactionPayments;
    }

    /**
     *  通过业务方上送的结果构造流水的优惠明细
     * @param promotionDetails
     * @return
     */
    public static List<Map<String, Object>> buildTransactionPayments(List<Map<String, Object>> promotionDetails) {

        List<Map<String, Object>> transactionPayments = new ArrayList<>();

        Set<String> promotions = new HashSet<>();

        promotionDetails.forEach(detail -> {
            String type = MapUtils.getString(detail, Payment.TYPE);
            String originType = MapUtils.getString(detail, Payment.ORIGIN_TYPE);
            String source = MapUtils.getString(detail, Payment.SOURCE);
            Long amount = MapUtils.getLong(detail, Payment.AMOUNT);
            // 强校验各个字段，以及业务唯一性约束
            if (StringUtils.isBlank(type) || StringUtils.isBlank(originType)
                    || !Payment.TYPE_WOSAI_DISCOUNT_SET.contains(type)
                    || Objects.isNull(amount) || amount <= 0
                    || promotions.contains(String.format("%s:%s", originType, source))) {
                throw new UpayBizException(UpayErrorScenesConstant.UPAY_ORDER_PAY_SQB_PROMOTION_DETAIL_ERROR, UpayErrorScenesConstant.UPAY_ORDER_PAY_SQB_PROMOTION_DETAIL_ERROR_MESSAGE);
            }

            transactionPayments.add(buildTransactionPayment(type, detail));
            promotions.add(String.format("%s:%s", originType, source));
        });

        return transactionPayments;
    }

    /**
     *  通过业务方上送的结果构造流水的优惠明细
     * @param promotionDetails
     * @return
     */
    public static List<Map<String, Object>> buildTransactionRefundOrCancelPayments(Map<String, Object> order, List<Map<String, Object>> promotionDetails) {

        List<Map<String, Object>> transactionPayments = new ArrayList<>();
        List<Map<String,Object>> orderPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH);
        if (CollectionUtils.isEmpty(orderPayments)) {
            return transactionPayments;
        }

        Map<String, Map<String, Object>> paymentsMap = orderPayments.stream().filter(item -> MapUtils.getBooleanValue(item, Payment.IS_DELIVERY))
                .collect(Collectors.toMap(item -> String.format("%s:%s", MapUtils.getString(item, Payment.ORIGIN_TYPE), MapUtils.getString(item, Payment.SOURCE)), Function.identity()));

        promotionDetails.forEach(detail -> {
            String type = MapUtils.getString(detail, Payment.TYPE);
            Long amount = MapUtils.getLong(detail, Payment.AMOUNT);
            String originType = MapUtils.getString(detail, Payment.ORIGIN_TYPE);
            String source  = MapUtils.getString(detail, Payment.SOURCE);

            Set<String> promotions = new HashSet<>();
            // 强校验各个字段，以及业务唯一性约束
            if (StringUtils.isBlank(type) || StringUtils.isBlank(originType)
                    || !Payment.TYPE_WOSAI_DISCOUNT_SET.contains(type)
                    || Objects.isNull(amount) || amount <= 0
                    || promotions.contains(String.format("%s:%s", originType, source))
                    || !paymentsMap.containsKey(String.format("%s:%s", originType, source))) {
                throw new UpayBizException(UpayErrorScenesConstant.UPAY_ORDER_REFUND_OR_CANCEL_SQB_PROMOTION_DETAIL_ERROR, UpayErrorScenesConstant.UPAY_ORDER_REFUND_OR_CANCEL_SQB_PROMOTION_DETAIL_ERROR_MESSAGE);
            }

            transactionPayments.add(buildTransactionPayment(type, detail));
            promotions.add(String.format("%s:%s", originType, source));
        });

        return transactionPayments;
    }

    private static Map<String,Object> buildTransactionPayment(String type, TcpPay tcpInfo){
        return CollectionUtil.hashMap(
                Payment.TYPE, type,
                Payment.AMOUNT, tcpInfo.getAmount(),
                Payment.SOURCE, tcpInfo.getSource(),
                Payment.ORIGIN_TYPE, tcpInfo.getOriginalType(),
                Payment.ORIGIN_NAME, tcpInfo.getOriginalName()
        );
    }

    private static Map<String,Object> buildTransactionPayment(String type, TcpRefund tcpInfo){
        return CollectionUtil.hashMap(
                Payment.TYPE, type,
                Payment.AMOUNT, tcpInfo.getAmount(),
                Payment.SOURCE, tcpInfo.getSource(),
                Payment.ORIGIN_TYPE, tcpInfo.getOriginalType(),
                Payment.ORIGIN_NAME, tcpInfo.getOriginalName()
        );
    }

    private static Map<String,Object> buildTransactionPayment(String type, Map<String, Object> promotion){
        return CollectionUtil.hashMap(
                Payment.TYPE, type,
                Payment.AMOUNT, MapUtils.getLong(promotion, Payment.AMOUNT),
                Payment.SOURCE, MapUtils.getString(promotion, Payment.SOURCE),
                Payment.IS_DELIVERY, true,
                Payment.ORIGIN_TYPE, MapUtils.getString(promotion, Payment.ORIGIN_TYPE),
                Payment.ORIGIN_NAME, MapUtils.getString(promotion, Payment.ORIGIN_NAME)
        );
    }


    public static List<Map<String, Object>> buildOrderPaymentsForPay(List<Map<String,Object>> transactionPayments){
        List<Map<String, Object>> orderPayments = new ArrayList<>();
        if(transactionPayments == null || transactionPayments.isEmpty()){
            return orderPayments;
        }
        for (Map<String, Object> transactionPayment : transactionPayments) {
            Map<String,Object> orderPayment = new LinkedHashMap<>(transactionPayment);
            Object amount = orderPayment.get(Payment.AMOUNT);
            orderPayment.put(Payment.AMOUNT_TOTAL, amount);
            orderPayment.put(Payment.NET_AMOUNT, amount);
            orderPayments.add(orderPayment);
        }
        return orderPayments;
    }



    /**
     * 退款成功后根据流水金额的变动金额信息更新订单里面的payments的剩余金额字段信息
     * @param orderPayments
     * @param transactionPayments
     */
    public static void updateOrderPaymentsNetAmountForRefundSuccess(List<Map<String, Object>> orderPayments, List<Map<String, Object>> transactionPayments) {
        // 将流水payments变动金额依次分配到订单payments相同类型type的剩余金额字段中
        if (orderPayments != null && orderPayments.size() > 0 && transactionPayments != null && transactionPayments.size() > 0) {

            List<Map<String, Object>> deliveryTransPayments = new ArrayList<>();
            List<Map<String, Object>> commonTransPayments = new ArrayList<>();
            for (Map<String, Object> transactionPayment : transactionPayments) {
                if(MapUtils.getBooleanValue(transactionPayment, Payment.IS_DELIVERY)){
                    deliveryTransPayments.add(transactionPayment);
                } else {
                    commonTransPayments.add(transactionPayment);
                }
            }

            //上送优惠的payments计算
            processDeliveryPaymentsNetAmountForRefundSuccess(orderPayments, deliveryTransPayments);
            //普通优惠的payments计算
            processCommonPaymentsNetAmountForRefundSuccess(orderPayments, commonTransPayments);
        }
    }

    public static void processDeliveryPaymentsNetAmountForRefundSuccess(List<Map<String, Object>> orderPayments, List<Map<String, Object>> deliveryTransPayments) {
        // 将流水payments变动金额依次分配到订单payments相同类型type的剩余金额字段中
        if (CollectionUtils.isNotEmpty(orderPayments) && CollectionUtils.isNotEmpty(deliveryTransPayments)) {

            Map<String, Map<String, Object>> paymentsMap = deliveryTransPayments.stream().filter(item -> MapUtils.getLongValue(item, Payment.AMOUNT) > 0)
                    .collect(Collectors.toMap(item -> String.format("%s:%s", MapUtils.getString(item, Payment.ORIGIN_TYPE), MapUtils.getString(item, Payment.SOURCE)), Function.identity()));

            if (MapUtils.isEmpty(paymentsMap)) {
                return;
            }

            for (Map<String, Object> orderPayment : orderPayments) {
                boolean isDelivery = MapUtils.getBooleanValue(orderPayment, Payment.IS_DELIVERY);
                long orderNetAmount = MapUtils.getLongValue(orderPayment, Payment.NET_AMOUNT);
                if (orderNetAmount <= 0 || !isDelivery) {
                    continue;
                }
                String originType = MapUtils.getString(orderPayment, Payment.ORIGIN_TYPE);
                String source = MapUtils.getString(orderPayment, Payment.SOURCE);

                if (paymentsMap.containsKey(String.format("%s:%s", originType, source))) {
                    Map<String, Object> transactionPayment = paymentsMap.get(String.format("%s:%s", originType, source));
                    long transactionAmount = BeanUtil.getPropLong(transactionPayment, Payment.AMOUNT);
                    orderPayment.put(Payment.NET_AMOUNT, orderNetAmount - transactionAmount);
                }
            }
        }
    }

    public static void processCommonPaymentsNetAmountForRefundSuccess(List<Map<String, Object>> orderPayments, List<Map<String, Object>> commonTransPayments) {
        // 将流水payments变动金额依次分配到订单payments相同类型type的剩余金额字段中
        if (CollectionUtils.isNotEmpty(orderPayments) && CollectionUtils.isNotEmpty(commonTransPayments)) {

            for (Map<String, Object> transactionPayment : commonTransPayments) {
                String transactionPaymentType = BeanUtil.getPropString(transactionPayment, Payment.TYPE);
                long transactionAmount = BeanUtil.getPropLong(transactionPayment, Payment.AMOUNT);

                for (Map<String, Object> orderPayment : orderPayments){
                    boolean isDelivery = MapUtils.getBooleanValue(orderPayment, Payment.IS_DELIVERY);
                    long orderNetAmount = BeanUtil.getPropLong(orderPayment, Payment.NET_AMOUNT);
                    if(orderNetAmount <= 0 || transactionAmount <= 0 || isDelivery){
                        continue;
                    }
                    String  orderPaymentType = BeanUtil.getPropString(orderPayment, Payment.TYPE);
                    if (orderPaymentType.equals(transactionPaymentType)) {
                        long currentRemainAmount = transactionAmount - orderNetAmount;
                        if(currentRemainAmount >= 0){
                            orderPayment.put(Payment.NET_AMOUNT, 0);
                            transactionAmount = currentRemainAmount;
                        }else{
                            orderPayment.put(Payment.NET_AMOUNT, orderNetAmount - transactionAmount);
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 收钱吧预授权完成　freezePayments - refundPayments
     *
     * @param freezePayments
     * @param refundPayments
     */
    public static long updateSqbDepositPaymentsAmountForRefundSuccess(List<Map<String, Object>> freezePayments, List<Map<String, Object>> refundPayments) {
        if (CollectionUtils.isEmpty(freezePayments) || CollectionUtils.isEmpty(refundPayments)) {
            return 0;
        }
        /*
        逻辑:
        1. 先正常分配扣减金额
        2. 出现不够扣情况再扣减其它mch金额
         */
        long diffRefundAmount = 0L;
        for (Map<String, Object> refundPayment : refundPayments) {
            String refundPaymentType = BeanUtil.getPropString(refundPayment, Payment.TYPE);
            long refundAmount = BeanUtil.getPropLong(refundPayment, Payment.AMOUNT);
            for (Map<String, Object> freezePayment : freezePayments) {
                long freezeAmount = BeanUtil.getPropLong(freezePayment, Payment.AMOUNT);
                if (freezeAmount <= 0 || refundAmount <= 0) {
                    continue;
                }
                String freezePaymentType = BeanUtil.getPropString(freezePayment, Payment.TYPE);
                if (freezePaymentType.equals(refundPaymentType)) {
                    long currentRemainAmount = refundAmount - freezeAmount;
                    if (currentRemainAmount >= 0) {
                        freezePayment.put(Payment.AMOUNT, 0);
                        refundAmount = currentRemainAmount;
                    } else {
                        freezePayment.put(Payment.AMOUNT, freezeAmount - refundAmount);
                        refundAmount = 0;
                        break;
                    }
                }
            }
            if (MCH_TYPE_LIST.contains(refundPaymentType)) {
                diffRefundAmount += refundAmount;
            }
        }
        if (diffRefundAmount <= 0) {
            return 0;
        }
        //存在差异, 不够扣减
        logger.warn("收钱吧预授权完成扣减存在差异. diffRefundAmount:{}", diffRefundAmount);
        for (Map<String, Object> freezePayment : freezePayments) {
            long freezeAmount = BeanUtil.getPropLong(freezePayment, Payment.AMOUNT);
            if (freezeAmount <= 0 || diffRefundAmount <= 0) {
                continue;
            }
            String freezePaymentType = BeanUtil.getPropString(freezePayment, Payment.TYPE);
            if (!MCH_TYPE_LIST.contains(freezePaymentType)) {
                continue;
            }
            long currentRemainAmount = diffRefundAmount - freezeAmount;
            if (currentRemainAmount >= 0) {
                freezePayment.put(Payment.AMOUNT, 0);
                diffRefundAmount = currentRemainAmount;
            } else {
                freezePayment.put(Payment.AMOUNT, freezeAmount - diffRefundAmount);
                diffRefundAmount = 0;
                break;
            }
        }
        logger.warn("收钱吧预授权完成扣减存在差异. after diffRefundAmount:{}", diffRefundAmount);
        return diffRefundAmount;
    }

    /**
     * 免充值金额汇总
     *
     * @param payments
     * @return
     */
    public static long sumMchAmount(List<Map<String, Object>> payments) {
        if (CollectionUtils.isEmpty(payments)) {
            return 0L;
        }
        long amount = 0L;
        for (Map<String, Object> freezePayment : payments) {
            String freezePaymentType = BeanUtil.getPropString(freezePayment, Payment.TYPE);
            if (!MCH_TYPE_LIST.contains(freezePaymentType)) {
                continue;
            }
            amount += BeanUtil.getPropLong(freezePayment, Payment.AMOUNT);
        }
        return amount;
    }

    /**
     * 退款撤销成功后根据流水金额的变动金额信息更新订单里面的payments的剩余金额字段信息
     * @param orderPayments
     */
    public static void updateOrderPaymentsNetAmountForRevokeRefundSuccess(List<Map<String, Object>> orderPayments, List<Map<String, Object>> transactionPayments) {
        if (orderPayments != null && orderPayments.size() > 0) {
            for (Map<String, Object> orderPayment : orderPayments) {
                String  orderPaymentType = BeanUtil.getPropString(orderPayment, Payment.TYPE);
                for (Map<String, Object> transactionPayment : transactionPayments) {
                    String transactionPaymentType = BeanUtil.getPropString(transactionPayment, Payment.TYPE);
                    if (orderPaymentType.equals(transactionPaymentType)) {
                        long orderNetAmount = BeanUtil.getPropLong(orderPayment, Payment.NET_AMOUNT);
                        long transactionAmount = BeanUtil.getPropLong(transactionPayment, Payment.AMOUNT);
                        orderPayment.put(Payment.NET_AMOUNT, orderNetAmount + transactionAmount);
                        break;
                    }
                }
            }
        }

    }

    /**
     * 更新订单里面的payments的剩余金额字段值
     * @param orderPayments
     */
    public static void updateOrderPaymentsNetAmountForCancelSuccess(List<Map<String, Object>> orderPayments) {
        updateOrderPaymentsNetAmountToZero(orderPayments);

    }

    /**
     * 根据付款流水的信息去更新撤单或者全额退款流水的paid_amount receive_amount 支付通道的payments信息
     * @param payTransaction
     * @param cancelOrRefundTransaction
     */
    public static void copyChannelPaymentsToCancelOrFullRefundTransaction(Map<String,Object> payTransaction, Map<String,Object> cancelOrRefundTransaction){
        List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(payments != null){
            BeanUtil.setNestedProperty(cancelOrRefundTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        }
        cancelOrRefundTransaction.put(Transaction.PAID_AMOUNT, BeanUtil.getPropLong(payTransaction, Transaction.PAID_AMOUNT));
        cancelOrRefundTransaction.put(Transaction.RECEIVED_AMOUNT, BeanUtil.getPropLong(payTransaction, Transaction.RECEIVED_AMOUNT));
        //拷贝结算金额
        Map<String,Object> extraOutFields = (Map<String, Object>) BeanUtil.getProperty(payTransaction, Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields != null && extraOutFields.containsKey(Transaction.SETTLEMENT_AMOUNT)){
            BeanUtil.setNestedProperty(cancelOrRefundTransaction, Transaction.KEY_SETTLEMENT_AMOUNT, extraOutFields.get(Transaction.SETTLEMENT_AMOUNT));
        }
    }



    /**
     * 更新订单里面的payments的剩余金额字段值为0
     * @param orderPayments
     */
    public static void updateOrderPaymentsNetAmountToZero(List<Map<String, Object>> orderPayments) {
        if (orderPayments != null && orderPayments.size() > 0) {
            for (Map<String, Object> orderPayment : orderPayments) {
                orderPayment.put(Payment.NET_AMOUNT, 0L);
            }
        }

    }

    /**
     * 更新订单里面的payments的剩余金额字段值为总金额值
     * @param orderPayments
     */
    public static void updateOrderPaymentsNetAmountToAmountTotal(List<Map<String, Object>> orderPayments) {
        if (orderPayments != null && orderPayments.size() > 0) {
            for (Map<String, Object> orderPayment : orderPayments) {
                long amountTotal = BeanUtil.getPropLong(orderPayment, Payment.AMOUNT_TOTAL);
                orderPayment.put(Payment.NET_AMOUNT, amountTotal);
            }
        }
    }

    /**
     * 支付，退款，撤单，撤单退款成功后，更新订单里面渠道支付明细的总金额或者剩余金额
     * @param order
     * @param transaction
     */
    @SuppressWarnings("unchecked")
    public static void updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(Map<String,Object> order, Map<String,Object> transaction){
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        List<Map<String,Object>> orderChannelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_CHANNEL_PAYMENTS_PATH);
        List<Map<String,Object>> transactionChannelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_DEPOSIT_FREEZE || type == Transaction.TYPE_DEPOSIT_CONSUME){
            if(transactionChannelPayments != null && transactionChannelPayments.size() > 0){
                orderChannelPayments = new ArrayList<>();
                BeanUtil.setNestedProperty(order, PaymentUtil.ORDER_CHANNEL_PAYMENTS_PATH, orderChannelPayments);
                Map<String,Map<String,Object>> typeOrderChannelPayments = new HashMap<>();
                for(Map<String,Object> payment: transactionChannelPayments){
                    String paymentType = BeanUtil.getPropString(payment, Payment.TYPE);
                    Map<String,Object> map;
                    if(!typeOrderChannelPayments.containsKey(paymentType)){
                        map = CollectionUtil.hashMap(
                                    Payment.AMOUNT_TOTAL, 0l,
                                    Payment.NET_AMOUNT, 0l,
                                    Payment.TYPE, paymentType
                                );
                        typeOrderChannelPayments.put(paymentType, map);
                    }else{
                        map = typeOrderChannelPayments.get(paymentType);
                    }
                    map.put(Payment.AMOUNT_TOTAL, BeanUtil.getPropLong(map, Payment.AMOUNT_TOTAL) + BeanUtil.getPropLong(payment, Payment.AMOUNT));
                    map.put(Payment.NET_AMOUNT, BeanUtil.getPropLong(map, Payment.NET_AMOUNT) + BeanUtil.getPropLong(payment, Payment.AMOUNT));
                }
                BeanUtil.setNestedProperty(order, PaymentUtil.ORDER_CHANNEL_PAYMENTS_PATH, MapUtil.getValues(typeOrderChannelPayments));

            }
        }else if(type == Transaction.TYPE_CANCEL || type == Transaction.TYPE_DEPOSIT_CANCEL){
            if(orderChannelPayments != null && orderChannelPayments.size() > 0){
                for(Map<String,Object> payment: orderChannelPayments){
                    payment.put(Payment.NET_AMOUNT, 0l);
                }
            }
        }else if(type == Transaction.TYPE_REFUND_REVOKE || type == Transaction.TYPE_REFUND){
            if(transactionChannelPayments != null && transactionChannelPayments.size() > 0 && orderChannelPayments != null && orderChannelPayments.size() > 0){
                for(Map<String,Object> transactionPayment: transactionChannelPayments){
                    long amount = BeanUtil.getPropLong(transactionPayment, Payment.AMOUNT);
                    int flag  = (type == Transaction.TYPE_REFUND_REVOKE) ? 1 : -1;
                    for(Map<String,Object> orderPayment: orderChannelPayments){
                        if(BeanUtil.getPropString(orderPayment, Payment.TYPE, "").equals(BeanUtil.getPropString(transactionPayment, Payment.TYPE))){
                            long orderNetAmount = BeanUtil.getPropLong(orderPayment, Payment.NET_AMOUNT);
                            orderPayment.put(Payment.NET_AMOUNT, orderNetAmount + amount * flag);
                            break;
                        }
                    }
                }
            }
        }
    }


    /**
     * 是否是新的活动服务产生的优惠订单
     * @param order
     * @return
     */
    public static boolean isNewTradeCoprocessorOrder(Map<String, Object> order){
        Map<String,Object> items = (Map<String, Object>) order.get(Order.ITEMS);
        if(items != null && items.containsKey(Order.PAYMENTS)){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 生成支付明细列表
     * @param order
     * @param transaction
     * @return
     */
    public static List<Map<String, Object>> buildPaymentListForQueryAndPayAndRefundResponse(Map<String, Object> order, Map<String, Object> transaction){
        Map<String,Map<String,Object>> paymentMap = new HashMap<>();
        //计算payment.amount_total值, 初始化payment.amount值
        List<Map<String,Object>> wosaiPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(order, ORDER_PAYMENTS_PATH);
        if(wosaiPayments != null) {
            for(Map<String,Object> payment: wosaiPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long total  = BeanUtil.getPropLong(payment, Payment.AMOUNT_TOTAL);
                if(paymentMap.containsKey(type)){
                    Map<String,Object> map = paymentMap.get(type);
                    map.put(Payment.AMOUNT_TOTAL, BeanUtil.getPropLong(map, Payment.AMOUNT_TOTAL) + total + "");
                }else{
                    paymentMap.put(type, CollectionUtil.hashMap(
                            Payment.TYPE, type,
                            Payment.AMOUNT_TOTAL, total + "",
                            Payment.AMOUNT, "0"

                    ));
                }
            }
        }

        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(order, ORDER_CHANNEL_PAYMENTS_PATH);
        if(channelPayments != null) {
            for(Map<String,Object> payment: channelPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                if(type == null){
                    continue;
                }
                long total  = BeanUtil.getPropLong(payment, Payment.AMOUNT_TOTAL);
                if(paymentMap.containsKey(type)){
                    Map<String,Object> map = paymentMap.get(type);
                    map.put(Payment.AMOUNT_TOTAL, (BeanUtil.getPropLong(map, Payment.AMOUNT_TOTAL) + total) + "");
                }else{
                    paymentMap.put(type, CollectionUtil.hashMap(
                            Payment.TYPE, type,
                            Payment.AMOUNT_TOTAL, total + "",
                            Payment.AMOUNT, "0"

                    ));
                }
            }
        }
        //计算payment.amount本次变动的值
        wosaiPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, TRANSACTION_PAYMENTS_PATH);
        if(wosaiPayments != null) {
            for(Map<String,Object> payment: wosaiPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long amount  = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(paymentMap.containsKey(type)){
                    Map<String,Object> map = paymentMap.get(type);
                    map.put(Payment.AMOUNT, BeanUtil.getPropLong(map, Payment.AMOUNT) + amount + "");
                }
            }
        }

        channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(channelPayments != null) {
            for(Map<String,Object> payment: channelPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                if(type == null){
                    continue;
                }
                long amount  = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(paymentMap.containsKey(type)){
                    Map<String,Object> map = paymentMap.get(type);
                    map.put(Payment.AMOUNT, BeanUtil.getPropLong(map, Payment.AMOUNT) + amount + "");
                }
            }
        }
        return MapUtil.getValues(paymentMap);
    }

    /**
     * 根据流水计算此次流水对应的结算金额
     * @param transaction
     * @return
     */
    public static long calculateSettlementAmountByTransaction(Map<String,Object> transaction){
        long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        long settlementAmount = originalAmount;

        List<Map<String,Object>> wosaiPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, TRANSACTION_PAYMENTS_PATH);
        if(wosaiPayments != null) {
            for(Map<String,Object> payment: wosaiPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long amount  = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(Payment.TYPE_DISCOUNT_WOSAI_MCH.equals(type) || Payment.TYPE_HONGBAO_WOSAI_MCH.equals(type)){
                    settlementAmount = settlementAmount - amount;
                }

            }
        }

        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(channelPayments != null) {
            for(Map<String,Object> payment: channelPayments){
                String type = BeanUtil.getPropString(payment, Payment.TYPE);
                long amount  = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type) || Payment.TYPE_HONGBAO_CHANNEL_MCH.equals(type)){
                    settlementAmount = settlementAmount - amount;
                }

            }
        }
        return settlementAmount;
    }

}