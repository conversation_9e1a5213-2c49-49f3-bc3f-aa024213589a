package com.wosai.upay.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import com.wosai.data.bean.BeanUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description RegeoUDFUtil
 * @Date 2021/6/29 3:21 PM
 */
public class GeoUDFUtil {
    private static final Logger logger = LoggerFactory.getLogger(GeoUDFUtil.class);
    private static final String URL_REGEO = "https://restapi.amap.com/v3/geocode/regeo";
    private static final String APP_KEY = "9f0fdbd3675e3ee0dd600d76caa0b366";
    private static final CloseableHttpClient httClient;
    private static final int RETRY_TIME = 3;
    private static final String ADDRESS_PREFIX = "regeocode.addressComponent";
    private static final int DEVIATE_METER = 10 ; // 经纬度偏移的最大距离
    private static final int DEVIATE_ANGLE = 360 ; // 经纬度偏移的最大方位角

    static {
        RequestConfig requestConfig = RequestConfig
                .custom()
                .setSocketTimeout(3000)
                .setConnectTimeout(3000)
                .build();
        httClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
    }
    private static final RateLimiter rateLimiter = RateLimiter.create(1000);

    public static Map<String, Object> evaluate(String longitude, String latitude) {
        if (NumberUtils.isParsable(latitude) && NumberUtils.isParsable(longitude)){
            rateLimiter.acquire();
            String requestUrl = String.format("%s?location=%s,%s&key=%s&extensions=base&radius=0", URL_REGEO, longitude, latitude, ApolloConfigurationCenterUtil.getGeoUdfAppKey(APP_KEY));
            Map<String, Object> result = retryIfConnectFail(requestUrl);
            result.put("longitude", longitude);
            result.put("latitude", latitude);
            return result;
        }
        return null;
    }

    private static Map<String, Object> retryIfConnectFail(String url) {
        HttpGet request = new HttpGet(url);
        for (int i = 0; i < RETRY_TIME; i++) {
            try {
                CloseableHttpResponse response = httClient.execute(request);
                String resultStr = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject geoResponse = JSONObject.parseObject(resultStr);
                if ("1".equals(BeanUtil.getPropString(geoResponse, "status"))){
                    HashMap<String, Object> result = Maps.newHashMap();
                    result.put("province", BeanUtil.getNestedProperty(geoResponse, ADDRESS_PREFIX + "." + "province"));
                    result.put("city", BeanUtil.getNestedProperty(geoResponse, ADDRESS_PREFIX + "." + "city"));
                    result.put("country", BeanUtil.getNestedProperty(geoResponse, ADDRESS_PREFIX + "." + "country"));
                    result.put("adCode", BeanUtil.getNestedProperty(geoResponse, ADDRESS_PREFIX + "." + "adcode"));
                    result.put("addr", BeanUtil.getNestedProperty(geoResponse, "regeocode" + "." + "formatted_address"));
                    if ("[]".equals(BeanUtil.getPropString(result, "city"))){
                        result.put("city", result.get("province"));
                    }
                    return result;
                }
            } catch (IOException e) {
                logger.error("get geo error", e);
            }
        }
        return Maps.newHashMap();
    }

    /**
     * 经纬度随机偏移, 随机偏移0~10的米数，方位随机偏移0～360，获取偏移后的经纬度
     *{@see <a href="https://zengmiaogen.blog.csdn.net/article/details/68490497">}
     *
     * @param longitude 经度
     * @param latitude 维度
     * @return
     */
    public static Map<String, Object> randomLongAndLat(double longitude, double latitude) {
        /*
         * 大地坐标系资料WGS-84 长半径6378137 短半径6356752.3142 扁率1/298.2572236
         */
        /* 长半径 6378137 */
        double LONG_R = 6378137;
        /* 短半径 6356752.3142 */
        double SHORT_R = 6356752.3142;
        /* 扁率 1/298.2572236 */
        double SPAN_RATIO = 1 / 298.2572236;
        /*随机偏移 方位角 0～360*/
        double brng = Math.random() * DEVIATE_ANGLE;
        /*随机偏移 距离 0～meter*/
        double dis = Math.random() * DEVIATE_METER;

        double alpha1 = rad(brng);
        double sinAlpha1 = Math.sin(alpha1);
        double cosAlpha1 = Math.cos(alpha1);

        double tanU1 = (1 - SPAN_RATIO) * Math.tan(rad(latitude));
        double cosU1 = 1 / Math.sqrt((1 + tanU1 * tanU1));
        double sinU1 = tanU1 * cosU1;
        double sigma1 = Math.atan2(tanU1, cosAlpha1);
        double sinAlpha = cosU1 * sinAlpha1;
        double cosSqAlpha = 1 - sinAlpha * sinAlpha;

        double uSq = cosSqAlpha * (LONG_R * LONG_R - SHORT_R * SHORT_R) / (SHORT_R * SHORT_R);
        double A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
        double B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));

        double cos2SigmaM=0;
        double sinSigma=0;
        double cosSigma=0;
        double sigma = dis / (SHORT_R * A), sigmaP = 2 * Math.PI;
        while (Math.abs(sigma - sigmaP) > 1e-12) {
            cos2SigmaM = Math.cos(2 * sigma1 + sigma);
            sinSigma = Math.sin(sigma);
            cosSigma = Math.cos(sigma);
            double deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)
                    - B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
            sigmaP = sigma;
            sigma = dis / (SHORT_R * A) + deltaSigma;
        }

        double tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
        double lat2 = Math.atan2(sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
                (1 - SPAN_RATIO) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp));
        double lambda = Math.atan2(sinSigma * sinAlpha1, cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1);
        double C = SPAN_RATIO / 16 * cosSqAlpha * (4 + SPAN_RATIO * (4 - 3 * cosSqAlpha));
        double L = lambda - (1 - C) * SPAN_RATIO * sinAlpha
                * (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));



        double lon = longitude+deg(L);
        double lat = deg(lat2);
        logger.info("偏移的角度为{}, 距离为{}, 新的经纬度为 {}", brng, dis, lon + "," + lat);

        return ImmutableMap.of("longitude", String.valueOf(lon).substring(0, 11), "latitude", String.valueOf(lat).substring(0, 10));
    }

    /**
     * 度换成弧度
     *
     * @param d 度
     * @return 弧度
     */
    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 弧度换成度
     *
     * @param x 弧度
     * @return 度
     */
    private static double deg(double x) {
        return x * 180 / Math.PI;
    }
}
