package com.wosai.upay.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class StoreAddressUtil {

    public static final Logger logger = LoggerFactory.getLogger(StoreAddressUtil.class);

    public static Set<String> storeAddressSet = new HashSet<>();

    static {
        String path = StoreAddressUtil.class.getClassLoader().getResource("store_address.txt").getPath();
        Set<String> set = new HashSet<>();
        try {
            try (BufferedReader reader = new BufferedReader(new FileReader(path))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    set.add(line);
                }
            } catch (IOException e) {
                logger.error("io异常", e);
            }
            storeAddressSet = set;
        } catch (Exception ex) {
            logger.error("解析失败", ex);
        }
    }

    /**
     * 加载门店地址信息数据
     */
    public static Set<String> loadStoreAddress() {
        return storeAddressSet;
    }


}
