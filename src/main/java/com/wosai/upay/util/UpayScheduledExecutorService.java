package com.wosai.upay.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by jianfree on 28/9/16.
 */
public class UpayScheduledExecutorService {
    private static final Logger logger = LoggerFactory.getLogger(UpayScheduledExecutorService.class);
    private ScheduledExecutorService delegate;
    private long largeDelayTime;
    private String executorName;


    public UpayScheduledExecutorService(String executorName, ScheduledExecutorService delegate, long largeDelayTime){
        this.delegate = delegate;
        this.largeDelayTime = largeDelayTime;
        this.executorName = executorName;
    }

    public ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit, Integer provider){
        long delayTime = unit.toMillis((delay < 0) ? 0 : delay);
        long approximateExecuteTime = System.currentTimeMillis() + delayTime;
        Runnable runnable = new TimedRunnable(command instanceof TimedRunnable ?((TimedRunnable) command).getOriginal():command, approximateExecuteTime, provider);
        return delegate.schedule(runnable, delay, unit);
    }

    public Future<?> submit(Runnable task, Integer provider) {
        Runnable runnable = new TimedRunnable(task instanceof TimedRunnable ? ((TimedRunnable) task).getOriginal() : task, System.currentTimeMillis(), provider);
        return delegate.submit(runnable);
    }

    class TimedRunnable implements Runnable{
        private Runnable original;
        private long approximateExecuteTime;
        private Integer provider;

        public TimedRunnable(Runnable runnable, long approximateExecuteTime, Integer provider){
            this.original = runnable;
            this.approximateExecuteTime = approximateExecuteTime;
            this.provider = provider;
        }
        @Override
        public void run() {
            long now = System.currentTimeMillis();
            long delay = now - approximateExecuteTime;
            if(delay > largeDelayTime){
                logger.warn("{}:{} execute delay is too long, should be execute at {}, but execute at {} , delay {} ms, activeCount = {}, queueSize = {}, corePoolSize = {}", 
                		executorName, provider, approximateExecuteTime, now, delay, ((ThreadPoolExecutor) delegate).getActiveCount(), ((ThreadPoolExecutor) delegate).getQueue().size(), 
                		((ThreadPoolExecutor) delegate).getCorePoolSize());
            }
            original.run();
           
        }

        public Runnable getOriginal() {
            return original;
        }
    }

    public ScheduledExecutorService getDelegate() {
        return delegate;
    }
}
