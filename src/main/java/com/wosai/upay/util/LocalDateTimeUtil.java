package com.wosai.upay.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.TemporalField;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description LocalDateTime工具类
 * @date 2024/5/23
 */
@Slf4j
public class LocalDateTimeUtil {
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYYMMDDHHmm = "yyyyMMddHHmm";
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyyMMddHHmmssSSS";

    public static final int START_DAY_OF_WEEK = 1;
    public static final int END_DAY_OF_WEEK = 7;
    public static final int START_DAY_OF_MONTH = 1;
    public static final int END_DAY_OF_MONTH = 31;

    /**
     * 半小时对应的分钟数
     */
    public static final int HALF_HOUR_OF_MINUTES = 30;

    // 周一
    public static final int MONDAY = 1;
    // 周日
    public static final int SUNDAY = 7;

    /**
     * 一秒钟的毫秒数
     */
    private static final long NS = 1000;
    /**
     * 一天的秒数
     */
    public static final long SECONDS_OF_DAY = 24 * 60 * 60;

    /**
     * 时间戳转LocalDateTime
     *
     * @param epochSecond
     * @return
     */
    public static LocalDateTime valueOf(long epochSecond) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(epochSecond), ZoneId.systemDefault());
    }

    /**
     * 时间戳转LocalDateTime
     *
     * @param epochMilliSecond 毫秒
     * @return
     */
    public static LocalDateTime valueOfEpochMs(long epochMilliSecond) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilliSecond), ZoneId.systemDefault());
    }

    /**
     * 获取时间戳（单位：秒）
     *
     * @return
     */
    public static long getEpochSecond() {
        return toEpochSecond(LocalDateTime.now());
    }

    /**
     * LocalDateTime转时间戳（单位：秒）
     *
     * @return
     */
    public static long toEpochSecond(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0L;
        }
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000;
    }

    /**
     * LocalDateTime转时间戳（单位：毫秒）
     *
     * @return
     */
    public static long toEpochMs(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0L;
        }
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * date转为LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime valueOf(Date date) {
        if (null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime转为Date
     *
     * @param localDateTime
     * @return
     */
    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 解析日期字符串，默认yyyy-MM-dd HH:mm:ss格式
     *
     * @param dateStr
     * @return
     */
    public static LocalDateTime parse(String dateStr) {
        return parse(dateStr, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 解析日期字符串，默认yyyy-MM-dd HH:mm:ss格式
     *
     * @param dateStr
     * @return
     */
    public static LocalDateTime parse(String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(format));
        } catch (Exception e) {
            log.error("解析时间出错, dateStr={}, format={}, error={}", dateStr, format, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取当前时间的格式化字符串
     *
     * @return 格式化好的时间
     */
    public static String getFormatDateTime() {
        return getFormatDateTime(LocalDateTime.now(), YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取当前时间的格式化字符串
     *
     * @return 格式化好的时间
     */
    public static String getFormatDateTime(String format) {
        return getFormatDateTime(LocalDateTime.now(), format);
    }

    /**
     * 获取指定时间的格式化字符串
     *
     * @param localDateTime 日期
     * @return 格式化好的时间
     */
    public static String getFormatDateTime(LocalDateTime localDateTime) {
        return getFormatDateTime(localDateTime, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取指定时间指定格式的时间字符串
     *
     * @param localDateTime 日期
     * @param format        日期格式
     * @return 格式化好的时间
     */
    public static String getFormatDateTime(LocalDateTime localDateTime, String format) {
        if (null == localDateTime) {
            return "";
        }
        return localDateTime.format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * 获取当天开始的时间
     *
     * @return
     */
    public static LocalDateTime getStartOfDay() {
        return LocalDateTime.now().with(LocalTime.MIN).withNano(0);
    }

    /**
     * 获取明天开始的时间
     *
     * @return
     */
    public static LocalDateTime getTomorrowStartOfDay() {
        return LocalDateTime.now().plusDays(1).with(LocalTime.MIN).withNano(0);
    }

    /**
     * 获取指定某天开始的时间
     *
     * @return
     */
    public static LocalDateTime getStartOfDay(LocalDateTime localDateTime) {
        return localDateTime.with(LocalTime.MIN).withNano(0);
    }

    /**
     * 获取昨天的开始时间
     *
     * @return
     */
    public static LocalDateTime getStartOfYesterday() {
        return getStartOfDay(LocalDateTime.now().minusDays(1));
    }

    /**
     * 获取当天结束的时间
     *
     * @return
     */
    public static LocalDateTime getEndOfDay() {
        return getEndOfDay(LocalDateTime.now());
    }

    /**
     * 获取指定某天结束的时间
     *
     * @return
     */
    public static LocalDateTime getEndOfDay(LocalDateTime localDateTime) {
        return localDateTime.with(LocalTime.MAX).withNano(0);
    }

    /**
     * 时间是否是今天
     *
     * @return
     */
    public static boolean isToday(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return false;
        }
        return localDateTime.with(LocalTime.MIN).equals(LocalDateTime.now().with(LocalTime.MIN));
    }

    /**
     * 是否是同一天
     *
     * @return
     */
    public static boolean isSameDay(LocalDateTime startDate, LocalDateTime endDate) {
        return startDate.with(LocalTime.MIN).equals(endDate.with(LocalTime.MIN));
    }

    /**
     * 是否在今日之前
     *
     * @param localDateTime
     * @return
     */
    public static boolean isBeforeToday(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return false;
        }
        return localDateTime.toLocalDate().isBefore(LocalDate.now());
    }

    /**
     * 给的时间是否在当前时间之后
     *
     * @param localDateTime
     * @return
     */
    public static boolean isAfterNow(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return false;
        }
        return localDateTime.isAfter(LocalDateTime.now());
    }

    /**
     * 获取本周开始的时间
     *
     * @return
     */
    public static LocalDateTime getStartOfWeek() {
        // 设置结束时间为LocalTime.MIN
        return LocalDateTime.now().with(DayOfWeek.MONDAY).with(LocalTime.MIN).withNano(0);
    }

    /**
     * 获取本周结束的时间
     *
     * @return
     */
    public static LocalDateTime getEndOfWeek() {
        // 使用WeekFields设置一周的开始为周一
        TemporalField fieldISO = WeekFields.of(DayOfWeek.MONDAY, 1).dayOfWeek();
        // 设置结束时间为LocalTime.MAX
        return LocalDateTime.now().with(fieldISO, 7).with(LocalTime.MAX).withNano(0);
    }

    /**
     * 获取当月的结束时间 23:59:59
     *
     * @return
     */
    public static LocalDateTime getEndOfMonth() {
        return getEndOfMonth(LocalDateTime.now());
    }

    /**
     * 获取指定时间所在月份的结束时间 23:59:59
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getEndOfMonth(LocalDateTime localDateTime) {
        return localDateTime.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX).withNano(0);
    }

    /**
     * 获取当月一共多少天
     *
     * @return
     */
    public static int getDaysOfMonth() {
        return getDaysOfMonth(LocalDate.now());
    }

    /**
     * 获取指定日期所在的当月一共多少天
     *
     * @return
     */
    public static int getDaysOfMonth(LocalDate localDate) {
        return localDate.lengthOfMonth();
    }

    /**
     * 获取当天剩余的秒数
     *
     * @return
     */
    public static long getLeftSecondsOfDay() {
        return SECONDS_OF_DAY - DateUtils.getFragmentInSeconds(Calendar.getInstance(), Calendar.DATE);
    }

    /**
     * 获取当天已过去的秒数
     *
     * @return
     */
    public static long getPassedSecondsOfDay() {
        return DateUtils.getFragmentInSeconds(Calendar.getInstance(), Calendar.DATE);
    }

    /**
     * 距离指定日期，还剩下多少时间，单位是秒
     *
     * @param localDateTime
     * @return
     */
    public static long getLeftSeconds(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0;
        }
        long leftSeconds = ChronoUnit.SECONDS.between(LocalDateTime.now(), localDateTime);
        if (leftSeconds < 0) {
            return 0;
        }
        return leftSeconds;
    }

    /**
     * 获取本周的剩余时间（单位：秒）
     *
     * @return
     */
    public static long getLeftSecondsOfWeek() {
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        // 设置星期一为每星期的第一天
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        // 设置时间为周日
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return (calendar.getTimeInMillis() + getLeftSecondsOfDay() * NS - System.currentTimeMillis()) / NS;
    }
}
