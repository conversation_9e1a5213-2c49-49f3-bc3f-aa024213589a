package com.wosai.upay.util;


import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2020/9/8 Time: 4:12 下午
 */
public class LanguageCaseHolder {
    private static ThreadLocal<Integer> LANGUAGE_CASE_THREAD_LOCAL = new ThreadLocal<>();


    public static void setLanguageCase(Map<String, Object> tradeParams) {
        int languageCase = MapUtil.getIntValue(tradeParams, TransactionParam.LANGUAGE_CASE, TransactionParam.LANGUAGE_CASE_CHINESE);
        if (TransactionParam.LANGUAGE_CASE_CHINESE != languageCase) {
            LANGUAGE_CASE_THREAD_LOCAL.set(languageCase);
        }
    }

    public static int getLanguageCase() {
        Integer languageCase = LANGUAGE_CASE_THREAD_LOCAL.get();
        return Objects.isNull(languageCase) ? TransactionParam.LANGUAGE_CASE_CHINESE : languageCase;
    }

    public static void remove() {
        LANGUAGE_CASE_THREAD_LOCAL.remove();
    }

}
