package com.wosai.upay.util;

import java.util.Random;

/**
 *
 * 灰度发布帮助类
 *
 */
public class GrayReleaseUtil {
    private static Random random = new Random();

    /**
     * 按照比例进行灰度
     *
     * @param headPct
     * @return
     */
    public static boolean shouldEnable(int headPct) {
        return shouldEnable(headPct, null);
    }

    /**
     * 按照业务属性进行灰度
     *
     * @param headPct
     * @param rowKey
     * @return
     */
    public static boolean shouldEnable(int headPct, String rowKey) {
        if (headPct >= 100){
            return true;
        } else if (headPct <= 0){
            return false;
        }
        int randInt;
        if (rowKey != null) {
            randInt = Math.abs(rowKey.hashCode() % 100);
        } else {
            randInt = random.nextInt(100);
        }

        if (randInt < headPct) {
            return true;
        }else{
            return false;
        }
    }
}
