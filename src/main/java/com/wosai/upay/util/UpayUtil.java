package com.wosai.upay.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.bestpay.BestpayConstants;
import com.wosai.mpay.api.xzx.constants.XZXRequestFieldsConstants;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.wallet.constant.ProviderWalletAccountTypeEnum;
import com.wosai.upay.workflow.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.wosai.upay.service.UpayService.LATITUDE;
import static com.wosai.upay.service.UpayService.LONGITUDE;

/**
 * Created by jianfree on 25/5/17.
 */
public class UpayUtil {

    private static final Logger logger = LoggerFactory.getLogger(UpayUtil.class);
    private static final Pattern ALIPAY_PATTERN = Pattern.compile("(2[5-9]|30)\\d{14,22}"); //25-30开头, 总共16-24位
    private static final Pattern WEIXIN_PATTERN = Pattern.compile("1[0-9]\\d{14,18}");
    private static final Pattern WEIXIN_HK_PATTERN = Pattern.compile("160\\d{15}");
    private static final Pattern BAIFUBAO_PATTERN = Pattern.compile("31\\d{14,20}");
    private static final Pattern JD_PATTERN = Pattern.compile("18\\d{16,20}");
    private static final Pattern QQ_PATTERN = Pattern.compile("9[0-9]\\d{16}");//手Q付款码支付展示18位的授权码，并且前两位固定。商户的POS设备可以根据前两位来 识别该选择何种支付方式。目前，手Q付款码支付使用  90~99作为授权码的前缀。
    private static final Pattern LKLWALLET_PATTERN = Pattern.compile("(1|3)6\\d{16}");//拉卡拉钱包,条码规则: 16、36打头， 18位数字
    private static final Pattern LKL_UNIONPAY_PATTERN = Pattern.compile("62\\d{16,20}");//云闪付银联二维码, 官方条码规则: 19位十进制数组,62开头，第3位区分借贷记计价，其中1-4位借记，5-8为贷记, 为了兼容以后的升级,我们定义规则为：62开头，18-22位十进制数字
    private static final Pattern BESTPAY_PATTERN = Pattern.compile("51\\d{16}");//翼支付,条码规则:51开头，共18位
    private static final Pattern CMCCPAY_PATTERN = Pattern.compile("81\\d{16}");//和支付,条码规则:81开头，共18位
    private static final Pattern GIFTCARD_PATTERN = Pattern.compile("^(?!43781)4378\\d{16}"); //礼品卡,条码规则:4378开头非43781,共20位
    private static final Pattern WELFARE_CARD_PATTERN = Pattern.compile("43781\\d{15}"); //福利卡,条码规则:43781开头,共20位
    private static final Pattern SODEXO_PATTERN = Pattern.compile("77\\w{16}");//索迪斯,条码规则:77开头，共18位
    private static Pattern DCEP_PATTERN = Pattern.compile("01\\d{17}");//数字钱包,条码规则:01开头,共19位
    private static final Pattern PREPAID_CARD_PATTERN = Pattern.compile("4678\\d{16}");//储值卡,条码规则:4678开头,共20位
    private static final Pattern FOXCONN_PATTERN = Pattern.compile("88\\d{16}");//富士康钱包,条码规则:88开头,共18位
    private static final Pattern GRABPAY_PATTERN = Pattern.compile("65\\d{16}");//grabpay钱包,条码规则:65开头,共18位

    private static final Pattern CCB_APP_PATTERN = Pattern.compile("62\\d0105\\d{12}"); //建行生活APP 条码规则: 62*0105* 62+1位数字+0105+其他数字 共19位

    private static final Pattern CMB_APP_PATTERN = Pattern.compile("(62355|62555|62354|62554)\\d{14}"); //招行生活APP 条码规则: 招行付款码以62355、62555、62354、62554开头 共19位
    private static final Pattern HOPE_EDU_PATTERN = Pattern.compile("66\\d{16,30}"); // 院校通 一码通 66开头18到32位数字
    private static final Pattern CCB_GIFT_CARD_PATTERN = Pattern.compile("^CT0001.*"); //建行福利卡 CT0001 开头

    private static Pattern MPAY_PATTERN = Pattern.compile("88\\d{18}");//澳门MPay,条码规则:88开头,共20位

    private static final String PREFIX_ORDER_SN = "o";
    private static final String PREFIX_TRANSACTION_SN = "t";

    private static final Map<String, String> KEYS_PROVIDER_ERROR_INFO = new ConcurrentHashMap<String, String>();
    private static final Map<String, String> KEYS_BIZ_ERROR_CODE = new ConcurrentHashMap<String, String>();
    private static final Map<String, Map<String, String>> SPAN_NAMES = new ConcurrentHashMap<String, Map<String,String>>();
    public static final char COMMON_SWTICH_OPEN = Character.forDigit(TransactionParam.STATUS_OPENED, 10);
    public static final char COMMON_SWTICH_NOT_CONFIGURED = Character.forDigit(TransactionParam.STATUS_NOT_CONFIGURED, 10);

    public static final String IGNORE_POI_VALUE = "4.9E-324"; // 此值为 api sdk 中经纬度的默认值，如果为此值，当做终端没有上送，不存储到流水里面

    public static final String IGNORE_ZERO_POI_VALUE = "0.0";// 此值为 api sdk 中经纬度的默认值，如果为此值，当做终端没有上送，不存储到流水里面

    // 本机ip，用于交易时上送
    private static String localHostIp = "";

    static{
    	InetAddress address;
		try {
			address = InetAddress.getLocalHost();
			localHostIp = address.getHostAddress().toString();
		} catch (UnknownHostException e) {
			localHostIp = "127.0.0.1";
		}
    }
    
    private static final Map<Integer,String> PAYWAY_MAP_V2 = CollectionUtil.hashMap(Order.PAYWAY_WEIXIN, "WEIXIN",
				Order.PAYWAY_ALIPAY, "ALIPAY",
				Order.PAYWAY_JD, "JD",
				Order.PAYWAY_BAIFUBAO, "BAIFUBAO",
				Order.PAYWAY_APPLEPAY, "NFC",
				Order.PAYWAY_QQWALLET, "QQWALLET",
				Order.PAYWAY_ALIPAY2, "ALIPAY_OPEN",
				Order.PAYWAY_LKL_UNIONPAY, "ALIPAY_OPEN",
				Order.PAYWAY_LKL_UNIONPAY, "UNIONPAY",
				Order.PAYWAY_BESTPAY, "BESTPAY",
				Order.PAYWAY_WEIXIN_HK, "WEIXIN",
				Order.PAYWAY_DCEP, "DCEP",
				Order.PAYWAY_PREPAID_CARD, "PREPAID_CARD",
				Order.PAYWAY_FOXCONN, "FOXCONN",
                Order.PAYWAY_GRABPAY, "GRABPAY"
    		
    );

    /**
     * 查看一笔交易是否是正式, 当交易参数没有设置active字段的时候
     * @param workflowManager
     * @param transaction
     * @return
     */
    public static boolean isFormal(WorkflowManager workflowManager, Map<String, Object> transaction){
        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        if(provider != null){
            Map<String, Object> tradeParams = provider.getTradeParams(transaction);
            return !BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, true);
        }else{
            return false;
        }

    }
    
    /**
     * 查看是否正式, 默认是二清
     * @param tradeParams
     * @return
     */
    public static  boolean isFormalByTradeParams(Map<String, Object> tradeParams) {
        return !BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, true);
    }

    /**
     * 查看是否正式, 默认是二清
     * @param config
     * @return
     */
    public static boolean isFormalByConfig(Map<String, Object> config) {
        for(String key: config.keySet()){
            Object value = config.get(key);
            if(value instanceof  Map){
                if(((Map) value).get(TransactionParam.FEE_RATE) != null){
                    return  !BeanUtil.getPropBoolean(value, TransactionParam.LIQUIDATION_NEXT_DAY, true);
                }
            }
        }
        return false;
    }

    /**
     * 查看是否正式, 默认是二清
     * @param existsTransaction
     * @return
     */
    public static  boolean isFormal(Map<String, Object> existsTransaction) {
        Map<String,Object> configSnapshot = (Map<String, Object>) existsTransaction.get(Transaction.CONFIG_SNAPSHOT);
        for(String key: configSnapshot.keySet()){
            Object value = configSnapshot.get(key);
            if(value instanceof  Map){
                if(BeanUtil.getPropBoolean(value, TransactionParam.ACTIVE, false)){
                    return  !BeanUtil.getPropBoolean(value, TransactionParam.LIQUIDATION_NEXT_DAY, true);
                }
            }
        }
        return false;
    }

    /**
     * 判断此笔交易在支付通道是不是免充值交易
     * @param existsTransaction
     * @return
     */
    public static  boolean isChannelNotTopUp(Map<String, Object> existsTransaction){
        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(existsTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(channelPayments != null){
            for(Map<String,Object> channelPayment: channelPayments){
                String type = BeanUtil.getPropString(channelPayment, Payment.TYPE);
                if(Payment.TYPE_HONGBAO_CHANNEL_MCH.equals(type) || Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                    return true;
                }
            }
        }
        return false;

    }

    /**
     * 判断此笔交易是否是信用卡交易
     * 
     * @param existsTransaction
     * @return
     */
    public static  boolean isCreditPay(Map<String, Object> existsTransaction){
        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(existsTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if(channelPayments != null){
            for(Map<String,Object> channelPayment: channelPayments){
                String type = BeanUtil.getPropString(channelPayment, Payment.TYPE);
                if(Payment.TYPE_BANKCARD_CREDIT.equals(type) 
                        || Payment.TYPE_BANKCARD_SEMI_CREDIT.equals(type)){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 返回银行卡外卡币种转换类型
     * 
     * @param existsTransaction
     * @return dcc or edc or null
     */
    public static String getWildCardType(Map<String, Object> existsTransaction){
        if (MapUtil.getIntValue(existsTransaction, Transaction.PAYWAY) == Payway.BANKCARD.getCode()) {
            return MapUtil.getString(MapUtil.getMap(existsTransaction, Transaction.EXTRA_OUT_FIELDS), Transaction.WILD_CARD_TYPE);
        }
        return null;
    }

    /**
     * 根据支付机构的商品详情格式转换商品详情字段，放入到扩展字段里面，然后透传给支付机构， 用于单品优惠以及支付宝智慧门店 等
     * @param goodsDetails
     * @param extended
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String,Object> getMergedExtendedWithGoodsDetails(List<Map<String,Object>> goodsDetails, Map<String,Object> extended, int payway, Map<String, Object> config, int type){
        boolean isChinaums = (null != config && config.containsKey(TransactionParam.CHINAUMS_TRADE_PARAMS));
        boolean isSyb = (null != config && config.containsKey(TransactionParam.TL_SYB_TRADE_PARAMS));
        boolean isLakalaUnionPayOpen = (null != config && config.containsKey(TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS));
        // 是否新中新支付
        boolean isXzx = (null != config && config.containsKey(TransactionParam.XZX_TRADE_PARAMS));

        if(payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_WEIXIN
                || (payway == Order.PAYWAY_UNIONPAY && (isChinaums || isSyb) )
                || payway == Order.PAYWAY_BESTPAY
                || (payway == Order.PAYWAY_BANKACCOUNT)){
            if(goodsDetails != null && goodsDetails.size() > 0){
                List<Map<String,Object>> newGoodsDetails = new ArrayList<>();
                String signType = BestpayConstants.KEY_MD5;
                for(Map<String,Object> goodsDetail: goodsDetails){
                    if(BeanUtil.getPropInt(goodsDetail, UpayConstant.PROMOTION_TYPE) != UpayConstant.PROMOTION_TYPE_CHANNEL){
                        continue;
                    }
                    if (isChinaums) {
                        newGoodsDetails.add(
                                CollectionUtil.hashMap(
                                        UpayConstant.CHINAUMS_GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                        UpayConstant.CHINAUMS_GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                        UpayConstant.QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY),
                                        UpayConstant.PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE)
                                )
                        );
                    } else if (isXzx) {
                        newGoodsDetails.add(
                                CollectionUtil.hashMap(
                                        XZXRequestFieldsConstants.GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                        XZXRequestFieldsConstants.GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                        XZXRequestFieldsConstants.QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY),
                                        XZXRequestFieldsConstants.PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE)
                                )
                        );
                    } else if (payway == Order.PAYWAY_UNIONPAY) {
                        if (isSyb || isLakalaUnionPayOpen) {
                            newGoodsDetails.add(
                                    CollectionUtil.hashMap(
                                            UpayConstant.UNION_PAY_GOODS_INFO_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                            UpayConstant.UNION_PAY_GOODS_INFO_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                            UpayConstant.UNION_PAY_GOODS_INFO_QUANTITY, BeanUtil.getPropLong(goodsDetail, UpayConstant.QUANTITY),
                                            UpayConstant.UNION_PAY_GOODS_INFO_PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE))
                            );
                        }
                    }else if(payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_ALIPAY){
                        Map<String, Object> tmpGoodsDetail = CollectionUtil.hashMap(
                                UpayConstant.GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                UpayConstant.GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                UpayConstant.QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY),
                                UpayConstant.PRICE, StringUtils.cents2yuan(BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE))
                        );
                        if(type == Transaction.TYPE_REFUND) {
                            if(goodsDetail.containsKey(UpayConstant.REFUND_AMOUNT)) {
                                tmpGoodsDetail.put(UpayConstant.REFUND_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(goodsDetail, UpayConstant.REFUND_AMOUNT)));
                                newGoodsDetails.add(tmpGoodsDetail);
                            }
                        }else {
                            newGoodsDetails.add(tmpGoodsDetail);
                        }
                    }else if(payway == Order.PAYWAY_WEIXIN){

                        boolean isV3Request = extended != null && Objects.equals(TransactionParam.WEIXIN_VERSION_V3, MapUtil.getString(extended, Transaction.SQB_WX_VERSION));
                        if (isV3Request) {
                            Map<String, Object> tmpGoodsDetail = CollectionUtil.hashMap(
                                    UpayConstant.VX_V3_MERCHANT_GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                    UpayConstant.VX_V3_GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                    UpayConstant.VX_V3_QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY),
                                    UpayConstant.VX_V3_UNIT_PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE)
                            );
                            if (type == Transaction.TYPE_REFUND) {
                                // 微信退款上送信息
                                if (goodsDetail.containsKey(UpayConstant.REFUND_AMOUNT)) {
                                    tmpGoodsDetail.put(UpayConstant.VX_V3_REFUND_AMOUNT, BeanUtil.getPropLong(goodsDetail, UpayConstant.REFUND_AMOUNT));
                                    tmpGoodsDetail.put(UpayConstant.VX_V3_REFUND_QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY));
                                    tmpGoodsDetail.remove(UpayConstant.QUANTITY);
                                    newGoodsDetails.add(tmpGoodsDetail);
                                }
                            } else {
                                newGoodsDetails.add(tmpGoodsDetail);
                            }
                        } else{
                            Map<String, Object> tmpGoodsDetail = CollectionUtil.hashMap(
                                    UpayConstant.GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                    UpayConstant.GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                    UpayConstant.QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY),
                                    UpayConstant.PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE)
                            );
                            if (type == Transaction.TYPE_REFUND) {
                                // 微信退款上送信息
                                if (goodsDetail.containsKey(UpayConstant.REFUND_AMOUNT)) {
                                    tmpGoodsDetail.put(UpayConstant.REFUND_AMOUNT, BeanUtil.getPropLong(goodsDetail, UpayConstant.REFUND_AMOUNT));
                                    tmpGoodsDetail.put(UpayConstant.REFUND_QUANTITY, BeanUtil.getPropInt(goodsDetail, UpayConstant.QUANTITY));
                                    tmpGoodsDetail.remove(UpayConstant.QUANTITY);
                                    newGoodsDetails.add(tmpGoodsDetail);
                                }
                            } else {
                                newGoodsDetails.add(tmpGoodsDetail);
                            }
                        }
                    }else  if (payway == Order.PAYWAY_BESTPAY) {
                        Map tradeParams = MapUtils.getMap(config, TransactionParam.BESTPAY_TRADE_PARAMS);
                        signType = BeanUtil.getPropString(tradeParams, TransactionParam.SIGN_TYPE);
                        if (BestpayConstants.KEY_RSA.equals(signType)) {
                            newGoodsDetails.add(
                                    CollectionUtil.hashMap(
                                            UpayConstant.BESTPAY_V2_GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                            UpayConstant.BESTPAY_V2_GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                            UpayConstant.QUANTITY, BeanUtil.getPropLong(goodsDetail, UpayConstant.QUANTITY),
                                            UpayConstant.BESTPAY_UNIT_PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE)
                                    ));
                        } else {
                            newGoodsDetails.add(
                                    CollectionUtil.hashMap(
                                            UpayConstant.BESTPAY_GOODS_ID, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_ID),
                                            UpayConstant.BESTPAY_GOODS_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                            UpayConstant.QUANTITY, BeanUtil.getPropLong(goodsDetail, UpayConstant.QUANTITY),
                                            UpayConstant.PRICE, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE),
                                            UpayConstant.BESTPAY_GOODS_CATEGORY, BeanUtil.getPropString(goodsDetail, UpayConstant.BESTPAY_GOODS_CATEGORY),
                                            UpayConstant.BODY, BeanUtil.getPropString(goodsDetail, UpayConstant.BODY)
                                    ));
                        }
                    } else if (payway == Order.PAYWAY_BANKACCOUNT) {
                            newGoodsDetails.add(
                                    CollectionUtil.hashMap(
                                            UpayConstant.ENTPAY_GOOD_NAME, BeanUtil.getPropString(goodsDetail, UpayConstant.GOODS_NAME),
                                            UpayConstant.ENTPAY_GOOD_NUMBER, BeanUtil.getPropLong(goodsDetail, UpayConstant.QUANTITY),
                                            UpayConstant.ENTPAY_GOOD_AMOUNT, BeanUtil.getPropLong(goodsDetail, UpayConstant.PRICE)
                                    ));
                    }
                }

                if(extended == null){
                    extended = new HashMap<>();
                }
                if(newGoodsDetails.size() > 0){
                    if(payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_ALIPAY){
                        extended.put(UpayConstant.GOODS_DETAIL, newGoodsDetails);
                        if(type == Transaction.TYPE_REFUND) {
                            extended.put(UpayConstant.REFUND_GOODS_DETAIL, newGoodsDetails);
                            extended.remove(UpayConstant.GOODS_DETAIL);
                        }
                    }else if(payway == Order.PAYWAY_WEIXIN){
                        if (isXzx) {
                            extended.put(XZXRequestFieldsConstants.GOODS_DETAIL, newGoodsDetails);
                        } else {
                            extendedSetWxDetail(newGoodsDetails, extended, type);
                        }
                    } else if (payway == Order.PAYWAY_UNIONPAY) {
                        if(isSyb){
                            extended.put(UpayConstant.DETAIL, CollectionUtil.hashMap(
                                    UpayConstant.SYB_UNION_PAY_GOODS_INFO, newGoodsDetails
                            ));
                        }  else {
                            extended.put(UpayConstant.GOODS, newGoodsDetails);
                        }
                    } else if (payway == Order.PAYWAY_BESTPAY) {
                        extended.put(UpayConstant.GOODS_DETAIL, newGoodsDetails);
                    } else if (payway == Order.PAYWAY_BANKACCOUNT) {
                        extended.put(UpayConstant.GOODS, newGoodsDetails);
                    }
                }
            }
        }
        return extended;
    }

    @SuppressWarnings("unchecked")
    private static final Map<String, String> paywayPaywayNames = CollectionUtil.hashMap(
            Order.PAYWAY_ALIPAY + "", "支付宝",
            Order.PAYWAY_ALIPAY2 + "", "支付宝",
            Order.PAYWAY_WEIXIN + "", "微信",
            Order.PAYWAY_BAIFUBAO + "", "百度钱包",
            Order.PAYWAY_JD + "", "京东支付",
            Order.PAYWAY_QQWALLET + "", "QQ钱包",
            Order.PAYWAY_APPLEPAY + "", "NFC支付",
            Order.PAYWAY_LKLWALLET + "", "拉卡拉钱包",
            Order.PAYWAY_LKL_UNIONPAY + "", "银联云闪付",
            Order.PAYWAY_BESTPAY + "", "翼支付",
            Order.PAYWAY_WEIXIN_HK + "", "微信",
            Order.PAYWAY_CMCC + "", "和包支付",
            Order.PAYWAY_ALIPAY_INTL + "", "支付宝",
            Order.PAYWAY_GIFT_CARD + "", "礼品卡",
            Order.PAYWAY_BANKCARD + "", "银行卡",
            Order.PAYWAY_BANKACCOUNT + "", "银行转账",
            Order.PAYWAY_SODEXO + "", "索迪斯",
            Order.PAYWAY_DCEP + "", "数字人民币",
            Order.PAYWAY_PREPAID_CARD + "", "储值卡",
            Order.PAYWAY_HOPE_EDU + "", "院校通-一码通",
            Order.PAYWAY_FOXCONN + "", "富士康钱包",
            Order.PAYWAY_MACAU_PASS + "", "澳门通",
            Order.PAYWAY_GRABPAY + "", "grabpay钱包"
    );

    public static String getPaywayName(int payway){
        String paywayName = paywayPaywayNames.getOrDefault(payway + "", Payway.getNameByCode(payway));
        return paywayName == null ? payway + "" : paywayName;
    }
    
    @SuppressWarnings("unchecked")
    private static final Map<String, String> subPaywayPaywayNames = CollectionUtil.hashMap(
            Order.SUB_PAYWAY_BARCODE + "", "商家扫码",
            Order.SUB_PAYWAY_QRCODE + "", "二维码",
            Order.SUB_PAYWAY_WAP + "", "顾客扫码",
            Order.SUB_PAYWAY_MINI + "", "小程序",
            Order.SUB_PAYWAY_APP + "", "APP支付",
            Order.SUB_PAYWAY_H5 + "", "H5支付"
    );
    public static String getSubPaywayName(int subPayway){
        String paywayName = subPaywayPaywayNames.get(subPayway + "");
        return paywayName == null ? subPayway + "" : paywayName;
    }
    
    

    /**
     * 订单id生成规则为，"o" + 订单编号
     * 之前为time based uuid
     * @param orderSn
     * @return
     */
    public static String getOrderIdBySn(String orderSn){
        return StringUtils.join(PREFIX_ORDER_SN, orderSn);
    }

    /**
     * 流水id生成规则为，"t" + 订单编号
     * 之前为time based uuid
     * @param transactionTsn
     * @return
     */
    public static String getTransactionIdBySn(String transactionTsn){
        return StringUtils.join(PREFIX_TRANSACTION_SN, transactionTsn);
    }

    public static int resolvePayway(String payway, String dynamicId, String dynamicIdType) {
        if(UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_AUTH.equals(dynamicIdType)
                || UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_FT_OFFLINE.equals(dynamicIdType)
                || UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_FT.equals(dynamicIdType)
                || UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(dynamicIdType)){
            return Order.PAYWAY_ALIPAY2;
        }
        if(UpayConstant.DYNAMIC_ID_TYPE_BANKCARD.equals(dynamicIdType)){
            return Order.PAYWAY_BANKCARD;
        }
        int guess = guessPayway(dynamicId);
        if(!StringUtils.isEmpty(payway) && Integer.parseInt(payway) != guess){
            if (guess == Order.PAYWAY_ALIPAY && Integer.parseInt(payway) == Order.PAYWAY_ALIPAY2) {
                return guess;
            }
            if (guess == Order.PAYWAY_WELFARE_CARD && Integer.parseInt(payway) == Order.PAYWAY_GIFT_CARD) {
                return guess;
            }
            //招行与建行生活APP优先用解析出的为准，获取交易参数时会兼容处理
            if (guess == Order.PAYWAY_CMB_APP || guess == Order.PAYWAY_CCB_APP) {
                return guess;
            }
            throw new InvalidBarcodeException(UpayErrorScenesConstant.INVALID_BARCODE_ERROR_PAYWAY, UpayErrorScenesConstant.INVALID_BARCODE_ERROR_PAYWAY_MESSAGE);
        }else{
            return guess;
        }
    }

    private static int guessPayway(String dynamicId) {
        int payWay = Order.PAYWAY_ALIPAY;

        if(ALIPAY_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_ALIPAY;
        }else if(WEIXIN_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_WEIXIN;
        }else if(WEIXIN_HK_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_WEIXIN_HK;
        }else if(LKLWALLET_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_LKLWALLET;
        } else if (CCB_APP_PATTERN.matcher(dynamicId).matches()) {  //建行生活APP
            payWay = Order.PAYWAY_CCB_APP;
        } else if (CMB_APP_PATTERN.matcher(dynamicId).matches()) {  //招行行生活APP
            payWay = Order.PAYWAY_CMB_APP;
        } else if(LKL_UNIONPAY_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_LKL_UNIONPAY;
        }else if(BESTPAY_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_BESTPAY;
        }else if(CMCCPAY_PATTERN.matcher(dynamicId).matches()) {
            payWay = Order.PAYWAY_CMCC;
        }else if(GIFTCARD_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_GIFT_CARD;
        }else if(SODEXO_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_SODEXO;
        }else if(DCEP_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_DCEP;
        }else if(PREPAID_CARD_PATTERN.matcher(dynamicId).matches()) {
            payWay = Order.PAYWAY_PREPAID_CARD;
        }else if(FOXCONN_PATTERN.matcher(dynamicId).matches()) {
            payWay = Order.PAYWAY_FOXCONN;
        }else if (GRABPAY_PATTERN.matcher(dynamicId).matches()) {
            payWay = Order.PAYWAY_GRABPAY;
        }else if(WELFARE_CARD_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_WELFARE_CARD;
        }else if(HOPE_EDU_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_HOPE_EDU;
        }else if(CCB_GIFT_CARD_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_CCB_GIFT_CARD;
        }else if(MPAY_PATTERN.matcher(dynamicId).matches()){
            payWay = Order.PAYWAY_MACAU_PASS;
        } else{
            throw new InvalidBarcodeException(UpayErrorScenesConstant.INVALID_BARCODE_GUESS, UpayErrorScenesConstant.INVALID_BARCODE_GUESS_MESSAGE);
        }

        return payWay;
    }
    public static String convertPaywayToString(int payway){
        return PAYWAY_MAP_V2.get(payway);
    }

    public static String getLocalHostIp(){
    	return localHostIp;
    }

    public static boolean isIPv6(String ipAddress){
        try {
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            if (inetAddress instanceof Inet6Address) {
                return true;
            }
        } catch (UnknownHostException ex) {
            return false;
        }
        return false;
    }

    public static String getProviderErrorInfoKey(String op){
    	String key = KEYS_PROVIDER_ERROR_INFO.get(op);
    	if(null == key){
    		key = String.format("%s.%s", Transaction.PROVIDER_ERROR_INFO, op);
    		KEYS_PROVIDER_ERROR_INFO.put(op, key);
    	}
    	return key;
    }
    
    public static String getBizErrorCodeKey(String op){
    	String key = KEYS_BIZ_ERROR_CODE.get(op);
    	if(null == key){
    		key = String.format("%s.%s", Transaction.BIZ_ERROR_CODE, op);
    		KEYS_BIZ_ERROR_CODE.put(op, key);
    	}
    	return key;
    }
    
    public static String getSpanName(String providerName, String op){
    	Map<String,String> spanNames = SPAN_NAMES.get(op);
    	if(null == spanNames){
    		spanNames = new HashMap<String,String>();
    		SPAN_NAMES.put(op, spanNames);
    	}
    	String name = spanNames.get(providerName);
    	if(null == name){
    		name = StringUtils.join(providerName, "@", op);
    		spanNames.put(providerName, name);
    	}
    	return name;
    }
    
    public static String getOpByTransType(int transactionType){
        String op = MpayServiceProvider.OP_PAY;
        if (transactionType == Transaction.TYPE_REFUND) {
            op = MpayServiceProvider.OP_REFUND;
        } else if (transactionType == Transaction.TYPE_CANCEL) {
            op = MpayServiceProvider.OP_CANCEL;
        } else if(transactionType == Transaction.TYPE_DEPOSIT_FREEZE) {
            op = MpayServiceProvider.OP_DEPOSIT_FREEZE;
        } else if(transactionType == Transaction.TYPE_DEPOSIT_CONSUME) {
            op = MpayServiceProvider.OP_DEPOSIT_CONSUME;
        } else if(transactionType == Transaction.TYPE_DEPOSIT_CANCEL) {
            op = MpayServiceProvider.OP_DEPOSIT_CANCEL;
        }
        return op;
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> apiSuccess(Map<String, Object> bizResponse) {
        return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                CommonResponse.BIZ_RESPONSE, bizResponse);
    }
    
    @SuppressWarnings("unchecked")
    public static Map<String, Object> bizResponse(String resultCode, String errorCode, String standardName, String errorMessage, Map<String, Object> data) {
        Map<String, Object> bizResponse = CollectionUtil.hashMap(CommonResponse.RESULT_CODE, resultCode);
        if (!StringUtil.empty(errorCode)) {
            bizResponse.put(CommonResponse.ERROR_CODE, errorCode);
        }
        if(!StringUtil.empty(standardName)) {
            bizResponse.put(CommonResponse.ERROR_CODE_STANDARD, standardName);
        }
        
        if(!StringUtil.empty(errorMessage)) {
            if(!StringUtil.empty(standardName)) {
                bizResponse.put(CommonResponse.ERROR_MESSAGE, StringUtils.join(errorMessage, "[", standardName, "]"));
            }else {
                bizResponse.put(CommonResponse.ERROR_MESSAGE, errorMessage);
            }
        }
        
        if (data != null) {
            bizResponse.put(CommonResponse.DATA, data);
        }

        return bizResponse;
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> bizResponse(String resultCode, String errorCode, String errorMessage, List<Map<String, Object>> data) {
        Map<String, Object> bizResponse = CollectionUtil.hashMap(CommonResponse.RESULT_CODE, resultCode);
        if (errorCode != null) {
            bizResponse.put(CommonResponse.ERROR_CODE, errorCode);
        }
        if(errorMessage != null) {
            bizResponse.put(CommonResponse.ERROR_MESSAGE, errorMessage);
        }
        if (data != null) {
            bizResponse.put(CommonResponse.DATA, data);
        }

        return bizResponse;
    }
    
    public static UpayBizError getBizError(Map<String, Object> transaction, String op) {
        Object bizError = transaction.get(Transaction.BIZ_ERROR_CODE);
        if (bizError == null) {
            return UpayBizError.fromCode(UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode());
        }else if (bizError instanceof Map){
            Object opBizError = BeanUtil.getProperty(bizError, op);
            if(opBizError == null){
                return UpayBizError.fromCode(UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode());
            }else if(opBizError instanceof Map){
                return UpayBizError.fromMap((Map) opBizError);
            }else if(opBizError instanceof UpayBizError){
                return (UpayBizError) opBizError;
            }else{
                try{
                    return UpayBizError.fromCode(BeanUtil.getPropInt(bizError, op, UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode()));
                }catch (NumberFormatException e) {
                    return UpayBizError.fromCode(UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode());
                }
            }

        }else if (bizError instanceof Integer) {
            return UpayBizError.fromCode((Integer)bizError);
        } else {
            return UpayBizError.fromCode(Integer.valueOf(bizError+""));
        }
    }

    /**
     * 转换扩展字段为map对象
     * @param extended
     * @return
     */
    public static Map formatExtended(Object extended, ObjectMapper om){
        Map map = null;
        if(extended instanceof String){
            if("".equals(extended)){
                map = null;
            }else{
                try {
                    map = om.readValue(((String) extended).getBytes("UTF-8"), Map.class);
                } catch (IOException e) {

                }
            }
        }else if(extended instanceof Map){
            map = (Map)extended;
        }
        return map;
    }

    /**
     * 取得交易参数
     * @param configSnapshot
     * @return
     */
    public static Map getTradeParamsFromConfigSnapshot(Map<String, Object> configSnapshot) {
        if (configSnapshot == null) {
            return null;
        }
        for (String key : configSnapshot.keySet()) {
            Object value = configSnapshot.get(key);
            if (value instanceof Map) {
                if (BeanUtil.getPropBoolean(value, TransactionParam.ACTIVE, false)) {
                    return (Map) value;
                }
            }
        }
        return null;
    }

    /**
     * 退款撤单的时候，校验上送优惠是否超过原交易可退优惠
     * @param order
     * @param sqbPromotionDetails
     */
    public static void validateRefundOrCancelPromotionDetail(Map<String,Object> order, List<Map<String, Object>> sqbPromotionDetails){
        List<Map<String,Object>> wosaiPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH);
        if (CollectionUtils.isNotEmpty(wosaiPayments) && CollectionUtils.isNotEmpty(sqbPromotionDetails)) {

            Map<String, Map<String, Object>> paymentsMap = wosaiPayments.stream().filter(item -> MapUtils.getBooleanValue(item, Payment.IS_DELIVERY))
                    .collect(Collectors.toMap(item -> String.format("%s:%s", MapUtils.getString(item, Payment.ORIGIN_TYPE), MapUtils.getString(item, Payment.SOURCE)), Function.identity()));

            Map<String, Map<String, Object>> sqbPromotionDetailsMap = sqbPromotionDetails.stream().filter(item -> MapUtils.getBooleanValue(item, Payment.IS_DELIVERY))
                    .collect(Collectors.toMap(item -> String.format("%s:%s", MapUtils.getString(item, Payment.ORIGIN_TYPE), MapUtils.getString(item, Payment.SOURCE)), Function.identity()));

            paymentsMap.forEach((key, value) -> {
                if (sqbPromotionDetailsMap.containsKey(key)) {
                    long netAmount = MapUtils.getLongValue(value, Payment.NET_AMOUNT);
                    long sqbPromotionAmount = MapUtils.getLongValue(sqbPromotionDetailsMap.get(key), Payment.AMOUNT);
                    if (sqbPromotionAmount > netAmount) {
                        logger.error("退款撤单时，优惠金额不能大于原交易优惠金额");
                        throw new UpayTcpOrderRefundError(UpayErrorScenesConstant.UPAY_TCP_ORDER_AMOUNT_NOT_MATCH, UpayErrorScenesConstant.UPAY_TCP_ORDER_AMOUNT_NOT_MATCH_MESSAGE);
                    }
                }
            });

        }
    }

    /**
     * 退款撤单的时候，退款标识必须和源支付流水匹配才能做退款或者撤单
     * @param payTransaction
     * @param extended
     */
    public static void validateRefundFlag(Map<String,Object> payTransaction, Map<String,Object> extended, Map<String, Object> extraParams){
        String originalRefundFlag = (String) BeanUtil.getNestedProperty(payTransaction, Transaction.KEY_REFUND_FLAG);
        if(!StringUtil.empty(originalRefundFlag)){
            String currRefundFlag = BeanUtil.getPropString(extended, Transaction.SQB_REFUND_FLAG);
            if(!Objects.equals(originalRefundFlag, currRefundFlag)){
                throw new RefundFlagNotMatchException(UpayErrorScenesConstant.REFUND_FLAG_NOT_MATCH, UpayErrorScenesConstant.REFUND_FLAG_NOT_MATCH_MESSAGE, originalRefundFlag);
            }
            extraParams.put(Transaction.SQB_REFUND_FLAG, currRefundFlag);
            extended.remove(Transaction.SQB_REFUND_FLAG);
        }
    }

    /**
     * 更新支付宝直连token
     *
     * @param serviceProvider
     * @param facade
     * @param transaction
     * @param storeId
     * @param orderCtime
     */
    public static void configAlipayV2AuthInfo(MpayServiceProvider serviceProvider, ExternalServiceFacade facade
            , Map<String, Object> transaction, String storeId, long orderCtime) {
        if (!(serviceProvider instanceof AlipayV2ServiceProvider)) {
            return;
        }
        Map config = serviceProvider.getTradeParams(transaction);
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(config, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        if (liquidationNextDay) {
            return;
        }
        if(Order.SUB_PAYWAY_H5 == subPayway || Order.SUB_PAYWAY_APP == subPayway){
            return;
        }
        if(serviceProvider.getName().equals(AlipayFitnessServiceProvider.NAME)){
            //阶段付不需要格外的参数
            return;
        }

        String authAppId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_AUTH_APP_ID);
        String appAuthToken = BeanUtil.getPropString(config, TransactionParam.APP_AUTH_TOKEN);
        Map authInfo = null;

        if(StringUtil.empty(appAuthToken) && StringUtil.empty(authAppId)){
            throw new UpayBizException("支付宝2.0交易参数错误, 授权token与授权appId不能同时为空");
        }

        if(!StringUtil.empty(appAuthToken)){
            if(System.currentTimeMillis() - orderCtime >= 1000 * 60 * 10 && !StringUtil.empty(authAppId)){
                authInfo = facade.getAlipayV2AppAuthInfo(authAppId, storeId);
            }
        }else{
            authInfo = facade.getAlipayV2AppAuthInfo(authAppId, storeId);
        }

        if (MapUtils.isNotEmpty(config) && MapUtils.isNotEmpty(authInfo)) {
            String appAuthTokenNew = MapUtil.getString(authInfo, TransactionParam.APP_AUTH_TOKEN);
            String authStoreIdNew = MapUtil.getString(authInfo, TransactionParam.APP_AUTH_STORE_ID);
            String authShopIdNew = MapUtil.getString(authInfo, TransactionParam.APP_AUTH_SHOP_ID);

            Map<String, String> effectiveAuthInfo  = new HashMap();
            if (!StringUtils.isEmpty(appAuthTokenNew)) {
                effectiveAuthInfo.put(TransactionParam.APP_AUTH_TOKEN, appAuthTokenNew);
            }
            if (!StringUtils.isEmpty(authStoreIdNew)) {
                effectiveAuthInfo.put(TransactionParam.APP_AUTH_STORE_ID, authStoreIdNew);
            }
            if (!StringUtils.isEmpty(authShopIdNew)) {
                effectiveAuthInfo.put(TransactionParam.APP_AUTH_SHOP_ID, authShopIdNew);
            }

            if (MapUtils.isNotEmpty(effectiveAuthInfo)) {
                config.putAll(effectiveAuthInfo);
            }
        }
    }
    
    public static boolean isCommonSwitchOpen(String commonSwitch, int type) {
        if (commonSwitch == null || commonSwitch.length() <= type) {
            return false;
        }
        char currentStatus = commonSwitch.charAt(type);
        if (currentStatus == COMMON_SWTICH_OPEN) {
            return true;
        }
        return false;
    }

    public static boolean isReturnProviderResponse(Map transaction) {
        return isCommonSwitchOpen(MapUtil.getString((Map)transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.COMMON_SWITCH), TransactionParam.TYPE_COMMON_SWITCH_RETURN_PROVIDER_RESPONSE);
    }

    public static boolean isReturnProviderResponse(String commonSwitch) {
        return isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_RETURN_PROVIDER_RESPONSE);
    }

    public static boolean isSendFundBillList(String commonSwitch) {
        return isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_SEND_FUND_BILL_LIST);
    }

    public static boolean isCanPreCancel(String commonSwitch) {
        return isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_PRE_CANCEL);
    }

    public static boolean isEntPaySupportProfit(String commonSwitch) {
        return isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_ENTPAY_SUPPORT_PROFIT);
    }

    public static void changeDcepPattern(String patternString) {
        if(!StringUtils.isEmpty(patternString)) {
            DCEP_PATTERN = Pattern.compile(patternString);
        }
    }

    public static void changeMPayPattern(String patternString) {
        if(!StringUtils.isEmpty(patternString)) {
            MPAY_PATTERN = Pattern.compile(patternString);
        }
    }

    public static int getFqTypeByProductFlag(String productFlag) {
        if (productFlag.contains(ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode())) {
            return UpayConstant.USE_CREDIT;
        }
        return UpayConstant.USE_HBFQ;
    }

    public static String stringAppendIfAbsent(String orgString, String append) {
        if (com.wosai.pantheon.util.StringUtil.isEmpty(orgString)) {
            return append;
        }
        Set<String> set = new HashSet<>(Arrays.asList(orgString.split(",")));
        set.add(append);
        return String.join(",", set);
    }

    /**
     * 通联切收银宝后，收银宝属于直清，并没有余额。因此需要跳过余额冻结
     * @param currentClearanceProvider
     * @param payTransaction
     * @return
     */
    public static boolean isTlSwitchToSyb(int currentClearanceProvider, Map<String, Object> payTransaction) {
        //结算参数在收银宝下,要退的交易在通联下
        if(currentClearanceProvider == TransactionParam.CLEARANCE_PROVIDER_SYB){
            Map configSnapshot = MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT);
            if (configSnapshot != null) {
                int payTransactionClearanceProvider = MapUtil.getIntValue(configSnapshot, TransactionParam.CLEARANCE_PROVIDER);
                int payTransactionProvider = MapUtil.getIntValue(payTransaction, Transaction.PROVIDER);
                if (payTransactionClearanceProvider == TransactionParam.CLEARANCE_PROVIDER_TL && payTransactionProvider == Provider.TONG_LIAN.getCode()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 拷贝参数
     * @param src
     * @param dest
     * @param keys
     */
    public static void copyIfExists(Map<String,Object> src, Map<String,Object> dest, List<String> keys){
        if(src == null){
            return;
        }
        for (String key : keys) {
            if(src.containsKey(key)){
                dest.put(key, src.get(key));
            }
        }
    }

    /**
     * 处理 poi 信息
     * @param request
     * @param extraParams
     */
    public static void processRequestPoi(Map<String, Object> request, Map<String, Object> extraParams) {
        String longitude = (String) request.get(LONGITUDE);
        String latitude = (String) request.get(LATITUDE);
        if (longitude == null || latitude == null) {
            return;
        }
        if (IGNORE_POI_VALUE.equals(longitude) || IGNORE_POI_VALUE.equals(latitude)) {
            return;
        }
        if (IGNORE_ZERO_POI_VALUE.equals(longitude) && IGNORE_ZERO_POI_VALUE.equals(latitude)) {
            return;
        }
        extraParams.put(Transaction.POI, CollectionUtil.hashMap(Transaction.LONGITUDE, longitude, Transaction.LATITUDE, latitude));
    }


    /**
     * 余额账户类型初始化
     *
     * @param transaction
     */
    public static void initWalletAccountType(Map<String, Object> transaction) {
        int walletAccountType = ProviderWalletAccountTypeEnum.DEFAULT.getValue();
        int payWay = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        int provider = BeanUtil.getPropInt(transaction, Transaction.PROVIDER);
        if (payWay == Payway.BANKCARD.getCode()) {

            String crossCardType = BeanUtil.getPropString(transaction, String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.WILD_CARD_TYPE));
            String productFlag = ProductFlagEnum.BANK_CARD_LOCAL_SOURCE.getCode();
            //跨境刷卡业务
            if (!StringUtil.empty(crossCardType)) {
                walletAccountType = ProviderWalletAccountTypeEnum.CROSS_CARD.getValue();
                productFlag = ProductFlagEnum.BANK_CARD_CROSS_SOURCE.getCode();
            }
            String productFlagValue = BeanUtil.getPropString(transaction, Transaction.PRODUCT_FLAG);
            if (StringUtil.empty(productFlagValue)) {
                productFlagValue = productFlag;
            } else {
                List<String> filterProducts = Arrays.stream(productFlagValue.split(UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER))
                        .filter(o -> !Objects.equals(ProductFlagEnum.BANK_CARD_LOCAL_SOURCE.getCode(), o) && !Objects.equals(ProductFlagEnum.BANK_CARD_CROSS_SOURCE.getCode(), o))
                        .collect(Collectors.toList());
                filterProducts.add(productFlag);
                productFlagValue = String.join(UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER, filterProducts);
            }
            //重新更新product_flag
            BeanUtil.setNestedProperty(transaction, Transaction.PRODUCT_FLAG, productFlagValue);
            if (provider == Provider.TL_SYB.getCode()) {
                TLSybBankServiceProvider.convertBankProductFlag(transaction);
            }
        }
        //设置余额账户类型
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_WALLET_ACCOUNT_TYPE, walletAccountType);
    }


    public static void resetTradeApp(Map<String, Object> transaction) {
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
        if (Objects.equals(payway, Payway.BANKCARD.getCode())) {
            Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT, new HashMap());
            //拉卡拉一体化刷卡 在交易发生前还不知道是属于外卡交易还是内卡交易，在交易完成后根据付款卡类型来设置 使用tradeApp区分外卡和内卡
            if (Objects.equals(MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP), TransactionParam.TRADE_APP_BASIC_PAY)) {
                String newTradeApp = ApolloConfigurationCenterUtil.getDomesticCardTradeApp(TransactionParam.TRADE_APP_BASIC_PAY);
                if ((Objects.equals(provider, Provider.LAKALA_UNION_PAY_V3.getCode()) && LakalaOpenV3BankCardServiceProvider.isLakalaWildCard(transaction))
                        || (Objects.equals(provider, Provider.FUYOU.getCode()) && FuyouBankServiceProvider.isFuyouWildCard(transaction))
                ) {
                    newTradeApp = ApolloConfigurationCenterUtil.getWildCardTradeApp(TransactionParam.TRADE_APP_BASIC_PAY);
                }
                configSnapshot.put(TransactionParam.TRADE_APP, newTradeApp);

            }
        }
    }

    private static void extendedSetWxDetail(List<Map<String, Object>> goodsDetails, Map<String, Object> extended, int type) {
        boolean isV3Request = extended != null && Objects.equals(TransactionParam.WEIXIN_VERSION_V3, MapUtil.getBooleanValue(extended, Transaction.SQB_WX_VERSION));
        if (isV3Request) {
            if (type == Transaction.TYPE_REFUND) {
                extended.put(UpayConstant.VX_V3_GOODS_DETAIL, goodsDetails);
            } else {
                extended.put(UpayConstant.DETAIL, CollectionUtil.hashMap(
                        UpayConstant.VX_V3_GOODS_DETAIL, goodsDetails
                ));
            }
        } else {
            extended.put(UpayConstant.DETAIL, CollectionUtil.hashMap(
                    UpayConstant.GOODS_DETAIL, goodsDetails
            ));
        }
    }
}
