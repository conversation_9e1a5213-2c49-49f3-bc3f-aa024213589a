package com.wosai.upay.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 字符工具类
 * @date 2024-03-13
 */

public class CharacterUtil {
    /**
     * 过滤掉特殊字符。仅保留汉字、字母和数字
     *
     * @return
     */
    public static String filterSpecialCharacter(String origin) {
        if (StringUtils.isEmpty(origin)) {
            return origin;
        }

        StringBuilder target = new StringBuilder(origin.length());
        for (int i = 0; i < origin.length(); i++) {
            char c = origin.charAt(i);
            if (Character.isAlphabetic(c) || Character.isDigit(c) || isChineseCharacter(c)) {
                target.append(c);
            }
        }

        return target.toString();
    }

    /**
     * 是否汉字
     *
     * @return
     */
    public static boolean isChineseCharacter(Character ch) {
        return Character.UnicodeScript.HAN == Character.UnicodeScript.of(ch);
    }

    /**
     * 字符串中是否包含字母
     *
     * @param origin
     * @return
     */
    public static boolean containsAlphabetic(String origin) {
        if (StringUtils.isEmpty(origin)) {
            return false;
        }
        for (int i = 0; i < origin.length(); i++) {
            char c = origin.charAt(i);
            if (Character.isAlphabetic(c)) {
                return true;
            }
        }
        return false;
    }

}