package com.wosai.upay.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MapUtil {

    public static void removeNullValues(Object value) {
        if (value instanceof Map) {
            Map<String, Object> m = (Map<String, Object>) value;
            List<String> nullKeys = new ArrayList<String>();
            for (String key: m.keySet()) {
                Object v = m.get(key);
                if (v == null) {
                    nullKeys.add(key);
                }else if (v instanceof Map){
                    removeNullValues(v);
                }
            }
            for (String key: nullKeys) {
                m.remove(key);
            }
        }
    }

    public static List<Map<String,Object>> getValues(Map<String,Map<String,Object>> map){
        List<Map<String,Object>> values = new ArrayList<>();
        for(String key: map.keySet()){
            values.add(map.get(key));
        }
        return values;
    }

}
