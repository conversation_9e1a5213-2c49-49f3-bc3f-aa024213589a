package com.wosai.upay.exception;

@SuppressWarnings("serial")
public class DepositSynclOrderStateError extends UpayBizException {

    public DepositSynclOrderStateError(String message) {
        super(null, message);
    }
    
    public DepositSynclOrderStateError(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }
    
    public DepositSynclOrderStateError(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "DEPOSIT_SYNC_INVALID_ORDER_STATE";
    }
}
