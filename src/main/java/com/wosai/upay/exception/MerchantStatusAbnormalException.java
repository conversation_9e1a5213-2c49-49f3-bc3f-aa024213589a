package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;

@SuppressWarnings("serial")
public class MerchantStatusAbnormalException extends UpayBizException {

    public MerchantStatusAbnormalException(String message) {
        this(message, null);
    }

    public MerchantStatusAbnormalException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_MERCHANT_STATUS_ABNORMAL";
    }
    
    @Override
    public String getStandardCode() {
    	return UpayErrorScenesConstant.UPAY_MERCHANT_STATUS_ABNORMAL;
    }

}
