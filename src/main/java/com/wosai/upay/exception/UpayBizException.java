package com.wosai.upay.exception;

import java.util.Map;

import com.wosai.upay.workflow.UpayBizError;

@SuppressWarnings("serial")
public class UpayBizException extends UpayException {

	private String standardCode = null;
	private Map<String,Object> data = null; 
	
    public UpayBizException(String message) {
        super(message, null);
    }
    
    public UpayBizException(String standardCode, String defaultMessage) {
    	super(defaultMessage, null);
    	this.standardCode = standardCode;
    }
    
    public UpayBizException(String standardCode, String defaultMessage, Throwable cause) {
    	super(defaultMessage, cause);
    	this.standardCode = standardCode;
    }
    
    public UpayBizException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public UpayBizException(String standardCode, String message, Map<String,Object> data){
    	super(message, null);
    	this.standardCode = standardCode;
    	this.data = data;
    	
    }
    
    @Override
    public String getCode() {
        return "UPAY_BIZ_ERROR";
    }

    public String getResultCode() {
        return "FAIL";
    }
	@Override
	public String getStandardCode() {
		return standardCode;
	}
	@Override
	public Map getData() {
		return data;
	}
}
