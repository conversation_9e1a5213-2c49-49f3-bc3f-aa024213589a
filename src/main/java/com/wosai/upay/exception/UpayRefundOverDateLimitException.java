package com.wosai.upay.exception;

import java.util.Map;

@SuppressWarnings("serial")
public class UpayRefundOverDateLimitException extends UpayBizException {

    public UpayRefundOverDateLimitException(String message) {
        super(message);
    }
    
    public UpayRefundOverDateLimitException(String standardCode, String defaultMessage, Map<String, Object> data) {
        super(standardCode, defaultMessage, data);
    }

    @Override
    public String getCode() {
        return "UPAY_REFUND_OVER_DATE_LIMIT";
    }
}
