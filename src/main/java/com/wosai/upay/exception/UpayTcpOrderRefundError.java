package com.wosai.upay.exception;

@SuppressWarnings("serial")
public class UpayTcpOrderRefundError extends UpayBizException {

    public UpayTcpOrderRefundError(String message) {
        super(message);
    }
    
    public UpayTcpOrderRefundError(String standardCode, String defaultMessage) {
    	super(standardCode, defaultMessage);
    }
    
    public UpayTcpOrderRefundError(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_TCP_ORDER_REFUND_ERROR";
    }
}
