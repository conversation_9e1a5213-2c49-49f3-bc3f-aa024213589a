package com.wosai.upay.exception;

@SuppressWarnings("serial")
public class UpayRefundOrderStateError extends UpayBizException {

    public UpayRefundOrderStateError(String message) {
    	super(message);
    }
    
    public UpayRefundOrderStateError(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }
    
    public UpayRefundOrderStateError(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_REFUND_INVALID_ORDER_STATE";
    }
}
