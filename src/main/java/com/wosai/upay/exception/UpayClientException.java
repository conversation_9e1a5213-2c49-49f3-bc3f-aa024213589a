package com.wosai.upay.exception;

import java.util.Map;

import com.wosai.upay.workflow.UpayBizError;

@SuppressWarnings("serial")
public class UpayClientException extends UpayException {
	private String standardCode = null;
	private Map<String,Object> data = null; 
	
    public UpayClientException(String message) {
        super(message, null);
    }

    public UpayClientException(String standardCode, String defaultMessage) {
        super(defaultMessage);
        this.standardCode = standardCode;
    }
    
    public UpayClientException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_CLIENT_ERROR";
    }

	@Override
	public String getStandardCode() {
		return standardCode;
	}
	
	@Override
	public Map getData() {
		return data;
	}

}
