package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;

/**
 * <AUTHOR> Date: 2019-07-08 Time: 10:08
 */
public class TerminalConfigAbnormalException extends UpayBizException {


    public TerminalConfigAbnormalException(String message) {
        super(message);
    }

    public TerminalConfigAbnormalException(String message, Throwable cause) {
        super(message, cause);
    }

    public TerminalConfigAbnormalException(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }

    @Override
    public String getCode() {
        return "UPAY_TERMINAL_CONFIG_STATUS_ABNORMAL";
    }

    @Override
    public String getStandardCode() {
        return UpayErrorScenesConstant.UPAY_TERMINAL_CONFIG_STATUS_ABNORMAL_DISABLED;
    }
}
