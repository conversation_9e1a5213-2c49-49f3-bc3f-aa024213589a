package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.mpay.util.StringUtils;

@SuppressWarnings("serial")
public class UpaySingleTradeOverLimitError extends UpayBizException {

    public UpaySingleTradeOverLimitError() {
        this("商户单笔收款额超过限额");
    }
    public UpaySingleTradeOverLimitError(long limit) {
        this("商户单笔收款额超过限额" + StringUtils.cents2yuan(limit));
    }
    public UpaySingleTradeOverLimitError(String message) {
        super(UpayErrorScenesConstant.UPAY_SINGLE_TRADE_OVER_LIMIT, message);
    }
    
    public UpaySingleTradeOverLimitError(String standardCode, String message) {
    	super(standardCode, message);
    }

    @Override
    public String getCode() {
        return "UPAY_SINGLE_TRADE_OVER_LIMIT";
    }
}
