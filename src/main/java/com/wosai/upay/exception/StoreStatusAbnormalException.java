package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;

@SuppressWarnings("serial")
public class StoreStatusAbnormalException extends UpayBizException {

    public StoreStatusAbnormalException(String message) {
        this(message, null);
    }

    public StoreStatusAbnormalException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_STORE_STATUS_ABNORMAL";
    }
    
    @Override
    public String getStandardCode() {
    	return UpayErrorScenesConstant.UPAY_STORE_STATUS_ABNORMAL;
    }

}
