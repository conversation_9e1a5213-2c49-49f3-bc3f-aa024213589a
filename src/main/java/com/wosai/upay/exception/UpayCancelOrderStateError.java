package com.wosai.upay.exception;

@SuppressWarnings("serial")
public class UpayCancelOrderStateError extends UpayBizException {

    public UpayCancelOrderStateError(String message) {
        super(null, message);
    }
    
    public UpayCancelOrderStateError(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }
    
    public UpayCancelOrderStateError(String message, Throwable cause) {
        super(message, cause);
        // TODO Auto-generated constructor stub
    }

    @Override
    public String getCode() {
        return "UPAY_CANCEL_INVALID_ORDER_STATE";
    }
}
