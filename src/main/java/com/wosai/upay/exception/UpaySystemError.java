package com.wosai.upay.exception;

import java.util.Map;

@SuppressWarnings("serial")
public abstract class UpaySystemError extends UpayException {

	private String standardCode = null;
	private Map<String, Object> data = null;
	
    public UpaySystemError(String message) {
        super(message);
    }
    
    public UpaySystemError(String standardCode, String message) {
        this(standardCode, message, null);
    }

    public UpaySystemError(String message, Throwable cause) {
        super(message, cause);
    }
    
    public UpaySystemError(String standardCode, String message, Map<String,Object> data) {
        this(standardCode, message, data, null);
    }
    
    public UpaySystemError(String standardCode, String message, Map<String,Object> data, Throwable cause) {
        super(message, cause);
        this.standardCode = standardCode;
        this.data = data;
    }

    @Override
    public String getCode() {
    	return "UPAY_BIZ_ERROR";
    }

    
    @Override
    public String getStandardCode() {
    	return standardCode;
    }
    
	public Map<String, Object> getData() {
		return data;
	}
}
