package com.wosai.upay.exception;

/**
 * Created by jian<PERSON> on 18/12/15.
 */
public class RefundableAmountNotEnough extends UpayBizException {
    public RefundableAmountNotEnough(String message) {
        super(message);
    }
    
    public RefundableAmountNotEnough(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }

    public RefundableAmountNotEnough(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "REFUNDABLE_AMOUNT_NOT_ENOUGH";
    }
}
