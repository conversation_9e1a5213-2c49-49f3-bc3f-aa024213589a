package com.wosai.upay.exception;

public class RefundFlagNotMatchException extends UpayBizException {
    /**
     * 
     */
    private static final long serialVersionUID = -8240755689138409783L;
    private String refundFlag;

    public RefundFlagNotMatchException(String message) {
        super(message);
    }
    
    public RefundFlagNotMatchException(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }
    
    public RefundFlagNotMatchException(String standardCode, String defaultMessage, String refundFlag) {
        super(standardCode, defaultMessage);
        this.refundFlag = refundFlag;
    }
    

    public String getRefundFlag() {
        return refundFlag;
    }
}
