package com.wosai.upay.exception;

import java.util.Map;

@SuppressWarnings("serial")
public class ProviderStatusException extends UpayBizException {

    public ProviderStatusException(String message) {
        super(message);
    }

    public ProviderStatusException(String standardCode, String message){
    	super(standardCode, message);
    }

    public ProviderStatusException(String standardCode, String message, Map<String,Object> data){
        super(standardCode, message, data);
    }
    
    public ProviderStatusException(String message, int provider) {
        super(String.format("%s, provider=%d", message, provider));
    }

    @Override
    public String getCode() {
        return "UPAY_PROVIDER_STATUS";
    }

}
