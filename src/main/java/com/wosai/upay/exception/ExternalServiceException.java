package com.wosai.upay.exception;

import java.util.Map;

@SuppressWarnings("serial")
public class ExternalServiceException extends UpaySystemError {

    public ExternalServiceException(String message) {
        super(message);
    }
    
    public ExternalServiceException(String standardCode, String message) {
        super(standardCode, message);
    }
    
    public ExternalServiceException(String standardCode, String message, Throwable cause) {
        super(standardCode, message, null, cause);
    }
    
    public ExternalServiceException(String standardCode, String message, Map<String,Object> data) {
        super(standardCode, message, data, null);
    }

    public ExternalServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "EXTERNAL_SERVICE_EXCEPTION";
    }

}
