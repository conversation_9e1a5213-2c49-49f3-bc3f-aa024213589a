package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.mpay.util.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/15.
 */
public class UpayMerchantOverDailyPaywayLimitError extends UpayBizException{
    public UpayMerchantOverDailyPaywayLimitError() {
        this("商户此支付方式日收款额超过限额");
    }
    public UpayMerchantOverDailyPaywayLimitError(long limit) {
        this("商户此支付方式日收款额超过限额" + StringUtils.cents2yuan(limit));
    }
    public UpayMerchantOverDailyPaywayLimitError(String message) {
        super(message);
    }

    @Override
    public String getCode() {
        return "UPAY_MERCHANT_OVER_DAILY_PAYWAY_LIMIT";
    }

    @Override
    public String getStandardCode() {
        return UpayErrorScenesConstant.UPAY_MERCHANT_OVER_DAILY_PAYWAY_LIMIT;
    }
}
