package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.mpay.util.StringUtils;

@SuppressWarnings("serial")
public class UpayStoreOverDailyLimitError extends UpayBizException {

    public UpayStoreOverDailyLimitError() {
        this("商户日收款额超过限额");
    }
    public UpayStoreOverDailyLimitError(long limit) {
        this("商户日收款额超过限额" + StringUtils.cents2yuan(limit));
    }
    public UpayStoreOverDailyLimitError(String message) {
        super(message);
    }

    @Override
    public String getCode() {
        return "UPAY_STORE_OVER_DAILY_LIMIT";
    }
    
    @Override
    public String getStandardCode() {
    	return UpayErrorScenesConstant.UPAY_STORE_OVER_DAILY_LIMIT;
    }
}
