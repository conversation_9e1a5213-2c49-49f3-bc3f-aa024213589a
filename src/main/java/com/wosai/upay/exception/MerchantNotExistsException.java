package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;

@SuppressWarnings("serial")
public class MerchantNotExistsException extends UpayBizException {

    public MerchantNotExistsException(String message) {
        this(message, null);
    }

    public MerchantNotExistsException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_MERCHANT_NOT_EXISTS";
    }
    
    @Override
    public String getStandardCode() {
    	return UpayErrorScenesConstant.UPAY_MERCHANT_NOT_EXISTS;
    }

}
