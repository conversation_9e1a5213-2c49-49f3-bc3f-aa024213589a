package com.wosai.upay.exception;

import java.util.Map;

@SuppressWarnings("serial")
public class UpayTcpOrderNotRefundable extends UpayBizException {

    public UpayTcpOrderNotRefundable(String message) {
        super(message);
    }
    
    public UpayTcpOrderNotRefundable(String standardCode, String defaultMessage) {
    	super(standardCode, defaultMessage);
    }

    public UpayTcpOrderNotRefundable(String standardCode, String defaultMessage, Map<String, Object> data) {
        super(standardCode, defaultMessage, data);
    }
    
    public UpayTcpOrderNotRefundable(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_TCP_ORDER_NOT_REFUNDABLE";
    }
}
