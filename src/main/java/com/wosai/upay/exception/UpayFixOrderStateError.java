package com.wosai.upay.exception;

@SuppressWarnings("serial")
public class UpayFixOrderStateError extends UpayBizException {

    public UpayFixOrderStateError(String message) {
    	super(message);
    }
    
    public UpayFixOrderStateError(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }
    
    public UpayFixOrderStateError(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String getCode() {
        return "UPAY_FIX_INVALID_ORDER_STATE";
    }
}
