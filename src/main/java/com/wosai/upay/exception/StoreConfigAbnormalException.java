package com.wosai.upay.exception;

import com.wosai.constant.UpayErrorScenesConstant;

/**
 * <AUTHOR> Date: 2019-07-08 Time: 10:08
 */
public class StoreConfigAbnormalException extends UpayBizException {


    public StoreConfigAbnormalException(String message) {
        super(message);
    }

    public StoreConfigAbnormalException(String message, Throwable cause) {
        super(message, cause);
    }

    public StoreConfigAbnormalException(String standardCode, String defaultMessage) {
        super(standardCode, defaultMessage);
    }

    @Override
    public String getCode() {
        return "UPAY_STORE_CONFIG_STATUS_ABNORMAL";
    }

    @Override
    public String getStandardCode() {
        return UpayErrorScenesConstant.UPAY_STORE_CONFIG_STATUS_ABNORMAL_DISABLED;
    }
}
