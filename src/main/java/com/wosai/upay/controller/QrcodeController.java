package com.wosai.upay.controller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

import net.glxn.qrgen.QRCode;
import net.glxn.qrgen.image.ImageType;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.wosai.upay.service.QrcodeImaging;

@Controller
public class QrcodeController {
    @RequestMapping(value=QrcodeImaging.PATH)
    public void qrcode(@RequestParam("content") String content,
                       @RequestParam(value="size", defaultValue="500") int size,
                       HttpServletResponse response) throws IOException {

        response.setContentType("image/png");
        try(ByteArrayOutputStream baos = QRCode.from(content).to(ImageType.PNG).withSize(size, size).stream()){
            response.setContentLength(baos.size());
            
            response.getOutputStream().write(baos.toByteArray());
            response.getOutputStream().close();
        }
    }
}
