package com.wosai.upay.controller;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.wosai.upay.service.AdminService;
import com.wosai.upay.service.DispatcherRegisterService;


/**
 * Created by jianfree on 13/6/17.
 */
@Controller
@RequestMapping("/upay/admin")
public class AdminController {
    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private AdminService adminService;
    @Autowired
    private DispatcherRegisterService dispatcherRegisterService;

    @RequestMapping(value="/health" , produces = "application/json")
    @ResponseBody
    public boolean health(){
        return adminService.health();
    }

    @RequestMapping(value="/backupHealth" , produces = "application/json")
    @ResponseBody
    public boolean backupHealth(){
        return true;
    }

    @RequestMapping(value = "/prestop")
    @ResponseBody
    public String prestop(HttpServletRequest request) {
        String forwardedFor = request.getHeader("x-forwarded-for");
        if (forwardedFor != null || (!"127.0.0.1".equals(request.getRemoteAddr()) && !"[0:0:0:0:0:0:0:1]".equals(request.getRemoteAddr()))){
            return "fail";
        }
        return dispatcherRegisterService.offline();
    }

}
