package com.wosai.upay.controller;

import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.async.DeferredResult;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;

@Controller
@RequestMapping("/upay/v2/deposit")
public class DepositController 
{
	public static Map<String, String> PAY_DEPOSIT_RESPONSE_MAPPING = CollectionUtil.hashMap(
        "PAY_SUCCESS", "DEPOSIT_FREEZE_SUCCESS",
        "PAY_FAIL", "DEPOSIT_FREEZE_FAIL",
        "PAY_IN_PROGRESS", "DEPOSIT_FREEZE_IN_PROGRESS",
        "PAY_FAIL_IN_PROGRESS", "DEPOSIT_FREEZE_FAIL_IN_PROGRESS",
        "PAY_FAIL_ERROR", "DEPOSIT_FREEZE_FAIL_ERROR",
        "PRECREATE_SUCCESS", "PREFREEZE_SUCCESS",
        "PRECREATE_FAIL", "PREFREEZE_FAIL",
        "PRECREATE_FAIL_ERROR", "PREFREEZE_FAIL_ERROR",
        "PRECREATE_FAIL_IN_PROGRESS", "PREFREEZE_FAIL_IN_PROGRESS"
    );

    @Autowired
    private DepositService service;
	
    @Autowired
    private UpayService upayService;

    @RequestMapping(value="/freeze", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> freeze(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        if(null != params) {
            BeanUtil.setProperty(params, DepositService.IS_DEPOSIT, true);
        }
        return upayService.pay(params, response);
    }

    //ha回调只对precreate pay cancel做了特殊路由处理
    @RequestMapping(value="/pay", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> pay(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        if(null != params) {
            BeanUtil.setProperty(params, DepositService.IS_DEPOSIT, true);
        }
        return upayService.pay(params, response);
    }

    @RequestMapping(value="/prefreeze", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> prefreeze(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        if(null != params) {
            BeanUtil.setProperty(params, DepositService.IS_DEPOSIT, true);
        }
        return upayService.precreate(params, response);
    }

    //ha回调只对precreate pay cancel做了特殊路由处理
    @RequestMapping(value="/precreate", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> precreate(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        if(null != params) {
            BeanUtil.setProperty(params, DepositService.IS_DEPOSIT, true);
        }
        return upayService.precreate(params, response);
    }
    
    @RequestMapping(value="/cancel", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> cancel(@RequestBody Map<String, Object> params) {
        return service.cancel(params);
    }

    @RequestMapping(value="/reconcileRevoke", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> reconcileRevoke(@RequestBody Map<String, Object> params) {
        return service.reconcileRevoke(params);
    }

    @RequestMapping(value="/consume", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> consume(@RequestBody Map<String, Object> params) {
        return service.consume(params);
    }

    @RequestMapping(value="/sync", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> sync(@RequestBody Map<String, Object> params) {
        return service.sync(params);
    }

    @RequestMapping(value="/fix", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fix(@RequestBody Map<String, Object> params) {
        return service.fix(params);
    }
    
    @RequestMapping(value="/fixConsumeToSuccess", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixConsumeToSuccess(@RequestBody Map<String, Object> params) {
        return service.fixConsumeToSuccess(params);
    }
    
    @RequestMapping(value="/fixOrderIfConsumeNotSuccess", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixOrderIfConsumeNotSuccess(@RequestBody Map<String, Object> params) {
        return service.fixOrderIfConsumeNotSuccess(params);
    }
    
    @RequestMapping(value="/fixCancel", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixCancel(@RequestBody Map<String, Object> params) {
        return service.fixCancel(params);
    }
    
    @RequestMapping(value="/fixOrderIfCancelNotSuccess", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixOrderIfCancelNotSuccess(@RequestBody Map<String, Object> params) {
        return service.fixOrderIfCancelNotSuccess(params);
    }
}