package com.wosai.upay.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.exception.ManagementException;
import com.wosai.upay.service.ProviderManager;

@Controller
@RequestMapping("/upay/provider")
public class ProviderMangerController {

    @Autowired
    private ProviderManager providerManager;

    @RequestMapping(value="/status", method=RequestMethod.GET, produces="application/json")
    @ResponseBody
    public Map<String, Map<String,Object>> paywayStatus() {
        return providerManager.status();
    }

    @RequestMapping(value="/update", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String,  Map<String,Object>> update(@RequestBody Map<String, Map<String,Object>> update) throws ManagementException {
        return providerManager.update(update);
    }

    @SuppressWarnings("unchecked")
    @ExceptionHandler(ManagementException.class)
    @ResponseBody
    public Map<String, Object> handleManagementException(ManagementException ex) {
        return CollectionUtil.hashMap("result_code", "400",
                                      "error_class", ex.getClass(),
                                      "error_message", ex.getMessage());
    }
}
