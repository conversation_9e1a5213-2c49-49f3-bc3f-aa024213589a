package com.wosai.upay.controller;

import com.ctrip.framework.apollo.ConfigService;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.SimpleRedisLock;
import com.wosai.upay.service.SimpleTsnGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/")
public class ApolloCheckController {
    @Resource
    DataRepository dataRepository;
    @Resource
    SimpleTsnGenerator simpleTsnGenerator;
    @Resource
    SimpleRedisLock simpleRedisLock;

    @RequestMapping(value="/apollo-check" , produces = "application/json")
    @ResponseBody
    public String apolloCheck(@RequestParam String checkValue){
        if (!checkValue.equals(ConfigService.getAppConfig().getProperty("test", null))) {
            throw new RuntimeException("apollo check fail");
        }
        simpleTsnGenerator.nextSn();
        dataRepository.getOrder("c98a99a7-719d-4128-adec-aade34f1f37f,", null, "7895226298293350", null);
        simpleRedisLock.unlock("apollo-test", "1");
        return "success";
    }

}
