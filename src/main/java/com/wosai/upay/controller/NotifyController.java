package com.wosai.upay.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.wosai.data.bean.BeanUtil;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.mpay.api.NuccBestPay.NuccBestPayConstants;
import com.wosai.mpay.api.NuccBestPay.RequestGenerator;
import com.wosai.mpay.api.bestpay.BusinessV2Fields;
import com.wosai.mpay.api.bestpay.ResponseV2Fields;
import com.wosai.mpay.api.ccb.ResponseFields;
import com.wosai.mpay.api.cgbbank.CGBBankProtocolFields;
import com.wosai.mpay.api.cmb.ProtocolFields;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.entpay.EntPayResponseFields;
import com.wosai.mpay.api.macaupass.enums.MacauPassTradeStatusEnum;
import com.wosai.mpay.api.guotong.GuotongResponseFields;
import com.wosai.mpay.api.psbcbank.PSBCResponseFields;
import com.wosai.mpay.api.tl.syb.BusinessFields;
import com.wosai.mpay.api.tl.syb.SybConstants;
import com.wosai.mpay.api.unionpayopen.UnionPayOpenConstants;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.api.xzx.constants.XZXMethodFieldsConstants;
import com.wosai.mpay.api.zjtlcb.TLBRequestFields;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.XmlUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.prepaid.api.result.TradeStateQueryResult;
import com.wosai.upay.service.FinishQuotaTransactionProcessor;
import com.wosai.upay.service.SupportService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.PSBCServiceProvider;
import com.wosai.upay.workflow.ZJTLCBServiceProvider;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ScheduledExecutorService;

@Controller
@RequestMapping("/upay/v2")
public class NotifyController 
{
    public static final String WEIXINV3_NOTIFICATION_SUCCESS = "{\"code\":\"SUCCESS\",\"message\":\"OK\"}";
    public static final String WEIXINV3_NOTIFICATION_FAIL = "{\"code\":\"FAIL\",\"message\":\"OK\"}";
    public static final String WEIXIN_NOTIFICATION_SUCCESS = "<xml><return_code>SUCCESS</return_code><return_msg>OK</return_msg></xml>";
    public static final String WEIXIN_NOTIFICATION_FAIL = "<xml><return_code>FAIL</return_code><return_msg>OK</return_msg></xml>";
    public static final String BESTPAY_NOTIFICATION_SUCCESS = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
            "<root xmlns=\"namespace_string\">" +
            "<MsgHeader><SndDt>%s</SndDt><MsgTp>bcs.901.001.01</MsgTp><SignSN>4052421310</SignSN></MsgXHeader>" +
            "<MsgBody><SysRtnTm>%s</SysRtnTm><SysRtnCd>00000000</SysRtnCd><SysRtnDesc>成功</SysRtnDesc><MsgBody>" +
            "</root>{S:p1GWYwrrr/N4OF67uZV9AglAlpCBZp86f1gozRoGAjFIrcrlV0HaVvSh0I2Nf5s2p83CDRbZiPNkDgjqboyYGwftwz3281GNXW8pc/LZnbxgDMyu63F5KVYYx6S8Uw6xgsPl0Lfc2mnNXFloBYNbKvUzFUgrRCGLbmI3nR4+HtE0C8BMUKT8TkWacYPLpfpR7t05jOgpX6+4g4NUtDidoGYmTk54505A+JxfwKnSlpYIgzPVxL8w+Jy6MOykXlczFHHRjwU6raOtQHrnW4NM+c+KzLTVysVnqTEGF2fJHb2dLSejVcUJg3TwUvxyK4ARWCM0iGGGxrYNWlYi+NmBfA==}";
    public static final String BESTPAYV2_NOTIFICATION_SUCCESS = "{\"success\":true,\"result\":{\"statusCode\":200,\"outTradeNo\":%s,\"tradeNo\":%s},\"errorCode\":%s,\"errorMsg\":%s}";
    public static final String RESPONSE_SUCCESS = "success";
    public static final String RESPONSE_SUCCESS_TL = "SUCCESS";
    public static final String RESPONSE_SUCCESS_UPPER_CASE = "SUCCESS";
    public static final String RESPONSE_SUCCESS_HAIKE = "ok";
    public static final String PREPAID_SUCCESS = "{\"returnCode\":\"SUCCESS\"}";
    public static final String CMB_RETURN_SUCCESS = "{\"returnCode\":\"SUCCESS\",\"encoding\":\"UTF-8\",\"version\":\"0.0.1\",\"signMethod\":\"02\",\"respCode\":\"SUCCESS\",\"sign\":\"MEUCID8AzwwdBjhMS59YFUA1qi+IvwXCEEL48yytKWWEmtMAAiEA6cuOTfdZhotJ2iC5kSqDzWosFQgXaTjnEIO/yUUUgzU=\"}";
    public static final String LAKALA_OPEN_V3_SUCCESS ="{\"code\":\"SUCCESS\",\"message\":\"执行成功\"}";
    public static final String FUYOU_NOTIFICATION_SUCCESS = "1";
    public static final String RESPONSE_SUCC = "SUCC";

    private static final Logger LOGGER = LoggerFactory.getLogger(NotifyController.class);

    @Autowired
    private UpayService service;
    @Autowired
    private SupportService supportService;
    @Autowired
    private ZJTLCBServiceProvider zjtlcbServiceProvider;

    @Autowired
    private PSBCServiceProvider psbcServiceProvider;

    @Resource(name = "clientNotifyThreadPool")
    private ScheduledExecutorService executor;

    @RequestMapping(value="/notify/alipay/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/alipayDebit/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayDebitNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/alipaywap/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayWapNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }
    
    @RequestMapping(value="/notify/alipay/deposit/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayDepositNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/alipaywap/deposit/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayWapDepositNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_NUCC + "/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayNuccNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_NUCC_WAP + "/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String alipayNuccWapNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_UNIONPAY + "/{upayOrderNumber}/*",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE, MediaType.APPLICATION_ATOM_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String alipayUnionpayNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_UNIONPAY_WAP + "/{upayOrderNumber}/*",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE, MediaType.APPLICATION_ATOM_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String alipayUnionpayWapNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_UNIONPAY + "/deposit/{upayOrderNumber}/*",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE, MediaType.APPLICATION_ATOM_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String alipayUnionpayDepositNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_UNIONPAY_WAP + "/deposit/{upayOrderNumber}/*",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE, MediaType.APPLICATION_ATOM_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String alipayUnionpayWapDepositNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_TL + "/{upayOrderNumber}/*",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE, MediaType.APPLICATION_ATOM_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String alipayTLNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_ALIPAY_WAP_TL + "/{upayOrderNumber}/*",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE, MediaType.APPLICATION_ATOM_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String alipayTLWapNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS_TL;
    }
    
    @RequestMapping(value="/notify/weixin/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml;charset=utf-8")
    @ResponseBody
    public String weixinNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        try{
            Map map = XmlUtils.parse(body);
            service.notify(upayOrderNumber, map);
            return WEIXIN_NOTIFICATION_SUCCESS;
        }catch (Exception e){
            return WEIXIN_NOTIFICATION_FAIL;
        }

    }


    @RequestMapping(value="/notify/cmcc/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String cmccNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/weixinwap/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml;charset=utf-8")
    @ResponseBody
    public String weixinWapNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        try{
            Map map = XmlUtils.parse(body);
            service.notify(upayOrderNumber, map);
            return WEIXIN_NOTIFICATION_SUCCESS;
        }catch (Exception e){
        	return WEIXIN_NOTIFICATION_FAIL;
        }
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_WEIXIN_NUCC+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String weixinNuccNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, map);
        return WEIXIN_NOTIFICATION_SUCCESS;
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_BESTPAY_NUCC+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String bestpayNuccNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        if(body.contains("{S:")){
            body = body.split("(\\{S:)")[0];
        }
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, (Map<String, Object>)map.get(NuccBestPayConstants.MSG_BODY));
        return String.format(BESTPAY_NOTIFICATION_SUCCESS, RequestGenerator.getCurrentTime(), RequestGenerator.getCurrentTime());
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_WEIXIN_NUCC_WAP+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String weixinNuccWapNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, map);
        return WEIXIN_NOTIFICATION_SUCCESS;
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_WEIXIN_UNIONPAY+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String weixinUnionpayNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, map);
        return WEIXIN_NOTIFICATION_SUCCESS;
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_WEIXIN_TL+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String weixinTLpayNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, map);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_WEIXIN_UNIONPAY_WAP+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String weixinUnionpayWapNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, map);
        return WEIXIN_NOTIFICATION_SUCCESS;
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_WEIXIN_WAP_TL+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml")
    @ResponseBody
    public String weixinTLWapNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        Map map = XmlUtils.parse(body);
        service.notify(upayOrderNumber, map);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value="/notify/bestpay/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String bestpay(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return "UPTRANSEQ_" + BeanUtil.getPropString(body, com.wosai.mpay.api.bestpay.BusinessFields.UPTRANSEQ);
    }

    @RequestMapping(value="/notify/bestpayv2/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String bestpav2(@RequestBody Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return String.format(BESTPAYV2_NOTIFICATION_SUCCESS, BeanUtil.getPropString(body, BusinessV2Fields.OUT_TRADE_NO), BeanUtil.getPropString(body, ResponseV2Fields.TRADE_NO), BeanUtil.getPropString(body, ResponseV2Fields.ERROR_CODE), BeanUtil.getPropString(body, ResponseV2Fields.ERROR_MSG));
    }

    @RequestMapping(value="/notify/swiftpass/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String swiftpassNotify(HttpServletRequest request, @PathVariable String upayOrderNumber){
        try{
            InputStream in = request.getInputStream();
            Map map = XmlUtils.parse(in);
            service.notify(upayOrderNumber, map);
            return RESPONSE_SUCCESS;
        }catch (Exception e){
            return "fail";
        }

    }

    @RequestMapping(value="/notify/unionpayOpen/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String unionpayOpen(@RequestBody Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return UnionPayOpenConstants.NOTIFY_SUCCESS;
    }

    @RequestMapping(value="/notify/"+MpayServiceProvider.NOTIFY_UNIONPAY_TL+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String unionpayTL(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value="/notify/unionpayOnline/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String unionpayOnline(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        //返回的http status 200为成功
        return "";
    }

    @RequestMapping(value="/notify/"+MpayServiceProvider.NOTIFY_UNIONPAY_HAIKE+"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String unionpayHaike(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return RESPONSE_SUCCESS_HAIKE;
    }

    @RequestMapping(value="/notify/trade/unionpayHaike/{orderSn}", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String unionpayHaikeCommon(@RequestBody Map<String, Object> body, @PathVariable String orderSn){
        service.notify(UpayUtil.getOrderIdBySn(orderSn), body);
        return RESPONSE_SUCCESS_HAIKE;
    }

    @RequestMapping(value="/notify/weixinV3/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml;charset=utf-8")
    @ResponseBody
    public String weixinV3Notify(@RequestBody String body, @PathVariable String upayOrderNumber,
                                 @RequestHeader("Wechatpay-Nonce")String weixinNonce,
                                 @RequestHeader("Wechatpay-Timestamp")String weixinTimestamp,
                                 @RequestHeader("Wechatpay-Signature")String weixinSignature){
        try{
            JSONObject map = JSON.parseObject(body);
            Map<String,Object> header = new HashMap<>();
            header.put(WeixinConstants.WECHATPAY_NONCE,weixinNonce);
            header.put(WeixinConstants.WECHATPAY_TIMESTAMP,weixinTimestamp);
            header.put(WeixinConstants.WECHATPAY_SIGNATURE,weixinSignature);
            map.put(HttpConstant.REQUEST_HEADER,header);
            map.put(HttpConstant.BODY,body);
            service.notify(upayOrderNumber, map);
            return WEIXINV3_NOTIFICATION_SUCCESS;
        }catch (Exception e){
            LOGGER.error("weixinV3Notify error:",e);
            return WEIXINV3_NOTIFICATION_FAIL;
        }
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_CHINAUMS +"/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain")
    @ResponseBody
    public String chinaumsNotify(@RequestParam Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return "SUCCESS";
    }

    @ResponseBody
    @RequestMapping(value = "/notify/cmbPay/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public String cmbPayNotify(HttpServletRequest request, @PathVariable String upayOrderNumber) {
        Map<String, Object> cmbNotify = new HashMap<>();
        Map<String, Object> notification = new HashMap<>();
        Enumeration<String> nameEnum = request.getParameterNames();
        while (nameEnum.hasMoreElements()) {
            String key = nameEnum.nextElement();
            String value= request.getParameter(key);
            LOGGER.info("[招行通道支付回调], key: {}, value: {}", key, value);
            if (StringUtils.isNotBlank(value)) {
                try {
                    if (StringUtils.equalsIgnoreCase(key, ProtocolFields.SIGN)) {
                        notification.put(key, value);
                    } else {
                        notification.put(key, URLDecoder.decode(value, "utf-8"));
                    }
                } catch (Exception e) {
                    LOGGER.warn("招商银行回调URL解码异常, 异常栈: ", e);
                }
            }
        }
        cmbNotify.put("cmbNotify", notification);
        service.notify(upayOrderNumber, cmbNotify);
        return CMB_RETURN_SUCCESS;
    }

    @RequestMapping(value="/notify/psbcbank/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public Map<String, Object> psbcbankNotify(@RequestBody Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return ImmutableMap.of(PSBCResponseFields.RESP_CODE, "0000", PSBCResponseFields.RESP_DESC, "邮储回调成功");
    }

    @RequestMapping(value="/notify/cgbbank/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String cgbbankNotify(HttpServletRequest request, @RequestBody String body, @PathVariable String upayOrderNumber, HttpServletResponse response){

        Map<String, Object> notification = new HashMap<>();
        notification.put(CGBBankProtocolFields.APP_ID, request.getHeader(CGBBankProtocolFields.APP_ID));
        notification.put(CGBBankProtocolFields.SIGNATURE, request.getHeader(CGBBankProtocolFields.SIGNATURE));
        notification.put(CGBBankProtocolFields.SIGN_TYPE, request.getHeader(CGBBankProtocolFields.SIGN_TYPE));
        notification.put(CGBBankProtocolFields.CERT_ID, request.getHeader(CGBBankProtocolFields.CERT_ID));
        notification.put(CGBBankProtocolFields.ENCRYPT_KEY, request.getHeader(CGBBankProtocolFields.ENCRYPT_KEY));
        notification.put(CGBBankProtocolFields.ENCRYPT_TYPE, request.getHeader(CGBBankProtocolFields.ENCRYPT_TYPE));
        notification.put(CGBBankProtocolFields.BODY, body);

        LOGGER.info("[广发通道支付回调], notification is {}", notification);
        service.notify(upayOrderNumber, notification);
        Map<String, Object> notifyResult = MapUtil.getMap(notification, "copsResponseEntity");
        if (MapUtil.isEmpty(notifyResult)){
            return null;
        }
        LOGGER.info("copsResponseEntity is {}", JSONObject.toJSONString(notifyResult));
        Map<String, Object> notifyHeader = MapUtil.getMap(notifyResult, "responseHeader");
        notifyHeader.forEach((k, v) -> response.setHeader(k, String.valueOf(v)));
        return MapUtil.getString(notifyResult, "responseStr");
    }

    @RequestMapping(value="/notify/hxbank/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String hxbankNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        LOGGER.info("[华夏银行通道支付回调], request: {}", body);
        JSONObject request = JSON.parseObject(body);
        Map<String, Object> notification = new HashMap<>();
        notification.put("hxNotify", request);
        service.notify(upayOrderNumber, notification);
        return MapUtil.getString(notification, "response");
    }


    @RequestMapping(value="/notify/trade/ccb/{orderSn}", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String ccbNotify(@RequestBody Map<String, Object> body, @PathVariable String orderSn){
        LOGGER.info("[建设银行通道支付回调], request: {}", body);
        service.notify(UpayUtil.getOrderIdBySn(orderSn), body);
        return MapUtil.getString(body, "response");
    }


    @RequestMapping(value="/notify/trade/zjtlcb/{upayOrderNumber}", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String zjtlNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        LOGGER.info("[浙江泰隆通道支付回调], request: {}", body);
        Map<String, Object> notification = new HashMap<>();
        notification.put(TLBRequestFields.ZJTLCB_NOTIFY, body);
        service.notify(upayOrderNumber, notification);
        String response = null;
        try{
            response = zjtlcbServiceProvider.makeNotifyResponseSuccessContent(body);
        }catch (Exception e){
            LOGGER.error("makeNotifyResponseSuccessContent error: " + e.getMessage(), e);
        }
        LOGGER.info("[浙江泰隆通道支付回调], response: {}", response);
        return response;
    }


    @RequestMapping(value = "/notify/ccb", method = RequestMethod.POST)
    public String ccbNotify(HttpServletRequest request) {
        Map<String, Object> ccbNotify = new HashMap<>();
        Map<String, Object> notification = new HashMap<>();
        String orderSn = null;
        Enumeration<String> nameEnum = request.getParameterNames();
        while (nameEnum.hasMoreElements()) {
            String key = nameEnum.nextElement();
            String value= request.getParameter(key);
            LOGGER.info("[建行通道支付回调], key: {}, value: {}", key, value);
            if (StringUtils.isNotBlank(value)) {
                try {
                    if (StringUtils.equalsIgnoreCase(key, ResponseFields.SIGN)) {
                        notification.put(key, value);
                    } else {
                        if (StringUtils.isEmpty(value)) {
                            continue;
                        }
                        String decodeValue = URLDecoder.decode(value, "utf-8");
                        if (StringUtils.equalsIgnoreCase(key, ResponseFields.ORDERID)) {
                            orderSn = decodeValue;
                        }
                        notification.put(key, decodeValue);
                    }
                } catch (Exception e) {
                    LOGGER.warn("建行银行回调URL解码异常, 异常栈: ", e);
                }
            }
        }

        ccbNotify.put("ccbNotify", notification);
        service.notify(orderSn, ccbNotify);
        return null;
    }

    @RequestMapping(value="/notify/icbcbank/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String icbcbankNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        LOGGER.info("[工商银行通道支付回调], request: {}", body);
        Map<String, Object> notification = new HashMap<>();
        notification.put("icbcNotify", body);
        service.notify(upayOrderNumber, notification);
        return MapUtil.getString(notification, "response");
    }

    @RequestMapping(value = "/notify/prepaid/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String prepaidNotify(@RequestBody TradeStateQueryResult result, @PathVariable String upayOrderNumber) {
        LOGGER.info("[储值支付回调], request: {}", JSON.toJSONString(result));
        Map<String, Object> notification = new HashMap<>();
        notification.put("prepaidNotify", result);
        service.notify(upayOrderNumber, notification);
        return PREPAID_SUCCESS;
    }

    @RequestMapping(value="/notify/"+ MpayServiceProvider.NOTIFY_LAKALA_OPEN_V3 + "/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String lakalaOpenV3Notify (@RequestBody Map<String, Object> body, @PathVariable String upayOrderNumber){
        service.notify(upayOrderNumber, body);
        return LAKALA_OPEN_V3_SUCCESS;
    }

    @RequestMapping(value = "/notify/" + MpayServiceProvider.NOTIFY_TL_SYB + "/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public void tlSybNotify(@PathVariable String upayOrderNumber, HttpServletRequest request, HttpServletResponse response) throws Exception {
        request.setCharacterEncoding("UTF-8");//通知传输的编码为GBK
        response.setCharacterEncoding("UTF-8");
        TreeMap<String, Object> requestParams = new TreeMap<>();

        Map reqMap = request.getParameterMap();
        for (Object key : reqMap.keySet()) {
            String value = ((String[]) reqMap.get(key))[0];
            requestParams.put(key.toString(), value);
        }
        if (SybConstants.DEFAULT_SIGN_TYPE.equals(requestParams.get(BusinessFields.SIGNTYPE))) {
            service.notify(upayOrderNumber, requestParams);
            response.getOutputStream().write("success".getBytes());
            response.flushBuffer();
        }
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_WEIXIN_HKV3 + "/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/xml;charset=utf-8")
    @ResponseBody
    public String weixinHKV3Notify(@RequestBody String body, @PathVariable String upayOrderNumber,
                                 @RequestHeader("Wechatpay-Nonce")String weixinNonce,
                                 @RequestHeader("Wechatpay-Timestamp")String weixinTimestamp,
                                 @RequestHeader("Wechatpay-Signature")String weixinSignature){
        try{
            JSONObject map = JSON.parseObject(body);
            Map<String,Object> header = new HashMap<>();
            header.put(WeixinConstants.WECHATPAY_NONCE,weixinNonce);
            header.put(WeixinConstants.WECHATPAY_TIMESTAMP,weixinTimestamp);
            header.put(WeixinConstants.WECHATPAY_SIGNATURE,weixinSignature);
            map.put(HttpConstant.REQUEST_HEADER,header);
            map.put(HttpConstant.BODY,body);
            service.notify(upayOrderNumber, map);
            return WEIXINV3_NOTIFICATION_SUCCESS;
        }catch (Exception e){
            LOGGER.error("weixinHKV3Notify error:",e);
            return WEIXINV3_NOTIFICATION_FAIL;
        }
    }

    @RequestMapping(value="/notify/fuyou/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/x-www-form-urlencoded")
    @ResponseBody
    public void fuyouNotify(@PathVariable String upayOrderNumber, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> requestParams = new HashMap<>();
        Map reqMap = request.getParameterMap();
        for (Object key : reqMap.keySet()) {
            String value = ((String[]) reqMap.get(key))[0];
            requestParams.put(key.toString(), value);
        }
        service.notify(upayOrderNumber, requestParams);
        response.getOutputStream().write(FUYOU_NOTIFICATION_SUCCESS.getBytes());
        response.flushBuffer();
    }

    /**
     * 富友手续费更新并同步余额接口
     * 由notify-pay服务发起回调通知
     *
     * @param body
     * @return
     */
    @Timed
    @Trace
    @RequestMapping(value = "/notify/fuyou/updateFuyouTradeFeeSyncWallet", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public DeferredResult<Map<String, Object>> updateFuyouTradeFeeSyncWallet(@RequestBody Map<String, Object> body) {
        DeferredResult<Map<String, Object>> deferredResult = new DeferredResult<>();
        executor.execute(RunnableWrapper.of(() -> {
            String tsn = BeanUtil.getPropString(body, Transaction.TSN);
            Boolean result = false;
            try {
                result = supportService.updateFuyouTradeFeeSyncWallet(body);
            } catch (Exception e) {
                LOGGER.error("富友手续费更新并同步余额接口失败. tsn:{}", tsn, e);
            }
            String resultCode = BooleanUtils.isTrue(result) ? CommonResponse.SUCCESS : CommonResponse.FAIL;
            deferredResult.setResult(UpayUtil.apiSuccess(UpayUtil.bizResponse(resultCode, null, null, null, null)));
        }));
        return deferredResult;
    }

    /**
     * 额度包交易流水完成接口
     * 用于额度包完全后置处理
     *
     * @param body
     * @return
     */
    @Timed
    @Trace
    @RequestMapping(value = "/notify/quota/finishTransaction", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public DeferredResult<Map<String, Object>> quotaFinishTransaction(@RequestBody Map<String, Object> body) {
        DeferredResult<Map<String, Object>> deferredResult = new DeferredResult<>();
        executor.execute(RunnableWrapper.of(() -> {
            String tsn = BeanUtil.getPropString(body, Transaction.TSN);
            Map<String, Object> result = null;
            try {
                result = supportService.finishQuotaTransaction(body);
            } catch (Exception e) {
                LOGGER.error("富友手续费更新并同步余额接口失败. tsn:{}", tsn, e);
            }
            Map transaction = MapUtil.getMap(result, FinishQuotaTransactionProcessor.TRANSACTION);
            String resultCode = MapUtil.getBooleanValue(result, FinishQuotaTransactionProcessor.RESULT) ? CommonResponse.SUCCESS : CommonResponse.FAIL;
            deferredResult.setResult(UpayUtil.apiSuccess(UpayUtil.bizResponse(resultCode, null, null, null, transaction)));
        }));
        return deferredResult;
    }

    /**
     * 富友同步es与分账
     * 由notify-pay服务发起回调通知
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/notify/fuyou/syncFuyouAnalyzeTradeAndSharing", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public DeferredResult<Map<String, Object>> syncFuyouAnalyzeTradeAndSharing(@RequestBody Map<String, Object> request) {
        DeferredResult<Map<String, Object>> deferredResult = new DeferredResult<>();
        executor.execute(() -> {
            Boolean result = false;
            try {
                result = supportService.syncFuyouAnalyzeTradeAndSharing(request);
            } catch (Exception e) {
                LOGGER.error("富友同步es与分账失败. request:{}", JacksonUtil.toJsonString(request), e);
            }
            String resultCode = BooleanUtils.isTrue(result) ? CommonResponse.SUCCESS : CommonResponse.FAIL;
            deferredResult.setResult(UpayUtil.apiSuccess(UpayUtil.bizResponse(resultCode, null, null, null, null)));
        });
        return deferredResult;
    }

    @RequestMapping(value="/notify/trade/alipayAfterUser/{orderSn}", method = RequestMethod.POST, produces = "text/plain;charset=utf-8")
    @ResponseBody
    public String alipayAfterUserTradeNotify(@RequestBody Map<String, Object> body, @PathVariable String orderSn){
        service.notify(UpayUtil.getOrderIdBySn(orderSn), body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value="/notify/entpay/{upayOrderNumber}/*", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Map<String, Object> entpayNotify(@PathVariable String upayOrderNumber, @RequestBody Map<String, Object> body) {
        service.notify(upayOrderNumber, body);
        return ImmutableMap.of(EntPayResponseFields.RET_CODE, 0, EntPayResponseFields.RET_MSG, "SUCCESS");
    }

    /**
     * 客户端回调接口， 通知网关交易状态
     * @param body
     * @return
     */
    @RequestMapping(value= {"/notify/trade/client", "/notify/trade/client/*" }, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public String tradeClientNotify(@RequestBody Map<String, Object> body) {
        String orderSn = MapUtil.getString(body, Transaction.ORDER_SN);
        service.notify(UpayUtil.getOrderIdBySn(orderSn), body);
        return RESPONSE_SUCCESS;
    }

    @RequestMapping(value = "/notify/trade/pufa/{upayOrderNumber}", method = RequestMethod.POST)
    public String spdbNotify(@PathVariable String upayOrderNumber, @RequestBody String body) {
        LOGGER.info("[浦发通道支付回调], request: {}", body);
        Map<String, Object> spdbNotify = new HashMap<>();
        spdbNotify.put("spdbNotify", body);
        service.notify(upayOrderNumber, spdbNotify);
        return null;
    }

    @RequestMapping(value="/notify/" + MpayServiceProvider.NOTIFY_CMBCBANK + "/{upayOrderNumber}/*", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public String cmbcbankNotify(@PathVariable String upayOrderNumber, @RequestBody Map<String, Object> body) {
        service.notify(upayOrderNumber, body);
        return "SUCCESS";
    }

    @RequestMapping(value="/notify/jsb/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String jsbNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        LOGGER.info("[江苏银行通道支付回调], request: {}", body);
        Map<String, Object> notification = new HashMap<>();
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCCESS;
    }


    @RequestMapping(value="/notify/ztkx/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String ztkxNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        LOGGER.info("[中投科信支付回调], request: {}", body);
        Map<String, Object> notification = new HashMap<>();
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCC;
    }

    @RequestMapping(value = "/notify/lzccb/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String lzccbNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[泸州银行通道支付回调], request: {}", body);
        Map<String, Object> notification = new HashMap<>();
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value="/notify/weixinB2b", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public Map<String, Object> weixinB2bTradeNotify(@RequestBody Map<String, Object> body){

        LOGGER.info("[微信B2b支付回调], request: {}", JacksonUtil.toJsonString(body));
        String orderSn = MapUtil.getString(body, com.wosai.mpay.api.weixin.B2b.ResponseFields.OUT_TRADE_NO);
        service.notify(UpayUtil.getOrderIdBySn(orderSn), body);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, null));
    }

    @RequestMapping(value = "/notify/tls2p/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String tls2pNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[通联apple pay支付回调], request: {}", body);
        Map<String, Object> notification = null;
        try {
            notification = JsonUtil.jsonStringToObject(body, Map.class);
        } catch (MpayException e) {
            LOGGER.error("[通联apple pay支付回调解析失败], request: {}", body, e);
            return RESPONSE_SUCCESS_TL;
        }
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value = "/notify/yop/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain;charset=utf-8")
    @ResponseBody
    public String yopNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[易宝yop支付回调], request: {}", body);
        Map<String, Object> notification;
        try {
            notification = new HashMap<>(com.wosai.mpay.util.StringUtils.parseEncodedFormString(body));
        } catch (Exception e) {
            LOGGER.error("[易宝yop支付回调解析失败], request: {}", body, e);
            return RESPONSE_SUCCESS_TL;
        }
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCCESS_TL;
    }

    @RequestMapping(value = "/notify/pkxairport/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain;charset=utf-8")
    @ResponseBody
    public String pkxAirportNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[大兴机场支付回调], request: {}", body);
        Map<String, Object> notification;
        try {
            notification = JsonUtil.jsonStringToObject(body, Map.class);
        } catch (Exception e) {
            LOGGER.error("[大兴机场支付回调解析失败], request: {}", body, e);
            return RESPONSE_SUCCESS_UPPER_CASE;
        }
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCCESS_UPPER_CASE;
    }

    @RequestMapping(value = "/notify/macaupass/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain;charset=utf-8")
    @ResponseBody
    public String macaupassNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[澳门通支付回调], upayOrderNumber: {}, request: {}", upayOrderNumber, body);
        Map<String, Object> notification;
        try {
            notification = JsonUtil.jsonStringToObject(body, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            LOGGER.error("[澳门通支付回调解析失败], request: {}", body, e);
            return MacauPassTradeStatusEnum.SUCCESS.getCode();
        }
        service.notify(upayOrderNumber, notification);
        return MacauPassTradeStatusEnum.SUCCESS.getCode();
    }

    @Trace
    @RequestMapping(value = "/notify/guotong/{upayOrderNumber}/*", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Map<String, Object> guotongNotify(@RequestBody Map<String, Object> body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[国通支付回调], upayOrderNumber={},request: {}", upayOrderNumber, JacksonUtil.toJsonString(body));
        service.notify(upayOrderNumber, body);
        return ImmutableMap.of(GuotongResponseFields.RSP_COD, StringUtils.EMPTY,
                GuotongResponseFields.RSP_MSG, RESPONSE_SUCCESS);
    }


    @RequestMapping(value = "/notify/xzx/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "text/plain;charset=utf-8")
    @ResponseBody
    public String xzxNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[新中新支付回调], upayOrderNumber: {}, request: {}", upayOrderNumber, body);
        Map<String, Object> notification = new HashMap<>();
        notification.put(XZXMethodFieldsConstants.METHOD_PAYMENT_ORDER_NOTIFY, body);
        service.notify(upayOrderNumber, notification);
        return RESPONSE_SUCCESS_UPPER_CASE;
    }

    @RequestMapping(value = "/notify/psbc/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String psbcNotify(@RequestBody String body, @PathVariable String upayOrderNumber) {
        LOGGER.info("[邮储银行山西分行通道支付回调], request: {}", body);
        Map<String, Object> notification = new HashMap<>();
        service.notify(upayOrderNumber, notification);
        String response = null;
        try {
            response = psbcServiceProvider.makeNotifyResponseSuccessContent(body);
        } catch (Exception e) {
            LOGGER.error("makeNotifyResponseSuccessContent error: " + e.getMessage(), e);
        }
        LOGGER.info("[邮储银行山西分行通道支付回调], response: {}", response);
        return response;
    }
    @RequestMapping(value = "/notify/airwallex", method = RequestMethod.POST ,produces = "application/json;charset=utf-8")
    @ResponseBody
    public String airwallexNotify(HttpServletRequest request, @RequestBody String payload, HttpServletResponse response) {
        //处理逻辑 http状态码：200成功、其他失败
        LOGGER.info("[airwallex回调], request: {}", payload);
        String responseBody = "";
        Map<String, Object> notification;
        String orderSn;
        try{
            notification = JsonUtil.jsonStringToObject(payload, Map.class);
            orderSn = (String) BeanUtil.getNestedProperty(notification, "data.object.merchant_order_id");
        }catch (Exception e) {
            LOGGER.error("[airwallex回调解析失败], request: {}", payload, e);
            return responseBody;
        }
        service.notify(UpayUtil.getOrderIdBySn(orderSn), notification);
        return responseBody;
    }
    @RequestMapping(value="/notify/wecard/{upayOrderNumber}/*", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public void wecardNotify(@RequestBody String body, @PathVariable String upayOrderNumber){
        LOGGER.info("[腾讯微卡通道支付回调],  upayOrderNumber={},request: {}", upayOrderNumber, JacksonUtil.toJsonString(body));
        Map<String, Object> notification = new HashMap<>();
        service.notify(upayOrderNumber, notification);
    }

    @RequestMapping(value="/notify/trade/sybBank/{orderSn}", method = RequestMethod.POST, produces = MediaType.ALL_VALUE)
    @ResponseBody
    public String sybBankNotify(@RequestBody String body, @PathVariable String orderSn){
        LOGGER.info("[收银宝刷卡回调通知], request: {}", body);
        service.notify(UpayUtil.getOrderIdBySn(orderSn), new HashMap<>());
        return RESPONSE_SUCCESS;
    }
}

