package com.wosai.upay.controller;

import java.util.Collections;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.async.DeferredResult;

import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;

@Controller
@RequestMapping("/upay/v2")
public class UpayController 
{
    private static final Logger LOGGER = LoggerFactory.getLogger(UpayController.class);

    @Autowired
    private UpayService service;
    
    @RequestMapping(value="/echo", method=RequestMethod.GET, produces="application/json")
    @ResponseBody
    public Map<String, String> say() {
        return Collections.singletonMap("message", "Hi, Upay!");
    }

    @RequestMapping(value="/pay", method=RequestMethod.POST, produces="application/json")
    public DeferredResult<Map<String, Object>> pay(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        if(null != params) {
            params.remove(DepositService.IS_DEPOSIT);
        }
        return service.pay(params, response);
    }

    @RequestMapping(value="/multiplePay", method=RequestMethod.POST, produces="application/json")
    public DeferredResult<Map<String, Object>> multiplePay(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        return service.multiplePay(params, response);
    }

    @RequestMapping(value="/precreate", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> precreate(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        DeferredResult<Map<String,Object>> result = service.precreate(params, response);
        return result;
    }

    @RequestMapping(value="/query", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> query(@RequestBody Map<String, Object> params) {
        return service.query(params);
    }

    @RequestMapping(value="/cancel", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> cancel(@RequestBody Map<String, Object> params) {
        return service.cancel(params);
    }

    @RequestMapping(value="/revoke", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> revoke(@RequestBody Map<String, Object> params) {
        return service.revoke(params);
    }

    @RequestMapping(value="/reconcileRevoke", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> reconcileRevoke(@RequestBody Map<String, Object> params) {
        return service.reconcileRevoke(params);
    }

    @RequestMapping(value="/refund", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> refund(@RequestBody Map<String, Object> params) {
        return service.refund(params);
    }

    @RequestMapping(value="/coldTradeRefund", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> coldTradeRefund(@RequestBody Map<String, Object> params) {
        return service.coldTradeRefund(params);
    }

    @RequestMapping(value="/fix", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fix(@RequestBody Map<String, Object> params) {
        return service.fix(params);
    }

    @RequestMapping(value="/refundRevoke", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> refundRevoke(@RequestBody Map<String, Object> params) {
        return service.refundRevoke(params);
    }

    @RequestMapping(value="/fixCancelOrRefund", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixCancelOrRefund(@RequestBody Map<String, Object> params) {
        return service.fixCancelOrRefund(params);
    }

    @RequestMapping(value="/fixOrderStatusIfRefundNotSuccess", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixOrderStatusIfRefundNotSuccess(@RequestBody Map<String, Object> params) {
        return service.fixOrderStatusIfRefundNotSuccess(params);
    }

    @RequestMapping(value="/fixOrderStatusToPaidIfCancelNotSuccess", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> fixOrderStatusToPaidIfCancelNotSuccess(@RequestBody Map<String, Object> params) {
        return service.fixOrderStatusToPaidIfCancelNotSuccess(params);
    }

    @RequestMapping(value="/monitor" , produces = "application/json")
    @ResponseBody
    public Map<String,Object> monitor(){
        return service.monitor();
    }

    @RequestMapping(value="/updateFlags", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public void updateFlags(@RequestBody Map<String,Object> body){
        service.updateFlags(body);
    }

    @RequestMapping(value = "/preCreateQr", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> preCreateQr(@RequestBody Map<String,Object> params){
        return service.preCreateQr(params);
    }

    @RequestMapping(value = "/createFitnessOrder", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Map<String, Object> createFitnessOrder(@RequestBody Map<String,Object> params){
        return service.createFitnessOrder(params);
    }
}
