package com.wosai.upay.controller;

import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.service.SupportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by wkx
 * @date 2018/2/8
 **/
@Slf4j
@RestController
@RequestMapping("/upay/support")
public class SupportController {

    @Autowired
    private SupportService supportService;

    @RequestMapping(value = "/queryUserId", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String,Object> queryUserId(@RequestBody Map<String,Object>request){
        return supportService.queryUserId(request);
    }

    /**
     * 获取银联userId
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryUnionUserId", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String,Object> queryUnionUserId(@RequestBody Map<String,Object>request){
        return supportService.queryUnionUserId(request);
    }

    /**
     * 
     *清理商户缓存接口
     * 
     */
    @RequestMapping(value="/clearBasicCache", method = RequestMethod.POST)
    @ResponseBody
    public String clearBasicCache(@RequestBody Map<String,Object> body){
        supportService.clearBasicCache(body);
        return "success";
    }

    @RequestMapping(value = "/getWxpayfaceAuthinfo", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> getWxpayfaceAuthinfo(@RequestBody Map<String, Object> request){
        return supportService.getWxpayfaceAuthinfo(request);
    }

    @RequestMapping(value="/genSn", method=RequestMethod.POST, produces="application/json;charset=utf-8")
    @ResponseBody
    public Map<String, Object> genSn(@RequestBody Map<String, Object> params) {
        return supportService.genSn(params);
    }

    @RequestMapping(value="/changeTradeHbfq", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> changeTradeHbfq(@RequestBody Map<String, Object> params) {
        return supportService.changeTradeHbfq(params);
    }

    @RequestMapping(value="/changeTradeHbfqSubsidy", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> changeTradeHbfqSubsidy(@RequestBody Map<String, Object> params) {
        return supportService.changeTradeHbfqSubsidy(params);
    }

    @RequestMapping(value = "/auth" , method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> auth(@RequestBody Map<String, Object> request){
        return supportService.auth(request);
    }

    @RequestMapping(value = "/authQuery", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> authQuery(@RequestBody Map<String, Object> request){
        return supportService.authQuery(request);
    }

    @RequestMapping(value = "/authTerminate", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> authTerminate(@RequestBody Map<String, Object> request){
        return supportService.authTerminate(request);
    }

    /**
     * 
     * 修改分账服务费
     * @param params
     * @return
     */
    @RequestMapping(value="/changeTradeProfitSharing", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> changeTradeProfitSharing(@RequestBody Map<String, Object> params) {
        return supportService.changeTradeProfitSharing(params);
    }

    /**
     * 
     * 查询花呗分期数
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryHbfqNum", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> queryHbfqNum(@RequestBody Map<String, Object> request){
        return supportService.queryHbfqNum(request);
    }

    /**
     * 索迪斯-支付
     * 
     * @param body
     */
    @RequestMapping(value = "/sodexo/expenseByToken", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Map<String, Object> sodexoExpenseByToken(@RequestBody Map<String, Object> body) {
        return supportService.sodexoExpenseByToken(body);
    }

    /**
     * 索迪斯预下单核销
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/sodexo/verification", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Map<String, Object> sodexoVerification(@RequestBody Map<String, Object> body) {
        List<?> jsonRpcParams = (List<?>) MapUtils.getObject(body, "params");
        if (CollectionUtils.isEmpty(jsonRpcParams) || jsonRpcParams.size() != 2) {
            throw new UpayBizException("入参格式错误");
        }
        Map<String, Object> requestBody = (Map<String, Object>) jsonRpcParams.get(0);
        requestBody.put(SupportService.USER_ID, jsonRpcParams.get(1));
        return supportService.sodexoExpenseByToken(requestBody);
    }

    /**
     * 索迪斯-余额查询
     * 
     * @param body
     * @return
     */
    @RequestMapping(value = "/sodexo/balanceQuery", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Map<String, Object> sodexoBalanceQuery(@RequestBody Map<String, Object> body) {
        return supportService.sodexoBalanceQuery(body);
    }

    /**
     *　支付宝-花呗分期吱口令码申领
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/alipay/hbfq/createShareCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Map<String, Object> alipayHbfqCreateShareCode(@RequestBody Map<String, Object> body) {
        return supportService.getAlipayHbfqShareCode(body);
    }

    /**
     * 预授权补录备注/手机
     *
     * @param params
     * @return
     */
    @RequestMapping(value="/deposit/changeReflect", method=RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> depositChangeReflect(@RequestBody Map<String, Object> params) {
        return supportService.depositChangeReflect(params);
    }

    /**
     * 获取泰隆通道收钱吧订单号
     * @param body
     * @return
     */
    @RequestMapping(value="/zjtl/queryOrderSn", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String getTLOrderSn(@RequestBody String body){
        return supportService.getTnFromZJTLEncryptBody(body);

    }

    @RequestMapping(value="/changeTradeFee", method = RequestMethod.POST, produces="application/json")
    @ResponseBody
    public Map<String, Object> changeTradeFee(@RequestBody Map<String, Object> params){
        return supportService.changeTradeFee(params);
    }


    /**
     * 获取浦发通道收钱吧订单号
     * 浦发通道返回不对签名做校验：
     * 1.upay-dispatcher 不会对非常用签名透传
     * 只拿到body数据解析，在
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/pufa/queryOrderSn", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @ResponseBody
    public String getPufaOrderSn(@RequestBody String body) {
        log.info("浦发回调查询订单号");
        return supportService.getTnFromSPDBEncryptBody(body);
    }

    /**
     * 确认支付
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/confirmPay", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public DeferredResult<Map<String, Object>> confirmPay(@RequestBody Map<String, Object> request) {
        return supportService.confirmPay(request);
    }

}
