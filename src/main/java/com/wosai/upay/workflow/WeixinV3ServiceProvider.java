package com.wosai.upay.workflow;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinV3Client;
import com.wosai.mpay.api.weixin.v3.BusinessFields;
import com.wosai.mpay.api.weixin.v3.ProtocolFields;
import com.wosai.mpay.api.weixin.v3.ResponseFields;
import com.wosai.mpay.api.weixin.v3.WeixinConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.ProfitSharing;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.StoreAddressUtil;
import com.wosai.upay.util.UpayUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 继承该类的canHandle需要满足super.canHandle()
 */
public abstract class WeixinV3ServiceProvider extends AbstractServiceProvider {
    @Autowired
    protected WeixinV3Client weixinV3Client;
    protected int retryTimes = 3;


    protected static final String KEY_STORE_CLIENT_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_CLIENT_SN);
    protected static final String KEY_STORE_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_SN);
    protected static final String KEY_STORE_NAME = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_NAME);

    protected static final String KEY_STORE_AREA_CODE = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.DISTRICT_CODE);


    private static final Map<String, String> REPLACE_TAGS = MapUtil.hashMap(OP_QUERY, "{out_trade_no}",
            OP_REFUND_QUERY, "{out_refund_no}",
            OP_CLOSE, "{out_trade_no}",
            OP_CANCEL, "{out_trade_no}");

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if (tradeParams == null) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_WEIXIN && Objects.equals(TransactionParam.WEIXIN_VERSION_V3, BeanUtil.getNestedProperty(transaction, Transaction.KEY_SQB_WX_VERSION));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (subPayway == Order.SUB_PAYWAY_WAP) {
            return getTradeParams(transaction, TransactionParam.WEIXIN_WAP_TRADE_PARAMS);
        } else if (subPayway == Order.SUB_PAYWAY_MINI) {
            return getTradeParams(transaction, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
        }
        return null;
    }


    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);

        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (subPayway == SubPayway.WAP.getCode() || subPayway == SubPayway.MINI.getCode()) {
            RequestBuilder builder = new RequestBuilder();
            builder.set(ProtocolFields.SP_MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
            builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
            Map<String, Object> result;
            String url = getUrl(transaction, OP_CANCEL);
            try {
                result = retryIfNetworkException(url, WeixinV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_CANCEL);
            } catch (Exception ex) {
                logger.error("failed to call weixin cancel", ex);
                setTransactionContextErrorInfo(context, OP_CANCEL, ex);
                return Workflow.RC_IOEX;
            }
            if (result == null) {
                return Workflow.RC_IOEX;
            }

            int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);//http返回状态码
            String code = (String) result.get(ResponseFields.CODE);//返回状态码
            setTransactionContextErrorInfo(result, context, OP_CANCEL);
            if (com.wosai.pantheon.util.StringUtil.isNotEmpty(code)) {
                if (WeixinConstants.CODE_SYSTEM_ERROR.equals(code)
                        || WeixinConstants.CODE_INVALID_REQUEST.equals(code)) {
                    return Workflow.RC_RETRY;
                } else {
                    if (WeixinConstants.CODE_REVERSE_EXPIRE.equals(code)) {
                        return cancelToRefund(context);
                    }
                    return Workflow.RC_ERROR;
                }
            } else if (HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE != httpCode) {
                return Workflow.RC_ERROR;
            }
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            return Workflow.RC_CANCEL_SUCCESS;
        } else {
            throw new UnsupportedOperationException("暂不支持撤单");
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> result = doQuery(context);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = (String) result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        if (WeixinConstants.CODE_SYSTEM_ERROR.equals(code)) {
            //通讯标识为失败，重新查询
            return Workflow.RC_IN_PROG;
        } else if (com.wosai.pantheon.util.StringUtil.isNotEmpty(code)) {
            return Workflow.RC_ERROR;
        }
        String tradeState = (String) result.get(ResponseFields.TRADE_STATE);
        String rcFlag = Workflow.RC_ERROR;
        if (WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)) {
            rcFlag = Workflow.RC_IN_PROG;
        } else if (WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState)) {
            //跟微信的人确认过，b2c下，not_pay表明用户已经取消了付款。
            rcFlag = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
        } else if (WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
            rcFlag = Workflow.RC_PAY_SUCCESS;
            //付款成功
            if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                resolvePayFund(result, context);
            }
        }
        return rcFlag;
    }

    protected Map<String, Object> doQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        try {
            RequestBuilder builder = new RequestBuilder();
            builder.set(ProtocolFields.SP_MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
            builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
            return retryIfNetworkException(getUrl(transaction, OP_QUERY), WeixinV3Client.METHOD_GET, config, builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return null;
        }
    }


    @Override
    public String refund(TransactionContext context) {
        return doRefund(context, OP_REFUND);
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }


    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if ((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.set(key, value);
            }
        }
    }

    protected void setSceneInfo(RequestBuilder builder, Map<String, Object> transaction, Map<String, Object> extended) {
        //v3场景微信下需要传入 【用户终端IP】、【商户端设备号】 选填、【商户门店信息】选填
        Map sceneInfoBuilder = new HashMap();

        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);

        String clientIp = MapUtil.getString(transaction, Transaction.CLIENT_IP);
        if (org.apache.commons.lang3.StringUtils.isEmpty(clientIp)) {
            clientIp = UpayUtil.getLocalHostIp();
        }
        //【用户终端IP】
        sceneInfoBuilder.put(BusinessFields.SCENE_INFO_PAYER_CLIENT_IP, clientIp);

        //【商户端设备号】
        String terminalSn = MapUtil.getString(configSnapshot, TransactionParam.TERMINAL_SN);
        sceneInfoBuilder.put(BusinessFields.SCENE_INFO_DEVICE_ID, terminalSn);

        builder.set(BusinessFields.SCENE_INFO, sceneInfoBuilder);


        //兼容v2逻辑：handlerCustomizedSwitch的历史逻辑 【商户门店信息】
        Map<String, Object> storeInfoBuilder = getDefaultWithHandlerCustomizedSwitch(transaction);

        String storeId = BeanUtil.getPropString(transaction, KEY_STORE_CLIENT_SN);
        Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
        //非微信直连或者门店号为空不作处理
        if (!(provider == null) || StringUtils.isEmpty(storeId)) {
            return;
        }

        Object sceneInfoObj = BeanUtil.getProperty(extended, com.wosai.mpay.api.weixin.BusinessFields.SCENE_INFO);
        if (Objects.isNull(sceneInfoObj)) {
            sceneInfoObj = new HashMap<>();
        }
        if (sceneInfoObj instanceof String) {
            try {
                sceneInfoObj = JsonUtil.jsonStringToObject((String) sceneInfoObj, Map.class);
            } catch (Exception e) {
                logger.warn("微信场景参数字符串转JSON异常, 异常栈: ", e);
                return;
            }
        }

        Map storeInfo = MapUtil.getMap((Map) sceneInfoObj, BusinessFields.SCEND_INFO_STORE_INFO);

        boolean storeSceneSwitch = MapUtil.getBooleanValue(configSnapshot, Transaction.STORE_SCENE_SWITCH);
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if (storeSceneSwitch && subPayWay == Order.SUB_PAYWAY_BARCODE) {
            String sceneInfoId = MapUtil.getString(storeInfo, BusinessFields.SCENE_INFO_ID);
            if (Objects.isNull(sceneInfoId) || sceneInfoId.length() == 0) {
                String storeSn = BeanUtil.getPropString(transaction, KEY_STORE_SN);
                storeInfoBuilder.put(BusinessFields.SCENE_INFO_ID, storeSn);
            }

            String extendStoreName = MapUtil.getString(storeInfo, BusinessFields.SCENE_INFO_NAME);
            if (Objects.isNull(extendStoreName) || extendStoreName.length() == 0) {
                String storeName = BeanUtil.getPropString(transaction, KEY_STORE_NAME);
                if (Objects.nonNull(storeName)) {
                    storeInfoBuilder.put(BusinessFields.SCENE_INFO_NAME, storeName);
                }
            }

            String extendStoreAreaCode = MapUtil.getString(storeInfo, BusinessFields.SCENE_INFO_AREA_CODE);
            if (Objects.isNull(extendStoreAreaCode) || extendStoreAreaCode.length() == 0) {
                String storeAreaCode = BeanUtil.getPropString(transaction, KEY_STORE_AREA_CODE);
                if (Objects.nonNull(storeAreaCode) && StoreAddressUtil.loadStoreAddress().contains(storeAreaCode)) {
                    storeInfoBuilder.put(BusinessFields.SCENE_INFO_AREA_CODE, storeAreaCode);
                }
            }
            sceneInfoBuilder.put(BusinessFields.SCEND_INFO_STORE_INFO, storeInfoBuilder);
        } else {
            String sceneInfoId = MapUtil.getString(storeInfo, BusinessFields.SCENE_INFO_ID);
            if (Objects.isNull(sceneInfoId) || sceneInfoId.length() == 0) {
                sceneInfoBuilder.put(BusinessFields.SCEND_INFO_STORE_INFO, storeInfoBuilder);
            }
        }

    }

    /**
     * 设置【订单优惠标记】
     *
     * @param builder
     * @param context
     */
    protected void setGoodsTag(RequestBuilder builder, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        String goodsTag = BeanUtil.getPropString(config, TransactionParam.GOODS_TAG);
        if (StringUtils.empty(goodsTag)) {
            return;
        }
        builder.set(BusinessFields.GOODS_TAG, goodsTag);
    }

    /**
     * 设置【结算信息】
     * 1.分账参数
     *
     * @param transaction
     * @param builder
     */
    public void setSettleInfo(Map<String, Object> transaction, RequestBuilder builder) {
        Map settleInfo = new HashMap();
        Map<String, Object> extraParams = com.wosai.pantheon.util.MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> profitSharing = com.wosai.pantheon.util.MapUtil.getMap(extraParams, Transaction.PROFIT_SHARING);
        String flag = com.wosai.pantheon.util.MapUtil.getString(profitSharing, ProfitSharing.SHARING_FLAG);
        String sharingApp = com.wosai.pantheon.util.MapUtil.getString(profitSharing, Transaction.PROFIT_SHARING_SHARING_APP, Transaction.SHARING_APP_PAY);
        if (ProfitSharing.SHARING_FLAG_ENABLE.equals(flag) && Transaction.SHARING_APP_PAY.equals(sharingApp)) {
            settleInfo.put(BusinessFields.PROFIT_SHARING, true);
        }
        if (MapUtil.isNotEmpty(settleInfo)) {
            builder.set(BusinessFields.SETTLE_INFO, settleInfo);
        }
    }

    /**
     * 兼容业务逻辑：早期商户会配置在交易参数中scene的信息，如果打开后回默认使用商户自己的场景信息。
     * 如果透传参数里有传，会在setSceneInfo方法中以上送的信息进行覆盖
     *
     * @param transaction
     */
    private Map<String, Object> getDefaultWithHandlerCustomizedSwitch(Map transaction) {
        Map<String, Object> storeInfoBuilder = new HashMap<>();
        String clientStoreSn = (String) BeanUtil.getNestedProperty(transaction, Transaction.CONFIG_SNAPSHOT + "." + TransactionParam.STORE_CLIENT_SN);
        if (TransactionParam.USE_CLIENT_STORE_SN_YES == BeanUtil.getPropInt(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.USE_CLIENT_STORE_SN, TransactionParam.USE_CLIENT_STORE_SN_NO) && !StringUtil.empty(clientStoreSn)) {
            storeInfoBuilder = CollectionUtil.hashMap(com.wosai.mpay.api.weixin.BusinessFields.SCENE_INFO_ID, clientStoreSn);
        }
        return storeInfoBuilder;
    }

    protected Map<String, Object> retryIfNetworkException(String url, String method, Map<String, Object> config, Map<String, Object> request, int times, String logFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                String mchId = MapUtil.getString(config, TransactionParam.WEIXIN_MCH_ID);
                String serialNo = MapUtil.getString(config, TransactionParam.WEIXIN_SERIAL_NO);
                String signKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.WEIXIN_PRIVATE_KEY_V3));
                return weixinV3Client.call(url, method, mchId, serialNo, signKey, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in weixin {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    protected void setPayer(RequestBuilder builder, Map transaction) {
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        if (WeixinServiceProvider.isUserOfSubAppid(extended, config, MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY))) {
            builder.set(BusinessFields.PAYER, MapUtil.hashMap(BusinessFields.PAYER_SUB_OPENID, payerUid));
        } else {
            builder.set(BusinessFields.PAYER, MapUtil.hashMap(BusinessFields.PAYER_SP_OPENID, payerUid));
        }
    }

    protected String getNonceStr() {
        return new Random().nextLong() + "";
    }


    protected String getUrl(Map<String, Object> transaction, String op) {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), op);
        if (REPLACE_TAGS.containsKey(op)) {
            return url.replace(REPLACE_TAGS.get(op), MapUtil.getString(transaction, Transaction.ORDER_SN));
        }
        return url;
    }

    protected String getRefundUrl(Map<String, Object> transaction, String op) {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), op);
        if (REPLACE_TAGS.containsKey(op)) {
            return url.replace(REPLACE_TAGS.get(op), MapUtil.getString(transaction, Transaction.TSN));
        }
        return url;
    }


    private String cancelToRefund(TransactionContext context) {
        String resultRc = doRefund(context, OP_CANCEL);
        return Workflow.RC_REFUND_SUCCESS.equals(resultRc) ? Workflow.RC_CANCEL_SUCCESS : Workflow.RC_ERROR;
    }

    private String doRefund(TransactionContext context, String operation) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);

        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        }

        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));

        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(
                BusinessFields.AMOUNT_REFUND, transaction.get(Transaction.EFFECTIVE_AMOUNT),
                BusinessFields.AMOUNT_TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                BusinessFields.AMOUNT_CURRENCY, getTradeCurrency(transaction)
        ));
        carryOverExtendedParams((Map) transaction.get(Transaction.EXTENDED_PARAMS), builder, WeixinConstants.REFUND_ALLOWED_FIELDS);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction, OP_REFUND), WeixinV3Client.METHOD_POST, config, builder.build(), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call weixin refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常进行重试
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;
        }
        return processRefundAndQueryResult(result, context, OP_REFUND);
    }

    private String processRefundAndQueryResult(Map<String, Object> result, TransactionContext context, String operation) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, operation);
        String code = (String) result.get(ResponseFields.CODE);
        if (com.wosai.pantheon.util.StringUtil.isNotEmpty(code)) {
            if (WeixinConstants.CODE_SYSTEM_ERROR.equals(code) || WeixinConstants.CODE_FREQUENCY_LIMITED.equals(code)) {
                return Workflow.RC_RETRY;
            } else {
                return Workflow.RC_ERROR;
            }
        }

        String status = (String) result.get(ResponseFields.STATUS);
        if (Objects.equals(WeixinConstants.TRADE_STATE_SUCCESS, status)) {
            long channelFinishTime = parseTimeString(MapUtil.getString(result, ResponseFields.SUCCESS_TIME));
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
            context.getTransaction().put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ID));
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (Objects.equals(WeixinConstants.TRADE_STATE_PROCESSING, status)) {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result;
        try {
            result = doRefundQuery(context);
        } catch (Exception ex) {
            logger.error("failed to call weixin refund query", ex);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
        return processRefundAndQueryResult(result, context, OP_REFUND_QUERY);
    }

    public Map<String, Object> doRefundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        try {
            RequestBuilder builder = new RequestBuilder();
            builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
            return retryIfNetworkException(getRefundUrl(transaction, OP_REFUND_QUERY), WeixinV3Client.METHOD_GET, config, builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return null;
        }
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String code = (String) result.get(ResponseFields.CODE);//返回状态码
        String message = (String) result.get(ResponseFields.MESSAGE);//返回状态码
        Integer httpCode = MapUtil.getInteger(result, HttpConstant.HTTP_CODE);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        boolean isSuccess = com.wosai.pantheon.util.StringUtil.isEmpty(code) || (OP_CANCEL.equals(key) && httpCode == HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE);
        map.put(ResponseFields.CODE, isSuccess ? WeixinConstants.RESULT_CODE_SUCCESS : code);
        map.put(ResponseFields.MESSAGE, message);
        map.put(HttpConstant.HTTP_CODE, httpCode);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, code, message);
    }

    protected void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        String timeEnd = BeanUtil.getPropString(result, ResponseFields.TIME_END);
        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
            String subOpenId = BeanUtil.getPropString(result, ResponseFields.SUB_OPEN_ID);
            if (!StringUtil.empty(subOpenId)) {
                transaction.put(Transaction.BUYER_UID, subOpenId);
            }
        }
        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
            String openId = BeanUtil.getPropString(result, ResponseFields.OPEN_ID);
            if (!StringUtil.empty(openId)) {
                transaction.put(Transaction.BUYER_LOGIN, openId);
            }
        }
        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = BeanUtil.getPropString(result, ResponseFields.TRANSACTION_ID);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
        if (StringUtil.empty(BeanUtil.getPropString(extraOutFields, Transaction.WEIXIN_APPID))) {
            extraOutFields.put(Transaction.WEIXIN_APPID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
        }

        if (!StringUtil.empty(timeEnd)) {
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME))) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(timeEnd));
            }
        }
    }


    public static Map<String, Object> getWeixinPaymentByBanktype(String banktype, long amount) {
        if (amount <= 0 || StringUtil.empty(banktype)) {
            return null;
        } else {
            String type = null;
            if (DirectWeixinServiceProvider.WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE.equals(banktype)) {
                type = Payment.TYPE_WALLET_WEIXIN;
            } else if (banktype.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX)) {
                type = Payment.TYPE_BANKCARD_CREDIT;
            } else if (banktype.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX)) {
                type = Payment.TYPE_BANKCARD_DEBIT;
            } else if (!StringUtil.empty(banktype)) {
                type = banktype.toUpperCase();
            }
            return CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, type,
                    Transaction.PAYMENT_ORIGIN_TYPE, banktype,
                    Transaction.PAYMENT_AMOUNT, amount
            );
        }
    }


    public static void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (result == null) {
            return;
        }

        //amount 　选填 object 【订单金额】 订单金额信息。
        Map amountDetail = MapUtil.getMap(result, ResponseFields.AMOUNT);
        if (MapUtil.isEmpty(amountDetail) || !amountDetail.containsKey(ResponseFields.TOTAL)) {
            logger.warn("resolvePayFund amount detail empty");
            return;
        }
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);


        long totalFee = MapUtil.getLongValue(amountDetail, ResponseFields.TOTAL);
        long payerTotalFee = MapUtil.getLongValue(amountDetail, ResponseFields.PAYER_TOTAL);

        boolean useInstallFlag = false;
        //处理分期相关的数据
        Map installmentInfo = MapUtil.getMap(result, ResponseFields.INSTALLMENT_INFO);
        if (MapUtil.isNotEmpty(installmentInfo)) {
            extraOutFields.put(Transaction.WX_INSTALLMENT_INFO, installmentInfo);
            extraOutFields.put(Transaction.FQ_SPONSOR, 1);
            useInstallFlag = true;
        }


        //解析消费者实付
        if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0) {
            transaction.put(Transaction.PAID_AMOUNT, payerTotalFee);
        }

        //免充值金额的总和
        long noCashSumAmount = 0;
        //解析优惠金额
        List<Map<String, Object>> promotionDetails = (List<Map<String, Object>>) BeanUtil.getNestedProperty(result, ResponseFields.PROMOTION_DETAIL);
        if (CollectionUtils.isNotEmpty(promotionDetails)) {
            noCashSumAmount = promotionDetails.stream().filter(o -> Objects.equals(WeixinConstants.PROMOTION_DETAIL_TYPE_NOCASH, MapUtil.getString(o, ResponseFields.PROMOTION_DETAIL_TYPE))).mapToLong(o -> MapUtil.getLongValue(o, ResponseFields.PROMOTION_DETAIL_AMOUNT)).sum();
            long sumDiscountAmount = promotionDetails.stream().mapToLong(o -> MapUtil.getLongValue(o, ResponseFields.PROMOTION_DETAIL_AMOUNT)).sum();
            if (BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && sumDiscountAmount != 0) {
                order.put(Order.TOTAL_DISCOUNT, sumDiscountAmount);
                order.put(Order.NET_DISCOUNT, sumDiscountAmount);
            }
            extraOutFields.put(Transaction.VOUCHER_DETAILS, promotionDetails);
            // 代金券信息需要从优惠信息中获取
            List<Map<String, Object>> goodsDetails = promotionDetails
                    .stream()
                    .filter(promotion -> Objects.nonNull(promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL)) && promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL) instanceof List)
                    .map(promotion -> (List<Map<String, Object>>)promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            extraOutFields.put(Transaction.GOODS_DETAILS, goodsDetails);
        }

        //解析商家实收 = 交易总金额 - 免充值金额
        long receiveAmount = totalFee - noCashSumAmount;
        if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0) {
            transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
        }

        //解析payments
        if (payments == null || payments.isEmpty()) {
            List<Map<String, Object>> weixinPayments = getWeixinPayments(result);
            if (CollectionUtils.isNotEmpty(weixinPayments) && useInstallFlag) {
                //如果使用了分付 资金渠道 需要改成微信分付（微信默认返回的是OTHER）
                Optional<Map<String, Object>> useInstallPaymentOptional = weixinPayments.stream().filter(o -> Objects.equals(WeixinServiceProvider.WEIXIN_PAYMENT_OTHERS, MapUtil.getString(o, Transaction.PAYMENT_ORIGIN_TYPE))).findFirst();
                if (useInstallPaymentOptional.isPresent()) {
                    Map<String, Object> useInstallPayment = useInstallPaymentOptional.get();
                    useInstallPayment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_WEIXIN_INSTALL);
                }
            }
            extraOutFields.put(Transaction.PAYMENTS, weixinPayments);
        }
    }

    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> getWeixinPayments(Map<String, Object> result) {
        List<Map<String, Object>> payments = new ArrayList<>();

        Map amountDetail = MapUtil.getMap(result, ResponseFields.AMOUNT);
        long totalAmount = MapUtil.getLongValue(amountDetail, ResponseFields.TOTAL);
        String banktype = BeanUtil.getPropString(result, ResponseFields.BANK_TYPE);

        List<Map<String, Object>> promotionDetails = (List<Map<String, Object>>) BeanUtil.getNestedProperty(result, ResponseFields.PROMOTION_DETAIL);


        long nonDiscountFee = totalAmount;
        if (CollectionUtils.isNotEmpty(promotionDetails)) {
            long sumDiscountAmount = promotionDetails.stream().mapToLong(o -> MapUtil.getLongValue(o, ResponseFields.PROMOTION_DETAIL_AMOUNT)).sum();
            nonDiscountFee = totalAmount - sumDiscountAmount;
            for (Map<String, Object> promotionDetail : promotionDetails) {
                String type = BeanUtil.getPropString(promotionDetail, ResponseFields.PROMOTION_DETAIL_TYPE);
                String couponId = BeanUtil.getPropString(promotionDetail, ResponseFields.PROMOTION_DETAIL_COUPON_ID);

                int amount = BeanUtil.getPropInt(promotionDetail, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                int weixinContributeAmount = BeanUtil.getPropInt(promotionDetail, ResponseFields.PROMOTION_DETAIL_WECHATPAY_CONTRIBUTE_AMOUNT);
                int merchantContributeAmount = BeanUtil.getPropInt(promotionDetail, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE_AMOUNT);
                int otherContributeAmount = BeanUtil.getPropInt(promotionDetail, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE_AMOUNT);

                if (Objects.equals(WeixinConstants.PROMOTION_DETAIL_TYPE_NOCASH, type)) {
                    //非免充值 都属于通道补贴
                    String paymentType = Payment.TYPE_DISCOUNT_CHANNEL;
                    if (merchantContributeAmount > 0) {
                        paymentType = Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                    }
                    payments.add(
                            CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, paymentType,
                                    Transaction.PAYMENT_ORIGIN_TYPE, type,
                                    Transaction.PAYMENT_AMOUNT, amount,
                                    Transaction.PAYMENT_SOURCE, couponId
                            )
                    );
                } else if (Objects.equals(WeixinConstants.PROMOTION_DETAIL_TYPE_CASH, type)) {
                    //免充值优惠 区分商户出资的免充值和 通道出资的免充值
                    String paymentType = Payment.TYPE_DISCOUNT_CHANNEL;
                    if (merchantContributeAmount > 0) {
                        paymentType = Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP;
                    }
                    payments.add(
                            CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, paymentType,
                                    Transaction.PAYMENT_ORIGIN_TYPE, type,
                                    Transaction.PAYMENT_AMOUNT, amount,
                                    Transaction.PAYMENT_SOURCE, couponId
                            )
                    );
                }
            }
        }
        Map<String, Object> payment = getWeixinPaymentByBanktype(banktype, nonDiscountFee);
        payments.add(payment);
        return payments;
    }


    /**
     * 解析退款返回金额信息
     *
     * @param result
     * @param context
     */
    private void resolveRefundFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }

        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        } else {
            //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String, Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            Map amountDetail = MapUtil.getMap(result, ResponseFields.AMOUNT);
            //【用户退款金额】 指用户实际收到的现金退款金额，数据类型为整型，单位为分。例如在一个10元的订单中，用户使用了2元的全场代金券，若商户申请退款5元，则用户将收到4元的现金退款(即该字段所示金额)和1元的代金券退款。
            int payerRefund = MapUtil.getIntValue(amountDetail, ResponseFields.PAYER_REFUND);
            if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0 && payerRefund != 0) {
                transaction.put(Transaction.PAID_AMOUNT, payerRefund);
            }

            //【应结退款金额】 去掉免充值代金券退款金额后的退款金额，整型，单位为分，例如10元订单用户使用了2元全场代金券(一张免充值1元 + 一张预充值1元)，商户申请退款5元，则该金额为 退款金额5元 - 0.5元免充值代金券退款金额 = 4.5元。
            int settlementRefund = MapUtil.getIntValue(amountDetail, ResponseFields.SETTLEMENT_REFUND);
            if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefund != 0) {
                transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefund);
            }


            if (payments != null) {
                List<Map<String, Object>> refundPayments = new ArrayList<>();
                for (int i = 0; i < payments.size(); i++) {
                    Map<String, Object> refundPayment = (Map<String, Object>) ((HashMap) payments.get(i)).clone();
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                    refundPayments.add(refundPayment);
                }

                List<Map<String, Object>> promotionDetails = (List<Map<String, Object>>) MapUtil.getObject(result, ResponseFields.PROMOTION_DETAIL);
                boolean oriPaymentTypeSetFlag = false;
                if (CollectionUtils.isNotEmpty(promotionDetails)) {
                    extraOutFields.put(Transaction.VOUCHER_DETAILS, promotionDetails);
                    // 代金券信息需要从优惠信息中获取
                    List<Map<String, Object>> goodsDetails = promotionDetails
                            .stream()
                            .filter(promotion -> Objects.nonNull(promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL)) && promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL) instanceof List)
                            .map(promotion -> (List<Map<String, Object>>)promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL))
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    extraOutFields.put(Transaction.GOODS_DETAILS, goodsDetails);

                    //优惠券id -【优惠退款金额】 mapping
                    Map<String, Integer> promotionAmountMapping = promotionDetails.stream().collect(Collectors.toMap(o -> MapUtil.getString(o, ResponseFields.PROMOTION_DETAIL_PROMOTION_ID), o -> MapUtil.getIntValue(o, ResponseFields.REFUND_AMOUNT)));
                    for (Map<String, Object> refundPayment : refundPayments) {
                        String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                        if (!oriPaymentTypeSetFlag &&
                                (Payment.TYPE_WALLET_WEIXIN.equals(type) || Payment.TYPE_BANKCARD_CREDIT.equals(type) || Payment.TYPE_BANKCARD_DEBIT.equals(type)) || Payment.TYPE_OTHERS.equals(type) || Payment.TYPE_WEIXIN_INSTALL.equals(type)) {
                            refundPayment.put(Transaction.PAYMENT_AMOUNT, settlementRefund);
                            //只设置一次这种类型的payment
                            oriPaymentTypeSetFlag = true;
                        } else {
                            String sourceId = MapUtil.getString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                            if (promotionAmountMapping.containsKey(sourceId)) {
                                int refundPromotionAmount = MapUtil.getIntValue(promotionAmountMapping, sourceId);
                                refundPayment.put(Transaction.PAYMENT_AMOUNT, refundPromotionAmount);
                            }
                        }
                    }
                }

                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = MapUtil.getString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_WALLET_WEIXIN.equals(type)
                            || Payment.TYPE_BANKCARD_CREDIT.equals(type)
                            || Payment.TYPE_BANKCARD_DEBIT.equals(type)
                            || Payment.TYPE_OTHERS.equals(type)
                            || Payment.TYPE_WEIXIN_INSTALL.equals(type)){
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, settlementRefund);
                        break;
                    }
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                long settlementRefundFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = MapUtil.getString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                        settlementRefundFee = settlementRefundFee - MapUtil.getLongValue(refundPayment, Transaction.PAYMENT_AMOUNT);
                    }
                }
            }
        }
    }
}
