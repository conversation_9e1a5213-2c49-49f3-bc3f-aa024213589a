package com.wosai.upay.workflow;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.cmbapp.*;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 招行生活APP B2C适配器
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
public class CmbAppServiceProvider extends AbstractServiceProvider {

    private final Logger logger = LoggerFactory.getLogger(CmbAppServiceProvider.class);

    public static final String NAME = "provider.cmbapp";

    private static final DateTimeFormatter TRADE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 未知状态列表
     */
    private static final List<String> UNKNOWN_RESP_CODES = CmbAppConstants.UNKNOWN_RESP_CODES;

    /**
     * 订单完结状态列表
     */
    private static final List<String> FINISH_ORDER_STATUS = CmbAppConstants.FINISH_ORDER_STATUS;

    /**
     * 优惠字段映射  paymentType -> originType
     */
    private static final Map<String, List<String>> PAYMENT_TYPE_MAP = ImmutableMap.of(
            //商户优惠
            Payment.TYPE_DISCOUNT_CHANNEL_MCH, ImmutableList.of(
                    ResponseFields.COUPON_MER_SUBSIDY_AMT,
                    ResponseFields.FULL_DISCOUNT_MER_SUBSIDY_AMT,
                    ResponseFields.VOUCHER_MER_SUBSIDY_AMT),
            //通道优惠
            Payment.TYPE_DISCOUNT_CHANNEL, ImmutableList.of(
                    ResponseFields.COUPON_CMB_SUBSIDY_AMT,
                    ResponseFields.RANDOM_DISCOUNT_AMT,
                    ResponseFields.FULL_DISCOUNT_CMB_SUBSIDY_AMT,
                    ResponseFields.VOUCHER_CMB_SUBSIDY_AMT
            )
    );

    @Resource
    private CmbAppClient cmbAppClient;

    @Autowired
    protected ExternalServiceFacade serviceFacade;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_CMB_APP;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CMB_APP_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    /**
     * 支付
     */
    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String merPrivateKey = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_PRIVATE_KEY);
        String cmbPublicKey = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_CMB_PUBLIC_KEY);
        String cert = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_CERT);
        String url = getGatewayUrl(CmbAppConstants.FUNC_NAME_ODDER_PAY, OP_PAY);
        //构建请求request
        RequestBuilder requestBuilder = buildCommonParamMap(CmbAppConstants.FUNC_NAME_ODDER_PAY, tradeParams)
                .set(BusinessFields.MER_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_NO))
                .set(BusinessFields.STR_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_STR_NO))
                .set(BusinessFields.MER_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN))
                .set(BusinessFields.AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT))
                .set(BusinessFields.TAG_CODE, MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE))
              ;
        Map<String, Object> result;
        try {
            result = retryPayIfNetworkException(url,
                    merPrivateKey, cmbPublicKey, cert,
                    requestBuilder, NumberUtils.INTEGER_ONE, OP_PAY);
        } catch (Exception e) {
            logger.error("招行生活APP支付请求失败. requestStr:{}", jsonString(requestBuilder), e);
            //异常设置
            setTransactionContextErrorInfo(transaction, OP_PAY, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        //设置交易流水上下文
        setTransactionErrorContextInfo(result, context, OP_PAY);
        //处理结果
        return processPayResult(result, context);
    }

    /**
     * 撤单
     *
     * @param context 上下文
     * @return
     */
    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String merPrivateKey = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_PRIVATE_KEY);
        String url = getGatewayUrl(CmbAppConstants.FUNC_NAME_ORDER_CANCEL, OP_CANCEL);
        //构建请求request
        RequestBuilder requestBuilder = buildCommonParamMap(CmbAppConstants.FUNC_NAME_ORDER_CANCEL, tradeParams)
                .set(BusinessFields.MER_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_NO))
                .set(BusinessFields.STR_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_STR_NO))
                .set(BusinessFields.MER_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        Map<String, Object> result;
        try {
            result = retryCallIfNetworkException(url, merPrivateKey, requestBuilder, NumberUtils.INTEGER_ONE, OP_CANCEL);
        } catch (Exception e) {
            logger.error("招行生活APP撤单请求失败. requestStr:{}", jsonString(requestBuilder), e);
            //异常设置
            setTransactionContextErrorInfo(transaction, OP_CANCEL, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError)
                    ? Workflow.RC_RETRY : Workflow.RC_IOEX;
        }
        //设置交易流水上下文
        setTransactionErrorContextInfo(result, context, OP_CANCEL);
        //处理结果
        return processCancelResult(result, context);
    }

    /**
     * 查询订单信息
     *
     * @param context 上下文
     * @return
     */
    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_PRIVATE_KEY);
        RequestBuilder requestBuilder = buildCommonParamMap(CmbAppConstants.FUNC_NAME_ORDER_QUERY, tradeParams)
                .set(BusinessFields.MER_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_NO))
                .set(BusinessFields.STR_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_STR_NO))
                .set(BusinessFields.MER_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN))
                //.set(BusinessFields.CMB_ORDER_NO, MapUtil.getString(transaction, Transaction.TRADE_NO))
                ;
        final String gatewayUrl = getGatewayUrl(CmbAppConstants.FUNC_NAME_ORDER_QUERY, OP_QUERY);
        Map<String, Object> result;
        try {
            result = retryCallIfNetworkException(gatewayUrl, privateKey, requestBuilder, NumberUtils.INTEGER_ONE, OP_QUERY);
        } catch (Exception e) {
            logger.error("招行生活APP查询请求失败. requestStr:{}", jsonString(requestBuilder), e);
            setTransactionContextErrorInfo(transaction, OP_QUERY, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError)
                    ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //设置交易流水上下文
        setTransactionErrorContextInfo(result, context, OP_QUERY);
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        //接口调用成功
        if (Objects.equals(respCode, CmbAppConstants.RESP_CODE_SUCCESS)) {
            return processQuerySuccessResult(result, context);
        }
        //未知响应状态重试
        if (UNKNOWN_RESP_CODES.contains(respCode)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    /**
     * 退款
     *
     * @param context 上下文
     * @return
     */
    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String merPrivateKey = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_PRIVATE_KEY);
        String url = getGatewayUrl(CmbAppConstants.FUNC_NAME_ORDER_REFUND, OP_REFUND);
        // 构建请求request
        RequestBuilder requestBuilder = buildCommonParamMap(CmbAppConstants.FUNC_NAME_ORDER_REFUND, tradeParams)
                .set(BusinessFields.MER_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MER_NO))
                .set(BusinessFields.STR_NO, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_STR_NO))
                .set(BusinessFields.MER_REFUND_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN))
                .set(BusinessFields.MER_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN))
                .set(BusinessFields.TOTAL_AMT, MapUtil.getString(order, Order.EFFECTIVE_TOTAL))
                .set(BusinessFields.REFUND_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT))
                .set(BusinessFields.REFUND_DESC, "顾客退款");
        Map<String, Object> result;
        try {
            result = retryCallIfNetworkException(url, merPrivateKey, requestBuilder, NumberUtils.INTEGER_ONE, OP_REFUND);
        } catch (Exception e) {
            logger.error("招行生活APP退款请求失败. requestStr:{}", jsonString(requestBuilder), e);
            // 异常设置
            setTransactionContextErrorInfo(transaction, OP_REFUND, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError)
                    ? Workflow.RC_RETRY : Workflow.RC_IOEX;
        }
        //设置交易流水上下文
        setTransactionErrorContextInfo(result, context, OP_REFUND);
        //处理结果
        return processRefundResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    @Override
    public String getPrivateKeyContent(String rsaKeyId){
        return serviceFacade.getRsaKeyDataById(rsaKeyId);
    }

    // ================================== 可继承方法 ==================================
    protected void resolvePayFund(Map<String, Object> bodyMap, TransactionContext context) {
        Map<String, Object> transactionDto = context.getTransaction();
        //用户实付
        long paidAmt = MapUtil.getLongValue(bodyMap, ResponseFields.ACTUAL_AMT);
        //商家实收
        long cashFee = MapUtil.getLongValue(bodyMap, ResponseFields.RECEIVABLE_AMT);
        //交易完成时间
        long channelFinishTime = getTradeTimestamp(bodyMap);
        transactionDto.put(Transaction.PAID_AMOUNT, paidAmt);
        transactionDto.put(Transaction.RECEIVED_AMOUNT, cashFee);
        transactionDto.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
        List<Map<String, Object>> payments = buildCMBPayments(bodyMap);
        if (CollectionUtils.isNotEmpty(payments)) {
            BeanUtil.setNestedProperty(transactionDto, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        }
    }

    // ================================== 私有方法 ==================================
    private String getGatewayUrl(String funcName, String opKey) {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), opKey);
        return StringUtils.replace(url, "{funcName}", funcName);
    }

    private Map<String, Object> retryCallIfNetworkException(String url,
                                                            String privateKeyId,
                                                            RequestBuilder builder,
                                                            int times,
                                                            String opFlag) throws Exception {
        String privateKey = getPrivateKeyContent(privateKeyId);
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return cmbAppClient.call(url, privateKey, builder);
            } catch (Exception ex) {
                exception = ex;
                logger.warn("[retryCallIfNetworkException] 招行APP网关请求失败. opFlag:{}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private Map<String, Object> retryPayIfNetworkException(String url,
                                                           String merPrivateKeyId,
                                                           String cmbPublicKeyId,
                                                           String certId,
                                                           RequestBuilder builder,
                                                           int times,
                                                           String opFlag) throws Exception {
        String merPrivateKey = getPrivateKeyContent(merPrivateKeyId);
        String cmbPublicKey = getPrivateKeyContent(cmbPublicKeyId);
        String cert = getPrivateKeyContent(certId);
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return cmbAppClient.payCall(url, merPrivateKey, cmbPublicKey, cert, builder);
            } catch (Exception ex) {
                exception = ex;
                logger.warn("[retryPayIfNetworkException] 招行APP网关请求失败. opFlag:{}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    /**
     * 处理支付结果
     *
     * @param result  结果
     * @param context 交易上下文
     * @return
     */
    private String processPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transactionDto = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transactionDto.get(Transaction.EXTRA_OUT_FIELDS);
        if (Objects.isNull(extraOutFields)) {
            transactionDto.put(Transaction.EXTRA_OUT_FIELDS, Collections.emptyMap());
        }
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        //接口调用成功
        if (Objects.equals(respCode, CmbAppConstants.RESP_CODE_SUCCESS)) {
            return Workflow.RC_IN_PROG;
        }
        if (UNKNOWN_RESP_CODES.contains(respCode)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_TRADE_CANCELED;
    }

    /**
     * 处理撤单结果
     *
     * @param result  结果
     * @param context 交易上下文
     * @return
     */
    private String processCancelResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        //接口调用成功
        if (Objects.equals(respCode, CmbAppConstants.RESP_CODE_SUCCESS)) {
            Map bodyMap = MapUtil.getMap(result, ResponseFields.BODY);
            String reverseResult = MapUtil.getString(bodyMap, ResponseFields.REVERSE_RESULT);
            //撤单成功
            if (Objects.equals(CmbAppConstants.REVERSE_RESULT_SUCCESS, reverseResult)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
            //撤单失败
            else {
                String reverseResultDesc = MapUtil.getString(bodyMap, ResponseFields.REVERSE_RESULT_DESC);
                //设置业务报错信息
                setBizError(context, OP_CANCEL, reverseResult, reverseResultDesc);
                return Workflow.RC_ERROR;
            }
        }
        if (UNKNOWN_RESP_CODES.contains(respCode)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }
    /**
     * 处理退款结果
     *
     * @param result  结果
     * @param context 交易上下文
     * @return
     */
    private String processRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        //接口调用成功
        if (Objects.equals(respCode, CmbAppConstants.RESP_CODE_SUCCESS)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        if (UNKNOWN_RESP_CODES.contains(respCode)) {
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }

    protected void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context
                .getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder()
                , DaoConstants.CTIME)));
    }


    /**
     * 处理响应成功结果
     *
     * @param result
     * @param context
     * @return
     */
    private String processQuerySuccessResult(Map<String, Object> result, TransactionContext context) {
        Map bodyMap = MapUtil.getMap(result, ResponseFields.BODY);
        String orderStatus = MapUtil.getString(bodyMap, ResponseFields.ORDER_STATUS);
        if (!FINISH_ORDER_STATUS.contains(orderStatus)) {
            return Workflow.RC_IN_PROG;
        }
        Map<String, Object> transactionDto = context.getTransaction();
        transactionDto.put(Transaction.TRADE_NO, MapUtil.getString(bodyMap, BusinessFields.CMB_ORDER_NO));
        //支付结果设置
        if (Objects.equals(orderStatus, CmbAppConstants.ORDER_STATUS_PAY_FINISH)) {
            resolvePayFund(bodyMap, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        //兜底状态, 理论上是不会走到这里的
        logger.error("未知的状态. orderStatus:{}", orderStatus);
        return Workflow.RC_ERROR;
    }


    /**
     * 构建公共入参
     *
     * @param funcName    请求动作方法名
     * @param tradeParams 交易参数
     * @return
     */
    private RequestBuilder buildCommonParamMap(String funcName, Map<String, Object> tradeParams) {
        return new RequestBuilder(funcName)
                .set(BusinessFields.MID, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_MID))
                .set(BusinessFields.AID, MapUtil.getString(tradeParams, TransactionParam.CMB_APP_AID))
                .set(BusinessFields.DATE, LocalDateTime.now().format(TRADE_TIME_FORMATTER))
                .set(BusinessFields.RANDOM, RandomUtil.randomString(32));
    }

    private String jsonString(RequestBuilder requestBuilder) {
        String requestStr = StringUtils.EMPTY;
        try {
            requestStr = JsonUtil.objectToJsonString(requestBuilder);
        } catch (Exception ex) {
            logger.error("json error.", ex);
        }
        return requestStr;
    }

    /**
     * 设置交易流水上下文
     *
     * @param result  招行返回结果
     * @param context 上下文
     * @param opKey   操作选项
     */
    private void setTransactionErrorContextInfo(Map<String, Object> result, TransactionContext context, String opKey) {
        if (MapUtil.isEmpty(result)) {
            return;
        }
        String respCode = (String) result.get(ResponseFields.RESP_CODE);
        String respMsg = (String) result.get(ResponseFields.RESP_MSG);
        Map<String, Object> providerErrorInfoMap = Maps.newHashMap();
        providerErrorInfoMap.put(ResponseFields.RESP_CODE, respCode);
        providerErrorInfoMap.put(ResponseFields.RESP_MSG, respMsg);
        setTransactionContextErrorInfo(context.getTransaction(), opKey, providerErrorInfoMap, Objects.equals(respCode, CmbAppConstants.RESP_CODE_SUCCESS), respCode, respMsg);
    }

    private void setBizError(TransactionContext context, String opKey, String code, String msg) {
        String terminalCategory = MapUtil.getString(MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        boolean isMergeMessage = StringUtil.empty(terminalCategory);
        //设置biz_error_code
        UpayBizError bizError = UpayBizError.getBizErrorByField(opKey, BeanUtil.getPropInt(context.getTransaction(), Transaction.PAYWAY), code , msg);

        if (Objects.isNull(bizError) || UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(bizError.getStandardName())) {
            bizError = UpayBizError.unexpectedProviderError(StringUtil.empty(msg)
                    ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : msg, isMergeMessage);
        }
        Map<String, Object> transaction = context.getTransaction();
        // 设置转换后的收钱吧异常信息
        Map<String, Object> bizErrorCode = MapUtil.getMap(transaction, Transaction.BIZ_ERROR_CODE);
        if (bizErrorCode == null) {
            bizErrorCode = new HashMap<String, Object>();
            transaction.put(Transaction.BIZ_ERROR_CODE, bizErrorCode);
        }
        bizErrorCode.put(opKey, bizError);
    }

    private long getTradeTimestamp(Map<String, Object> bodyMap) {
        try {
            String tradeTime = MapUtil.getString(bodyMap, ResponseFields.ORDER_DATE);
            if (StringUtils.isEmpty(tradeTime)) {
                return System.currentTimeMillis();
            }
            return LocalDateTime.parse(tradeTime, TRADE_TIME_FORMATTER).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        } catch (Exception e) {
            return System.currentTimeMillis();
        }
    }

    private List<Map<String, Object>> buildCMBPayments(Map<String, Object> bodyMap) {
        //总金额
        long totalAmt = MapUtil.getLongValue(bodyMap, ResponseFields.TOTAL_AMT);
        //用户实付金额
        long actualAmt = MapUtil.getLongValue(bodyMap, ResponseFields.ACTUAL_AMT);
        //通道优惠
        long channelAmt = totalAmt - actualAmt;
        if (channelAmt <= 0) {
            return null;
        }
        List<Map<String, Object>> payments = Lists.newArrayList();
        for (Map.Entry<String, List<String>> entry : PAYMENT_TYPE_MAP.entrySet()) {
            String paymentType = entry.getKey();
            List<String> originTypes = entry.getValue();
            List<Map<String, Object>> payments0 = buildPayments(bodyMap, paymentType, originTypes);
            if (CollectionUtils.isNotEmpty(payments0)) {
                payments.addAll(payments0);
            }
        }
        return payments;
    }

    /**
     * 构建支付优惠
     *
     * @param bodyMap     招行响应body
     * @param paymentType 支付类型
     * @param originTypes 原始类型
     * @return
     */
    private List<Map<String, Object>> buildPayments(
            Map<String, Object> bodyMap, String paymentType, List<String> originTypes) {
        List<Map<String, Object>> payments = Lists.newArrayListWithCapacity(originTypes.size());
        for (String originType : originTypes) {
            long amt = MapUtil.getLongValue(bodyMap, originType);
            if (amt > 0L) {
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, paymentType,
                        Transaction.PAYMENT_ORIGIN_TYPE, originType,
                        Transaction.PAYMENT_AMOUNT, amt
                ));
            }
        }
        return payments;
    }

}
