package com.wosai.upay.workflow;

import java.util.Map;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.alipay.RequestV2Builder;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ServiceProvicerPriority(priority = 1)
public class DirectUnionPayAlipayV2ServiceProvider extends UnionPayAlipayV2ServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(DirectUnionPayAlipayV2ServiceProvider.class);
    public static final String NAME = "provider.direct.unionpay.alipay.v2";
    private static final String DEFAULT_CERT_ID = "**********";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_DIRECT_UNIONPAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(type == Transaction.TYPE_REFUND && (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE)) {
            return getTradeParams(transaction) != null && getTradeCurrency(transaction).equals(TransactionParam.UPAY_DEFAULT_CURRENCY_CNY);
        }
        return false;
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = new RequestV2Builder();

        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(config,TransactionParam.UNION_PAY_DIRECT_ALIPAY_APP_ID));
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, MapUtil.getString(config, TransactionParam.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA2));
        builder.set(ProtocolV2Fields.CERT_ID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_DIRECT_CERT_ID, DEFAULT_CERT_ID));
        String sysProviderId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_DIRECT_SYS_PROVIDER_ID);

        String alipaySubMchId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID);
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(
                BusinessV2Fields.SUB_MERCHANT_ID, alipaySubMchId,
                BusinessV2Fields.MERCHANT_NAME, getSubMerchantName(config, transaction)
        ));

        Map extraParam = CollectionUtil.hashMap(
                BusinessV2Fields.SECONDARY_MERCHANT_ID, alipaySubMchId
        );
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, extraParam);

        if(!StringUtil.empty(sysProviderId)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, sysProviderId
            ));
            builder.bizSet(BusinessV2Fields.ORG_PID, sysProviderId);
            extraParam.put(BusinessV2Fields.REQUEST_ORG_ID, sysProviderId);

            if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                builder.bizSet(BusinessV2Fields.REQUEST_ORG_PID, sysProviderId);
            }
        }

        setTerminalInfo(context, MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), builder);

        return builder;
    }

}
