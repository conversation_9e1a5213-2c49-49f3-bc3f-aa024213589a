package com.wosai.upay.workflow;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.bocom.*;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

/***
 * @ClassName: BOCOMServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/12/11 6:27 PM
 */
public class BOCOMServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(BOCOMServiceProvider.class);
    public static final String NAME = "provider.bocom";
    private static final SafeSimpleDateFormat DATE_SIMPLE_FORMAT = new SafeSimpleDateFormat(BOCOMConstants.DATE_SIMPLE_FORMAT);
    private static final SafeSimpleDateFormat DATE_TIME_SIMPLE_FORMAT = new SafeSimpleDateFormat(BOCOMConstants.DATE_TIME_SIMPLE_FORMAT);
    List<String> requireData = Lists.newArrayList(BOCOMConstants.BANK_TRAN_NO, BOCOMConstants.THIRD_PARTY, BOCOMConstants.THIRD_PARTY_TRAN_NO, BOCOMConstants.PAYMENT_INFO, BOCOMConstants.OPEN_ID, BOCOMConstants.SUB_OPEN_ID);
    List<Map<String, Object>> requires;

    @Resource
    protected BOCOMClient bocomClient;

    public BOCOMServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat(BOCOMConstants.DATE_TIME_FORMAT_SIMPLE);
        requires = buildRequires();
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_BOCOM;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return !Objects.isNull(getTradeParams(transaction));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.BOCOM_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PRIVATE_KEY);
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PUBLIC_KEY);

        BOCOMRequestBuilder builder = getDefaultRequestBuilder(context);

        builder.setReqBody(BOCOMBusinessFields.REQUIRE_FIELDS, requires);

        //币种
        builder.setReqBody(BOCOMBusinessFields.CURRENCY, BOCOMConstants.TRANSACTION_CURRENCY);
        //交易场景
        builder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);
        //ip
        String sqbIp = MapUtil.getString(extraParams, Transaction.SQB_IP);
        String clientIp = MapUtil.getString(extraParams, Transaction.CLIENT_IP);
        String ip = StringUtils.isEmpty(sqbIp) ? (StringUtils.isEmpty(clientIp) ? UpayUtil.getLocalHostIp() : clientIp) : sqbIp;
        builder.setReqBody(BOCOMBusinessFields.IP, ip);
        long orderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        //商户侧交易日期
        builder.setReqBody(BOCOMBusinessFields.MER_TRADE_DATE, DATE_SIMPLE_FORMAT.format(new Date(orderTime)));
        //商户侧交易时间
        builder.setReqBody(BOCOMBusinessFields.MER_TRADE_TIME, DATE_TIME_SIMPLE_FORMAT.format(new Date(orderTime)));
        //订单总金额（元）
        builder.setReqBody(BOCOMBusinessFields.TOTAL_AMOUNT, StringUtils.cents2yuan(MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.setReqBody(BOCOMBusinessFields.LOCATION, BOCOMConstants.OFFLINE);
        //条码
        builder.setReqBody(BOCOMBusinessFields.SCAN_CODE_TEXT, MapUtil.getString(extraParams, Transaction.BARCODE));
        //商户交易编号
        builder.setReqBody(BOCOMBusinessFields.PAY_MER_TRAN_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call bocom pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("交行通道暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PRIVATE_KEY);
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PUBLIC_KEY);

        BOCOMRequestBuilder builder = getDefaultRequestBuilder(context);

        builder.setReqBody(BOCOMBusinessFields.REQUIRE_FIELDS, requires);

        //交易场景
        builder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);
        //原交易商户侧交易日期
        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.setReqBody(BOCOMBusinessFields.MER_TRADE_DATE, DATE_SIMPLE_FORMAT.format(new Date(orgOrderTime)));
        //商户交易编号
        builder.setReqBody(BOCOMBusinessFields.PAY_MER_TRAN_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), 3, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call bocom query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            //连接错误，继续轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PRIVATE_KEY);
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PUBLIC_KEY);

        BOCOMRequestBuilder builder = getDefaultRequestBuilder(context);

        //币种
        builder.setReqBody(BOCOMBusinessFields.CURRENCY, BOCOMConstants.TRANSACTION_CURRENCY);
        //交易场景
        builder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);
        //原交易商户侧交易日期
        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.setReqBody(BOCOMBusinessFields.MER_TRADE_DATE, DATE_SIMPLE_FORMAT.format(new Date(orgOrderTime)));
        //商户交易编号
        builder.setReqBody(BOCOMBusinessFields.PAY_MER_TRAN_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));

        //商户退款的交易编号
        builder.setReqBody(BOCOMBusinessFields.REFUND_MER_TRAN_NO, MapUtil.getString(transaction, Transaction.TSN));
        long transactionTime = MapUtil.getLongValue(context.getTransaction(), DaoConstants.CTIME);
        //商户侧退款日期
        builder.setReqBody(BOCOMBusinessFields.MER_REFUND_DATE, DATE_SIMPLE_FORMAT.format(new Date(transactionTime)));
        //商户侧退款时间
        builder.setReqBody(BOCOMBusinessFields.MER_REFUND_TIME, DATE_TIME_SIMPLE_FORMAT.format(new Date(transactionTime)));
        //退款金额，单位元
        builder.setReqBody(BOCOMBusinessFields.AMOUNT, StringUtils.cents2yuan(MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT)) );

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call bocom refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常重试
            return Workflow.RC_RETRY;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PRIVATE_KEY);
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PUBLIC_KEY);

        BOCOMRequestBuilder builder = getDefaultRequestBuilder(context);

        //交易场景
        builder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);

        //商户退款的交易编号
        builder.setReqBody(BOCOMBusinessFields.REFUND_MER_TRAN_NO, MapUtil.getString(transaction, Transaction.TSN));
        //商户侧退款日期
        long orgTransactionTime = MapUtil.getLongValue(context.getTransaction(), DaoConstants.CTIME);
        builder.setReqBody(BOCOMBusinessFields.MER_REFUND_DATE, DATE_SIMPLE_FORMAT.format(new Date(orgTransactionTime)));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_QUERY), builder.build(), getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), 3, OP_REFUND_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call bocom refund query", ex);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
            //异常重试
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);

        return buildRefundQueryResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("交行通道暂不支持预下单");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);

        Map<String, Object> rspBody = MapUtil.getMap(result, BOCOMResponseFields.RSP_BODY);
        Map<String, Object> requireValues = MapUtil.getMap(rspBody, BOCOMResponseFields.REQUIRE_VALUES);
        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
                String openId = MapUtil.getString(requireValues, BOCOMResponseFields.OPEN_ID);
                if (!StringUtils.isEmpty(openId) && !"null".equals(openId)) {
                    transaction.put(Transaction.BUYER_UID, openId);
                }
            } else if (Order.PAYWAY_WEIXIN == payway) {
                String subOpenId = MapUtil.getString(requireValues, BOCOMResponseFields.SUB_OPENID);
                if (!StringUtils.isEmpty(subOpenId) && !"null".equals(subOpenId)) {
                    transaction.put(Transaction.BUYER_UID, subOpenId);
                }
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
                String subOpenId = MapUtil.getString(requireValues, BOCOMResponseFields.SUB_OPENID);
                if (!StringUtils.isEmpty(subOpenId) && !"null".equals(subOpenId)) {
                    transaction.put(Transaction.BUYER_LOGIN, subOpenId);
                }
            } else if (Order.PAYWAY_WEIXIN == payway) {
                String openId = MapUtil.getString(requireValues, BOCOMResponseFields.OPEN_ID);
                if (!StringUtils.isEmpty(openId) && !"null".equals(openId)) {
                    transaction.put(Transaction.BUYER_LOGIN, openId);
                }
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String sysOrderNo = MapUtil.getString(rspBody, BOCOMResponseFields.SYS_ORDER_NO); //交行内部订单号
            if (!StringUtils.isEmpty(sysOrderNo)) {
                transaction.put(Transaction.TRADE_NO, sysOrderNo);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String thirdPartyTranNo = MapUtil.getString(requireValues, BOCOMResponseFields.THIRD_PARTY_TRAN_NO); //第三方渠道交易流水号
            if (!StringUtils.isEmpty(thirdPartyTranNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, thirdPartyTranNo);
            }
        }
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> rspHead = MapUtil.getMap(result, BOCOMResponseFields.RSP_HEAD);
        String responseStatus = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_STATUS);//交易状态

        setTradeNoBuyerInfoIfExists(result, context);

        if (BOCOMConstants.RESPONSE_STATUS_SUCCESS.equals(responseStatus)) {
            //付款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        } else if (BOCOMConstants.RESPONSE_STATUS_PROCESS.equals(responseStatus)) {
            return Workflow.RC_IN_PROG;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Map<String, Object> rspHead = MapUtil.getMap(result, BOCOMResponseFields.RSP_HEAD);
        String responseCode = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_CODE);//返回码

        setTradeNoBuyerInfoIfExists(result, context);
        if (!BOCOMConstants.RESPONSE_CODE_SUCCESS.equals(responseCode)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> rspBody = MapUtil.getMap(result, BOCOMResponseFields.RSP_BODY);
        String orderStatus = MapUtil.getString(rspBody, BOCOMResponseFields.ORDER_STATUS);//订单状态
        switch (orderStatus) {
            //支付成功
            case BOCOMConstants.ORDER_STATUS_PAIED:
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            //交易已关闭
            case BOCOMConstants.ORDER_STATUS_REFUNDED:
            case BOCOMConstants.ORDER_STATUS_REFUNDALL:
            case BOCOMConstants.ORDER_STATUS_CLOSED:
                return Workflow.RC_TRADE_CANCELED;
            //需要轮训
            case BOCOMConstants.ORDER_STATUS_INITIAL:
            case BOCOMConstants.ORDER_STATUS_WAITPAY:
                return Workflow.RC_IN_PROG;
            default:
                return Workflow.RC_ERROR;
        }
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> rspHead = MapUtil.getMap(result, BOCOMResponseFields.RSP_HEAD);
        String responseCode = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_CODE);//返回码
        String responseStatus = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_STATUS);//交易状态

        if (BOCOMConstants.RESPONSE_STATUS_SUCCESS.equals(responseStatus)) {
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            Map<String, Object> rspBody = MapUtil.getMap(result, BOCOMResponseFields.RSP_BODY);
            context.getTransaction().put(Transaction.TRADE_NO, MapUtil.getString(rspBody, BOCOMResponseFields.REFUND_ORDER_NO));//商户通道退款订单号
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }else if (BOCOMConstants.RESPONSE_STATUS_PROCESS.equals(responseStatus)){
            //须进行退款查询，从而确定最终的退款情况
            return refundQuery(context);
        }

        return Workflow.RC_ERROR;
    }

    private String buildRefundQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> rspHead = MapUtil.getMap(result, BOCOMResponseFields.RSP_HEAD);
        String responseCode = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_CODE);//返回码

        if (!BOCOMConstants.RESPONSE_CODE_SUCCESS.equals(responseCode)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> rspBody = MapUtil.getMap(result, BOCOMResponseFields.RSP_BODY);
        String tranState = MapUtil.getString(rspBody, BOCOMResponseFields.TRAN_STATE);//流水状态

        if (BOCOMConstants.TRAN_STATE_SUCCESS.equals(tranState)) {
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }

        return Workflow.RC_ERROR;
    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments  = new ArrayList<>();
        }

        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        Map<String, Object> rspBody = MapUtil.getMap(result, BOCOMResponseFields.RSP_BODY);
        long buyerPayAmount = StringUtils.yuan2cents(MapUtil.getString(rspBody, BOCOMResponseFields.BUYER_PAY_AMOUNT)); //买家实付金额

        long discount = effectiveAmount - buyerPayAmount;

        //折扣金额大于0，记录优惠信息
        if (discount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, discount,
                    Transaction.PAYMENT_ORIGIN_TYPE, null,
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL));
        }

        transaction.put(Transaction.PAID_AMOUNT, buyerPayAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);

        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, buyerPayAmount,
                Transaction.PAYMENT_ORIGIN_TYPE, null,
                Transaction.PAYMENT_TYPE, Payment.TYPE_OTHERS));

        extraOutFields.put(Transaction.PAYMENTS, payments);

    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    /**
     * 公共请求参数
     *
     * @param context
     */
    private BOCOMRequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        BOCOMRequestBuilder requestBuilder = new BOCOMRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.BOCOM_APP_ID);
        String providerMchId = MapUtil.getString(tradeParams, TransactionParam.BOCOM_PROVIDER_MCH_ID);
        //开发者ID
        requestBuilder.bizSet(BOCOMProtocolFields.APP_ID, appId);
        requestBuilder.setReqHead(BOCOMBusinessFields.TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setReqHead(BOCOMBusinessFields.VERSION, BOCOMConstants.VERSION);

        //商户编号
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_PTC_ID, providerMchId);

        return requestBuilder;
    }

    private Map<String, Object> retryIfNetworkException(String serviceUrl, Map<String, Object> request, String privateKey, String publicKey, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {

            try {
                return bocomClient.call(serviceUrl, request, privateKey, publicKey);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in bocom {}", opFlag, ex);
            }

        }
        logger.error("still network i/o error after retrying {} times, op is {}", times, opFlag);

        throw exception;
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();

        String bizState = MapUtil.getString(result, BOCOMResponseFields.BIZ_STATE);
        String rspCode = MapUtil.getString(result, BOCOMResponseFields.RSP_CODE); //返回码
        String rspMsg = MapUtil.getString(result, BOCOMResponseFields.RSP_MSG); //逻辑异常返回信息

        Map<String, Object> rspHead = MapUtil.getMap(result, BOCOMResponseFields.RSP_HEAD);
        String responseCode = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_CODE);//返回码
        String responseMsg = MapUtil.getString(rspHead, BOCOMResponseFields.RESPONSE_MSG);//返回码描述

        map.put(BOCOMResponseFields.BIZ_STATE, bizState);//返回访问状态字
        if (!StringUtils.isEmpty(rspCode)) {
            map.put(BOCOMResponseFields.RSP_CODE, rspCode);//返回码
        }
        if (!StringUtils.isEmpty(rspMsg)) {
            map.put(BOCOMResponseFields.RSP_MSG, rspMsg);//逻辑异常返回信息
        }
        map.put(BOCOMResponseFields.RESPONSE_CODE, responseCode);//返回码
        map.put(BOCOMResponseFields.RESPONSE_MSG, responseMsg);//返回码描述

        boolean isSuccess;
        String providerCode;
        String providerMsg;
        if (!StringUtils.isEmpty(responseCode)) {
            isSuccess = BOCOMConstants.RESPONSE_CODE_SUCCESS.equals(responseCode);
            providerCode = responseCode;
            providerMsg = responseMsg;
        } else {
            isSuccess = BOCOMConstants.BIZ_STATE_SUCCESS.equals(bizState);
            providerCode = rspCode;
            providerMsg = rspMsg;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, providerCode, providerMsg);
    }

    private List<Map<String, Object>> buildRequires() {
        List<Map<String, Object>> requires = new ArrayList<>();
        requireData.forEach(item -> {
            Map<String, Object> require = new HashMap<>();
            require.put(BOCOMBusinessFields.REQUIRE_FIELD, item);
            requires.add(require);
        });

        return requires;
    }
}