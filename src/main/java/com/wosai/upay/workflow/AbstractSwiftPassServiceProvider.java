package com.wosai.upay.workflow;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.constant.UpayConstant;
import com.wosai.mpay.api.cibbank.*;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.QrcodeImaging;

/**
 * Created by jianfree on 17/6/16.
 */
abstract class AbstractSwiftPassServiceProvider extends AbstractServiceProvider {
    protected String notifyHost;
    protected  Logger logger = LoggerFactory.getLogger(AbstractSwiftPassServiceProvider.class);
    protected int retryTimes = 3;

    protected String SWIFT_ERR_CODE_SELLER_OVER_DAILY_LIMIT = "Auth valid fail";
    protected String SWIFT_ERR_CODE_REFUND_EXISTS = "Refund exists";
    protected String SWIFT_ERR_MSG_SELLER_OVER_DAILY_LIMIT = "消费超过该商户支持的限额";
    protected String SWIFT_ERR_CODE_FREQUENCY_LIMIT = "refund frequency high";
    protected String SWIFT_MESSAGE_INVALID_BARCODE = "条形码验证异常";
    protected String SWIFT_MESSAGE_ORDER_NOT_EXISTS_OR_OUT_OF_REFUND_TIME_LIMIT = "原交易不存在或超过退款限制时间。";

    protected long defaultTimeExpire = DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
    protected long b2cTimeExpire = B2C_TIME_EXPIRE_MINUTE * 60 * 1000;
    
    @Autowired
    protected CIBBankClient client;
    @Autowired
    protected QrcodeImaging qrcodeImaging;


    public AbstractSwiftPassServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_FEE, BusinessFields.REFUND_FEE));
    }


    public abstract  Map<String, Object> getTradeParams(Map<String, Object> transaction) ;

    public abstract RequestBuilder getDefaultRequestBuilder(String service, Map<String, Object> config);

    public abstract String getSignKey(Map<String,Object> config);

    public abstract String getRefundUserId(Map<String,Object> config);
    public abstract Integer getProvider(Map<String, Object> transaction);

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map config = getTradeParams(transaction);
        if(config != null && !config.isEmpty()){
            return true;
        }else{
            return false;
        }
    }


    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_MICROPAY, config);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.MCH_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        builder.set(BusinessFields.AUTH_CODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.DEVICE_INFO, BeanUtil.getPropString(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_SN, "0000"));
        builder.set(BusinessFields.ATTACH, getAttach(transaction));
        long start = System.currentTimeMillis();
        builder.set(BusinessFields.TIME_START, formatTimeString(start));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(start + b2cTimeExpire));
        limitCredit(builder,transaction);
        carryOverExtendedParams(extended, builder, BeanUtil.getPropInt(transaction, Transaction.PAYWAY), WeixinConstants.PAY_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            //重复提交付款请求，对方返回状态不明确，故网络错误不重试, 次数为1
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), getSignKey(config), builder.build(), 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call cibbank pay", ex);
            return Workflow.RC_IN_PROG;
        }
        if (result == null) {
            return Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setTradeNoBuyerInfoIfExists(result,context);
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        String payResult = BeanUtil.getPropString(result, ResponseFields.PAY_RESULT);
        String needQuery = BeanUtil.getPropString(result, ResponseFields.NEED_QUERY, CIBBankConstants.NEED_QUERY_YES);

        if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                && CIBBankConstants.PAY_RESULT_SUCCESS.equals(payResult)){
            //付款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String) result.get(ResponseFields.TIME_END)));
            resoveFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        }else if(CIBBankConstants.NEED_QUERY_YES.equals(needQuery)){
        	if(CIBBankConstants.MESSAGE_BESTPAY_MERCHANT_FREEZE.equals(message)) {
        		return Workflow.RC_TRADE_CANCELED;
        	}
            return Workflow.RC_IN_PROG;
        }else if(CIBBankConstants.PROTOCAL_ERR_CODE_LISTS.contains(errCode)){
            return Workflow.RC_TRADE_CANCELED;
        }else if(CIBBankConstants.FAIL_ERR_CODE_LISTS.contains(errCode)){
            return Workflow.RC_TRADE_CANCELED;
        }else{
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        Map result = reverse(context); //关闭订单或者冲正
        if(result == null){
            return Workflow.RC_RETRY;
        }
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //撤单成功
            return Workflow.RC_CANCEL_SUCCESS;
        }else{
        	setTransactionContextErrorInfo(result, context, OP_CANCEL);
        	
            //todo 如果威富通透传返回了错误码，就不用根据返回的中文错误信息来判断了，可删除下面的逻辑
            if(SWIFT_MESSAGE_ORDER_NOT_EXISTS_OR_OUT_OF_REFUND_TIME_LIMIT.equals(message) 
                    && BeanUtil.getPropLong(context.getTransaction(), Transaction.TYPE) == Transaction.TYPE_PAYMENT 
                    && BeanUtil.getPropInt(context.getTransaction(), Transaction.PAYWAY) == Order.PAYWAY_BESTPAY){
                return Workflow.RC_CANCEL_SUCCESS;
            }
            if(!StringUtils.empty(errCode)){
                if(CIBBankConstants.ERR_CODE_SWIFT_PASS_ORDER_NOT_EXISTS.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_WEIXIN_ORDER_NOT_EXIST.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_ALIPAY2_QUERY_TRADE_NOT_EXIST.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_QQ_TRADE_NOT_EXIST.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_BESTPAY_ORDER_NOT_EXISTS.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_QQ_TRADE_CLOSED_CANCEL_FAIL.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_QQ_TRADE_CLOSED.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_JD_ORDER_CLOSED.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_BESTPAY_ORDER_REFUNDED_OR_CANCELED.equals(errCode)
                        ||CIBBankConstants.ERR_CODE_BESTPAY_ORDER_CLOSED.equals(errCode)
                        ){
                    return Workflow.RC_CANCEL_SUCCESS;
                }else if(CIBBankConstants.PROTOCAL_ERR_CODE_LISTS.contains(errCode)){
                    return Workflow.RC_PROTOCOL_ERROR;
                }else if(CIBBankConstants.FAIL_ERR_CODE_LISTS.contains(errCode)){
                    return Workflow.RC_ERROR;
                }else{
                    //查单， 如果未支付则返回撤单成功，已支付，(如果是在支付流程中的撤单，返回失败, 等待勾兑对其做单边账处理， 如果是在退款或者撤单的流程，做退款处理)
                    //todo 在支付流程中发现订单是成功的，最好修改状态机，直接处理为成功，而不是依赖勾兑程序来处理。
                    result = doQuery(context, OP_CANCEL);
                    String queryStatus = BeanUtil.getPropString(result, ResponseFields.STATUS);
                    String queryResultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
                    String queryErrCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
                    String queryTradeState = BeanUtil.getPropString(result, ResponseFields.TRADE_STATE);
                    if(CIBBankConstants.STATUS_SUCCESS.equals(queryStatus)&& CIBBankConstants.RESULT_CODE_SUCCESS.equals(queryResultCode)){
                           if(CIBBankConstants.TRADE_STATE_CLOSED.equals(queryTradeState) 
                                   || CIBBankConstants.TRADE_STATE_REVOKED.equals(queryTradeState)
                                   ||CIBBankConstants.TRADE_STATE_NOTPAY.equals(queryTradeState)) {
                               return Workflow.RC_CANCEL_SUCCESS;
                           }else if(CIBBankConstants.TRADE_STATE_SUCCESS.equals(queryTradeState)){
                               int type = BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE);
                               if(type == Transaction.TYPE_PAYMENT){
                                   return Workflow.RC_ERROR;
                               }else{
                                   String refundFlag = doRefund(context, OP_CANCEL);
                                   if(Workflow.RC_REFUND_SUCCESS.equals(refundFlag)){
                                       return Workflow.RC_CANCEL_SUCCESS;
                                   }else{
                                       return Workflow.RC_RETRY;
                                   }
                               }

                           }else {
                               return Workflow.RC_RETRY;
                           }

                    }else {
                        if(CIBBankConstants.ERR_CODE_SWIFT_PASS_ORDER_NOT_EXISTS.equals(queryErrCode)
                                ||CIBBankConstants.ERR_CODE_WEIXIN_ORDER_NOT_EXIST.equals(queryErrCode)
                                ||CIBBankConstants.ERR_CODE_ALIPAY2_QUERY_TRADE_NOT_EXIST.equals(queryErrCode)
                                ||CIBBankConstants.ERR_CODE_QQ_TRADE_NOT_EXIST.equals(queryErrCode)
                                ||CIBBankConstants.ERR_CODE_JD_ORDER_NOT_EXISTS.equals(queryErrCode)
                                ||CIBBankConstants.ERR_CODE_JD_ORDER_NOT_EXISTS_TRADE.equals(queryErrCode)
                                ||CIBBankConstants.ERR_CODE_BESTPAY_ORDER_NOT_EXISTS.equals(queryErrCode)
                                ){
                            return Workflow.RC_CANCEL_SUCCESS;
                        }else {
                            return Workflow.RC_RETRY;
                        }
                    }
                }
            }else{
                return Workflow.RC_RETRY;
            }
        }
    }

    

    private Map reverse(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_MICROPAY_REVERSE, config);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), getSignKey(config), builder.build(), retryTimes, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call cibbank cancel", ex);
        }
        return result;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_QUERY, config);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        Map<String, Object> result = doQuery(context, OP_QUERY);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        String tradeState = BeanUtil.getPropString(result, ResponseFields.TRADE_STATE);
        if(!StringUtils.empty(errCode)){
            if(CIBBankConstants.ERR_CODE_SWIFT_PASS_ORDER_NOT_EXISTS.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_WEIXIN_ORDER_NOT_EXIST.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_ALIPAY2_QUERY_TRADE_NOT_EXIST.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_JD_ORDER_NOT_EXISTS.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_JD_ORDER_NOT_EXISTS_TRADE.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_BESTPAY_ORDER_NOT_EXISTS.equals(errCode)
                    ){
                int subPayway = BeanUtil.getPropInt(context.getOrder(), Order.SUB_PAYWAY);
                return (subPayway == Order.SUB_PAYWAY_BARCODE) ? Workflow.RC_IN_PROG : Workflow.RC_TRADE_NOT_EXISTS;
            }else if(CIBBankConstants.ERR_CODE_QQ_TRADE_NOT_EXIST.equals(errCode)){
                //qq b2c当需要用户输入密码的时候，会返回订单不存在的错误， 后续威富通才会修改。暂时对此错误做查单处理。 威富通修改后，再把逻辑改回。
                return Workflow.RC_IN_PROG;
            }else if(CIBBankConstants.PROTOCAL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }else if(CIBBankConstants.FAIL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_ERROR;
            }else{
                return Workflow.RC_IN_PROG;
            }
        }else if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                && CIBBankConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
            //付款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String) result.get(ResponseFields.TIME_END)));
            resoveFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        }else if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                && CIBBankConstants.TRADE_STATE_NOTPAY.equals(tradeState)
                && BeanUtil.getPropInt(context.getOrder(), Order.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE
                && BeanUtil.getPropInt(context.getOrder(), Order.PAYWAY) == Order.PAYWAY_WEIXIN){
            //微信如果b2c 返回为notpay 表明 用户已经取消了支付。
            return Workflow.RC_ERROR;
        }else{
            return Workflow.RC_IN_PROG;
        }
    }


    public Map<String,Object> doQuery(TransactionContext context, String opFlag){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_QUERY, config);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        try {
            return retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), getSignKey(config), builder.build(), retryTimes, opFlag);
        } catch (Exception ex) {
            logger.error("failed to call cibbank query", ex);
        }
        return null;
    }


    @Override
    public String refund(TransactionContext context) {
        return doRefund(context, OP_REFUND);
    }

    public String doRefund(TransactionContext context, String opFlag){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_REFUND, config);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.TOTAL_FEE, order.get(Order.EFFECTIVE_TOTAL) + "");
        builder.set(BusinessFields.REFUND_FEE, OP_REFUND.equals(opFlag) ? transaction.get(Transaction.EFFECTIVE_AMOUNT)+"" : order.get(Order.EFFECTIVE_TOTAL)+"");
        builder.set(BusinessFields.OP_USER_ID, getRefundUserId(config));
        carryOverExtendedParams(extended, builder, BeanUtil.getPropInt(transaction, Transaction.PAYWAY), WeixinConstants.REFUND_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), getSignKey(config), builder.build(), retryTimes, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call cibbank refund", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, opFlag);
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        if(!StringUtils.empty(errCode)){
            //todo 其他收款通道退款成功的错误码
            if(CIBBankConstants.ERR_CODE_WEIXIN_ORDERREFUND.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_JD_ORDER_REFUNDED.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_JD_ORDER_REFUNDED_TRADE.equals(errCode)
                    ||CIBBankConstants.ERR_CODE_BESTPAY_ORDER_REFUNDED_OR_CANCELED.equals(errCode)
                    ||SWIFT_ERR_CODE_REFUND_EXISTS.equals(errCode)
                    ){
                return Workflow.RC_REFUND_SUCCESS;
            }else if(SWIFT_ERR_CODE_FREQUENCY_LIMIT.equals(errCode)){
                return Workflow.RC_RETRY;
            }else if(CIBBankConstants.PROTOCAL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }else if(CIBBankConstants.FAIL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_ERROR;
            }else{
                return Workflow.RC_ERROR;
            }
        }else if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //退款成功
            transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ID));
            return Workflow.RC_REFUND_SUCCESS;
        }else {
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI){
            return wapPrecreate(context, resume);
        }else{
            return c2bPrecreate(context, resume);
        }
    }

    protected String c2bPrecreate(TransactionContext context, boolean resume){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String service = null;
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        if(Order.PAYWAY_WEIXIN == payway){
            service = CIBBankConstants.SERVICE_PAY_WEIXIN_NATIVE;
        }else if(Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway){
            service = CIBBankConstants.SERVICE_PAY_ALIPAY_NATIVE;
        }else if(Order.PAYWAY_QQWALLET == payway){
            service = CIBBankConstants.SERVICE_PAY_QQ_NATIVE;
        }else if(Order.PAYWAY_JD == payway){
            service = CIBBankConstants.SERVICE_PAY_JD_NATIVE;
        }
        RequestBuilder builder = getDefaultRequestBuilder(service, config);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.MCH_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.TIME_START, getOrderStartTime());
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(System.currentTimeMillis() + defaultTimeExpire));
        builder.set(BusinessFields.ATTACH, getAttach(transaction));
        limitCredit(builder,transaction);
        carryOverExtendedParams(extended, builder, BeanUtil.getPropInt(transaction, Transaction.PAYWAY), WeixinConstants.PRECREATE_ALLOWED_FIELDS);
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        String notifyUrl = getNotifyUrl(notifyHost,context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        Map<String,Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE), getSignKey(config), builder.build(), 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call cibbank precreate", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        if(!StringUtils.empty(errCode)){
            if(CIBBankConstants.PROTOCAL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }else if(CIBBankConstants.FAIL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_ERROR;
            }else{
                return Workflow.RC_ERROR;
            }
        }else if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //预下单成功
            if(context.getApiVer() == 1){
                String qrcode = BeanUtil.getPropString(result, ResponseFields.CODE_URL);
                if(payway == Order.PAYWAY_WEIXIN){
                    transaction.put(Transaction.PROVIDER_RESPONSE, CollectionUtil.hashMap(
                            PayResponse.CODE_URL,qrcode
                    ));
                }else if(payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2){
                    transaction.put(Transaction.PROVIDER_RESPONSE, CollectionUtil.hashMap(PayResponse.RESPONSE, CollectionUtil.hashMap(
                            PayResponse.ALIPAY,CollectionUtil.hashMap(
                                    PayResponse.QR_CODE, qrcode,
                                    PayResponse.PIC_URL, qrcodeImaging.getQrcodeImageUrl(qrcode)
                            )
                    )));
                }
            }
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.QRCODE, result.get(ResponseFields.CODE_URL));
            return Workflow.RC_CREATE_SUCCESS;
        }else {
            return Workflow.RC_ERROR;
        }

    }

    protected String wapPrecreate(TransactionContext context, boolean resume){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String service = null;
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        if(Order.PAYWAY_WEIXIN == payway){
            service = CIBBankConstants.SERVICE_PAY_WEIXIN_JSPAY;
        }else if(Order.PAYWAY_ALIPAY == payway|| Order.PAYWAY_ALIPAY2 == payway){
            service = CIBBankConstants.SERVICE_PAY_ALIPAY_JSPAY;
        }else if(Order.PAYWAY_QQWALLET == payway){
            service = CIBBankConstants.SERVICE_PAY_QQ_JSPAY;
        }else if(Order.PAYWAY_JD == payway){
            service = CIBBankConstants.SERVICE_PAY_JD_JSPAY;
        }else if(Order.PAYWAY_BESTPAY == payway){
            service = CIBBankConstants.SERVICE_PAY_BESTPAY_JSPAY;
        }
        
        RequestBuilder builder = getDefaultRequestBuilder(service, config);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.MCH_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.TIME_START, getOrderStartTime());
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(System.currentTimeMillis() + defaultTimeExpire));
        builder.set(BusinessFields.IS_RAW, CIBBankConstants.IS_RAW_YES);
        builder.set(BusinessFields.ATTACH, getAttach(transaction));
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        limitCredit(builder,transaction);
        carryOverExtendedParams(extended, builder, BeanUtil.getPropInt(transaction, Transaction.PAYWAY), WeixinConstants.PRECREATE_ALLOWED_FIELDS);

        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);

        if (payerUid != null) {
            if(Order.PAYWAY_WEIXIN == payway){
                builder.set(BusinessFields.SUB_OPENID, payerUid);
            }else if(Order.PAYWAY_ALIPAY == payway|| Order.PAYWAY_ALIPAY2 == payway){
                builder.set(BusinessFields.BUYER_ID, payerUid);
            }
        }
        String notifyUrl = getNotifyUrl(notifyHost,context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_MINI){
        	builder.set(BusinessFields.IS_MINIPG, 1);
        }

        if (Order.PAYWAY_WEIXIN == payway) {
            if (Order.SUB_PAYWAY_MINI == subPayway && !StringUtils.empty(BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID))) {
                builder.set(BusinessFields.SUB_APPID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID));
            } else {
                builder.set(BusinessFields.SUB_APPID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
            }
        }
        
        Map<String,Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAPORMINI), getSignKey(config), builder.build(), 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call cibbank wap create", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        if(!StringUtils.empty(errCode)){
            if(CIBBankConstants.PROTOCAL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }else if(CIBBankConstants.FAIL_ERR_CODE_LISTS.contains(errCode)){
                return Workflow.RC_ERROR;
            }else{
                return Workflow.RC_ERROR;
            }
        }else if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //预下单成功
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            Map payInfo = null;
            if(Order.PAYWAY_JD == payway){
                payInfo = CollectionUtil.hashMap(
                        PayResponse.PAY_URL, BeanUtil.getPropString(result, ResponseFields.PAY_URL)
                );
            }else {
                String payInfoString = BeanUtil.getPropString(result, ResponseFields.PAY_INFO);
                try {
                    payInfo = objectMapper.readValue(payInfoString.getBytes(CIBBankConstants.CHARSET_UTF8), Map.class);
                } catch (IOException e) {
                    return Workflow.RC_ERROR;
                }
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, payInfo);
            return Workflow.RC_CREATE_SUCCESS;

        }else {
            return Workflow.RC_ERROR;
        }

    }



    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        String status = BeanUtil.getPropString(providerNotification, ResponseFields.STATUS);
        if(!CIBBankConstants.STATUS_SUCCESS.equals(status)){
            return null;
        }
        //验证签名
        Map<String, Object> config = getTradeParams(transaction);
        boolean valid = false;
        try {
            String publicKey  = MapUtil.getString(config, TransactionParam.SWIFTPASS_PUBLIC_KEY);
            if(publicKey != null){
                //rsa签名验签
                String sign = MapUtil.getString(providerNotification, ProtocolFields.SIGN);
                String signType = MapUtil.getString(providerNotification, ProtocolFields.SIGN_TYPE);
                valid = RsaSignature.validateSign(providerNotification, ProtocolFields.SIGN, sign, CIBBankConstants.SIGN_TYPE_SHA256RSA.equals(signType) ? RsaSignature.SIG_ALG_NAME_SHA256_With_RSA : RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, getPrivateKeyContent(publicKey));
            }else{
                //md5签名验签
                valid = WeixinSignature.checkIsSignValidFromResponse(providerNotification, getSignKey(config), CIBBankConstants.CHARSET_UTF8);
            }
        } catch (MpayException e) {
            valid = false;
        }
        if(!valid){
            logger.error("cibbank notification sign not valid {}", providerNotification);
            return null;
        }
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        String notifyOrderSn = BeanUtil.getPropString(providerNotification, ResponseFields.OUT_TRADE_NO);
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        Long subPayway = BeanUtil.getPropLong(transaction, Transaction.SUB_PAYWAY);

        if(Transaction.TYPE_PAYMENT == type
                && (Order.SUB_PAYWAY_QRCODE == subPayway || Order.SUB_PAYWAY_WAP == subPayway || Order.SUB_PAYWAY_MINI == subPayway)
                && notifyOrderSn.equals(orderSn)){
            String resultCode = BeanUtil.getPropString(providerNotification, ResponseFields.RESULT_CODE);
            String payResult = BeanUtil.getPropString(providerNotification, ResponseFields.PAY_RESULT);
            if(CIBBankConstants.STATUS_SUCCESS.equals(status)
                    && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                    && CIBBankConstants.PAY_RESULT_SUCCESS.equals(payResult)
                    && orderSn.equals(BeanUtil.getPropString(providerNotification, ResponseFields.OUT_TRADE_NO))){
                setTradeNoBuyerInfoIfExists(providerNotification, context);
                resoveFund(context, providerNotification);
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String) providerNotification.get(ResponseFields.TIME_END)));
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return null;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, int payway, Set<String> weixinAllowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();
            if((payway == Order.PAYWAY_WEIXIN && weixinAllowedFields != null && weixinAllowedFields.size() > 0 && !weixinAllowedFields.contains(key)) || overFilterField(key)){
                continue;
            }
            if (value != null) {
                try {
                    //如果设置了单品优惠，由于威富通传值与直连微信不一致，这里需要特殊处理
                    if(UpayConstant.DETAIL.equals(key) && value instanceof Map && ((Map) value).get(UpayConstant.GOODS_DETAIL) != null){
                        builder.set(UpayConstant.GOODS_DETAIL, objectMapper.writeValueAsString(value));
                    }else{
                        builder.set(key, value instanceof String ? value : objectMapper.writeValueAsString(value));
                    }
                } catch (JsonProcessingException e) {
                    logger.error("process extend fields fail: " + e.getMessage(), e);
                }
            }
        }
    }

    protected void limitCredit(RequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction,Transaction.CONFIG_SNAPSHOT),TransactionParam.ALLOW_CREDIT_PAY,TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_CREDIT_PAY, CIBBankConstants.NO_CREDIT);
        }
    }

    protected Map<String, Object> retryIfNetworkException(String url, String key, Map<String,Object> request, int times, String logFlag) throws Exception {
        String signType = MapUtil.getString(request, ProtocolFields.SIGN_TYPE);
        if(CIBBankConstants.SIGN_TYPE_SHA1RSA.equals(signType) || CIBBankConstants.SIGN_TYPE_SHA256RSA.equals(signType)){
            //rsa 签名
            return retryIfNetworkException(()->client.call(url, serviceFacade.getRsaKeyDataById(key), request), logger, times, logFlag, "cibbank");
        }else{
            //md5签名
            return retryIfNetworkException(()->client.call(url, key, request), logger, times, logFlag, "cibbank");
        }
    }


    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String status = BeanUtil.getPropString(result, ResponseFields.STATUS);
        String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        String resultCode = BeanUtil.getPropString(result, ResponseFields.RESULT_CODE);
        String errCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        String errMsg = BeanUtil.getPropString(result, ResponseFields.ERR_MSG);
        Map map = CollectionUtil.hashMap(
                ResponseFields.STATUS, status, ResponseFields.MESSAGE, message,
                ResponseFields.RESULT_CODE, resultCode, ResponseFields.ERR_CODE, errCode,
                ResponseFields.ERR_MSG, errMsg
        );
        setTransactionContextErrorInfo(context.getTransaction(), key, map, 
                CIBBankConstants.STATUS_SUCCESS.equals(status) && CIBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode), errCode, StringUtils.empty(errMsg) ? message : errMsg);
    }

    /**
     * 设置订单号等信息
     * @param result
     * @param context
     */
    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        if(result == null || result.isEmpty()){
            return;
        }
        Map transaction = context.getTransaction();
        Map order = context.getOrder();
        int payway = MapUtil.getIntValue(order, Order.PAYWAY);
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String,Object> config = getTradeParams(transaction);
        String buyerUid = null;
        String buyerLogin = null;

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            if (Order.PAYWAY_WEIXIN == payway) {
                buyerUid = MapUtil.getString(result, ResponseFields.SUB_OPENID);
            } else if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
                buyerUid = MapUtil.getString(result, ResponseFields.BUYER_USER_ID);
            } else if (Order.PAYWAY_JD == payway) {
                buyerUid = MapUtil.getString(result, ResponseFields.OPENID);
            } else if (Order.PAYWAY_BESTPAY == payway) {
                buyerUid = MapUtil.getString(result, ResponseFields.OPENID);
            }

            if (!StringUtils.isEmpty(buyerUid)) {
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
                buyerLogin = MapUtil.getString(result, ResponseFields.BUYER_LOGON_ID);
            } else if (Order.PAYWAY_JD == payway) {
                buyerLogin = MapUtil.getString(result, buyerUid);
            } else if (Order.PAYWAY_BESTPAY == payway) {
                buyerLogin = MapUtil.getString(result, buyerUid);
            }

            if (!StringUtils.isEmpty(buyerLogin)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerLogin);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(result, ResponseFields.TRANSACTION_ID); //兴业银行订单号
            if (!StringUtils.isEmpty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String channelTradeNo = MapUtil.getString(result, ResponseFields.OUT_TRANSACTION_ID);//微信订单号
            if (!StringUtils.isEmpty(channelTradeNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
            }
        }

        if (payway == Order.PAYWAY_WEIXIN) {
            if (StringUtils.empty(MapUtil.getString(extraOutFields, Transaction.WEIXIN_APPID))) {
                extraOutFields.put(Transaction.WEIXIN_APPID, MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID));
            }
        }
    }


    public void resoveFund(TransactionContext context, Map<String,Object> result){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        int payway = BeanUtil.getPropInt(order, Order.PAYWAY);
        if(payway == Order.PAYWAY_ALIPAY2){
            DirectAlipayV2ServiceProvider.resolvePayFund(order, transaction, result);
        }else if(payway == Order.PAYWAY_WEIXIN){
            DirectWeixinServiceProvider.resolvePayFund(result, context);
        }

    }

    /**
     * 获取订单开始时间
     * @return
     */
    public static String getOrderStartTime(){
        return new SafeSimpleDateFormat(CIBBankConstants.DATE_TIME_FORMAT).format(new Date());
    }


    /**
     * 银行服务商以一个子标识码接入大客户模式下,需要将大客户下的商户信息在请求支付接口中的 attach 中传递过来,以满足业务管理和风控要求
     *
     * @param transaction
     * @return
     */
    public static String getAttach(Map<String,Object> transaction){
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        String merchantSn = BeanUtil.getPropString(configSnapshot, TransactionParam.MERCHANT_SN);
        String merchantName = BeanUtil.getPropString(configSnapshot, TransactionParam.MERCHANT_NAME);
        //(utf-8编码下，一个中文对应3到4个字节) attach最大长度128字节
        //"bank_mch_name=&bank_mch_id="为27个字节, bank_mch_id最大为20个字节,暂时预留10个字节 剩下71个字节，可用于bank_mch_name
        if(merchantName.getBytes().length <= 71){
        	return StringUtils.join(BusinessFields.ATTACH_BANK_MCH_NAME, "=", merchantName, "&", BusinessFields.ATTACH_BANK_MCH_ID, "=", merchantSn);

        }else{
            StringBuilder newMerchantName = new StringBuilder();
            long newMerchantNameByteLength = 0;
            for(String s: merchantName.split("")){
                int length = s.getBytes().length;
                if(newMerchantNameByteLength + length <= 71){
                    newMerchantName.append(s);
                    newMerchantNameByteLength = newMerchantNameByteLength + length;
                }else{
                    break;
                }
            }
            return StringUtils.join(BusinessFields.ATTACH_BANK_MCH_NAME, "=", newMerchantName.toString(), "&", BusinessFields.ATTACH_BANK_MCH_ID, "=", merchantSn);
        }
    }



    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }
}
