package com.wosai.upay.workflow;


import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.hopeedu.HopeEduClient;
import com.wosai.mpay.api.hopeedu.HopeEduRequestBuilder;
import com.wosai.mpay.api.hopeedu.constants.HopeEduProtocolFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduResponseFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduUrlPathConstants;
import com.wosai.mpay.api.hopeedu.enums.HopeEduResultCodeEnum;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.ExternalServiceException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING;
import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING_MESSAGE;
import static com.wosai.upay.util.ProviderUtil.updateMapIfResponseNotNull;


/**
 * @version 1.0
 * @author: yuhai
 * @program: upay-gateway
 * @className HopeEduServiceProvider
 * @description: 院校通Provider
 * @create: 2025-05-24 08:48
 **/
@Slf4j
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class HopeEduServiceProvider extends AbstractServiceProvider{
    protected static final Logger logger = LoggerFactory.getLogger(HopeEduServiceProvider.class);

    protected  static final String EMPTY_STR = "";

    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    @Resource
    private HopeEduClient hopeEduClient;

    public static final String NAME = "provider.hopeedu";

    private static final String ORDER_STATUS_PAY = "已支付";
    private static final String ORDER_STATUS_CANCEL = "未⽀付";


    public HopeEduServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(HopeEduRequestFieldsConstants.Body.ORDER_AMOUNT));
        dateFormat = new SafeSimpleDateFormat(HopeEduProtocolFieldsConstants.DATE_TIME_MESSAGE_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_HOPE_EDU;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {

        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.HOPE_EDU_TRADE_PARAMS);
    }

    /**
     * B扫C
     * @param context
     * @param resume
     * @return
     */
    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        String channelCode = getChannelCode(tradeParams);
        String urlPath = HopeEduUrlPathConstants.ORDER_CREATE;

        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        long amount = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String transAmount = com.wosai.mpay.util.StringUtils.cents2yuan(amount);

        String machineNumber = MapUtils.getString(configSnapshot, TransactionParam.TERMINAL_DEVICE_FINGERPRINT);

        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(channelCode);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_AMOUNT, transAmount);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_TYPE, HopeEduProtocolFieldsConstants.ORDER_TYPE_VALUE);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MACHINE_NUMBER, machineNumber);


        Map<String, Object> result;
        try {
            result = retryIfNetworkException(urlPath, requestBuilder.build(), tradeParams);
        } catch (Exception ex) {
            logger.error("failed to call hope edu order create", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_TRADE_CANCELED;
        }
        if (result == null) {
            return Workflow.RC_TRADE_CANCELED;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if(!requestIsSuccess(result)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        Map<String, Object> body = MapUtils.getMap(result, HopeEduResponseFieldsConstants.BODY);
        String orderId = updateTransactionCommonInfo(transaction, context.getOrder(), body);

        if (StringUtils.isEmpty(orderId)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        String payUrlPath = HopeEduUrlPathConstants.ORDER_PAY;

        String buyerIdentityCode = MapUtils.getString(extraParams, Transaction.BARCODE);

        HopeEduRequestBuilder payRequest = new HopeEduRequestBuilder(channelCode);
        payRequest.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_AMOUNT, transAmount);
        payRequest.setBodyField(HopeEduRequestFieldsConstants.Body.MACHINE_NUMBER, machineNumber);
        payRequest.setBodyField(HopeEduRequestFieldsConstants.Body.CODE_VALUE, buyerIdentityCode);
        payRequest.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_ID, orderId);

        Map<String, Object> payResult;
        try {
            payResult = retryIfNetworkException(payUrlPath, payRequest.build(), tradeParams);
        } catch (Exception ex) {
            logger.error("failed to call hope edu order pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (payResult == null) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(payResult, context, OP_PAY);
        if(!requestIsSuccess(payResult)) {
            return Workflow.RC_ERROR;
        }
        // success
        updateTransactionPaymentInfo(transaction, amount);
        return Workflow.RC_PAY_SUCCESS;
    }


    @Override
    public String cancel(TransactionContext context) {
        // not support
        throw new UnsupportedOperationException("院校通暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String orderId = MapUtils.getString(transaction, Transaction.TRADE_NO);

        String channelCode = getChannelCode(tradeParams);
        String urlPath = HopeEduUrlPathConstants.ORDER_QUERY;

        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(channelCode);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_ID, orderId);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(urlPath, requestBuilder.build(), tradeParams);
        } catch (Exception ex) {
            logger.error("failed to call hope edu query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if(!requestIsSuccess(result)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> body = MapUtils.getMap(result, HopeEduResponseFieldsConstants.BODY);

        String orderStatus = BeanUtil.getPropString(body, String.format("%s.%s", HopeEduResponseFieldsConstants.Body.DATA, HopeEduResponseFieldsConstants.Body.Data.ORDER_STATUS));

        if (ORDER_STATUS_PAY.equals(orderStatus)) {
            long amount = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            updateTransactionPaymentInfo(transaction, amount);
            return Workflow.RC_PAY_SUCCESS;
        } else if (ORDER_STATUS_CANCEL.equals(orderStatus)) {
            // 虽然未支付一直能支付，但如果b2c当时未支付成功无法再次支付，因此关单
            return Workflow.RC_TRADE_CANCELED;
        } else {
            return Workflow.RC_IN_PROG;
        }
    }


    @Override
    public String refund(TransactionContext context) {
        // not support
        throw new UnsupportedOperationException("院校通暂不支持退款");
    }

    /**
     * 请求院校通接口
     * @param service
     * @param request
     * @param tradeParams
     * @return
     */
    private Map<String, Object> retryIfNetworkException(String service, Map<String, Object> request, Map<String, Object> tradeParams) {
        String gatewayUrl = getGatewayUrl();
        String privateKey = getPrivateKey(tradeParams);
        String publicKey = getPublicKey(tradeParams);
        try {
            return new RetryUtil<Map<String, Object>>()
                    .retry(new RetryUtil.TimingStrategy.Builder().setRetry(2, 50, 1.0).build())
                    .method(() -> {
                        try {
                            return hopeEduClient.call(gatewayUrl, service, request, privateKey, publicKey);
                        } catch (MpayException | MpayApiNetworkError e) {
                            log.error("{}: 调用院校通接口异常: method={}, request={}, error={}",
                                    NAME, service, JacksonUtil.toJsonString(request), e.getMessage());
                            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
                        }
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull)
                    .execute();
        } catch (Exception e) {
            log.error("{}: 调用院校通接口异常: method={}, request={}, error={}",
                    NAME, service, JacksonUtil.toJsonString(request), e.getMessage());
            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }

    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        // not support
        throw new UnsupportedOperationException("院校通只支持B2C");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        // not support
        throw new UnsupportedOperationException("院校通暂不支持回调");
    }

    /**
     * 更新付款信息
     * @param transaction
     * @param amount
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, long amount){
        Map<String, Object> data = new HashMap<>();
        data.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        data.put(Transaction.PAID_AMOUNT, amount);

        // 更新支付完成时间
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, data, Transaction.CHANNEL_FINISH_TIME);

        // 支付金额
        updateMapIfResponseNotNull(transaction, Transaction.PAID_AMOUNT, data, Transaction.PAID_AMOUNT);

        List<Map<String,Object>> payments = new ArrayList<>();

        if(amount > 0){
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_HOPE_EDU,
                            Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_HOPE_EDU,
                            Transaction.PAYMENT_AMOUNT, amount
                    )
            );
        }
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }


    /**
     * 更新通用字段
     * @param transaction
     */
    protected String updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> order, Map<String, Object> body) {

        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> data = MapUtils.getMap(body, HopeEduResponseFieldsConstants.Body.DATA);
        String orderId = MapUtils.getString(data, HopeEduResponseFieldsConstants.Body.Data.ORDER_ID);

        if (!StringUtils.isEmpty(orderId)) {
            transaction.put(Transaction.TRADE_NO, orderId);
            extraOutFields.put(Transaction.TRADE_NO, orderId);
            order.put(Transaction.TRADE_NO, orderId);
        }
        return orderId;
    }


    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {

        String resultCode = BeanUtil.getPropString(result, String.format("%s.%s", HopeEduResponseFieldsConstants.BODY, HopeEduResponseFieldsConstants.Body.RESULT_CODE));
        String message = BeanUtil.getPropString(result, String.format("%s.%s", HopeEduResponseFieldsConstants.BODY, HopeEduResponseFieldsConstants.Body.MESSAGE));

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(HopeEduResponseFieldsConstants.Body.RESULT_CODE, resultCode);
        map.put(HopeEduResponseFieldsConstants.Body.MESSAGE, message);

        setTransactionContextErrorInfo(context.getTransaction(), key, map, HopeEduResultCodeEnum.isSuccess(resultCode),
                resultCode, message);
    }


    /**
     * 判断请求是否成功
     * @param result
     * @return
     */
    protected boolean requestIsSuccess(Map<String, Object> result) {
        String resultCode = BeanUtil.getPropString(result, String.format("%s.%s", HopeEduResponseFieldsConstants.BODY, HopeEduResponseFieldsConstants.Body.RESULT_CODE));
        return HopeEduResultCodeEnum.isSuccess(resultCode);
    }

    /**
     * 获取channel code
     * @param tradeParams
     * @return
     */
    protected String getChannelCode(Map<String, Object> tradeParams) {
        return MapUtils.getString(tradeParams, TransactionParam.HOPE_EDU_CHANNEL_CODE);
    }

    /**
     * 获取私钥
     * @param tradeParams
     * @return
     */
    protected String getPrivateKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.HOPE_EDU_PRIVATE_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    /**
     * 获取公钥
     * @param tradeParams
     * @return
     */
    protected String getPublicKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.HOPE_EDU_PUBLIC_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    /**
     * 获取网关地址
     * @return
     */
    protected  String getGatewayUrl() {
        return ApolloConfigurationCenterUtil.getProviderGateway(getName(), "");
    }
}
