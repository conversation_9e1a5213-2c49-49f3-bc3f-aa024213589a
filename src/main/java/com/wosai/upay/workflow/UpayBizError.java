package com.wosai.upay.workflow;

import java.util.HashMap;
import java.util.Map;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.service.SceneConfigFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

public class UpayBizError {
	private static Map<Integer, UpayBizError> registry = new HashMap<Integer, UpayBizError>();
	private static BiMap<Integer,String> errorCodeMap = HashBiMap.create();
    public static final UpayBizError INSUFFICIENT_FUND = new UpayBizError(1001, "INSUFFICIENT_FUND", "EP23", "账户金额不足");
    public static final UpayBizError EXPIRED_BARCODE = new UpayBizError(1002, "EXPIRED_BARCODE", "EP24", "过期的支付条码");
    public static final UpayBizError INVALID_BARCODE = new UpayBizError(1003, "INVALID_BARCODE", "EP25", "不合法的支付条码");
    public static final UpayBizError BUYER_OVER_DAILY_LIMIT = new UpayBizError(1004, "BUYER_OVER_DAILY_LIMIT", "EP26", "付款人当日付款金额超过上限");
    public static final UpayBizError BUYER_OVER_TRANSACTION_LIMIT = new UpayBizError(1005, "BUYER_OVER_TRANSACTION_LIMIT", "EP28", "付款人单笔付款金额超过上限");
    public static final UpayBizError SELLER_OVER_DAILY_LIMIT = new UpayBizError(1006, "SELLER_OVER_DAILY_LIMIT", "EP51", "收款账户当日收款金额超过上限");
    public static final UpayBizError TRADE_NOT_EXIST = new UpayBizError(1007,"TRADE_NOT_EXIST","EP50", "交易不存在");
    public static final UpayBizError TRADE_HAS_SUCCESS = new UpayBizError(1008,"TRADE_HAS_SUCCESS","", "交易已被支付");
    public static final UpayBizError SELLER_BALANCE_NOT_ENOUGH = new UpayBizError(1009, "SELLER_BALANCE_NOT_ENOUGH", "EP46","卖家余额不足");
    public static final UpayBizError REFUND_AMT_NOT_EQUAL_TOTAL = new UpayBizError(1010,"REFUND_AMT_NOT_EQUAL_TOTAL", "EP39","退款金额无效");
    public static final UpayBizError TRADE_FAILED = new UpayBizError(1011, "TRADE_FAILED", "EP60","交易失败");
    public static final UpayBizError TRADE_TIMEOUT = new UpayBizError(1012, "TRADE_TIMEOUT", "EP33","交易超时自动撤单");
    public static final UpayBizError AUTH_TOKEN_INVALID_OR_TIMEOUT = new UpayBizError(1013, "AUTH_TOKEN_INVALID_OR_TIMEOUT", "EP59","支付宝token不合法或已过期");
    public static final UpayBizError NO_AUTH = new UpayBizError(1014, "NO_AUTH", "EP31","没有权限执行此操作");
    public static final UpayBizError REFUND_FAILED = new UpayBizError(1015, "REFUND_FAILED", "EP52","退款失败");
    public static final UpayBizError BUYER_SELLER_EQUAL = new UpayBizError(1016, "BUYER_SELLER_EQUAL", "EP30","付款者不能为卖家账户"); //支付宝2.0才会有这个错误
	public static final UpayBizError PAYER_AUTH_AUTH = new UpayBizError(1017, "PAYER_AUTH_AUTH", "EP235","用户授权失效");
    public static final UpayBizError UNEXPECTED_PROVIDER_ERROR = new UpayBizError(9999, "UNEXPECTED_PROVIDER_ERROR", "EP99", "不认识的支付通道返回错误码");

    private int code;
    private String name;
    private String standardName;
    private String message;

    public UpayBizError(int code, String name, String standardName, String message) {
        this.code = code;
        this.name = name;
        this.standardName = standardName;
        this.message = message;
        if(!registry.containsKey(code)){
            registry.put(code, this);
        }
        if(!errorCodeMap.containsKey(code)){
        	errorCodeMap.put(code, standardName);
        }
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static UpayBizError fromMap(Map upayBizError){
    	UpayBizError bizError = null;
    	Integer code = BeanUtil.getPropInt(upayBizError, UpayConstant.CODE);
    	String errorCode = BeanUtil.getPropString(upayBizError, UpayConstant.ERROR_CODE);
    	if(StringUtil.empty(errorCode)){
    		bizError = new UpayBizError(code, BeanUtil.getPropString(upayBizError, UpayConstant.NAME),
    				BeanUtil.getPropString(upayBizError, UpayConstant.STANDARD_NAME),
    				BeanUtil.getPropString(upayBizError, UpayConstant.MESSAGE));

    	}else {
    		bizError = new UpayBizError(code,
    				errorCode,
    				BeanUtil.getPropString(upayBizError, CommonResponse.ERROR_CODE_STANDARD),
    				BeanUtil.getPropString(upayBizError, CommonResponse.ERROR_MESSAGE));
    	}

        return bizError;
    }

    private static UpayBizError fromApolloConf(Map upayBizError){
    	if(null != upayBizError){
	        String name = BeanUtil.getPropString(upayBizError, CommonResponse.ERROR_CODE);
	        String standardName = BeanUtil.getPropString(upayBizError, CommonResponse.ERROR_CODE_STANDARD);
	        String message = BeanUtil.getPropString(upayBizError, CommonResponse.ERROR_MESSAGE);
	        return new UpayBizError(null != errorCodeMap.inverse().get(standardName) ? errorCodeMap.inverse().get(standardName) : UNEXPECTED_PROVIDER_ERROR.getCode(),
	        		name, standardName, message);
    	}
    	return UNEXPECTED_PROVIDER_ERROR;
    }

    public static UpayBizError fromCode(Integer code){
		UpayBizError bizError = registry.get(code);
		if(null != bizError) {
			String sceneErrorCode = bizError.getStandardName();
			return fromApolloConf(SceneConfigFacade.getWosaiErrorDefinition(sceneErrorCode, null));
		}
		return fromApolloConf(SceneConfigFacade.getWosaiErrorDefinition(UNEXPECTED_PROVIDER_ERROR.getStandardName(), null));
    }



	public static UpayBizError getBizErrorByField(String model, int payway, String providerCode, String providerMessage){
		// 预下单和支付都配置的是pay
		if(MpayServiceProvider.OP_PRECREATE.equals(model)){
			model = MpayServiceProvider.OP_PAY;
		}
		Map info = SceneConfigFacade.getBizErrorInfo(model, payway, providerCode, providerMessage);
		if(null != info){
			if(StringUtil.empty(BeanUtil.getPropString(info, CommonResponse.ERROR_MESSAGE))){
				providerMessage = StringUtil.empty(providerMessage) ? UNEXPECTED_PROVIDER_ERROR.getMessage() : providerMessage;
				BeanUtil.setProperty(info, CommonResponse.ERROR_MESSAGE, providerMessage);
			}
			return fromApolloConf(info);
		}

		return fromApolloConf(SceneConfigFacade.getWosaiErrorDefinition(UNEXPECTED_PROVIDER_ERROR.getStandardName(), null));
	}

	public static UpayBizError unexpectedProviderError(String customMessage, boolean isMergeMessage){
		UpayBizError bizError = fromApolloConf(SceneConfigFacade.getWosaiErrorDefinition(UNEXPECTED_PROVIDER_ERROR.getStandardName(), null));

		if(StringUtil.empty(bizError.getMessage())){
    		bizError.setMessage(customMessage);
    	} else if (!StringUtil.empty(customMessage)) {
			//判断terminal_category是否为空，为空即代表非收钱吧侧的业务，减少影响范围；同时增加Apollo配置开关，用于灵活控制
			if (isMergeMessage && ApolloConfigurationCenterUtil.getIsEP99AppendProviderMessage()) {
				bizError.setMessage(bizError.getMessage() + ": " + customMessage);
			}
		}
    	return bizError;
    }

    @Override
    public String toString() {
        return String.format("%s:%s:%s:%s", getCode(), getName(), getStandardName(), getMessage());
    }

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getStandardName() {
		return standardName;
	}

	public void setStandardName(String standardName) {
		this.standardName = standardName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
