package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.xzx.XZXClient;
import com.wosai.mpay.api.xzx.XZXRequestBuilder;
import com.wosai.mpay.api.xzx.constants.XZXMethodFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXProtocolFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXRequestFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXResponseFieldsConstants;
import com.wosai.mpay.api.xzx.enums.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.ExternalServiceException;
import com.wosai.upay.exception.ProviderStatusException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.signature.model.request.XZXQueryAccessTokenReqest;
import com.wosai.upay.signature.model.response.XZXQueryAccessTokenResponse;
import com.wosai.upay.signature.service.XZXService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.constant.UpayConstant.ACCESS_TOKEN_EXPIRE_TIME_BUFFER;
import static com.wosai.constant.UpayConstant.STANDARD_DATE_FORMAT;
import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING;
import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING_MESSAGE;
import static com.wosai.mpay.api.jycard.JyCardProtocolFields.*;
import static com.wosai.upay.util.ProviderUtil.updateMapIfResponseNotNull;

/**
 * @version 1.0
 * @author: yuhai
 * @program: upay-gateway
 * @className XZXServiceProvider
 * @description: 新中新 service provider
 * @create: 2025-04-24 19:41
 **/

@Slf4j
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class XZXServiceProvider extends AbstractServiceProvider {
    public static final String NAME = "provider.xzx";
    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    // 新中新访问令牌缓存key
    private static final String XZX_ACCESS_TOKEN_CACHE_KEY = "xzx_access_token:";

    @Resource
    private StringRedisTemplate redisTemplate;

    @Resource
    private XZXClient xzxClient;

    @Resource
    private XZXService xzxService;

    @Resource
    protected DataRepository repository;

    public XZXServiceProvider() {
        dateFormat = new SafeSimpleDateFormat(UpayConstant.SHORT_DATETIME_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_XZX;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payWay = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_WEIXIN != payWay) {
            // 新中新支持微信小程序
            return false;
        }
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.XZX_TRADE_PARAMS);
    }

    protected Map<String, Object> getExtraOutFields(Map<String, Object> transaction) {
        Map<String,Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null || extraOutFields.isEmpty()) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        return extraOutFields;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        // not support 仅支持微信小程序支付
        throw new UnsupportedOperationException("新中新仅支持微信小程序支付");
    }

    @Override
    public String cancel(TransactionContext context) {
        // not support
        throw new UnsupportedOperationException("新中新暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        try {
            String transactionId = MapUtils.getString(transaction, Transaction.TRADE_NO);
            if (StringUtils.isAllBlank(transactionId)) {
                Exception e = new UpayBizException(UpayErrorScenesConstant.FIX_CONSUME_QUERY_ERROR, UpayErrorScenesConstant.FIX_CONSUME_QUERY_ERROR_MESSAGE);
                setTransactionContextErrorInfo(transaction, OP_QUERY, e);
                throw e;
            }

            Map<String, Object> result = doQueryOrder(context, transactionId);
            if (!checkCallSucceed(result)) {
                setTransactionContextErrorInfo(transaction, OP_QUERY, result, false, getName(), "查询新中新订单失败");
                return Workflow.RC_ERROR;
            } else {
                setSuccessTransactionContextErrorInfo(transaction, result, OP_QUERY);
            }

            Map<String, Object> objResult = MapUtils.getMap(result, XZXResponseFieldsConstants.OBJ);
            String bizStatus = MapUtils.getString(objResult, XZXResponseFieldsConstants.BIZ_STATUS, "");

            if (XZXBizStatusEnum.SUCCEED.getCode().equals(bizStatus)) {
                updateTransactionPaymentInfo(transaction, objResult);

                if (Transaction.TYPE_REFUND == MapUtils.getIntValue(transaction, Transaction.TYPE)) {
                    // 退款订单
                    return Workflow.RC_REFUND_SUCCESS;
                } else {
                    return Workflow.RC_PAY_SUCCESS;
                }
            } else if (XZXBizStatusEnum.FAILED.getCode().equals(bizStatus)){
                return Workflow.RC_TRADE_CANCELED;
            } else if (XZXBizStatusEnum.IN_TRADING.getCode().equals(bizStatus)){
                if (Transaction.TYPE_REFUND == MapUtils.getIntValue(transaction, Transaction.TYPE)) {
                    // 退款订单
                    return Workflow.RC_RETRY;
                } else {
                    return Workflow.RC_IN_PROG;
                }
            }

            return Workflow.RC_IOEX;
        }catch (UpayBizException e){
            setTransactionContextErrorInfo(transaction, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }catch (Exception e){
            setTransactionContextErrorInfo(transaction, OP_QUERY, e);
            log.error("{}: 新中新 query 失败, tsn={} error={} ", NAME, tsn, e.getMessage(), e);
        }

        return Workflow.RC_IOEX;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = getExtraOutFields(transaction);
        boolean onlyRefundQuery = MapUtils.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        String result = "";
        if(onlyRefundQuery){
            result = query(context);
        }else{
            result = doRefund(context);
        }
        return result;

    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        try{
            // 先下单
            Map<String, Object> applyResult = doApply(context);
            if (!checkCallSucceed(applyResult)) {
                setTransactionContextErrorInfo(transaction, OP_PRECREATE, applyResult, false, getName(), "新中新商户下单请求失败");
                return Workflow.RC_ERROR;
            }

            Map objResult = MapUtils.getMap(applyResult, XZXResponseFieldsConstants.OBJ);
            String prepayId = MapUtils.getString(objResult, XZXResponseFieldsConstants.PREPAY_ID);

            // 然后调收银台
            Map<String, Object> cashierResult = doCashier(context, prepayId);
            if (!checkCallSucceed(cashierResult)) {
                setTransactionContextErrorInfo(transaction, OP_PRECREATE, cashierResult, false, getName(), "新中新调起收银台请求失败");
                return Workflow.RC_ERROR;
            } else {
                setSuccessTransactionContextErrorInfo(transaction, cashierResult, OP_PRECREATE);
            }

            Map cashierObjResult = MapUtils.getMap(cashierResult, XZXResponseFieldsConstants.OBJ);

            updateTransactionCommonInfo(transaction, context.getOrder(), cashierObjResult);

            Map<String, Object> extendParams = MapUtils.getMap(cashierObjResult, XZXResponseFieldsConstants.EXTEND_PARAMS);

            //需要返回给前端小程序的参数, 都放入miniPayRequest中
            if(extendParams != null && !extendParams.isEmpty()){
                Map<String,Object> extraOutFields = getExtraOutFields(transaction);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, extendParams);

                extraOutFields.put(Transaction.PREPAY_ID, prepayId);
                String transactionId = MapUtils.getString(cashierObjResult, XZXResponseFieldsConstants.TRANSACTION_ID);
                extraOutFields.put(Transaction.TRANSACTION_ID, transactionId);

                return Workflow.RC_CREATE_SUCCESS;
            } else {
                setTransactionContextErrorInfo(transaction, OP_PRECREATE, cashierResult, false, getName(), "新中新商户下单请求失败");
                return Workflow.RC_ERROR;
            }
        }catch (UpayBizException e){
            setTransactionContextErrorInfo(transaction, OP_PRECREATE, e);
            return Workflow.RC_ERROR;
        }catch (Exception e){
            log.error("{}: 新中新 precreate 失败, tsn={} error={} ", NAME, tsn, e.getMessage(), e);
            setTransactionContextErrorInfo(transaction, OP_PRECREATE, e);
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT){
            return null;
        }
        String status = query(context);
        if (Workflow.RC_PAY_SUCCESS.equals(status)) {
            return status;
        }
        return null;
    }



    /**
     * doApply 下单
     * @param context
     * @return
     */
    protected Map<String,Object> doApply(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        XZXRequestBuilder xzxRequestBuilder = buildSystemLevelRequestParams(tradeParams);
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessTokenFromCache(transaction));

        String method = XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_APPLY;

        Date now = new Date();
        Date timeOut = new Date(now.getTime() + 1000 * 60 * DEFAULT_TIME_EXPIRE_MINUTE);
        String nowStr = DateUtil.formatDate(now, UpayConstant.SHORT_DATETIME_FORMAT);
        String timeOutStr = DateUtil.formatDate(timeOut, UpayConstant.SHORT_DATETIME_FORMAT);
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String body = MapUtils.getString(transaction, Transaction.SUBJECT);
        long tranAmt = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String openId = MapUtils.getString(extraParams, Transaction.PAYER_UID);

//        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put(XZXRequestFieldsConstants.OPEN_ID, openId);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.MCH_ACCT_ID, getMchAcctId(tradeParams));
        params.put(XZXRequestFieldsConstants.DEV_ID, XZXDevIdEnum.DEV_ID_MINI_PROGRAM.getCode());
        params.put(XZXRequestFieldsConstants.OUT_TRADE_NO, tsn);
        params.put(XZXRequestFieldsConstants.TRAN_DT, nowStr);
        params.put(XZXRequestFieldsConstants.PAY_TIMEOUT, timeOutStr);
        params.put(XZXRequestFieldsConstants.TRAN_AMT, tranAmt);
        params.put(XZXRequestFieldsConstants.BODY, body);
        // goods 可空，暂时不传
        //        params.put(XZXRequestFieldsConstants.GOODS_DETAIL, extendedParams.getOrDefault(XZXRequestFieldsConstants.GOODS_DETAIL, new ArrayList<>()));
//        payParams.put(XZXRequestFieldsConstants.ATTACH, "附加数据");
        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            params.put(XZXRequestFieldsConstants.NOTIFY_URL, notifyUrl);
        }
        params.put(XZXRequestFieldsConstants.EXTEND_PARAMS, extendParams);

        //设置应用级请求参数
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = retryIfNetworkException(method, xzxRequestBuilder.build(), tradeParams, tsn);

        return result;
    }

    /**
     * doCashier 调起收银台
     * @param context
     * @return
     */
    protected Map<String,Object> doCashier(TransactionContext context, String prepayId) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        XZXRequestBuilder xzxRequestBuilder = buildSystemLevelRequestParams(tradeParams);
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessTokenFromCache(transaction));

        String method = XZXMethodFieldsConstants.METHOD_PAYMENT_CASHIER;

        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.MCH_ACCT_ID, getMchAcctId(tradeParams));
        params.put(XZXRequestFieldsConstants.OUT_TRADE_NO, tsn);
        params.put(XZXRequestFieldsConstants.PREPAY_ID, prepayId);
        params.put(XZXRequestFieldsConstants.QUICK_JUMP, "1");
        params.put(XZXRequestFieldsConstants.DEV_ID, XZXDevIdEnum.DEV_ID_MINI_PROGRAM.getCode());
        params.put(XZXRequestFieldsConstants.PAY_PRD_CODE, XZXPayPrdCodeEnum.BANK_WXPAY.getCode());

        //设置应用级请求参数
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = retryIfNetworkException(method, xzxRequestBuilder.build(), tradeParams, tsn);

        return result;
    }


    /**
     * doQueryOrder 查询订单
     * @param context
     * @return
     */
    protected Map<String,Object> doQueryOrder(TransactionContext context, String transactionId) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);


        XZXRequestBuilder xzxRequestBuilder = buildSystemLevelRequestParams(tradeParams);
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessTokenFromCache(transaction));

        String method = XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_QUERY;

        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.TRANSACTION_ID, transactionId);

        //设置应用级请求参数
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = retryIfNetworkException(method, xzxRequestBuilder.build(), tradeParams, tsn);
        return result;
    }

    /**
     * doRefund 订单退款
     * @param context
     * @return
     */
    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        XZXRequestBuilder xzxRequestBuilder = buildSystemLevelRequestParams(tradeParams);
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessTokenFromCache(transaction));

        String method = XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_REFUND;

        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        long tranAmt = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        String oriTransactionId = MapUtils.getString(context.getOrder(), Order.TRADE_NO, "");
        long totalAmt = MapUtils.getLong(context.getOrder(), Order.EFFECTIVE_TOTAL, 0L);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.MCH_ACCT_ID, getMchAcctId(tradeParams));
        params.put(XZXRequestFieldsConstants.OUT_TRADE_NO, tsn);
        params.put(XZXRequestFieldsConstants.ORI_TRANSACTION_ID, oriTransactionId);
        params.put(XZXRequestFieldsConstants.TRAN_AMT, tranAmt);
        params.put(XZXRequestFieldsConstants.TOTAL_AMT, totalAmt);

        //设置应用级请求参数
        xzxRequestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = retryIfNetworkException(method, xzxRequestBuilder.build(), tradeParams, tsn);

        if (!checkCallSucceed(result)) {
            setTransactionContextErrorInfo(transaction, OP_REFUND, result, false, getName(), "新中新退款请求失败");
            return Workflow.RC_ERROR;
        } else {
            setSuccessTransactionContextErrorInfo(transaction, result, OP_REFUND);
        }

        Map objResult = MapUtils.getMap(result, XZXResponseFieldsConstants.OBJ);

        if (objResult != null && !objResult.isEmpty()) {
            updateRefundCommonInfo(transaction, objResult);
            String bizStatus = MapUtils.getString(objResult, XZXResponseFieldsConstants.BIZ_STATUS, "");
            if (XZXBizStatusEnum.SUCCEED.getCode().equals(bizStatus)) {
                return Workflow.RC_REFUND_SUCCESS;
            } else if (XZXBizStatusEnum.FAILED.getCode().equals(bizStatus)) {
                return Workflow.RC_ERROR;
            }
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_SYS_ERROR;
    }



    /**
     * 更新通用字段
     * @param transaction
     * @param objectMap
     */
    protected void updateRefundCommonInfo(Map<String, Object> transaction, Map<String, Object> objectMap) {
        Map<String, Object> extraOutFields = getExtraOutFields(transaction);
        // 解析订单号
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, objectMap, XZXResponseFieldsConstants.TRANSACTION_ID);
        // 解析订单号
        updateMapIfResponseNotNull(extraOutFields, Transaction.TRADE_NO, objectMap, XZXResponseFieldsConstants.TRANSACTION_ID);
        // 标识状态
        extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
    }



    /**
     * 更新通用字段
     * @param transaction
     * @param objectMap
     */
    protected void updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> order, Map<String, Object> objectMap) {
        Map<String, Object> extraOutFields = getExtraOutFields(transaction);
        // 解析订单号
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, objectMap, XZXResponseFieldsConstants.TRANSACTION_ID);
        // 解析订单号
        updateMapIfResponseNotNull(extraOutFields, Transaction.TRADE_NO, objectMap, XZXResponseFieldsConstants.TRANSACTION_ID);
        // 解析订单号
        updateMapIfResponseNotNull(order, Order.TRADE_NO, objectMap, XZXResponseFieldsConstants.TRANSACTION_ID);
    }


    /**
     * 更新付款信息
     * @param transaction
     * @param objectMap
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> objectMap){

        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, objectMap, XZXResponseFieldsConstants.TRAN_DT, object -> parseTimeString((String) object));

        // 使用返回金额
        Long effectiveAmount = MapUtils.getLong(objectMap, XZXResponseFieldsConstants.TRAN_AMT);

        if (effectiveAmount > 0) {
            List<Map<String, Object>> payments = new ArrayList<>();
            payments.add(
                    CollectionUtil.hashMap(
                            // 只接微信小程序
                            Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_WEIXIN,
                            Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_WALLET_WEIXIN,
                            Transaction.PAYMENT_AMOUNT, effectiveAmount
                    )
            );
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        }
    }



    /**
     * 获取访问令牌
     *
     * @param transaction
     * @return
     */
    private String getAccessTokenFromCache(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appKey = getAppKey(tradeParams);

        String accessToken = redisTemplate.opsForValue().get(buildAccessTokenCacheKey(appKey));
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }

        log.info("{}: 获取accessToken, tsn={}", NAME, MapUtils.getString(transaction, Transaction.TSN));
        return getAccessToken(transaction, tradeParams);
    }


    private String getAccessToken(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String appKey = getAppKey(tradeParams);
        XZXQueryAccessTokenResponse accessTokenResult;

        try {
            XZXQueryAccessTokenReqest reqest = new XZXQueryAccessTokenReqest();
            reqest.setAppKey(appKey);
            accessTokenResult = xzxService.queryAccessToken(reqest);
        } catch (Exception e) {
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "获取accessToken异常:" + e.getMessage());
        }

        try {
            //放入缓存
            String accessToken = accessTokenResult.getAccessToken();
            long expiresIn = accessTokenResult.getExpiresIn();
            //失效时间缩短几分钟，这样可以在失效前就重新向锦医一卡通请求新的token
            expiresIn -= ACCESS_TOKEN_EXPIRE_TIME_BUFFER;
            if (expiresIn > 0) {
                log.info("{}: 获取最新accessToken, expireSeconds={}, tsn={}", NAME, expiresIn, tsn);
                String cacheKey = buildAccessTokenCacheKey(appKey);
                redisTemplate.opsForValue().set(cacheKey, accessToken, expiresIn, TimeUnit.SECONDS);
            }
            return accessToken;
        } catch (Exception e) {
            log.error("{}: 获取accessToken异常, tsn={}, error={}", NAME, tsn, e.getMessage(), e);
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "获取accessToken异常:" + e.getMessage());
        }
    }

    /**
     * 构建系统级的请求参数
     *
     * @param tradeParams
     * @return
     */
    private XZXRequestBuilder buildSystemLevelRequestParams(Map<String, Object> tradeParams) {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, DateUtil.formatDate(new Date(), STANDARD_DATE_FORMAT));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, getAppKey(tradeParams));
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, DEFAULT_SIGN_METHOD);
        return requestBuilder;
    }

    /**
     * 检查 access token 是否过期
     * @param response
     * @param tsn
     * @param tradeParams
     */
    private void checkIfNeedClearAccessTokenCache(Map<String, Object> response, String tsn, Map<String, Object> tradeParams) {
        String systemErrorCode = getSystemErrorCode(response);
        if (!XZXSystemErrorEnum.ACCESS_TOKEN_INVALID.getCode().equals(systemErrorCode) && !XZXSystemErrorEnum.REQUEST_FAILED.getCode().equals(systemErrorCode)) {
            return;
        }

        String appKey = MapUtils.getString(tradeParams, TransactionParam.XZX_APP_KEY);
        //accessToken已失效, 则清空缓存中的accessToken
        log.warn("{}: accessToken已失效, 清空accessToken缓存 tsn={}, appKey={}, errorCode={}", NAME, tsn, appKey, systemErrorCode);
        try {
            redisTemplate.delete(buildAccessTokenCacheKey(appKey));
        } catch (Exception e) {
            //do nothing
        }
    }


    private Map<String, Object> retryIfNetworkException(String method, Map<String, Object> request, Map<String, Object> tradeParams, String tsn) {
        String gatewayUrl = getGatewayUrl();
        try {
            Map<String, Object> result = new RetryUtil<Map<String, Object>>()
                    .retry(new RetryUtil.TimingStrategy.Builder().setRetry(2, 50, 1.0).build())
                    .method(() -> {
                        try {
                            return xzxClient.call(gatewayUrl, method, request,
                                    getAppSecret(tradeParams), getPrivateKey(tradeParams), getPublicKey(tradeParams));
                        } catch (MpayException | MpayApiNetworkError e) {
                            log.error("{}: 调用新中新接口异常: method={}, request={}, error={}",
                                    NAME, method, JacksonUtil.toJsonString(request), e.getMessage());
                            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
                        }
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull)
                    .execute();

            //判断是否需要重置accessToken缓存
            checkIfNeedClearAccessTokenCache(result, tsn, tradeParams);

            return result;
        } catch (Exception e) {
            log.error("{}: 调用新中新接口异常: method={}, request={}, error={}",
                    NAME, method, JacksonUtil.toJsonString(request), e.getMessage());
            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }

    }

    /**
     * 检查请求是否成功
     * @param result
     * @return
     */
    protected boolean checkCallSucceed(Map<String, Object> result) {
        String systemErrorCode = getSystemErrorCode(result);
        String respCode = getRespErrorCode(result);

        // 调起收银台没有 systemErrorCode
        return (StringUtils.isEmpty(systemErrorCode) || XZXSystemErrorEnum.isSuccess(systemErrorCode)) && XZXResponseErrorEnum.isSuccess(respCode);
    }

    /**
     * 操作成功记录日志
     * @param transaction
     * @param result
     * @param oper
     */
    protected void setSuccessTransactionContextErrorInfo(Map<String, Object> transaction, Map<String, Object> result, String oper) {

        Map<String, Object> map = new HashMap<>();
        map.put(XZXResponseFieldsConstants.SYSTEM_ERROR_CODE, getSystemErrorCode(result));
        map.put(XZXResponseFieldsConstants.RESP_CODE, getRespErrorCode(result));
        String respInfo =  MapUtils.getString(result, XZXResponseFieldsConstants.RESP_INFO);
        respInfo = com.wosai.mpay.util.StringUtils.isEmpty(respInfo) ? "" : respInfo;
        map.put(XZXResponseFieldsConstants.RESP_INFO, respInfo);

        Map objResult = MapUtils.getMap(result, XZXResponseFieldsConstants.OBJ);
        if (null != objResult) {
            String bizStatus =  MapUtils.getString(objResult, XZXResponseFieldsConstants.BIZ_STATUS);
            if (StringUtils.isNotEmpty(bizStatus)) {
                map.put(XZXResponseFieldsConstants.BIZ_STATUS, bizStatus);
                XZXBizStatusEnum status = XZXBizStatusEnum.of(bizStatus);
                if (null != status) {
                    map.put(XZXResponseFieldsConstants.BIZ_STATUS+ "_msg", status.getDescription());
                }
            }
            String transactionId =  MapUtils.getString(objResult, XZXResponseFieldsConstants.TRANSACTION_ID);
            if (StringUtils.isNotEmpty(transactionId)) {
                map.put(XZXResponseFieldsConstants.TRANSACTION_ID, transactionId);
            }
        }

        setTransactionContextErrorInfo(transaction, oper, map, true, getName(), respInfo);
    }

    protected String getSystemErrorCode(Map<String, Object> response) {
        return MapUtils.getString(response, XZXResponseFieldsConstants.SYSTEM_ERROR_CODE);
    }

    protected String getRespErrorCode(Map<String, Object> response) {
        return MapUtils.getString(response, XZXResponseFieldsConstants.RESP_CODE);
    }

    protected  String getGatewayUrl() {
        return ApolloConfigurationCenterUtil.getProviderGateway(getName(), "");
    }

    protected String buildAccessTokenCacheKey(String appKey) {
        return XZX_ACCESS_TOKEN_CACHE_KEY + appKey;
    }

    protected String getMchAcctId(Map<String, Object> tradeParams) {
        return MapUtils.getString(tradeParams, TransactionParam.XZX_MCH_ACCT_ID);
    }

    protected String getAppKey(Map<String, Object> tradeParams) {
        return MapUtils.getString(tradeParams, TransactionParam.XZX_APP_KEY);
    }

    protected String getPrivateKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.XZX_PRIVATE_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    protected String getPublicKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.XZX_PUBLIC_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    protected String getAppSecret(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.XZX_APP_SECRET);
        return getPrivateKeyContent(rsaKeyId);
    }

}
