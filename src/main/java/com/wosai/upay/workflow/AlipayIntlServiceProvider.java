package com.wosai.upay.workflow;

import java.text.ParseException;
import java.text.ParsePosition;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.databind.util.ISO8601Utils;
import com.fasterxml.uuid.Generators;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.intl.AlipayIntlClient;
import com.wosai.mpay.api.alipay.intl.AlipayIntlConstants;
import com.wosai.mpay.api.alipay.intl.BusinessFields;
import com.wosai.mpay.api.alipay.intl.RequestBuilder;
import com.wosai.mpay.api.alipay.intl.ResponseFields;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class AlipayIntlServiceProvider extends AbstractServiceProvider{
    private static final Logger logger = LoggerFactory.getLogger(AlipayIntlServiceProvider.class);
    private static com.fasterxml.uuid.impl.TimeBasedGenerator generator = Generators.timeBasedGenerator();
    public static final String NAME = "provider.alipay.intl";
    
    private static final String KEY_PAY_STATUS = String.format("%s.%s", ResponseFields.PAYMENT, ResponseFields.PAY_STATUS);
    // 支付宝钱包和支付金额对应关系
    private static final Map<String, String> ALIPAY_INTL_CURRENCY = CollectionUtil.hashMap("ALIPAY_CN","CNY",
                "ALIPAY_HK", "HKD",
                "ALIPAY_USA", "USD",
                "ALIPAY_JP", "JPY",
                "ALIPAY_KR", "KRW",
                "ALIPAY_UK", "GBP",
                "ALIPAY_SG", "SGD"
    );
    
    @Autowired
    protected AlipayIntlClient client;
    @Autowired
    protected ExternalServiceFacade facade;

    protected String notifyHost;
    protected int retryTimes = 3;


    public AlipayIntlServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(AlipayIntlConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.ORDER_INFO));
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if(null != extended){
            for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
                String key = extendedParam.getKey();
                if(overFilterField(key)) {
                    continue;
                }
                Object value = extendedParam.getValue();
                if (value != null) {
                    builder.bodySet(key, value);
                }
            }
        }
    }

    @Override
    public String pay(TransactionContext context, boolean resume)  {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_PAY_BARCODE);
        builder.bodySet(BusinessFields.PRODUCT_CODE, AlipayIntlConstants.PRODUCT_CODE_OFFLINE_PAY);
        builder.bodySet(BusinessFields.MERCHANT_TRANS_ID, transaction.get(Transaction.TSN));
        builder.bodySet(BusinessFields.CODE_TYPE, AlipayIntlConstants.CODE_TYPE_BARCODE);
        builder.bodySet(BusinessFields.IDENTITY_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        
        Map configSnapShot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.bodySet(BusinessFields.ORDER_INFO, new HashMap<String, Object>(){{
            put(BusinessFields.ORDER_AMOUNT, new LinkedHashMap(){{
                put(BusinessFields.CURRENCY, getTradeCurrency(transaction));
                put(BusinessFields.VALUE, BeanUtil.getPropString(transaction, (Transaction.EFFECTIVE_AMOUNT)));
            }});
            put(BusinessFields.SELLER, new LinkedHashMap(){{
                put(BusinessFields.SELLER_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.MERCHANT_SN));
                put(BusinessFields.SELLER_NAME, BeanUtil.getPropString(configSnapShot, TransactionParam.MERCHANT_NAME));
                put(BusinessFields.STORE_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_CLIENT_SN, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_SN)));
                put(BusinessFields.STORE_NAME, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_NAME));
                put(BusinessFields.TERMINAL_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.TERMINAL_SN));
                put(BusinessFields.MCC, BeanUtil.getPropString(config, TransactionParam.ALIPAY_INTL_MERCHANT_ALIPAY_INDUSTRY,TransactionParam.MERCHANT_DEFAULT_ALIPAY_INDUSTRY));
            }});
            put(BusinessFields.ORDER_TITLE, transaction.get(Transaction.SUBJECT));
            put(BusinessFields.ORDER_DETAIL, StringUtil.empty((String)transaction.get(Transaction.BODY)) ? transaction.get(Transaction.SUBJECT) : transaction.get(Transaction.BODY));
        }});
        
        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);
        Map<String,Object> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay intl request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), request, 1, OP_PAY);
        }catch (Exception ex) {
            logger.error("failed to call alipay intl pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
       
        Map responseBody = (Map) BeanUtil.getProperty(result, BusinessFields.BODY);
        Map resultInfo = (Map) BeanUtil.getProperty(responseBody, ResponseFields.RESULT_INFO);
        String resultCode = BeanUtil.getPropString(resultInfo, ResponseFields.RESULT_CODE);//返回状态码
        setTransactionContextErrorInfo(resultInfo, context, OP_PAY);
        if(AlipayIntlConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //付款成功,代码之所以不写到setTradeRespInfo2Trans 中是因为 Pay接口和Query接口查询到的数据结构不是一样的。
            String buyerUid = BeanUtil.getPropString(responseBody, ResponseFields.USER_ID);
            String tradeNo = BeanUtil.getPropString(responseBody, ResponseFields.ACQUIREMENT_ID);
            String buyerLogin = BeanUtil.getPropString(responseBody, ResponseFields.USER_LOGIN_ID);
            String finishTime = BeanUtil.getPropString(responseBody, ResponseFields.CREATE_TIME);
            String userSite = BeanUtil.getPropString(responseBody, ResponseFields.USER_SITE);
            setTradeRespInfo2Trans(context, buyerUid, buyerLogin, tradeNo, finishTime, userSite);
            
            return Workflow.RC_PAY_SUCCESS;
            
        }else if(AlipayIntlConstants.RESP_RESULT_CODE_PAYMENT_IN_PROCESS.equals(resultCode)
                    || AlipayIntlConstants.RESP_RESULT_CODE_PROCESS_FAIL.equals(resultCode)
                    || AlipayIntlConstants.RESP_RESULT_CODE_UNKNOWN_EXCEPTION.equals(resultCode)
                    || AlipayIntlConstants.RESP_RESULT_CODE_REPEAT_REQ_INCONSISTENT.equals(resultCode)){
            //业务处理失败
            return Workflow.RC_IN_PROG;
        }else if(AlipayIntlConstants.PAY_FAIL_ERR_CODE_LISTS.contains(resultCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    private void setTradeRespInfo2Trans(TransactionContext context, String buyerUid, String buyerLogin, String tradeNo, String channelFinishTime, String userSite) {
        if(!StringUtil.empty(buyerUid)){
            context.getTransaction().put(Transaction.BUYER_UID,  buyerUid);
            context.getOrder().put(Order.BUYER_UID,  buyerUid);
        }
        
        if(!StringUtil.empty(buyerLogin)){
            context.getTransaction().put(Transaction.BUYER_LOGIN,  buyerLogin);
            context.getOrder().put(Order.BUYER_LOGIN,  buyerLogin);
        }
        
        if(!StringUtil.empty(tradeNo)){
            context.getTransaction().put(Transaction.TRADE_NO, tradeNo);
            context.getOrder().put(Transaction.TRADE_NO, tradeNo);
        }
        
        if(!StringUtil.empty(channelFinishTime)){
            long finishTimeLong = 0l;
            try {
                finishTimeLong = ISO8601Utils.parse(channelFinishTime, new ParsePosition(0)).getTime();
            } catch (ParseException e) {
                logger.error("error in ISO8601Utils.parse ", e);
                finishTimeLong = System.currentTimeMillis();
            }
            
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, finishTimeLong);
        }
        
        if(!StringUtil.empty(userSite)){
            Map<String, Object> extraOutFields = (Map<String, Object>) context.getTransaction().get(Transaction.EXTRA_OUT_FIELDS);
            if (extraOutFields == null) {
                extraOutFields = new HashMap<>();
                context.getTransaction().put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
            if(null == overseas){
                overseas = new HashMap<>();
                extraOutFields.put(Transaction.OVERSEAS, overseas);
            }
            overseas.put(Transaction.USER_SITE, userSite);
            String currency = ALIPAY_INTL_CURRENCY.get(userSite);
            if(null != currency){
                overseas.put(Transaction.CURRENCY, currency);
            }
        }
    }

    private RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        RequestBuilder builder = new RequestBuilder();
        Map config = getTradeParams(context.getTransaction());
        builder.headSet(BusinessFields.VERSION, AlipayIntlConstants.DEFAULT_VERSION);
        builder.headSet(BusinessFields.CLINET_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_INTL_CLIENT_ID));
        builder.headSet(BusinessFields.REQ_TIME, ISO8601Utils.format(new Date(), false, TimeZone.getDefault()));
        builder.headSet(BusinessFields.REQ_MSG_ID, "shouqianba-" + generator.generate().toString().replaceAll("-", ""));
        
        builder.bodySet(BusinessFields.MERCHANT_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_INTL_MERCHANT_ID));

        return builder;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);

        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_CANCEL);
        builder.bodySet(BusinessFields.MERCHANT_TRANS_ID, transaction.get(Transaction.ORDER_SN));
        
        Map<String,Object> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay intl request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), request, 1, OP_CANCEL);
        }catch (Exception ex) {
            logger.error("failed to call alipay intl cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return Workflow.RC_RETRY;
        }
        if(result == null){
            return Workflow.RC_RETRY;
        }
       
        Map responseBody = (Map) BeanUtil.getProperty(result, BusinessFields.BODY);
        Map resultInfo = (Map) BeanUtil.getProperty(responseBody, ResponseFields.RESULT_INFO);
        String resultCode = BeanUtil.getPropString(resultInfo, ResponseFields.RESULT_CODE);//返回状态码
        setTransactionContextErrorInfo(resultInfo, context, OP_CANCEL);
        if(AlipayIntlConstants.RESULT_CODE_SUCCESS.equals(resultCode) 
                || AlipayIntlConstants.RESP_RESULT_CODE_ORDER_IS_CLOSED.equals(resultCode)
                || AlipayIntlConstants.RESP_RESULT_CODE_ORDER_NOT_EXIST.equals(resultCode)
                || AlipayIntlConstants.RESP_RESULT_CODE_USER_STATUS_ABNORMAL.equals(resultCode)){
            
            transaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
            String channelFinishTime = ISO8601Utils.format(new Date(), false, TimeZone.getDefault());
            setTradeRespInfo2Trans(context, null, null, null, channelFinishTime, null);
            
            return Workflow.RC_CANCEL_SUCCESS;
        }else if(AlipayIntlConstants.RESP_RESULT_CODE_UNKNOWN_EXCEPTION.equals(resultCode) 
                    || AlipayIntlConstants.RESP_RESULT_CODE_REQUEST_TRAFFIC_EXCEED_LIMIT.equals(resultCode) ){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);

        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_QUERY);
        builder.bodySet(BusinessFields.MERCHANT_TRANS_ID, transaction.get(Transaction.TSN));
        Map<String,Object> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay intl request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), request, 1, OP_QUERY);
        }catch (Exception ex) {
            logger.error("failed to call alipay intl query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
       
        Map responseBody = (Map) BeanUtil.getProperty(result, BusinessFields.BODY);
        Map resultInfo = (Map) BeanUtil.getProperty(responseBody, ResponseFields.RESULT_INFO);
        String resultCode = BeanUtil.getPropString(resultInfo, ResponseFields.RESULT_CODE);//返回状态码
        setTransactionContextErrorInfo(resultInfo, context, OP_QUERY);
        String payStatus = BeanUtil.getPropString(responseBody, KEY_PAY_STATUS);
        
        if(AlipayIntlConstants.RESULT_CODE_SUCCESS.equals(resultCode) 
                && AlipayIntlConstants.PAY_STATUS_SUCCESS.equals(payStatus)){
            //付款成功,代码之所以不写到setTradeRespInfo2Trans 中是因为 Pay接口和Query接口查询到的数据结构不是一样的。
            String buyerUid = BeanUtil.getPropString(responseBody, ResponseFields.USER_ID);
            String buyerLogin = BeanUtil.getPropString(responseBody, ResponseFields.USER_LOGIN_ID);
            String userSite = BeanUtil.getPropString(responseBody, ResponseFields.USER_SITE);

            Map paymentInfo = (Map) BeanUtil.getProperty(responseBody, ResponseFields.PAYMENT);
            String tradeNo = BeanUtil.getPropString(paymentInfo, ResponseFields.ACQUIREMENT_ID);
            String finishTime = BeanUtil.getPropString(paymentInfo, ResponseFields.PAYMENT_TIME);
            setTradeRespInfo2Trans(context, buyerUid, buyerLogin, tradeNo, finishTime, userSite);
            return Workflow.RC_PAY_SUCCESS;
            
        }else if(AlipayIntlConstants.RESP_RESULT_CODE_PAYMENT_IN_PROCESS.equals(resultCode)
                    || AlipayIntlConstants.RESP_RESULT_CODE_PROCESS_FAIL.equals(resultCode)
                    || AlipayIntlConstants.RESP_RESULT_CODE_UNKNOWN_EXCEPTION.equals(resultCode)
                    || AlipayIntlConstants.RESP_RESULT_CODE_REPEAT_REQ_INCONSISTENT.equals(resultCode)
                    || AlipayIntlConstants.PAY_STATUS_WAIT_PAY.equals(payStatus)){
            //业务处理中
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);

        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_REFUND);
        builder.bodySet(BusinessFields.ACQUIREMENT_ID, context.getOrder().get(Order.TRADE_NO));
        builder.bodySet(BusinessFields.MERCHANT_REFUND_ID, transaction.get(Transaction.TSN));
        builder.bodySet(BusinessFields.REFUND_AMOUNT, new HashMap(){{
            put(BusinessFields.CURRENCY, getTradeCurrency(transaction));
            put(BusinessFields.VALUE, BeanUtil.getPropString(transaction, Transaction.EFFECTIVE_AMOUNT));
        }});
        builder.bodySet(BusinessFields.REFUND_IS_SYNC, AlipayIntlConstants.REFUND_IS_SYNC_YES);
        
        Map<String,Object> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay intl request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), request, 1, OP_REFUND);
        }catch (Exception ex) {
            logger.error("failed to call alipay intl refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_RETRY;
        }
        if(result == null){
            return Workflow.RC_RETRY;
        }
       
        Map responseBody = (Map) BeanUtil.getProperty(result, BusinessFields.BODY);
        Map resultInfo = (Map) BeanUtil.getProperty(responseBody, ResponseFields.RESULT_INFO);
        String resultCode = BeanUtil.getPropString(resultInfo, ResponseFields.RESULT_CODE);//返回状态码
        setTransactionContextErrorInfo(resultInfo, context, OP_REFUND);
        if(AlipayIntlConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            // 同一单号重复退款返回的都是成功，且只会退一笔
            String refundTime = BeanUtil.getPropString(responseBody, ResponseFields.REFUND_TIME);
            setTradeRespInfo2Trans(context, null, null, null, refundTime, null);
            
            return Workflow.RC_REFUND_SUCCESS;
        }else if(AlipayIntlConstants.RESP_RESULT_CODE_UNKNOWN_EXCEPTION.equals(resultCode) 
                    || AlipayIntlConstants.RESP_RESULT_CODE_REQUEST_TRAFFIC_EXCEED_LIMIT.equals(resultCode) ){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(type == Transaction.TYPE_PAYMENT){
            return Workflow.RC_PAY_SUCCESS.equals(query(context))?Workflow.RC_PAY_SUCCESS:null;
        }
        return null;
    }



    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String returnCode = (String)result.get(ResponseFields.RESULT_CODE);//返回状态码
        String returnMsg = (String)result.get(ResponseFields.RESULT_MSG);//返回信息
        map.put(ResponseFields.RESULT_CODE, returnCode);//返回状态码
        map.put(ResponseFields.RESULT_MSG, returnMsg);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, AlipayIntlConstants.RESULT_CODE_SUCCESS.equals(returnCode), returnCode, returnMsg);
    }

    protected Map<String,Object> retryIfNetworkException(Map<String, Object>  config, String gatewayUrl, Map<String, Object> request, int retryTimes, String logFlag) throws Exception{
        Exception exception = null;
        for (int i = 0; i< retryTimes; ++i) {
            try {
                return client.call(gatewayUrl, getPrivateKeyContent((String) config.get(TransactionParam.ALIPAY_INTL_PRIVATE_KEY)), request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in alipayIntl {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", retryTimes));
        throw exception;

    }

    public String getPrivateKeyContent(String rsaKeyId){
        return facade.getRsaKeyDataById(rsaKeyId);
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(getTradeCurrency(transaction)) 
                || getTradeParams(transaction) == null ) {
             return false;
        }
        return true;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ALIPAY_INTL_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }
}
