package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.Action;
import com.wosai.fsm.MachineBuilder;
import com.wosai.fsm.MachineContext;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.FeeRateProcessor;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class DepositConsumeOrderWorkflow extends Workflow {
    private static final Logger logger = LoggerFactory.getLogger(DepositConsumeOrderWorkflow.class);
    private static final long[] delays = {500, 1000, 2000, 3000, 5000};
    public static final String NAME = "generic.deposit.consume.workflow";

    public DepositConsumeOrderWorkflow(){
        this(delays);
    }

    public DepositConsumeOrderWorkflow(long[] delays) {
        MachineBuilder builder = new MachineBuilder();
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return depositConsumeOrder((TransactionContext)context);
            }
        }).transition(RC_CONSUME_SUCCESS, SUCCESS)
          .transition(RC_CONSUME_FAIL, FAIL_ERROR)
          .transition(RC_ERROR, CONSUME_ERROR)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_IN_PROG, IN_PROG)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_IOEX,  FAIL_IO_1)

        .on(SUCCESS).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return finish((TransactionContext)context);
            }
        }).end()

        .on(IN_PROG).delay(delays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                Map<String,Object> transaction = ((TransactionContext)context).getTransaction();
                int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
                int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
                if((payway == Order.PAYWAY_WEIXIN || payway == Order.PAYWAY_UNIONPAY) && subPayWay == Order.SUB_PAYWAY_MINI){
                    return query((TransactionContext) context);
                }
                return depositConsumeOrder((TransactionContext)context);
            }
        }).transition(RC_CONSUME_SUCCESS, SUCCESS)
          .transition(RC_CONSUME_FAIL, FAIL_ERROR)
          .transition(RC_IN_PROG, IN_PROG)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_IOEX, FAIL_IO_2)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_ERROR, CONSUME_ERROR)
          .transition(RC_EXPIRE, CONSUME_ERROR)
          .transition(RC_TRADE_CANCELED, CONSUME_ERROR)

        .on(CONSUME_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return safeClose((TransactionContext)context);
            }
        }).end();
        
        machine = builder.build();
    }

    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME
                && MapUtil.getIntValue(transaction, Transaction.PAYWAY) != Order.PAYWAY_BANKCARD) {
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        return context.getServiceProvider().explainNotification(notification);
    }

    public String depositConsumeOrder(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_DEPOSIT_CONSUME), () -> {
            String result = depositProvider.depositConsume(context);
            logger.debug("TID {} deposit consume method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @SuppressWarnings("unchecked")
    @Override
    public String finish(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        order.put(Order.NET_ORIGINAL, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        order.put(Order.NET_EFFECTIVE, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        order.put(Order.STATUS, Order.STATUS_DEPOSIT_CONSUMED);
        transaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
        //重置手续费
        resetTradeFeeRate(context, order, transaction);
        Object effectiveAmount = transaction.get(Transaction.EFFECTIVE_AMOUNT);
        if (transaction.get(Transaction.PAID_AMOUNT) == null) {
            transaction.put(Transaction.PAID_AMOUNT, effectiveAmount);
        }
        if (transaction.get(Transaction.RECEIVED_AMOUNT) == null) {
            transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
        }
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.RECEIVED_AMOUNT, transaction.get(Transaction.RECEIVED_AMOUNT),
                                                                        Transaction.PAID_AMOUNT, transaction.get(Transaction.PAID_AMOUNT),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.CONFIG_SNAPSHOT, transaction.get(Transaction.CONFIG_SNAPSHOT));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.NET_ORIGINAL, order.get(Order.NET_ORIGINAL),
                                                                  Order.NET_EFFECTIVE, order.get(Order.NET_EFFECTIVE),
                                                                  Order.NET_DISCOUNT, order.get(Order.NET_DISCOUNT),
                                                                  Order.ITEMS, order.get(Order.ITEMS),
                                                                  Order.STATUS, order.get(Order.STATUS));

        if (BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_MINI
                && (BeanUtil.getPropInt(transaction, Transaction.PAYWAY) == Order.PAYWAY_WEIXIN || BeanUtil.getPropInt(transaction, Transaction.PAYWAY) == Order.PAYWAY_UNIONPAY)) {
            orderUpdates.put(Order.TRADE_NO, transaction.get(Transaction.TRADE_NO));
        }

        Map<String, Object> extraOutFileds = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);
        if(MapUtil.getBooleanValue(extraOutFileds, Transaction.IS_HISTORY_DEPOSIT_CONSUME, false)){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION) + 1);
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
            orderUpdates = null;
        }
        
        if(MapUtil.getBooleanValue(extraOutFileds, Transaction.IN_HBASE, false)){
            try {
                extraOutFileds.remove(Transaction.IN_HBASE);
                transaction.put(DaoConstants.MTIME, System.currentTimeMillis());
                transaction.put(DaoConstants.VERSION, MapUtil.getIntValue(transaction, DaoConstants.VERSION) + 1);
                upayOrderService.updateTransaction(transaction);
            }catch (Exception e) {
                logger.error("update transaction hbase data error, data = {} , ex= {}", transaction, e);
            }
            transactionUpdates = null;
        }
        //是否关闭记录log并设置富友更新flag
        boolean disableLog = disableLogAndSetFuyouFeeFlag(transactionUpdates, context);
        finishTransaction0(context, orderUpdates, transactionUpdates, disableLog);

        if(context.isFakeRequest()){
            logger.debug("TID {} deposit consume finished", context.getTid());
            return null;
        }
        notifyClient(context);
        logger.debug("TID {} deposit consume finished", context.getTid());
        return null;
    }
    @SuppressWarnings("unchecked")
    public String unsafeClose(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        if (BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_DEPOSIT_FREEZED 
                || BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_DEPOSIT_CONSUME_INPROGRESS) {
            order.put(Order.STATUS, Order.STATUS_DEPOSIT_CONSUME_ERROR);
        }
        // RC_EXPIRE 不会变更流水状态，需要做下状态变更
        if (MapUtil.getIntValue(context.getTransaction(), Transaction.STATUS) == Transaction.STATUS_IN_PROG) {
            context.getTransaction().put(Transaction.STATUS, Transaction.STATUS_CONSUME_ERROR);
        }

        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.STATUS, order.get(Order.STATUS));

        repository.getTransactionDao().updatePart(transactionUpdates);
        if(BeanUtil.getPropBoolean(context.getTransaction(), Transaction.KEY_IS_HISTORY_DEPOSIT_CONSUME, false)){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
        }else {
            repository.getOrderDao().updatePart(orderUpdates);
        }
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        notifyClient(context);
        logger.debug("TID {} closed", context.getTid());
        return null;
    }

    @SuppressWarnings("unchecked")
    public String safeClose(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        order.put(Order.STATUS, Order.STATUS_DEPOSIT_FREEZED);
        transaction.put(Transaction.STATUS, Transaction.STATUS_CONSUME_ERROR);
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.STATUS, order.get(Order.STATUS));

        repository.getTransactionDao().updatePart(transactionUpdates);
        if(BeanUtil.getPropBoolean(context.getTransaction(), Transaction.KEY_IS_HISTORY_DEPOSIT_CONSUME, false)){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
        }else {
            repository.getOrderDao().updatePart(orderUpdates);
        }
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        notifyClient(context);
        logger.debug("TID {} closed", context.getTid());
        return null;
    }

    public String query(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_DEPOSIT_QUERY), () -> {
            String result = depositProvider.depositQuery(context);
            logger.debug("TID {} deposit query method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    /**
     * 授权完成 - 收钱吧扫码预授权payments处理
     *
     * @param context
     */
    private void setFinishBySqbDepositPayments(TransactionContext context, Map<String, Object> freezeTransaction) {
        Map<String, Object> transaction = context.getTransaction();
        List<Map<String, Object>> refundPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        List<Map<String, Object>> freezePayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(freezeTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);

        //保存refund数据
        long refundAmount = BeanUtil.getPropLong(transaction, Transaction.KEY_DEPOSIT_REFUND_EFFECTIVE_AMOUNT);
        Map<String, Object> consumeRefundInfoMap = MapUtil.hashMap(
                Transaction.ORIGINAL_AMOUNT, (BeanUtil.getPropLong(freezeTransaction, Transaction.ORIGINAL_AMOUNT) - BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)),
                Transaction.PAID_AMOUNT, getAmount(transaction, Transaction.PAID_AMOUNT, refundAmount),
                Transaction.RECEIVED_AMOUNT, getAmount(transaction, Transaction.RECEIVED_AMOUNT, refundAmount),
                Transaction.EFFECTIVE_AMOUNT, refundAmount
        );
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_DEPOSIT_CONSUME_REFUND_INFO, consumeRefundInfoMap);
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_DEPOSIT_REFUND_PAYMENTS_PATH, refundPayments);
        //保存consume数据
        BeanUtil.setNestedProperty(transaction, Transaction.PAID_AMOUNT, (BeanUtil.getPropLong(freezeTransaction, Transaction.PAID_AMOUNT) - BeanUtil.getPropLong(consumeRefundInfoMap, Transaction.PAID_AMOUNT)));
        BeanUtil.setNestedProperty(transaction, Transaction.RECEIVED_AMOUNT, (BeanUtil.getPropLong(freezeTransaction, Transaction.RECEIVED_AMOUNT) - BeanUtil.getPropLong(consumeRefundInfoMap, Transaction.RECEIVED_AMOUNT)));
        BeanUtil.setNestedProperty(transaction, Transaction.EFFECTIVE_AMOUNT, (BeanUtil.getPropLong(freezeTransaction, Transaction.EFFECTIVE_AMOUNT) - BeanUtil.getPropLong(consumeRefundInfoMap, Transaction.EFFECTIVE_AMOUNT)));
        //计算
        long diffAmount = PaymentUtil.updateSqbDepositPaymentsAmountForRefundSuccess(freezePayments, refundPayments);
        if (diffAmount > 0) {
            externalServiceFacade.sendWarnTalk("收钱吧预授权免充值异常", String.format("tsn:%s,diffAmount:%d", transaction.get(Transaction.TSN), diffAmount));
        }
        //写入实际数据
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, freezePayments);
        long sumMchAmount = PaymentUtil.sumMchAmount(freezePayments);
        if (sumMchAmount > 0) {
            long paidAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) - sumMchAmount;
            BeanUtil.setNestedProperty(transaction, Transaction.PAID_AMOUNT, paidAmount);
            BeanUtil.setNestedProperty(transaction, Transaction.RECEIVED_AMOUNT, paidAmount);
        }
    }

    /**
     * 预授权完成 - 收钱吧扫码预授权手续费处理
     *
     * @param context
     */
    private void setFinishBySqbDepositFee(TransactionContext context, Map<String, Object> freezeTransaction, boolean sqbDeposit) {
        if (!sqbDeposit) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> consumeTradeParams = context.getServiceProvider().getTradeParams(transaction);
        long consumeFee = BeanUtil.getPropLong(consumeTradeParams, TransactionParam.FEE);
        Object refundFeeObject = BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.REAL_TRADE_FEE);
        Map<String, Object> freezeTradeParams = context.getServiceProvider().getTradeParams(freezeTransaction);
        long refundFee;
        long freezeFee = BeanUtil.getPropLong(freezeTradeParams, TransactionParam.FEE);
        //通道返回退款手续费时，重新计算预授权完成部分手续费。即: 完成手续费 = 冻结手续费 - 退款手续费
        if (Objects.nonNull(refundFeeObject)) {
            refundFee = BeanUtil.getPropLong(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.REAL_TRADE_FEE);
            consumeFee = freezeFee - refundFee;
        }
        //通道未返回退款手续费时，重新计算预授权退款部分手续费。即: 退款手续费 = 冻结手续费 - 完成手续费
        else {
            refundFee = freezeFee - consumeFee;
        }
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_DEPOSIT_CONSUME_REFUND_INFO + "." + TransactionParam.FEE, refundFee);
        BeanUtil.setNestedProperty(consumeTradeParams, TransactionParam.FEE, consumeFee); //重写预授权完成手续费
    }

    private long getAmount(Map<String, Object> transaction, String amountKey, long defaultAmount) {
        Object amountObj = BeanUtil.getNestedProperty(transaction, amountKey);
        if (Objects.nonNull(amountObj)) {
            return BeanUtil.getPropLong(transaction, amountKey);
        }
        return defaultAmount;
    }

    private Map<String, Object> getFreezeTransaction(Map<String, Object> order) {
        final String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        Map<String, Object> freezeTransaction = repository.getFreezeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
        if (Objects.isNull(freezeTransaction)) {
            freezeTransaction = gatewaySupportService.getTransactionByClientTsn(merchantId, BeanUtil.getPropString(order, Order.SN), BeanUtil.getPropString(order, Order.CLIENT_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        return freezeTransaction;
    }

    /**
     * 是否关闭记录log并设置富友更新flag
     *
     * @param transactionUpdates
     * @param context
     * @return true if log should be disabled, false otherwise
     */
    private boolean disableLogAndSetFuyouFeeFlag(Map<String, Object> transactionUpdates, TransactionContext context) {
        Integer provider = context.getServiceProvider().getProvider();
        // 富友结算通道部分完成，不写log
        if (Objects.equals(provider, Order.PROVIDER_FUYOU)) {
            //富友非收钱吧类的预授权完成是需要等回调通知的 不应该直接写手续费
            if (!depositProvider.sqbDeposit(context)) {
                return true;
            }
            long effectiveAmount = BeanUtil.getPropLong(context.getTransaction(), Transaction.KEY_DEPOSIT_REFUND_EFFECTIVE_AMOUNT);
            if (effectiveAmount > NumberUtils.LONG_ZERO) {
                return true;
            }
            //富友预授权全部完成，手续费是否更新设置为True
            if (Objects.nonNull(transactionUpdates)) {
                BeanUtil.setNestedProperty(transactionUpdates, Transaction.KEY_IS_FEE_UPDATE_FLAG, true);
            }
        } else {
            //其它通道以是否异步同步余额服务为准
            return context.asyncWalletLog();
        }
        return false;
    }

    /**
     * 重置手续费
     *
     * @param context
     * @param order
     * @param transaction
     */
    public void resetTradeFeeRate(TransactionContext context, Map<String, Object> order, Map<String, Object> transaction) {
        //授权完成 - 收钱吧扫码预授权payments处理
        boolean sqbDeposit = depositProvider.sqbDeposit(context);
        Map<String, Object> freezeTransaction = null;
        if (sqbDeposit) {
            freezeTransaction = getFreezeTransaction(order);
            setFinishBySqbDepositPayments(context, freezeTransaction);
        }
        PaymentUtil.updateOrderPaymentsNetAmountForCancelSuccess((List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH));
        PaymentUtil.updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(order, transaction);
        //免充值交易，重新计算手续费
        if (UpayUtil.isChannelNotTopUp(transaction)
                && !(context.getServiceProvider() instanceof DirectWeixinServiceProvider
                || context.getServiceProvider() instanceof DirectWeixinWapOrMiniServiceProvider)) {
            long fee = FeeUtil.reCalculatePayOrPrecreateFee(transaction);
            Map<String,Object> tradeParams = context.getServiceProvider().getTradeParams(transaction);
            tradeParams.put(TransactionParam.FEE, fee);
        }
        //预授权完成 - 收钱吧预授权手续费计算
        setFinishBySqbDepositFee(context, freezeTransaction, sqbDeposit);
        //设置额度减免手续费
        FeeRateProcessor.setPayQuotaFee(transaction);
    }
}
