package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 网联微信支付
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2018/5/24.
 */
public class UnionPayWeixinServiceProvider extends WeixinServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(UnionPayWeixinServiceProvider.class);

    public static final String NAME = "provider.unionpay.weixin";



    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError {
        removeIllegalFields(request);
        return client.call(serviceUrl, WeixinClient.SIGN_TYPE_UNIONPAY, getPrivateKeyContent((String)config.get(TransactionParam.UNION_PAY_PRIVATE_KEY)), null, request);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_UNIONPAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE)) ? true : false;
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHANNEL_ID, config.get(TransactionParam.UNION_PAY_CHANNEL_ID));
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.UNION_PAY_WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.UNION_PAY_WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID));
        return builder;
    }

}
