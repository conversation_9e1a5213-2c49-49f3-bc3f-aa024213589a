package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.cibbank.BusinessFields;
import com.wosai.mpay.api.cibbank.CIBBankConstants;
import com.wosai.mpay.api.cibbank.ProtocolFields;
import com.wosai.mpay.api.cibbank.RequestBuilder;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.SystemConfigService;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by jianfree on 18/9/16.
 */
public class DefaultSwiftPassServiceProvider extends AbstractSwiftPassServiceProvider {
    public static final String NAME = "provider.swiftpass";

    @Autowired
    private SystemConfigService systemConfigService;

    public DefaultSwiftPassServiceProvider(){
        this.logger = LoggerFactory.getLogger(DefaultSwiftPassServiceProvider.class);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, getTradeParamKey(transaction));
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(String service, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARSET, CIBBankConstants.CHARSET_UTF8);
        builder.set(ProtocolFields.VERSION, CIBBankConstants.VERSION);
        builder.set(ProtocolFields.SERVICE, service);
        String sysProviderId = MapUtil.getString(config, TransactionParam.SWIFTPASS_ALIPAY_SYS_PROVIDER_ID);
        if(!StringUtil.empty(sysProviderId)){
            builder.set(BusinessFields.SYS_PROVIDER_ID, sysProviderId);
        }
        String privateKey  = MapUtil.getString(config, TransactionParam.SWIFTPASS_PRIVATE_KEY);
        String mchId = MapUtil.getString(config, TransactionParam.SWIFTPASS_MCH_ID);
        String agentNo = MapUtil.getString(config, TransactionParam.SWIFTPASS_SIGN_AGENT_NO);
        String groupNo = MapUtil.getString(config, TransactionParam.SWIFTPASS_GROUP_NO);
        if(!StringUtil.empty(privateKey)){
            //rsa签名
            builder.set(ProtocolFields.SIGN_TYPE, CIBBankConstants.SIGN_TYPE_SHA256RSA);
            if(!StringUtil.empty(mchId)){
                builder.set(ProtocolFields.MCH_ID, mchId);
            }
            if(!StringUtil.empty(agentNo)){
                builder.set(ProtocolFields.SIGN_AGENT_NO, agentNo);
            }else if(!StringUtil.empty(groupNo)){
                builder.set(ProtocolFields.GROUP_NO, groupNo);
            }
        }else{
            //md5签名
            builder.set(ProtocolFields.SIGN_TYPE, CIBBankConstants.SIGN_TYPE_MD5);
            builder.set(ProtocolFields.MCH_ID, MapUtil.getString(config, TransactionParam.SWIFTPASS_MCH_ID));
            String agentKey = MapUtil.getString(config, TransactionParam.SWIFTPASS_SIGN_AGENT_KEY);
            if(!StringUtil.empty(agentNo) && !StringUtil.empty(agentKey)){
                builder.set(ProtocolFields.SIGN_AGENT_NO, agentNo);
            }else{
                String groupKey = MapUtil.getString(config, TransactionParam.SWIFTPASS_GROUP_KEY);
                if(!StringUtil.empty(groupNo) && !StringUtil.empty(groupKey)){
                    builder.set(ProtocolFields.GROUP_NO, groupNo);
                }
            }
        }
        return builder;
    }

    @Override
    public String getSignKey(Map<String, Object> config) {
        String privateKey  = MapUtil.getString(config, TransactionParam.SWIFTPASS_PRIVATE_KEY);
        String agentNo = MapUtil.getString(config, TransactionParam.SWIFTPASS_SIGN_AGENT_NO);
        String agentKey = MapUtil.getString(config, TransactionParam.SWIFTPASS_SIGN_AGENT_KEY);
        if(!StringUtil.empty(privateKey)){
            return privateKey;
        }else if(!StringUtil.empty(agentNo) && !StringUtil.empty(agentKey)){
            return agentKey;
        }else{
            String groupNo = MapUtil.getString(config, TransactionParam.SWIFTPASS_GROUP_NO);
            String groupKey = MapUtil.getString(config, TransactionParam.SWIFTPASS_GROUP_KEY);
            if(!StringUtil.empty(groupNo) && !StringUtil.empty(groupKey)){
                return groupKey;
            }else{
                return MapUtil.getString(config, TransactionParam.SWIFTPASS_MCH_KEY);
            }
        }
    }

    @Override
    public String getRefundUserId(Map<String, Object> config) {
        return BeanUtil.getPropString(config, TransactionParam.SWIFTPASS_MCH_ID);
    }



    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider(Map<String, Object> transaction) {
        String providerStr = BeanUtil.getPropString(transaction, Transaction.PROVIDER);
        if(!StringUtil.empty(providerStr)){
            return BeanUtil.getPropInt(transaction, Transaction.PROVIDER);
        }else{
            String key = getTradeParamKey(transaction);
            return getTradeParamsKeyProvider().get(key);
        }
    }

    public Integer getProvider() {
        return null;
    }

    private Map<String, Integer> getTradeParamsKeyProvider(){
        Map map = (Map) systemConfigService.getSystemConfigContentByName(UpayConstant.SYSTEM_CONFIG_NAME_TRADE_PARAMS_KEY_PROVIDER);
        if(map == null || map.isEmpty()){
            return new HashMap<>();
        }else{
            return map;
        }
    }
    private String getTradeParamKey(Map<String, Object> transaction){
        for(String tradeParamsKey: getTradeParamsKeyProvider().keySet()){
            //todo 新增一个provider后，如果不走此provider，则需要在下面的代码添加对应的key, 加速判断
            if(TransactionParam.LAKALA_TRADE_PARAMS.equals(tradeParamsKey) || TransactionParam.CIBBANK_TRADE_PARAMS.equals(tradeParamsKey)
                    || TransactionParam.CITICBANK_TRADE_PARAMS.equals(tradeParamsKey) || TransactionParam.CMCC_TRADE_PARAMS.equals(tradeParamsKey)
                    || TransactionParam.BESTPAY_TRADE_PARAMS.equals(tradeParamsKey) || TransactionParam.NUCC_TRADE_PARAMS.equals(tradeParamsKey)
                    || TransactionParam.UNION_PAY_TRADE_PARAMS.equals(tradeParamsKey) || TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS.equals(tradeParamsKey) ){
                continue;
            }
            Map config =  (Map<String, Object>) BeanUtil.getNestedProperty(transaction, StringUtils.join(Transaction.CONFIG_SNAPSHOT, ".", tradeParamsKey));
            if(config != null && !config.isEmpty() && BeanUtil.getPropString(config, TransactionParam.SWIFTPASS_MCH_ID) != null){
                return tradeParamsKey;
            }
        }
        return null;
    }

}
