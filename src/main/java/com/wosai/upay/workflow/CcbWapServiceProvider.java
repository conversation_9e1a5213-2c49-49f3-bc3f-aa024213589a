package com.wosai.upay.workflow;

import ccb.pay.api.util.CCBPayUtil;
import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.ccb.*;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2021/8/27 Time: 2:36 下午
 */
public abstract class CcbWapServiceProvider extends CcbServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(CcbWapServiceProvider.class);
    protected DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final long TIME_MILLS = 4 * 3600 * 1000;
    public static final String NAME = "provider.ccb.precreate";
    private static final Map<String, String> HEADERS = CollectionUtil.hashMap("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36");

    @Autowired
    private CcbClient ccbClient;

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String ccbMchId = MapUtil.getString(tradeParams, TransactionParam.CCB_MERCHANT_ID);
        String branchId = MapUtil.getString(tradeParams, TransactionParam.CCB_BRANCH_ID);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        String publicKeyId = MapUtil.getString(tradeParams, TransactionParam.CCB_PUBLIC_KEY);

        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Pair<String, String> timePair = getBeginAndEndDatetime(transaction);

        RequestBuilder builder = new RequestBuilder();
        builder.pfSet(BusinessFields.SYSID, CcbConstants.COPLFID);
        builder.set(BusinessFields.TX_CODE, CcbConstants.QUERY_TX_CODE);
        builder.set(BusinessFields.Mrch_ID, ccbMchId);
        builder.set(BusinessFields.TX_SPECIAL_EC, CcbConstants.TX_SPECIAL_EC_PAY);
        builder.set(BusinessFields.StDt_Tm, timePair.getLeft());
        builder.set(BusinessFields.EdDt_Tm, timePair.getRight());
        builder.set(BusinessFields.OnLn_Py_Txn_Ordr_ID, tsn);
        builder.set(BusinessFields.PAGE, CcbConstants.DEFAULT_PAGE);
        builder.set(BusinessFields.BRANCH_ID, branchId);

        logger.info("[建行查单参数]>>>>>>RequestBuilder: {}", JSON.toJSONString(builder));
        Map<String, Object> result;
        try {
            result = ccbClient.call(builder, url, getPrivateKeyContent(publicKeyId), HEADERS);
        } catch (Exception e) {
            logger.error("failed to call ccb wap query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        return buildQueryResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String merchantId = MapUtil.getString(tradeParams, TransactionParam.CCB_MERCHANT_ID);
        String posId = MapUtil.getString(tradeParams, TransactionParam.CCB_POS_ID);
        String branchId = MapUtil.getString(tradeParams, TransactionParam.CCB_BRANCH_ID);
        String publicKeyId = MapUtil.getString(tradeParams, TransactionParam.CCB_PUBLIC_KEY);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(NAME, OP_PRECREATE);

        RequestBuilder builder = new RequestBuilder();
        builder.pfSet(BusinessFields.COPLFID, CcbConstants.COPLFID);
        builder.set(BusinessFields.COPLFID, CcbConstants.COPLFID);
        builder.set(BusinessFields.MERCHANT_ID, merchantId);
        builder.set(BusinessFields.POS_ID, posId);
        builder.set(BusinessFields.BRANCH_ID, branchId);
        buildWapParams(builder, transaction);

        Map<String, Object> result;
        try {
            result = ccbClient.call(builder, url, getPrivateKeyContent(publicKeyId));
        } catch (Exception e) {
            LOGGER.error("failed to call ccb way", e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildPrepaidResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction
                , MapUtil.getLongValue(order, DaoConstants.CTIME));
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(payTransaction);

        Pair<String, String> timePair = getBeginAndEndDatetime(payTransaction);

        String publicKeyId = MapUtil.getString(tradeParams, TransactionParam.CCB_PUBLIC_KEY);
        String merchantId = MapUtil.getString(tradeParams, TransactionParam.CCB_MERCHANT_ID);
        String branchId = MapUtil.getString(tradeParams, TransactionParam.CCB_BRANCH_ID);
        long refundAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        String refundSn = MapUtil.getString(transaction, Transaction.TSN);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);

        RequestBuilder builder = new RequestBuilder();
        builder.pfSet(BusinessFields.SYSID, CcbConstants.COPLFID);
        builder.set(BusinessFields.TX_CODE, CcbConstants.REFUND_TX_CODE);
        builder.set(BusinessFields.REQUEST_SN, refundSn);
        builder.set(BusinessFields.Mrch_ID, merchantId);
        builder.set(BusinessFields.MONEY
                , com.wosai.mpay.util.StringUtils.cents2yuan(refundAmount));
        builder.set(BusinessFields.ORDER, orderSn);
        builder.set(BusinessFields.StDt_Tm, timePair.getLeft());
        builder.set(BusinessFields.EdDt_Tm, timePair.getRight());
        builder.set(BusinessFields.BRANCH_ID, branchId);
        builder.set(BusinessFields.REFUND_CODE, refundSn);

        Map<String, Object> result;
        try {
            result = ccbClient.call(builder, url,  getPrivateKeyContent(publicKeyId), HEADERS);
        } catch (Exception e) {
            LOGGER.error("failed to call ccb refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context);
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String ccbPublicKeyId = MapUtil.getString(tradeParams, TransactionParam.CCB_PUBLIC_KEY);
        Map<String, Object> ccbNotify = MapUtil.getMap(providerNotification, "ccbNotify");
        String sign = MapUtil.getString(ccbNotify, ResponseFields.SIGN);
        String ccbPublicKey = getPrivateKeyContent(ccbPublicKeyId);

        boolean isPass;
        try {
            String signStr = RsaSignature.getSignCheckContent(ccbNotify, ResponseFields.SIGN);
            CCBPayUtil ccbPayUtil = new CCBPayUtil();
            isPass = ccbPayUtil.verifyNotifySign(signStr, sign, ccbPublicKey);
        } catch (Exception e) {
            LOGGER.warn("[建设银行回调验签]验签异常, 异常栈: ", e);
            return null;
        }

        long type = MapUtil.getLongValue(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }

        if (context.isFakeRequest() || isPass) {
            return buildNotifyResult(context, ccbNotify);
        }

        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    protected abstract String buildPrepaidResult(Map<String, Object> result
            , TransactionContext context);

    private String buildNotifyResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        Map<String, Object> transaction = context.getTransaction();
        //交易金额
        long totalFee = MapUtil.getLongValue(result, ResponseFields.PAYMENT);

        transaction.put(Transaction.PAID_AMOUNT, totalFee);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        return Workflow.RC_PAY_SUCCESS;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Map<String, Object> transaction = context.getTransaction();
        String resultStr = MapUtil.getString(result, ResponseFields.RESULT);
        if (StringUtils.equalsIgnoreCase(resultStr, CcbConstants.WAP_QUERY_RESULT_SUCCESS)) {
            Map<String, Object> dataInfo = MapUtil.getMap(result, ResponseFields.DATA_INFO);
            Object listObj =  MapUtil.getObject(dataInfo, ResponseFields.LIST);
            if (Objects.isNull(listObj) || !(listObj instanceof List)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            List<Map<String, Object>> listDate = (List) listObj;
            if (listDate.isEmpty()) {
                return Workflow.RC_TRADE_CANCELED;
            }
            Map<String, Object> resultData =  listDate.get(0);
            if (MapUtils.isEmpty(resultData)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            String txnStatus = MapUtil.getString(resultData, ResponseFields.TXN_STATUS);
            if (StringUtils.equals(txnStatus, CcbConstants.WAP_QUERY_STATUS_SUCCESS)) {
                long amount = com.wosai.mpay.util.StringUtils.yuan2cents(MapUtil.getString(resultData, ResponseFields.AHN_TXNAMT));
                transaction.put(Transaction.PAID_AMOUNT, amount);
                transaction.put(Transaction.RECEIVED_AMOUNT, amount);
                //建行未返回用户信息，故使用接口上送的值
                transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(transaction, KEY_PAYER_UID));
                resolvePayFund(resultData, context);
                return Workflow.RC_PAY_SUCCESS;
            } if (CcbConstants.WAP_QUERY_PROCESSING_SET.contains(txnStatus)) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String resultStr = MapUtil.getString(result, ResponseFields.RESULT);
        String errorCode = MapUtil.getString(result, ResponseFields.ERROR_CODE);
        if (StringUtils.equals(resultStr, CcbConstants.WAP_QUERY_RESULT_SUCCESS)
                && StringUtils.equals(errorCode, StringUtils.EMPTY)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    protected Pair<String, String> getBeginAndEndDatetime(Map<String, Object> transaction) {
        long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
        long beginTimestamp = ctime - TIME_MILLS;
        long endTimestamp = ctime + TIME_MILLS;
        String beginTime = FORMATTER.format(LocalDateTime.ofInstant(Instant
                        .ofEpochMilli(beginTimestamp), ZoneId.systemDefault()));
        String endTime = FORMATTER.format(LocalDateTime.ofInstant(Instant
                .ofEpochMilli(endTimestamp), ZoneId.systemDefault()));
        return Pair.of(beginTime, endTime);
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result
            , TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String terminalCategory = MapUtil.getString(MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        boolean isMergeMessage = StringUtil.empty(terminalCategory);
        //建行预下单接口与查单接口返回的错误码相关字段值不一致
        String respCode = getOrDefault(result, ResponseFields.ERROR_CODE, ResponseFields.ERR_CODE);
        String respMsg = getOrDefault(result, ResponseFields.ERROR_MSG, ResponseFields.ERR_MSG);
        map.put(ResponseFields.ERROR_CODE, respCode);
        if (Objects.nonNull(respCode) && (StringUtils.equals(CcbConstants.RET_CODE_FAILURE, respCode) || StringUtils.equals(CcbConstants.RET_CODE_UNKNOWN, respCode))){
            respMsg = respMsg.replaceAll("[0-9]", StringUtils.EMPTY); //当retCode = -1/-2时，过滤错误信息里面的随机数
        }
        map.put(ResponseFields.ERROR_MSG, respMsg);

        com.wosai.upay.util.MapUtil.removeNullValues(map);

        BeanUtil.setNestedProperty(context.getTransaction(),
                UpayUtil.getProviderErrorInfoKey(key),
                map);

        if(Objects.equals(respCode, CcbConstants.WAP_CODE_SUCCESS)
                || Objects.equals(respCode, StringUtils.EMPTY)) {
            // 支付通道返回成功，清空原先设置的UpayBizError
            Map bizErrorCode = (Map) BeanUtil.getProperty(context.getTransaction()
                    , Transaction.BIZ_ERROR_CODE);
            if (null != bizErrorCode && bizErrorCode.containsKey(key)){
                bizErrorCode.remove(key);
            }
        }else {
            UpayBizError bizError = UpayBizError.getBizErrorByField(key, BeanUtil.getPropInt(context.getTransaction(), Transaction.PAYWAY), respCode, respMsg);

            String path = UpayUtil.getBizErrorCodeKey(key);
            if (bizError != null && !UpayBizError.UNEXPECTED_PROVIDER_ERROR
                    .getStandardName().equals(bizError.getStandardName())){
                BeanUtil.setNestedProperty(context.getTransaction(), path, bizError);
            } else {
                BeanUtil.setNestedProperty(context.getTransaction(), path,
                        UpayBizError.unexpectedProviderError(StringUtil.empty(respMsg)
                                ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : respMsg, isMergeMessage));
            }
        }
    }

    protected abstract void buildWapParams(RequestBuilder builder, Map<String, Object> transaction);


    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {

        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        if (extraParams == null) {
            return null;
        }
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.CCB_PUBLIC_KEY);
        String privateKeyContent = getPrivateKeyContent(sqbPrivateKey);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(NAME, OP_QUERY);

        //密钥
        String branchId = MapUtil.getString(tradeParams, TransactionParam.CCB_BRANCH_ID);

        //授权码
        String authCode = MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE);
        //银联支付标识
        String appUpIdentifier = MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER);

        RequestBuilder builder = new RequestBuilder();
        builder.pfSet(BusinessFields.SYSID, CcbConstants.COPLFID);
        builder.set(BusinessFields.CCB_IBS_VERSION, CcbConstants.CCB_IBS_VERSION);
        builder.set(BusinessFields.TX_CODE, CcbConstants.UNIONPAY_TX_CODE_V2);
        builder.set(BusinessFields.BRANCH_ID, branchId);

        builder.set(CcbConstants.AHN_CD, authCode);
        builder.set(CcbConstants.PY_INF_DSC, appUpIdentifier);

        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(builder, url, privateKeyContent, HEADERS);
        } catch (Exception ex) {
            logger.error("failed to call ccbUnionPayWap queryUserInfo", ex);
            return null;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        String userID = MapUtil.getString(result, CcbConstants.USR_ID);
        userID = URLEncoder.encode(userID);
        if (Objects.equals(CcbConstants.WAP_CODE_SUCCESS, responseCode)) {
            return CollectionUtil.hashMap(com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID, userID);
        }
        return null;
    }

    private Map<String, Object> retryIfNetworkException(RequestBuilder requestBuilder, String url, String secretKey, Map<String, String> headers) throws Exception {
        Exception exception = null;
        for (int i = 0; i < 3; ++i) {
            try {
                return ccbClient.call(requestBuilder, url, secretKey, HEADERS);
            } catch (Exception e) {
                exception = e;
                logger.warn("encountered ioex in ccbUnionPayWap queryUserInfo", e);

            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", 3));

        throw exception;
    }

}
