package com.wosai.upay.workflow;

import java.util.Map;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

public class BankcardDepositComsumeWorkflow  extends DepositConsumeOrderWorkflow{
    private static long[] delays = {7000, 3000, 5000, 5000, 5000, 5000, 5000};
    private static final String NAME = "generic.bankcard.deposit.consume.workflow";

    public BankcardDepositComsumeWorkflow() {
        super(delays);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME
                && MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_BANKCARD) {
            return true;
        }
        return false;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
