package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.alipay.RequestV2Builder;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@ServiceProvicerPriority(priority = 3)
public class TLUnionPayAlipayV2ServiceProvider extends UnionPayAlipayV2ServiceProvider{

    protected static final int NOTIFY_URL_LIMIT = 128;

    public static final Logger logger = LoggerFactory.getLogger(TLUnionPayAlipayV2ServiceProvider.class);
    public static final String NAME = "provider.tl.unionpay.alipay.v2";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_TL;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if((Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE)) {
            return getTradeParams(transaction) != null && getTradeCurrency(transaction).equals(TransactionParam.UPAY_DEFAULT_CURRENCY_CNY);
        }
        return false;
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(BusinessV2Fields.ORGID, BeanUtil.getPropString(config,TransactionParam.UNION_PAY_TL_ORGID));
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA2);
        String sysProviderId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_SYS_PROVIDER_ID);
        if(!StringUtil.empty(sysProviderId)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, sysProviderId
            ));
        }
        String alipaySubMchId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_ALIPAY_SUB_MCH_ID);
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(
                BusinessV2Fields.SUB_MERCHANT_ID, alipaySubMchId,
                BusinessV2Fields.MERCHANT_NAME, getSubMerchantName(config, transaction)
        ));

        setTerminalInfo(context, MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), builder);

        return builder;
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

}
