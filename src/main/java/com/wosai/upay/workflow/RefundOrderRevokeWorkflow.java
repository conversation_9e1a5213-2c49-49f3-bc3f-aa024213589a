package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.MachineBuilder;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.PaymentUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class RefundOrderRevokeWorkflow extends Workflow {
    private static final Logger logger = LoggerFactory.getLogger(RefundOrderRevokeWorkflow.class);

    public static final String NAME = "generic.refund.revoke.workflow";

    public RefundOrderRevokeWorkflow() {
        MachineBuilder builder = new MachineBuilder();
        machine = builder.build();
    }

    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_REFUND_REVOKE) {
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        throw new UnsupportedOperationException();
    }


    @SuppressWarnings("unchecked")
    @Override
    public String finish(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        long netOriginal = BeanUtil.getPropLong(order, Order.NET_ORIGINAL);
        long totalOriginal = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL);
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long netEffective = BeanUtil.getPropLong(order, Order.NET_EFFECTIVE);
        long totalEffective = BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL);

        if (netOriginal +  originalAmount < totalOriginal) {
            order.put(Order.NET_ORIGINAL, netOriginal + originalAmount);
            order.put(Order.NET_EFFECTIVE, netEffective + effectiveAmount);
            order.put(Order.STATUS, Order.STATUS_PARTIAL_REFUNDED);
        }else{
            order.put(Order.NET_ORIGINAL, totalOriginal);
            order.put(Order.NET_EFFECTIVE, totalEffective);
            order.put(Order.STATUS, Order.STATUS_PAID);
        }
        PaymentUtil.updateOrderPaymentsNetAmountForRevokeRefundSuccess(
                (List<Map<String, Object>>)BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH),
                (List<Map<String, Object>>)BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH)
        );
        PaymentUtil.updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(order, transaction);
        transaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);

        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS));


        Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                Order.NET_ORIGINAL, order.get(Order.NET_ORIGINAL),
                Order.NET_EFFECTIVE, order.get(Order.NET_EFFECTIVE),
                Order.ITEMS, order.get(Order.ITEMS),
                Order.STATUS, order.get(Order.STATUS));

        boolean isHistoryRefund = BeanUtil.getPropBoolean(context.getTransaction(), Transaction.KEY_IS_HISTORY_TRADE_REFUND, false);
        // 历史退款交易需要更新hbase
        if(isHistoryRefund){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
            orderUpdate = null;
        }
        finishTransaction(context, orderUpdate, transactionUpdates);

        //not to notify trade corprocessor
        logger.debug("TID {} refundRevoke finished", context.getTid());
        return null;
    }

}
