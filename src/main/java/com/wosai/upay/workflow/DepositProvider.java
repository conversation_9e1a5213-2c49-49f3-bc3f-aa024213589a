package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * Description: 收钱吧预授权
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/10/25
 */
public class DepositProvider implements MpayServiceProvider {
    @Resource
    private SqbDepositProvider sqbDepositProvider;

    /**
     * 预授权完成
     *
     * @param context
     * @return
     */
    @Override
    public String depositConsume(TransactionContext context) {
        return matchServiceProvider(context).depositConsume(context);
    }

    @Override
    public String depositCancel(TransactionContext context) {
        return matchServiceProvider(context).depositCancel(context);
    }

    @Override
    public String depositQuery(TransactionContext context) {
        return matchServiceProvider(context).depositQuery(context);
    }

    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        return matchServiceProvider(context).depositFreeze(context, resume);
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        return matchServiceProvider(context).depositPreFreeze(context, resume);
    }

    public boolean sqbDeposit(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Object depositType = BeanUtil.getNestedProperty(transaction, Transaction.KEY_DEPOSIT_TYPE);
        //收钱吧预授权
        return Objects.equals(depositType, TransactionParam.DEPOSIT_SQB);
    }

    public MpayServiceProvider matchServiceProvider(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Object depositType = BeanUtil.getNestedProperty(transaction, Transaction.KEY_DEPOSIT_TYPE);
        if (Objects.equals(depositType, TransactionParam.DEPOSIT_SQB)) {
            return sqbDepositProvider;
        }
        return context.getServiceProvider();
    }

    //-----------------　无需实现的方法　-----------------------
    @Override
    public String getName() {
        return null;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return false;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String cancel(TransactionContext context) {
        return null;
    }

    @Override
    public String query(TransactionContext context) {
        return matchServiceProvider(context).query(context);
    }

    @Override
    public String refund(TransactionContext context) {
        return matchServiceProvider(context).refund(context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    @Override
    public String depositSync(TransactionContext context) {
        return null;
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        return null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return null;
    }
}
