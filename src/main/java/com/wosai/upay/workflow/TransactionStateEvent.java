package com.wosai.upay.workflow;

import com.wosai.eventbus.Event;

public abstract class TransactionStateEvent implements Event {

    private int key;
    private TransactionContext context;

    public TransactionStateEvent(int key, TransactionContext context) {
        this.key = key;
        this.context = context;
    }

    @Override
    public int getKey() {
        return key;
    }

    public TransactionContext getContext() {
        return context;
    }
}
