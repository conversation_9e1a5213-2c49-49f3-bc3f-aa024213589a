package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.jycard.*;
import com.wosai.mpay.api.jycard.enums.IdTypeEnum;
import com.wosai.mpay.api.jycard.enums.JyCardOrderStatusEnum;
import com.wosai.mpay.api.jycard.enums.JyCardSystemErrorEnum;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.ExternalServiceException;
import com.wosai.upay.exception.ProviderStatusException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.GatewayNodeNotifier;
import com.wosai.upay.service.SupportService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.signature.service.JyCardService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.constant.UpayConstant.*;
import static com.wosai.constant.UpayErrorScenesConstant.*;
import static com.wosai.mpay.api.jycard.JyCardConstants.*;
import static com.wosai.mpay.api.jycard.JyCardProtocolFields.*;
import static com.wosai.mpay.util.StringUtils.cents2yuan;

/**
 * <AUTHOR>
 * @description 锦医一卡通
 * @date 2024/9/7
 */
@Slf4j
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class JyCardProvider extends AbstractServiceProvider {
    public static final String NAME = "provider.jycard";
    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    @Resource
    protected GatewayNodeNotifier gatewayNodeNotifier;

    //锦医一卡通访问令牌缓存key
    private static final String JY_CARD_ACCESS_TOKEN_CACHE_KEY = "jycard_access_token:";

    @Resource
    private StringRedisTemplate redisTemplate;

    @Resource
    private JyCardClient jyCardClient;

    @Resource
    private JyCardService jyCardService;

    @Resource
    protected DataRepository repository;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payWay = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_CAMPUS_CARD != payWay) {
            //锦医一卡通目前只支持校园卡
            return false;
        }
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.JYCARD_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_JY_CARD;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("锦医一卡通暂不支持被扫支付");
    }

    /**
     * 预下单
     *
     * @param context
     * @param resume
     * @return
     */
    @Override
    @SuppressWarnings("unchecked")
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        //需要返回给前端的参数, 都放入wapPayRequest中
        Map<String, Object> wapPayRequest = new HashMap<>(8);
        //前端根据返回的provider来判断是否需要弹框让用户输入用户名和密码
        wapPayRequest.put(Transaction.PROVIDER, getProvider());
        wapPayRequest.put(Transaction.MERCHANT_ID, MapUtils.getString(transaction, Transaction.MERCHANT_ID));
        wapPayRequest.put(Transaction.ORDER_SN, MapUtils.getString(transaction, Transaction.ORDER_SN));
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapPayRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public Map<String, Object> confirmPay(Map<String, Object> request, Map<String, Object> transaction) {
        initTransactionSomeValue(transaction);
        String merchantId = MapUtils.getString(transaction, Transaction.MERCHANT_ID);
        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        String token;
        try {
            //获取token
            token = getAccessTokenFromCache(transaction);
        } catch (Exception e) {
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING);
        }

        Map<String, Object> accountInfo;
        try {
            //校验密码并获取账号信息
            accountInfo = checkPassword(token, request, transaction);
        } catch (ProviderStatusException e) {
            throw e;
        } catch (Exception e) {
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, e.getMessage());
        }

        try {
            //通知账号信息
            notifyAccountInfo(tsn, accountInfo);

            //检查账号信息是否已持久化到DB
            checkAccountInfoSavedToDB(merchantId, tsn);
        } catch (Exception e) {
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, e.getMessage());
        }

        Map<String, Object> paySuccessResult;
        try {
            //支付
            Map<String, Object> jycardPayResponse = providerPay(token, accountInfo, transaction);

            if (!isPayOrRefundSuccess(jycardPayResponse, OP_PAY)) {
                log.warn("{}: 支付失败, tsn={}", NAME, tsn);
                //通知失败失败
                notifyPayResult(tsn, CommonResponse.FAIL);
                throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING);
            }

            //请求成功，解析支付结果
            paySuccessResult = parsePayResultFromJyCardResponse(jycardPayResponse);
            //回调通知支付结果
            notifyPayResult(tsn, CommonResponse.SUCCESS);

            Map<String, Object> result = new HashMap<>();
            result.put(Transaction.ORDER_SN, tsn);
            result.put(Transaction.TRADE_NO, MapUtils.getString(paySuccessResult, JyCardBusinessFields.TRADE_NO));
            return result;
        } catch (Exception e) {
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, e.getMessage());
        }
    }

    private void checkAccountInfoSavedToDB(String merchantId, String orderSn) {
        Map<String, Object> transaction = dataRepository.getPayTransactionByOrderSn(merchantId, orderSn);
        if (null == transaction) {
            logger.error("{}: 未查到交易流水日志, merchantId={}, orderSn={}", NAME, merchantId, orderSn);
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, "订单不存在");
        }

        String accountId = getAccountId(transaction);
        if (StringUtils.isEmpty(accountId)) {
            logger.error("{}: 账号信息未持久化, 不能进行支付, merchantId={}, orderSn={}", NAME, merchantId, orderSn);
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, "账号信息未持久化");
        }
    }

    /**
     * 同步账号信息到transaction表
     *
     * @param transaction
     */
    private void syncAccountInfoToDB(Map<String, Object> transaction) {
        String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        try {
            log.info("{}: 保存账号信息到DB, tsn={}, merchantId={}", NAME, tsn, merchantId);
            Map<String, Object> transactionUpdatePart = MapUtil.hashMap(DaoConstants.ID, transaction.get(DaoConstants.ID),
                    Transaction.MERCHANT_ID, merchantId,
                    Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                    Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                    DaoConstants.MTIME, System.currentTimeMillis()
            );
            repository.getTransactionDao().updatePart(transactionUpdatePart);
        } catch (Exception e) {
            log.error("{}: 保存账号信息到DB异常, merchantId={}, tsn={}, error={}", NAME, merchantId, tsn, e.getMessage(), e);
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "保存账号信息到DB异常");
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        String opType = OP_QUERY;

        String accountId = getAccountId(transaction);
        if (StringUtils.isEmpty(accountId)) {
            //查不到账号信息, 说明此时消费者还没交易过
            return Workflow.RC_IN_PROG;
        }

        String token;
        try {
            //获取token
            token = getAccessTokenFromCache(transaction);
        } catch (Exception e) {
            return Workflow.RC_IN_PROG;
        }

        Map<String, Object> jyCardResponse;
        try {
            //查询订单信息
            jyCardResponse = providerQuery(token, accountId, transaction);
        } catch (Exception e) {
            setTransactionContextErrorInfo(context, opType, e);
            return Workflow.RC_IOEX;
        }

        try {
            if (!isQuerySuccess(jyCardResponse)) {
                //订单
                String errorCode = getSystemErrorCode(jyCardResponse);
                String errorMsg = getBizErrorMsg(jyCardResponse);
                setTransactionContextErrorInfo(transaction, opType, jyCardResponse, false, errorCode, errorMsg);
                return Workflow.RC_ERROR;
            }

            //请求成功，解析查询结果
            Map<String, Object> orderInfo = parseQueryResultFromJyCardResponse(jyCardResponse);
            setTransactionContextOrderErrorInfo(orderInfo, transaction, opType);
            return buildQueryResult(orderInfo, transaction, opType);
        } catch (Exception e) {
            setTransactionContextErrorInfo(context, opType, e);
            return Workflow.RC_IN_PROG;
        }
    }

    /**
     * 构建buyerUid
     * 格式：providerMchId_accountId
     *
     * @param transaction
     * @param accountInfo
     * @return
     */
    private String buildBuyerUid(Map<String, Object> transaction, Map<String, Object> accountInfo) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        //获取通道侧的商户号
        String providerMchId = MapUtils.getString(tradeParams, TransactionParam.JYCARD_PROVIDER_MCH_ID);
        String accountId = MapUtils.getString(accountInfo, JyCardBusinessFields.ACCOUNT);
        return providerMchId + "_" + accountId;
    }

    /**
     * 获取accountId
     * 格式：providerMchId_accountId
     *
     * @param transaction
     * @return
     */
    private String getAccountId(Map<String, Object> transaction) {
        String buyerId = MapUtils.getString(transaction, Transaction.BUYER_UID);
        if (StringUtils.isEmpty(buyerId)) {
            return null;
        }
        String[] info = buyerId.split("_");
        return info.length == 1 ? info[0] : info[1];
    }

    /**
     * 根据用户名和密码获取到账号信息，回调通知
     *
     * @param orderSn
     * @param accountInfo
     * @return
     */
    private void notifyAccountInfo(String orderSn, Map<String, Object> accountInfo) {
        //通知地址
        String notifyUrl = getUpayDispatcherNotifyUrl(orderSn);

        Map<String, Object> notification = new HashMap<>();
        notification.put(UpayConstant.CLIENT_NOTIFY_STATUS, CommonResponse.IN_PROCESS);
        notification.put(Transaction.ORDER_SN, orderSn);
        notification.put(CLIENT_NOTIFY_ACCOUNT_INFO, accountInfo);

        //通知账号信息
        try {
            notifyWithRetry(notifyUrl, orderSn, notification);
        } catch (Exception e) {
            log.error("通知账号信息失败, orderSn={}", orderSn);
            throw e;
        }
    }

    /**
     * 支付结果回调通知
     *
     * @param orderSn
     * @return
     */
    private void notifyPayResult(String orderSn, String notifyStatus) {
        //通知地址
        String notifyUrl = getUpayDispatcherNotifyUrl(orderSn);

        Map<String, Object> notification = new HashMap<>();
        notification.put(UpayConstant.CLIENT_NOTIFY_STATUS, notifyStatus);
        notification.put(Transaction.ORDER_SN, orderSn);

        try {
            notifyWithRetry(notifyUrl, orderSn, notification);
        } catch (Exception e) {
            throw new ProviderStatusException(ORDER_PAY_FAILED_ERROR, ORDER_PAY_FAILED_ERROR_MESSAGE);
        }
    }

    private String buildQueryResult(Map<String, Object> orderInfo, Map<String, Object> transaction, String opType) {
        String retCode = MapUtils.getString(orderInfo, JyCardResponseFields.BIZ_ERROR_CODE);
        if (!JyCardResponseUtil.isSuccess(retCode)) {
            String errorMsg = MapUtils.getString(orderInfo, JyCardResponseFields.BIZ_ERROR_MSG);
            log.warn("{}: 查询订单状态失败, tsn={}, opType={}, retCode={}, error={}",
                    NAME, MapUtils.getString(transaction, Transaction.TSN), opType, retCode, errorMsg);
            return Workflow.RC_ERROR;
        }

        String orderStatus = MapUtils.getString(orderInfo, JyCardBusinessFields.ORDER_STATUS);
        JyCardOrderStatusEnum statusEnum = JyCardOrderStatusEnum.of(orderStatus);
        if (JyCardOrderStatusEnum.PROCESSING == statusEnum || JyCardOrderStatusEnum.ORDER_NOT_EXIST == statusEnum) {
            return Workflow.RC_IN_PROG;
        }

        //订单状态未成功
        if (!isOrderStatusSuccess(statusEnum)) {
            return Workflow.RC_ERROR;
        }

        //解析通道返回的交易信息
        resolveTradeFund(transaction, orderInfo);

        int type = MapUtil.getInteger(transaction, Transaction.TYPE);
        return getWorkflowSuccessByType(type);
    }

    private String getWorkflowSuccessByType(int type) {
        if (Transaction.TYPE_PAYMENT == type) {
            return Workflow.RC_PAY_SUCCESS;
        }
        if (Transaction.TYPE_REFUND == type) {
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_CANCEL_SUCCESS;
    }

    @Override
    public String refund(TransactionContext context) {
        return doRefund(context, OP_REFUND);
    }

    private String doRefund(TransactionContext context, String opType) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        String token;
        try {
            //获取token
            token = getAccessTokenFromCache(transaction);
        } catch (Exception e) {
            setTransactionContextErrorInfo(context, opType, e);
            return Workflow.RC_IOEX;
        }

        Map<String, Object> jyCardResponse;
        try {
            //查询订单信息
            jyCardResponse = providerRefund(token, transaction);
        } catch (Exception e) {
            setTransactionContextErrorInfo(context, opType, e);
            return Workflow.RC_IOEX;
        }

        try {
            if (!isPayOrRefundSuccess(jyCardResponse, opType)) {
                //退款失败
                String errorCode = getSystemErrorCode(jyCardResponse);
                String errorMsg = getBizErrorMsg(jyCardResponse);
                setTransactionContextErrorInfo(transaction, opType, getProviderError(jyCardResponse), false, errorCode, errorMsg);
                return Workflow.RC_ERROR;
            }

            //退款
            Map<String, Object> refundResult = parseRefundResultFromJyCardResponse(jyCardResponse);
            setTransactionContextErrorInfo(refundResult, transaction, opType);
            resolveTradeFund(transaction, refundResult);
            return OP_REFUND.equals(opType) ? Workflow.RC_REFUND_SUCCESS : Workflow.RC_CANCEL_SUCCESS;
        } catch (Exception e) {
            setTransactionContextErrorInfo(transaction, opType, e);
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("锦医一卡通暂不支持撤单");
    }

    /**
     * 保存通道的交易信息
     *
     * @param transaction
     * @param providerTradeResult
     */
    private void resolveTradeFund(Map<String, Object> transaction, Map<String, Object> providerTradeResult) {
        //记录通道侧交易单号
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(providerTradeResult, JyCardBusinessFields.TRADE_NO);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }

        //记录通道交易完成时间
        if (Objects.isNull(transaction.get(Transaction.CHANNEL_FINISH_TIME))) {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        }

        //记录交易金额
        String amount = MapUtils.getString(providerTradeResult, JyCardBusinessFields.TRANAMT);
        if (StringUtils.isEmpty(amount)) {
            log.error("{}: 响应结果中的金额无效, providerTradeResult={}", NAME, JacksonUtil.toJsonString(providerTradeResult));
            return;
        }
        long tranAmt = com.wosai.mpay.util.StringUtils.yuan2cents(amount); //交易金额
        transaction.put(Transaction.PAID_AMOUNT, tranAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, tranAmt);
    }

    //账户信息更新到交易流水中
    private void setTradeNoBuyerInfoIfExists(Map<String, Object> transaction, Map<String, Object> accountInfo) {
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerUid = buildBuyerUid(transaction, accountInfo);
            if (!StringUtil.empty(buyerUid)) {
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerLogin = MapUtil.getString(accountInfo, JyCardBusinessFields.SNO);
            if (!StringUtil.empty(buyerLogin)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerLogin);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public void setTransactionContextErrorInfo(UpayException exception, Map<String, Object> transaction, String key) {
        setTransactionContextErrorInfo(transaction, key, exception.getData(), false, exception.getCode(), exception.getMessage());
    }

    public void setTransactionContextErrorInfo(Map<String, Object> result, Map<String, Object> transaction, String key) {
        setTransactionContextErrorInfo(transaction, key, getProviderError(result), true, null, null);
    }

    public void setTransactionContextOrderErrorInfo(Map<String, Object> orderInfo, Map<String, Object> transaction, String key) {
        String orderStatus = MapUtils.getString(orderInfo, JyCardBusinessFields.ORDER_STATUS);
        JyCardOrderStatusEnum statusEnum = JyCardOrderStatusEnum.of(orderStatus);
        if (JyCardOrderStatusEnum.PROCESSING == statusEnum || JyCardOrderStatusEnum.ORDER_NOT_EXIST == statusEnum) {
            return;
        }

        String retCode = MapUtils.getString(orderInfo, JyCardResponseFields.BIZ_ERROR_CODE);
        String errorMsg = MapUtils.getString(orderInfo, JyCardResponseFields.BIZ_ERROR_MSG);

        boolean isSuccess = isOrderStatusSuccess(statusEnum);
        setTransactionContextErrorInfo(transaction, key, getProviderError(orderInfo), isSuccess, retCode, errorMsg);
    }

    @Override
    @SuppressWarnings("unchecked")
    public String explainNotification(Map<String, Object> notification) {
        TransactionContext context = (TransactionContext) notification.get(TransactionContext.class.getName());
        notification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT != type) {
            //只处理支付交易
            log.error("{}: explainNotification, 不支持的交易类型, type={}, tsn={}", NAME, type, MapUtil.getString(transaction, Transaction.TSN));
            return null;
        }

        try {
            //回调通知账号信息
            Map<String, Object> accountInfo = MapUtils.getMap(notification, CLIENT_NOTIFY_ACCOUNT_INFO);

            //保存账号信息
            setTradeNoBuyerInfoIfExists(transaction, accountInfo);

            //持久化账号信息到DB
            syncAccountInfoToDB(transaction);
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 获取访问令牌
     *
     * @param transaction
     * @return
     */
    private String getAccessTokenFromCache(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appKey = MapUtils.getString(tradeParams, TransactionParam.JYCARD_APP_KEY);

        String accessToken = redisTemplate.opsForValue().get(buildAccessTokenCacheKey(appKey));
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }

        log.info("{}: 获取accessToken, tsn={}", NAME, MapUtils.getString(transaction, Transaction.TSN));
        return getAccessToken(transaction, tradeParams);
    }

    private String getAccessToken(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String appKey = MapUtils.getString(tradeParams, TransactionParam.JYCARD_APP_KEY);
        Map<String, Object> accessTokenResult;

        try {
            accessTokenResult = jyCardService.queryAccessToken(Collections.singletonMap(JyCardProtocolFields.APP_KEY, appKey));
        } catch (Exception e) {
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "获取accessToken异常:" + e.getMessage());
        }

        try {
            //放入缓存
            String accessToken = MapUtils.getString(accessTokenResult, ACCESS_TOKEN);
            long expiresIn = MapUtils.getLongValue(accessTokenResult, JyCardResponseFields.EXPIRES_IN);
            //失效时间缩短几分钟，这样可以在失效前就重新向锦医一卡通请求新的token
            expiresIn -= ACCESS_TOKEN_EXPIRE_TIME_BUFFER;
            log.info("{}: 获取最新accessToken, expireSeconds={}, tsn={}", NAME, expiresIn, tsn);
            String cacheKey = buildAccessTokenCacheKey(appKey);
            redisTemplate.opsForValue().set(cacheKey, accessToken, expiresIn, TimeUnit.SECONDS);
            return accessToken;
        } catch (Exception e) {
            log.error("{}: 获取accessToken异常, tsn={}, error={}", NAME, tsn, e.getMessage(), e);
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "获取accessToken异常:" + e.getMessage());
        }
    }

    private String buildAccessTokenCacheKey(String appKey) {
        return JY_CARD_ACCESS_TOKEN_CACHE_KEY + appKey;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseAccountFromJyCardResponse(Map<String, Object> jyCardResponse, String tsn) {
        Map<String, Object> result = MapUtils.getMap(jyCardResponse, JyCardBusinessFields.REQUEST_OBJ_CHECK_PWD);
        String retCode = MapUtils.getString(result, JyCardResponseFields.BIZ_ERROR_CODE);
        if (!JyCardResponseUtil.isSuccess(retCode)) {
            String errorMsg = MapUtils.getString(result, JyCardResponseFields.BIZ_ERROR_MSG);
            log.warn("{}: 校验用户名和密码失败, tsn={}, retCode={}, error={}", NAME, tsn, retCode, errorMsg);
            throw new ProviderStatusException(retCode, "校验用户名和密码失败:" + errorMsg, result);
        }

        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put(JyCardBusinessFields.ACCOUNT, MapUtils.getString(result, JyCardBusinessFields.ACCOUNT));
        accountInfo.put(JyCardBusinessFields.IDENTITY_ID, MapUtils.getString(result, JyCardBusinessFields.IDENTITY_ID));
        accountInfo.put(JyCardBusinessFields.SNO, MapUtils.getString(result, JyCardBusinessFields.SNO));
        return accountInfo;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parsePayResultFromJyCardResponse(Map<String, Object> jyCardResponse) {
        return MapUtils.getMap(jyCardResponse, JyCardBusinessFields.REQUEST_OBJ_PAY);
    }

    private boolean isQuerySuccess(Map<String, Object> jyCardResponse) {
        //获取系统错误码
        String systemErrorCode = getSystemErrorCode(jyCardResponse);
        if (!JyCardResponseUtil.isSuccess(systemErrorCode)) {
            return false;
        }

        Map<String, Object> result = MapUtils.getMap(jyCardResponse, JyCardBusinessFields.REQUEST_OBJ_ORDER_QUERY);
        List<Map<String, Object>> orderList = (List<Map<String, Object>>) MapUtils.getObject(result, JyCardBusinessFields.QUERYS);
        return CollectionUtils.isNotEmpty(orderList);
    }

    private boolean isPayOrRefundSuccess(Map<String, Object> jyCardResponse, String opType) {
        //获取系统错误码
        String systemErrorCode = getSystemErrorCode(jyCardResponse);
        if (!JyCardResponseUtil.isSuccess(systemErrorCode)) {
            return false;
        }

        String resultKey = opType.equals(OP_PAY) ? JyCardBusinessFields.REQUEST_OBJ_PAY : JyCardBusinessFields.REQUEST_OBJ_REFUND;
        Map<String, Object> result = MapUtils.getMap(jyCardResponse, resultKey);
        //获取业务错误码
        String retCode = MapUtils.getString(result, JyCardResponseFields.BIZ_ERROR_CODE);
        return JyCardResponseUtil.isSuccess(retCode);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseRefundResultFromJyCardResponse(Map<String, Object> jyCardResponse) {
        return MapUtils.getMap(jyCardResponse, JyCardBusinessFields.REQUEST_OBJ_REFUND);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseQueryResultFromJyCardResponse(Map<String, Object> jyCardResponse) {
        Map<String, Object> result = MapUtils.getMap(jyCardResponse, JyCardBusinessFields.REQUEST_OBJ_ORDER_QUERY);
        List<Map<String, Object>> orderList = (List<Map<String, Object>>) MapUtils.getObject(result, JyCardBusinessFields.QUERYS);

        Map<String, Object> orderInfo = orderList.get(0);
        String bizErrorCode = MapUtils.getString(orderInfo, JyCardResponseFields.BIZ_ERROR_CODE);
        if (!JyCardResponseUtil.isSuccess(bizErrorCode) && null == MapUtils.getString(orderInfo, JyCardResponseFields.BIZ_ERROR_MSG)) {
            orderInfo.put(JyCardResponseFields.BIZ_ERROR_MSG, MapUtils.getString(orderInfo, JyCardResponseFields.ERROR_MSG));
        }
        return orderInfo;
    }

    private ImmutablePair<String, String> getUserAndPassword(Map<String, Object> request) {
        String encryptDynamicId = MapUtils.getString(request, UpayService.DYNAMIC_ID);
        String orderSn = MapUtils.getString(request, Transaction.ORDER_SN);
        //用户名和密码放在encryptDynamicId中。2024-09-26后该方案可以废弃，使用下面的auth_token和auth_code的方式。目前两种方案都兼容
        if (StringUtils.isNotEmpty(encryptDynamicId)) {
            return getUserAndPasswordByDynamicId(encryptDynamicId, orderSn);
        }

        String encryptUserName = MapUtils.getString(request, SupportService.AUTH_TOKEN);
        String encryptPassword = MapUtils.getString(request, SupportService.USER_AUTH_CODE);

        if (StringUtils.isAnyBlank(encryptUserName, encryptPassword)) {
            log.error("getUserAndPassword, auth_token或user_auth_code为空, orderSn={}", orderSn);
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING);
        }

        //解析用户名和密码
        String userName = new String(Base64.decode(encryptUserName));
        String password = new String(Base64.decode(encryptPassword));
        return ImmutablePair.of(userName, password);
    }

    private ImmutablePair<String, String> getUserAndPasswordByDynamicId(String encryptDynamicId, String orderSn) {
        String dynamicId = new String(Base64.decode(encryptDynamicId));
        //解析用户名和密码
        int lastUnderscoreIndex = dynamicId.lastIndexOf('_');
        if (-1 == lastUnderscoreIndex) {
            log.error("getUserAndPassword, dynamicId格式不正确, dynamicId={}, orderSn={}", dynamicId, orderSn);
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING);
        }
        String userName = dynamicId.substring(0, lastUnderscoreIndex);
        String password = dynamicId.substring(lastUnderscoreIndex + 1);
        return ImmutablePair.of(userName, password);
    }

    /**
     * 校验密码
     *
     * @param token
     * @param request
     * @param transaction
     * @return
     */
    private Map<String, Object> checkPassword(String token, Map<String, Object> request, Map<String, Object> transaction) {
        ImmutablePair<String, String> userPassword = getUserAndPassword(request);
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        JyCardRequestBuilder requestBuilder = buildSystemLevelRequestParams(tradeParams);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, token);

        //构建应用级请求参数
        Map<String, Object> pwdParams = new HashMap<>();
        pwdParams.put(JyCardBusinessFields.ID_TYPE, IdTypeEnum.ID_SNO.getType());
        pwdParams.put(JyCardBusinessFields.ID, userPassword.getLeft());
        pwdParams.put(JyCardBusinessFields.PWD, userPassword.getRight());
        requestBuilder.set(APPLICATION_PARAMS_KEY, Collections.singletonMap(JyCardBusinessFields.REQUEST_OBJ_CHECK_PWD, pwdParams));

        //校验用户名和密码
        Map<String, Object> jyCardResponse = retryIfNetworkException(METHOD_CHECK_PASSWORD, requestBuilder.build(), tradeParams);

        ///判断是否需要重置accessToken缓存
        checkIfNeedClearAccessTokenCache(jyCardResponse, tsn, tradeParams);

        //请求成功，解析accessToken
        return parseAccountFromJyCardResponse(jyCardResponse, tsn);
    }

    /**
     * 支付
     *
     * @param token
     * @param accountInfo
     * @param transaction
     * @return
     */
    private Map<String, Object> providerPay(String token, Map<String, Object> accountInfo, Map<String, Object> transaction) {
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        JyCardRequestBuilder requestBuilder = buildSystemLevelRequestParams(tradeParams);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, token);

        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        //锦医一卡通通道的金额单位是元
        String tranAmt = cents2yuan(amount);

        //构建应用级请求参数
        Map<String, Object> payParams = new HashMap<>();
        payParams.put(JyCardBusinessFields.ACCOUNT, MapUtils.getString(accountInfo, JyCardBusinessFields.ACCOUNT));
        payParams.put(JyCardBusinessFields.TRANAMT, tranAmt);
        payParams.put(JyCardBusinessFields.ACCTYPE, MapUtils.getString(tradeParams, TransactionParam.JYCARD_ACC_TYPE));
        payParams.put(JyCardBusinessFields.MERCACC, MapUtils.getString(tradeParams, TransactionParam.JYCARD_PROVIDER_MCH_ID));
        payParams.put(JyCardBusinessFields.ORDER_NO, tsn);
        requestBuilder.set(APPLICATION_PARAMS_KEY, Collections.singletonMap(JyCardBusinessFields.REQUEST_OBJ_PAY, payParams));

        //支付
        Map<String, Object> jyCardResponse = retryIfNetworkException(METHOD_PAY, requestBuilder.build(), tradeParams);

        //判断是否需要重置accessToken缓存
        checkIfNeedClearAccessTokenCache(jyCardResponse, tsn, tradeParams);
        return jyCardResponse;
    }

    /**
     * 退款
     *
     * @param token
     * @param transaction
     * @return
     */
    private Map<String, Object> providerRefund(String token, Map<String, Object> transaction) {
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        JyCardRequestBuilder requestBuilder = buildSystemLevelRequestParams(tradeParams);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, token);

        String accountId = getAccountId(transaction);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        //锦医一卡通通道的金额单位是元
        String tranAmt = cents2yuan(amount);

        //构建应用级请求参数
        Map<String, Object> refundParams = new HashMap<>();
        refundParams.put(JyCardBusinessFields.ACCOUNT, accountId);
        refundParams.put(JyCardBusinessFields.TRANAMT, tranAmt);
        refundParams.put(JyCardBusinessFields.ACCTYPE, MapUtils.getString(tradeParams, TransactionParam.JYCARD_ACC_TYPE));
        refundParams.put(JyCardBusinessFields.MERCACC, MapUtils.getString(tradeParams, TransactionParam.JYCARD_PROVIDER_MCH_ID));
        refundParams.put(JyCardBusinessFields.ORDER_NO, tsn);
        refundParams.put(JyCardBusinessFields.REFUND_ORDER_NO, MapUtils.getString(transaction, Transaction.ORDER_SN));
        requestBuilder.set(APPLICATION_PARAMS_KEY, Collections.singletonMap(JyCardBusinessFields.REQUEST_OBJ_REFUND, refundParams));

        //退款
        Map<String, Object> jyCardResponse = retryIfNetworkException(METHOD_REFUND, requestBuilder.build(), tradeParams);

        //判断是否需要重置accessToken缓存
        checkIfNeedClearAccessTokenCache(jyCardResponse, tsn, tradeParams);

        return jyCardResponse;
    }

    /**
     * 查询订单状态
     *
     * @param token
     * @param account
     * @param transaction
     * @return
     */
    private Map<String, Object> providerQuery(String token, String account, Map<String, Object> transaction) {
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        JyCardRequestBuilder requestBuilder = buildSystemLevelRequestParams(tradeParams);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, token);

        //构建应用级请求参数
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put(JyCardBusinessFields.ACCOUNT, account);
        queryParam.put(JyCardBusinessFields.ACCTYPE, MapUtils.getString(tradeParams, TransactionParam.JYCARD_ACC_TYPE));
        queryParam.put(JyCardBusinessFields.ORDER_NO, tsn);
        Map<String, Object> applicationRequest = new HashMap<>();
        applicationRequest.put(JyCardBusinessFields.REQUEST_OBJ_ORDER_QUERY,
                Collections.singletonMap(JyCardBusinessFields.REQUEST_OBJ_ORDER_QUERY_LIST, Collections.singletonList(queryParam)));
        //设置应用级请求参数
        requestBuilder.set(APPLICATION_PARAMS_KEY, applicationRequest);

        //查询
        Map<String, Object> jyCardResponse = retryIfNetworkException(METHOD_QUERY_ORDER, requestBuilder.build(), tradeParams);

        //判断是否需要重置accessToken缓存
        checkIfNeedClearAccessTokenCache(jyCardResponse, tsn, tradeParams);
        return jyCardResponse;
    }

    /**
     * 构建系统级的请求参数
     *
     * @param tradeParams
     * @return
     */
    private JyCardRequestBuilder buildSystemLevelRequestParams(Map<String, Object> tradeParams) {
        JyCardRequestBuilder requestBuilder = new JyCardRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(JyCardProtocolFields.TIMESTAMP, DateUtil.formatDate(new Date(), STANDARD_DATE_FORMAT));
        requestBuilder.set(JyCardProtocolFields.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(JyCardProtocolFields.APP_KEY, MapUtils.getString(tradeParams, TransactionParam.JYCARD_APP_KEY));
        requestBuilder.set(JyCardProtocolFields.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(JyCardProtocolFields.SIGN_METHOD, DEFAULT_SIGN_METHOD);
        return requestBuilder;
    }

    private String getPrivateKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.JYCARD_PRIVATE_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    private String getPublicKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.JYCARD_PUBLIC_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    private String getAppSecret(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.JYCARD_APP_SECRET);
        return getPrivateKeyContent(rsaKeyId);
    }

    private String getSystemErrorCode(Map<String, Object> jyCardResponse) {
        return MapUtils.getString(jyCardResponse, JyCardResponseFields.SYSTEM_ERROR_CODE);
    }

    private String getBizErrorMsg(Map<String, Object> jyCardResponse) {
        return MapUtils.getString(jyCardResponse, JyCardResponseFields.BIZ_ERROR_MSG);
    }

    private void checkIfNeedClearAccessTokenCache(Map<String, Object> jyCardResponse, String tsn, Map<String, Object> tradeParams) {
        String systemErrorCode = getSystemErrorCode(jyCardResponse);
        if (!JyCardSystemErrorEnum.ACCESS_TOKEN_INVALID.getCode().equals(systemErrorCode)) {
            return;
        }

        String appKey = MapUtils.getString(tradeParams, TransactionParam.JYCARD_APP_KEY);
        //accessToken已失效, 则清空缓存中的accessToken
        log.warn("{}: accessToken已失效, 清空accessToken缓存 tsn={}, appKey={}, errorCode={}", NAME, tsn, appKey, systemErrorCode);
        try {
            redisTemplate.delete(buildAccessTokenCacheKey(appKey));
        } catch (Exception e) {
            //do nothing
        }
    }

    private Map<String, Object> retryIfNetworkException(String method, Map<String, Object> request, Map<String, Object> tradeParams) {
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        try {
            return new RetryUtil<Map<String, Object>>()
                    .retry(new RetryUtil.TimingStrategy.Builder().setRetry(2, 50, 1.0).build())
                    .method(() -> {
                        try {
                            return jyCardClient.call(gatewayUrl, method, request,
                                    getAppSecret(tradeParams), getPrivateKey(tradeParams), getPublicKey(tradeParams));
                        } catch (MpayException | MpayApiNetworkError e) {
                            log.error("{}: 调用锦医一卡通接口异常: method={}, request={}, error={}",
                                    NAME, method, JacksonUtil.toJsonString(request), e.getMessage());
                            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
                        }
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull)
                    .execute();
        } catch (Exception e) {
            log.error("{}: 请求锦医一卡通接口异常: method={}, request={}, error={}",
                    NAME, method, JacksonUtil.toJsonString(request), e.getMessage());
            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }

    }

    /**
     * 回调通知
     *
     * @param notifyUrl
     * @param orderSn
     * @param notification
     * @return
     */
    private String notifyWithRetry(String notifyUrl, String orderSn, Map<String, Object> notification) {
        try {
            return new RetryUtil<String>()
                    .retry(new RetryUtil.TimingStrategy.Builder().setRetry(2, 50, 1.0).build())
                    .method(() -> {
                        try {
                            return gatewayNodeNotifier.syncNotify(notifyUrl, orderSn, notification);
                        } catch (IOException e) {
                            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
                        }
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull)
                    .execute();
        } catch (Exception e) {
            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }
    }

    private String getUpayDispatcherNotifyUrl(String orderSn) {
        return getCommonNotifyUrl(notifyHost);
        //local debug
        //return getCommonNotifyUrl(notifyHost) + "/" + orderSn;
    }

    private boolean isOrderStatusSuccess(JyCardOrderStatusEnum statusEnum) {
        return JyCardOrderStatusEnum.SUCCESS == statusEnum || JyCardOrderStatusEnum.REFUNDED == statusEnum;
    }

    private Map<String, Object> getProviderError(Map<String, Object> providerResult){
        if (providerResult != null) {
            return MapUtil.copyInclusive(providerResult, JyCardResponseFields.BIZ_ERROR_CODE,
                    JyCardResponseFields.BIZ_ERROR_MSG,
                    JyCardResponseFields.SYSTEM_ERROR_CODE,
                    JyCardResponseFields.ERROR_MSG);
        } else {
            return Collections.emptyMap();
        }
    }
}
