package com.wosai.upay.workflow;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.UUIDGenerator;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.InvalidBarcodeException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.Map;

/**
 * Created by maoyu on 15/11/20.
 */
@ServiceProvicerPriority(priority = 3)
public class DirectAlipayV2ServiceProvider extends AlipayV2ServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(DirectAlipayV2ServiceProvider.class);

    public static final String NAME = "provider.alipay.v2";
   /**
     * 支付宝2.0直连agent name
     */
    public static final String ALIPAY_AGENT_NAME = "*_2_*_true_true_0002";
    /**
     * 默认子商户号
     */
    private static final String DEFAULT_SUB_MCH_ID = "2088";
    /**
     * 业务来源，业务接入的约定标识，代表业务的调用方。例如 :ISV公司名称缩写
     */
    @Value("${alipay.hbfq.shareCode.source:shouqianba}")
    private String shareCodeSource;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if ((Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway)
                && Order.SUB_PAYWAY_WAP != MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) 
                && TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(getTradeCurrency(transaction))) {

            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = new RequestV2Builder();

        String signType = MapUtil.getString(config, TransactionParam.SIGN_TYPE);
        if (StringUtils.isEmpty(signType)) {
            signType = AlipayConstants.SIGN_TYPE_RSA;
        }

        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(config,TransactionParam.APP_ID));
        builder.set(ProtocolV2Fields.CHARSET,AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, signType);
        String alipayPid = BeanUtil.getPropString(config, TransactionParam.ALIPAY_PID);
        if(TradeConfigService.PROVIDER_LKLWANMA == BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
        	alipayPid = BeanUtil.getPropString(config, TransactionParam.LAKALA_WNAMA_SYS_PROVIDER_ID);
        }
        if(!StringUtil.empty(alipayPid)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, alipayPid
            ));
        }
        return builder;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String gatewayUrl, Map<String, String> request) throws MpayException, MpayApiNetworkError {

        String signType = MapUtil.getString(config, TransactionParam.SIGN_TYPE);
        if (StringUtils.isEmpty(signType)) {
            signType = AlipayV2NewClient.SIGN_TYPE_ALIPAY;
        }
        return client.call(gatewayUrl, signType, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
    }


    @SuppressWarnings("unchecked")
    public Map<String,Object> queryUserIdByBarcode(Map<String,Object> config,String dynamicId){
        Map<String,Object> tradeConfig=(Map<String, Object>) config.get(TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        if(tradeConfig==null){
            throw new InvalidBarcodeException(UpayErrorScenesConstant.INVALID_BARCODE_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_BARCODE_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        RequestV2Builder builder=new RequestV2Builder();
        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(tradeConfig,TransactionParam.APP_ID));
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_MOBILE_SHAKE_USER_QUERY);
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE,AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.TIMESTAMP,dateFormat.format(new Date()));
        builder.set(ProtocolV2Fields.VERSION,"1.0");
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, "201907BB6017c3ac819645dd98a23bf96317dX84");
        builder.set(BusinessV1Fields.DYNAMIC_ID_TYPE,AlipayConstants.SCENE_BAR_CODE);
        builder.set(BusinessV1Fields.DYNAMIC_ID,dynamicId);
        Map<String,Object> result;
        try {
            result = retryIfNetworkException(tradeConfig, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_OPENID_BARCODE_QUERY), builder.build(), retryTimes, "queryOpenIdByBarCode");
        }catch (Exception ex){
            logger.error("failed to call ali queryOpenIdByBarCode", ex);
            return CollectionUtil.hashMap("sub_msg",ex.getMessage());
        }
        return result;
    }

    /**
     * 获取吱口令
     *
     * @param terminalSn
     * @param request
     * @return
     */
    public Map<String, Object> getShareCode(String terminalSn, Map<String, Object> request) {
        Map<String, Object> extendedMap = MapUtil.getMap(request, "extended");
        String tradeApp = MapUtil.getString(request, UpayService.TRADE_APP);
        String bizModel = MapUtil.getString(extendedMap, Transaction.SQB_BIZ_MODEL);
        Map<String, Object> agentParams = serviceFacade.getAgentParamMap(DirectAlipayV2ServiceProvider.ALIPAY_AGENT_NAME);
        String alipaySubMchId = serviceFacade.getAlipaySubMerchantId(terminalSn, tradeApp, bizModel);
        if (StringUtil.empty(alipaySubMchId)) {
            alipaySubMchId = DEFAULT_SUB_MCH_ID;
        }
        Map tradeParams = MapUtil.getMap(agentParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY);
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), "getShareCode");
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_PCREDIT_HUABEI_SHARDCODE_HBFQ_CREATE);
        builder.set(ProtocolV2Fields.APP_ID, MapUtil.getString(tradeParams, TransactionParam.APP_ID));
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.VERSION, AlipayConstants.VERSION_ONE);
        builder.set(ProtocolV2Fields.TIMESTAMP, dateFormat.format(new Date()));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, UUIDGenerator.getUUID());
        builder.bizSet(BusinessV2Fields.BIZ_SCENE, AlipayConstants.SHARECODE_BIZ_SCENE);
        builder.bizSet(BusinessV2Fields.SOURCE, shareCodeSource);
        builder.bizSet(BusinessV2Fields.HBFQ_SUB_MERCHANT_ID, alipaySubMchId);
        // 追加前端入参到biz map
        if (MapUtil.isNotEmpty(extendedMap)) {
            extendedMap.forEach(builder::bizSet);
        }
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(MapUtil.hashMap(TransactionParam.PRIVATE_KEY, privateKey), gatewayUrl, builder.build(), retryTimes, "getShareCode");
        } catch (Exception ex) {
            logger.error("获取吱口令失败", ex);
            throw new UpayBizException(UpayErrorScenesConstant.UPAY_HUABEI_FQ_SHARECODE, UpayErrorScenesConstant.UPAY_HUABEI_FQ_SHARECODE_MESSAGE, ex);
        }
        return result;
    }
}
