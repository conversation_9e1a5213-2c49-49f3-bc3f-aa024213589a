package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.icbc.ICBCResponseFields;
import com.wosai.mpay.api.pab.*;
import com.wosai.mpay.api.pab.ResponseFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.LocalDateTimeUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.*;


/**
 * <AUTHOR>
 * @Date 2023/12/14、15:50
 * 平安银行通道
 **/

public class PabServiceProvider extends AbstractServiceProvider {

    public static final Logger LOGGER = LoggerFactory.getLogger(PabServiceProvider.class);

    public static final String NAME = "provider.pab";

    public static final String useRetrivlRefNum = "0";


    private static Integer retryTime = 3;

    @Autowired
    PabClient pabClient;

    @Autowired
    protected ExternalServiceFacade serviceFacade;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_PAD_PAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.PAB_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();

        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        PabRequestBuilder requestBuilder = buildPayRequest(context);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY);
        String sm2PrivateKey = serviceFacade.getRsaKeyDataById(privateKey);

        String userId = MapUtil.getString(tradeParams, TransactionParam.PAB_SM2_USER_ID);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PAY);
        Map<String, Object> result = null;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = pabClient.call(requestBuilder, gatewayUrl.getUrl(), sm2PrivateKey, userId);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), requestBuilder.build(), "application/json", FakeConstant.JSON_FORMAT);
            }
        } catch (MpayException | MpayApiNetworkError ex) {
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            LOGGER.error("failed to call pab pay", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context, OP_PAY);
    }

    public PabRequestBuilder buildPayRequest(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraParams = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_PARAMS);
        initTransactionSomeValue(transaction);

        Map<String, Object> order = context.getOrder();
        String orderSn = MapUtil.getString(order, Order.SN);

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String merchantId = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_PROVIDER_MCH_ID);
        String terminalId = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_B2C_TERMINAL_ID);

        PabRequestBuilder requestBuilder = new PabRequestBuilder();
        requestBuilder.set(RequestFields.S_TRANS_CLASS, PabConstant.PAY);
        requestBuilder.set(RequestFields.S_SERVICE_DISTINCT_CODE, PabConstant.SCAN_CODE);
        String time = PabUtil.convertTimestampToDateTime(MapUtil.getLong(transaction, DaoConstants.CTIME));
        requestBuilder.set(RequestFields.S_TRANSMISSION_DATE_TIME, time);

        requestBuilder.set(RequestFields.S_ENCRYPT_FLAG, PabConstant.ASYMMETRIC_ENCRYPTION);

        //商户流水号
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        String merchantTranSn = org.apache.commons.lang3.StringUtils.substring(tsn, -6);
        requestBuilder.set(RequestFields.S_TERM_SSN, merchantTranSn);

        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        extraOutFields.put(ResponseFields.S_ORIG_DATA_ELEMTS, time + merchantTranSn);

        //商户号
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_ID, MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_PROVIDER_MCH_ID));

        requestBuilder.set(RequestFields.S_CARD_ACCPTR_TERMNL_ID, MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_B2C_TERMINAL_ID));

        requestBuilder.set(RequestFields.S_FWD_INST_ID_CODE, PabConstant.RETAIL);

        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        requestBuilder.set(RequestFields.S_AMT_TRANS, org.apache.commons.lang3.StringUtils.leftPad(amount + "", 12, "0"));

        requestBuilder.set(RequestFields.S_CURRCY_CODE_TRANS, PabConstant.RMB);

        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());

        String ip = terminalInfo.getIp();
        if (Objects.isNull(ip)) {
            ip = PabConstant.DEFAULT_IP;
        }
        String iPv4Address = getIPv4Address(ip);

        requestBuilder.set(RequestFields.S_MCHT_IP, iPv4Address);

        String barCode = MapUtil.getString(extraParams, Transaction.BARCODE);

        requestBuilder.set(RequestFields.S_AUTH_CODE, barCode);
        String sTranLogTraceNo = PabConstant.RETAIL + merchantId + terminalId + merchantTranSn + time;
        requestBuilder.set(RequestFields.S_TRAN_LOG_TRACE_NO, sTranLogTraceNo);

        requestBuilder.set(RequestFields.S_OUT_ORDER_NO, orderSn);

        requestBuilder.set(RequestFields.S_SIGNCERT_ID, MapUtil.getString(tradeParams, TransactionParam.S_SIGNCERT_ID));
        return requestBuilder;
    }

    @Override
    public String cancel(TransactionContext context) {
        return null;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PabRequestBuilder requestBuilder = null;
        Integer subpayway = MapUtil.getInteger(transaction, Transaction.SUB_PAYWAY);
        if (SubPayway.BARCODE.getCode().equals(subpayway)) {
            requestBuilder = buildB2cQueryRequest(context);
        } else {
            requestBuilder = buildC2BQuery(context);
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY);
        String sm2PrivateKey = serviceFacade.getRsaKeyDataById(privateKey);

        String userId = MapUtil.getString(tradeParams, TransactionParam.PAB_SM2_USER_ID);


        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_QUERY);

        Map<String, Object> result = null;

        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(requestBuilder, gatewayUrl.getUrl(), sm2PrivateKey, null, userId, retryTime, OP_QUERY);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), requestBuilder.build(), "application/json", FakeConstant.JSON_FORMAT);
            }
        } catch (MpayException | MpayApiNetworkError ex) {
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            LOGGER.error("failed to call pab query", ex);
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);
        return queryResult(result, context, OP_QUERY);
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = MapUtil.getString(result, ResponseFields.S_RESP_CODE, "");

        if (Objects.equals(PabConstant.RET_CODE_SUCCESS, respCode)) {
            //退款
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        } else {
            return Workflow.RC_ERROR;
        }
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context, String op) {

        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String respCode = MapUtil.getString(result, ResponseFields.S_RESP_CODE, "");

        setTradeNoBuyerInfoIfExists(result, context);
        //支付或者退款00000000直接视为成功
        if (Objects.equals(PabConstant.RET_CODE_SUCCESS, respCode)) {
            //交易
            resolvePayFund(context, result, OP_PAY);
            return Workflow.RC_PAY_SUCCESS;
        } else if (respCode.contains(PabConstant.RET_CODE_NOT_SYSTEM_ERROR_LAST_SIX) || respCode.contains(PabConstant.RET_CODE_NOT_SYSTEM_ERROR_LAST_SIX) || PabConstant.IN_PROG.contains(respCode)) {
            return Workflow.RC_IN_PROG;
        } else {
            return Workflow.RC_ERROR;
        }

    }

    private String queryResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String respCode = MapUtil.getString(result, ResponseFields.S_RESP_CODE, ""); //返回状态码

        //原始交易状态
        String sOrgTransState = MapUtils.getString(result, ResponseFields.S_ORG_TRANS_STATE, "");//响应描述

        setTradeNoBuyerInfoIfExists(result, context);
        //支付成功
        if (Objects.equals(sOrgTransState, PabConstant.SUCCESS)) {
            resolvePayFund(context, result, OP_QUERY);
            return Workflow.RC_PAY_SUCCESS;

        } else if (Objects.equals(sOrgTransState, PabConstant.IN_PROCESS) ) {
            return Workflow.RC_IN_PROG;
        } else {
            return Workflow.RC_ERROR;
        }

    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result, String op) {
        //交易成功时间
        context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        Map<String, Object> transaction = context.getTransaction();
        String sRetrivlRefNum = null;
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());

        String sRetrivlRefNumKey = Objects.equals(op, OP_PAY) ? ResponseFields.S_RETRIVL_REF_NUM : ResponseFields.S_ORG_RETRIVL_REF_NUM;
        sRetrivlRefNum = MapUtil.getString(result, sRetrivlRefNumKey);

        extraOutFields.put(ResponseFields.S_RETRIVL_REF_NUM, sRetrivlRefNum);

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Order.PAYWAY_WEIXIN) {
            resolveWeixinPayFund(result, context);
        } else {
            resolveOtherPayFund(result, context);
        }
    }

    private void resolveOtherPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long tradeAmt = MapUtil.getLongValue(result, RequestFields.S_AMT_TRANS); //用户实际支付金额

        long totalDiscountAmount = MapUtil.getLongValue(result, ResponseFields.S_AMT_COUPON);

        long paidAmt = tradeAmt - totalDiscountAmount;

        //通道优惠
        long channelDiscountAmount = totalDiscountAmount;
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        if (channelDiscountAmount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                    )
            );
        }

        long amount = effectiveAmount - totalDiscountAmount;
        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (amount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));

        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (effectiveAmount - paidAmt > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, totalDiscountAmount);
            context.getOrder().put(Order.NET_DISCOUNT, totalDiscountAmount);
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount - channelDiscountAmount);
    }

    private void resolveWeixinPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long couponSum = 0;
        //sAmtTrans
        long discountChanelMchTotal = 0; // 商户免充值不结算给商户
        long tradeAmount = MapUtil.getLongValue(result, RequestFields.S_AMT_TRANS); //用户实际支付金额
        long channelDiscountAmount = MapUtil.getLongValue(result, ResponseFields.S_AMT_COUPON);

        long paidAmt = tradeAmount - channelDiscountAmount;
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();
        if (result.containsKey(ResponseFields.S_PROMOTION_DETAIL)) {
            List<Map<String, Object>> promotions = getPromotions(result);
            Pair<Long, Long> discount = buildWeixinPaymentsByPromotions(promotions, payments);
            couponSum = discount.getLeft();
            discountChanelMchTotal = discount.getRight();
        } else {
            if (channelDiscountAmount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                        )
                );
                couponSum += channelDiscountAmount;
            }
        }

        long totalFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long cashFee = totalFee - couponSum - discountChanelMchTotal;
        if (cashFee > 0) {
            String banktype = BeanUtil.getPropString(result, ICBCResponseFields.BANK_TYPE);
            Map<String, Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(banktype, cashFee);
            if (payment != null) {
                payments.add(payment);
            }
        }

        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum + discountChanelMchTotal);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum + discountChanelMchTotal);
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee - discountChanelMchTotal);
    }

    private List<Map<String, Object>> getPromotions(Map<String, Object> result) {
        Object promotionsStr = MapUtil.getObject(result, ResponseFields.S_PROMOTION_DETAIL);
        List<Map<String, Object>> promotionList = new ArrayList<>();
        if (promotionsStr instanceof List) {
            promotionList = (List<Map<String, Object>>) promotionsStr;
        } else if (promotionsStr instanceof String) {
            if (StringUtils.isEmpty(String.valueOf(promotionsStr))) {
                promotionsStr = "[]";
            }
            promotionList = JsonUtil.jsonStrToObject(String.valueOf(promotionsStr), List.class);
        }
        return promotionList;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();

        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
            String userId = MapUtils.getString(result, ResponseFields.S_SUB_OPENID); //用户id
            if (!StringUtil.empty(userId)) {
                transaction.put(Transaction.BUYER_UID, userId);
            }
        }

        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String tpOrderId = MapUtils.getString(result, ResponseFields.S_VOUCHER_NUM); //平安单号
            if (!StringUtils.isEmpty(tpOrderId)) {
                transaction.put(Transaction.TRADE_NO, tpOrderId);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(BeanUtil.getPropString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {

            String tpOrderId = MapUtils.getString(result, ResponseFields.S_TRADE_NO); //支付宝/微信单号
            if(!StringUtils.isEmpty(tpOrderId)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tpOrderId);
            }

        }
    }

    PabRequestBuilder buildB2cQueryRequest(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);

        PabRequestBuilder requestBuilder = buildCommonQuery(context);
        String terminalId = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_B2C_TERMINAL_ID);
        requestBuilder.set(RequestFields.S_TRANS_CLASS, PabConstant.B2C_QUERY);
        requestBuilder.set(RequestFields.S_SERVICE_DISTINCT_CODE, PabConstant.SCAN_CODE);

        //商户号
        String merchantSn = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_PROVIDER_MCH_ID);
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_ID, merchantSn);
        String terminalSn = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_B2C_TERMINAL_ID);

        //商户流水号
        String  merchantTranSn = PabUtil.generateString(6);


        requestBuilder.set(RequestFields.S_TERM_SSN, merchantTranSn);

        //终端号
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_TERMNL_ID, terminalSn);

        String orderSn = MapUtil.getString(context.getOrder(), Order.SN);
        requestBuilder.set(RequestFields.S_ORG_OUT_ORDER_NO, orderSn);
        String tradeTime = MapUtil.getString(requestBuilder.getRequest(), RequestFields.S_TRANSMISSION_DATE_TIME);

        String sTranLogTraceNo = PabConstant.RETAIL + merchantSn + terminalId + merchantTranSn + tradeTime;
        requestBuilder.set(RequestFields.S_TRAN_LOG_TRACE_NO, sTranLogTraceNo);
        return requestBuilder;
    }

    //通用查询请求
    PabRequestBuilder buildCommonQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String merchantId = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_PROVIDER_MCH_ID);
        PabRequestBuilder requestBuilder = new PabRequestBuilder();

        String time = PabUtil.convertTimestampToDateTime(System.currentTimeMillis());
        requestBuilder.set(RequestFields.S_TRANSMISSION_DATE_TIME, time);

        //商户号
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_ID, merchantId);
        requestBuilder.set(RequestFields.S_FWD_INST_ID_CODE, PabConstant.RETAIL);

        //原交易信息
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        String sOrigDataElemts = MapUtil.getString(extraOutFields, ResponseFields.S_ORIG_DATA_ELEMTS);
        requestBuilder.set(RequestFields.S_ORIG_DATA_ELEMTS, sOrigDataElemts);
        requestBuilder.set(RequestFields.S_ENCRYPT_FLAG, PabConstant.ASYMMETRIC_ENCRYPTION);
        String sSigncertID = MapUtil.getString(tradeParams, TransactionParam.S_SIGNCERT_ID);
        requestBuilder.set(RequestFields.S_SIGNCERT_ID, sSigncertID);
        return requestBuilder;
    }

    PabRequestBuilder buildC2BQuery(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);
        PabRequestBuilder requestBuilder = buildCommonQuery(context);

        String terminalId = MapUtil.getString(tradeParams, RequestFields.OTHER_TERMINAL_ID);

        requestBuilder.set(RequestFields.S_TRANS_CLASS, PabConstant.C2B_QUERY);
        requestBuilder.set(RequestFields.S_SERVICE_DISTINCT_CODE, PabConstant.C2B);

        requestBuilder.set(RequestFields.S_CARD_ACCPTR_TERMNL_ID, terminalId);

        //商户流水号
        String  merchantTranSn = PabUtil.generateString(6);

        requestBuilder.set(RequestFields.S_TRANS_SSN, merchantTranSn);
        return requestBuilder;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        PabRequestBuilder request = buildRefundRequest(context);
        String op = OP_REFUND;

        String privateKey = MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY);

        String sm2PrivateKey = serviceFacade.getRsaKeyDataById(privateKey);

        String userId = MapUtil.getString(tradeParams, TransactionParam.PAB_SM2_USER_ID);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_REFUND);

        Map<String, Object> result = null;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(request, gatewayUrl.getUrl(), sm2PrivateKey, null, userId, retryTime, OP_REFUND);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), request.build(), "application/json", FakeConstant.JSON_FORMAT);
            }
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, op, ex);
            LOGGER.error("failed to call pab refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, op, e);
            LOGGER.error("encountered ioex in pab refund", e);
            return Workflow.RC_IOEX;
        } catch (Exception e) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, op);
        return buildRefundResult(result, context, op);
    }

    PabRequestBuilder buildRefundRequest(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);

        PabRequestBuilder requestBuilder = new PabRequestBuilder();

        Integer subPayway = MapUtil.getInteger(transaction, Transaction.SUB_PAYWAY);

        String terminalId = null;

        String sServiceDistinctCode = null;

        if (subPayway == SubPayway.BARCODE.getCode()) {
            terminalId = MapUtil.getString(tradeParams, RequestFields.B2C_TERMINAL_ID);

            sServiceDistinctCode = PabConstant.B2C;

        } else {
            terminalId = MapUtil.getString(tradeParams, RequestFields.OTHER_TERMINAL_ID);
            sServiceDistinctCode = PabConstant.C2B;

        }

        requestBuilder.set(RequestFields.S_CARD_ACCPTR_TERMNL_ID, terminalId);
        requestBuilder.set(RequestFields.S_SERVICE_DISTINCT_CODE, sServiceDistinctCode);

        requestBuilder.set(RequestFields.S_TRANS_CLASS, PabConstant.REFUND);

        String time = PabUtil.convertTimestampToDateTime(MapUtil.getLong(transaction, DaoConstants.CTIME));
        requestBuilder.set(RequestFields.S_TRANSMISSION_DATE_TIME, time);

        //商户流水号
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        String merchantTranSn = org.apache.commons.lang3.StringUtils.substring(tsn, -6);
        requestBuilder.set(RequestFields.S_TERM_SSN, merchantTranSn);

        String orderSn = MapUtil.getString(context.getOrder(), com.wosai.profit.sharing.model.upay.Order.SN);

        requestBuilder.set(RequestFields.S_OUT_ORDER_NO, orderSn);

        String merchantSn = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_PROVIDER_MCH_ID);
        //商户号
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_ID, merchantSn);

        requestBuilder.set(RequestFields.S_FWD_INST_ID_CODE, PabConstant.RETAIL);

        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        requestBuilder.set(RequestFields.S_AMT_TRANS, org.apache.commons.lang3.StringUtils.leftPad(amount + "", 12, "0"));

        requestBuilder.set(RequestFields.S_CURRCY_CODE_TRANS, PabConstant.RMB);

        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLong(context.getOrder(), "ctime"));

        Long finishTime = MapUtil.getLong(payOrConsumerTransaction, Transaction.CHANNEL_FINISH_TIME);
        String date = DateUtil.formatDate(new Date(finishTime), LocalDateTimeUtil.YYYYMMDD);

        //付款日期
        requestBuilder.set(RequestFields.S_ORIG_DATA_ELEMTS, date);

        requestBuilder.set(RequestFields.S_INQUIRY_MOD, useRetrivlRefNum);

        Map extraOutFields = MapUtil.getMap(payOrConsumerTransaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        String sRetrivlRefNum = MapUtil.getString(extraOutFields, ResponseFields.S_RETRIVL_REF_NUM);
        //待定 原交易渠道订单号
        requestBuilder.set(ResponseFields.S_RETRIVL_REF_NUM, sRetrivlRefNum);

        //记录这个值，退款查询需要用到
        String sOrigDataElemts = MapUtil.getString(extraOutFields, ResponseFields.S_ORIG_DATA_ELEMTS);
        Map refundTransactionExtraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        refundTransactionExtraOutFields.put(ResponseFields.S_ORIG_DATA_ELEMTS, sOrigDataElemts);

        String sTranLogTraceNo = PabConstant.RETAIL + merchantSn + terminalId + merchantTranSn + time;

        requestBuilder.set(RequestFields.S_TRAN_LOG_TRACE_NO, sTranLogTraceNo);

        requestBuilder.set(RequestFields.S_ENCRYPT_FLAG, PabConstant.ASYMMETRIC_ENCRYPTION);

        String sSigncertID = MapUtil.getString(tradeParams, TransactionParam.S_SIGNCERT_ID);

        requestBuilder.set(RequestFields.S_SIGNCERT_ID, sSigncertID);

        return requestBuilder;

    }


    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {

        PabRequestBuilder pabRequestBuilder = buildPreCreateRequest(context);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);

        String privateKey = MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY);
        String sm2PrivateKey = serviceFacade.getRsaKeyDataById(privateKey);
        String userId = MapUtil.getString(tradeParams, TransactionParam.PAB_SM2_USER_ID);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PRECREATE);
        Map<String, Object> result = null;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(pabRequestBuilder, gatewayUrl.getUrl(), sm2PrivateKey, null, userId, retryTime, OP_PRECREATE);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), pabRequestBuilder.build(), "application/json", FakeConstant.JSON_FORMAT);
            }
        } catch (MpayException | MpayApiNetworkError ex) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            LOGGER.error("failed to call pab precreate", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildPreCreateResult(result, context);
    }

    private PabRequestBuilder buildPreCreateRequest(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();

        initTransactionSomeValue(transaction);

        Map<String, Object> tradeParams = getTradeParams(transaction);

        PabRequestBuilder requestBuilder = new PabRequestBuilder();
        requestBuilder.set(RequestFields.S_TRANS_CLASS, PabConstant.C2B_PAY);
        requestBuilder.set(RequestFields.S_SERVICE_DISTINCT_CODE, PabConstant.C2B);
        String time = PabUtil.convertTimestampToDateTime(MapUtil.getLong(transaction, DaoConstants.CTIME));
        requestBuilder.set(RequestFields.S_TRANSMNS_DATE_TIME, time);

        String tsn = MapUtil.getString(context.getTransaction(), Transaction.TSN);
        //商户流水号
        String merchantTranSn = org.apache.commons.lang3.StringUtils.substring(tsn, -6);
        requestBuilder.set(RequestFields.S_TRANS_SSN, merchantTranSn);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());

        //原交易信息
        extraOutFields.put(ResponseFields.S_ORIG_DATA_ELEMTS, time + merchantTranSn);

        //商户号
        String merchantSn = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_PROVIDER_MCH_ID);
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_ID, merchantSn);
        Integer subPayway = MapUtil.getInteger(transaction, Transaction.SUB_PAYWAY);
        String terminalSn = null;
        terminalSn = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_OTHER_B2C_TERMINAL_ID);
        //终端号
        requestBuilder.set(RequestFields.S_CARD_ACCPTR_TERMNL_ID, terminalSn);

        requestBuilder.set(RequestFields.S_FWD_INST_ID_CODE, PabConstant.RETAIL);

        //外部订单号 取流水号 保证唯一。
        requestBuilder.set(RequestFields.S_ORDER_NO, tsn);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        requestBuilder.set(RequestFields.S_AMT_TRANS, org.apache.commons.lang3.StringUtils.leftPad(amount + "", 12, "0"));
        requestBuilder.set(RequestFields.S_CURRCY_CODE_TRANS, PabConstant.RMB);

        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());

        String ip = terminalInfo.getIp();

        if (Objects.isNull(ip)) {
            ip = PabConstant.DEFAULT_IP;
        }

        String iPv4Address = getIPv4Address(ip);

        requestBuilder.set(RequestFields.S_MCHT_IP, iPv4Address);

        String sTransMedia = null;
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        if (payway == Payway.ALIPAY.getCode() || payway == Payway.ALIPAY2.getCode()) {
            sTransMedia = PabConstant.ALIPAY;
        } else if (payway == Payway.WEIXIN.getCode()) {
            sTransMedia = PabConstant.WEIXIN;
            requestBuilder.set(RequestFields.S_TRADE_TYPE, PabConstant.JSAPI);
        }

        requestBuilder.set(RequestFields.S_TRANS_MEDIA, sTransMedia);

        if (Order.PAYWAY_WEIXIN == payway) {
            //子商户公众号id
            String subAppid = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID);
            if (subPayway == SubPayway.MINI.getCode()) {
                String minSubAppId = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_WEIXIN_MINI_SUB_APP_ID);
                if (!StringUtils.isEmpty(minSubAppId)) {
                    subAppid = minSubAppId;
                }
            }

            requestBuilder.set(RequestFields.S_SUB_APPID, subAppid);

            Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

            // 优先使用前端上送的sub_app_id
            String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, ProtocolFields.SUB_APP_ID);
            if (!StringUtil.empty(sendSubAppId)) {
                requestBuilder.set(RequestFields.S_SUB_APPID, sendSubAppId);
            }
            //用户id
            String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
            requestBuilder.set(RequestFields.S_SUB_OPENID, payerUid);
        }

        if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
            if (payerUid != null) {
                requestBuilder.set(RequestFields.S_BUYER_ID, payerUid);
            }
        }
        requestBuilder.set(RequestFields.S_ENCRYPT_FLAG, PabConstant.ASYMMETRIC_ENCRYPTION);

        String sSigncertID = MapUtil.getString(tradeParams, TransactionParam.S_SIGNCERT_ID);

        requestBuilder.set(RequestFields.S_SIGNCERT_ID, sSigncertID);

        return requestBuilder;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String respCode = MapUtil.getString(result, ResponseFields.S_RESP_CODE, "");
        String retMsg = MapUtils.getString(result, ResponseFields.S_TRANS_STATE_MSG, "");//响应描述

        //原始交易状态
        String sOrgTransState = MapUtils.getString(result, ResponseFields.S_ORG_TRANS_STATE, "");//响应描述

        map.put(ResponseFields.S_RESP_CODE, respCode);//返回信息
        map.put(ResponseFields.S_TRANS_STATE_MSG, retMsg);
        Boolean success = false;
        if (key.equals(OP_QUERY)) {
            success = sOrgTransState.equals(PabConstant.SUCCESS);
        } else if (OP_PRECREATE.equals(key) || OP_PAY.equals(key) || OP_REFUND.equals(key)) {
            success = PabConstant.RET_CODE_SUCCESS.equals(respCode);

        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, success, respCode, retMsg);
    }

    public Map<String, Object> retryIfNetworkException(PabRequestBuilder requestBuilder, String gateway
            , String privateKey, Map<String, String> headers, String userId, int times, String logFlag) throws MpayException, MpayApiNetworkError {

        MpayApiNetworkError tex = null;
        for (int i = 0; i < times; ++i) {
            try {
                return pabClient.call(requestBuilder, gateway, privateKey, userId);
            } catch (MpayApiNetworkError ex) {
                tex = ex;
                LOGGER.warn("encountered ioex in  pab {}", logFlag, ex);
            }
        }
        LOGGER.error(String.format("still network i/o error after retrying %d times.", times));
        throw tex;
    }

    private String buildPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String retCode = MapUtil.getString(result, ResponseFields.S_RESP_CODE);

        if (PabConstant.RET_CODE_SUCCESS.equals(retCode)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if (Order.PAYWAY_ALIPAY2 == payway) {
                String tradeNo = MapUtils.getString(result, ResponseFields.S_TRADE_NO);
                //前面两个入参截断。
                tradeNo = tradeNo.substring(2);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            } else if (Order.PAYWAY_WEIXIN == payway) {
                String data = MapUtils.getString(result, ResponseFields.SWC_PAY_DATA);
                Map<String, Object> wxDataPackage = wxDataFormatConvert(data);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wxDataPackage);
            }
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private Map<String, Object> wxDataFormatConvert(String data) {
        Map<String, Object> wxData = JsonUtil.jsonStrToObject(data, Map.class);
        Map<String, Object> result = new HashMap<>();
        result.put(WapFields.APP_ID, MapUtil.getString(wxData, ResponseFields.PAB_APP_ID));
        result.put(WapFields.PACKAGE, MapUtil.getString(wxData, ResponseFields.PAB_PACKAGE_NAME));
        result.put(WapFields.NONCE_STR, MapUtil.getString(wxData, ResponseFields.PAB_NONCE_STR));
        result.put(WapFields.SIGN_TYPE, MapUtil.getString(wxData, ResponseFields.PAB_SIGN_TYPE));
        result.put(WapFields.PAY_SIGN, MapUtil.getString(wxData, ResponseFields.PAB_PAY_SIGN));
        result.put(WapFields.TIME_STAMP, MapUtil.getString(wxData, ResponseFields.PAB_TIME_STAMP));
        return result;
    }

    public static String getIPv4Address(String address) {
        try {
            InetAddress inetAddress = InetAddress.getByName(address);
            if (inetAddress instanceof java.net.Inet4Address) {
                // 如果该地址是 IPv4 地址，则直接返回
                return address;
            } else if (inetAddress instanceof java.net.Inet6Address) {
                // 如果该地址是 IPv6 地址，则获取本机的 IPv4 地址
                Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                while (networkInterfaces.hasMoreElements()) {
                    NetworkInterface ni = networkInterfaces.nextElement();
                    Enumeration<InetAddress> ips = ni.getInetAddresses();
                    while (ips.hasMoreElements()) {
                        InetAddress ip = ips.nextElement();
                        if (ip instanceof java.net.Inet4Address) {
                            return ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("获取ipv4地址失败", e);
        }
        return PabConstant.DEFAULT_IP;
    }

}



