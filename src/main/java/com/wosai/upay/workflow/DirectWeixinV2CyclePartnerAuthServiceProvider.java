package com.wosai.upay.workflow;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.constant.SupportConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.exception.ValidationException;
import com.wosai.upay.model.api.AuthResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.ProviderAuthService;
import com.wosai.upay.service.SupportService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.LocalDateTimeUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import static com.wosai.mpay.api.weixin.WeixinConstants.CHARSET_UTF8;

/**
 * <AUTHOR>
 * @description 微信周期代扣款 服务商模式签约服务
 * @link <a href="https://pay.weixin.qq.com/doc/v2/partner/**********">微信周期代扣费签约相关文档</a>
 * @date 2025-04-27
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class DirectWeixinV2CyclePartnerAuthServiceProvider extends DirectWeixinV2CycleServiceProvider implements ProviderAuthService {
    public static final Logger logger = LoggerFactory.getLogger(DirectWeixinV2CyclePartnerAuthServiceProvider.class);
    public static final String NAME = "provider.weixin.cycle.v2.partner.auth";

    @Resource
    private ObjectMapper om;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getProductCode() {
        return TransactionParam.SQB_PRODUCT_CODE_WEIXIN_CYCLE;
    }

    @Override
    public boolean isMatch(Map<String, Object> request, Map<String, Object> tradeConfig) {
        //默认是服务商模式
        Map<String, Object> tradeParams = getSubPaywayTradeParams(request, tradeConfig);
        int serviceMode = MapUtil.getIntValue(tradeParams, TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_PARTNER);
        return TransactionParam.SERVICE_MODE_PARTNER == serviceMode;
    }

    /**
     * 处理扩展参数，子类可以重写此方法
     *
     * @param extended 扩展参数
     */
    protected void processExtendedParams(Map<String, Object> extended) {
        // 默认实现不做任何处理
    }

    @Override
    public Map<String, Object> call(Map<String, Object> config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError {
        String appKey = MapUtils.getString(config, TransactionParam.WEIXIN_APP_KEY);
        return client.call(serviceUrl, WeixinClient.SIGN_TYPE_WEIXIN_ADD, appKey, null, request);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        //该通道禁止根据canHandle来匹配，只能根据product_code来匹配，见SupportServiceImpl.getProviderAuthServiceByProductCode
        return false;
    }

    @Override
    public AuthResponse auth(Map<String, Object> request, Map<String, Object> tradeConfig) {
        logger.info("微信周期代扣款签约, request={}", JacksonUtil.toJsonString(request));
        String terminalSn = MapUtils.getString(request, SupportService.TERMINAL_SN);
        int subPayway = MapUtils.getIntValue(request, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_MINI);

        //查询交易参数
        Map<String, Object> tradeParams = getSubPaywayTradeParams(request, tradeConfig);
        Map<String, Object> extended = getExtendedParam(request);

        Map<String, Object> result = new HashMap<>();

        //根据子支付方式，构建参数
        Map<String, Object> defaultRequestParams = getDefaultRequestParams(tradeParams);
        Map<String, Object> authParams = new HashMap<>(16);
        authParams.putAll(defaultRequestParams);
        authParams.putAll(extended);
        authParams.put(ProtocolFields.TIMESTAMP, LocalDateTimeUtil.getEpochSecond());

        if (Order.SUB_PAYWAY_WAP == subPayway || Order.SUB_PAYWAY_H5 == subPayway) {
            if (Order.SUB_PAYWAY_H5 == subPayway) {
                //h5需要指定signType
                authParams.put(ProtocolFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_MD5);
            }
            String serverUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_AUTH_APPLY);
            String authPath = AUTH_PATH_OF_SUB_PAYWAY.get(subPayway);
            //加签
            String sign = getSign(authParams, tradeParams);
            String sortedContent = getSortedContent(authParams);
            String authUrl = String.format("%s/%s?%s&sign=%s", serverUrl, authPath, sortedContent, sign);
            result.put(SupportConstant.AUTH_URL, authUrl);
        } else if (Order.SUB_PAYWAY_MINI == subPayway) {
            //加签
            String sign = getSign(authParams, tradeParams);
            authParams.put(ProtocolFields.SIGN, sign);
            //对notify_url进行urlEncode
            urlEncodeOfKey(authParams, BusinessFields.NOTIFY_URL);
            result.put(SupportConstant.AUTH_PARAMS, authParams);
        } else if (Order.SUB_PAYWAY_APP == subPayway) {
            String defaultUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_AUTH_APPLY);
            String authPath = AUTH_PATH_OF_SUB_PAYWAY.get(subPayway);
            String serverUrl = String.format("%s/%s", defaultUrl, authPath);
            //加签
            String sign = getSign(authParams, tradeParams);
            authParams.put(ProtocolFields.SIGN, sign);
            Map<String, Object> weixinResult;
            try {
                weixinResult = retryIfNetworkException(tradeParams, serverUrl, authParams, 1, OP_DEPOSIT_AUTH_APPLY);
            } catch (Exception e) {
                logger.error("微信周期代扣款App预签约异常, error={}", e.getMessage(), e);
                return AuthResponse.ofFailed(null, e.getMessage());
            }

            //处理微信响应结果
            Triple<Boolean, String, String> responseStatus = processWeixinResult(weixinResult);
            if (!responseStatus.getLeft()) {
                return AuthResponse.ofFailed(responseStatus.getMiddle(), responseStatus.getRight());
            }

            //获取预签约id
            String preEntrustwebId = MapUtils.getString(weixinResult, BusinessFields.PRE_AUTH_ID);
            result.put(BusinessFields.PRE_AUTH_ID, preEntrustwebId);
        } else {
            logger.warn("微信周期代扣款签约, 不支持的子支付方式, terminalSn={}, subPayway={}", terminalSn, subPayway);
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }

        return AuthResponse.ofSuccess(result);
    }

    @Override
    public AuthResponse authQuery(Map<String, Object> request, Map<String, Object> tradeConfig) {
        logger.info("微信周期代扣款签约查询, request={}", JacksonUtil.toJsonString(request));
        return handleAuthOperation(request, tradeConfig, OP_DEPOSIT_AUTH_QUERY);
    }

    @Override
    public AuthResponse authTerminate(Map<String, Object> request, Map<String, Object> tradeConfig) {
        logger.info("微信周期代扣款解约, request={}", JacksonUtil.toJsonString(request));
        return handleAuthOperation(request, tradeConfig, OP_DEPOSIT_AUTH_TERMINATE);
    }

    /**
     * 处理签约相关操作，如签约查询、解约等
     *
     * @param request     请求参数
     * @param tradeConfig 交易配置
     * @param opFlag      操作标识
     * @return 处理结果
     */
    private AuthResponse handleAuthOperation(Map<String, Object> request, Map<String, Object> tradeConfig, String opFlag) {
        //查询交易参数
        Map<String, Object> tradeParams = getSubPaywayTradeParams(request, tradeConfig);
        Map<String, Object> extended = getExtendedParam(request);

        //构建请求
        Map<String, Object> requestParams = getDefaultRequestParams(tradeParams);
        requestParams.putAll(extended);
        requestParams.put(ProtocolFields.VERSION, VERSION_ONE);

        Map<String, Object> weixinResult;
        String serverUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag);
        try {
            weixinResult = retryIfNetworkException(tradeParams, serverUrl, requestParams, 1, opFlag);
        } catch (Exception e) {
            String logPrefix = opFlag.equals(OP_DEPOSIT_AUTH_QUERY) ? "微信周期代扣款签约关系查询异常" : "微信周期代扣款申请解约异常";
            logger.error("{}, error={}", logPrefix, e.getMessage(), e);
            return AuthResponse.ofFailed(null, e.getMessage());
        }

        //处理微信响应结果
        Triple<Boolean, String, String> responseStatus = processWeixinResult(weixinResult);
        if (!responseStatus.getLeft()) {
            return AuthResponse.ofFailed(responseStatus.getMiddle(), responseStatus.getRight());
        }

        //移除冗余字段
        removeUnnecessaryFields(weixinResult);
        return AuthResponse.ofSuccess(weixinResult);
    }

    /**
     * 构建默认的请求参数
     *
     * @param tradeConfig 交易配置
     * @return 默认请求参数
     */
    protected Map<String, Object> getDefaultRequestParams(Map<String, Object> tradeConfig) {
        Map<String, Object> requestParams = new HashMap<>(8);
        requestParams.put(ProtocolFields.APP_ID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_APP_ID));
        requestParams.put(ProtocolFields.MCH_ID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_MCH_ID));
        requestParams.put(ProtocolFields.SUB_MCH_ID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_SUB_MCH_ID));
        return requestParams;
    }

    /**
     * 移除不必要的字段
     *
     * @param result 结果
     */
    private void removeUnnecessaryFields(Map<String, Object> result) {
        result.remove(ResponseFields.RETURN_CODE);
        result.remove(ResponseFields.RESULT_CODE);
    }

    /**
     * 获取扩展参数
     *
     * @param request 请求
     * @return 扩展参数
     */
    @SuppressWarnings("ALL")
    private Map<String, Object> getExtendedParam(Map<String, Object> request) {
        String terminalSn = MapUtils.getString(request, SupportService.TERMINAL_SN);
        Map<String, Object> extended = UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);
        if (MapUtils.isEmpty(extended)) {
            logger.warn("微信周期代扣款签约相关接口, 入参无效, extended参数不能为空, terminalSn={}", terminalSn);
            throw new ValidationException("extended参数不能为空");
        }

        // 子类可以对扩展参数进行处理
        processExtendedParams(extended);

        return extended;
    }

    /**
     * 获取签名
     *
     * @param params      参数
     * @param tradeParams 交易参数
     * @return 签名
     */
    private String getSign(Map<String, Object> params, Map<String, Object> tradeParams) {
        try {
            String weixinAppKey = MapUtils.getString(tradeParams, TransactionParam.WEIXIN_APP_KEY);
            return WeixinSignature.getSign(params, weixinAppKey, CHARSET_UTF8);
        } catch (MpayException e) {
            logger.error("微信周期代扣款签约, 加签失败, error={}", e.getMessage(), e);
            throw new UpayBizException("加签失败");
        }
    }

    /**
     * 获取排序后的内容
     *
     * @param content 内容
     * @return 排序后的内容
     */
    private String getSortedContent(Map<String, Object> content) {
        //对notify_url进行urlEncode
        urlEncodeOfKey(content, BusinessFields.NOTIFY_URL);
        return WeixinSignature.getSortedContent(content);
    }

    /**
     * 对指定key进行urlEncode
     *
     * @param map 参数map
     * @param key 需要编码的key
     */
    private void urlEncodeOfKey(Map<String, Object> map, String key) {
        String value = MapUtils.getString(map, key);
        if (StringUtils.isEmpty(value)) {
            return;
        }

        try {
            map.put(key, URLEncoder.encode(value, WeixinConstants.CHARSET_UTF8));
        } catch (UnsupportedEncodingException e) {
            logger.error("{} encode error: value={}, error={}", key, value, e.getMessage(), e);
        }
    }
}
