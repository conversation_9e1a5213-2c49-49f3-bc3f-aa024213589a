package com.wosai.upay.workflow;

import com.google.common.collect.Sets;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.fuyou.FuyouConstants;
import com.wosai.mpay.api.fuyou.FuyouResponseFields;
import com.wosai.mpay.api.fuyou.bank.*;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.swing.plaf.metal.MetalRootPaneUI;
import java.util.*;

@ServiceProvicerPriority(priority = 3)
public class FuyouBankServiceProvider extends AbstractServiceProvider {
    private static final String NAME = "provider.fuyou.bank";
    public static final Logger logger = LoggerFactory.getLogger(FuyouBankServiceProvider.class);
    public static final String QUERY_FORMAT_PROVIDER_NAME = "%s." + OP_QUERY;
    @Resource
    FuyouBankClient fuyouBankClient;
    private static final SafeSimpleDateFormat SETTLE_DATE_FORMAT = new SafeSimpleDateFormat(FuyouBankConstants.SETTLE_DATE);
    private static final SafeSimpleDateFormat CREATE_TIME_FORMAT = new SafeSimpleDateFormat(FuyouBankConstants.CREATE_TIME_FORMAT);

    public static final Set<String> PAY_STATUS_SUCCESS = Sets.newHashSet(FuyouBankConstants.PAY_STATUS_SUCCESS, FuyouBankConstants.PAY_STATUS_REFUND_SUCCESS, FuyouBankConstants.PAY_STATUS_CANCEL_SUCCESS);

    public static final Map<String, String> DCC_FLG_MAP = MapUtil.hashMap(FuyouResponseFields.BusiCdEnum.TX68.getCode(), FuyouResponseFields.BusiCdEnum.TX68.getMeaning(), FuyouResponseFields.BusiCdEnum.TX70.getCode(), FuyouResponseFields.BusiCdEnum.TX70.getMeaning());


    private static final Map<String, String> CARD_TYPE_MAPPING = MapUtil.hashMap(
            FuyouBankConstants.CARD_TYPE_DEBIT, Payment.TYPE_BANKCARD_DEBIT,
            FuyouBankConstants.CARD_TYPE_CREDIT, Payment.TYPE_BANKCARD_CREDIT,
            FuyouBankConstants.CARD_TYPE_PREPAID_CARD, Payment.TYPE_BANKCARD_PREPAID,
            FuyouBankConstants.CARD_TYPE_SEMI_CREDIT, Payment.TYPE_BANKCARD_SEMI_CREDIT
    );

    private static final Map<Integer, String> DEPOSIT_SUCCESS_WORKFLOW_MSG = MapUtil.hashMap(
            Transaction.TYPE_DEPOSIT_FREEZE, Workflow.RC_PAY_SUCCESS,
            Transaction.TYPE_DEPOSIT_CANCEL, Workflow.RC_CANCEL_SUCCESS,
            Transaction.TYPE_DEPOSIT_CONSUME, Workflow.RC_CONSUME_SUCCESS
    );

    private String notifyHost;

    public String getNotifyHost() {
        return notifyHost;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_FUYOU;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return Objects.nonNull(getTradeParams(transaction))
                && MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Payway.BANKCARD.getCode()
                && existTermId(getTradeParams(transaction));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.FUYOU_TRADE_PARAMS);
    }

    private boolean existTermId(Map<String, Object> tradeParams) {
        return Objects.nonNull(tradeParams) && StringUtil.isNotEmpty(MapUtil.getString(tradeParams, TransactionParam.FUYOU_BANK_TERM_ID));
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("下单暂不支持");
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("撤单暂不支持");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Map<String,Object> result = null;
        try {
            result = doQueryProviderResponse(context);
        } catch (Exception e) {
            logger.error("fuyou bankcard query error tsn {} e:", tsn, e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return (e instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionQueryContextErrorInfo(result, context, OP_QUERY);
        setTransactionQueryIfSuccess(result, context, OP_QUERY);
        if (isTradeSuccess(result)) {
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            return type == Transaction.TYPE_PAYMENT ? Workflow.RC_PAY_SUCCESS : Workflow.RC_REFUND_SUCCESS;
        } else if (Objects.equals(FuyouBankConstants.PAY_STATUS_FAIL, getQueryTradeStatus(result))) {
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_IN_PROG;
    }


    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);

        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String, Object> refundRequest = null;
        if (MapUtil.getBooleanValue(extraOutFields, Transaction.IS_DEPOSIT, false)) {
            refundRequest =  buildDepositRefundRequest(context);
        }else {
            refundRequest = buildRefundRequest(context);
        }
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        String tsn = MapUtil.getString(context.getTransaction(), Transaction.TSN);

        boolean refundQueryFlag = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (refundQueryFlag) {
            return refundQueryResult(context);
        }
        Map<String, Object> result;
        try {
            result = retryRefundIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), refundRequest, getPrivateKeyContent(privateKey), 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("fuyou bankcard refund error tsn {} e:", tsn, e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return (e instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_ERROR;
        }
        extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);

        setTransactionRefundContextErrorInfo(result, context, OP_REFUND);
        setTransactionRefundIfSuccess(result, context);
        if (isRefundSuccess(result)) {
            return refundQueryResult(context);
        }
        return Workflow.RC_ERROR;
    }

    /**
     * 构建退款请求
     *
     * @param context
     * @return
     */
    private Map<String, Object> buildRefundRequest(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String orderSn = MapUtil.getString(order, Order.SN);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        String orgTradeNo = MapUtil.getString(order, Order.TRADE_NO);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        String paySettleDate = MapUtil.getString(extraOutFields, Transaction.PAY_SETTLE_DATE);
        String termId = MapUtil.getString(tradeParams, TransactionParam.FUYOU_BANK_TERM_ID);
        String providerMchId = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PROVIDER_MCH_ID);

        FuyouBankRequestBuilder builder = new FuyouBankRequestBuilder();
        builder.set(FuyouBankProtocolFields.VERSION, FuyouConstants.DEFAULT_VERSION);
        builder.set(FuyouBankBusinessFields.INS_CD, MapUtil.getString(tradeParams, TransactionParam.FUYOU_AGENT_NO));
        builder.set(FuyouBankBusinessFields.MCH_NT_CD, providerMchId);
        builder.set(FuyouBankBusinessFields.EX_ORDER_NO, tsn);
        builder.set(FuyouBankBusinessFields.SRC_EX_ORDER_NO, orderSn);
        builder.set(FuyouBankBusinessFields.FY_TRACE_NO, orgTradeNo);
        builder.set(FuyouBankBusinessFields.RANDOM_STR, System.currentTimeMillis() + "");
        builder.set(FuyouBankBusinessFields.SRC_ORDER_DATE, paySettleDate);
        builder.set(FuyouBankBusinessFields.REFUND_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(FuyouBankBusinessFields.SRC_TOTAL_AMT, MapUtil.getString(order, Order.EFFECTIVE_TOTAL));
        builder.set(FuyouBankBusinessFields.FY_TERM_ID, termId);
        builder.set(FuyouBankBusinessFields.RESERVER_JNL_NO, tsn);
        builder.set(FuyouBankBusinessFields.SRC_ORDER_TYPE, FuyouBankConstants.ORDER_TYPE_CARD);
        return builder.build();
    }

    /**
     * 构建退款请求
     *
     * @param context
     * @return
     */
    private Map<String, Object> buildDepositRefundRequest(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(context.getTransaction(), MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        String conusmerTsn = MapUtil.getString(payOrConsumerTransaction, Transaction.TSN);
        String orgTradeNo = MapUtil.getString(payOrConsumerTransaction, Transaction.TRADE_NO);
        Map extraOutFields = MapUtil.getMap(payOrConsumerTransaction, Transaction.EXTRA_OUT_FIELDS);
        String paySettleDate = MapUtil.getString(extraOutFields, Transaction.PAY_SETTLE_DATE);
        String termId = MapUtil.getString(tradeParams, TransactionParam.FUYOU_BANK_TERM_ID);
        String providerMchId = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PROVIDER_MCH_ID);
        FuyouBankRequestBuilder builder = new FuyouBankRequestBuilder();
        builder.set(FuyouBankProtocolFields.VERSION, FuyouConstants.DEFAULT_VERSION);
        builder.set(FuyouBankBusinessFields.INS_CD, MapUtil.getString(tradeParams, TransactionParam.FUYOU_AGENT_NO));
        builder.set(FuyouBankBusinessFields.MCH_NT_CD, providerMchId);
        builder.set(FuyouBankBusinessFields.EX_ORDER_NO, tsn);
        builder.set(FuyouBankBusinessFields.SRC_EX_ORDER_NO, conusmerTsn);
        builder.set(FuyouBankBusinessFields.FY_TRACE_NO, orgTradeNo);
        builder.set(FuyouBankBusinessFields.RANDOM_STR, System.currentTimeMillis() + "");
        builder.set(FuyouBankBusinessFields.SRC_ORDER_DATE, paySettleDate);
        builder.set(FuyouBankBusinessFields.REFUND_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(FuyouBankBusinessFields.SRC_TOTAL_AMT, MapUtil.getString(payOrConsumerTransaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(FuyouBankBusinessFields.FY_TERM_ID, termId);
        builder.set(FuyouBankBusinessFields.RESERVER_JNL_NO, tsn);
        builder.set(FuyouBankBusinessFields.SRC_ORDER_TYPE, FuyouBankConstants.ORDER_TYPE_CARD);
        return builder.build();
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        mockTransactionPrecreateSuccess(context);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    public String depositCancel(TransactionContext context) {
        forceReturn(context);
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Map<String, Object> result = null;
        try {
            result = doQueryProviderResponse(context);
        } catch (Exception e) {
            logger.error("fuyou bankcard deposit cancel error tsn {} e:", tsn, e);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CANCEL, e);
            return (e instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionQueryContextErrorInfo(result, context, OP_DEPOSIT_CANCEL);
        setTransactionQueryIfSuccess(result, context, OP_DEPOSIT_CANCEL);
        return doDepositQueryByResult(context, result);
    }

    @Override
    public String depositConsume(TransactionContext context) {
        forceReturn(context);
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Map<String, Object> result = null;
        try {
            result = doQueryProviderResponse(context);
        } catch (Exception e) {
            logger.error("fuyou bankcard deposit consume error tsn {} e:", tsn, e);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CONSUME, e);
            return (e instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionQueryContextErrorInfo(result, context, OP_DEPOSIT_CONSUME);
        setTransactionQueryIfSuccess(result, context, OP_DEPOSIT_CONSUME);
        return doDepositQueryByResult(context, result);
    }


    private String doDepositQueryByResult(TransactionContext context, Map result) {
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        String inProg = type == Transaction.TYPE_DEPOSIT_FREEZE ? Workflow.RC_IN_PROG : Workflow.RC_RETRY;
        String tradePayType = getTradePayType(result);
        String queryTradeStatus = getQueryTradeStatus(result);
        boolean isSuccess = false;
        if (Objects.equals(queryTradeStatus, FuyouBankConstants.PAY_STATUS_FAIL)) {
            return Workflow.RC_ERROR;
        }
        if (StringUtil.isNotEmpty(tradePayType) && StringUtil.isNotEmpty(queryTradeStatus)) {
            if (type == Transaction.TYPE_DEPOSIT_FREEZE || type == Transaction.TYPE_DEPOSIT_CONSUME) {
                isSuccess = Objects.equals(tradePayType, FuyouBankConstants.PAY_TYPE_PRE_AUTHORIZATION) && Objects.equals(queryTradeStatus, FuyouBankConstants.PAY_STATUS_SUCCESS);
            } else if (type == Transaction.TYPE_DEPOSIT_CANCEL) {
                isSuccess = Objects.equals(tradePayType, FuyouBankConstants.PAY_TYPE_PRE_AUTHORIZATION) && Objects.equals(queryTradeStatus, FuyouBankConstants.PAY_STATUS_CANCEL_SUCCESS);
            }
        }
        if (isSuccess) {
            return DEPOSIT_SUCCESS_WORKFLOW_MSG.get(type);
        }
        return inProg;
    }

    @Override
    public String depositQuery(TransactionContext context) {
        forceReturn(context);
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Map<String, Object> result = null;
        try {
            result = doQueryProviderResponse(context);
        } catch (Exception e) {
            logger.error("fuyou bankcard deposit query error tsn {} e:", tsn, e);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_QUERY, e);
            return (e instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionQueryContextErrorInfo(result, context, OP_DEPOSIT_QUERY);
        setTransactionQueryIfSuccess(result, context, OP_DEPOSIT_QUERY);
        return doDepositQueryByResult(context, result);
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        wapRequest.put(ResponseV3Fields.PAY_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        return Workflow.RC_CREATE_SUCCESS;
    }

    protected Map doQueryProviderResponse(TransactionContext context) throws Exception {
        FuyouBankRequestBuilder builder = new FuyouBankRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        builder.set(FuyouBankBusinessFields.IN_ORDER_NO, tsn);
        builder.set(FuyouBankBusinessFields.RELATE_INS_CD, MapUtil.getString(tradeParams, TransactionParam.FUYOU_AGENT_NO));
        long tradeCtime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
        String tradeCtimeStr = SETTLE_DATE_FORMAT.format(new Date(tradeCtime));
        builder.set(FuyouBankBusinessFields.KBPS_SRC_SETTLE_DT, tradeCtimeStr);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_BANK_QUERY_PRIVATE_KEY);
        return retryQueryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(String.format(QUERY_FORMAT_PROVIDER_NAME, getName(), OP_QUERY), OP_QUERY), builder.build(), getPrivateKeyContent(privateKey), 1, OP_QUERY);
    }

    private void forceReturn(TransactionContext context) {
        if (!context.isForceReturn()) {
            context.setForceReturn(true);
        }
    }

    private String refundQueryResult(TransactionContext context) {
        if (Objects.equals(Workflow.RC_REFUND_SUCCESS, query(context))) {
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_RETRY;
    }

    protected void mockTransactionPrecreateSuccess(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        wapRequest.put(ResponseV3Fields.COUNTER_URL, "");
        wapRequest.put(ResponseV3Fields.PAY_ORDER_NO, MapUtil.getString(context.getTransaction(), Transaction.TSN));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
    }

    private Map<String, Object> retryQueryIfNetworkException(String serviceUrl, Map<String, Object> request, String privateKey, int times, String opFlag) throws Exception {
        return retryIfNetworkException(() -> fuyouBankClient.queryCall(serviceUrl, privateKey, request), logger, times, opFlag, this.getName());
    }

    private Map<String, Object> retryRefundIfNetworkException(String serviceUrl, Map<String, Object> request, String privateKey, int times, String opFlag) throws Exception {
        return retryIfNetworkException(() -> fuyouBankClient.refundCall(serviceUrl, privateKey, request), logger, times, opFlag, this.getName());
    }

    protected void setTransactionQueryContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String resultCode = MapUtil.getString(result, FuyouBankProtocolFields.CODE);//业务返回码
        String resultMsg = MapUtil.getString(result, FuyouBankProtocolFields.MSG);//业务返回信息
        String payStatus = null;
        String payMsg = null;
        if (FuyouBankConstants.QUERY_SUCCESS_CODE.equals(resultCode)) {
            Map resultData = MapUtil.getMap(result, FuyouBankProtocolFields.DATA);
            if (resultData != null) {
                payStatus = MapUtil.getString(resultData, FuyouBankResponseFields.PAY_STATUS);//业务返回码
                payMsg = MapUtil.getString(resultData, FuyouBankResponseFields.PAY_MSG);//业务返回码
                map.put(BusinessV2Fields.STATUS_CODE, payStatus);//返回订单状态码
                map.put(BusinessV2Fields.STATUS_DESC, payMsg);//返回信息
                //如果接口协议上没有问题 记录返回的状态
                resultCode = MapUtil.getString(resultData, FuyouBankResponseFields.KBPS_RSP_CD);
                resultMsg = MapUtil.getString(resultData, FuyouBankResponseFields.KBPS_RSP_DESC);
            }
        }
        map.put(BusinessV2Fields.CODE, resultCode);//返回状态码
        map.put(BusinessV2Fields.MSG, resultMsg);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isTradeSuccess(result),
                StringUtil.isNotEmpty(payStatus) ? payStatus : resultCode,
                StringUtil.isNotEmpty(payMsg) ? payMsg : resultMsg);
    }

    protected void setTransactionRefundContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String resultCode = MapUtil.getString(result, FuyouBankProtocolFields.RESULT_CODE);//业务返回码
        String resultMsg = MapUtil.getString(result, FuyouBankProtocolFields.RESULT_MSG);//业务返回信息
        map.put(BusinessV2Fields.CODE, resultCode);//返回状态码
        map.put(BusinessV2Fields.MSG, resultMsg);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isTradeSuccess(result), resultCode, resultMsg);
    }

    private void setTransactionQueryIfSuccess(Map<String, Object> result, TransactionContext context, String op) {
        if (isTradeSuccess(result)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String, Object> order = context.getOrder();
            Map<String, Object> tradeParams = getTradeParams(transaction);
            int tradeType = MapUtil.getIntValue(transaction, Transaction.TYPE);
            Map resultData = MapUtil.getMap(result, FuyouBankProtocolFields.DATA);

            //手续费走通知接口 https://fundwx.fuiou.com/doc/#/scanpay/api?id=_315-%e4%ba%a4%e6%98%93%e6%89%8b%e7%bb%ad%e8%b4%b9%e6%8e%a8%e9%80%81%e6%8e%a5%e5%8f%a3
            tradeParams.put(TransactionParam.FEE, 0);

            String payStatus = MapUtil.getString(resultData, FuyouBankResponseFields.PAY_STATUS);
            //设置支付机构交易流水号
            String tradeNo = MapUtil.getString(resultData, FuyouBankResponseFields.OUT_TRADE_NO);
            if (StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
            if (StringUtil.isEmpty(MapUtil.getString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, tradeNo);
            }

            //设置通道源流水号
            String channelTradeNo = MapUtil.getString(resultData, FuyouBankResponseFields.CHANNEL_TRADE_NO);
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (!StringUtils.isEmpty(channelTradeNo)) {
                if (extraOutFields == null) {
                    extraOutFields = new HashMap<>();
                    transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                }
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
            }
            String terminalTrace = MapUtil.getString(resultData, FuyouBankResponseFields.TERMINAL_TRACE);
            if (!StringUtils.isEmpty(terminalTrace)) {
                extraOutFields.put(Transaction.SYS_TRACE_NO, terminalTrace);
            }
            String settleDate = MapUtil.getString(resultData, FuyouBankResponseFields.SETTLE_DATE);
            if (!StringUtils.isEmpty(settleDate)) {
                extraOutFields.put(Transaction.BATCH_BILL_NO, settleDate);
            }
            //设置结算日期 退款时需要 （不确定上面的批次号是否以后会替换成别的业务属性 因此还是单独存在一个确定的字段中
            String oldSettleDate = MapUtil.getString(extraOutFields, Transaction.PAY_SETTLE_DATE);
            if ((tradeType == Transaction.TYPE_PAYMENT || tradeType == Transaction.TYPE_DEPOSIT_CONSUME) && StringUtil.isEmpty(oldSettleDate) && StringUtil.isNotEmpty(settleDate)) {
                extraOutFields.put(Transaction.PAY_SETTLE_DATE, settleDate);
            }

            //设置payments
            long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            if (Objects.equals(FuyouBankConstants.PAY_STATUS_SUCCESS, payStatus)) {
                amount = MapUtil.getLongValue(resultData, FuyouBankResponseFields.FEE_AMT, amount);
            } else if (Objects.equals(FuyouBankConstants.PAY_STATUS_REFUND_SUCCESS, payStatus)) {
                amount = MapUtil.getLongValue(resultData, FuyouBankResponseFields.REFUND_FEE, amount);
            }
            String cardType = MapUtil.getString(resultData, FuyouBankResponseFields.CARD_TYPE);
            String paymentType = CARD_TYPE_MAPPING.getOrDefault(cardType, Payment.TYPE_BANKCARD);
            List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();
            payments.add(MapUtil.hashMap(
                    Transaction.PAYMENT_TYPE, paymentType,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardType,
                    Transaction.PAYMENT_AMOUNT, amount
            ));
            extraOutFields.put(Transaction.PAYMENTS, payments);

            //设置银行卡卡号
            String cardNo = MapUtil.getString(resultData, FuyouBankResponseFields.CARDNO);
            if (StringUtil.isEmpty(MapUtil.getString(order, Order.BUYER_UID))) {
                order.put(Order.BUYER_UID, cardNo);
            }
            if (StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
                transaction.put(Transaction.BUYER_UID, cardNo);
            }
            if (StringUtil.isEmpty(MapUtil.getString(order, Order.BUYER_LOGIN))) {
                order.put(Order.BUYER_LOGIN, cardNo);
            }
            if (StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
                transaction.put(Transaction.BUYER_LOGIN, cardNo);
            }

            //设置channel_finish_time 查询接口中只有返回 交易时间(createtime) 取当前时间
            if (Objects.isNull(transaction.get(Transaction.CHANNEL_FINISH_TIME))) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            }
            boolean isDeposit = MapUtil.getBooleanValue((Map)transaction.get(Transaction.EXTRA_OUT_FIELDS), Transaction.IS_DEPOSIT, false);

            if (isDeposit) {
                extraOutFields.put(Transaction.REFER_NUMBER, tradeNo);
                if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                    extraOutFields.put(Transaction.CONSUME_TIME, MapUtil.getLongValue(transaction, com.wosai.data.dao.DaoConstants.CTIME));
                }
            }
        }
    }

    private void setTransactionRefundIfSuccess(Map<String, Object> result, TransactionContext context) {
        if (isRefundSuccess(result)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String, Object> order = context.getOrder();
            String refundTraceNo = MapUtil.getString(result, FuyouBankResponseFields.REFUND_TRACE_NO);
            if (StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
                transaction.put(Transaction.TRADE_NO, refundTraceNo);
            }
            if (StringUtil.isEmpty(MapUtil.getString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, refundTraceNo);
            }
            //接口中未返回信息
            if (Objects.isNull(transaction.get(Transaction.CHANNEL_FINISH_TIME))) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            }
        }
    }

    private boolean isRefundSuccess(Map<String, Object> refundResult) {
        return Objects.equals(FuyouBankConstants.REFUND_SUCCESS_CODE, MapUtil.getString(refundResult, FuyouBankProtocolFields.RESULT_CODE));
    }

    private boolean isTradeSuccess(Map<String, Object> result) {
        String queryTradeStatus = getQueryTradeStatus(result);
        if (StringUtil.isNotEmpty(queryTradeStatus)) {
            return PAY_STATUS_SUCCESS.contains(queryTradeStatus);
        }
        return false;
    }

    private String getQueryTradeStatus(Map<String, Object> result) {
        String resultCode = MapUtil.getString(result, FuyouBankProtocolFields.CODE);//业务返回码
        boolean isSuccess = FuyouBankConstants.QUERY_SUCCESS_CODE.equals(resultCode);
        if (!isSuccess) {
            return null;
        }
        Map resultData = MapUtil.getMap(result, FuyouBankProtocolFields.DATA);
        if (resultData != null) {
            return MapUtil.getString(resultData, FuyouBankResponseFields.PAY_STATUS);//业务返回码
        }
        return null;
    }

    private String getTradePayType(Map<String, Object> result) {
        String resultCode = MapUtil.getString(result, FuyouBankProtocolFields.CODE);//业务返回码
        boolean isSuccess = FuyouBankConstants.QUERY_SUCCESS_CODE.equals(resultCode);
        if (!isSuccess) {
            return null;
        }

        Map resultData = MapUtil.getMap(result, FuyouBankProtocolFields.DATA);
        if (resultData != null) {
            return MapUtil.getString(resultData, FuyouBankResponseFields.PAY_TYPE);//业务返回码
        }
        return null;
    }


    public static boolean isFuyouWildCard(Map<String, Object> transaction) {
        Map<String, Object> extraOutFileds = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        String wildCardType = MapUtil.getString(extraOutFileds, Transaction.WILD_CARD_TYPE);
        return StringUtil.isNotEmpty(wildCardType) && DCC_FLG_MAP.values().contains(wildCardType);
    }
}
