package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.uepay.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2020/6/10 Time: 4:11 下午
 */
public class UepayServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(UepayServiceProvider.class);

    public static final String NAME = "provider.uepay";
    private static final DateTimeFormatter TRADE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd Hh:mm:ss");
    private static final String NEED_TO_REFUND_CODE = "4012";


    @Autowired
    private UepayClient client;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_ALIPAY
                || payway == Order.PAYWAY_ALIPAY2
                || payway == Order.PAYWAY_WEIXIN;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UEPAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_UEPAY;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String key = MapUtil.getString(tradeParams, TransactionParam.UEPAY_SECRET_KEY);
        String merchantNo = MapUtil.getString(tradeParams, TransactionParam.UEPAY_MERCHANT_NO);
        String storeNo = MapUtil.getString(tradeParams, TransactionParam.UEPAY_STORE_CODE);
        String terminalNo = MapUtil.getString(tradeParams, TransactionParam.UEPAY_TERMINAL_CODE);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.bizSet(BusinessFields.AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.bizSet(BusinessFields.STORE_CODE, storeNo);
        builder.bizSet(BusinessFields.TERMINAL, terminalNo);
        builder.bizSet(BusinessFields.QR_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.getBizContent(), merchantNo
                    , UepayConstants.REQUEST_TYPE_MICROPAY, key, 1, OP_PAY);
        } catch (Exception e) {
            logger.error("failed to call uepay pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        if (needRefund(context.getOrder(), transaction)) {
            return cancelToRefundProcess(context);
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String key = MapUtil.getString(tradeParams, TransactionParam.UEPAY_SECRET_KEY);
        String merchantNo = MapUtil.getString(tradeParams, TransactionParam.UEPAY_MERCHANT_NO);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.getBizContent(), merchantNo
                    , UepayConstants.REQUEST_TYPE_REVOKE, key, 1, OP_CANCEL);
        } catch (Exception e) {
            logger.error("failed to call uepay cancel", e);
            setTransactionContextErrorInfo(context, OP_CANCEL, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(context, result);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String key = MapUtil.getString(tradeParams, TransactionParam.UEPAY_SECRET_KEY);
        String merchantNo = MapUtil.getString(tradeParams, TransactionParam.UEPAY_MERCHANT_NO);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.getBizContent(), merchantNo
                    , UepayConstants.REQUEST_TYPE_QUERY, key, 1, OP_QUERY);
        } catch (Exception e) {
            logger.error("failed to call uepay query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String key = MapUtil.getString(tradeParams, TransactionParam.UEPAY_SECRET_KEY);
        String merchantNo = MapUtil.getString(tradeParams, TransactionParam.UEPAY_MERCHANT_NO);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.bizSet(BusinessFields.REFUND_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.bizSet(BusinessFields.REFUND_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.getBizContent(), merchantNo
                    , UepayConstants.REQUEST_TYPE_REFUND, key, 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call uepay refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);
    }

    private Map<String, Object> retryIfNetworkException(String url, Map<String,String> bizParams, String merchantNo
            , String requestType, String secret, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(url, bizParams, merchantNo, requestType, secret);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in uepay {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String respCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String respMsg = MapUtil.getString(result, ResponseFields.MESSAGE);
        String resultStr = MapUtil.getString(result, ResponseFields.RESULT);
        map.put(ResponseFields.RET_CODE, respCode);
        map.put(ResponseFields.MESSAGE, respMsg);

        if (OP_QUERY.equals(key)) {
            Map bizResults = MapUtil.getMap(result, ResponseFields.RESULTS);
            String payLog = MapUtil.getString(bizResults, ResponseFields.PAY_LOG);
            String tradeState = MapUtil.getString(bizResults, ResponseFields.TRADE_STATE);
            map.put(ResponseFields.PAY_LOG, payLog);
            map.put(ResponseFields.TRADE_STATE, tradeState);
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, StringUtils.equals(resultStr, Boolean.TRUE.toString()), respCode, respMsg);
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTradeNoBuyerInfoIfExists(result, context);

        String resultStr = MapUtil.getString(result, ResponseFields.RESULT);
        if (StringUtils.equals(resultStr, Boolean.FALSE.toString())) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> bizResult = MapUtil.getMap(result, ResponseFields.RESULTS);
        String tradeState = MapUtil.getString(bizResult, ResponseFields.TRADE_STATE);
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_SUCCESS)) {
            resolvePayFund(context, bizResult);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_PAYERROR)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    private String buildCancelResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        Boolean resultBoolean = MapUtil.getBoolean(result, ResponseFields.RESULT);
        if (Boolean.TRUE.equals(resultBoolean)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        if (NEED_TO_REFUND_CODE.equals(retCode)) {
            return cancelToRefundProcess(context);
        }
        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        setTradeNoBuyerInfoIfExists(result, context);

        Map<String, Object> bizResult = MapUtil.getMap(result, ResponseFields.RESULTS);
        String tradeState = MapUtil.getString(bizResult, ResponseFields.TRADE_STATE);
        //支付
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_SUCCESS)) {
            resolvePayFund(context, bizResult);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_USERPAYING)) {
            return Workflow.RC_IN_PROG;
        }
        //退款
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_REFUNDYES)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_REFUND)) {
            return Workflow.RC_RETRY;
        }

        return Workflow.RC_ERROR;
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> bizResult = MapUtil.getMap(result, ResponseFields.RESULTS);
        String tradeState = MapUtil.getString(bizResult, ResponseFields.TRADE_STATE);
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_REFUNDYES)
                || StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_BACKOUT)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        if (StringUtils.equals(tradeState, UepayConstants.TRADE_STATE_REFUND)
                || MapUtils.isEmpty(bizResult)) {
            return query(context);
        }

        return Workflow.RC_ERROR;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();

        Map<String, Object> bizResult = MapUtil.getMap(result, ResponseFields.RESULTS);
        String thirdPartyBuyerId = MapUtil.getString(bizResult, ResponseFields.OPENID);
        String tranNo = MapUtil.getString(result, ResponseFields.TRAN_NO);

        if(!StringUtil.empty(thirdPartyBuyerId)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
                transaction.put(Transaction.BUYER_UID, thirdPartyBuyerId);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_UID))){
                order.put(Order.BUYER_UID, thirdPartyBuyerId);
            }
        }

        if(!StringUtil.empty(tranNo)){
            if(StringUtil.empty(tranNo)){
                transaction.put(Transaction.TRADE_NO, tranNo);
            }
            if(StringUtil.empty(tranNo)) {
                order.put(Order.TRADE_NO, tranNo);
            }
        }
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> bizResult) {
        Map<String, Object> transaction = context.getTransaction();

        //交易金额
        long transactionAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long channelFinishTime = getTradeTimestamp(bizResult);
        transaction.put(Transaction.PAID_AMOUNT, transactionAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
    }

    private void resolveRefundFund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        //本次退款总额
        long refundAmountTotal = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        transaction.put(Transaction.PAID_AMOUNT, refundAmountTotal);
        transaction.put(Transaction.RECEIVED_AMOUNT, refundAmountTotal);
    }

    private long getTradeTimestamp(Map<String, Object> bizResult) {
        try {
            String tradeTime = MapUtil.getString(bizResult, ResponseFields.TRADE_TIME);
            return LocalDateTime.parse(tradeTime, TRADE_TIME_FORMATTER).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        } catch (Exception e) {
            return System.currentTimeMillis();
        }
    }

    private String cancelToRefundProcess(TransactionContext context) {
        String result = refund(context);
        if (Workflow.RC_REFUND_SUCCESS.equals(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        return result;
    }

    private boolean needRefund(Map<String, Object> order, Map<String, Object> transaction) {
        Map<String, Object> payTrans = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(order, DaoConstants.CTIME));

        long channelFinishTime = BeanUtil.getPropLong(payTrans, Transaction.CHANNEL_FINISH_TIME);
        if (channelFinishTime == 0) {
            return false;
        }

        long todayStartTimestamp = DateTimeUtil.getOneDayStart(System.currentTimeMillis());

        return channelFinishTime < todayStartTimestamp;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }
}
