package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.Action;
import com.wosai.fsm.MachineBuilder;
import com.wosai.fsm.MachineContext;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@WorkflowPriority(priority = 7)
public class CancelOrderWorkflow extends Workflow {
    private static final Logger logger = LoggerFactory.getLogger(CancelOrderWorkflow.class);

    public static final String NAME = "generic.cancel.workflow";

    @Autowired
    private ExternalServiceFacade facade;
    
    public CancelOrderWorkflow() {
        long[] delays = {1000, 2000, 3000, 3000, 5000, 5000 };
        MachineBuilder builder = new MachineBuilder();
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return cancelOrder((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, SUCCESS)
          .transition(RC_ERROR, CANCEL_ERROR)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_IOEX,  FAIL_IO_1)

        .on(SUCCESS).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return finish((TransactionContext)context);
            }
        }).end()

        .on(IN_PROG).delay(delays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return cancelOrder((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, SUCCESS)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_IOEX, FAIL_IO_2)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_ERROR, CANCEL_ERROR)
          .transition(RC_EXPIRE, CANCEL_ERROR)

        .on(CANCEL_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end();
        
        machine = builder.build();
    }

    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_CANCEL) {
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        return context.getServiceProvider().explainNotification(notification);
    }

    public String cancelOrder(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_CANCEL), () -> {
            String result = context.getServiceProvider().cancel(context);
            logger.debug("TID {} cancel method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @SuppressWarnings("unchecked")
    @Override
    public String finish(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        order.put(Order.NET_ORIGINAL, 0L);
        order.put(Order.NET_EFFECTIVE, 0L);
        order.put(Order.NET_DISCOUNT, 0L);
        order.put(Order.STATUS, Order.STATUS_CANCELED);
        transaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
        PaymentUtil.updateOrderPaymentsNetAmountForCancelSuccess((List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH));
        PaymentUtil.updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(order, transaction);
        Map<String,Object> tradeParams = context.getServiceProvider().getTradeParams(transaction);
        // 设置SPDB退款手续费, 撤单时不退手续费
        FeeUtil.resetSPDBTradeFee(transaction, tradeParams);
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.RECEIVED_AMOUNT, transaction.get(Transaction.RECEIVED_AMOUNT),
                                                                        Transaction.PAID_AMOUNT, transaction.get(Transaction.PAID_AMOUNT),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE));
        setExtraOutFields(transaction, transactionUpdates);
        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.NET_ORIGINAL, order.get(Order.NET_ORIGINAL),
                                                                  Order.NET_EFFECTIVE, order.get(Order.NET_EFFECTIVE),
                                                                  Order.NET_DISCOUNT, order.get(Order.NET_DISCOUNT),
                                                                  Order.ITEMS, order.get(Order.ITEMS),
                                                                  Order.STATUS, order.get(Order.STATUS));

        finishTransaction(context, orderUpdates, transactionUpdates);

        if(context.isFakeRequest()){
            logger.debug("TID {} cancel finished", context.getTid());
            return null;
        }
        logger.debug("TID {} cancel finished", context.getTid());
        return null;
    }
    @SuppressWarnings("unchecked")
    public String closeOrder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        if (BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_PAID) {
            order.put(Order.STATUS, Order.STATUS_CANCEL_ERROR);
        }
        if (!UpayUtil.isFormal(transaction) && BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT) != 0){
            facade.unfreezeWalletBalanceDeduction(BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID), BeanUtil.getPropString(transaction, DaoConstants.ID));
        }

        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS));
        setExtraOutFields(transaction, transactionUpdates);
        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.STATUS, order.get(Order.STATUS));

        repository.getTransactionDao().updatePart(transactionUpdates);
        repository.getOrderDao().updatePart(orderUpdates);
        
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        logger.debug("TID {} closed", context.getTid());
        return null;
    }

    private void setExtraOutFields(Map<String, Object> transaction, Map<String, Object> transactionUpdates) {
        if(MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_WEIXIN) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(MapUtil.isNotEmpty(extraOutFields)) {
                transactionUpdates.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
        }
        //金额小于0时说明撤单失败，则清理额度包相关信息
        long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        if(originalAmount <= 0) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(MapUtil.isNotEmpty(extraOutFields)) {
                extraOutFields.remove(Transaction.QUOTA_FEE);
                extraOutFields.remove(Transaction.QUOTA_FEE_RATE);
                extraOutFields.remove(Transaction.QUOTA_FEE_RATE_TAG);
                transactionUpdates.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
        }
    }
}
