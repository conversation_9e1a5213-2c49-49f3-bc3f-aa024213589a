package com.wosai.upay.workflow;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.dao.DaoConstants;
import com.wosai.market.user.dto.sqbmp.UcUserDTO;
import com.wosai.mpay.api.sodexo.BusinessFields;
import com.wosai.mpay.api.sodexo.RequestBuilder;
import com.wosai.mpay.api.sodexo.ResponseFields;
import com.wosai.mpay.api.sodexo.SodexoConstants;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.UUIDGenerator;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.CryptoService;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class SodexoWapServiceProvider extends SodexoServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(SodexoWapServiceProvider.class);

    @Autowired
    private CryptoService cryptoService;

    public static final String NAME = "provider.sodexo.wap";

    private static final String OP_OPEN_ID = "openId";
    private static final String OP_PAY_TOKEN = "payToken";
    private static final String OP_QUERY_BALANCE = "queryBalance";
    private static final String OP_OTHER_PAY = "otherPay";

    @Autowired
    private ExternalServiceFacade externalServiceFacade;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        return MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_SODEXO
                && MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_MINI;
    }

    /**
     * 预下单
     * 该接口由前端调用，经过智慧门店转发；手机号参数为智慧门店传入base64编码
     *
     * @param context
     * @param resume
     * @return
     */
    @Override
    public String precreate(TransactionContext context, boolean resume) {
        String payToken;
        Map<String, Object> transaction = context.getTransaction();
        Map<?, ?> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        String userId = MapUtils.getString(extraParams, Transaction.PAYER_UID);
        UcUserDTO userInfo = externalServiceFacade.findUserInfo(userId);
        if (Objects.isNull(userInfo)) {
            logger.error("用户信息不存在, user_id: {}", userId);
            return Workflow.RC_ERROR;
        }
        String mobileNumber = userInfo.getCellphone();
        if (StringUtils.isBlank(mobileNumber)) {
            logger.error("用户信息内手机号为空, user_id: {}", userId);
            return Workflow.RC_ERROR;
        }

        // 获取支付token
        try {
            payToken = queryPayToken(mobileNumber, getTradeParams(transaction));
        } catch (Exception e) {
            //预下单流程中，调用外部接口出现异常，
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            logger.error("[precreate]获取payToken失败. mobileNumber:{}", mobileNumber, e);
            return Workflow.RC_ERROR;
        }
        Map<String, Object> encryptDataMap = MapUtils.hashMap(
                Transaction.PAYER_UID, mobileNumber,
                Transaction.PAY_TOKEN, payToken,
                Transaction.ORDER_SN, MapUtils.getString(transaction, Transaction.ORDER_SN)
        );

        // 加密
        String encryptData;
        try {
            encryptData = cryptoService.encrypt(JacksonUtil.toJsonString(encryptDataMap));
        } catch (Exception e) {
            logger.error("[precreate]加密失败. encryptDataMap:{}", encryptDataMap, e);
            return Workflow.RC_ERROR;
        }
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (MapUtils.isEmpty(extraOutFields)) {
            extraOutFields = Maps.newHashMap();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, ImmutableMap.of(Transaction.ENCRYPT_DATA, encryptData));
        return Workflow.RC_CREATE_SUCCESS;
    }

    /**
     * 索迪斯 - 在线消费
     *
     * @param mobileNumber 手机号
     * @param encryptData  加密密文
     * @param merchantId
     * @param clientSn
     */
    public String expenseByToken(String mobileNumber, String encryptData, String merchantId, String clientSn) {
        String payToken;
        String mobileNumber0;
        String orderSn;
        // 解密; 支付token、手机号
        try {
            String encryptDataStr = cryptoService.decrypt(encryptData);
            Map<String, Object> encryptDataMap = JacksonUtil.toBean(encryptDataStr, Map.class);
            payToken = MapUtils.getString(encryptDataMap, Transaction.PAY_TOKEN);
            mobileNumber0 = MapUtils.getString(encryptDataMap, Transaction.PAYER_UID);
            orderSn = MapUtils.getString(encryptDataMap, Transaction.ORDER_SN);
        } catch (Exception e) {
            logger.error("解密失败. 异常栈: ", e);
            throw new UpayBizException("加密数据非法");
        }
        //校验密文内手机号与所传手机号是否一致
        if (!Objects.equals(mobileNumber, mobileNumber0)) {
            throw new UpayBizException("当前用户信息非法");
        }

        Map<String, Object> transaction = getTransactionDto(merchantId, orderSn, clientSn);
        verifyTransaction(transaction);
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        final String OP_FLAG = OP_OTHER_PAY;
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_FLAG);
        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.TRANS_TYPE, SodexoConstants.EXPENSE_BY_TOKEN_TRANS_TYPE);
        builder.set(BusinessFields.SUBMIT_TIME, formatTimeString(System.currentTimeMillis()));
        builder.set(BusinessFields.CLIENT_TRACE_NO, tsn);
        builder.set(BusinessFields.EXPENSE_TOKEN, payToken);
        builder.set(BusinessFields.ACTUAL_AMOUNT, com.wosai.mpay.util.StringUtils.cents2yuan(amount));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(builder.build(), url, queryToken(tradeParams), 3, OP_FLAG);
        } catch (Exception e) {
            logger.error("failed to call sodexo query", e);
            throw new UpayBizException(UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }
        tokenInvalidProcess(result, tradeParams);
        String respCode = MapUtils.getString(result, ResponseFields.RETURN_CODE);
        if (!Objects.equals(respCode, SodexoConstants.RESP_CODE_SUCCESS)) {
            logger.error("支付状态异常.tsn:{},respCode:{},result:{}", tsn, respCode, JacksonUtil.toJsonString(result));
            throw new UpayBizException("支付失败。" + MapUtils.getString(result, ResponseFields.RETURN_MESSAGE));
        }
        return orderSn;
    }

    /**
     * 索迪斯 - 余额查询
     *
     * @param mobileNumber 手机号
     * @param tradeParams  交易参数
     * @return
     */
    public String queryBalance(String mobileNumber, Map<String, Object> tradeParams) {
        final String OP_FLAG = OP_QUERY_BALANCE;
        // 获取支付token
        String payToken;
        try {
            payToken = queryPayToken(mobileNumber, tradeParams);
        } catch (Exception e) {
            logger.error("[queryBalance]获取payToken失败. mobileNumber:{}", mobileNumber, e);
            throw new UpayBizException("查询余额失败");
        }
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_FLAG);
        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.TRANS_TYPE, SodexoConstants.BALANCE_QUERY_TRANS_TYPE_FIX);
        builder.set(BusinessFields.SUBMIT_TIME, formatTimeString(System.currentTimeMillis()));
        builder.set(BusinessFields.CLIENT_TRACE_NO, UUIDGenerator.getUUID());
        builder.set(BusinessFields.EXPENSE_TOKEN, payToken);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(builder.build(), url, queryToken(tradeParams), 3, OP_FLAG);
        } catch (Exception e) {
            logger.error("failed to call sodexo query", e);
            throw new UpayBizException("余额查询异常，请稍后重试");
        }
        tokenInvalidProcess(result, tradeParams);
        String respCode = MapUtils.getString(result, ResponseFields.RETURN_CODE_FIX);
        if (!Objects.equals(respCode, SodexoConstants.RESP_CODE_SUCCESS)) {
            logger.error("余额查询失败. result:{}", JacksonUtil.toJsonString(result));
            throw new UpayBizException("余额查询失败。" + MapUtils.getString(result, ResponseFields.RETURN_MESSAGE));
        }

        return MapUtils.getString(result, ResponseFields.AVA_AMOUNT);
    }

    /**
     * 通过手机号查询支付token
     *
     * @param mobileNumber
     * @return
     */
    public String queryPayToken(String mobileNumber, Map<String, Object> tradeParams) throws Exception {
        final String OP_FLAG = OP_PAY_TOKEN;
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_FLAG);
        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.MOBILE_NUMBER, mobileNumber);
        builder.set(BusinessFields.TRANS_TYPE, SodexoConstants.EXPENSE_TOKEN_REQUEST_TRANS_TYPE);
        builder.set(BusinessFields.CLIENT_TRACE_NO, UUIDGenerator.getUUID());
        builder.set(BusinessFields.OPEN_ID, queryOpenId(mobileNumber, tradeParams));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(builder.build(), url, queryToken(tradeParams), 3, OP_FLAG);
        } catch (Exception e) {
            logger.error("查询支付token失败. mobileNumber:{}", mobileNumber, e);
            throw new UpayBizException("查询支付token失败", e);
        }
        tokenInvalidProcess(result, tradeParams);
        String respCode = MapUtils.getString(result, ResponseFields.RETURN_CODE);
        if (!Objects.equals(respCode, SodexoConstants.RESP_CODE_SUCCESS)) {
            logger.error("查询支付token失败. result:{}", JacksonUtil.toJsonString(result));
            throw new UpayBizException("查询支付token失败. respCode:" + respCode);
        }
        String token = MapUtils.getString(result, BusinessFields.EXPENSE_TOKEN);
        if (org.apache.commons.lang3.StringUtils.isBlank(token)) {
            logger.error("查询支付token失败,token为空. result:{}", JacksonUtil.toJsonString(result));
            throw new UpayBizException("查询支付token失败,openId为空");
        }
        return token;
    }

    /**
     * 通过手机号查询openId
     *
     * @param mobileNumber
     * @return
     */
    private String queryOpenId(String mobileNumber, Map<String, Object> tradeParams) throws Exception {
        final String OP_FLAG = OP_OPEN_ID;
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_FLAG);
        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.MOBILE_NUMBER, mobileNumber);
        builder.set(BusinessFields.TRANS_TYPE, SodexoConstants.OPEN_ID_REQUEST_TRANS_TYPE);
        builder.set(BusinessFields.CLIENT_TRACE_NO, UUIDGenerator.getUUID());
        Map<String, Object> result = retryIfNetworkException(builder.build(), url, queryToken(tradeParams), 3, OP_FLAG);
        tokenInvalidProcess(result, tradeParams);
        String respCode = MapUtils.getString(result, ResponseFields.RETURN_CODE);
        if (!Objects.equals(respCode, SodexoConstants.RESP_CODE_SUCCESS)) {
            logger.error("查询openId失败. result:{}", JacksonUtil.toJsonString(result));
            throw new UpayBizException("查询openId失败. respCode:" + respCode);
        }
        String openId = MapUtils.getString(result, BusinessFields.OPEN_ID);
        if (StringUtils.isBlank(openId)) {
            logger.error("查询openId失败,openId为空. result:{}", JacksonUtil.toJsonString(result));
            throw new UpayBizException("查询openId失败,openId为空");
        }
        return openId;
    }

    protected RequestBuilder buildBaseRequestBuilder(Map<String, Object> tradeParams) {
        RequestBuilder builder = super.buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.SUBMIT_TIME, formatTimeString(System.currentTimeMillis()));
        return builder;
    }

    private Map<String, Object> getTransactionDto(String merchantId, String orderSn, String clientSn) {
        Map<String, Object> transaction = dataRepository.getPayTransactionByOrderSn(merchantId, orderSn);
        if (Objects.isNull(transaction)) {
            Map<String, Object> order = gatewaySupportService.getOrderBySn(merchantId, orderSn, clientSn, DataPartitionConst.HOT);
            if (Objects.isNull(order)) {
                throw new UpayBizException(UpayErrorScenesConstant.TRADE_NO_REPEAT_MESSAGE);
            }
            transaction = gatewaySupportService.getPayOrConsumerTransaction(merchantId, orderSn, MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        if (Objects.isNull(transaction)) {
            throw new UpayBizException(UpayErrorScenesConstant.TRADE_NO_REPEAT_MESSAGE);
        }
        return transaction;

    }

    @Override
    protected String getExpenseToken(Map<String, Object> transaction) {
        String encryptData = (String) MapUtils.getNestedProperty(transaction,
                String.format("%s.%s.%s",
                        Transaction.EXTRA_OUT_FIELDS, Transaction.WAP_PAY_REQUEST, Transaction.ENCRYPT_DATA));
        if (StringUtils.isBlank(encryptData)) {
            return null;
        }
        // 解密; 支付token
        try {
            String encryptDataStr = cryptoService.decrypt(encryptData);
            Map<String, Object> encryptDataMap = JacksonUtil.toBean(encryptDataStr, Map.class);
            return MapUtils.getString(encryptDataMap, Transaction.PAY_TOKEN);
        } catch (Exception e) {
            logger.error("解密失败. tsn:{}", MapUtils.getString(transaction, Transaction.TSN), e);
            throw new UpayBizException("加密数据非法");
        }
    }

    private void verifyTransaction(Map<String, Object> transaction) {
        int status = MapUtil.getIntValue(transaction, Transaction.STATUS);
        if (!(status == Transaction.STATUS_CREATED || status == Transaction.STATUS_IN_PROG)) {
            throw new UpayBizException("未在支付中的订单，请重新操作");
        }
        //订单有效期设置为4分钟
        long now = System.currentTimeMillis();
        long end = MapUtil.getLongValue(transaction, DaoConstants.CTIME) + TimeUnit.MINUTES.toMillis(4);
        if (now > end) {
            throw new UpayBizException("订单处理超时，请重新操作");
        }
    }
}
