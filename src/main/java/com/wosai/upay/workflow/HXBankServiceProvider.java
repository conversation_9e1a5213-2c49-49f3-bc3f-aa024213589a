package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.chinaums.ResponseFields;
import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.hxbank.*;
import com.wosai.mpay.api.unionpayopen.UnionPayOpenConstants;
import com.wosai.mpay.api.unionqrcode.BusinessFields;
import com.wosai.mpay.api.unionqrcode.ProtocolFields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.*;
import com.wosai.mpay.util.hxbank.EnvApplication;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.QrcodeImaging;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wosai.constant.UpayConstant.*;

/**
 * <AUTHOR>
 * @Description HXBankServiceProvider
 * @Date 2021/8/24 7:59 PM
 */
public class HXBankServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(HXBankServiceProvider.class);
    public static final String NAME = "provider.hxbank";

    private static final String SUB_APPID = "sub_appid";
    private static final int NOTIFY_URL_LIMIT = 200;
    protected String b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE + "";
    protected String dcepB2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE * 60 + ""; // 数字人民币过期时间，单位秒
    protected String defaultTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE + "";//csb 二维码支付,超时时间
    public static final String UNION_PAYMENT_BANKCARD_DEBIT_TYPE = "DEBIT_CARD"; //储蓄卡
    public static final String UNION_PAYMENT_BANKCARD_CREDIT_TYPE = "CREDIT_CARD"; //信用卡

    private String notifyHost;
    EnvApplication envApplication;
    public HXBankServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        extendedFilterFields = new HashSet<String>(Arrays.asList(HXBankBusinessFields.AMOUNT));
        envApplication = new EnvApplication();
    }

    @Autowired
    protected HXBankClient hxBankClient;
    @Autowired
    protected QrcodeImaging qrcodeImaging;

    protected DateFormat df = new SimpleDateFormat(HXBankConstants.DATE_TIME_FORMAT);

    @Override
    public String getName() {
        return NAME;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return !Objects.isNull(getTradeParams(transaction));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.HXBANK_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_HXBANK;
    }

    @Override
    public Map<String,Object> queryUserInfo(Map<String,Object> transaction) {
        Map<String,Object> tradeParams= getTradeParams(transaction);
        Map<String,Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            return null;
        }
        HXBankRequestBuilder builder = new HXBankRequestBuilder();
        String developAppId = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_DEVELOP_APP_ID);
        String merchantNo = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_PROVIDER_MCH_ID);
        String serviceId = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_PROVIDER_SERVICE_ID);

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);


        //开发者ID
        builder.bizSet(HXBankProtocolFields.APP_ID, developAppId);
        //请求时间
        builder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        String version = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_VERSION, HXBankConstants.VERSION_1);
        //2.0版本接口必须上送服务商商户编号
        if (HXBankConstants.VERSION_2.equals(version)){
            //服务商商户编号
            builder.bizBodySet(HXBankBusinessFields.MERCHANT_ID, serviceId);
            //2.0接口版本
            builder.bizSet(HXBankProtocolFields.VERSION, version);
        }
        //华夏分配的商户号
        builder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, merchantNo);
        //商户流水号
        builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        //授权码
        builder.bizBodySet(HXBankBusinessFields.USER_AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        //银联支付标识
        builder.bizBodySet(HXBankBusinessFields.APP_UP_IDENTIFIER, MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER));

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY);
        Map<String,Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 1, OP_QUERY);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        }catch (Exception ex){
            logger.error("failed to call hxbank queryUserInfo", ex);
            return CollectionUtil.hashMap(HXBankResponseFields.RESP_MSG,ex.getMessage());
        }

        if (MapUtils.isEmpty(result)) {
            return null;
        }
        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        if (!HXBankConstants.RESP_CODE_SUCCESS.equals(respCode)) {
            return null;
        }

        return CollectionUtil.hashMap(BusinessFields.USER_ID, MapUtil.getString(result, BusinessFields.USER_ID));
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        HXBankRequestBuilder builder = getDefaultRequestBuilder(context);

        //请求时间 使用订单ctime, 否则在跨日临界点会出现 请求日期是********， 但是订单生成日期是******** 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date(orderCtime)));
        //商户流水号
        builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //支付授权码
        builder.bizBodySet(HXBankBusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        //订单金额,单位为元
        builder.bizBodySet(HXBankBusinessFields.AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //订单标题, 过滤掉特殊字符
        builder.bizBodySet(HXBankBusinessFields.SUBJECT, CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT)));
        //订单有效时间 分钟数
        builder.bizBodySet(HXBankBusinessFields.EXPIRE_TIME, b2cTimeoutExpress);
        if (Order.PAYWAY_WEIXIN == payway) {
            String subAppId = MapUtil.getString(tradeParams, TransactionParam.HXBANK_WEIXIN_SUB_APP_ID);
            if (!StringUtils.isEmpty(subAppId) && ApolloConfigurationCenterUtil.isSendHXB2cSubAppId()) {
                builder.bizBodySet(HXBankBusinessFields.SUB_APP_ID, subAppId);
            }
        }
        //数字人民币时间单位为秒
        if (Order.PAYWAY_DCEP == payway) {
            //业务类型编码
            builder.bizBodySet(HXBankBusinessFields.SVC_SYS_CODE, HXBankConstants.SVC_SYS_CODE_CONSUME);
            //业务种类编码
            builder.bizBodySet(HXBankBusinessFields.BUS_CTGTY, MapUtil.getString(tradeParams, TransactionParam.HXBANK_DCEP_BUS_CTGTY));
            builder.bizBodySet(HXBankBusinessFields.EXPIRE_TIME, dcepB2cTimeoutExpress);
        }
        //终端编号,银联云闪付交易时，该字段为必传
        if (Order.PAYWAY_UNIONPAY == payway) {
            String version = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_VERSION, HXBankConstants.VERSION_1);
            //1.0版本接口上送华夏商户虚拟终端号
            if (HXBankConstants.VERSION_1.equals(version)){
                String termId = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PROVIDER_TERM_ID);
                builder.bizBodySet(HXBankBusinessFields.TERM_ID, termId);
            }
            String prnInsIdCd = MapUtils.getString(tradeParams, TransactionParam.UNION_PAY_OPEN_PNR_INS_ID_CD);
            if (!StringUtils.empty(prnInsIdCd)) {
                //云闪付交易上送服务商机构标识 "C1000001"
                builder.bizBodySet(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
            }

        }

        //借贷标识
        limitCredit(builder, transaction);

        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PAY);
        Map<String,Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 1, OP_PAY);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        } catch (Exception ex) {
            logger.error("failed to call hxbank pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_DCEP == payway) {
            throw new UnsupportedOperationException("数币交易暂不支持撤单");
        }

        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        HXBankRequestBuilder builder = getDefaultRequestBuilder(context);

        //商户请求流水号
        builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN) + "-C");
        //原交易流水号
        builder.bizBodySet(HXBankBusinessFields.ORIG_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //原交易日期
        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.bizBodySet(HXBankBusinessFields.ORG_PCS_DATE, DateUtil.formatDate(new Date(orgOrderTime), DateUtil.FORMATTER_DATE_INT));

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_CANCEL);
        Map<String,Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 3, OP_CANCEL);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        } catch (Exception ex) {
            logger.error("failed to call hxbank cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        HXBankRequestBuilder builder = getDefaultRequestBuilder(context);
        String opFlag = OP_QUERY;
        //原支付交易流水号
        builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //原交易日期
        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.bizBodySet(HXBankBusinessFields.ORG_PCS_DATE, DateUtil.formatDate(new Date(orgOrderTime), DateUtil.FORMATTER_DATE_INT));
        if (Transaction.TYPE_REFUND == type){
            //退款/撤销查询
            builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
            builder.bizBodySet(HXBankBusinessFields.TRADE_TYPE, HXBankConstants.TRADE_TYPE_REFUND_OR_CANCEL);
            opFlag = OP_REFUND_QUERY;
        }else if (Transaction.TYPE_CANCEL == type){
            builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN) + "-C");
            builder.bizBodySet(HXBankBusinessFields.TRADE_TYPE, HXBankConstants.TRADE_TYPE_REFUND_OR_CANCEL);
            opFlag = OP_CANCEL_QUERY;
        }
        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), opFlag);
        Map<String, Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 3, opFlag);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        } catch (Exception ex) {
            logger.error("failed to call hxbank query", ex);
            setTransactionContextErrorInfo(context, opFlag, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, opFlag);

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        HXBankRequestBuilder builder = getDefaultRequestBuilder(context);

        //商户请求流水号
        builder.bizBodySet(HXBankBusinessFields.REFUND_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        //原交易流水号
        builder.bizBodySet(HXBankBusinessFields.ORIG_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //退款金额
        builder.bizBodySet(HXBankBusinessFields.AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //原交易日期
        long orgOrderTime = MapUtils.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.bizBodySet(HXBankBusinessFields.ORG_PCS_DATE, DateUtil.formatDate(new Date(orgOrderTime / 1000 * 1000), DateUtil.FORMATTER_DATE_INT));

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 1, OP_REFUND);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        } catch (Exception ex) {
            logger.error("failed to call hxbank refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI){
            return wapPrecreate(context, resume);
        }else{
            return c2bPrecreate(context, resume);
        }
    }

    private String wapPrecreate(TransactionContext context, boolean resume){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        HXBankRequestBuilder builder = getDefaultRequestBuilder(context);

        //请求时间 使用订单ctime, 否则在跨日临界点会出现 请求日期是********， 但是订单生成日期是******** 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date(orderCtime)));

        String clientIp = MapUtil.getString(extraParams, Transaction.CLIENT_IP);

        String version = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_VERSION, HXBankConstants.VERSION_1);
        //2.0版本接口交易上送terminalId
        if (HXBankConstants.VERSION_2.equals(version)) {
            //终端设备号, 在被扫和退款时上送的是termId， 主扫需要上送terminalId
            builder.bizBodySet(HXBankBusinessFields.TERMINAL_ID, MapUtil.getString(builder.getRequestBody(), HXBankBusinessFields.TERM_ID));
            builder.getRequestBody().remove(HXBankBusinessFields.TERM_ID);
        }
        //商户流水号
        builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单金额,单位为元
        builder.bizBodySet(HXBankBusinessFields.AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //订单标题, 过滤掉特殊字符
        builder.bizBodySet(HXBankBusinessFields.SUBJECT, CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT)));
        //通知地址
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.bizBodySet(HXBankBusinessFields.NOTIFY_URL, notifyUrl);

        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        String opFlag = "";
        builder.bizBodySet(HXBankBusinessFields.EXPIRE_TIME, defaultTimeoutExpress);
        if (Order.PAYWAY_WEIXIN == payway){
            //订单有效时间 分钟数
            builder.bizBodySet(HXBankBusinessFields.SUB_OPEN_ID, payerUid);
            boolean isMini = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_MINI;
            String subAppId = MapUtil.getString(tradeParams, isMini ? TransactionParam.HXBANK_WEIXIN_MINI_SUB_APP_ID : TransactionParam.HXBANK_WEIXIN_SUB_APP_ID);
            if(!StringUtil.empty(subAppId)) {
                builder.bizBodySet(HXBankBusinessFields.SUB_APP_ID, subAppId);
            }
            opFlag = ApolloConfigurationCenterUtil.GATEWAY_OP_WX_WAP;
        }else if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway){
            builder.bizBodySet(HXBankBusinessFields.BUYER_ID, payerUid);
            opFlag = ApolloConfigurationCenterUtil.GATEWAY_OP_ALIPAY_WAP;
        } else if (Order.PAYWAY_UNIONPAY == payway){
            builder.bizBodySet(HXBankBusinessFields.CUSTOMER_IP, clientIp);
            builder.bizBodySet(HXBankBusinessFields.QR_CODE_TYPE, HXBankConstants.QR_CODE_TYPE_DYNAMIC);
            builder.bizBodySet(BusinessFields.USER_ID, payerUid);
            builder.bizBodySet(BusinessFields.USER_AUTH_CODE, MapUtil.getString(extendedParams, Transaction.EXTENDED_USER_AUTH_CODE));
            builder.bizBodySet(BusinessFields.QRCODE, MapUtil.getString(extendedParams, Transaction.QR_CODE));
            if (extendedParams != null) {
                extendedParams.remove(Transaction.EXTENDED_USER_AUTH_CODE);
                extendedParams.remove(Transaction.QR_CODE);
            }
            String prnInsIdCd = MapUtils.getString(tradeParams, TransactionParam.UNION_PAY_OPEN_PNR_INS_ID_CD);
            if (!StringUtils.empty(prnInsIdCd)) {
                //云闪付交易上送服务商机构标识 "C1000001"
                builder.bizBodySet(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
            }
            opFlag = ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_WAP;
        }

        //借贷标识
        limitCredit(builder, transaction);
        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder);

        //由于华夏银行未返回，故我们自己取
        transaction.put(Transaction.BUYER_UID, payerUid);
        context.getOrder().put(Order.BUYER_UID, payerUid);
        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), opFlag);
        Map<String, Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 1, OP_PRECREATE);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        } catch (Exception ex) {
            logger.error("failed to call hxbank precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildWapPrecreateResult(result, context);
    }

    private String c2bPrecreate(TransactionContext context, boolean resume){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        HXBankRequestBuilder builder = getDefaultRequestBuilder(context);

        //请求时间 使用订单ctime, 否则在跨日临界点会出现 请求日期是********， 但是订单生成日期是******** 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date(orderCtime)));

        String version = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_VERSION, HXBankConstants.VERSION_1);
        //2.0版本接口交易上送terminalId
        if (HXBankConstants.VERSION_2.equals(version)) {
            //终端设备号, 在被扫和退款时上送的是termId， 主扫需要上送terminalId
            builder.bizBodySet(HXBankBusinessFields.TERMINAL_ID, MapUtil.getString(builder.getRequestBody(), HXBankBusinessFields.TERM_ID));
            builder.getRequestBody().remove(HXBankBusinessFields.TERM_ID);
        }
        //商户流水号
        builder.bizBodySet(HXBankBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单金额,单位为元
        builder.bizBodySet(HXBankBusinessFields.AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //订单标题, 过滤掉特殊字符
        builder.bizBodySet(HXBankBusinessFields.SUBJECT, CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT)));
        //通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        builder.bizBodySet(HXBankBusinessFields.NOTIFY_URL, notifyUrl);

        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PRECREATE);

        Map<String, Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = retryIfNetworkException(gatewayUrl.getUrl(), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(hxPublicKey), 1, OP_PRECREATE);
            } else {
                result = fakeClient.call(gatewayUrl.getUrl(), builder.build(), "application/json;charset=utf-8", FakeConstant.JSON_FORMAT);
            }
        } catch (Exception ex) {
            logger.error("failed to call hxbank precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildCSBPrecreateResult(result, context);
    }
    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_HXBANK_PRIVATE_KEY);
        String hxPublicKey = MapUtil.getString(tradeParams, TransactionParam.HXBANK_PUBLIC_KEY);

        Map<String, Object> hxNotify = MapUtil.getMap(providerNotification, "hxNotify");

        long type = MapUtil.getLongValue(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }


        // 解密：商户使用自己的私钥进行解密
        // 参数说明：(1)商户私钥(jks文件)；(2)密钥口令；(3)待解密内容(请求body内容)
        byte[] decryption = envApplication.openEnvelope(Base64.decode(getPrivateKeyContent(sqbPrivateKey)), String.valueOf(hxNotify.get("body")));
        String body = new String(decryption, StandardCharsets.UTF_8);
        logger.info("decrypt notify request is {}",body);
        JSONObject notifyResult = JSON.parseObject(body);
        String status = MapUtil.getString(notifyResult, HXBankResponseFields.STATUS);
        if (HXBankConstants.NOTIFY_STATUS_SUCCESS.equals(status)){
            setTradeNoBuyerInfoIfExists(notifyResult, context);
            resolvePayFund(context, notifyResult);

            Map<String, Object> returnBody = ImmutableMap.of(HXBankResponseFields.STATUS, HXBankConstants.NOTIFY_SUCCESS);

            String retSigned = null;
            try {
                retSigned = RsaSignature.sign(returnBody, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, getPrivateKeyContent(sqbPrivateKey));
            } catch (Exception e) {
                logger.error("hxbank notify sign error", e);
            }

            // 加密:商户使用综合支付的公钥进行加密
            // 参数说明：(1)银行公钥证书；(2)对称算法AES(固定)；(3)待加密内容
            String sRetBody = "";
            try {
                sRetBody = envApplication.makeEnvelope(getPrivateKeyContent(hxPublicKey), AesUtil.AES, JsonUtil.objectToJsonString(returnBody).getBytes(StandardCharsets.UTF_8));
            } catch (MpayException e) {
                logger.error("hxbank notify envelope error", e);
            }

            hxNotify.put(HXBankProtocolFields.SIGNATURE, retSigned);
            hxNotify.put(HXBankProtocolFields.BODY, sRetBody);
            providerNotification.put("response", JSON.toJSONString(hxNotify));
            return Workflow.RC_PAY_SUCCESS;
        }

        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }


    /**
     * 公共请求参数
     *
     * @param context
     */
    protected HXBankRequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String developAppId = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_DEVELOP_APP_ID);
        String merchantNo = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_PROVIDER_MCH_ID);
        String serviceId = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_PROVIDER_SERVICE_ID);
        String version = BeanUtil.getPropString(tradeParams, TransactionParam.HXBANK_VERSION, HXBankConstants.VERSION_1);

        //2.0版本接口必须上送服务商商户编号
        if (HXBankConstants.VERSION_2.equals(version)){
            //服务商商户编号
            requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_ID, serviceId);
            //2.0接口版本
            requestBuilder.bizSet(HXBankProtocolFields.VERSION, version);
        }
        //开发者ID
        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, developAppId);
        //华夏分配的商户号
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, merchantNo);
        //请求时间
        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));

        //设置259号文终端信息
        if (ApolloConfigurationCenterUtil.isSendHX259Params()) {
            TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
            requestBuilder.bizBodySet(HXBankBusinessFields.TERMINAL_TYPE, terminalInfo.getOrDefaultType(HXBankConstants.TERM_INFO_TERMINAL_TYPE));
            requestBuilder.bizBodySet(HXBankBusinessFields.TERM_ID, terminalInfo.getId());
            if( terminalInfo.getSerialNum() != null){
                requestBuilder.bizBodySet(HXBankBusinessFields.SERIAL_NUM, terminalInfo.getSerialNum());
            }

            int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
            //华夏通道，只有BSC类终端上送POI，其他类型终端只上送ip
            if (Order.SUB_PAYWAY_BARCODE == subPayway && terminalInfo.isSendPoi()) {
                requestBuilder.bizBodySet(HXBankBusinessFields.GPS, terminalInfo.getFormatLongitude() + "/" + terminalInfo.getFormatLatitude());
                BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
            }

            if (terminalInfo.isSendIp()) {
                requestBuilder.bizBodySet(HXBankBusinessFields.OCCUR_ADD, terminalInfo.getIp());
            }
        }

        return requestBuilder;

    }

    /**
     * 设置借贷标识 no_credit--指定不能使用信用卡支付
     *
     * @param builder
     * @param transaction
     */
    private void limitCredit(HXBankRequestBuilder builder, Map transaction) {
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))) {
            builder.bizBodySet(HXBankBusinessFields.LIMIT_PAY, HXBankConstants.LIMIT_PAY_NO_CREDIT);
        }
    }

    private void carryOverExtendedParams(Map<String, Object> extended, HXBankRequestBuilder builder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }
        // 华夏渠道参数转化
        if (extended.containsKey(TERMINAL_SECRET_TEXT)) {
            extended.put(HXBankBusinessFields.SECRET_TEXT, MapUtil.getString(extended, TERMINAL_SECRET_TEXT));
        }
        if (extended.containsKey(TERMINAL_ENCRYPT_RAND_NUM)) {
            extended.put(HXBankBusinessFields.ENCRYPT_RAND_NUM, MapUtil.getString(extended, TERMINAL_ENCRYPT_RAND_NUM));
        }

        // 间连渠道参数转换
        if(extended.containsKey(BusinessV2Fields.EXTEND_PARAMS) && extended.get(BusinessV2Fields.EXTEND_PARAMS) instanceof Map) {
            Map<String, Object> extendParams = (Map)extended.get(BusinessV2Fields.EXTEND_PARAMS);
            if(null != extendParams) {
                if(extendParams.containsKey(SUB_APPID)) {
                    // weixin sub_appid转换
                    extended.put(HXBankBusinessFields.SUB_APP_ID, MapUtil.getString(extendParams, SUB_APPID));
                }
            }
        }

        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if(SUB_APPID.equals(key)) {
                // weixin sub_appid转换
                builder.bizBodySet(HXBankBusinessFields.SUB_APP_ID, value);
                continue;
            }
            // 华夏渠道过滤掉原始的参数secret_text和encrypt_rand_num
            if ((TERMINAL_SECRET_TEXT.equals(key) || TERMINAL_ENCRYPT_RAND_NUM.equals(key))) {
                continue;
            }
            builder.bizBodySet(key, value);
        }
    }

    private Map<String, Object> retryIfNetworkException(String serviceUrl, Map<String, Object> request, String secretKey, String hxkey, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            return hxBankClient.call(serviceUrl, request, secretKey, hxkey);
        }
        logger.error("still network i/o error after retrying {} times, op is {}", times, opFlag);

        throw exception;
    }

    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);//返回的响应码
        String respMsg = MapUtils.getString(result, HXBankResponseFields.RESP_MSG);//响应描述
        String statusCode = MapUtils.getString(result, HXBankResponseFields.STATUS_CODE);
        String statusMsg = MapUtils.getString(result, HXBankResponseFields.STATUS_MSG);

        map.put(HXBankResponseFields.RESP_CODE, respCode);//返回状态码
        map.put(HXBankResponseFields.RESP_MSG, respMsg);//返回信息
        map.put(HXBankResponseFields.STATUS_CODE, statusCode);//返回信息
        map.put(HXBankResponseFields.STATUS_MSG, statusMsg);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, HXBankConstants.RESP_CODE_SUCCESS.equals(respCode) || HXBankConstants.RESP_CODE_PROCESSING.equals(respCode), respCode, respMsg);
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();

        //华夏返回的BUYER_ID是支付宝账号，非支付宝用户ID
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerId = MapUtil.getString(result, HXBankResponseFields.BUYER_ID);
            if (!StringUtil.empty(buyerId)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            int payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
            String buyerUserId = "";
            if (payway == Order.PAYWAY_ALIPAY2) {
                buyerUserId = MapUtil.getString(result, HXBankResponseFields.BUYER_USER_ID);
            } else if (payway == Order.PAYWAY_WEIXIN) {
                buyerUserId = MapUtil.getString(result, HXBankResponseFields.SUB_OPEN_ID);
            }
            if (!StringUtil.empty(buyerUserId)) {
                transaction.put(Transaction.BUYER_UID, buyerUserId);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            //商户通道交易订单号(华夏生成)
            String channelNo = MapUtil.getString(result, HXBankResponseFields.CHANNEL_NO); //条码支付、异步通知返回
            String hxOrderNo = MapUtil.getString(result, HXBankResponseFields.CHANNEL_ORDER_NO); //交易查询返回
            String channelOrderNo = StringUtils.isEmpty(channelNo) ? hxOrderNo : channelNo;
            if (!StringUtils.isEmpty(channelOrderNo)) {
                transaction.put(Transaction.TRADE_NO, channelOrderNo);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String channelTradeNo = MapUtil.getString(result, HXBankResponseFields.TRADE_NO);        //平台流水号(微信、支付宝、银联等平台返回的订单号)
            int payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
            if (payway == Order.PAYWAY_ALIPAY2) {
                channelTradeNo = getAlipayRealTradeNo(Order.PROVIDER_HXBANK, channelTradeNo);
            }
            if (!StringUtils.isEmpty(channelTradeNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
            }
        }
    }

    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        String statusCode = MapUtils.getString(result, HXBankResponseFields.STATUS_CODE);

        setTradeNoBuyerInfoIfExists(result, context);
        if (HXBankConstants.RESP_CODE_LACK_OF_PARAMS.equals(respCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (HXBankConstants.RESP_CODE_PROCESSING.equals(respCode)) {
            return Workflow.RC_IN_PROG;
        } else if (HXBankConstants.RESP_CODE_SUCCESS.equals(respCode)) {
            if (HXBankConstants.STATUS_CODE_OO.equals(statusCode)) {
                //付款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, HXBankResponseFields.SUCCESS_TIME)));
                resolvePayFund(context, result);
                return Workflow.RC_PAY_SUCCESS;
            } else if (HXBankConstants.STATUS_CODE_WI.equals(statusCode)) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_ERROR;
    }

    private String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        String respMsg = MapUtils.getString(result, HXBankResponseFields.RESP_MSG);

        if (HXBankConstants.RESP_CODE_LACK_OF_PARAMS.equals(respCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (HXBankConstants.RESP_CODE_SUCCESS.equals(respCode)
                || (HXBankConstants.RESP_CODE_CANCEL_ERROR.equals(respCode) &&
                (HXBankConstants.RESP_MSG_REPEAT_CANCEL.equals(respMsg) || HXBankConstants.RESP_MSG_CANCEL_ERROR_CAUSE_NOT_PAY.equals(respMsg)))) {
            //撤销成功
            return Workflow.RC_CANCEL_SUCCESS;
        } else if (HXBankConstants.RESP_CODE_NO_CROSS_DAY_CANCEL.equals(respCode)
                || (HXBankConstants.RESP_CODE_CANCEL_ERROR.equals(respCode) && HXBankConstants.RESP_MSG_CANCEL_ERROR_CAUSE_PRECREATE.equals(respMsg))) {
            String refundResult = refund(context);
            return Workflow.RC_REFUND_SUCCESS.equals(refundResult) ? Workflow.RC_CANCEL_SUCCESS : refundResult;
        }else if (HXBankConstants.RESP_CODE_PROCESSING.equals(respCode)){
            //须进行撤单查询，从而确定最终的撤单情况
            return query(context);
        }

        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getInteger(transaction, Transaction.TYPE);
        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        String tradeType = MapUtils.getString(result, HXBankResponseFields.TRADE_TYPE);

        setTradeNoBuyerInfoIfExists(result, context);
        if (HXBankConstants.RESP_CODE_LACK_OF_PARAMS.equals(respCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (HXBankConstants.RESP_CODE_PROCESSING.equals(respCode)) {
            return Workflow.RC_IN_PROG;
        }

        if("2000".equals(respCode)){
            return Workflow.RC_IN_PROG;
        }
        if (!HXBankConstants.RESP_CODE_SUCCESS.equals(respCode)) {
            return Workflow.RC_ERROR;
        }
        String statusCode = MapUtils.getString(result, HXBankResponseFields.STATUS_CODE);

        if (HXBankConstants.TRADE_TYPE_PAY.equals(tradeType)) {
            switch (statusCode) {
                //支付成功 或 退款/撤销成功
                case HXBankConstants.STATUS_CODE_OO:
                    context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, HXBankResponseFields.SUCCESS_TIME)));
                    resolvePayFund(context, result);
                    return Workflow.RC_PAY_SUCCESS;
                //需要轮训
                case HXBankConstants.STATUS_CODE_WI:
                    return Workflow.RC_IN_PROG;
                //交易已关闭
                case HXBankConstants.STATUS_CODE_FL:
                    return Workflow.RC_TRADE_CANCELED;
                default:
                    return Workflow.RC_IN_PROG;
            }
        }else if (HXBankConstants.TRADE_TYPE_REFUND_OR_CANCEL.equals(tradeType)){
            if (HXBankConstants.STATUS_CODE_OO.equals(statusCode) && Transaction.TYPE_REFUND == type){
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, HXBankResponseFields.SUCCESS_TIME)));
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            }else if (HXBankConstants.STATUS_CODE_OO.equals(statusCode) && Transaction.TYPE_CANCEL == type){
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, HXBankResponseFields.SUCCESS_TIME)));
                return Workflow.RC_CANCEL_SUCCESS;
            }
        }

        return Workflow.RC_ERROR;
    }

    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        if (HXBankConstants.RESP_CODE_LACK_OF_PARAMS.equals(respCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (HXBankConstants.RESP_CODE_SUCCESS.equals(respCode)) {
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, HXBankResponseFields.SUCCESS_TIME)));
            context.getTransaction().put(Transaction.TRADE_NO, MapUtils.getString(result, HXBankResponseFields.CHANNEL_ORDER_NO));//商户通道退款订单号
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }else if (HXBankConstants.RESP_CODE_PROCESSING.equals(respCode)){
            //须进行退款查询，从而确定最终的退款情况
            return query(context);
        }

        return Workflow.RC_ERROR;
    }

    private String buildWapPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        //明确成功
        if (Objects.equals(respCode, HXBankConstants.RESP_CODE_SUCCESS)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if(payway == Order.PAYWAY_WEIXIN) {
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, MapUtil.getMap(result, HXBankResponseFields.WX_PAY_INFO));
            }else if(payway == Order.PAYWAY_ALIPAY2) {
                String tradeNo = getAlipayRealTradeNo(Order.PROVIDER_HXBANK, MapUtil.getString(result, HXBankResponseFields.TRADE_NO));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            } else if(payway == Order.PAYWAY_UNIONPAY) {
                String redirectUrl = MapUtil.getString(result, ResponseFields.REDIRECTURL);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(ResponseFields.REDIRECTURL, redirectUrl));
            }
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private String buildCSBPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        String respCode = MapUtils.getString(result, HXBankResponseFields.RESP_CODE);
        //明确成功
        if (Objects.equals(respCode, HXBankConstants.RESP_CODE_SUCCESS)) {
            Map<String, Object> transaction = context.getTransaction();
            if(context.getApiVer() == 1){
                String qrcode = BeanUtil.getPropString(result, HXBankResponseFields.QRCODE);
                transaction.put(Transaction.PROVIDER_RESPONSE, CollectionUtil.hashMap(PayResponse.RESPONSE, CollectionUtil.hashMap(
                        PayResponse.ALIPAY,CollectionUtil.hashMap(
                                PayResponse.QR_CODE, qrcode,
                                PayResponse.PIC_URL, qrcodeImaging.getQrcodeImageUrl(qrcode)
                        )
                )));
            }
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.QRCODE, result.get(HXBankResponseFields.QRCODE));
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        switch (MapUtil.getIntValue(context.getTransaction(), Transaction.PAYWAY)) {
            case Order.PAYWAY_ALIPAY2:
                resolveAlipayPayFund(result, context);
                break;
            case Order.PAYWAY_WEIXIN:
                resolveWeixinPayFund(result, context);
                break;
            case Order.PAYWAY_UNIONPAY:
                resolveUnionPayPayFund(result, context);
                break;
        }
    }

    private void resolveAlipayPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long effectiveAmount = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = StringUtils.yuan2cents(MapUtils.getString(result, HXBankResponseFields.AMOUNT)); //订单金额
        long userPayAmount = StringUtils.yuan2cents(MapUtils.getString(result, HXBankResponseFields.USER_PAY_AMT)); //实际付款金额
        String bankType = MapUtils.getString(result, HXBankResponseFields.BANK_TYPE);
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        if (userPayAmount > 0) {
            Map<String, Object> payment = (Map<String, Object>) AlipayV2ServiceProvider.fundChannelPayment.get(AlipayV2ServiceProvider.FC_BANKCARD + "." + bankType);
            if (payment == null) {
                payment = CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                        Transaction.PAYMENT_ORIGIN_TYPE, AlipayV2ServiceProvider.FC_ALIPAYACCOUNT
                );
            } else {
                payment = (Map) ((HashMap) payment).clone();
            }
            payment.put(Transaction.PAYMENT_AMOUNT, userPayAmount);
            payments.add(payment);
        }

        long discount = amount - userPayAmount;
        List<Map<String, Object>> voucherList = (List<Map<String, Object>>) MapUtils.getObject(result, HXBankResponseFields.ALI_VOUCHER_DETAIL_LIST);
        if (CollectionUtils.isNotEmpty(voucherList)) {
            for (Map<String, Object> voucher : voucherList) {
                if (voucher.isEmpty()) {
                    return;
                }
                //开始处理支付宝优惠券信息
                String id = MapUtils.getString(voucher, HXBankResponseFields.VOUCHER_ID);
                String type = MapUtil.getString(voucher, HXBankResponseFields.VOUCHER_TYPE);
                long voucherAmount = StringUtils.yuan2cents(MapUtils.getString(voucher, HXBankResponseFields.VOUCHER_AMOUNT, "0"));
                long otherContribute = StringUtils.yuan2cents(MapUtils.getString(voucher, HXBankResponseFields.VOUCHER_OTHER_CONTRIBUTE, "0"));
                long mchContribute = voucherAmount - otherContribute;
                if (mchContribute > 0) {
                    //商户出资
                    Map<String, Object> payment = CollectionUtil.hashMap(
                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                            Transaction.PAYMENT_AMOUNT, mchContribute,
                            Transaction.PAYMENT_SOURCE, id
                    );
                    if (HXBankConstants.ALIPAY_CASH_VOUCHER.equalsIgnoreCase(type)) {
                        payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP);
                    } else {
                        payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH);
                    }
                    payments.add(payment);
                }
                if (otherContribute > 0) {
                    payments.add(
                            CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                    Transaction.PAYMENT_ORIGIN_TYPE, type,
                                    Transaction.PAYMENT_AMOUNT, otherContribute,
                                    Transaction.PAYMENT_SOURCE, id
                            )
                    );
                }
            }
        } else if (discount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, discount
                    )
            );
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (discount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
        }
        transaction.put(Transaction.PAID_AMOUNT, userPayAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);

        if (UpayUtil.isReturnProviderResponse(transaction)) {
            String discountGoodsDetail = MapUtils.getString(result, HXBankResponseFields.ALI_DISCOUNT_GOODS_DETAIL);
            if (!StringUtils.empty(discountGoodsDetail)) {
                extraOutFields.put(Transaction.GOODS_DETAILS, JsonUtil.jsonStrToObject(discountGoodsDetail, List.class));
            }
            Object aliVoucherDetailList = MapUtils.getObject(result, HXBankResponseFields.ALI_VOUCHER_DETAIL_LIST);
            if (!ObjectUtils.isEmpty(aliVoucherDetailList)) {
                extraOutFields.put(Transaction.VOUCHER_DETAILS, result.get(HXBankResponseFields.ALI_VOUCHER_DETAIL_LIST));
            }
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
    }

    private void resolveWeixinPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long effectiveAmount = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long userPayAmount = StringUtils.yuan2cents(MapUtils.getString(result, HXBankResponseFields.USER_PAY_AMT)); //实际付款金额
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        long couponSum = 0;
        long discountChanelMchTotal = 0;
        Object wxPromotionDetail = MapUtil.getObject(result, HXBankResponseFields.WX_PROMOTION_DETAIL);
        if (Objects.nonNull(wxPromotionDetail)) {
            List<Map<String, Object>> promotionDetailList = (List<Map<String, Object>>) wxPromotionDetail;
            if (!promotionDetailList.isEmpty()) {
                // 解析微信优惠
                for (Map<String, Object> promotion : promotionDetailList) {
                    Map<String, Object> payment = null;
                    String promotionType = BeanUtil.getPropString(promotion, HXBankResponseFields.PROMOTION_TYPE);
                    String promotionId = BeanUtil.getPropString(promotion, HXBankResponseFields.PROMOTION_ID);
                    long promotionAmount = StringUtils.yuan2cents(MapUtils.getString(promotion, HXBankResponseFields.PROMOTION_AMOUNT));
                    long wxpayContribute = StringUtils.yuan2cents(MapUtils.getString(promotion, HXBankResponseFields.PROMOTION_WXPAY_CONTRIBUTE));
                    long otherContribute = StringUtils.yuan2cents(MapUtils.getString(promotion, HXBankResponseFields.PROMOTION_OTHER_CONTRIBUTE));
                    long channelAmount = wxpayContribute + otherContribute;
                    long mchAmount = promotionAmount - channelAmount;
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(promotionType)) {
                        if (mchAmount > 0) {
                            payment = CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                    Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                    Transaction.PAYMENT_AMOUNT, mchAmount,
                                    Transaction.PAYMENT_SOURCE, promotionId
                            );
                            discountChanelMchTotal += mchAmount;
                        } else if (channelAmount > 0) {
                            payment = CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                    Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                    Transaction.PAYMENT_AMOUNT, channelAmount,
                                    Transaction.PAYMENT_SOURCE, promotionId
                            );
                        }
                    } else if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(promotionType)) {
                        if (mchAmount > 0) {
                            payment = CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                    Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                    Transaction.PAYMENT_AMOUNT, mchAmount,
                                    Transaction.PAYMENT_SOURCE, promotionId
                            );
                        } else if (channelAmount > 0) {
                            payment = CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                    Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                    Transaction.PAYMENT_AMOUNT, channelAmount,
                                    Transaction.PAYMENT_SOURCE, promotionId
                            );
                        }
                    }
                    if (Objects.nonNull(payment)) {
                        couponSum += MapUtil.getLongValue(payment, Transaction.PAYMENT_AMOUNT);
                        payments.add(payment);
                    }
                }
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            String bankType = MapUtil.getString(result, HXBankResponseFields.BANK_TYPE);
            Map<String, Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(bankType, amount);
            if (payment != null) {
                payments.add(payment);
            }
        }

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum);
        }
        transaction.put(Transaction.PAID_AMOUNT, userPayAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount - discountChanelMchTotal);
    }

    private void resolveUnionPayPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> payments = new ArrayList<>();
        long couponSum = 0;
        long receiverAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        Object ylCouponInfo = MapUtil.getObject(result, HXBankResponseFields.YL_COUPON_INFO);
        if (Objects.nonNull(ylCouponInfo)) {
            List<Map<String, Object>> ylCouponInfoList = (List<Map<String, Object>>) ylCouponInfo;
            if (!ylCouponInfoList.isEmpty()) {
                //解析银联商品优惠明细内容
                for (Map<String, Object> couponInfo : ylCouponInfoList) {
                    String spnsrId = MapUtils.getString(couponInfo, HXBankResponseFields.COUPON_SPNSR_ID); //出资方
                    long amount = StringUtils.yuan2cents(MapUtils.getString(couponInfo, HXBankResponseFields.COUPON_OFFST_AMT)); //抵消交易金额
                    String couponId = MapUtils.getString(couponInfo, HXBankResponseFields.COUPON_ID); //项目编号
                    String couponType = MapUtils.getString(couponInfo, HXBankResponseFields.COUPON_TYPE); //项目类型

                    couponSum = couponSum + amount;
                    //注意银联接口返回不能准确的区分商户优惠是否是免充值，默认当做是免充值的优惠。如果后续此通道对接了微信交易，需要特别注意。
                    String paymentType;
                    if (UnionPayOpenConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)) {
                        paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                    } else {
                        paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                    }
                    payments.add(CollectionUtil.hashMap(
                            Transaction.PAYMENT_AMOUNT, amount,
                            Transaction.PAYMENT_SOURCE, couponId,
                            Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                            Transaction.PAYMENT_TYPE, paymentType
                    ));
                    // 商户免充值不结算给商户
                    if (Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(paymentType)) {
                        receiverAmount -= amount;
                    }
                }
            }
        }
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String respAmount = MapUtils.getString(result, HXBankResponseFields.AMOUNT);
        long invoiceAmount = StringUtils.yuan2cents(MapUtils.getString(result, HXBankResponseFields.USER_PAY_AMT, respAmount));//实际付款金额,默认订单金额
        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            //银联交易
            String bankType = MapUtil.getString(result, HXBankResponseFields.BANK_TYPE, UNION_PAYMENT_BANKCARD_DEBIT_TYPE);
            String paymentType = UNION_PAYMENT_BANKCARD_CREDIT_TYPE.equals(bankType) ? Payment.TYPE_BANKCARD_CREDIT : Payment.TYPE_BANKCARD_DEBIT;
            payments.add(CollectionUtil.hashMap(
                            Transaction.PAYMENT_AMOUNT, amount,
                            Transaction.PAYMENT_ORIGIN_TYPE, bankType,
                            Transaction.PAYMENT_TYPE, paymentType
                    )
            );
        }

        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        long discount = effectiveAmount - invoiceAmount;
        if (effectiveAmount - invoiceAmount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
        }
        transaction.put(Transaction.PAID_AMOUNT, invoiceAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiverAmount);
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }




}
