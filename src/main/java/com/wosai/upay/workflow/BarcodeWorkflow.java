package com.wosai.upay.workflow;

import java.util.Map;

import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.fsm.Action;
import com.wosai.fsm.MachineBuilder;
import com.wosai.fsm.MachineContext;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.UpayUtil;


@WorkflowPriority(priority = 2)
public class BarcodeWorkflow extends AbstractPayWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(BarcodeWorkflow.class);

    public static final String NAME = "generic.barcode.workflow";
    
    public BarcodeWorkflow() {
        long[] delays = {1000, 2000, 3000, 3000, 5000, 5000, 5000, 5000, 5000};
        long[] cancelDelays = {200, 1500, 5000, 10000, 20000};
        MachineBuilder builder = new MachineBuilder();
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return createAndPay((TransactionContext)context, resume);
            }
        }).transition(RC_PAY_SUCCESS, PRE_SUCCESS)
          .transition(RC_ERROR, FAIL_ERROR)
          .transition(RC_SYS_ERROR, FAIL_ERROR)
          .transition(RC_IN_PROG, IN_PROG)
          .transition(RC_IOEX,  FAIL_IO_1)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_TRADE_CANCELED, FAIL_CANCELED)
          .transition(RC_TRADE_DISCARD, ABORTED)

        .on(PRE_SUCCESS).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return finish((TransactionContext)context);
            }
        }).end()

        .on(IN_PROG).delay(delays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return query((TransactionContext)context);
            }
        }).transition(RC_PAY_SUCCESS, PRE_SUCCESS)
          .transition(RC_IN_PROG, IN_PROG)
          .transition(RC_IOEX, FAIL_IO_2)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_ERROR, FAIL_ERROR)
          .transition(RC_EXPIRE, QUERY_EXPIRE)
          .transition(RC_ABORT, ABORTING)
          .transition(RC_TRADE_NOT_EXISTS, FAIL_ERROR)
          .transition(RC_TRADE_CANCELED, FAIL_CANCELED)

        .on(ERROR_RECOVERY).delay(cancelDelays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return cancel((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, FAIL_CANCELED)
          .transition(RC_IOEX, FAIL_IO_3)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_3)
          .transition(RC_ERROR, FAIL_ERROR)
          .transition(RC_SYS_ERROR, FAIL_ERROR)
          .transition(RC_RETRY, ERROR_RECOVERY)
          .transition(RC_EXPIRE, FAIL_ERROR)

        .on(ABORTING).delay(cancelDelays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return cancel((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, ABORTED)
          .transition(RC_IOEX, FAIL_IO_3)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_3)
          .transition(RC_ERROR, FAIL_ERROR)
          .transition(RC_SYS_ERROR, FAIL_ERROR)
          .transition(RC_RETRY, ABORTING)
          .transition(RC_EXPIRE, FAIL_ERROR)

        .on(FAIL_CANCELED).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return safeClose((TransactionContext)context);
            }
        }).end()
        
        .on(QUERY_EXPIRE).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return queryExpireClose((TransactionContext)context);
            }
        }).end()
        
        .on(ABORTED).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return safeAbort((TransactionContext)context);
            }
        }).end()

        .on(FAIL_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_PROTOCOL_3).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_IO_3).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end();
        
        
        machine = builder.build();
    }

    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE &&
                MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        return context.getServiceProvider().explainNotification(notification);
    }

    public String createAndPay(TransactionContext context, boolean resume) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_PAY), () -> {
            //时间过长 丢弃
            long delayMs = System.currentTimeMillis() - MapUtil.getLongValue(context.getTransaction(), DaoConstants.CTIME);
            if(delayMs > ApolloConfigurationCenterUtil.getWorkflowDiscardThreshold()) {
                // 刷脸支付有部分场景支持多次提交，此时不做控制
                Map<String, Object> extra = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_PARAMS);
                boolean isMultiplePay = MapUtil.getBooleanValue(extra, Transaction.IS_MULTIPLE_PAY, false);
                if (!isMultiplePay) {
                    logger.warn("sn {} delay {} ms, trade discard", MapUtil.getString(context.getOrder(), Order.SN), delayMs);
                    return RC_TRADE_DISCARD;
                }
            }
            String result = context.getServiceProvider().pay(context, resume);
            if (RC_IN_PROG.equals(result)) {
                context.setForceReturn(true);
            }
            logger.debug("TID {} createAndPay method returns {}", context.getTid(), result);
            return result;
        }).call();
    }
}
