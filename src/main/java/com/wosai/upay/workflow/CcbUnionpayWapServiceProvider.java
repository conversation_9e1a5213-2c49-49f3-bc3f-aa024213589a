package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.ccb.*;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2021/8/25 Time: 3:07 下午
 */
public class CcbUnionpayWapServiceProvider extends CcbWapServiceProvider {

    public static final String NAME = "provider.ccb.wap.unionpay";
    private static final DateTimeFormatter UNION_TIME_FORMATTER
            = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String UNION_PAY_QRCODE_REDIRECT_URL = "redirectUrl";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_UNIONPAY
                && (subPayway == Order.SUB_PAYWAY_WAP
                    || subPayway == Order.SUB_PAYWAY_MINI);
    }

    @Override
    protected String buildPrepaidResult(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_OUT_FIELDS);

        String redirectUrl =  MapUtil.getString(result, ResponseFields.AGN_DIR_ADR);
        if (StringUtils.isEmpty(redirectUrl)) {
            return Workflow.RC_ERROR;
        }
        Map<String, Object> wapRequest = new HashMap();
        wapRequest.put(UNION_PAY_QRCODE_REDIRECT_URL, redirectUrl);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    protected void buildWapParams(RequestBuilder builder, Map<String, Object> transaction) {
        Map<String, Object> extendedParams = (Map<String, Object>) transaction
                .get(Transaction.EXTENDED_PARAMS);
        String clientIp = MapUtil.getString(transaction, Transaction.CLIENT_IP);
        if (StringUtils.isEmpty(clientIp)) {
            clientIp = UpayUtil.getLocalHostIp();
        }

        long payAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String buyerId = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        String subject = BeanUtil.getPropString(transaction, Transaction.SUBJECT);
        builder.set(BusinessFields.CCB_IBS_VERSION, CcbConstants.CCB_IBS_VERSION);
        builder.set(BusinessFields.TX_CODE, CcbConstants.UNIONPAY_TX_CODE);
        builder.set(BusinessFields.AHN_TXN_AMT
                , com.wosai.mpay.util.StringUtils.cents2yuan(payAmount));
        builder.set(BusinessFields.ORDR_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.PY_VLD_TM, DEFAULT_TIME_EXPIRE_MINUTE * 60 + "");
        builder.set(BusinessFields.DEAL_OVERTIME, LocalDateTime.now()
                .plusMinutes(DEFAULT_TIME_EXPIRE_MINUTE).format(UNION_TIME_FORMATTER));
        builder.set(BusinessFields.ORDR_DSC, subject);
        builder.set(BusinessFields.USR_ID, buyerId);
        builder.set(BusinessFields.IP, clientIp);
        builder.set(BusinessFields.TXN_NTC_ADR, MapUtil.getString(extendedParams,"frontUrl"));
        builder.set(BusinessFields.TDCD_ADR, MapUtil.getString(extendedParams,"frontFailUrl"));
        builder.set(BusinessFields.VCHR_TDCD_IND, CcbConstants.TDCD_IND_DYNAMIC);
        builder.set(BusinessFields.USER_AUTH_CODE, MapUtil.getString(extendedParams, Transaction.EXTENDED_USER_AUTH_CODE));

    }

    protected void resolvePayFund(Map<String, Object> result, TransactionContext context) {}
}
