package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Created by wujianwei on 2018/5/24.
 */
public abstract class WeixinWapOrMiniServiceProvider extends WeixinServiceProvider {

    public static final Map PRECREATE_SUB_PAYWAY_TRADE_TYPE = CollectionUtil.hashMap(Order.SUB_PAYWAY_WAP + "", WeixinConstants.TRADE_TYPE_JSAPI,
            Order.SUB_PAYWAY_MINI + "", WeixinConstants.TRADE_TYPE_JSAPI,
            Order.SUB_PAYWAY_H5 + "", WeixinConstants.TRADE_TYPE_H5,
            Order.SUB_PAYWAY_APP + "", WeixinConstants.TRADE_TYPE_APP);

    private static String WEIXIN_H5_V2_RETUEN_URL = String.format("%s.%s", Transaction.EXTENDED_PARAMS, Transaction.RETURN_URL);

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        int provider = BeanUtil.getPropInt(transaction, Transaction.PROVIDER);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(ProtocolFields.VERSION, VERSION_ONE);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        if(TradeConfigService.PROVIDER_NUCC != provider){
            builder.set(BusinessFields.DETAIL, transaction.get(Transaction.BODY));
        }
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        builder.set(BusinessFields.TRADE_TYPE, BeanUtil.getPropString(PRECREATE_SUB_PAYWAY_TRADE_TYPE,subPayway + "",WeixinConstants.TRADE_TYPE_JSAPI));
        builder.set(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(System.currentTimeMillis() + defaultTimeExpire));
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        handlerCustomizedSwitch(builder,transaction);
        if (payerUid != null) {
            if(isUserOfSubAppid(extended, config, subPayway)){
                builder.set(BusinessFields.SUB_OPEN_ID, payerUid);
            }else{
                builder.set(BusinessFields.OPEN_ID, payerUid);
            }
        }
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        String currency = getTradeCurrency(transaction);
        builder.set(BusinessFields.FEE_TYPE, currency);
        if(!TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)) {
            handlerOverseasIndustry((Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS), config);
        }
        setProfitSharing(transaction, builder, BusinessFields.PROFIT_SHARING, WeixinConstants.PROFIT_SHARING_YES);
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        String merchantId = BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID);
        if (ApolloConfigurationCenterUtil.getWxNotSharingMerchantSet().contains(merchantId) &&
                (subPayWay == SubPayway.H5.getCode() || subPayWay == SubPayway.APP.getCode())) {
            builder.set(BusinessFields.PROFIT_SHARING, WeixinConstants.PROFIT_SHARING_NO);
        }
        //限制未成年人交易
        setLimitPayer(builder, transaction);
        // 设置万码银行服务商参数
        setDefaultAttachInSomeProvider(builder, transaction);
        carryOverExtendedParams(extended, builder, WeixinConstants.PRECREATE_ALLOWED_FIELDS);
        //设置场景字段的顺序一定要在carryOverExtendedParams方法之后
        setSceneInfo(builder, transaction, extended);
        Map<String,Object> request = builder.build();
        removeSubAppIdIfNeeded(request, extended, subPayway);
        savePrecreateSubAppIdWhenDirect(request, extended, transaction);
        Map<String,Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), 1, "wap create");
        } catch (Exception ex) {
            logger.error("failed to call weixin wap create", ex);
            setTransactionContextErrorInfo(context, "wap create", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)){
                return Workflow.RC_SYS_ERROR;
            }
            return Workflow.RC_ERROR;
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(provider == Order.PROVIDER_UNIONPAY
        		|| provider == Order.PROVIDER_DIRECT_UNIONPAY
                || provider == Order.PROVIDER_TL
                || provider == Order.PROVIDER_LAKALA_UNION_PAY
                || provider == Order.PROVIDER_HAIKE_UNION_PAY){
            String wcPayData = BeanUtil.getPropString(result, com.wosai.mpay.api.unionpay.ResponseFields.WC_PAY_DATA);
            try{
                Map<String, Object> wapRequest = objectMapper.readValue(wcPayData.getBytes(), Map.class);
                if(wapRequest != null && !wapRequest.isEmpty()){
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                    return Workflow.RC_CREATE_SUCCESS;
                }
            }catch (Exception e){

            }
            return Workflow.RC_IOEX;
        }
        //预下单成功
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        String subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String miniSubAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        if (Order.SUB_PAYWAY_MINI == subPayway && !StringUtil.empty(miniSubAppid)){
            subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        }
        if(subPayway == Order.SUB_PAYWAY_WAP) {
            subAppid = com.wosai.pantheon.util.MapUtil.getString(config, TransactionParam.WEIXIN_APP_ID);
        }
        // 优先使用上送的sub_app_id
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, ProtocolFields.SUB_APP_ID);
        if(!StringUtil.empty(sendSubAppId)) {
            subAppid = sendSubAppId;
        }
        
        wapRequest.put(WapFields.APP_ID, subAppid);
        wapRequest.put(WapFields.TIME_STAMP, System.currentTimeMillis()/1000 +"");
        wapRequest.put(WapFields.NONCE_STR, getNonceStr());
        switch (subPayway){
            case Order.SUB_PAYWAY_APP:
                if (StringUtil.empty(BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID)) && StringUtil.empty(sendSubAppId)) {
                    wapRequest.put(WapFields.APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
                }
                if (StringUtil.empty(BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_MCH_ID))) {
                    wapRequest.put(WapFields.PARTNER_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
                } else {
                    wapRequest.put(WapFields.PARTNER_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
                }
                wapRequest.put(WapFields.PACKAGE, "Sign=WXPay");//微信APP支付,接口文档中要求  填写固定值Sign=WXPay
                wapRequest.put(WapFields.PREPAY_ID, result.get(ResponseFields.PREPAY_ID));
                break;
            case Order.SUB_PAYWAY_H5:
                wapRequest.put(WapFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_MD5);
                wapRequest.put(WapFields.PACKAGE, StringUtils.join(WapFields.PREPAY_ID, "=", result.get(ResponseFields.PREPAY_ID)));
                wapRequest.put(WapFields.PREPAY_ID, result.get(ResponseFields.PREPAY_ID));
                wapRequest.put(WapFields.H5_PAY_DIRECT_URL, result.get(ResponseFields.MWEB_URL));
                Object returnUrl = BeanUtil.getNestedProperty(transaction, WEIXIN_H5_V2_RETUEN_URL);
                if (returnUrl != null && !StringUtil.empty(String.valueOf(returnUrl))) {
                    try {
                        String redirectUrl = URLEncoder.encode(String.valueOf(returnUrl), WeixinConstants.CHARSET_UTF8);
                        wapRequest.put(WapFields.H5_PAY_DIRECT_URL, StringUtils.join(BeanUtil.getPropString(wapRequest, WapFields.H5_PAY_DIRECT_URL), "&", WapV2Fields.REDIRECT_URL, "=", redirectUrl));
                    } catch (UnsupportedEncodingException e) {
                        logger.error("returnUrl 编码失败:" + returnUrl);
                    }
                }
                break;
            case Order.SUB_PAYWAY_WAP:
            default:
                wapRequest.put(WapFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_MD5);
                wapRequest.put(WapFields.PACKAGE, StringUtils.join(WapFields.PREPAY_ID, "=", result.get(ResponseFields.PREPAY_ID)));
                break;
        }
        try {
            Map<String,Object> signParams = wapRequest;
            if(subPayway == Order.SUB_PAYWAY_APP){
                signParams = getAppSignParams(signParams);
            }
            String paySign = WeixinSignature.getSign(signParams, config.get(TransactionParam.WEIXIN_APP_KEY)+"", WeixinConstants.CHARSET_UTF8);
            wapRequest.put(WapFields.PAY_SIGN, paySign);
        } catch (MpayException e) {
            return Workflow.RC_ERROR;
        }
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    private String getNonceStr(){
        return new Random().nextLong() + "";
    }

    private Map<String,Object> getAppSignParams(Map<String,Object> wapRequest){
        Map<String,Object> map = new HashMap<>();
        map.put(PayResponse.APP_ID,wapRequest.get(WapFields.APP_ID));
        map.put(PayResponse.PARTNER_ID,wapRequest.get(WapFields.PARTNER_ID));
        map.put(PayResponse.PREPAY_ID,wapRequest.get(WapFields.PREPAY_ID));
        map.put(PayResponse.NONCESTR,wapRequest.get(WapFields.NONCE_STR));
        map.put(PayResponse.TIMESTAMP,wapRequest.get(WapFields.TIME_STAMP));
        map.put(PayResponse.PACKAGE,wapRequest.get(WapFields.PACKAGE));
        return map;
    }


    private void removeSubAppIdIfNeeded(Map<String,Object> request, Map<String,Object> extendedParams, int subPayway){
        if(subPayway == Order.SUB_PAYWAY_MINI){
            String extendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extendedParams, ProtocolFields.SUB_APP_ID);
            String appId = com.wosai.pantheon.util.MapUtil.getString(request, ProtocolFields.APP_ID, "");
            String subAppId = com.wosai.pantheon.util.MapUtil.getString(request, ProtocolFields.SUB_APP_ID, "");
            if(appId.equals(extendSubAppId) && appId.equals(subAppId)){
                //上送的与appid值一致，说明是用服务商级别配置的支付小程序获取到的用户信息, 移除请求参数的sub_appid, 防止 sub_mch_id与sub_appid不匹配
                request.remove(ProtocolFields.SUB_APP_ID);
            }
        }
    }

    /**
     * 记录直连交易预下单时候的sub_appid值，以便查单的时候上送相同的值，解决sub_mch_id 与 sub_appid不匹配的问题
     * @param request
     * @param extendedParams
     * @param transaction
     */
    private void savePrecreateSubAppIdWhenDirect(Map<String,Object> request, Map<String,Object> extendedParams, Map<String,Object> transaction){
        //扩展参数没有上送，不处理
        String extendSubAppId = MapUtil.getString(extendedParams, ProtocolFields.SUB_APP_ID);
        if(extendSubAppId == null){
            return;
        }
        // 间联不处理
        Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
        if(provider != null){
            return;
        }
        String subAppId = (String) request.get(ProtocolFields.SUB_APP_ID);
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_EXTRA_OUT_FIELDS_WEIXIN_SUB_APPID, subAppId);
    }

}
