package com.wosai.upay.workflow;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.cmbcbank.BusinessFields;
import com.wosai.mpay.api.cmbcbank.CMBCBankClient;
import com.wosai.mpay.api.cmbcbank.CMBCBankConstants;
import com.wosai.mpay.api.cmbcbank.RequestBuilder;
import com.wosai.mpay.api.cmbcbank.ResponseFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class CMBCBankServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(CMBCBankServiceProvider.class);
    public static final String NAME = "provider.cmbcbank";
    public static final SafeSimpleDateFormat DATE_FORMAT = new SafeSimpleDateFormat(CMBCBankConstants.DATE_TIME_FORMAT_DATE);
    public static final SafeSimpleDateFormat TIME_FORMAT = new SafeSimpleDateFormat(CMBCBankConstants.DATE_TIME_FORMAT_TIME);
    private String notifyHost;
    private int retryTimes = 3;

    @Autowired
    protected CMBCBankClient client;

    @SuppressWarnings("unchecked")
    private static final Map<String, Object> PAYWAY_PAYCHANNEL_TYPE = CollectionUtil.hashMap(
            Order.PAYWAY_ALIPAY2 + "" + Order.SUB_PAYWAY_BARCODE, CMBCBankConstants.PAY_WAY_ALIPAY_SCAN, 
            Order.PAYWAY_ALIPAY2 + "" + Order.SUB_PAYWAY_QRCODE, CMBCBankConstants.PAY_WAY_ALIPAY_QRCODE, 
            Order.PAYWAY_ALIPAY2 + "" + Order.SUB_PAYWAY_WAP, CMBCBankConstants.PAY_WAY_ALIPAY_H5_ZFBJSAPI, 
            Order.PAYWAY_ALIPAY2 + "" + Order.SUB_PAYWAY_MINI, CMBCBankConstants.PAY_WAY_ALIPAY_H5_ZFBJSAPI, 
            Order.PAYWAY_WEIXIN + "" + Order.SUB_PAYWAY_BARCODE, CMBCBankConstants.PAY_WAY_WEIXIN_SCAN, 
            Order.PAYWAY_WEIXIN + "" + Order.SUB_PAYWAY_QRCODE, CMBCBankConstants.PAY_WAY_WEIXIN_QRCODE,
            Order.PAYWAY_WEIXIN + "" + Order.SUB_PAYWAY_WAP, CMBCBankConstants.PAY_WAY_WEIXIN_H5_WXJSAPI,
            Order.PAYWAY_WEIXIN + "" + Order.SUB_PAYWAY_MINI, CMBCBankConstants.PAY_WAY_WEIXIN_WX_APPLET,
            Order.PAYWAY_UNIONPAY + "" + Order.SUB_PAYWAY_WAP, CMBCBankConstants.PAY_WAY_API_UNIONQRCODE,
            Order.PAYWAY_UNIONPAY + "" + Order.SUB_PAYWAY_BARCODE, CMBCBankConstants.PAY_WAY_API_CMBCSCAN,
            Order.PAYWAY_DCEP + "" + Order.SUB_PAYWAY_BARCODE, CMBCBankConstants.PAY_WAY_DCNY_SCAN,
            Order.PAYWAY_DCEP + "" + Order.SUB_PAYWAY_WAP, CMBCBankConstants.PAY_WAY_DCNY_QRCODE,
            Order.PAYWAY_DCEP + "" + Order.SUB_PAYWAY_MINI, CMBCBankConstants.PAY_WAY_DCNY_QRCODE
        );

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CMBC_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        initTransactionSomeValue(transaction);
        builder.set(BusinessFields.SELECT_TRADE_TYPE, PAYWAY_PAYCHANNEL_TYPE.get(MapUtil.getString(transaction, Transaction.PAYWAY) 
                + MapUtil.getString(transaction, Transaction.SUB_PAYWAY)));
        builder.set(BusinessFields.AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.ORDER_INFO, MapUtil.getString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.MERCHANT_SEQ, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.MCH_SEQ_NO, MapUtil.getString(transaction, Transaction.TSN));
        Date orderDate = new Date(MapUtil.getLongValue(order, DaoConstants.CTIME));
        builder.set(BusinessFields.TRANS_DATE, DATE_FORMAT.format(orderDate));
        builder.set(BusinessFields.TRANS_TIME, TIME_FORMAT.format(orderDate));
        builder.set(BusinessFields.REMARK, new String(Base64.encode(MapUtil.getString(extraParams, Transaction.BARCODE).getBytes())));
        setTerminalInfo(context, config, builder);
        setExtended(context, builder);
        
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(request, config, retryTimes, OP_PAY);
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in cmbcbank pay", ex);
            setTransactionContextErrorInfo(transaction, OP_PAY, ex);
            return Workflow.RC_IOEX;
        } catch (MpayException ex) {
            logger.error("failed to call cmbcbank pay", ex);
            setTransactionContextErrorInfo(transaction, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        String returnType = MapUtil.getString(result, ResponseFields.GATE_RETURN_TYPE);
        if (CMBCBankConstants.GATE_RETURN_TYPE_SUCCESS.equals(returnType)){
            Map<String,Object> business = MapUtil.getMap(result, ResponseFields.BUSINESS_CONTEXT);
            Map<String,Object> body = MapUtil.getMap(business, ResponseFields.BODY); 
            setTradeNoBuyerInfoIfExists(body, context);
            if (CMBCBankConstants.TRADE_STATUS_R.equals(MapUtil.getString(body, ResponseFields.TRADE_STATUS))) {
                // 民生银行会先返回支付中，然后再异步通知，需要做下查单
                int delayMs = ApolloConfigurationCenterUtil.getCMBCPayInProgDelayMs();
                if (delayMs > 0) {
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException e) {
                    }
                    if (Workflow.RC_PAY_SUCCESS.equals(query(context))){
                        return Workflow.RC_PAY_SUCCESS;
                    }
                }
                return Workflow.RC_IN_PROG;
            } else if (CMBCBankConstants.TRADE_STATUS_S.equals(MapUtil.getString(body, ResponseFields.TRADE_STATUS))) {
                resolvePayFund(body, context);
                return Workflow.RC_PAY_SUCCESS;
           }
        } else if (CMBCBankConstants.GATE_RETURN_TYPE_R.equals(returnType)) {
            return Workflow.RC_IN_PROG;
        } else if (CMBCBankConstants.GATE_RETURN_TYPE_E.equals(returnType)) {
            String gateReturnMessage = MapUtil.getString(result, ResponseFields.GATE_RETURN_MESSAGE);
            if (gateReturnMessage != null) {
                if (gateReturnMessage.endsWith(CMBCBankConstants.RETURN_MESSAGE_PAY_REPEAT)) {
                    return Workflow.RC_IN_PROG;
                } else {
                    return Workflow.RC_TRADE_CANCELED;
                }
            }
        }
        return Workflow.RC_ERROR;
    }

    private void resolvePayFund(Map<String, Object> body, TransactionContext context) {
        // 通道手续费
        String fee = MapUtil.getString(body, ResponseFields.FEE);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<String, Object>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        extraOutFields.put(Transaction.PROVIDER_FEE, fee);

        // 设置实付金额
        long actualPayAmt = MapUtil.getLongValue(body, ResponseFields.ACTUAL_PAY_AMT);
        if (actualPayAmt != 0) {
            transaction.put(Transaction.PAID_AMOUNT, actualPayAmt);
        }

        // 营销信息
        List<Map<String, Object>> payments = new ArrayList<Map<String,Object>>();
        String redInfo = MapUtil.getString(body, ResponseFields.RED_INFO);
        long discountAmount = 0L;
        if (!StringUtils.isEmpty(redInfo)) {
            redInfo = redInfo.replaceAll("\\n", "");
            Map<String, Object> redInfoMap = JsonUtil.jsonStrToObject(redInfo, Map.class);
            if (redInfoMap != null && redInfoMap.get(ResponseFields.RED_LIST) instanceof List) {
                List<Map<String, Object>> redList = (List<Map<String, Object>>) redInfoMap.get(ResponseFields.RED_LIST);
                for (Map<String, Object> rInfo : redList) {
                    String redChnl = MapUtil.getString(rInfo, ResponseFields.RED_CHNL);
                    long redFavAmt = MapUtil.getLongValue(rInfo, ResponseFields.RED_FAV_AMT);
                    // 非支付宝解析营销信息，支付宝会资金渠道中会返回
                    if (!redChnl.equals(CMBCBankConstants.RED_CHNL_ALPAY)) {
                        payments.add(CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, redChnl,
                                Transaction.PAYMENT_AMOUNT, redFavAmt
                                ));
                    }
                    discountAmount += redFavAmt;
                }
            }
        }

        // 支付资金信息
        Map<String, Object> centerInfo = MapUtil.getMap(body, ResponseFields.CENTER_INFO);
        if (MapUtil.isNotEmpty(centerInfo)) {
            int payway = MapUtil.getIntValue(context.getTransaction(), Transaction.PAYWAY);
            if (payway == Order.PAYWAY_ALIPAY2) {
                String fundBillList = MapUtil.getString(centerInfo, ResponseFields.FUND_BILL_LIST);
                AlipayV2ServiceProvider.resolvePayFund(context.getOrder(), context.getTransaction(), MapUtil.hashMap(BusinessV2Fields.FUND_BILL_LIST, fundBillList));
                List<Map<String, Object>> alipayPayments = (List<Map<String, Object>>) MapUtil.getObject(extraOutFields, Transaction.PAYMENTS);
                if (alipayPayments != null) {
                    payments.addAll(alipayPayments);
                }
            } else {
                String bankType = MapUtil.getString(centerInfo, ResponseFields.BANK_TYPE);
                long amount = MapUtil.getLongValue(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT) - discountAmount;
                if (payway == Order.PAYWAY_WEIXIN) {
                    payments.add(WeixinServiceProvider.getWeixinPaymentByBanktype(bankType, amount));
                } else {
                    if (StringUtils.isEmpty(bankType)){
                        String cardType = MapUtil.getString(body, ResponseFields.CARD_TYPE);
                        bankType = CMBCBankConstants.CARD_TYPE_CREDIT.equals(cardType) ? Payment.TYPE_BANKCARD_CREDIT : Payment.TYPE_BANKCARD_DEBIT;
                    }
                    payments.add(CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, bankType,
                            Transaction.PAYMENT_ORIGIN_TYPE, bankType,
                            Transaction.PAYMENT_AMOUNT, amount
                            ));
                }
            }
        }
        BeanUtil.setNestedProperty(context.getTransaction(), PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }

    private void setExtended(TransactionContext context, RequestBuilder builder) {
        Map<String, Object> extended = (Map<String, Object>) context.getTransaction().get(Transaction.EXTENDED_PARAMS);
        if (MapUtil.isNotEmpty(extended)) {
            Map copyExtended = new HashMap(extended);
            if (copyExtended.containsKey(com.wosai.mpay.api.weixin.BusinessFields.SUB_APPID)) {
                builder.set(BusinessFields.SUB_APP_ID, copyExtended.get(com.wosai.mpay.api.weixin.BusinessFields.SUB_APPID));
                copyExtended.remove(com.wosai.mpay.api.weixin.BusinessFields.SUB_APPID);
            }
            if (MapUtil.isNotEmpty(copyExtended)) {
                builder.set(BusinessFields.IN_EXT_DAT, JsonUtil.toJsonStr(copyExtended));
            }
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.MCH_SEQ_NO, MapUtil.getString(transaction, Transaction.TSN));
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(request, config, retryTimes, OP_CANCEL);
        } catch (Exception ex) {
            setTransactionContextErrorInfo(transaction, OP_CANCEL, ex);
            logger.error("encountered ioex in cmbcbank cancel", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String returnType = MapUtil.getString(result, ResponseFields.GATE_RETURN_TYPE);
        if (CMBCBankConstants.GATE_RETURN_TYPE_SUCCESS.equals(returnType)){
            Map<String,Object> business = MapUtil.getMap(result, ResponseFields.BUSINESS_CONTEXT);
            Map<String,Object> body = MapUtil.getMap(business, ResponseFields.BODY); 
            String tradeStatus = MapUtil.getString(body, ResponseFields.TRADE_STATUS);
             if (CMBCBankConstants.TRADE_STATUS_S.equals(tradeStatus)) {
                 return Workflow.RC_CANCEL_SUCCESS;
             }else if(CMBCBankConstants.TRADE_STATUS_R.equals(tradeStatus)){
                 return Workflow.RC_IN_PROG;
             }else if(CMBCBankConstants.TRADE_STATUS_C.equals(tradeStatus)){
                 return Workflow.RC_CANCEL_SUCCESS;
             }else if(CMBCBankConstants.TRADE_STATUS_T.equals(tradeStatus)){
                 return Workflow.RC_CANCEL_SUCCESS;
             }
        } else if (CMBCBankConstants.GATE_RETURN_TYPE_E.equals(returnType)) {
            String gateReturnMessage = MapUtil.getString(result, ResponseFields.GATE_RETURN_MESSAGE);
            if (!StringUtils.isEmpty(gateReturnMessage) && gateReturnMessage.endsWith(CMBCBankConstants.RETURN_MESSAGE_CANCEL_FAIL_PAY_SUCESS)) {
                return Workflow.RC_REFUND_SUCCESS.equals(refund(context)) ? Workflow.RC_CANCEL_SUCCESS : Workflow.RC_ERROR;
            }
        }
        return Workflow.RC_ERROR;
    }

    

    protected Map<String, Object> retryIfNetworkException(Map<String, Object> request, Map<String, Object> config, int times, String op) throws MpayApiNetworkError, MpayException {
        String privateKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.CMBC_PRIVATE_KEY));
        String pubicKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.CMBC_PLATFORM_PUBLIC_KEY));
        String privatePwd = MapUtil.getString(config, TransactionParam.CMBC_PRIVATE_KEY_PASSWORD);
        MpayApiNetworkError e = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), op), request, pubicKey, privateKey, privatePwd);
            } catch (MpayApiNetworkError ex) {
                logger.warn("encountered ioex in cmbcbank {}", op, ex);
                e = ex;
            }
        }
        if (e != null) {
            throw e;
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        return null;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.MERCHANT_SEQ, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.MCH_SEQ_NO, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(request, config, 1, OP_QUERY);
        } catch (MpayException | MpayApiNetworkError ex) {
            logger.error("failed to call cmbcbank query", ex);
            setTransactionContextErrorInfo(transaction, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        String returnType = MapUtil.getString(result, ResponseFields.GATE_RETURN_TYPE);
        if (CMBCBankConstants.GATE_RETURN_TYPE_SUCCESS.equals(returnType)){
            Map<String,Object> business = MapUtil.getMap(result, ResponseFields.BUSINESS_CONTEXT);
            Map<String,Object> body = MapUtil.getMap(business, ResponseFields.BODY);
            setTradeNoBuyerInfoIfExists(body, context);
            String tradeStatus = MapUtil.getString(body, ResponseFields.TRADE_STATUS);
            if (CMBCBankConstants.TRADE_STATUS_S.equals(tradeStatus)) {
                 resolvePayFund(body, context);
                 return Workflow.RC_PAY_SUCCESS;
            }else if(CMBCBankConstants.TRADE_STATUS_R.equals(tradeStatus)){
                 return Workflow.RC_IN_PROG;
            }else if(CMBCBankConstants.TRADE_STATUS_C.equals(tradeStatus)){
                 return Workflow.RC_TRADE_CANCELED;
            }else if(CMBCBankConstants.TRADE_STATUS_T.equals(tradeStatus)){
                 return Workflow.RC_REFUND_SUCCESS;
            }
        } else if (CMBCBankConstants.GATE_RETURN_TYPE_E.equals(returnType)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.MERCHANT_SEQ, MapUtil.getString(order, Order.SN));
        builder.set(BusinessFields.MCH_SEQ_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.ORDER_AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(request, config, 1, OP_REFUND);
        } catch (MpayException | MpayApiNetworkError ex) {
            logger.error("failed to call cmbcbank refund", ex);
            setTransactionContextErrorInfo(transaction, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String returnType = MapUtil.getString(result, ResponseFields.GATE_RETURN_TYPE);
        if (CMBCBankConstants.GATE_RETURN_TYPE_SUCCESS.equals(returnType)){
            Map<String,Object> business = MapUtil.getMap(result, ResponseFields.BUSINESS_CONTEXT);
            Map<String,Object> body = MapUtil.getMap(business, ResponseFields.BODY); 
            String tradeStatus = MapUtil.getString(body, ResponseFields.TRADE_STATUS);
             if (CMBCBankConstants.TRADE_STATUS_S.equals(tradeStatus)) {
                 transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                 resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
                 return Workflow.RC_REFUND_SUCCESS;
             } else if (CMBCBankConstants.TRADE_STATUS_R.equals(tradeStatus)){
                 return Workflow.RC_IN_PROG;
             }
        } else if(CMBCBankConstants.GATE_RETURN_TYPE_E.equals(returnType)) {
            String gateReturnMessage = MapUtil.getString(result, ResponseFields.GATE_RETURN_MESSAGE);
            if (gateReturnMessage!= null && gateReturnMessage.endsWith(CMBCBankConstants.RETURN_MESSAGE_REFUND_REPEAT)) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
                return Workflow.RC_REFUND_SUCCESS;
            } 
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        initTransactionSomeValue(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.SELECT_TRADE_TYPE, PAYWAY_PAYCHANNEL_TYPE.get(MapUtil.getString(transaction, Transaction.PAYWAY) 
                + MapUtil.getString(transaction, Transaction.SUB_PAYWAY)));
        builder.set(BusinessFields.AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.ORDER_INFO, MapUtil.getString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.MERCHANT_SEQ, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.MCH_SEQ_NO, MapUtil.getString(transaction, Transaction.TSN));
        Date orderDate = new Date(MapUtil.getLongValue(order, DaoConstants.CTIME));
        builder.set(BusinessFields.TRANS_DATE, DATE_FORMAT.format(orderDate));
        builder.set(BusinessFields.TRANS_TIME, TIME_FORMAT.format(orderDate));
        builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI){
            String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
            if (Order.PAYWAY_WEIXIN == payway){
                builder.set(BusinessFields.SUB_OPEN_ID, payerUid);
                builder.set(BusinessFields.SUB_APP_ID, config.get(TransactionParam.WEIXIN_SUB_APP_ID));
            } else if(Order.PAYWAY_ALIPAY == payway|| Order.PAYWAY_ALIPAY2 == payway){
                builder.set(BusinessFields.USER_ID, payerUid);
            }
        }
        setTerminalInfo(context, config, builder);
        setExtended(context, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(request, config, retryTimes, OP_PRECREATE);
        } catch (MpayException ex) {
            logger.error("failed to call cmbcbank precreate", ex);
            setTransactionContextErrorInfo(transaction, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in cmbcbank precreate", ex);
            setTransactionContextErrorInfo(transaction, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }

        if(CMBCBankConstants.GATE_RETURN_TYPE_SUCCESS.equals(MapUtil.getString(result, ResponseFields.GATE_RETURN_TYPE))){
            Map<String,Object> business = MapUtil.getMap(result, ResponseFields.BUSINESS_CONTEXT);
            Map<String,Object> body = MapUtil.getMap(business, ResponseFields.BODY); 
            String tradeStatus = MapUtil.getString(body, ResponseFields.TRADE_STATUS);
            if (CMBCBankConstants.TRADE_STATUS_R.equals(tradeStatus) || CMBCBankConstants.TRADE_STATUS_S.equals(tradeStatus)) {
                if (subPayway == Order.SUB_PAYWAY_QRCODE){
                    String payerInfo = MapUtil.getString(body, ResponseFields.PAY_INFO);
                    Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                    extraOutFields.put(Transaction.QRCODE, new String(Base64.decode(payerInfo)));
                } else if (subPayway == Order.SUB_PAYWAY_WAP ||subPayway == Order.SUB_PAYWAY_MINI) {
                    String payInfo = MapUtil.getString(body, ResponseFields.PAY_INFO);
                    Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                    Map<String, Object>  wapInfo = new HashMap<>();
                    if (payway == Order.PAYWAY_UNIONPAY) {
                        wapInfo.put(Transaction.REDIRECT_URL, new String(Base64.decode(payInfo)));
                    } else {
                        String [] payInfos = payInfo.indexOf("|")>= 0 ? payInfo.split("\\|") : new String[]{payInfo};
                        if(payInfos.length > 0) {
                            for (String info : payInfos) {
                                if(StringUtils.isEmpty(info)){
                                    continue;
                                }
                                int index = info.indexOf("=");
                                if (index < 0) {
                                    continue;
                                }
                                String key = info.substring(0, index);
                                String value = info.substring(index + 1);
                                if (ResponseFields.PREPAY_ID.equals(key)) {
                                    key = WapFields.PACKAGE;
                                    value = WapFields.PREPAY_ID + "=" + value;
                                } else if (ResponseFields.TRADE_NO.equals(key)) {
                                    key = WapV2Fields.TRADE_NO;
                                }
                                wapInfo.put(key, value);
                            }
                        }
                    }
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapInfo);
                }
                return Workflow.RC_CREATE_SUCCESS;
             }
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        return query(context);
    }

    protected void setTerminalInfo(TransactionContext context, Map<String, Object> configSnapshot, RequestBuilder builder) {
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
            builder.set(BusinessFields.GPS, terminalInfo.getFormatLatitude() + "," + terminalInfo.getFormatLongitude());
        }
        Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        String baseStation = MapUtil.getString(extraParams, Transaction.SQB_STATION);
        if (!StringUtils.isEmpty(baseStation)) {
            builder.set(BusinessFields.STATION, baseStation.replaceAll(",", "-"));
        }
        if (!terminalInfo.isOffset() && terminalInfo.isSendIp()) {
            builder.set(BusinessFields.TERM_IP, terminalInfo.getIp());
        }
    }


    /**
     * 获取默认的requestBuilder，设置请求默认值。
     *
     * @param context
     * @return
     */
    protected RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.PLATFORM_ID, MapUtil.getString(config, BusinessFields.CONFIG_PLATFORM_ID));
        builder.set(BusinessFields.MERCHANT_NO, MapUtil.getString(config, TransactionParam.CMBC_MCH_ID));
        return builder;
    }

    protected static void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();

            if (value != null) {
                builder.set(key, value);
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String returnType = MapUtil.getString(result, ResponseFields.GATE_RETURN_TYPE);
        String returnCode = MapUtil.getString(result, ResponseFields.GATE_RETURN_CODE);
        String returnMessage = MapUtil.getString(result, ResponseFields.GATE_RETURN_MESSAGE);
        if (!StringUtils.isEmpty(returnMessage)) {
            returnMessage = returnMessage.replaceFirst(CMBCBankConstants.RETURN_MESSAGE_REPLACE, "");
        }
        String tradeStatus = (String) com.wosai.mpay.util.MapUtils.getNestedProperty(result, String.format("%s.%s.%s", ResponseFields.BUSINESS_CONTEXT, ResponseFields.BODY, ResponseFields.TRADE_STATUS));
        Map<String, Object> providerInfo = CollectionUtil.hashMap(
                ResponseFields.GATE_RETURN_TYPE, returnType, 
                ResponseFields.GATE_RETURN_CODE, returnCode,
                ResponseFields.GATE_RETURN_MESSAGE, returnMessage, 
                ResponseFields.TRADE_STATUS, tradeStatus
        );
        setTransactionContextErrorInfo(context.getTransaction(), key, providerInfo, CMBCBankConstants.GATE_RETURN_TYPE_SUCCESS.equals(returnType), returnCode, returnMessage);
    }

    /**
     * 设置订单号
     * 
     * @param result
     * @param context
     */
    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        String buyerLogin = null;
        String buyerUid = null;
        String tradeNo = MapUtil.getString(result, ResponseFields.BANK_ORDER_NO); // 民生支付订单号
        String channelTradeNo = MapUtil.getString(result, ResponseFields.CENTER_SEQ_ID);
        Map<String, Object> centerInfo = MapUtil.getMap(result, ResponseFields.CENTER_INFO);
        if (MapUtil.isNotEmpty(centerInfo)) {
            int payway = MapUtil.getIntValue(context.getTransaction(), Transaction.PAYWAY);
            if (payway == Order.PAYWAY_ALIPAY2) {
                buyerLogin = MapUtil.getString(centerInfo, ResponseFields.BUYER_LOGON_ID);
                // 支付中返回buyer_user_id
                buyerUid = MapUtil.getString(centerInfo, ResponseFields.BUYER_USER_ID);
                if (StringUtils.isEmpty(buyerUid)) {
                    // 支付成功后返回buyer_id
                    buyerUid = MapUtil.getString(centerInfo, ResponseFields.BUYER_ID);
                }
            } else if(payway == Order.PAYWAY_WEIXIN) {
                buyerUid = MapUtil.getString(centerInfo, ResponseFields.OPEN_ID);

            }
        } else {
            buyerLogin = MapUtil.getString(result, ResponseFields.CARD_NO);
        }
        if (!StringUtils.isEmpty(buyerLogin)) {
            if (StringUtils.isEmpty(MapUtil.getString(order, Order.BUYER_LOGIN))) {
                order.put(Order.BUYER_LOGIN, buyerLogin);
            }
            if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
                transaction.put(Transaction.BUYER_LOGIN, buyerLogin);
            }
        }
        if (!StringUtils.isEmpty(buyerUid)) {
            if (StringUtils.isEmpty(MapUtil.getString(order, Order.BUYER_UID))) {
                order.put(Order.BUYER_UID, buyerUid);
            }
            if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }
        if (!StringUtils.isEmpty(tradeNo)) {
            if (StringUtils.isEmpty(MapUtil.getString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, tradeNo);
            }
            if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
        if (!StringUtils.isEmpty(channelTradeNo)) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (extraOutFields == null) {
                extraOutFields = new HashMap<>();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
        }
        String tranDate = MapUtil.getString(result, ResponseFields.TRAN_DATE);
        String tranTime = MapUtil.getString(result, ResponseFields.TRAN_TIME);
        if (!StringUtils.isEmpty(tranDate)  && !StringUtils.isEmpty(tranTime)) {
            try {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, TIME_FORMAT.parse(tranDate + tranTime + "000").getTime());
            } catch (ParseException e) {
                logger.warn("完成时间处理失败", e);
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            }
        }
    }

    

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_DIRECT_CMBCBANK;
    }
}
