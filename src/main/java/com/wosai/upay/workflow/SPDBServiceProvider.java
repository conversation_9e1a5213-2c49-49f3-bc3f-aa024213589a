package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.chinaums.ResponseFields;
import com.wosai.mpay.api.spdb.*;
import com.wosai.mpay.api.spdb.security.SpdbRsaUtil;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.CharacterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;


public class SPDBServiceProvider extends AbstractServiceProvider {
    public static final Logger LOGGER = LoggerFactory.getLogger(SPDBServiceProvider.class);
    @Autowired
    SPDBClient spdbClient;

    public static final String NAME = "provider.spdb";

    protected long b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE * 60 * 1000;

    protected long c2bTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;

    public static final String ONLINE = "01";// 渠道号线上

    public static final String POST = "post";// post

    public static final String GET = "get";// get

    private static final String SUB_APPID = "sub_appid";

    private static final SafeSimpleDateFormat dateTimeFormatSimple = new SafeSimpleDateFormat(SPDBConstant.YYYYMMDDHHMMSS);

    public SPDBServiceProvider() {
        super.dateFormat = new SafeSimpleDateFormat(SPDBConstant.YYYYMMDD);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_SPDB;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.SPDB_UP_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraParams = (Map<String, Object>) MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);

        //密钥
        String sqbSm2PrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_SM2_PRIVATE_KEY); //收钱吧sm2私钥加签
        String spdbSm2PublicKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_SM2_PUBLIC_KEY); //浦发sm2公钥验签
        String appSecretKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_APP_SECRET_KEY);

        SPDBRequestBuilder spdbRequestBuilder = getDefaultRequestBuilder(context);

        // 订单创建时间
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        spdbRequestBuilder.setBody(SPDBRequestFields.CRT_TM, dateTimeFormatSimple.format(new Date(orderCtime)));

        // 订单结束时间
        long aplEndTm = orderCtime + b2cTimeoutExpress;
        spdbRequestBuilder.setBody(SPDBRequestFields.APL_END_TM, dateTimeFormatSimple.format(new Date(aplEndTm)));

        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);

        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));

        String subject = CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT));
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);


        spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_AMT, amount);
        spdbRequestBuilder.setBody(SPDBRequestFields.MRCH_ORDR_NO, orderSn);
        spdbRequestBuilder.setBody(SPDBRequestFields.CHANNEL_NO, ONLINE);
        spdbRequestBuilder.setBody(SPDBRequestFields.CMDTY_DSC, subject);
        spdbRequestBuilder.setBody(SPDBRequestFields.ORD_TTL, subject);
        //支付授权码
        spdbRequestBuilder.setBody(SPDBRequestFields.AUTHR_CD, MapUtil.getString(extraParams, Transaction.BARCODE));

        if (Order.PAYWAY_WEIXIN == payway) {
            spdbRequestBuilder.setBody(SPDBRequestFields.USR_CHILD_FLG, payerUid);
            spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_OC);

        } else if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            spdbRequestBuilder.setBody(SPDBRequestFields.USER_ID2, payerUid);
            spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_OF);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_UA);
        }

        // 将参数投传到支付源
        carryOverExtendedParams(extendedParams, spdbRequestBuilder);
        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(spdbRequestBuilder, serviceUrl, getPrivateKeyContent(sqbSm2PrivateKey), getPrivateKeyContent(spdbSm2PublicKey), getPrivateKeyContent(appSecretKey), 1, OP_PAY, POST);
        } catch (Exception ex) {
            logger.error("failed to call spdb pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context);
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);

        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);

        //密钥
        String sqbSm2PrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_SM2_PRIVATE_KEY); //收钱吧sm2私钥加签
        String spdbSm2PublicKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_SM2_PUBLIC_KEY); //浦发sm2公钥验签
        String appSecretKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_APP_SECRET_KEY);


        SPDBRequestBuilder spdbRequestBuilder = getDefaultRequestBuilder(context);

        // 订单创建时间
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        spdbRequestBuilder.setBody(SPDBRequestFields.CRT_TM, dateTimeFormatSimple.format(new Date(orderCtime)));

        // 订单结束时间
        long aplEndTm = orderCtime + c2bTimeoutExpress;
        spdbRequestBuilder.setBody(SPDBRequestFields.APL_END_TM, dateTimeFormatSimple.format(new Date(aplEndTm)));

        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);

        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));

        String subject = CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT));

        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);


        spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_AMT, amount);
        spdbRequestBuilder.setBody(SPDBRequestFields.MRCH_ORDR_NO, orderSn);
        spdbRequestBuilder.setBody(SPDBRequestFields.CHANNEL_NO, ONLINE);
        spdbRequestBuilder.setBody(SPDBRequestFields.CMDTY_DSC, subject);


        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        if (Order.PAYWAY_WEIXIN == payway && terminalInfo.isSendPoi()) {
            spdbRequestBuilder.setBody(SPDBRequestFields.IP_ADDRESS, terminalInfo.getIp());
        }

        if (Order.PAYWAY_WEIXIN == payway) {
            spdbRequestBuilder.setBody(SPDBRequestFields.USR_CHILD_FLG, payerUid);
            if (Order.SUB_PAYWAY_WAP == subPayway) {
                spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_OA);
            } else if (Order.SUB_PAYWAY_MINI == subPayway) {
                spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_OM);
            }
        } else if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_OD);
            spdbRequestBuilder.setBody(SPDBRequestFields.USER_ID2, payerUid);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            spdbRequestBuilder.setBody(SPDBRequestFields.USER_ID2, payerUid);
            spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_TYPE, SPDBConstant.TRAN_TYPE_UB);
        }

        // 将参数投传到支付源
        carryOverExtendedParams(extendedParams, spdbRequestBuilder);

        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(spdbRequestBuilder, serviceUrl, getPrivateKeyContent(sqbSm2PrivateKey), getPrivateKeyContent(spdbSm2PublicKey), getPrivateKeyContent(appSecretKey), 1, OP_PRECREATE, POST);
        } catch (Exception ex) {
            logger.error("failed to call spdb precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildPrecreateResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbSm2PrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_SM2_PRIVATE_KEY); //收钱吧sm2私钥加签
        String spdbSm2PublicKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_SM2_PUBLIC_KEY); //浦发sm2公钥验签
        String appSecretKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_APP_SECRET_KEY);


        SPDBRequestBuilder spdbRequestBuilder = getDefaultRequestBuilder(context);
        spdbRequestBuilder.getBody().remove(SPDBRequestFields.TERMINAL_NO);
        // 订单创建日期
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        spdbRequestBuilder.setBody(SPDBRequestFields.ORIG_TRAN_DATE, formatTimeString(orderCtime));
        // 原始交易订单号
        String tradeNo = MapUtils.getString(transaction, Transaction.TRADE_NO);
        spdbRequestBuilder.setBody(SPDBRequestFields.EBANK_PY_ORDR_NO, tradeNo);

        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(spdbRequestBuilder, serviceUrl, getPrivateKeyContent(sqbSm2PrivateKey), getPrivateKeyContent(spdbSm2PublicKey), getPrivateKeyContent(appSecretKey), 3, OP_CANCEL, POST);
        } catch (Exception ex) {
            logger.error("failed to call spdb cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbSm2PrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_SM2_PRIVATE_KEY); //收钱吧sm2私钥加签
        String spdbSm2PublicKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_SM2_PUBLIC_KEY); //浦发sm2公钥验签
        String appSecretKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_APP_SECRET_KEY);

        int type = MapUtil.getInteger(transaction, Transaction.TYPE);
        SPDBRequestBuilder spdbRequestBuilder = getDefaultRequestBuilder(context);
        // 订单创建日期
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_DATE, formatTimeString(orderCtime));
        String tradeNo = MapUtil.getString(transaction, Transaction.TRADE_NO);
        spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_ORDR_NO, tradeNo);
        String opFlag = OP_QUERY;
        if (Transaction.TYPE_REFUND == type) {
            opFlag = OP_REFUND_QUERY;
        } else if (Transaction.TYPE_CANCEL == type) {
            opFlag = OP_CANCEL_QUERY;
        }
        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(spdbRequestBuilder, serviceUrl, getPrivateKeyContent(sqbSm2PrivateKey), getPrivateKeyContent(spdbSm2PublicKey), getPrivateKeyContent(appSecretKey), 3, opFlag, GET);
        } catch (Exception ex) {
            logger.error("failed to call spdbbank refund query", ex);
            setTransactionContextErrorInfo(context, opFlag, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, opFlag);
        return buildQueryResult(result, context, opFlag);
    }

    @Override
    public String refund(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbSm2PrivateKey = MapUtil.getString(tradeParams, TransactionParam.SQB_SM2_PRIVATE_KEY); //收钱吧sm2私钥加签
        String spdbSm2PublicKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_SM2_PUBLIC_KEY); //浦发sm2公钥验签
        String appSecretKey = MapUtil.getString(tradeParams, TransactionParam.SPDB_APP_SECRET_KEY);


        SPDBRequestBuilder spdbRequestBuilder = getDefaultRequestBuilder(context);
        // 订单创建日期
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN); // 订单号
        spdbRequestBuilder.setBody(SPDBRequestFields.TRAN_AMT, amount);
        spdbRequestBuilder.setBody(SPDBRequestFields.ORIG_TRAN_DATE, formatTimeString(orderCtime));
        spdbRequestBuilder.setBody(SPDBRequestFields.BUSS_LST_NO, orderSn);

        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(spdbRequestBuilder, serviceUrl, getPrivateKeyContent(sqbSm2PrivateKey), getPrivateKeyContent(spdbSm2PublicKey), getPrivateKeyContent(appSecretKey), 1, OP_REFUND, POST);
        } catch (Exception ex) {
            logger.error("failed to call spdb pay", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context, OP_REFUND);
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        logger.info("浦发回调返回,重新查询结果");
        return query(context);
    }

    protected SPDBRequestBuilder getDefaultRequestBuilder(TransactionContext context) {

        SPDBRequestBuilder spdbRequestBuilder = new SPDBRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String clientId = BeanUtil.getPropString(tradeParams, TransactionParam.SPDB_CLIENT_ID);
        String merchantNo = BeanUtil.getPropString(tradeParams, TransactionParam.SPDB_PROVIDER_MCH_ID);
        String terminalNo = BeanUtil.getPropString(tradeParams, TransactionParam.SPDB_PROVIDER_TERM_ID);

        Map<String, String> head = spdbRequestBuilder.getHead();
        head.put(SPDBRequestFields.X_SPDB_CLIENT_ID, clientId);
        head.put(SPDBRequestFields.CONTENT_TYPE, "application/json;charset=utf-8");
        head.put(SPDBRequestFields.X_SPDB_SM, "true");
        head.put(SPDBRequestFields.X_SPDB_ENCRYPTION, "true");


        Map<String, Object> body = spdbRequestBuilder.getBody();
        body.put(SPDBRequestFields.MRCH_ID, merchantNo);
        body.put(SPDBRequestFields.TERMINAL_NO, terminalNo);

        return spdbRequestBuilder;

    }

    private Map<String, Object> retryIfNetworkException(SPDBRequestBuilder spdbRequestBuilder, String serviceUrl, String sqbPrivateKey, String spdbPublicKey, String secretKey, int times, String opFlag, String method) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            return spdbClient.call(spdbRequestBuilder, serviceUrl, sqbPrivateKey, spdbPublicKey, secretKey, method);
        }
        logger.error("still network i/o error after retrying {} times, op is {}", times, opFlag);

        throw exception;
    }


    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String returnCode = MapUtils.getString(result, SPDBResponseFields.STATUS_CODE, "");//返回的响应码
        String returnMsg = MapUtils.getString(result, SPDBResponseFields.STATUS_MSG, "");//响应描述
        String orderStatus = MapUtils.getString(result, SPDBResponseFields.ORDR_ST, "");//订单状态
        String cancelOrderSt = MapUtils.getString(result, SPDBResponseFields.ORG_ORDR_ST_CD);  //订单取消状态
        String errorCode = MapUtils.getString(result, SPDBResponseFields.ERR_CODE, ""); //错误code
        String errorMsg = MapUtils.getString(result, SPDBResponseFields.ERR_INFO, "");  // 错误msg

        map.put(SPDBResponseFields.STATUS_CODE, returnCode);//返回状态码
        map.put(SPDBResponseFields.STATUS_MSG, returnMsg);//返回信息
        map.put(SPDBResponseFields.ORDR_ST, orderStatus);
        map.put(SPDBResponseFields.ERR_CODE, errorCode);
        map.put(SPDBResponseFields.ERR_INFO, errorMsg);
        boolean isSuccess = false;
        // 查询订单状态为00表示交易成功、09支付中、或者 关单成功 都标记为成功
        if (Objects.equals(returnCode, SPDBConstant.STATUS_CODE_0000)
                && (SPDBConstant.STATUS_CODE_00.equals(orderStatus)
                || SPDBConstant.STATUS_CODE_09.equals(orderStatus)
                || (SPDBConstant.STATUS_CODE_00.equals(cancelOrderSt) && SPDBConstant.ORDER_ClOSE_SUCCESS.equals(errorCode)))) {
            isSuccess = true;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, errorCode, errorMsg);
    }


    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String returnCode = MapUtils.getString(result, SPDBResponseFields.STATUS_CODE);//返回的响应码
        String statusCode = MapUtils.getString(result, SPDBResponseFields.ORDR_ST);//订单状态

        setTradeNoBuyerInfoIfExists(result, context, OP_PAY);
        if (SPDBConstant.STATUS_CODE_0002.equals(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0001.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0000.equals(returnCode)) {
            if (SPDBConstant.STATUS_CODE_00.equals(statusCode)) {
                long channelFinishTime = getChannelFinishTime(result);
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                resolvePayFund(context, result);
                return Workflow.RC_PAY_SUCCESS;
            } else if (SPDBConstant.STATUS_CODE_09.equals(statusCode)) {
                return Workflow.RC_IN_PROG;
            }
        }
        return Workflow.RC_ERROR;
    }

    private static long getChannelFinishTime(Map<String, Object> result) {
        long channelFinishTime = System.currentTimeMillis();
        //付款成功
        try {
            channelFinishTime = dateTimeFormatSimple.parse(MapUtils.getString(result, SPDBResponseFields.TRAN_DATE)).getTime();
        } catch (Exception e) {

        }
        return channelFinishTime;
    }

    public String buildPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String returnCode = MapUtils.getString(result, SPDBResponseFields.STATUS_CODE);//返回的响应码
        String statusCode = MapUtils.getString(result, SPDBResponseFields.ORDR_ST);//订单状态

        setTradeNoBuyerInfoIfExists(result, context, OP_PRECREATE);
        if (SPDBConstant.STATUS_CODE_0002.equals(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0001.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0000.equals(returnCode)) {
            if (SPDBConstant.STATUS_CODE_09.equals(statusCode)) {
                Map<String, Object> transaction = context.getTransaction();
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
                if (payway == Order.PAYWAY_WEIXIN) {
                    Map<String, Object> buildWapMap = new HashMap<>();
                    String sgnData = MapUtil.getString(result, SPDBResponseFields.SGN_DATA);
                    String signature = MapUtil.getString(result, SPDBResponseFields.SIGNATURE);
                    buildWapMap.put(WapFields.PAY_SIGN, signature);
                    builderWapRequest(buildWapMap, sgnData);
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, buildWapMap);
                } else if (payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_ALIPAY) {
                    String tradeNo = MapUtils.getString(result, SPDBResponseFields.SGN_DATA); //支付单号
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
                } else if (payway == Order.PAYWAY_UNIONPAY) {
                    String redirectUrl = MapUtil.getString(result, SPDBResponseFields.QR_CD_LINK);
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(ResponseFields.REDIRECTURL, redirectUrl));
                }
                return Workflow.RC_CREATE_SUCCESS;
            }
        }
        return Workflow.RC_TRADE_CANCELED;
    }

    // 构建微信小程序和公众号支付返回请求参数
    public void builderWapRequest(Map<String, Object> buildWapMap, String signData) {
        String[] signArr = signData.split("&");
        for (String signAttr : signArr) {
            String[] sign = signAttr.split("=");
            if (sign.length == 3) {
                buildWapMap.put(sign[0], sign[1] + "=" + sign[2]);
            } else {
                buildWapMap.put(sign[0], sign[1]);
            }
        }
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
            String userId = MapUtils.getString(result, SPDBResponseFields.USER_ID);
            if (!StringUtil.empty(userId)) {
                transaction.put(Transaction.BUYER_UID, userId);
            }
        }
        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
            String accountId = MapUtils.getString(result, SPDBResponseFields.BYR_OF_ALIPAY_ACCT_NO);
            if (!StringUtil.empty(accountId)) {
                transaction.put(Transaction.BUYER_LOGIN, accountId);
            }
        }

        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String orderId = null;
            orderId = MapUtils.getString(result, SPDBResponseFields.TRAN_ORDR_NO); // 支付第三方通道的订单号
            if (!StringUtils.isEmpty(orderId)) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
        }
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(BeanUtil.getPropString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String tpOrderId = null;
            if (Objects.equals(op, OP_PRECREATE) && (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway)) {
                //支付宝预下单返回付款返回
                tpOrderId = MapUtils.getString(result, SPDBResponseFields.SGN_DATA); //第三方订单号
            } else {
                // 微信订单号
                tpOrderId = MapUtils.getString(result, SPDBResponseFields.THD_PTY_SEQ);
            }
            if (!StringUtils.isEmpty(tpOrderId)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tpOrderId);
            }
        }
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        long amount = StringUtils.yuan2cents(MapUtils.getString(result, SPDBResponseFields.TRAN_AMT)); //交易金额
        long userPayAmount = StringUtils.yuan2cents(MapUtils.getString(result, SPDBResponseFields.BYR_PAY_AMT)); //实际付款金额
        long receiveAmount = StringUtils.yuan2cents(MapUtils.getString(result, SPDBResponseFields.ACT_RCV_AMT)); //实际付款金额

        long discount = amount - userPayAmount;
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (userPayAmount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, userPayAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));
        }
        if (discount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, discount
                    )
            );
        }
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (discount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
        }
        transaction.put(Transaction.PAID_AMOUNT, userPayAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);

    }


    private void resolveQueryFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        long tranAmt = StringUtils.yuan2cents(MapUtils.getString(result, SPDBResponseFields.TRAN_AMT)); //交易金额
        transaction.put(Transaction.PAID_AMOUNT, tranAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, tranAmt);
    }

    public String buildRefundResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String returnCode = MapUtils.getString(result, SPDBResponseFields.STATUS_CODE); //返回的响应码
        String returnStCd = MapUtils.getString(result, SPDBResponseFields.ORDR_ST);//订单状态
        setTradeNoBuyerInfoIfExists(result, context, op);
        if (SPDBConstant.STATUS_CODE_0002.equals(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0001.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0000.equals(returnCode)) {
            if (SPDBConstant.STATUS_CODE_00.equals(returnStCd)) {
                //退款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, SPDBResponseFields.TRAN_DATE)));
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            } else {
                //须进行退款查询，从而确定最终的退款情况
                return query(context);
            }
        }
        return Workflow.RC_ERROR;
    }


    public String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String returnCode = MapUtils.getString(result, SPDBResponseFields.STATUS_CODE); //返回的响应码
        String orderSt = MapUtils.getString(result, SPDBResponseFields.ORG_ORDR_ST_CD);  //订单状态
        String errorCode = MapUtils.getString(result, SPDBResponseFields.ERR_CODE); //错误code

        if (SPDBConstant.STATUS_CODE_0002.equals(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0001.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0000.equals(returnCode)) {
            if (SPDBConstant.ORDER_ClOSE_SUCCESS.equals(errorCode)) {
                return Workflow.RC_CANCEL_SUCCESS;
            } else if (SPDBConstant.STATUS_CODE_00.equals(orderSt) && SPDBConstant.ORDER_ClOSE_FAIL.equals(errorCode)) {
                // 关单失败，但是原始交易成功 发起退款
                String refundResult = refund(context);
                return Workflow.RC_REFUND_SUCCESS.equals(refundResult) ? Workflow.RC_CANCEL_SUCCESS : refundResult;
            } else if (SPDBConstant.STATUS_CODE_00.equals(orderSt)) {
                //须进行撤单查询，从而确定最终的撤单情况
                return query(context);
            }
        }
        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String returnCode = MapUtils.getString(result, SPDBResponseFields.STATUS_CODE);//返回的响应码
        String returnStCd = MapUtils.getString(result, SPDBResponseFields.ORDR_ST);//订单状态
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getInteger(transaction, Transaction.TYPE);
        if (SPDBConstant.STATUS_CODE_0002.equals(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0001.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (SPDBConstant.STATUS_CODE_0000.equals(returnCode)) {
            if (SPDBConstant.STATUS_CODE_00.equals(returnStCd)) {
                if (Transaction.TYPE_PAYMENT == type) {
                    long channelFinishTime = getChannelFinishTime(result);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                    resolveQueryFund(context, result);
                    return Workflow.RC_PAY_SUCCESS;
                } else if (Transaction.TYPE_CANCEL == type) {
                    long channelFinishTime = getChannelFinishTime(result);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                    return Workflow.RC_CANCEL_SUCCESS;
                } else if (Transaction.TYPE_REFUND == type) {
                    long channelFinishTime = getChannelFinishTime(result);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                    resolveRefundFund(context);
                    return Workflow.RC_REFUND_SUCCESS;
                }
            } else if (SPDBConstant.STATUS_CODE_09.equals(returnStCd)) {
                return Workflow.RC_IN_PROG;
            }
        }
        return Workflow.RC_ERROR;
    }


    public String getOrderSn(String encryptRequest) {
        Map<String, Object> stringObjectMap = decryptNotify(encryptRequest);
        return MapUtil.getString(stringObjectMap, SPDBResponseFields.MRCH_ORDR_NO3, null);
    }

    public Map<String, Object> decryptNotify(String encryptRequest) {
        Map<String, String> spdbDecryptParams = ApolloConfigurationCenterUtil.getSpdbDecryptParamsMap();
        if (MapUtil.isEmpty(spdbDecryptParams)) {
            return new HashMap<>();
        }
        String sqbWdPrivateKey = MapUtil.getString(spdbDecryptParams, TransactionParam.SQB_WD_PRIVATE_KEY);
        String sqbWdPrivateKeyContent = getPrivateKeyContent(sqbWdPrivateKey);

        String decryptBody = SpdbRsaUtil.decryptStr(encryptRequest, sqbWdPrivateKeyContent);
        return JSONObject.parseObject(decryptBody, new TypeReference<HashMap<String, Object>>() {
        });
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }


    private void carryOverExtendedParams(Map<String, Object> extended, SPDBRequestBuilder spdbRequestBuilder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();
            // 微信支付时用户子标识字段传微信用户id
            if (SUB_APPID.equals(key)) {
                // weixin sub_appid转换
                spdbRequestBuilder.setBody(SPDBRequestFields.SUB_MECH_NO_ACCT_ID, value);
                continue;
            }
            spdbRequestBuilder.setBody(key, value);
        }
    }


}
