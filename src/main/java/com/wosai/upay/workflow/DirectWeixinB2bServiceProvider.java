package com.wosai.upay.workflow;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.weixin.B2b.*;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/***
 * @ClassName: DirectWeixinB2bServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/10 11:46
 */
public class DirectWeixinB2bServiceProvider extends AbstractServiceProvider {

    public DirectWeixinB2bServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(UpayConstant.STANDARD_DATE_FORMAT);
        extendedFilterFields = new HashSet<>(Arrays.asList(BusinessFields.MCHID, BusinessFields.AMOUNT, BusinessFields.OUT_TRADE_NO));
    }

    @Resource
    protected WeixinB2bClient b2bClient;

    public static final String NAME = "provider.weixin.mini.B2b";
    private static int retryTimes = 3;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        boolean matchProductCode = matchProductCode(transaction, tradeParams, TransactionParam.SQB_PRODUCT_CODE_WEIXIN_B2B);
        return Order.PAYWAY_WEIXIN == payway && Order.SUB_PAYWAY_MINI == subPayway && MapUtil.isNotEmpty(tradeParams) && matchProductCode;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        // 微信B2B支付的参数配置是使用小程序的key
        return getTradeParams(transaction, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String cancel(TransactionContext context) {
        return null;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String appKey = MapUtil.getString(config, TransactionParam.WEIXIN_APP_KEY);
        String uri = WeixinConstants.COMMON_PAYMENT_URI;

        RequestBuilder builder = getB2BDefaultRequestBuilder(context);
        builder.set(BusinessFields.DESCRIPTION, MapUtil.getString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(BusinessFields.ORDER_AMOUNT, MapUtil.getInteger(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessFields.ENV, WeixinConstants.ENV_PRODUCTION);
        carryOverExtendedParams(extended, builder, WeixinConstants.PRE_CREATE_ALLOWED_FIELDS);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = b2bClient.buildSignData(uri, WeixinConstants.SIGN_TYPE_HMAC_SHA256, appKey, request);
        } catch (Exception ex) {
            logger.error("failed to call weixin B2b pay", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        result.put(BusinessFields.MODE, WeixinConstants.MODE_RETAIL_PAY_GOODS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, result);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getB2BDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));

        String subAppId = MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String resetSubAppId = resetSubAppId(transaction);
        if (com.wosai.pantheon.util.StringUtil.isNotBlank(resetSubAppId)) {
            subAppId = resetSubAppId;
        }
        builder.set(BusinessFields.ACCESS_TOKEN, getAccessToken(subAppId));

        Map<String, Object> result;
        try {
            GatewayUrl providerGateway = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_QUERY);
            result = retryIfNetworkException(providerGateway, config, builder.build(), FakeConstant.JSON_FORMAT, FakeConstant.CONTENT_TYPE_JSON, logger, 1, OP_PRECREATE, "wxB2B");
        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            logger.error("failed to call weixin B2b query", ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);  //错误码

        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if (!WeixinConstants.ERR_CODE_SUCCESS.equals(errCode)) {
            //通讯标识为失败，重新查询
            return Workflow.RC_IN_PROG;
        }
        setTradeNoBuyerInfoIfExists(result, context);
        String payStatus = MapUtil.getString(result, ResponseFields.PAY_STATUS);    //订单状态

        String rcFlag = Workflow.RC_ERROR;
        if (WeixinConstants.PAY_STATUS_ORDER_INIT.equals(payStatus) || WeixinConstants.PAY_STATUS_ORDER_PRE_PAY.equals(payStatus)) {
            rcFlag = Workflow.RC_IN_PROG;
        } else if (WeixinConstants.PAY_STATUS_ORDER_CLOSE.equals(payStatus)) {
            rcFlag = Workflow.RC_TRADE_CANCELED;
        } else if (WeixinConstants.PAY_STATUS_ORDER_PAY_SUCC.equals(payStatus)) {
            rcFlag = Workflow.RC_PAY_SUCCESS;
            //付款成功
            if (BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                resolvePayFund(result, context);
            }
        }
        return rcFlag;
    }

    /**
     * 解析返回金额相关信息
     *
     * @param context
     */
    public static void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (result == null) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> amount = MapUtil.getMap(result, ResponseFields.AMOUNT);
        long orderAmount = BeanUtil.getPropLong(amount, ResponseFields.ORDER_AMOUNT, 0);
        long payerAmount = BeanUtil.getPropLong(amount, ResponseFields.PAYER_AMOUNT, 0);
        transaction.put(Transaction.PAID_AMOUNT, payerAmount);
        if (orderAmount > 0) {
            transaction.put(Transaction.RECEIVED_AMOUNT, orderAmount);
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        }

        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getB2BDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.OUT_REFUND_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.REFUND_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.REFUND_FROM, WeixinConstants.REFUND_FROM_OTHERS);
        builder.set(BusinessFields.REFUND_REASON, WeixinConstants.REFUND_REASON_OTHER_REASON);

        String subAppId = MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String resetSubAppId = resetSubAppId(transaction);
        if (com.wosai.pantheon.util.StringUtil.isNotBlank(resetSubAppId)) {
            subAppId = resetSubAppId;
        }
        builder.set(BusinessFields.ACCESS_TOKEN, getAccessToken(subAppId));

        Map<String, Object> result;
        try {
            GatewayUrl providerGateway = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_REFUND);
            result = retryIfNetworkException(providerGateway, config, builder.build(), FakeConstant.JSON_FORMAT, FakeConstant.CONTENT_TYPE_JSON, logger, retryTimes, OP_REFUND, "wxB2b");
        } catch (Exception ex) {
            logger.error("failed to call weixin B2b refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常进行重试
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);  //错误码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if (WeixinConstants.ERR_CODE_SUCCESS.equals(errCode)) {
            //由于退款接口返回值中关键信息较少，故直接走退款查询接口
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String refundQuery(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());

        RequestBuilder builder = getB2BDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_REFUND_NO, MapUtil.getString(transaction, Transaction.TSN));

        String subAppId = MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String resetSubAppId = resetSubAppId(transaction);
        if (com.wosai.pantheon.util.StringUtil.isNotBlank(resetSubAppId)) {
            subAppId = resetSubAppId;
        }
        builder.set(BusinessFields.ACCESS_TOKEN, getAccessToken(subAppId));

        Map<String, Object> result;
        try {
            GatewayUrl providerGateway = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_REFUND_QUERY);
            result = retryIfNetworkException(providerGateway, config, builder.build(), FakeConstant.JSON_FORMAT, FakeConstant.CONTENT_TYPE_JSON, logger, 1, OP_REFUND_QUERY, "wxB2b");
        } catch (Exception ex) {
            logger.error("failed to call weixin B2b refund query", ex);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);  //错误码
        if (WeixinConstants.ERR_CODE_SUCCESS.equals(errCode)) {
            String refundStatus = MapUtil.getString(result, ResponseFields.REFUND_STATUS);    //退款状态
            if (WeixinConstants.REFUND_STATUS_REFUND_SUCC.equals(refundStatus)) {
                //支付成功
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, ResponseFields.REFUND_TIME)));
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            } else if (WeixinConstants.REFUND_STATUS_REFUND_INIT.equals(refundStatus) || WeixinConstants.REFUND_STATUS_REFUND_PROCESSING.equals(refundStatus)) {
                return Workflow.RC_RETRY;
            } else if (WeixinConstants.REFUND_STATUS_REFUND_FAIL.equals(refundStatus)) {
                return Workflow.RC_ERROR;
            }
        }

        return Workflow.RC_ERROR;
    }

    @Override
    protected Map<String, Object> call(String url, Map<String, Object> config, Map<String, Object> request) throws Exception {

        String appKey = MapUtil.getString(config, TransactionParam.WEIXIN_APP_KEY);
        String accessToken = (String)request.remove(BusinessFields.ACCESS_TOKEN);

        return b2bClient.call(url, WeixinConstants.SIGN_TYPE_HMAC_SHA256, appKey, null, request, accessToken);
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(Transaction.TYPE_PAYMENT != type){
            return null;
        }
        try{
            if(context.isFakeRequest()){
                boolean asExpected = true; //回调是否符合预期

                String outTradeNo = MapUtil.getString(providerNotification, ResponseFields.OUT_TRADE_NO);
                if(!MapUtil.getString(transaction, Transaction.ORDER_SN).equals(outTradeNo)){
                    asExpected = false;
                }

                String payStatus = MapUtil.getString(providerNotification, ResponseFields.PAY_STATUS);    //订单状态
                if (!WeixinConstants.PAY_STATUS_ORDER_PAY_SUCC.equals(payStatus)) {
                    asExpected = false;
                }

                if(context.isFakeRequest() || asExpected) {
                    //success
                    setTradeNoBuyerInfoIfExists(providerNotification, context);
                    resolvePayFund(providerNotification, context);
                    return Workflow.RC_PAY_SUCCESS;
                }
            }
        }catch (Exception e){
            logger.error("process notify error ", e);
        }
        //默认还是再查询一遍
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    public RequestBuilder getB2BDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.MCHID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        return builder;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if ((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                try {
                    if (BusinessFields.DELIVERY_TYPE.equals(key)) {
                        builder.set(key, Integer.parseInt(value.toString()));
                    } else {
                        builder.set(key, value instanceof String ? value : objectMapper.writeValueAsString(value));
                    }
                } catch (JsonProcessingException e) {
                    logger.error("process extend fields fail: " + e.getMessage(), e);
                }
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);  //错误码
        String errMsg = MapUtil.getString(result, ResponseFields.ERR_MSG);    //错误信息
        Map<String, Object> map = new LinkedHashMap<>();
        map.put(ResponseFields.ERR_CODE, errCode);
        map.put(ResponseFields.ERR_MSG, errMsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map,
                WeixinConstants.ERR_CODE_SUCCESS.equals(errCode), errCode, errMsg
        );
    }


    protected void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        //支付者
        String payerOpenid = BeanUtil.getPropString(result, ResponseFields.PAYER_OPENID);
        //B2b支付订单号
        String orderId = BeanUtil.getPropString(result, ResponseFields.ORDER_ID);
        //微信支付订单号
        String wxpayTransactionId = BeanUtil.getPropString(result, ResponseFields.WXPAY_TRANSACTION_ID);
        //支付完成时间
        String payTime = BeanUtil.getPropString(result, ResponseFields.PAY_TIME);

        if (!StringUtil.empty(payerOpenid)) {
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                transaction.put(Transaction.BUYER_UID, payerOpenid);
            }
            if (StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_UID))) {
                order.put(Order.BUYER_UID, payerOpenid);
            }
        }

        if (!StringUtil.empty(orderId)) {
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
            if (StringUtil.empty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, orderId);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            if (!StringUtils.isEmpty(wxpayTransactionId)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, wxpayTransactionId);
            }
        }

        if (!StringUtil.empty(payTime)) {
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME))) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(payTime));
            }
        }
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    //微信公众平台后台会保证在5分钟内，新老 access_token 都可用
    public String getAccessToken(String authorizerAppId){
        return serviceFacade.getAccessTokenByAppId(authorizerAppId);
    }

    /**
     * 上送的sub_appid值
     * @param transaction
     */
    private String resetSubAppId(Map<String,Object> transaction){
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        //不包含这个key， 说明支付请求没有记录相关值，此时不做处理
        if(!extraOutFields.containsKey(Transaction.WEIXIN_SUB_APPID)){
            return null;
        }
        return MapUtil.getString(extraOutFields, Transaction.WEIXIN_SUB_APPID);
    }
}
