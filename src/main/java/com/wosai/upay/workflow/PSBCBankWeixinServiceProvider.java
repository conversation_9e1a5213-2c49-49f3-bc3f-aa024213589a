package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.psbcbank.PSBCBankConstants;
import com.wosai.mpay.api.psbcbank.PSBCBusinessFields;
import com.wosai.mpay.api.psbcbank.PSBCRequestBuilder;
import com.wosai.mpay.api.psbcbank.PSBCResponseFields;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.NumberUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Description PSBCBankWeixinServiceProvider
 * @Date 2021/4/9 9:42 AM
 */
@ServiceProvicerPriority(priority = 1)
public class PSBCBankWeixinServiceProvider extends PSBCBankServiceProvider {

    public static final String NAME = "provider.psbcbank.weixin";

    public static final String WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE = "CFT";
    public static final String WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX = "DEBIT";
    public static final String WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX = "CREDIT";
    public static final String PROMOTION_DETAIL_TYPE_COUPON = "COUPON"; //代金券，需要走结算资金的 充值型代金券
    public static final String PROMOTION_DETAIL_TYPE_DISCOUNT = "DISCOUNT"; //优惠券，不走结算资金的免 充值型优惠券
    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_WEIXIN;
    }

    public PSBCBankWeixinServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_FEE, BusinessFields.FEE_TYPE));
    }

    @Override
    public PSBCRequestBuilder getPayRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);

        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_WEIXIN_ORDER_PAY);

        builder = getDefaultWeixinRequestBuilder(builder, context);
        //订单描述
        builder.bizSet(BusinessFields.BODY, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //商户订单号
        builder.bizSet(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //订单总金额，单位为分
        builder.bizSet(BusinessFields.TOTAL_FEE, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        //货币类型, 默认CNY
        builder.bizSet(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        //终端 IP,调用微信支付 API 的机器 IP
        builder.bizSet(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        //授权码
        builder.bizSet(BusinessFields.AUTH_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        long start = System.currentTimeMillis();
        builder.bizSet(BusinessFields.TIME_START, formatTimeString(start));
        builder.bizSet(BusinessFields.TIME_EXPIRE, formatTimeString(start + b2cTimeExpire));
        return builder;
    }

    @Override
    public PSBCRequestBuilder getPreCreateRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_WEIXIN_CREATE_ORDER);
        builder = getDefaultWeixinRequestBuilder(builder, context);

        //商品描述
        builder.bizSet(BusinessFields.BODY, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //商户订单号
        builder.bizSet(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //货币类型, 默认CNY
        builder.bizSet(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        //订单总金额，单位为分
        builder.bizSet(BusinessFields.TOTAL_FEE, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        //终端 IP,调用微信支付 API 的机器 IP
        builder.bizSet(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        //交易类型
        builder.bizSet(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_JSAPI);
        //openid
        builder.bizSet(BusinessFields.SUB_OPEN_ID, BeanUtil.getPropString(transaction, KEY_PAYER_UID));
        long start = System.currentTimeMillis();
        builder.bizSet(BusinessFields.TIME_START, formatTimeString(start));
        builder.bizSet(BusinessFields.TIME_EXPIRE, formatTimeString(start + defaultTimeExpire));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getRefundRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);

        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_WEIXIN_REFUND_ORDER);

        builder = getDefaultWeixinRequestBuilder(builder, context);
        //商户订单号
        builder.bizSet(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //商户退款单号
        builder.bizSet(BusinessFields.OUT_REFUND_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //总金额
        builder.bizSet(BusinessFields.TOTAL_FEE, BeanUtil.getPropString(order, Order.EFFECTIVE_TOTAL));
        //申请退款金额
        builder.bizSet(BusinessFields.REFUND_FEE, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        //退款货币种类
        builder.bizSet(BusinessFields.REFUND_FEE_TYPE, getTradeCurrency(transaction));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getRefundQueryRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_WEIXIN_REFUND_QUERY_ORDER);
        builder= getDefaultWeixinRequestBuilder(builder, context);
        //商户订单号
        builder.bizSet(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //商户退款单号
        builder.bizSet(BusinessFields.OUT_REFUND_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //退款金额,单位为元,精确到小数点后两位
        builder.set(PSBCResponseFields.TXN_AMT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getQueryRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_WEIXIN_QUERY_ORDER);
        builder = getDefaultWeixinRequestBuilder(builder, context);

        //商户订单号
        builder.bizSet(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getCloseRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_WEIXIN_CLOSE_ORDER);
        builder = getDefaultWeixinRequestBuilder(builder, context);
        //商户订单号
        builder.bizSet(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));

        return builder;
    }

    @Override
    public String resolveNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        String content = BeanUtil.getPropString(providerNotification, PSBCBusinessFields.REQ_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if(type == Transaction.TYPE_PAYMENT) {
            boolean asExpected = true; //回调是否符合预期
            String returnCode = BeanUtil.getPropString(respContent, ResponseFields.RETURN_CODE);
            String resultCode = BeanUtil.getPropString(respContent, ResponseFields.RESULT_CODE);
            if(!(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode))){
                asExpected = false;
            }
            String outTradeNo = BeanUtil.getPropString(respContent, ResponseFields.OUT_TRADE_NO);
            if(!BeanUtil.getPropString(transaction, Transaction.ORDER_SN).equals(outTradeNo)){
                asExpected = false;
            }
            long totalFee = BeanUtil.getPropLong(respContent, ResponseFields.TOTAL_FEE);
            if(totalFee != BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)){
                asExpected = false;
            }
            if(asExpected) {
                //success
                setTradeNoBuyerInfoIfExists(providerNotification, context);
                resolvePayFund(respContent, context);
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> call(String gatewayUrl, Map<String, Object> request, String secretKey, String sm2Pass) throws MpayException, MpayApiNetworkError, JsonProcessingException {
        return psbcBankClient.call(gatewayUrl, request, secretKey, sm2Pass);
    }

    @Override
    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        String respCode = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);//返回的响应码
        String respDesc = MapUtils.getString(result, PSBCResponseFields.RESP_DESC);//响应描述
        String returnCode = MapUtils.getString(respContent, ResponseFields.RETURN_CODE);//返回状态码
        String returnMsg = MapUtils.getString(respContent, ResponseFields.RETURN_MSG);//返回信息
        String resultCode = MapUtils.getString(respContent, ResponseFields.RESULT_CODE);//业务结果
        String errCode = MapUtils.getString(respContent, ResponseFields.ERR_CODE); //错误代码
        String errCodeDes = MapUtils.getString(respContent, ResponseFields.ERR_CODE_DES); //错误代码描述

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(PSBCResponseFields.RESP_CODE, respCode);//返回状态码
        map.put(PSBCResponseFields.RESP_DESC, respDesc);//返回信息
        map.put(ResponseFields.RETURN_CODE, returnCode);
        map.put(ResponseFields.RETURN_MSG, returnMsg);
        map.put(ResponseFields.RESULT_CODE, resultCode);
        map.put(ResponseFields.ERR_CODE, errCode);
        map.put(ResponseFields.ERR_CODE_DES, errCodeDes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, 
                WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode), 
                getNonValue(errCode, resultCode, respCode), 
                getNonValue(errCodeDes, returnMsg, respDesc));
    }

    @Override
    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTradeNoBuyerInfoIfExists(result, context);
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_TRADE_CANCELED;
        }
        if (PSBCBankConstants.RESP_CODE_PAYING.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_PROCESSING.equalsIgnoreCase(respCode)
                || PSBCBankConstants.RESP_CODE_ERROR.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR.equalsIgnoreCase(respCode)
                ||PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR_2.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_TIMEOUT.equalsIgnoreCase(respCode)){
            return Workflow.RC_IN_PROG;
        }
        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equalsIgnoreCase(respCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if(MapUtils.isNotEmpty(respContent)) {
            String returnCode = MapUtils.getString(respContent, ResponseFields.RETURN_CODE);//返回状态码
            String resultCode = MapUtils.getString(respContent, ResponseFields.RESULT_CODE);//业务结果
            String errCode = MapUtils.getString(respContent, ResponseFields.ERR_CODE); //错误代码

            if (StringUtil.empty(returnCode)) {
                return Workflow.RC_IN_PROG;
            }
            if (!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
                String returnMsg = BeanUtil.getPropString(respContent, ResponseFields.RETURN_MSG);
                if ("SYSTEM ERROR".equals(returnMsg)) {
                    return Workflow.RC_IN_PROG;
                } else {
                    return Workflow.RC_PROTOCOL_ERROR;
                }
            }
            if (!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
                if (WeixinConstants.RESULT_ERROR_CODE_ORDER_PAID.equals(errCode)) {
                    return Workflow.RC_IN_PROG;
                }
                if (WeixinConstants.RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST.contains(errCode)) {
                    return Workflow.RC_TRADE_CANCELED;
                }
                if (WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_FAIL_LIST.contains(errCode)) {
                    return Workflow.RC_TRADE_CANCELED;
                } else if (WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST.contains(errCode)) {
                    return Workflow.RC_IN_PROG;
                } else if (WeixinConstants.RESULT_ERROR_TRADE_ERROR.equals(errCode)) {
                    String errCodeDes = BeanUtil.getPropString(respContent, ResponseFields.ERR_CODE_DES); //错误代码描述
                    if (WeixinConstants.TRADE_ERROR_FAIL_MESSAGE.contains(errCodeDes)) {
                        return Workflow.RC_TRADE_CANCELED;
                    }
                } else {
                    return Workflow.RC_ERROR;
                }
            }

            if (WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
                boolean success = true;
                //刷卡交易应答报文增加交易状态(trade_state)字段，当return_code 、result_code、 trade_state都为SUCCESS的时收单机构才可认为交易成功
                String tradeState = MapUtils.getString(respContent, ResponseFields.TRADE_STATE);
                if (!WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
                    success = false;
                }
                if (success) {
                    //付款成功
                    resolvePayFund(respContent, context);
                    return Workflow.RC_PAY_SUCCESS;
                }
            }
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String buildPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();

        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.RESP_CODE_PAYING.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_PROCESSING.equalsIgnoreCase(respCode)
                || PSBCBankConstants.RESP_CODE_ERROR.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR.equalsIgnoreCase(respCode)
                ||PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR_2.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_TIMEOUT.equalsIgnoreCase(respCode)){
            return Workflow.RC_ERROR;
        }
        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equalsIgnoreCase(respCode)){
            return Workflow.RC_TRADE_CANCELED;
        }

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if(MapUtils.isNotEmpty(respContent)) {
            String returnCode = MapUtils.getString(respContent, ResponseFields.RETURN_CODE);//返回状态码
            String resultCode = MapUtils.getString(respContent, ResponseFields.RESULT_CODE);//业务结果
            String errCode = MapUtils.getString(respContent, ResponseFields.ERR_CODE); //错误代码

            if (!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
                return Workflow.RC_PROTOCOL_ERROR;
            }
            if (!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
                if (WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)) {
                    return Workflow.RC_RETRY;
                }
                return Workflow.RC_ERROR;
            }
            //预下单成功
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            String wcPayData = BeanUtil.getPropString(respContent, com.wosai.mpay.api.unionpay.ResponseFields.WC_PAY_DATA);
            try {
                Map wapRequest = objectMapper.readValue(wcPayData.getBytes(), Map.class);
                if (wapRequest != null && !wapRequest.isEmpty()) {
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                    return Workflow.RC_CREATE_SUCCESS;
                }
            } catch (Exception e) {
                return Workflow.RC_IOEX;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.REFUND_ERROR_LIST.contains(respCode)){
            return Workflow.RC_ERROR;
        }

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if(MapUtils.isNotEmpty(respContent)) {
            String returnCode = MapUtils.getString(respContent, ResponseFields.RETURN_CODE);//返回状态码
            String resultCode = MapUtils.getString(respContent, ResponseFields.RESULT_CODE);//业务结果

            if (WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
                //退款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                context.getTransaction().put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ID));
                resolveRefundFund(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            } else if (!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
                return Workflow.RC_PROTOCOL_ERROR;
            }
        } else {
            return doRefundQuery(context);
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        if(MapUtils.isEmpty(result)){
            return Workflow.RC_IOEX;
        }

        setTradeNoBuyerInfoIfExists(result, context);
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.QUERY_ERROR_LIST.contains(respCode)){
            return Workflow.RC_ERROR;
        }

        //原订单未成功需要继续轮训
        if (PSBCBankConstants.RESP_CODE_CLOSE_PAY_ERROR.equals(respCode) || PSBCBankConstants.RESP_CODE_PROCESSING.equals(respCode)){
            return Workflow.RC_IN_PROG;
        }

        String rcFlag = Workflow.RC_ERROR;
        //code:0000，响应成功
        if (Objects.equals(respCode, PSBCBankConstants.RESP_CODE_SUCCESS)) {
            String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
            Map respContent = JSONObject.parseObject(content, Map.class);
            if(MapUtils.isNotEmpty(respContent)) {
                String returnCode = MapUtils.getString(respContent, ResponseFields.RETURN_CODE);//返回状态码
                String resultCode = MapUtils.getString(respContent, ResponseFields.RESULT_CODE);//业务结果
                String errCode = MapUtils.getString(respContent, ResponseFields.ERR_CODE); //错误代码

                if (!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
                    //通讯标识为失败，重新查询
                    return Workflow.RC_IN_PROG;
                }
                if (!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
                    if (WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)) {
                        return Workflow.RC_IN_PROG;
                    } else if (WeixinConstants.RESULT_ERROR_ORDER_NOT_EXIST.equals(errCode)) {
                        // 下单成功后，再次查单返回“ORDERNOTEXIST”时，需要进行查单动作
                        Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction,
                                UpayUtil.getProviderErrorInfoKey(
                                        Order.SUB_PAYWAY_BARCODE == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) ? MpayServiceProvider.OP_PAY : MpayServiceProvider.OP_PRECREATE));
                        if (null == payResultMap
                                || WeixinConstants.RESULT_CODE_SUCCESS.equals(BeanUtil.getPropString(payResultMap, ResponseFields.RESULT_CODE))
                                || WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(BeanUtil.getPropString(payResultMap, ResponseFields.ERR_CODE))) {
                            return Workflow.RC_IN_PROG;
                        } else {
                            return Workflow.RC_ERROR;
                        }
                    } else {
                        return Workflow.RC_ERROR;
                    }
                }
                String tradeState = MapUtils.getString(respContent, ResponseFields.TRADE_STATE);
                if (WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)) {
                    rcFlag = Workflow.RC_IN_PROG;
                } else if (WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState)) {
                    //跟微信的人确认过，b2c下，not_pay表明用户已经取消了付款。
                    rcFlag = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
                } else if (WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
                    rcFlag = Workflow.RC_PAY_SUCCESS;
                    //付款成功
                    if (BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                        resolvePayFund(respContent, context);
                    }
                } else if (WeixinConstants.TRADE_STATE_PAYERROR.equals(tradeState)) {
                    rcFlag = Workflow.RC_TRADE_CANCELED;
                }
            }
        }
        return rcFlag;

    }

    @Override
    public String buildCloseResult(Map<String, Object> result, TransactionContext context) {

        if(MapUtils.isEmpty(result)){
            return Workflow.RC_IOEX;
        }

        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        String respDesc = BeanUtil.getPropString(result, PSBCResponseFields.RESP_DESC);////响应码

        boolean needRefund = false;
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.RESP_CODE_ORDER_SUCCESS.equals(respCode)){
            if ("该订单交易已成功".equals(respDesc)){
                needRefund = true;
            }
        }

        //若返回订单不存在或订单已关闭时，则返回Workflow.RC_CANCEL_SUCCESS
        if (PSBCBankConstants.QUERY_ERROR_LIST.contains(respCode)){
            return Workflow.RC_CANCEL_SUCCESS;
        }
        Map<String, Object> transaction = context.getTransaction();

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String returnCode = MapUtils.getString(respContent, ResponseFields.RETURN_CODE);//微信返回状态码
            String resultCode = MapUtils.getString(respContent, ResponseFields.RESULT_CODE);//微信业务结果
            String errCode = MapUtils.getString(respContent, ResponseFields.ERR_CODE); //微信错误代码
                if (!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
                    return Workflow.RC_PROTOCOL_ERROR;
                }
                if (!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
                    if (WeixinConstants.RESULT_ERROR_CODE_ORDER_CLOSED.equals(errCode)) {
                        return Workflow.RC_CANCEL_SUCCESS;
                    } else if (WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)) {
                        return Workflow.RC_RETRY;
                    } else if (WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(errCode)) {
                        // 支付请求返回“USERPAYING：用户支付中，需要输入密码”时，不再进行cancel操作，直接返回io异常，由勾兑服务进行处理
                        if (BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                            logger.warn("{}: provider cancel fail, return USERPAYING.", BeanUtil.getPropString(transaction, Transaction.TSN));
                            return Workflow.RC_IOEX;
                        } else {
                            return Workflow.RC_RETRY;
                        }
                    } else if (WeixinConstants.RESULT_ERROR_REVERSE_EXPIRE.equals(errCode)) {
                        //超出撤单时限， 查单，如果付款成功，则退款
                        Map queryResult = doQuery(context);
                        String queryReturnCode = (String) queryResult.get(ResponseFields.RETURN_CODE);//返回状态码
                        String queryResultCode = (String) queryResult.get(ResponseFields.RESULT_CODE);//业务结果
                        String tradeState = (String) queryResult.get(ResponseFields.TRADE_STATE);
                        if (WeixinConstants.RETURN_CODE_SUCCESS.equals(queryReturnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(queryResultCode)) {
                            if (WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
                                needRefund = true;
                            } else {
                                return Workflow.RC_CANCEL_SUCCESS;
                            }
                        } else {
                            return Workflow.RC_ERROR;
                        }
                    } else {
                        return Workflow.RC_ERROR;
                    }
                } else {
                    return Workflow.RC_CANCEL_SUCCESS;
            }
        }
        if(needRefund){
            String rcFlag = refund(context);
            if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
                return Workflow.RC_CANCEL_SUCCESS;
            }else{
                return rcFlag;
            }
        }
        return Workflow.RC_ERROR;
    }

    /**
     * 解析返回金额相关信息
     *
     * @param context
     */
    public static void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return;
        }

        //免充值升级后的接口返回与以前的不一样
        List<Map<String,Object>> promotions = null;
        if(result.get(ResponseFields.PROMOTION_DETAIL)!= null && result.get(ResponseFields.PROMOTION_DETAIL) instanceof String) {
            String promotionDetailStr = (String) result.get(ResponseFields.PROMOTION_DETAIL);
            promotionDetailStr = promotionDetailStr.replaceAll("\\\\", "");
            if(!StringUtils.isEmpty(promotionDetailStr)) {
                Map promotionDetails = JsonUtil.jsonStrToObject(promotionDetailStr, Map.class);
                promotions = (List<Map<String, Object>>) BeanUtil.getProperty(promotionDetails, ResponseFields.PROMOTION_DETAIL);
                result.remove(ResponseFields.PROMOTION_DETAIL);
                BeanUtil.setNestedProperty(result, ResponseFields.RESPONSE_KEY_PROMOTION_DETAIL, promotions);
            }
        }
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL) && promotions != null){
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            if(promotions != null){
                Map<String,Object> order = context.getOrder();
                Map<String, Object> transaction = context.getTransaction();

                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                if(payments == null || payments.isEmpty()){
                    extraOutFields.put(Transaction.PAYMENTS, WeixinServiceProvider.getWeixinPayments(result));
                }
                long discountAmount = WeixinServiceProvider.getSumAmountOfPromotionDetail(promotions);
                if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discountAmount != 0){
                    order.put(Order.TOTAL_DISCOUNT, discountAmount);
                    order.put(Order.NET_DISCOUNT, discountAmount);
                }
                long paidAmount = totalFee - discountAmount;
                if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0){
                    transaction.put(Transaction.PAID_AMOUNT, paidAmount);
                }
                long receiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for(Map<String,Object> promotion: promotions){
                    if(promotion.isEmpty()){
                        continue;
                    }
                    String type = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                    long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
//                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if(PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                        receiveAmount = receiveAmount - (amount - wxpayContribute -otherContribute);

                    }
                }
                if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
                }
            }
        }else{
            Map<String, Object> transaction = context.getTransaction();
            //判断并重置total_fee
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            if (totalFee <= NumberUtil.LONG_ZERO) {
                totalFee = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                result.put(ResponseFields.TOTAL_FEE, totalFee);
            }
            long discount = BeanUtil.getPropLong(result, ResponseFields.COUPON_FEE, 0);
            long cashFee = totalFee - discount;
            transaction.put(Transaction.PAID_AMOUNT, cashFee);
            if(totalFee > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, totalFee);
            }
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
            if(payments == null || payments.isEmpty()){
                extraOutFields.put(Transaction.PAYMENTS, WeixinServiceProvider.getWeixinPayments(result));
            }
            Map<String,Object> order = context.getOrder();
            if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discount != 0){
                order.put(Order.TOTAL_DISCOUNT, discount);
                order.put(Order.NET_DISCOUNT, discount);
            }
        }
    }

    /**
     * 解析退款返回金额信息
     * @param result
     * @param context
     */
    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        Map<String,Object> order = context.getOrder();
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME));
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            long paidAmount = BeanUtil.getPropLong(result, ResponseFields.CASH_REFUND_FEE);

            if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0 && paidAmount != 0){
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }

            if(payments != null){
                List<Map<String,Object>> refundPayments = new ArrayList<>();
                for (int i = 0; i < payments.size(); i++) {
                    Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                    refundPayments.add(refundPayment);
                }

                //直连微信 会返回 coupon_refund_count coupon_xxx_$n 等字段, 但是网联银联不会返回，而是返回refund_details字段
                List<Map<String,Object>> refundDetail = (List<Map<String, Object>>) result.get(ResponseFields.REFUND_DETAILS);
                if(refundDetail != null){
                    for (int i = 0; i < refundDetail.size(); i++) {
                        Map<String,Object> detail = refundDetail.get(i);
                        String couponRefundId = BeanUtil.getPropString(detail, ResponseFields.REFUND_DETAILS_PROMOTION_ID);
                        long couponRefundFee = BeanUtil.getPropLong(detail, ResponseFields.REFUND_DETAILS_REFUND_AMOUNT);
                        for (int j = 0; j < refundPayments.size(); j++) {
                            Map<String,Object> refundPayment = refundPayments.get(j);
                            String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                            if(sourceId.equals(couponRefundId)){
                                refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                            }
                        }
                    }
                }

                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(!Payment.TYPE_DISCOUNT_SET.contains(type)){
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, paidAmount);
                        break;
                    }
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                long settlementRefundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                        settlementRefundFee = settlementRefundFee - BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT);
                    }
                }
                if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefundFee != 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefundFee);
                }
            }
        }
    }

    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> config = getTradeParams(transaction);
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        String content = MapUtil.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);

        String timeEnd = MapUtil.getString(respContent, ResponseFields.TIME_END);
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String subOpenId = MapUtil.getString(respContent, ResponseFields.SUB_OPEN_ID);
            if (!StringUtil.empty(subOpenId)) {
                transaction.put(Transaction.BUYER_UID, subOpenId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String openId = MapUtil.getString(respContent, ResponseFields.OPEN_ID);
            if (!StringUtil.empty(openId)) {
                transaction.put(Transaction.BUYER_LOGIN, openId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(result, PSBCResponseFields.ORDER_NO);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }

        if (StringUtil.empty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String channelTradeNo = MapUtil.getString(respContent, ResponseFields.TRANSACTION_ID);
            if (!StringUtil.empty(channelTradeNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
            }
        }

        if (StringUtil.empty(MapUtil.getString(extraOutFields, Transaction.WEIXIN_APPID))) {
            extraOutFields.put(Transaction.WEIXIN_APPID, MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID));
        }

        if (!StringUtil.empty(timeEnd)) {
            if (StringUtil.empty(MapUtil.getString(transaction, Transaction.CHANNEL_FINISH_TIME))) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(timeEnd));
            }
        }

    }

    /**
     * alipay 公共请求参数
     * @param context
     */
    private PSBCRequestBuilder getDefaultWeixinRequestBuilder(PSBCRequestBuilder builder, TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        builder.bizSet(ProtocolFields.APP_ID, "");
        builder.bizSet(ProtocolFields.MCH_ID, "");
        builder.bizSet(ProtocolFields.SUB_APP_ID, BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_WEIXIN_SUB_APP_ID));
        //todo sub Merchant id
        builder.bizSet(ProtocolFields.SUB_MCH_ID, BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_WEIXIN_SUB_MCH_ID));
        builder.bizSet(ProtocolFields.CHANNEL_ID, "");
        builder.bizSet(ProtocolFields.DEVICE_INFO, "");
        builder.bizSet(ProtocolFields.NONCE_STR, "");
        builder.bizSet(ProtocolFields.SIGN_TYPE, "");
        builder.bizSet(ProtocolFields.CERT_ID, "");
        //小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_MINI == subPayway){
            String miniSubAppId = BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            if(!StringUtil.empty(miniSubAppId)){
                builder.bizSet(ProtocolFields.SUB_APP_ID, miniSubAppId);
            }
        }

        return builder;
    }
}
