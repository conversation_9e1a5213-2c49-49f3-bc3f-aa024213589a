package com.wosai.upay.workflow;

import com.google.common.collect.Maps;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.prepaid.api.PrepaidBusinessQueryService;
import com.wosai.upay.prepaid.api.PrepaidVerificationService;
import com.wosai.upay.prepaid.api.enums.BusinessSceneEnum;
import com.wosai.upay.prepaid.api.enums.TradeStateEnum;
import com.wosai.upay.prepaid.api.exception.UpayPrepaidBizException;
import com.wosai.upay.prepaid.api.request.*;
import com.wosai.upay.prepaid.api.result.*;
import com.wosai.upay.prepaid.api.result.base.BaseTradeResult;
import com.wosai.upay.util.FakeRequestUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.*;

/**
 * <AUTHOR> Date: 2021/6/28 Time: 9:33 上午
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE -1)
public class PrepaidCardProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(PrepaidCardProvider.class);
    private static final String TIMEOUT_RETURN_MSG = "Caught error with no response body";
    public static final String NAME = "provider.prepaid.card";
    private static final String BIZ_EXT_OPERATOR = "operator";

    private String notifyHost;

    @Autowired
    @Qualifier("prepaidVerificationService")
    private PrepaidVerificationService prepaidVerificationService;
    @Autowired
    @Qualifier("fakePrepaidVerificationService")
    private PrepaidVerificationService fakePrepaidVerificationService;
    @Autowired
    @Qualifier("prepaidBusinessQueryService")
    private PrepaidBusinessQueryService prepaidBusinessQueryService;
    @Autowired
    @Qualifier("fakePrepaidBusinessQueryService")
    private PrepaidBusinessQueryService fakePrepaidBusinessQueryService;

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_PREPAID_CARD && (subPayway == Order.SUB_PAYWAY_BARCODE
                || subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.PREPAID_CARD_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        VerificationResult result;
        try {
            result = verification(transaction);
            logger.info("[储值核销]>>>>>>出参: {}", result);
        } catch (UpayPrepaidBizException e) {
            logger.error("call prepaid-card pay biz err", e);
            setTransactionContextErrorInfo(null, e, context, OP_PAY);
            return Workflow.RC_TRADE_CANCELED;
        } catch (Exception e) {
            logger.error("failed to call prepaid-card pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IN_PROG;
        }

        setTransactionContextErrorInfo(result, null, context, OP_PAY);
        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        return cancelToRefundProcess(context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        String merchantId = MapUtil.getString(configSnapShot, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(configSnapShot, TransactionParam.STORE_ID);
        String sn = MapUtil.getString(transaction, Transaction.TSN);
        long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);

        TradeStateQueryResult result;
        try {
            result = queryTradeState(merchantId, storeId, sn, ctime);
            logger.info("[储值查单]>>>>>>出参: {}", result);
        } catch (Exception e) {
            logger.error("failed to call prepaid-card query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IN_PROG;
        }

        return buildQueryResult(result, context);
    }



    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        VerificationRefundResult result;
        try {
            result = verificationRefund(transaction);
            logger.info("[储值核销退款]>>>>>>出参: {}", result);
        } catch (UpayPrepaidBizException e) {
            logger.error("failed to call prepaid-card refund", e);
            setTransactionContextErrorInfo(null, e, context, OP_PAY);
            return Workflow.RC_ERROR;
        } catch (Exception e) {
            logger.error("failed to call prepaid-card refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, null, context, OP_REFUND);
        return buildRefundResult(result, context);
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        PreOrderResult result;
        try {
            result = preOrder(context, transaction);
            logger.info("[储值预下单]>>>>>>出参: {}", result);
        } catch (UpayPrepaidBizException e) {
            logger.error("call prepaid-card precreate biz err", e);
            setTransactionContextErrorInfo(null, e, context, OP_PRECREATE);
            return Workflow.RC_TRADE_CANCELED;
        } catch (Exception e) {
            logger.error("failed to call prepaid-card precreate", e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }

        return buildPrepaidResult(context, result);
    }

    private String buildPrepaidResult(TransactionContext context, PreOrderResult result) {
        if (Objects.isNull(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, result);
        return Workflow.RC_CREATE_SUCCESS;
    }

    private String cancelToRefundProcess(TransactionContext context) {
        String result = refund(context);
        if (Workflow.RC_REFUND_SUCCESS.equals(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        return result;
    }

    private String buildRefundResult(VerificationRefundResult result, TransactionContext context) {
        if (Objects.isNull(result)) {
            return Workflow.RC_IOEX;
        }
        resolveRefundFund(context.getOrder(), context.getTransaction(), result);
        return Workflow.RC_REFUND_SUCCESS;
    }

    private void resolveRefundFund(Map<String, Object> order, Map<String, Object> transaction, VerificationRefundResult result){
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME));
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款，根据返回的BaseTradeResult.ChangedAmount来计算
            long transactionAmount = result.getChangedAmount().getTotalAmount();
            long transactionRechargeAmount = result.getChangedAmount().getRechargeAmount();
            long transactionGiftAmount = result.getChangedAmount().getGiftAmount();
            transaction.put(Transaction.PAID_AMOUNT, transactionAmount);
            transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);

            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            if (CollectionUtils.isEmpty(payments)) {
                return;
            }
            List<Map<String,Object>> refundPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            if (refundPayments == null) {
                refundPayments = new ArrayList<>();
            }

            if (transactionRechargeAmount > 0) {
                refundPayments.add(MapUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, transactionRechargeAmount,
                        Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_SQB_PREPAID_CARD_RECHARGE,
                        Transaction.PAYMENT_TYPE, Payment.TYPE_SQB_PREPAID_CARD));
            }
            if (transactionGiftAmount > 0) {
                refundPayments.add(MapUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, transactionGiftAmount,
                        Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_SQB_PREPAID_CARD_GIFT,
                        Transaction.PAYMENT_TYPE, Payment.TYPE_SQB_PREPAID_CARD));
            }

            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
        }
    }

    private String buildQueryResult(TradeStateQueryResult result, TransactionContext context) {
        if (Objects.isNull(result)) {
            return Workflow.RC_IOEX;
        }
        setTradeNoBuyerInfoIfExists(result, context);
        if (Objects.equals(result.getTradeState(), TradeStateEnum.SUCCESS)) {
            resolveTradeFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (Objects.equals(result.getTradeState(), TradeStateEnum.UNKNOWN)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_TRADE_CANCELED;
    }

    private void resolveTradeFund(BaseTradeResult result, TransactionContext context) {
        if (Objects.isNull(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments  = new ArrayList<>();
        }

        //交易金额
        long transactionAmount = result.getChangedAmount().getTotalAmount();
        long transactionRechargeAmount = result.getChangedAmount().getRechargeAmount();
        long transactionGiftAmount = result.getChangedAmount().getGiftAmount();
        transaction.put(Transaction.PAID_AMOUNT, transactionAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);

        if (transactionRechargeAmount > 0) {
            payments.add(MapUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, transactionRechargeAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_SQB_PREPAID_CARD_RECHARGE,
                    Transaction.PAYMENT_TYPE, Payment.TYPE_SQB_PREPAID_CARD));
        }
        if (transactionGiftAmount > 0) {
            payments.add(MapUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, transactionGiftAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_SQB_PREPAID_CARD_GIFT,
                    Transaction.PAYMENT_TYPE, Payment.TYPE_SQB_PREPAID_CARD));
        }

        extraOutFields.put(Transaction.PAYMENTS, payments);
    }

    private String buildPayResult(VerificationResult result, TransactionContext context) {
        if (Objects.isNull(result)) {
            return Workflow.RC_IOEX;
        }
        setTradeNoBuyerInfoIfExists(result, context);
        resolveTradeFund(result, context);
        return Workflow.RC_PAY_SUCCESS;
    }

    private void setTradeNoBuyerInfoIfExists(BaseTradeResult baseTradeResult, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        BaseTradeResult.TradeMember tradeMember = baseTradeResult.getTradeMember();
        if (Objects.nonNull(tradeMember)) {
            if(StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
                transaction.put(Transaction.BUYER_UID, tradeMember.getSqbMemberId());
            }
            if(StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
                transaction.put(Transaction.BUYER_LOGIN, tradeMember.getMemberId());
            }
            if(StringUtils.isEmpty(MapUtil.getString(order, Order.BUYER_UID))) {
                order.put(Order.BUYER_UID, tradeMember.getSqbMemberId());
            }
            if (StringUtils.isEmpty(MapUtil.getString(order, Order.BUYER_LOGIN))) {
                order.put(Order.BUYER_LOGIN, tradeMember.getMemberId());
            }
        }

        Long statementId = baseTradeResult.getStatementId();
        if(Objects.nonNull(statementId)){
            if(StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, statementId);
            }
            if(StringUtils.isEmpty(MapUtil.getString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, statementId);
            }
        }
    }


    private void setTransactionContextErrorInfo(Object result, UpayPrepaidBizException e, TransactionContext context, String key) {
        String terminalCategory = MapUtil.getString(MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        boolean isMergeMessage = StringUtil.empty(terminalCategory);
        if (Objects.nonNull(result)) {
            // 支付通道返回成功，清空原先设置的UpayBizError
            Map bizErrorCode = (Map) BeanUtil.getProperty(context.getTransaction(), Transaction.BIZ_ERROR_CODE);
            if (null != bizErrorCode && bizErrorCode.containsKey(key)){
                bizErrorCode.remove(key);
            }
        }
        if (Objects.nonNull(e)) {
            String errCode = e.getCode();
            String errMsg = e.getMessage();
            UpayBizError bizError = UpayBizError.getBizErrorByField(key, BeanUtil.getPropInt(context.getTransaction(), Transaction.PAYWAY), errCode, errMsg);

            String path = UpayUtil.getBizErrorCodeKey(key);
            if (bizError != null && !UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(bizError.getStandardName())){
                BeanUtil.setNestedProperty(context.getTransaction(), path, bizError);
            } else {
                BeanUtil.setNestedProperty(context.getTransaction(), path,
                        UpayBizError.unexpectedProviderError(StringUtil.empty(errMsg)
                                ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : errMsg, isMergeMessage));
            }
        }
    }

    private VerificationResult verification(Map<String, Object> transaction) {
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil
                .getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extraParams = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_PARAMS);
        String merchantId = MapUtil.getString(configSnapShot, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(configSnapShot, TransactionParam.STORE_ID);
        String terminalSn = MapUtil.getString(configSnapShot, TransactionParam.TERMINAL_SN);
        Long amount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        String sn = MapUtil.getString(transaction, Transaction.TSN);
        String barcode = MapUtil.getString(extraParams, Transaction.BARCODE);
        String subject = getNewSubject(transaction);
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);

        VerificationRequest request = new VerificationRequest();
        request.setMerchantId(merchantId);
        request.setStoreId(storeId);
        request.setTerminalSn(terminalSn);
        request.setAmount(amount);
        request.setBusinessScene(genBusinessSceneEnum(productFlag));
        request.setSn(sn);
        request.setPaymentCode(barcode);
        request.setSubject(subject);
        request.setBizExt(genBizExt(transaction));
        logger.info("[储值核销]>>>>>>入参: {}", request);
        return getPrepaidVerificationService().verification(request);
    }

    private VerificationRefundResult verificationRefund(Map<String, Object> transaction) {
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil
                .getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        String merchantId = MapUtil.getString(configSnapShot, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(configSnapShot, TransactionParam.STORE_ID);
        String terminalSn = MapUtil.getString(configSnapShot, TransactionParam.TERMINAL_SN);
        String refundSn = MapUtil.getString(transaction, Transaction.TSN);
        String oriSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        Long refundAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        String operator = MapUtil.getString(transaction, Transaction.OPERATOR);


        VerificationRefundRequest request = new VerificationRefundRequest();
        request.setMerchantId(merchantId);
        request.setStoreId(storeId);
        request.setTerminalSn(terminalSn);
        request.setSn(refundSn);
        request.setOriSn(oriSn);
        request.setRefundAmount(refundAmount);
        request.setBizExt(genBizExt(transaction));
        logger.info("[储值核销退款]>>>>>>入参: {}", request);
        return getPrepaidVerificationService().verificationRefund(request);
    }

    private TradeStateQueryResult queryTradeState(String merchantId, String storeId, String sn, long ctime) {
        TradeStateQueryRequest request = new TradeStateQueryRequest();
        request.setMerchantId(merchantId);
        request.setStoreId(storeId);
        request.setSn(sn);
        request.setTradeTime(ctime);
        logger.info("[储值查单]>>>>>>入参: {}", request);
        return getPrepaidBusinessQueryService().queryTradeState(request);
    }

    private PreOrderResult preOrder(TransactionContext context, Map<String, Object> transaction) {
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        String merchantId = MapUtil.getString(configSnapShot, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(configSnapShot, TransactionParam.STORE_ID);
        String terminalSn = MapUtil.getString(configSnapShot, TransactionParam.TERMINAL_SN);
        String buyerId = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        Long amount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        String sn = MapUtil.getString(transaction, Transaction.TSN);
        String subject = getNewSubject(transaction);
        String notifyUrl = getNotifyUrl(notifyHost, MpayServiceProvider.NOTIFY_PREPAID, context);
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);

        PreOrderRequest request = new PreOrderRequest();
        request.setMerchantId(merchantId);
        request.setStoreId(storeId);
        request.setTerminalSn(terminalSn);
        request.setMemberId(buyerId);
        request.setSn(sn);
        request.setAmount(amount);
        request.setBusinessScene(genBusinessSceneEnum(productFlag));
        request.setSubject(subject);
        request.setValidMinutes(DEFAULT_TIME_EXPIRE_MINUTE);
        request.setNotifyUrl(notifyUrl);
        request.setBizExt(genBizExt(transaction));
        logger.info("[储值预下单]>>>>>>入参: {}", request);
        return getPrepaidVerificationService().preOrder(request);
    }

    private BusinessSceneEnum genBusinessSceneEnum(String productFlag) {
        BusinessSceneEnum businessScene;
        if (org.apache.commons.lang3.StringUtils.isEmpty(productFlag)) {
            businessScene = BusinessSceneEnum.OFFLINE_PAID;
        } else if (productFlag.contains(ProductFlagEnum.UFOOD.getCode())) {
            businessScene = BusinessSceneEnum.SCAN_CODE_ORDER;
        } else if (productFlag.contains(ProductFlagEnum.JJZ_WM.getCode())
                || productFlag.contains(ProductFlagEnum.JJZ_ZQ.getCode())) {
            businessScene = BusinessSceneEnum.SELF_EMPLOYED_TAKEAWAY;
        } else {
            businessScene = BusinessSceneEnum.OFFLINE_PAID;
        }
        return businessScene;
    }

    @SneakyThrows
    private String genBizExt(Map<String, Object> transaction) {
        Map<String, Object> bizExt = Maps.newHashMap();
        String operator = MapUtil.getString(transaction, Transaction.OPERATOR);
        if (StringUtils.isNotEmpty(operator)) {
            bizExt.put(BIZ_EXT_OPERATOR, operator);
        }
        if (MapUtil.isNotEmpty(bizExt)) {
            return JsonUtil.objectToJsonString(bizExt);
        }
        return null;
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    public PrepaidVerificationService getPrepaidVerificationService() {
        return FakeRequestUtil.isFakeRequest() ? fakePrepaidVerificationService : prepaidVerificationService;
    }

    public PrepaidBusinessQueryService getPrepaidBusinessQueryService() {
        return FakeRequestUtil.isFakeRequest() ? fakePrepaidBusinessQueryService : prepaidBusinessQueryService;
    }

    /**
     * 储值底层最大只支持32个字符
     * @param transaction
     * @return
     */
    private String getNewSubject(Map<String,Object> transaction){
        String subject = MapUtil.getString(transaction, Transaction.SUBJECT);
        return subject.length() > 32 ? subject.substring(0, 32) : subject;
    }
}
