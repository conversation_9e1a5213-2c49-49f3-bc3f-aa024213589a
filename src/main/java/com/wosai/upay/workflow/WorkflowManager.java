package com.wosai.upay.workflow;

import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.fsm.StateLabel;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.mpay.util.Digest;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayClientException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

public class WorkflowManager {
    @Autowired
    private WorkflowDriver driver;
    private List<MpayServiceProvider>[] providerGroups; 
    private Map<String, MpayServiceProvider> providers;
    private List<Workflow> workflows;

    private Map<String, TransactionContext> runningContexts;
    
    @Resource(name = "asyncThreadPool")
    private ScheduledExecutorService executor;
    
    public WorkflowManager() {
        this.providerGroups = new ArrayList[UpayConstant.DEFAULT_PROVIDER_GROUPS_SIZE];
        this.providers = new HashMap<String, MpayServiceProvider>();
        this.workflows = new ArrayList<Workflow>();
        this.runningContexts = new ConcurrentHashMap<String, TransactionContext>();
    }

    public void addWorkflow(Workflow workflow) {
        workflows.add(workflow);
        workflows.sort((p1, p2) -> {
            WorkflowPriority spp1 = p1.getClass().getAnnotation(WorkflowPriority.class);
            WorkflowPriority spp2 = p2.getClass().getAnnotation(WorkflowPriority.class);

            int spp1Priority = (null != spp1) ? spp1.priority() : Integer.MAX_VALUE;
            int spp2Priority = (null != spp2) ? spp2.priority() : Integer.MAX_VALUE;
            return spp1Priority < spp2Priority ? -1 : 1;
        });
    }

    public void addServiceProvider(MpayServiceProvider provider) {
        // 威富通渠道是个通用通道，通道号在交易时才能指定
        if (!(provider instanceof DefaultSwiftPassServiceProvider)) {
            int idx = (null == provider.getProvider()) ? UpayConstant.DIRECT_PROVIDER_GROUPS_INDEX : provider.getProvider() % UpayConstant.PROVIDER_GROUPS_INDEX_BASE;
            providerGroupsResize(idx);
            List<MpayServiceProvider> providerGroup = providerGroups[idx];
            if (providerGroup == null) {
                providerGroup = new ArrayList<MpayServiceProvider>();
                providerGroups[idx] = providerGroup;
            }
            providerGroup.add(provider);
            providerGroup.sort((p1, p2) -> {
                ServiceProvicerPriority spp1 = p1.getClass().getAnnotation(ServiceProvicerPriority.class);
                ServiceProvicerPriority spp2 = p2.getClass().getAnnotation(ServiceProvicerPriority.class);
    
                int spp1Priority = (null != spp1) ? spp1.priority() : Integer.MAX_VALUE;
                int spp2Priority = (null != spp2) ? spp2.priority() : Integer.MAX_VALUE;
                return spp1Priority < spp2Priority ? -1 : 1;
            });
        }
        providers.put(provider.getName(), provider);
    }

    public void setDriver(WorkflowDriver driver) {
        this.driver = driver;
    }

    public String upayOrderNumber(TransactionContext context) {
        return upayOrderNumber(context.getTerminalOrStoreSn(), MapUtil.getString(context.getOrder(), Order.CLIENT_SN));
    }
    
    public String upayOrderNumber(String terminalOrStoreSn, String clientSn) {
        if (terminalOrStoreSn != null) {
            try {
                return Digest.md5(StringUtils.join(terminalOrStoreSn, "-", clientSn).getBytes());
            } catch (NoSuchAlgorithmException e) {
                return null;
            }
        }else{
            return null;
        }
    }
    public TransactionContext lookupTransactionContext(String terminalOrStoreSn, String clientSn) {
        return runningContexts.get(upayOrderNumber(terminalOrStoreSn, clientSn));
    }
    
    public TransactionContext lookupTransactionContext(String key) {
        return runningContexts.get(key);
    }
    public TransactionContext lookupTransactionContext(Map<String, Object> transaction) {
        String oid = BeanUtil.getPropString(transaction, Transaction.ORDER_ID);
        return runningContexts.get(oid);
    }
    public void removeTransactionContext(TransactionContext context) {
        String oid = context.getOid();
        runningContexts.remove(oid);
        if (context.getTerminalOrStoreSn()!=null) {
            runningContexts.remove(upayOrderNumber(context));
        }
    }
    public TransactionContext createTransactionContext(String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction) {
        return createTransactionContext(terminalOrStoreSn, order, transaction, null);
    }

    public TransactionContext spawnTransactionContext(String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction) {
        return spawnTransactionContext(terminalOrStoreSn, order, transaction, null);
    }

    public TransactionContext startWorkflow(String apiVer, String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction) {
        return startWorkflow(apiVer, terminalOrStoreSn, order, transaction, (MpayServiceProvider)null);
    }
    
    public TransactionContext startWorkflow(String apiVer, String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction, MpayServiceProvider serviceProvider) {
        TransactionContext context = spawnTransactionContext(terminalOrStoreSn, order, transaction, serviceProvider);
        if (apiVer != null) {
            try {
                int apiVersion = Integer.parseInt(apiVer);
                context.setApiVer(apiVersion);
            }catch (NumberFormatException ex) {
            }
        }
        driver.start(context);
        return context;
    }

    /**
     * 
     * 开始下单任务，此接口默认5秒后返回执行结果
     * 
     * @param apiVer
     * @param terminalOrStoreSn
     * @param order
     * @param transaction
     * @param returnConsumer
     * @return
     */
    public TransactionContext startWorkflow(String apiVer, String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction, Consumer returnConsumer) {
        return startWorkflow(apiVer, terminalOrStoreSn, order, transaction, null, returnConsumer);
    }
    
    public TransactionContext startWorkflow(String apiVer, String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction, MpayServiceProvider serviceProvider, Consumer returnConsumer) {
        TransactionContext context = spawnTransactionContext(terminalOrStoreSn, order, transaction, serviceProvider);
        context.setReturnConsumer(returnConsumer);
        // 设置5秒超时返回的任务，如果响应在5秒内返回，需要将此任务撤销掉
        context.setTimeoutSchedule(executor.schedule(() -> returnConsumer.accept(context), 5000, TimeUnit.MILLISECONDS));
        if (apiVer != null) {
            try {
                int apiVersion = Integer.parseInt(apiVer);
                context.setApiVer(apiVersion);
            }catch (NumberFormatException ex) {
            }
        }
        driver.start(context);
        return context;
    }
    
    public TransactionContext spawnTransactionContext(String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction, MpayServiceProvider serviceProvider) {
        TransactionContext context = createTransactionContext(terminalOrStoreSn, order, transaction, serviceProvider);
        runningContexts.put(context.getOid(), context);
        if (terminalOrStoreSn != null) {
            runningContexts.put(upayOrderNumber(context), context);
        }
        return context;
    }
    
    public TransactionContext createTransactionContext(String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction, MpayServiceProvider provider) {
        Workflow workflow = matchWorkflow(transaction);
        if (workflow == null) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_FAIL_NO_AUTH, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_FAIL_NO_AUTH_MESSAGE);
        }
        provider = (null == provider) ? matchServiceProvider(transaction) : provider;
        if (provider == null) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE);
        }
        String oid = MapUtil.getString(transaction, Transaction.ORDER_ID);
        String tid = MapUtil.getString(transaction, DaoConstants.ID);
        int stateId = MapUtil.getIntValue(transaction, Transaction.STATUS);
        StateLabel initialStateLabel = StateLabel.fromId(stateId);
        
        TransactionContext context = new TransactionContext(terminalOrStoreSn, workflow, provider, initialStateLabel, oid, order, tid, transaction);

        return context;
    }
    
    public void resumeWorkflow(String terminalOrStoreSn, Map<String, Object> order, Map<String, Object> transaction) {
        TransactionContext context = spawnTransactionContext(terminalOrStoreSn, order, transaction);
        driver.resume(context);
    }
    

    public void raise(TransactionContext context, String result) {
        driver.raise(context, result);
    }
    public Workflow matchWorkflow(Map<String, Object> transaction) {
        for (Workflow workflow: workflows) {
            if (workflow.canHandle(transaction)) {
                return workflow;
            }
        }
        return null;
    }

    public MpayServiceProvider matchServiceProvider(Map<String, Object> transaction) {
        boolean matchProvider = true;
        Integer provider = null;
        if (transaction.containsKey(Transaction.PROVIDER)) {
            provider = MapUtil.getInteger(transaction, TransactionParam.PROVIDER);
        } else {
            Map<String, Object> configSnapshot = (Map)transaction.get(Transaction.CONFIG_SNAPSHOT);
            if (configSnapshot.containsKey(Transaction.PROVIDER)) {
                provider = MapUtil.getInteger(configSnapshot, TransactionParam.PROVIDER);
            } else {
                matchProvider = false;
            }
        }
        if (matchProvider) {
            int idx = (provider == null) ? UpayConstant.DIRECT_PROVIDER_GROUPS_INDEX : provider % UpayConstant.PROVIDER_GROUPS_INDEX_BASE;
            List<MpayServiceProvider> providerGroup = providerGroups[idx];
            if (providerGroup != null) {
                for (MpayServiceProvider sp : providerGroup) {
                    if (sp.canHandle(transaction)) {
                        return sp;
                    }
                }
            } else {
                // 威富通渠道未在代码中指定，需要交易时动态添加
                DefaultSwiftPassServiceProvider dspp = getServiceProvider(DefaultSwiftPassServiceProvider.class);
                if (dspp.canHandle(transaction)) {
                    Integer swiftProvider = dspp.getProvider(transaction);
                    if (swiftProvider == null) {
                        return null;
                    }
                    synchronized (Integer.toString(swiftProvider).intern()) {
                        idx = swiftProvider % UpayConstant.PROVIDER_GROUPS_INDEX_BASE;
                        providerGroupsResize(idx);
                        if (providerGroups[idx] == null) {
                            providerGroups[idx] = Arrays.asList(dspp);
                        }
                    }
                    transaction.put(TransactionParam.PROVIDER, swiftProvider);
                    return dspp;
                }
            }
        }
        for (MpayServiceProvider mp : providers.values()) {
            if (mp.canHandle(transaction)) {
                return mp;
            }
        }
        return null;
    }

    public <T> T getServiceProvider(Class<T> c){
        for (MpayServiceProvider provider: providers.values()){
            if(provider.getClass()==c){
                return (T)provider;
            }
        }
        return null;
    }

    // 交易通道数组扩容
    private void providerGroupsResize(int idx) {
        // 多扩容10位是为了确保动态获取威富通通道时不报数组越界
        if (providerGroups.length - 10 <= idx) {
            List<MpayServiceProvider> [] newProviderGroups = new List[idx + 11];
            System.arraycopy(providerGroups, 0, newProviderGroups, 0, providerGroups.length);
            providerGroups = newProviderGroups;
        }
    }
}
