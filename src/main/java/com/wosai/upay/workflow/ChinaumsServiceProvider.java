package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.chinaums.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 *  银商支付通道v2版本（仅支持BSC）
 * <AUTHOR> Date: 2019/4/28 Time: 9:33 AM
 */
public class ChinaumsServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(ChinaumsServiceProvider.class);

    public static final String NAME = "provider.chinaums";

    @Autowired
    private ChinaumsClient client;

    public ChinaumsServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TRANSACTION_AMOUNT, BusinessFields.TRANSACTION_CURRENCY_CODE));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if (Objects.isNull(tradeParams)) {
            return false;
        }
        boolean isVersionOne = Objects.equals(ChinaumsV1ServiceProvider.TRADE_PARAMS_SQB_VERSION_1, MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_SQB_VERSION));
        if(isVersionOne){
            return false;
        }
        return UpayUtil.isFormalByTradeParams(tradeParams) && MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CHINAUMS_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_CHINAUMS;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams);
        builder.set(BusinessFields.TRANSACTION_AMOUNT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.TRANSACTION_CURRENCY_CODE, ChinaumsConstants.TRANSACTION_CURRENCY_CODE);
        builder.set(BusinessFields.MERCHANT_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.MERCHANT_REMARK, buildMerchantRemark(BeanUtil.getPropString(transaction, Transaction.SUBJECT)));
        builder.set(BusinessFields.PAY_MODE, ChinaumsConstants.PayModeEnum.CODE_SCAN);
        builder.set(BusinessFields.PAY_CODE, extraParams.get(Transaction.BARCODE));
        //259终端信息上送
        setTerminalInfo(context, builder);
        limitCredit(builder, transaction);
        carryOverExtendedParams(extendedParams, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_PAY);
        } catch (Exception e) {
            logger.error("failed to call chinaums pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams);
        builder.set(BusinessFields.MERCHANT_ORDER_ID, transaction.get(Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 3, OP_CANCEL);
        } catch (Exception e) {
            logger.error("failed to call chinaums cancel", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams);
        builder.set(BusinessFields.MERCHANT_ORDER_ID, transaction.get(Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 3, OP_QUERY);
        } catch (Exception e) {
            logger.error("failed to call chinaums query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams);
        builder.set(BusinessFields.MERCHANT_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.REFUND_REQUEST_ID, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.TRANSACTION_AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call chinaums refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context);
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String respCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        String respMsg = BeanUtil.getPropString(result, ResponseFields.ERR_INFO);
        map.put(ResponseFields.ERR_CODE, respCode);
        map.put(ResponseFields.ERR_INFO, respMsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, Objects.equals(respCode, ChinaumsConstants.RESP_CODE_BUSINESS_ACCEPTANCE_SUCCESS), respCode, respMsg);
    }

    private Map<String, Object> retryIfNetworkException(String url, Map<String,Object> request, String appId, String appKey
            , int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                // 银商bsc接口是同步的，只到有明确接口后才会返回，将次接口改为2秒超时
                if(OP_PAY.equals(opFlag)) {
                    return client.call(url, appId, appKey, request, 4000);
                }else {
                    return client.call(url, appId, appKey, request);
                }
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in chinaums {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        //明确成功
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_BUSINESS_ACCEPTANCE_SUCCESS)) {
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        //明确失败
        if (ChinaumsConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_IN_PROG;
    }

    private String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        String responseMsg = BeanUtil.getPropString(result, ResponseFields.ERR_INFO);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_BUSINESS_ACCEPTANCE_SUCCESS)
                || (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_ORI_TRADE_NOT_FOUND_22)
                        && (Objects.equals(responseMsg, ChinaumsConstants.RESP_MSG_CANCELED) || Objects.equals(responseMsg, ChinaumsConstants.RESP_MSG_ORDER_NOT_EXISTS)))) {

            return Workflow.RC_CANCEL_SUCCESS;
        }
        if (ChinaumsConstants.RESP_CODE_ER.equals(responseCode) && ChinaumsConstants.RESP_MSG_NOT_SUPPORT_CANCEL.equals(responseMsg)) {
            return Workflow.RC_REFUND_SUCCESS.equals(refund(context)) ? Workflow.RC_CANCEL_SUCCESS : Workflow.RC_ERROR;
        }
        if (ChinaumsConstants.CANCEL_RESP_CODE_RETRY_SET.contains(responseCode)) {
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String responseCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_BUSINESS_ACCEPTANCE_SUCCESS)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
        if (!Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_BUSINESS_ACCEPTANCE_SUCCESS)) {
            return Workflow.RC_IOEX;
        }

        setTradeNoBuyerInfoIfExists(result, context);
        String queryResCode = BeanUtil.getPropString(result, ResponseFields.QUERY_RES_CODE, StringUtils.EMPTY);
        switch (queryResCode) {
            //支付成功
            case ChinaumsConstants.PAY_QUERY_RES_CODE_SUCCESS:
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            //处理中，状态未知
            case ChinaumsConstants.PAY_QUERY_RES_CODE_UNKNOWN:
                return Workflow.RC_IN_PROG;
            //交易取消
            case ChinaumsConstants.PAY_QUERY_RES_CODE_CANCELED:
            case ChinaumsConstants.PAY_QUERY_RES_CODE_REFUNDED:
            case ChinaumsConstants.PAY_QUERY_RES_CODE_REVERSED:
            case ChinaumsConstants.PAY_QUERY_RES_CODE_FAIL:
                return Workflow.RC_TRADE_CANCELED;
            default:
                return Workflow.RC_IN_PROG;
        }

    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments  = new ArrayList<>();
        }

        extraOutFields.put(Transaction.CHANNEL_TRADE_NO, BeanUtil.getPropString(result, ResponseFields.THIRD_PARTY_ORDER_ID));

        //交易金额
        long transactionAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        //用户实付金额
        long amount = BeanUtil.getPropLong(result, ResponseFields.AMOUNT, BeanUtil.getPropLong(result, ResponseFields.ACTUAL_TRANSACTION_AMOUNT));
        //折扣金额
        long discountAmount = transactionAmount - amount;
        //折扣金额大于0，记录优惠信息
        if (discountAmount > 0) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, discountAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, null,
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL));
        }

        transaction.put(Transaction.PAID_AMOUNT, amount);
        transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);

        String cardAttr = (BeanUtil.getPropString(result, ResponseFields.CARD_ATTR));
        ChinaumsConstants.CardAttrEnum cardAttrEnum
                = ChinaumsConstants.CardAttrEnum.queryByCode(cardAttr);

        String paymentType = Payment.TYPE_BANKCARD_DEBIT;
        if (Objects.nonNull(cardAttrEnum)) {
            switch (cardAttrEnum) {
                case DEBIT_CARD:
                    paymentType = Payment.TYPE_BANKCARD_DEBIT;
                    break;
                case CREDIT_CARD:
                    paymentType = Payment.TYPE_BANKCARD_CREDIT;
                    break;
                case WALLET_BALANCE:
                    paymentType = Payment.TYPE_BANKCARD_DEBIT;
                    logger.warn("chinaums response field card_attr is  wallet_balance");
                    break;
                default:
                    paymentType = Payment.TYPE_BANKCARD_DEBIT;
            }
        }
        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, amount,
                Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                Transaction.PAYMENT_TYPE, paymentType));

        extraOutFields.put(Transaction.PAYMENTS, payments);
    }


    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String thirdPartyBuyerId = MapUtil.getString(result, ResponseFields.THIRD_PARTY_BUYER_ID);
            if (payway == Order.PAYWAY_UNIONPAY) {
                thirdPartyBuyerId = MapUtil.getString(result, ResponseFields.BANK_CARD_NO);
            }
            if (!StringUtil.empty(thirdPartyBuyerId)) {
                transaction.put(Transaction.BUYER_UID, thirdPartyBuyerId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String thirdPartyBuyerUserName = MapUtil.getString(result, ResponseFields.THIRD_PARTY_BUYER_USER_NAME);
            if (!StringUtil.empty(thirdPartyBuyerUserName)) {
                transaction.put(Transaction.BUYER_LOGIN, thirdPartyBuyerUserName);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String orderId = MapUtil.getString(result, ResponseFields.ORDER_ID);
            if(!StringUtil.empty(orderId)){
                transaction.put(Transaction.TRADE_NO, orderId);
            }
        }
    }

    public RequestBuilder getDefaultRequestBuilder(Map<String,Object> config) {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ProtocolFields.MERCHANT_CODE, config.get(TransactionParam.CHINAUMS_MCH_CODE));
        requestBuilder.set(ProtocolFields.TERMINAL_CODE, config.get(TransactionParam.CHINAUMS_TERM_CODE));
        return requestBuilder;
    }

    public String buildMerchantRemark(String subject) {
        if (subject.length() > 30) {
            StringBuilder merchantRemarkBuilder = new StringBuilder(30);
            return merchantRemarkBuilder.append(subject, 0, 27).append("...").toString();
        }
        return subject;
    }

    private void limitCredit(RequestBuilder builder, Map<String, Object> transaction) {
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_CREDIT_CARD, true);
        }
    }

    private void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            builder.set(key, value);
        }
    }


    protected void setTerminalInfo(TransactionContext context, RequestBuilder builder) {

        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        builder.set(BusinessFields.DEVICE_TYPE, ChinaumsConstants.DEVICE_TYPE_11);
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
            builder.set(BusinessFields.LONGITUDE, terminalInfo.getChinaumsFormatLongitude());
            builder.set(BusinessFields.LATITUDE, terminalInfo.getChinaumsFormatLatitude());
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }
        if (terminalInfo.isSendIp()) {
            builder.set(BusinessFields.IP, terminalInfo.getIp());
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

}
