package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 2)
public class DirectWeixinV3PreDepositServiceProvider extends WeixinServiceProvider{

    @Autowired
    private WeixinV3Client client;

    public static final String NAME = "provider.weixin.wapOrMini.v3";

    public DirectWeixinV3PreDepositServiceProvider(){
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_AMOUNT));
    }

    /**
     * 是否是微信刷掌支付
     * @return
     */
    public boolean isPalmService(){
        return false;
    }
    
    @Override
    public Map<String, Object> call(Map<String, Object> config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError {
        String method = WeixinV3Client.METHOD_POST;
        if(OP_DEPOSIT_QUERY.equals(opFlag) || OP_DEPOSIT_AUTH_QUERY.equals(opFlag)) {
            method = WeixinV3Client.METHOD_GET;
        }
        String serialNo = (String) config.get(TransactionParam.WEIXIN_SERIAL_NO);
        String mchId = (String) config.get(TransactionParam.WEIXIN_MCH_ID);
        return client.call(serviceUrl, method,mchId ,serialNo, getPrivateKeyContent((String) config.get(TransactionParam.WEIXIN_PRIVATE_KEY_V3)), request);
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        builder.set(BusinessFields.SERVICE_ID, config.get(TransactionParam.WEIXIN_SERVICE_ID));
        return builder;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (subPayway != Order.SUB_PAYWAY_MINI) {
            return false;
        }
        int type = MapUtil.getIntValue(transaction,Transaction.TYPE);
        if(!Transaction.TYPE_DEPOSIT_SET.contains(type)){
            return false;
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);
        //区分商户支付分和服务商支付分，默认使用商户支付分
        boolean matchProductCode = matchProductCode(transaction, tradeParams, TransactionParam.SQB_PRODUCT_CODE_PAY_SCORE, true);
        return Objects.nonNull(tradeParams) && matchProductCode && TransactionParam.SERVICE_MODE_MERCHANT == MapUtil.getIntValue(tradeParams, TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_MERCHANT);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    /**
     *
     * @param context
     * @param transaction
     * @param config
     * @return
     */
    protected RequestBuilder buildPreFreezeRequest(TransactionContext context, Map<String, Object> transaction, Map<String, Object> config){
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder = buildPreFreezeRequest(builder, transaction, config);
        builder.set(BusinessFields.SERVICE_INTRODUCTION, transaction.get(Transaction.SUBJECT));
        return builder;
    }

    protected RequestBuilder buildPreFreezeRequest(RequestBuilder builder, Map<String, Object> transaction, Map<String, Object> config){
        return builder;
    }

    @Override
    @SuppressWarnings("unchecked")
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = buildPreFreezeRequest(context, transaction, config);
        builder.set(BusinessFields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_PRE_CREATE_ALLOWED_FIELDS);
        //其余字段全部透传到支付通道去 不做解析
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_PREFREEZE), builder.build(), retryTimes, OP_DEPOSIT_PREFREEZE);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit query", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_PREFREEZE);

        return buildPreFreezeResult(result, context);
    }

    public String buildPreFreezeResult(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
        String state = BeanUtil.getPropString(result, ResponseFields.STATE);
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode && state.equals(WeixinConstants.DEPOSIT_STATE_CREATE)) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            Map<String, Object> wapRequest = new HashMap<>();

            wapRequest.put(WapV3Fields.MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
            wapRequest.put(WapV3Fields.PACKAGE, BeanUtil.getPropString(result, ResponseFields.PACKAGE));
            wapRequest.put(WapV3Fields.TIMESTAMP, System.currentTimeMillis() / 1000 + "");
            wapRequest.put(WapV3Fields.NONCE_STR, getNonceStr());
            wapRequest.put(WapV3Fields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_HMAC_SHA256);
            try {
                String paySign = HmacSignature.sign(wapRequest, config.get(TransactionParam.WEIXIN_APP_KEY) + "");
                wapRequest.put(WapV3Fields.ORDER_STR, BeanUtil.getPropString(result, ResponseFields.PACKAGE));
                wapRequest.put(WapV3Fields.SIGN, paySign);
            }catch (Exception ex){
                logger.error("sign error:",ex);
                return Workflow.RC_ERROR;
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    @SuppressWarnings("unchecked")
    public String depositQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        if(!isPalmService()){
            builder.set(BusinessFields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        }
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, getDepositQueryUrl(transaction), builder.build(), retryTimes, OP_DEPOSIT_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit query", ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_QUERY);
        int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
        int type = BeanUtil.getPropInt(transaction,Transaction.TYPE);
        if (type == Transaction.TYPE_DEPOSIT_FREEZE || type == Transaction.TYPE_DEPOSIT_CANCEL) {
            if (httpCode == HttpConstant.HTTP_CODE_SUCCESS) {
                String state = BeanUtil.getPropString(result, ResponseFields.STATE);
                switch (state) {
                    case WeixinConstants.DEPOSIT_STATE_DOING:
                    case WeixinConstants.DEPOSIT_STATE_DONE:
                        processWhenPreFreezeSuccess(transaction, result);
                        return Workflow.RC_PAY_SUCCESS;
                    case WeixinConstants.DEPOSIT_STATE_EXPIRED:
                        return Workflow.RC_EXPIRE;
                    case WeixinConstants.DEPOSIT_STATE_REVOKED:
                    case WeixinConstants.DEPOSIT_STATE_CLOSED:
                        return Workflow.RC_TRADE_CANCELED;
                    case WeixinConstants.DEPOSIT_STATE_CREATE:
                        //刷掌支付， 创建成功当做成功
                        if(isPalmService()){
                            processWhenPreFreezeSuccess(transaction, result);
                            return Workflow.RC_PAY_SUCCESS;
                        }
                }
            }
            return Workflow.RC_IN_PROG;
        } else if(type == Transaction.TYPE_DEPOSIT_CONSUME) {
            if (httpCode == HttpConstant.HTTP_CODE_SUCCESS) {
                String state = BeanUtil.getPropString(result, ResponseFields.STATE);
                Map<String, Object> collection = (Map<String, Object>) result.get(ResponseFields.COLLECTION);
                String payState = BeanUtil.getPropString(collection, ResponseFields.COLLECTION_STATE);
                int payingAmount = BeanUtil.getPropInt(collection, ResponseFields.COLLECTION_PAYING_AMOUNT, 0);
                if ((WeixinConstants.DEPOSIT_STATE_DONE.equals(state) || WeixinConstants.DEPOSIT_STATE_COMPLETED.equals(state)) && WeixinConstants.DEPOSIT_USER_PAID.equals(payState) && payingAmount == 0) {
                    setTradeNoBuyerInfoIfExists(result, context);
                    resolvePayFund(result,context);
                    return Workflow.RC_CONSUME_SUCCESS;
                }
            }
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    public String getDepositQueryUrl(Map<String,Object> transaction){
        return ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_QUERY);
    }

    protected void processWhenPreFreezeSuccess(Map<String, Object> transaction, Map<String,Object> result){
        //设置服务开始时间
        try{
            Map<String,Object> timeRange = com.wosai.pantheon.util.MapUtil.getMap(result, BusinessFields.TIME_RANGE);
            String startTime = com.wosai.pantheon.util.MapUtil.getString(timeRange, BusinessFields.START_TIME);
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseDepositTimeRangeTimeString(startTime));
        }catch (Exception e){
            logger.error("parse time start error", e);
        }
    }

    public String depositCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String defaultReason = StringUtils.join(DateUtil.formatDate(new Date(), DateUtil.FORMATTER_DATE_INT), " 解冻", StringUtils.cents2yuan(BeanUtil.getPropLong(context.getOrder(), Order.EFFECTIVE_TOTAL)), "元");
        if(isPalmService()){
            defaultReason = WeixinConstants.REASON_FREE_CHARGE;
            builder.set(BusinessFields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        }
        builder.set(BusinessFields.REASON, defaultReason);
        String url = getDepositCancelUrl(transaction);
        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_DEPOSIT_CANCEL_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), retryTimes, OP_DEPOSIT_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit query", ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_CANCEL);

        return buildCancelResult(result, context);
    }

    /**
     * 获取撤销地址
     * @param transaction
     * @return
     */
    public String getDepositCancelUrl(Map<String, Object> transaction){
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CANCEL);
        String orderNo = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        return url + URL_INTERVAL + orderNo + URL_INTERVAL + MpayServiceProvider.OP_CANCEL;
    }

    protected String buildCancelResult(Map<String, Object> result, TransactionContext context){
        int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
        if(httpCode == HttpConstant.HTTP_CODE_SUCCESS || httpCode == HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE){
            return Workflow.RC_CANCEL_SUCCESS;
        }
        String code = BeanUtil.getPropString(result, ResponseFields.CODE);
        if (code.equals(WeixinConstants.DEPOSIT_CODE_ORDER_CANCELED)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        if(WeixinConstants.DEPOSIT_CODE_SYSTEM_ERROR.equals(code) || WeixinConstants.DEPOSIT_CODE_FREQUENCY_LIMITED.equals(code)){
            return Workflow.RC_RETRY;
        }
        if(httpCode == HttpConstant.HTTP_CODE_400 
                && WeixinConstants.DEPOSIT_CODE_PARAM_ERROR.equals(code)) {
            String message = com.wosai.pantheon.util.MapUtil.getString(result, ResponseFields.MESSAGE);
            if(WeixinConstants.DEPOSIT_CANCEL_TRADE_EXISTS_MSG.equals(message) && Workflow.RC_TRADE_CANCELED.equals(depositQuery(context))) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
            
        }
        return Workflow.RC_ERROR;
    }

    @Override
    @SuppressWarnings("unchecked")
    public String depositConsume(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        Map<String, Object> transaction = context.getTransaction();
        if(isPalmService()){
            builder.set(BusinessFields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        }
        builder.set(BusinessFields.TOTAL_AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT));
        setProfitSharing(transaction, builder, BusinessFields.PROFIT_SHARING, Boolean.TRUE);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_PRE_CREATE_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, getDepositConsumeUrl(transaction), builder.build(), retryTimes, OP_DEPOSIT_CONSUME);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit query", ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }

        return buildConsumeResult(result, context);
    }

    /**
     * 获取完成url
     * @param transaction
     * @return
     */
    public String getDepositConsumeUrl(Map<String, Object> transaction){
        String orderNo = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CONSUME);
        return url + URL_INTERVAL + orderNo + "/complete";
    }

    protected String buildConsumeResult(Map<String, Object> result, TransactionContext context){
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
        int httpCode = BeanUtil.getPropInt(result,HttpConstant.HTTP_CODE);
        String state = BeanUtil.getPropString(result,ResponseFields.STATE);
        if (httpCode == HttpConstant.HTTP_CODE_SUCCESS) {
            if (WeixinConstants.DEPOSIT_STATE_DONE.equals(state)) {
                return Workflow.RC_IN_PROG;
            } else if (WeixinConstants.DEPOSIT_STATE_DOING.equals(state)) {
                return Workflow.RC_IN_PROG;
            }else if (WeixinConstants.DEPOSIT_STATE_COMPLETED.equals(state)){
                return Workflow.RC_IN_PROG;
            }
        }
        String code = BeanUtil.getPropString(result, ResponseFields.CODE);
        if (WeixinConstants.DEPOSIT_CODE_ORDER_DONE.equals(code) || WeixinConstants.DEPOSIT_CODE_PARAM_ERROR.equals(code)) {
            return Workflow.RC_IN_PROG;
        }
        if (WeixinConstants.RESULT_ERROR_INVALID_REQUEST.equals(code)) {
            String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
            if (WeixinConstants.MESSAGE_INVALID_ORDER_STATUS.equals(message)) {
                return Workflow.RC_IN_PROG;
            }
        }
        if(WeixinConstants.DEPOSIT_CONSUMER_V3_FAIL_LIST.contains(code)) {
            return Workflow.RC_CONSUME_FAIL;
        }else if (WeixinConstants.DEPOSIT_CODE_SYSTEM_ERROR.equals(code)) {
            return Workflow.RC_SYS_ERROR;
        }else if(WeixinConstants.DEPOSIT_CODE_FREQUENCY_LIMITED.equals(code)){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }


    protected void carryDepositOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        if(extended == null || extended.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)){
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.set(key,value);
            }
        }
    }

    protected Map<String, Object> retryIfNetworkException(Map<String,Object> config, String url, Map<String,Object> request, int times, String opFlag) throws Exception {
        return retryIfNetworkException(()-> call(config, url, request, opFlag), logger, times, opFlag, "weixin");
    }

    @SuppressWarnings("unchecked")
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_DEPOSIT_FREEZE && type != Transaction.TYPE_DEPOSIT_CONSUME) {
            return null;
        }
        Map<String, Object> header = (Map<String, Object>) providerNotification.get(HttpConstant.REQUEST_HEADER);
        providerNotification.remove(HttpConstant.REQUEST_HEADER);
        String publicKey = BeanUtil.getPropString(getTradeParams(context.getTransaction()), TransactionParam.WEIXIN_PUBLIC_KEY_V3);
        String appKey = BeanUtil.getPropString(getTradeParams(context.getTransaction()), TransactionParam.WEIXIN_APP_KEY_V3);
        if (!StringUtils.isEmpty(publicKey) && !StringUtils.isEmpty(appKey)) {
            String content = BeanUtil.getPropString(header, WeixinConstants.WECHATPAY_TIMESTAMP) + "\n" +
                    BeanUtil.getPropString(header, WeixinConstants.WECHATPAY_NONCE) + "\n" +
                    BeanUtil.getPropString(providerNotification,HttpConstant.BODY)+"\n";
            String sign = BeanUtil.getPropString(header, WeixinConstants.WECHATPAY_SIGNATURE);
            boolean isValidatePass = false;
            try {
                isValidatePass = RsaSignature.validateSign(content, sign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, serviceFacade.getRsaKeyDataById(publicKey));
            } catch (Exception ex) {
                logger.error("rsaCheckContent error", ex);
            }
            if (isValidatePass) {
                try {
                    Map<String, Object> resource = (Map<String, Object>) providerNotification.get(WeixinConstants.RESOURCE);
                    String associatedData = BeanUtil.getPropString(resource, WeixinConstants.ASSOCIATED_DATA);
                    String nonce = BeanUtil.getPropString(resource, WeixinConstants.NONCE);
                    String ciphertext = BeanUtil.getPropString(resource, WeixinConstants.CIPHER_TEXT);
                    Map<String, Object> notifyResult = JSONObject.parseObject(AesUtil.decryptToString(appKey, associatedData, nonce, ciphertext));
                    String state = BeanUtil.getPropString(notifyResult, ResponseFields.STATE);
                    if (type == Transaction.TYPE_DEPOSIT_FREEZE) {
                        if (WeixinConstants.DEPOSIT_STATE_DOING.equals(state)) {
                            processWhenPreFreezeSuccess(transaction, notifyResult);
                            return Workflow.RC_PAY_SUCCESS;
                        }

                    } else {
                        Map<String, Object> collection = (Map<String, Object>) notifyResult.get(ResponseFields.COLLECTION);
                        String payState = BeanUtil.getPropString(collection, ResponseFields.COLLECTION_STATE);
                        int payingAmount = BeanUtil.getPropInt(collection, ResponseFields.COLLECTION_PAYING_AMOUNT, 0);
                        if (WeixinConstants.DEPOSIT_STATE_DONE.equals(state) && WeixinConstants.DEPOSIT_USER_PAID.equals(payState) && payingAmount == 0) {
                            setTradeNoBuyerInfoIfExists(notifyResult, context);
                            resolvePayFund(notifyResult,context);
                            return Workflow.RC_PAY_SUCCESS;
                        }
                    }
                } catch (Exception ex) {
                    logger.error("decryptToString error", ex);
                }
            }
        }
        if (type == Transaction.TYPE_DEPOSIT_FREEZE) {
            return Workflow.RC_PAY_SUCCESS.equals(depositQuery(context)) ? Workflow.RC_PAY_SUCCESS : null;
        } else {
            return Workflow.RC_CONSUME_SUCCESS.equals(depositQuery(context)) ? Workflow.RC_CONSUME_SUCCESS : null;
        }
    }

    @SuppressWarnings("unchecked")
    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        int httpCode = BeanUtil.getPropInt(result,HttpConstant.HTTP_CODE);//返回状态码
        String code = (String)result.get(ResponseFields.CODE);//业务结果
        String message = (String)result.get(ResponseFields.MESSAGE);//业务信息
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(HttpConstant.HTTP_CODE, httpCode);
        map.put(ResponseFields.CODE, code);
        map.put(ResponseFields.MESSAGE, message);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, HttpConstant.HTTP_CODE_SUCCESS == httpCode, code, message);
    }

    @SuppressWarnings("unchecked")
    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        String tradeNo = BeanUtil.getPropString(result,ResponseFields.FINISH_TRANSACTION_ID);
        if(StringUtils.isEmpty(tradeNo)) {
            Map<String, Object> collection = (Map<String, Object>) result.get(ResponseFields.COLLECTION);
            if (collection != null && WeixinConstants.DEPOSIT_USER_PAID.equals(BeanUtil.getPropString(collection, ResponseFields.STATE))) {
                List<Map<String, Object>> details = (List<Map<String, Object>>) collection.get(ResponseFields.COLLECTION_DETAILS);
                if (details != null) {
                    for (Map<String, Object> detail : details) {
                        if (WeixinConstants.DEPOSIT_PAID_TYPE_NEWTON.equals(BeanUtil.getPropString(detail, ResponseFields.COLLECTION_DETAIL_PAID_TYPE))) {
                            String transactionId = BeanUtil.getPropString(detail, ResponseFields.TRANSACTION_ID);
                            if (!StringUtils.isEmpty(transactionId)) {
                                tradeNo = transactionId;
                                break;
                            }
                        }
                    }
                }
            }
            if(isPalmService() && StringUtils.isEmpty(tradeNo)){
                tradeNo = com.wosai.pantheon.util.MapUtil.getString(collection, ResponseFields.TRANSACTION_ID);
            }
        }
        if (!StringUtil.empty(tradeNo)) {
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
            if (StringUtil.empty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, tradeNo);
            }
        }
    }


    @SuppressWarnings("unchecked")
    public static void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (result == null) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> collection = (Map<String, Object>) result.get(ResponseFields.COLLECTION);
        if(collection == null || collection.isEmpty()){
            return;
        }
        long paidAmount = BeanUtil.getPropLong(collection, ResponseFields.COLLECTION_PAID_AMOUNT, com.wosai.pantheon.util.MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0) {
            transaction.put(Transaction.PAID_AMOUNT, paidAmount);
        }
        long receiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        List<Map<String,Object>> payments = new ArrayList<>();
        List<Map<String,Object>> details = (List<Map<String, Object>>) collection.get(ResponseFields.COLLECTION_DETAILS);
        if(details != null){
            for (Map<String,Object> map:details){
                String paidType = BeanUtil.getPropString(map,ResponseFields.COLLECTION_DETAIL_PAID_TYPE);
                if(paidType.equals(WeixinConstants.DEPOSIT_PAID_TYPE_MCH)){
                    payments.add(
                            CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                    Transaction.PAYMENT_ORIGIN_TYPE, paidType,
                                    Transaction.PAYMENT_AMOUNT, BeanUtil.getPropInt(map, ResponseFields.COLLECTION_DETAIL_AMOUNT),
                                    Transaction.PAYMENT_SOURCE, BeanUtil.getPropString(map, ResponseFields.COLLECTION_DETAIL_TRANSACTION_ID)
                            )
                    );
                }else if(paidType.equals(WeixinConstants.DEPOSIT_PAID_TYPE_NEWTON)){
                    receiveAmount = receiveAmount - BeanUtil.getPropInt(map,ResponseFields.COLLECTION_DETAIL_AMOUNT);
                    payments.add(
                            CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                    Transaction.PAYMENT_ORIGIN_TYPE, paidType,
                                    Transaction.PAYMENT_AMOUNT, BeanUtil.getPropInt(map,ResponseFields.COLLECTION_DETAIL_AMOUNT),
                                    Transaction.PAYMENT_SOURCE, BeanUtil.getPropString(map,ResponseFields.COLLECTION_DETAIL_TRANSACTION_ID)
                            )
                    );
                }
            }
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(!extraOutFields.containsKey(Transaction.PAYMENTS) && !payments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0) {
            transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
        }
    }

    private String getNonceStr(){
        return new Random().nextLong() + "";
    }

    @Override
    public String depositSync(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_SYNC);
        Map<String, Object> transaction = context.getTransaction();
        String orderNo = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        url = url + URL_INTERVAL + orderNo + "/sync";
        builder.set(BusinessFields.TYPE, WeixinConstants.TYPE_ORDER_PAID);

        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_SYNC_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), retryTimes, OP_DEPOSIT_SYNC);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit sync", ex);
            return Workflow.RC_CONSUME_FAIL;
        }
        if(result == null){
            return Workflow.RC_CONSUME_FAIL;
        }
        int httpCode = BeanUtil.getPropInt(result,HttpConstant.HTTP_CODE);
        String state = BeanUtil.getPropString(result,ResponseFields.STATE);
        if (httpCode == HttpConstant.HTTP_CODE_SUCCESS) {
            if (WeixinConstants.DEPOSIT_STATE_DONE.equals(state)) {
                setTradeNoBuyerInfoIfExists(result, context);
                resolvePayFund(result,context);
                return Workflow.RC_CONSUME_SUCCESS;
            }
        }
        return Workflow.RC_CONSUME_FAIL;
    }


    private static final SafeSimpleDateFormat ymdSDF = new SafeSimpleDateFormat("yyyyMMdd");
    private static final SafeSimpleDateFormat ymdhmsSDF = new SafeSimpleDateFormat("yyyyMMddHHmmss");

    @SneakyThrows
    public static Long parseDepositTimeRangeTimeString(String time){
        if(time == null){
            return null;
        }
        if(time.length() == 8){
            return ymdSDF.parse(time).getTime();
        }else if(time.length() == 14){
            return ymdhmsSDF.parse(time).getTime();
        }
        return null;
    }
}
