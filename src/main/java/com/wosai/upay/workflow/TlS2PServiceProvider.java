package com.wosai.upay.workflow;

import com.wosai.mpay.api.tl.s2p.S2PClient;
import com.wosai.mpay.api.tl.s2p.S2PConstants;
import com.wosai.mpay.api.tl.s2p.S2PRequestFields;
import com.wosai.mpay.api.tl.s2p.S2PResponseFields;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING;

/**
 * 通联收银宝海外
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/3/20.
 */
public class TlS2PServiceProvider extends AbstractServiceProvider{

    protected String notifyHost;

    @Autowired
    private S2PClient s2PClient;

    public static final String NAME = "provider.tls2p";

    protected Set<String> allowExtendsFields = new HashSet<>(Arrays.asList(S2PRequestFields.PreProcess.VALIDATION_URL, S2PRequestFields.PreProcess.DISPLAY_NAME, S2PRequestFields.PreProcess.DOMAIN_NAME));


    public TlS2PServiceProvider(){
        extendedFilterFields = new HashSet<String>(Arrays.asList(S2PRequestFields.PreProcess.AMOUNT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Provider.TL_S2P.getCode();
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.TL_S2P_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String resultCode = MapUtil.getString(result, S2PResponseFields.Common.RESULT_CODE);
        if(S2PConstants.ResultCode.PAYING.equals(resultCode) || S2PConstants.ResultCode.ORDER_NOT_EXIST.equals(resultCode)){
            return Workflow.RC_IN_PROG;
        }
        if(!S2PConstants.ResultCode.SUCCESS.equals(resultCode)){
            return Workflow.RC_ERROR;
        }
        String status = MapUtil.getString(result, S2PResponseFields.Query.STATUS);
        if(S2PConstants.OrderStatus.PAIED.equals(status)){
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            transaction.put(Transaction.TRADE_NO, MapUtil.getString(result, S2PResponseFields.Query.ORDER_ID));
            return Workflow.RC_PAY_SUCCESS;

        }else if(S2PConstants.OrderStatus.READY.equals(status) || S2PConstants.OrderStatus.PAYING.equals(status)){
            return Workflow.RC_IN_PROG;
        }else if(S2PConstants.OrderStatus.REVOKED.equals(status) || S2PConstants.OrderStatus.CLOSED.equals(status) || S2PConstants.OrderStatus.FAILED.equals(status)){
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String resultCode = MapUtil.getString(result, S2PResponseFields.Common.RESULT_CODE);
        if(resultCode.equals(S2PConstants.ResultCode.TIMEOUT) || resultCode.equals(S2PConstants.ResultCode.SYSTEM_ERROR) || resultCode.equals(S2PConstants.ResultCode.PAYING)){
            return Workflow.RC_RETRY;
        }
        if(!S2PConstants.ResultCode.SUCCESS.equals(resultCode)){
            return Workflow.RC_ERROR;
        }
        String status = MapUtil.getString(result, S2PResponseFields.Query.STATUS);
        // refund success
        if(S2PConstants.OrderStatus.PAIED.equals(status)){
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            transaction.put(Transaction.TRADE_NO, MapUtil.getString(result, S2PResponseFields.Query.ORDER_ID));
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_RETRY;
    }


    // doQuery
    protected Map<String,Object> doQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, String> request = buildCommonParams(config);
        // 查询参数
        request.put(S2PRequestFields.Common.TRANS_TYPE, S2PConstants.TransType.QUERY);
        request.put(S2PRequestFields.Query.ORI_ACCESS_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        Map<String, Object> result = null;
        try {
            result = s2PClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), request, getPrivateKeyContent(MapUtil.getString(config, TransactionParam.TL_SYB_PRIVATE_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call tls2p query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
        }
        return result;

    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery){
            return refundQuery(context);
        }else{
            return doRefund(context);
        }
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map<String, String> request = buildCommonParams(config);
        request.put(S2PRequestFields.Common.TRANS_TYPE, S2PConstants.TransType.TOKEN_REFUND);
        request.put(S2PRequestFields.Refund.ORI_ACCESS_ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        request.put(S2PRequestFields.Common.ACCESS_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(S2PRequestFields.Refund.REFUND_AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        Map<String, Object> result = null;
        try {
            result = s2PClient.call(url, request, getPrivateKeyContent(MapUtil.getString(config, TransactionParam.TL_SYB_PRIVATE_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call tls2p refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String resultCode = MapUtil.getString(result, S2PResponseFields.Common.RESULT_CODE, "");
        if(resultCode.equals(S2PConstants.ResultCode.SUCCESS)){
            return Workflow.RC_REFUND_SUCCESS;
        }
        if(resultCode.equals(S2PConstants.ResultCode.TIMEOUT) || resultCode.equals(S2PConstants.ResultCode.SYSTEM_ERROR)){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, String> request = buildCommonParams(config);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        // 授权请求参数
        request.put(S2PRequestFields.Common.ACCESS_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(S2PRequestFields.PreProcess.CURRENCY, getTradeCurrency(transaction));
        request.put(S2PRequestFields.PreProcess.AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        request.put(S2PRequestFields.PreProcess.NOTIFY_URL, notifyUrl);
        request.put(S2PRequestFields.PreProcess.EXPIRE_MINS, DEFAULT_TIME_EXPIRE_MINUTE + "");
        carryOverExtendedParams(extended, request, allowExtendsFields);
        Map<String, Object> result = null;
        try {
            result = s2PClient.call(url, request, getPrivateKeyContent(MapUtil.getString(config, TransactionParam.TL_SYB_PRIVATE_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call tls2p precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        String resultCode = MapUtil.getString(result, S2PResponseFields.Common.RESULT_CODE);
        if(!S2PConstants.ResultCode.SUCCESS.equals(resultCode)){
            return Workflow.RC_ERROR;
        }
        //预下单成功
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, MapUtil.hashMap(
                S2PResponseFields.PreProcess.APPLE_PAY_SESSION, result.get(S2PResponseFields.PreProcess.APPLE_PAY_SESSION)
        ));
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    public Map<String, Object> confirmPay(Map<String, Object> confirmPayRequest, Map<String, Object> transaction) {
        Map<String, Object> config = getTradeParams(transaction);

        Map<String, String> request = buildCommonParams(config);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        // 授权请求参数
        request.put(S2PRequestFields.Common.ACCESS_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(S2PRequestFields.Authorized.ORI_ACCESS_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(S2PRequestFields.Authorized.CURRENCY, getTradeCurrency(transaction));
        request.put(S2PRequestFields.Authorized.AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        request.put(S2PRequestFields.Authorized.PAYLOAD, MapUtil.getString(confirmPayRequest, S2PRequestFields.Authorized.PAYLOAD));
        Map<String, Object> result = null;
        try {
            result = s2PClient.call(url, request, getPrivateKeyContent(MapUtil.getString(config, TransactionParam.TL_SYB_PRIVATE_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call tls2p pay", ex);
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, ex.getMessage());
        }
        if (result == null) {
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING);
        }
        String resultCode = MapUtil.getString(result, S2PResponseFields.Common.RESULT_CODE);
        String resultDesc = MapUtil.getString(result, S2PResponseFields.Common.RESULT_DESC);
        if(!S2PConstants.ResultCode.SUCCESS.equals(resultCode)){
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, resultDesc);
        }
        return result;
    }

    // build common params
    private Map<String, String> buildCommonParams(Map<String, Object> config) {
        Map<String, String> params = new LinkedHashMap<>();
        params.put(S2PRequestFields.Common.VERSION, S2PConstants.VERSION);
        params.put(S2PRequestFields.Common.INST_NO, MapUtil.getString(config, TransactionParam.TL_S2P_INST_NO));
        params.put(S2PRequestFields.Common.MCHT_ID, MapUtil.getString(config, TransactionParam.PROVIDER_MCH_ID));
        return params;
    }


    protected void carryOverExtendedParams(Map<String, Object> extended, Map<String, String> request, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)){
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                request.put(key, value.toString());
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String resultCode = MapUtil.getString(result, S2PResponseFields.Common.RESULT_CODE);
        String resultDesc = MapUtil.getString(result, S2PResponseFields.Common.RESULT_DESC);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(S2PResponseFields.Common.RESULT_CODE, resultCode);
        map.put(S2PResponseFields.Common.RESULT_DESC, resultDesc);
        setTransactionContextErrorInfo(context.getTransaction(), key, map,
                S2PConstants.ResultCode.SUCCESS.equals(resultCode), resultCode, resultDesc
        );
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }
}
