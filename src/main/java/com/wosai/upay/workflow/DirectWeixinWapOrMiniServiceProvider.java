package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

import java.util.Map;

/**
 * Created by jianfree on 6/12/15.
 */
@ServiceProvicerPriority(priority = 2)
public class DirectWeixinWapOrMiniServiceProvider extends WeixinWapOrMiniServiceProvider {

    public static final String NAME = "provider.weixin.wapOrMini";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if(tradeParams == null || tradeParams.containsKey(TransactionParam.WEIXIN_VERSION)){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        int type = MapUtil.getIntValue(transaction,Transaction.TYPE);
        if(Transaction.TYPE_DEPOSIT_SET.contains(type) && subPayway == Order.SUB_PAYWAY_MINI) {
            return false;
        }
        if (subPayway == Order.SUB_PAYWAY_MINI) {
            return payway == Order.PAYWAY_WEIXIN && matchProductCode(transaction, tradeParams, null, true);
        }

        return payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_H5 || subPayway == Order.SUB_PAYWAY_APP);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        String propTradeParams;
        if(subPayway == Order.SUB_PAYWAY_WAP){
            propTradeParams = TransactionParam.WEIXIN_WAP_TRADE_PARAMS;
        }else if(subPayway == Order.SUB_PAYWAY_H5){
            propTradeParams = TransactionParam.WEIXIN_H5_TRADE_PARAMS;
        }else if(subPayway == Order.SUB_PAYWAY_APP){
            propTradeParams = TransactionParam.WEIXIN_APP_TRADE_PARAMS;
        }else{
            propTradeParams = TransactionParam.WEIXIN_MINI_TRADE_PARAMS;
        }
        return getTradeParams(transaction, propTradeParams);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public Map<String, Object> call(Map<String, Object> config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError {
    	if(OP_CANCEL.equals(opFlag) || OP_REFUND.equals(opFlag)){
	    	String certConfigKey = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_CONFIG_KEY);
	        String password = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_PASSWORD);
	        return client.call(serviceUrl, WeixinClient.SIGN_TYPE_WEIXIN, (String) config.get(TransactionParam.WEIXIN_APP_KEY), getSSLContext(certConfigKey, password), request);
    	}else{
    		return client.call(serviceUrl, WeixinClient.SIGN_TYPE_WEIXIN, (String) config.get(TransactionParam.WEIXIN_APP_KEY), null, request);
    	}
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        //微信直连正式的交易，小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
        //对接了万码，门店码支付的sub_app_id为weixin_sub_appid, 小程序的sub_app_id为weixin_mini_sub_appid
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_MINI == subPayway){
            String miniSubAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            if(!StringUtil.empty(miniSubAppId)){
                builder.set(ProtocolFields.SUB_APP_ID, miniSubAppId);
            }
        }
        return builder;
    }


}
