package com.wosai.upay.workflow;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.foxconn.BusinessFields;
import com.wosai.mpay.api.foxconn.FoxconnClient;
import com.wosai.mpay.api.foxconn.FoxconnConstants;
import com.wosai.mpay.api.foxconn.ProtocolFields;
import com.wosai.mpay.api.foxconn.RequestBuilder;
import com.wosai.mpay.api.foxconn.ResponseFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class FoxconnServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(FoxconnServiceProvider.class);

    public static final String NAME = "provider.foxconn";

    @Autowired
    private FoxconnClient client;

    public FoxconnServiceProvider(){
        dateFormat = new SafeSimpleDateFormat(FoxconnConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.AMOUNT));
    }
    
    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if (Objects.isNull(tradeParams)) {
            return false;
        }
        return true;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.FOXCONN_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_FOXCONN;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.FOXCONN_PRIVATE_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams);
        builder.set(BusinessFields.TYPE, FoxconnConstants.TYPE_FOXCONN_PAY);
        builder.dataSet(BusinessFields.EQUIPMENT_SN, BeanUtil.getPropString(tradeParams, TransactionParam.FOXCONN_EQUIPMENT_SN));
        builder.dataSet(BusinessFields.MER_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.dataSet(BusinessFields.BAR_CODE, extraParams.get(Transaction.BARCODE));
        builder.dataSet(BusinessFields.AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));

        carryOverExtendedParams(extendedParams, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), getPrivateKeyContent(privateKey), 1, OP_PAY);
        } catch (Exception e) {
            logger.error("failed to call foxconn pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.FOXCONN_PRIVATE_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams);
        builder.set(BusinessFields.TYPE, FoxconnConstants.TYPE_FOXCONN_PAY_QUERY);
        builder.dataSet(BusinessFields.MER_ORDER_NO, transaction.get(Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), getPrivateKeyContent(privateKey), 3, OP_QUERY);
        } catch (Exception e) {
            logger.error("failed to call foxconn query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持退款");
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String respCode = MapUtil.getString(result, ResponseFields.CODE);
        String respMsg = MapUtil.getString(result, ResponseFields.MSG);
        Map<String, Object> data = MapUtil.getMap(result, ResponseFields.DATA);
        String statusCode = MapUtil.getString(data, ResponseFields.STATUS_CODE);
        String statusDesc = MapUtil.getString(data, ResponseFields.STATUS_DESC);
        map.put(ResponseFields.CODE, respCode);
        map.put(ResponseFields.MSG, respMsg);
        map.put(ResponseFields.STATUS_CODE, statusCode);
        map.put(ResponseFields.STATUS_DESC, statusDesc);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, 
                Objects.equals(respCode, FoxconnConstants.CODE_SUCCESS) && Objects.equals(statusCode, FoxconnConstants.STATUS_CODE_SUCCESS), 
                !StringUtil.empty(respCode) ? respCode : statusCode,
                !StringUtil.empty(statusDesc) ? statusDesc : respMsg);
    }

    private Map<String, Object> retryIfNetworkException(String url, Map<String, String> request, String appKey, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(url,  appKey, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in foxconn {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.CODE);
        //明确失败
        if (!Objects.equals(responseCode, FoxconnConstants.CODE_SUCCESS)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        Map<String, Object> data = MapUtil.getMap(result, ResponseFields.DATA);
        String statusCode = MapUtil.getString(data, ResponseFields.STATUS_CODE);
        setTradeNoBuyerInfoIfExists(data, context);
        if(Objects.equals(FoxconnConstants.STATUS_CODE_SUCCESS, statusCode)) {
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            resolveFund(context);
            return Workflow.RC_PAY_SUCCESS;
        }
        
        // 明确失败
        if(FoxconnConstants.CODE_PAY_CANCELED.contains(statusCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        // 支付中
        if (FoxconnConstants.CODE_IN_PROG.contains(statusCode)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String responseCode = MapUtil.getString(result, ResponseFields.CODE);
        if (!Objects.equals(responseCode, FoxconnConstants.CODE_SUCCESS)) {
            return Workflow.RC_IN_PROG;
        }

        Map<String, Object> data = MapUtil.getMap(result, ResponseFields.DATA);
        String statusCode = MapUtil.getString(data, ResponseFields.STATUS_CODE);
        setTradeNoBuyerInfoIfExists(data, context);
        if(Objects.equals(FoxconnConstants.STATUS_CODE_SUCCESS, statusCode)) {
            String tradeStatus = MapUtil.getString(data, ResponseFields.TRADE_STATUS);
            if(Objects.equals(FoxconnConstants.TRADE_STATUS_PAID, tradeStatus)) {
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolveFund(context);
                return Workflow.RC_PAY_SUCCESS;
            }else if(Objects.equals(FoxconnConstants.TRADE_STATUS_CREATED, tradeStatus) || Objects.equals(FoxconnConstants.TRADE_STATUS_IN_PROG, tradeStatus)) {
                return Workflow.RC_IN_PROG;
            }else {
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        // 支付中
        if (FoxconnConstants.CODE_IN_PROG.contains(statusCode)) {
            return Workflow.RC_IN_PROG;
        }
        // 明确失败
        if(FoxconnConstants.CODE_PAY_CANCELED.contains(statusCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_ERROR;
    }

    private void resolveFund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments  = new ArrayList<>();
        }else{
            payments.clear();
        }

        //交易金额
        long transactionAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, transactionAmount,
                Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_WALLET_FOXCONN,
                Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_FOXCONN));

        extraOutFields.put(Transaction.PAYMENTS, payments);
        transaction.put(Transaction.PAID_AMOUNT, transactionAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> data, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        String orderId = BeanUtil.getPropString(data, ResponseFields.ORDER_ID);

        if(!StringUtil.empty(orderId)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, orderId);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, orderId);
            }
        }
    }

    public RequestBuilder getDefaultRequestBuilder(Map<String,Object> config) {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ProtocolFields.ACCESS_ID, MapUtil.getString(config, TransactionParam.FOXCONN_ACCESS_ID));
        requestBuilder.set(ProtocolFields.VERSION, FoxconnConstants.VERSION_1_0);
        requestBuilder.set(ProtocolFields.TIMESTAMP, formatTimeString(System.currentTimeMillis()));
        requestBuilder.set(ProtocolFields.FORMAT, FoxconnConstants.FORMAT_JSON);
        requestBuilder.set(BusinessFields.USER_ID, MapUtil.getString(config, TransactionParam.FOXCONN_USER_ID));
        return requestBuilder;
    }


    private void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            builder.dataSet(key, value);
        }
    }



    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("暂不支持预下单");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

}
