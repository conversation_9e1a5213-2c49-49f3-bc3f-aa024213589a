package com.wosai.upay.workflow;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import com.wosai.upay.common.util.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.lakala.open.BusinessV3Fields;
import com.wosai.mpay.api.lakala.open.LakalaConstants;
import com.wosai.mpay.api.lakala.open.LakalaOpenClient;
import com.wosai.mpay.api.lakala.open.RequestV3Builder;
import com.wosai.mpay.api.lakala.open.ResponseFields;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields.DccFlgEnum;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields.TradeTypeEnum;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields.WildCardBusiModeEnum;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class LakalaOpenV3BankCardServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(LakalaOpenV3BankCardServiceProvider.class);
    public static final String NAME = "provider.lakala.openv3.bankcard";
    protected static final String LOG_NO_KEY = String.format("%s.%s", ResponseV3Fields.RESP_DATA, ResponseV3Fields.LOG_NO);
    protected static final String TRADE_TIME_KEY = String.format("%s.%s", ResponseV3Fields.RESP_DATA, ResponseV3Fields.TRADE_TIME);
    protected static final String REFUND_AMOUNT_KEY = String.format("%s.%s", ResponseV3Fields.RESP_DATA, ResponseV3Fields.REFUND_AMOUNT);
    protected static final String COUNTER_URL = String.format("%s.%s", ResponseV3Fields.RESP_DATA, ResponseV3Fields.COUNTER_URL);
    protected static final String PAY_ORDER_NO_KEY = String.format("%s.%s", ResponseV3Fields.RESP_DATA, ResponseV3Fields.PAY_ORDER_NO);
    protected static final SafeSimpleDateFormat NEW_VERSION_QUERY_FORMAT = new SafeSimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT_V3);

    protected static final Map<String, String> ACC_TYPE_MAPPING = MapUtil.hashMap(
            ResponseV3Fields.AccTypeEnum.DEBIT_CARD.getCode(), Payment.TYPE_BANKCARD_DEBIT,
            ResponseV3Fields.AccTypeEnum.CREDIT_CARD.getCode(), Payment.TYPE_BANKCARD_CREDIT,
            ResponseV3Fields.AccTypeEnum.UNKNOWN.getCode(), Payment.TYPE_BANKCARD_CREDIT
    );

    protected static final Set<String> WILD_CARD_BUSI_MODE_MAP = Arrays.asList(WildCardBusiModeEnum.values()).stream().map(WildCardBusiModeEnum::getCode).collect(Collectors.toSet());

    protected static final Map<String, String> DCC_FLG_MAP = Arrays.asList(DccFlgEnum.values()).stream().collect(Collectors.toMap(DccFlgEnum::getCode, DccFlgEnum::getMeaning));

    
    public static final Map<String, String> PAYMENT_MAP = MapUtil.hashMap(
            Payment.TYPE_BANKCARD_DEBIT, TransactionParam.PARAMS_BANKCARD_FEE_DEBIT,
            Payment.TYPE_BANKCARD_CREDIT, TransactionParam.PARAMS_BANKCARD_FEE_CREDIT,
            Payment.TYPE_BANKCARD_SEMI_CREDIT, TransactionParam.PARAMS_BANKCARD_FEE_CREDIT,
            Payment.TYPE_BANKCARD_PREPAID, TransactionParam.PARAMS_BANKCARD_FEE_DEBIT
    );

    public String notifyHost;

    private int retryTimes = 3;
    //订单有效期时间 4分账
    private long orderExpiryTime = 4 * 60 * 1000;

    protected Map<Integer, QueryResolver> QUERY_RESOLVER_MAP = MapUtil.hashMap(
            Transaction.TYPE_PAYMENT, new PayQueryResolver(),
            Transaction.TYPE_REFUND, new RefundQueryResolver(),
            Transaction.TYPE_DEPOSIT_FREEZE, new DepositQueryResolver(),
            Transaction.TYPE_DEPOSIT_CANCEL, new DepositQueryResolver(),
            Transaction.TYPE_DEPOSIT_CONSUME, new DepositQueryResolver()
    );

    public static final Map<Integer, String> QUERY_RC_SUCCESS = MapUtil.hashMap(
            Transaction.TYPE_PAYMENT, Workflow.RC_PAY_SUCCESS,
            Transaction.TYPE_DEPOSIT_FREEZE, Workflow.RC_PAY_SUCCESS,
            Transaction.TYPE_DEPOSIT_CANCEL, Workflow.RC_CANCEL_SUCCESS,
            Transaction.TYPE_DEPOSIT_CONSUME, Workflow.RC_CONSUME_SUCCESS,
            Transaction.TYPE_REFUND, Workflow.RC_REFUND_SUCCESS
    );

    @Autowired
    LakalaOpenClient client;

    public LakalaOpenV3BankCardServiceProvider() {
        super.dateFormat = new SafeSimpleDateFormat(com.wosai.mpay.api.lakala.open.LakalaConstants.DATE_TIME_FORMAT);

    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP);
        if (payway == Order.PAYWAY_BANKCARD && subPayway == Order.SUB_PAYWAY_QRCODE) {
            Map<String, Object> params = getTradeParams(transaction);
            return params != null && getTradeCurrency(transaction).equals(TransactionParam.UPAY_DEFAULT_CURRENCY_CNY) && !Objects.equals(tradeApp, ApolloConfigurationCenterUtil.getPhonePosTradeAppId());
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("下单暂不支持");
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        //由于并没有撤销 退款接口 因此撤销退款全依赖查询接口来处理
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        QueryResolver queryResolver = QUERY_RESOLVER_MAP.get(type);
        return queryResolver.handler(context, getTradeParams(transaction));
    }

    @Override
    public String refund(TransactionContext context) {
        return doPaymentRefund(context);
    }

    private String doPaymentRefund(TransactionContext context) {
        RequestV3Builder request = initRefundRequest(context);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> result;
        Map<String, Object> config = getTradeParams(transaction);
        try {
            //退款只做一次 并没有幂等处理
            result = retryIfNetworkException(OP_REFUND, config, request.build(), 1);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            logger.error("failed to call lakala refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            logger.error("encountered ioex in lakala refund", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String resultCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        if (!StringUtils.isEmpty(resultCode) && resultCode.equals(LakalaConstants.SUCCESS_CODE)) {
            String logNo = BeanUtil.getPropString(result, LOG_NO_KEY);
            String tradeTime = BeanUtil.getPropString(result, TRADE_TIME_KEY);
            long refundAmount = BeanUtil.getPropLong(result, REFUND_AMOUNT_KEY);
            long finishTime = StringUtils.isEmpty(tradeTime) ? System.currentTimeMillis() : parseTimeString(tradeTime);
            setTransactionRefundSuccess(logNo, finishTime, refundAmount, context);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (!StringUtils.isEmpty(resultCode) && LakalaConstants.REFUND_RETRY_CODE.contains(resultCode)) {
            return query(context);
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        RequestV3Builder request = initGenerateOrderRequest(context);
        Map<String, Object> build = request.build();
        Map<String, Object> result;
        Map<String, Object> config = getTradeParams(context.getTransaction());
        try {
            result = retryIfNetworkException(OP_PRECREATE, config, build, retryTimes);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            logger.error("failed to call lakala open query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            logger.error("encountered ioex in lakala open pay", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        String resultCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        if (!StringUtils.isEmpty(resultCode) && resultCode.equals(LakalaConstants.SUCCESS_CODE)) {
            setTransactionPrecreateSuccess(result, context);
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LAKALA_UNION_PAY_V3;
    }

    protected RequestV3Builder initGenerateOrderRequest(TransactionContext context) {
        RequestV3Builder requestBuilder = initCommonRequest(context);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> order = context.getOrder();
        String sn = BeanUtil.getPropString(order, Order.SN);
        requestBuilder.reqDataSet(BusinessV3Fields.TOTAL_AMOUNT, BeanUtil.getPropString(transaction, Transaction.EFFECTIVE_AMOUNT));
        requestBuilder.reqDataSet(BusinessV3Fields.ORDER_EFFICIENT_TIME, formatTimeString(System.currentTimeMillis() + orderExpiryTime));
        requestBuilder.reqDataSet(BusinessV3Fields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        requestBuilder.reqDataSet(BusinessV3Fields.SUPPORT_CANCEL, BusinessV3Fields.SUPPORT);
        requestBuilder.reqDataSet(BusinessV3Fields.SUPPORT_REFUND, BusinessV3Fields.SUPPORT);
        requestBuilder.reqDataSet(BusinessV3Fields.ORDER_INFO, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        requestBuilder.reqDataSet(BusinessV3Fields.VPOS_ID, BeanUtil.getPropString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID));
        requestBuilder.reqDataSet(BusinessV3Fields.CHANNEL_ID, BeanUtil.getPropString(tradeParams, BusinessV3Fields.CHANNEL_ID));
        requestBuilder.reqDataSet(BusinessV3Fields.OUT_ORDER_NO, sn);
        return requestBuilder;
    }

    protected RequestV3Builder initRefundRequest(TransactionContext context) {
        RequestV3Builder builder = initCommonRequest(context);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> order = context.getOrder();
        builder.reqDataSet(BusinessV3Fields.TERM_NO, BeanUtil.getPropString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO));
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.reqDataSet(BusinessV3Fields.REFUND_AMOUNT, BeanUtil.getPropString(transaction, Transaction.EFFECTIVE_AMOUNT));
        //交易成功后TRADE_NO存在order里
        Map<String, Object> extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);
        String tradeNo = BeanUtil.getPropString(order, Order.TRADE_NO);
        if (MapUtil.getBooleanValue(extraOutFields, Transaction.IS_DEPOSIT, false)) {
            //预授权退款需要使用预授权完成时的订单信息
            Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME));
            tradeNo = BeanUtil.getPropString(payOrConsumerTransaction, Transaction.TRADE_NO);
        }
        builder.reqDataSet(BusinessV3Fields.ORIGIN_LOG_NO, tradeNo);
        builder.reqDataSet(BusinessV3Fields.ORIGIN_TRADE_DATE, getTradeTime(context));
        builder.reqDataSet(BusinessV3Fields.ORIGIN_BIZ_TYPE, isLakalaWildCard(transaction) ? BusinessV3Fields.ORIGIN_BIZ_TYPE_WILD_CARD : BusinessV3Fields.ORIGIN_BIZ_TYPE_BANKCARD);
        builder.reqDataSet(BusinessV3Fields.ORIGIN_CARD_NO, BeanUtil.getPropString(order, Order.BUYER_UID));
        return builder;
    }

    protected RequestV3Builder initRefundQueryRequest(TransactionContext context) {
        RequestV3Builder builder = initCommonRequest(context);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        builder.reqDataSet(BusinessV3Fields.TERM_NO, BeanUtil.getPropString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO));
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.reqDataSet(BusinessV3Fields.ORIGIN_TRADE_DATE, getRefundDate(context));
        builder.reqDataSet(BusinessV3Fields.ORIGIN_BIZ_TYPE, isLakalaWildCard(transaction) ? BusinessV3Fields.ORIGIN_BIZ_TYPE_WILD_CARD : BusinessV3Fields.ORIGIN_BIZ_TYPE_BANKCARD);
        builder.reqDataSet(BusinessV3Fields.ORIGIN_OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        return builder;
    }

    protected RequestV3Builder initDepositQueryOrderRequest(TransactionContext context) {
        RequestV3Builder builder = initCommonRequest(context);
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, BeanUtil.getPropString(context.getTransaction(), Transaction.TSN));
        return builder;
    }

    protected RequestV3Builder initCommonRequest(TransactionContext context) {
        RequestV3Builder requestBuilder = new RequestV3Builder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        requestBuilder.reqDataSet(BusinessV3Fields.MERCHANT_NO, BeanUtil.getPropString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID));
        requestBuilder.reqSet(BusinessV3Fields.REQ_TIME, formatTimeString(System.currentTimeMillis()));
        requestBuilder.reqSet(BusinessV3Fields.VERSION, LakalaConstants.VERSION_3);
        return requestBuilder;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String responseCode = BeanUtil.getPropString(result, ResponseFields.CODE);
        String message = BeanUtil.getPropString(result, ResponseV3Fields.MSG);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseV3Fields.CODE, responseCode);//返回状态码
        map.put(ResponseV3Fields.MSG, message);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, LakalaConstants.SUCCESS_CODE.equals(responseCode), responseCode, message);
    }

    protected void setTransactionPrecreateSuccess(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        wapRequest.put(ResponseV3Fields.COUNTER_URL, BeanUtil.getNestedProperty(result, COUNTER_URL));
        wapRequest.put(ResponseV3Fields.PAY_ORDER_NO, BeanUtil.getNestedProperty(result, PAY_ORDER_NO_KEY));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
    }

    protected void setTransactionRefundSuccess(String tradeNo, Long finishTime, long refundAmount, TransactionContext context) {
        //需要存下log_no 查询订单时匹配
        Map<String, Object> transaction = context.getTransaction();
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        transaction.put(Transaction.PAID_AMOUNT, effectiveAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, finishTime);
        transaction.put(Transaction.TRADE_NO, tradeNo);
        //退款后需要原订单的批次号跟流水号
        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLong(context.getOrder(), DaoConstants.CTIME));
        Map<String, Object> payOrConsumerExtraOutFields = (Map<String, Object>) payOrConsumerTransaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        String originBillNo = (String) payOrConsumerExtraOutFields.getOrDefault(Transaction.BATCH_BILL_NO, "");
        String originTraceNo = (String) payOrConsumerExtraOutFields.getOrDefault(Transaction.SYS_TRACE_NO, "");
        Map<String, Object> currentExtraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        List<Map<String, Object>> payments = (List<Map<String, Object>>) payOrConsumerExtraOutFields.get(Transaction.PAYMENTS);
        if (payments != null && !payments.isEmpty()) {
            Map<String, Object> payment = payments.get(0);
            payment.put(Transaction.PAYMENT_AMOUNT, refundAmount);
            currentExtraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (!StringUtils.isEmpty(originBillNo)) {
            currentExtraOutFields.put(Transaction.BATCH_BILL_NO, originBillNo);
        }
        if (!StringUtils.isEmpty(originTraceNo)) {
            currentExtraOutFields.put(Transaction.SYS_TRACE_NO, originTraceNo);
        }
        transaction.put(Transaction.EXTRA_OUT_FIELDS, currentExtraOutFields);
    }

    public static boolean isLakalaWildCard(Map<String, Object> transaction) {
        boolean isWildCard = false;
        Map<String, Object> extraOutFileds = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> payments = (List<Map<String, Object>>) MapUtil.getObject(extraOutFileds, Transaction.PAYMENTS);
        if (com.wosai.pantheon.util.CollectionUtil.isNotEmpty(payments)) {
            for (Map<String, Object> payment : payments) {
                String originType = MapUtil.getString(payment, Payment.ORIGIN_TYPE);
                if (WILD_CARD_BUSI_MODE_MAP.contains(originType)) {
                    isWildCard = true;
                    break;
                }
            }
        }
        return isWildCard;
    }

    protected Map<String, Object> retryIfNetworkException(String logFlag, Map<String, Object> config, Map<String, Object> request, int times) throws MpayException, MpayApiNetworkError {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), logFlag);
        String appId = MapUtil.getString(config, TransactionParam.LAKALA_UNION_PAY_OPEN_APP_ID);
        String privateKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.LAKALA_PRIVATE_KEY));
        String serialNo = MapUtil.getString(config, BusinessV3Fields.SERIA_NO);
        MpayApiNetworkError tex = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(url, appId, serialNo, privateKey, request);
            } catch (MpayApiNetworkError ex) {
                tex = ex;
                logger.warn("encountered ioex in lakala open {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw tex;
    }

    private String getTradeTime(TransactionContext context) {
        Map<String, Object> transaction = getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME));
        return formatTimeString(BeanUtil.getPropLong(transaction, Transaction.CHANNEL_FINISH_TIME));
    }

    private String getRefundDate(TransactionContext context) {
        return formatTimeString(BeanUtil.getPropLong(context.getTransaction(), DaoConstants.CTIME)).substring(0, 8);
    }

    /**
     * 支付查单状态的上下文处理器
     */
    final class PayQueryResolver extends QueryResolver {

        @Override
        String handler(TransactionContext context, Map<String, Object> config) {
            Map<String, Object> order = context.getOrder();
            Map<String, Object> transaction = context.getTransaction();
            RequestV3Builder builder = initCommonRequest(context);
            Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT, new HashMap());
            String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP);
            boolean isPhonePosFlag = Objects.equals(tradeApp, ApolloConfigurationCenterUtil.getPhonePosTradeAppId());
            if (isPhonePosFlag) {
                // 拉卡拉手机刷卡交易
                builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, BeanUtil.getPropString(context.getTransaction(), Transaction.ORDER_SN));
            }else {
                // 消费类交易需要上送订单号，否则会查询不出来
                builder.reqDataSet(BusinessV3Fields.OUT_ORDER_NO, BeanUtil.getPropString(context.getTransaction(), Transaction.ORDER_SN));
            }
            Map<String, Object> result;
            try {
                result = retryIfNetworkException(OP_QUERY, config, builder.build(), retryTimes);
            } catch (MpayException ex) {
                setTransactionContextErrorInfo(context, OP_QUERY, ex);
                logger.error("failed to call lakala open query", ex);
                return Workflow.RC_IOEX;
            } catch (MpayApiNetworkError e) {
                setTransactionContextErrorInfo(context, OP_QUERY, e);
                return Workflow.RC_IN_PROG;
            }
            setTransactionContextErrorInfo(result, context, OP_QUERY);
            String returnCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
            if (!LakalaConstants.SUCCESS_CODE.equals(returnCode)) {
                return Workflow.RC_ERROR;
            }
            // 获取返回结果中的流水信息
            Map<String, Object> respData = MapUtil.getMap(result, ResponseV3Fields.RESP_DATA);
            Object orderInfoObj = MapUtil.getObject(respData, ResponseV3Fields.ORDER_TRADE_INFO_LIST);
            List<Map<String, Object>> orderInfoList = null;
            if (orderInfoObj instanceof List) {
                orderInfoList = (List) orderInfoObj;
            }
            if (CollectionUtils.isEmpty(orderInfoList)) {
                return Workflow.RC_IN_PROG;
            }
            Map<String, Object> matchOrderTradeInfo = null;
            Map<String, Object> subOrderTradeInfo = null;
            // 消费交易上送的是订单号，需要根据订单列表中类型进行匹配
            for (Map<String, Object> orderInfo : orderInfoList) {
                String tradeType = MapUtil.getString(orderInfo, ResponseV3Fields.TRADE_TYPE);
                if (isPhonePosFlag && TradeTypeEnum.TRAN_COMBINE_MUSTER.getValue().equals(tradeType)) {
                    matchOrderTradeInfo = orderInfo;
                } else if (isPhonePosFlag && TradeTypeEnum.TRAN_COMBINE_SUB.getValue().equals(tradeType)) {
                    subOrderTradeInfo = orderInfo;
                } else if (TradeTypeEnum.TRAN_CONSUME.getValue().equals(tradeType)) {
                    matchOrderTradeInfo = orderInfo;
                }
            }
            if (MapUtil.isEmpty(matchOrderTradeInfo)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            String tradeStatus = MapUtil.getString(matchOrderTradeInfo, ResponseV3Fields.TRADE_STATUS);
            if (ResponseV3Fields.TradeStatusEnum.SUCCESS.getValue().equals(tradeStatus)) {
                String payerAccountNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.PAYER_ACCOUNT_NO);
                String tradeNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_NO);
                String batchNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.CLIENT_BATCH_NO);
                String seqNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.CLIENT_SEQ_NO);
                String tradeTime = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_TIME);
                String accTradeNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.ACC_TRADE_NO);
                String accType = MapUtil.getString(matchOrderTradeInfo, ResponseV3Fields.ACC_TYPE);
                Long payerAmount = MapUtil.getLongValue(matchOrderTradeInfo, ResponseV3Fields.PAYER_AMOUNT);
                if(isPhonePosFlag){
                    payerAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                }
                String busiMode = MapUtil.getString(matchOrderTradeInfo, ResponseV3Fields.BUSI_MODE);

                Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
                if (extraOutFields == null) {
                    extraOutFields = new HashMap<>();
                    transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                }
                if (!StringUtils.isEmpty(batchNo)) {
                    extraOutFields.put(Transaction.BATCH_BILL_NO, batchNo);
                }
                if (!StringUtils.isEmpty(seqNo)) {
                    extraOutFields.put(Transaction.SYS_TRACE_NO, seqNo);
                }
                if (!StringUtils.isEmpty(tradeNo)) {
                    if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                        order.put(Order.TRADE_NO, tradeNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
                        transaction.put(Transaction.TRADE_NO, tradeNo);
                    }
                }
                if (isPhonePosFlag && subOrderTradeInfo != null) {
                    String logNo = BeanUtil.getPropString(subOrderTradeInfo, ResponseV3Fields.LOG_NO);
                    if (!StringUtils.isEmpty(logNo)) {
                        extraOutFields.put(Transaction.LOG_NO, logNo);
                    }
                }
                if (!StringUtils.isEmpty(payerAccountNo)) {
                    //保存下 退款时需要退款方银行卡号(脱敏)
                    if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_UID))) {
                        order.put(Order.BUYER_UID, payerAccountNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                        order.put(Order.BUYER_LOGIN, payerAccountNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                        transaction.put(Transaction.BUYER_UID, payerAccountNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
                        transaction.put(Transaction.BUYER_LOGIN, payerAccountNo);
                    }
                }
                if (!StringUtils.isEmpty(accTradeNo)) {
                    extraOutFields.put(Transaction.CHANNEL_TRADE_NO, accTradeNo);
                }
                if (!StringUtils.isEmpty(tradeTime)) {
                    try {
                        transaction.put(Transaction.CHANNEL_FINISH_TIME, NEW_VERSION_QUERY_FORMAT.parse(tradeTime).getTime());
                    } catch (ParseException e) {
                        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                    }
                } else {
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                }
                // 设置流水payment
                List<Map<String, Object>> transactionPayments = new ArrayList<>();
                extraOutFields.put(Transaction.PAYMENTS, transactionPayments);
                Map<String, Object> transactionPayment = CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, ACC_TYPE_MAPPING.getOrDefault(accType, Payment.TYPE_BANKCARD_CREDIT),
                        Transaction.PAYMENT_ORIGIN_TYPE, busiMode,
                        Transaction.PAYMENT_AMOUNT, payerAmount
                );
                transactionPayments.add(transactionPayment);
                String dccFlag = MapUtil.getString(respData, ResponseV3Fields.DCC_FLG);
                if(isPhonePosFlag && !Objects.equals(dccFlag,DccFlgEnum.EDC.getCode())){
                    logger.error("lakala phone pos trade response type error.tsn:{}", MapUtil.getString(transaction, Transaction.TSN));
                }
                if (!StringUtils.isEmpty(dccFlag)) {
                    extraOutFields.put(Transaction.WILD_CARD_TYPE, DCC_FLG_MAP.getOrDefault(dccFlag, DccFlgEnum.EDC.getMeaning()));
                }
                return Workflow.RC_PAY_SUCCESS;
            } else if(ResponseV3Fields.TradeStatusEnum.FAIL.getValue().equals(tradeStatus)) {
                return Workflow.RC_ERROR;
            }
            return Workflow.RC_IN_PROG;
        }
    }

    /**∂
     * 支付查单状态的上下文处理器
     */
    final class DepositQueryResolver extends QueryResolver {

        @Override
        String handler(TransactionContext context, Map<String, Object> config) {
            Map<String, Object> result;
            RequestV3Builder request = initDepositQueryOrderRequest(context);
            Map<String, Object> order = context.getOrder();
            Map<String, Object> transaction = context.getTransaction();
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            String inProg = type == Transaction.TYPE_DEPOSIT_FREEZE ? Workflow.RC_IN_PROG : Workflow.RC_RETRY;
            try {
                result = retryIfNetworkException(OP_DEPOSIT_QUERY, config, request.build(), retryTimes);
            } catch (MpayException ex) {
                setTransactionContextErrorInfo(context, OP_QUERY, ex);
                logger.error("failed to call lakala open query", ex);
                return Workflow.RC_IOEX;
            } catch (MpayApiNetworkError e) {
                setTransactionContextErrorInfo(context, OP_QUERY, e);
                return inProg;
            }
            setTransactionContextErrorInfo(result, context, OP_QUERY);
            String returnCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
            if (!LakalaConstants.SUCCESS_CODE.equals(returnCode)) {
                return Workflow.RC_ERROR;
            }
            boolean isDeposit = MapUtil.getBooleanValue((Map)transaction.get(Transaction.EXTRA_OUT_FIELDS), Transaction.IS_DEPOSIT, false);
            //获取返回结果中的流水信息
            Map<String, Object> respData = MapUtil.getMap(result, ResponseV3Fields.RESP_DATA);
            Object orderInfoObj = MapUtil.getObject(respData, ResponseV3Fields.ORDER_TRADE_INFO_LIST);
            List<Map<String, Object>> orderInfoList = null;
            if (orderInfoObj instanceof List) {
                orderInfoList = (List) orderInfoObj;
            }
            if (CollectionUtils.isEmpty(orderInfoList)) {
                return !isDeposit ? Workflow.RC_ERROR : inProg;
            }
            Map<String, Object> matchOrderTradeInfo = orderInfoList.get(0);
            if (MapUtil.isEmpty(matchOrderTradeInfo)) {
                return !isDeposit ? Workflow.RC_TRADE_CANCELED : inProg;
            }

            // 流水状态匹配
            String tradeStatus = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_STATUS);
            if (ResponseV3Fields.TradeStatusEnum.SUCCESS.getValue().equals(tradeStatus)) {
                String payerAccountNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.PAYER_ACCOUNT_NO);
                String tradeNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_NO);
                String refernumber = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_REF_NO);
                String tradeTime = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_TIME);
                String accType = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.ACC_TYPE);
                String batchNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.CLIENT_BATCH_NO);
                String seqNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.CLIENT_SEQ_NO);
                Long payerAmount = BeanUtil.getPropLong(matchOrderTradeInfo, ResponseV3Fields.PAYER_AMOUNT);
                Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
                if (extraOutFields == null) {
                    extraOutFields = new HashMap<>();
                    transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                }

                if (!StringUtils.isEmpty(payerAccountNo)) {
                    if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_UID))) {
                        order.put(Order.BUYER_UID, payerAccountNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                        order.put(Order.BUYER_LOGIN, payerAccountNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                        transaction.put(Transaction.BUYER_UID, payerAccountNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
                        transaction.put(Transaction.BUYER_LOGIN, payerAccountNo);
                    }
                }
                if (!StringUtils.isEmpty(tradeNo)) {
                    if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                        order.put(Order.TRADE_NO, tradeNo);
                    }
                    if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
                        transaction.put(Transaction.TRADE_NO, tradeNo);
                    }
                }
                if (!StringUtils.isEmpty(refernumber)) {
                    extraOutFields.put(Transaction.REFER_NUMBER, refernumber);
                }
                if (!StringUtils.isEmpty(tradeTime)) {
                    try {
                        transaction.put(Transaction.CHANNEL_FINISH_TIME, NEW_VERSION_QUERY_FORMAT.parse(tradeTime).getTime());
                    } catch (ParseException e) {
                        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                    }
                } else {
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                }
                if (isDeposit) {
                    String tradeRefNo = BeanUtil.getPropString(matchOrderTradeInfo, ResponseV3Fields.TRADE_REF_NO);
                    String autCod = BeanUtil.getPropString(respData, ResponseV3Fields.AUT_COD);
                    extraOutFields.put(Transaction.AUTH_NO, autCod);
                    extraOutFields.put(Transaction.REFER_NUMBER, tradeRefNo);
                    if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                        extraOutFields.put(Transaction.CONSUME_TIME, MapUtil.getLongValue(transaction, DaoConstants.CTIME));
                    }
                }
                List<Map<String, Object>> transactionPayments = new ArrayList<>();
                if (!StringUtils.isEmpty(batchNo)) {
                    extraOutFields.put(Transaction.BATCH_BILL_NO, batchNo);
                }
                if (!StringUtils.isEmpty(seqNo)) {
                    extraOutFields.put(Transaction.SYS_TRACE_NO, seqNo);
                }
                extraOutFields.put(Transaction.PAYMENTS, transactionPayments);
                transactionPayments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, ACC_TYPE_MAPPING.getOrDefault(accType, Payment.TYPE_BANKCARD_CREDIT),
                        Transaction.PAYMENT_ORIGIN_TYPE, accType,
                        Transaction.PAYMENT_AMOUNT, payerAmount
                ));
                // 设置币种转换方式
                String dccFlag = MapUtil.getString(respData, ResponseV3Fields.DCC_FLG);
                if (!StringUtils.isEmpty(dccFlag)) {
                    extraOutFields.put(Transaction.WILD_CARD_TYPE, DCC_FLG_MAP.getOrDefault(dccFlag, DccFlgEnum.EDC.getMeaning()));
                }
                return QUERY_RC_SUCCESS.get(type);
            } else if(ResponseV3Fields.TradeStatusEnum.FAIL.getValue().equals(tradeStatus)) {
                return Workflow.RC_ERROR;
            }
            //其他状态重新查询
            return inProg;
        }
    }

    /**
     * 退款状态的上下文查询处理器
     */
    final class RefundQueryResolver extends QueryResolver {
        private String refundListField = "refund_list";
        private String tradeState = "trade_state";

        @Override
        String handler(TransactionContext context, Map<String, Object> config) {
            RequestV3Builder request = initRefundQueryRequest(context);
            Map<String, Object> result;
            try {
                result = retryIfNetworkException(OP_REFUND_QUERY, config, request.build(), retryTimes);
            } catch (MpayException ex) {
                setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
                logger.error("failed to call lakala open query", ex);
                return Workflow.RC_IOEX;
            } catch (MpayApiNetworkError e) {
                setTransactionContextErrorInfo(context, OP_REFUND_QUERY, e);
                return Workflow.RC_RETRY;
            }
            setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
            String returnCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
            if (LakalaConstants.SUCCESS_CODE.equals(returnCode)) {
                Object refundList = BeanUtil.getNestedProperty(result, ResponseV3Fields.RESP_DATA + "." + refundListField);
                if (refundList != null && refundList instanceof List) {
                    Object o = ((List) refundList).get(0);
                    if (o != null && o instanceof Map) {
                        String tradeStatus = BeanUtil.getPropString(o, tradeState);
                        if (!StringUtils.isEmpty(tradeStatus)) {
                            if (tradeStatus.equals(LakalaConstants.TRADE_STATE_SUCCESS)) {
                                String logNo = BeanUtil.getPropString(o, ResponseV3Fields.LOG_NO);
                                String tradeTime = BeanUtil.getPropString(o, ResponseV3Fields.TRADE_TIME);
                                long refundAmount = BeanUtil.getPropLong(o, ResponseV3Fields.REFUND_AMOUNT);
                                long finishTime = StringUtils.isEmpty(tradeTime) ? System.currentTimeMillis() : parseTimeString(tradeTime);
                                setTransactionRefundSuccess(logNo, finishTime,refundAmount, context);
                                return Workflow.RC_REFUND_SUCCESS;
                            }
                        }
                    }
                }
            }
            return Workflow.RC_RETRY;
        }
    }

    /**
     * 避免if else 策略+字典
     */
    abstract class QueryResolver {
        abstract String handler(TransactionContext context, Map<String, Object> tradeParams);
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        wapRequest.put(ResponseV3Fields.PAY_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        return Workflow.RC_CREATE_SUCCESS;
    }

    public String depositCancel(TransactionContext context) {
        forceReturn(context);
        return QUERY_RESOLVER_MAP.get(Transaction.TYPE_DEPOSIT_CANCEL).handler(context, getTradeParams(context.getTransaction()));
    }

    public String depositConsume(TransactionContext context) {
        forceReturn(context);
        return QUERY_RESOLVER_MAP.get(Transaction.TYPE_DEPOSIT_CONSUME).handler(context, getTradeParams(context.getTransaction()));
    }

    @Override
    public String depositQuery(TransactionContext context) {
        forceReturn(context);
        return QUERY_RESOLVER_MAP.get(Transaction.TYPE_DEPOSIT_FREEZE).handler(context, getTradeParams(context.getTransaction()));
    }

    private void forceReturn(TransactionContext context) {
        if (!context.isForceReturn()) {
            context.setForceReturn(true);
        }
    }
}
