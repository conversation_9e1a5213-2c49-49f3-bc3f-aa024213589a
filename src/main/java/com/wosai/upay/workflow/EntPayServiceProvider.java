package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.entpay.*;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

/***
 * @ClassName: EntPayServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/20 5:51 PM
 */
public class EntPayServiceProvider extends AbstractServiceProvider {

    @Resource
    private EntPayClient entPayClient;

    public static final Logger logger = LoggerFactory.getLogger(EntPayServiceProvider.class);
    public static final String NAME = "provider.entpay";
    protected static final int TIME_EXPIRE = 24 * 60 * 60 * 1000; // 订单默认过期时间设定为24小时
    private static final String QRCODE_PAY_PATH = "/qrcode-pay";    //二维码支付
    private static final String MP_PAY_PATH = "/mp-pay";            //小程序支付
    private static final String APP_PAY_PATH = "/app-pay";          //app支付
    private static final String H5_PAY_PATH = "/h5-pay";            //h5支付
    private static final int NOTIFY_URL_LIMIT = 256;
    private String notifyHost;
    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(EntPayConstants.DATE_TIME_FORMAT_WITH_T);

    public EntPayServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(EntPayConstants.DATE_TIME_FORMAT_WITH_T);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_ENTPAY;
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_BANKACCOUNT == payway) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ENTPAY_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("微企付通道暂不支持被扫支付");
    }

    @Override
    public String cancel(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);

        EntPayRequestBuilder builder = new EntPayRequestBuilder();
        builder.set(EntPayBusinessFields.CLOSE_REASON, "微企付订单撤销");

        //解析extended透传给支付通道，包括关单原因等
        carryOverExtendedParams(extendedParams, builder, EntPayBusinessFields.CANCEL_ALLOWED_FIELDS);

        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);
        String paymentId = MapUtil.getString(context.getOrder(), Order.TRADE_NO);
        gatewayUrl = String.format(gatewayUrl, paymentId);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, EntPayClient.METHOD_POST, builder.build(), tradeParams, 1, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call entpay cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        //响应码
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);
        //请求成功
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
            String status = MapUtil.getString(result, EntPayResponseFields.STATUS);
            if (EntPayConstants.CLOSED.equals(status)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String query(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        gatewayUrl = String.format(gatewayUrl, orderSn);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, EntPayClient.METHOD_GET, null, tradeParams, 1, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call entpay query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);

        //响应码
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        //请求成功
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
            String payStatus = MapUtil.getString(result, EntPayResponseFields.PAY_STATUS);
            if (EntPayConstants.SUCCEEDED.equals(payStatus)) {
                //支付成功
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, EntPayResponseFields.PAY_TIME)));
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            } else if (EntPayConstants.PROCESSING.equals(payStatus) || EntPayConstants.BANK_ACCEPTED.equals(payStatus)) {
                return Workflow.RC_IN_PROG;
            } else if (EntPayConstants.REVOKED.equals(payStatus) || EntPayConstants.CLOSED.equals(payStatus)) {
                return Workflow.RC_TRADE_CANCELED;
            }
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        }

        //商户企业信息
        String entId = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_ENT_ID);

        EntPayRequestBuilder builder = new EntPayRequestBuilder();
        //退款商户企业ID
        builder.set(EntPayBusinessFields.ENT_ID, entId);
        //平台退款单号
        builder.set(EntPayBusinessFields.OUT_REFUND_ID, MapUtil.getString(transaction, Transaction.TSN));
        //平台支付单号
        builder.set(EntPayBusinessFields.OUT_PAYMENT_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //原支付订单支付金额
        builder.set(EntPayBusinessFields.TOTAL_AMOUNT, MapUtil.getLongValue(context.getOrder(), Order.EFFECTIVE_TOTAL));
        //退款金额
        builder.set(EntPayBusinessFields.REFUND_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        //回调地址，退款异步通知回调地址(退款回调不处理)
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String gatewayNotifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.set(EntPayBusinessFields.SERVER_NOTIFY_URL, gatewayNotifyUrl);
        builder.set(EntPayBusinessFields.REFUND_REASON, "微企付订单退款");

        //解析extended透传给支付通道，包括退款原因等
        carryOverExtendedParams(extendedParams, builder, EntPayBusinessFields.REFUND_ALLOWED_FIELDS);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), EntPayClient.METHOD_POST, builder.build(), tradeParams, 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call entpay refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_REFUND);

        //响应码
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);
        //请求成功
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
            setTradeNoBuyerInfoIfExists(result, context);
            String status = MapUtil.getString(result, EntPayResponseFields.STATUS);
            if (EntPayConstants.REFUND_SUCCEEDED.equals(status) || EntPayConstants.ACCEPTED.equals(status)) {
                //退款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                context.getTransaction().put(Transaction.TRADE_NO, MapUtil.getString(result, EntPayResponseFields.REFUND_ID));//渠道退款流水号
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            } else if (EntPayConstants.INIT.equals(status) || EntPayConstants.CANCELLING.equals(status)) {
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return Workflow.RC_RETRY;
            }
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String refundQuery(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_QUERY);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        gatewayUrl = String.format(gatewayUrl, tsn);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, EntPayClient.METHOD_GET, null, tradeParams, 1, OP_REFUND_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call entpay refund query", ex);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);

        //响应码
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);
        //请求成功
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
            String status = MapUtil.getString(result, EntPayResponseFields.STATUS);
            if (EntPayConstants.REFUND_SUCCEEDED.equals(status)) {
                //支付成功
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, EntPayResponseFields.SUCCEEDED_TIME)));
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            } else if (EntPayConstants.FAILED.equals(status)) {
                return Workflow.RC_ERROR;
            } else if (EntPayConstants.INIT.equals(status) || EntPayConstants.ACCEPTED.equals(status) || EntPayConstants.CANCELLING.equals(status)) {
                return Workflow.RC_RETRY;
            }
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);

        //商户企业信息
        String entId = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_ENT_ID);
        String entName = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_ENT_NAME);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);

        EntPayRequestBuilder builder = new EntPayRequestBuilder();
        //平台支付单号
        builder.set(EntPayBusinessFields.OUT_PAYMENT_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //支付金额
        builder.set(EntPayBusinessFields.AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        //微企付收款方信息
        Map<String, Object> payee = new HashMap<>();
        payee.put(EntPayBusinessFields.ENT_ID, entId);
        payee.put(EntPayBusinessFields.ENT_NAME, entName);
        builder.set(EntPayBusinessFields.PAYEE, payee);
        //币种
        builder.set(EntPayBusinessFields.CURRENCY, TransactionParam.UPAY_DEFAULT_CURRENCY_CNY);
        //过期时间
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + TIME_EXPIRE);
        builder.set(EntPayBusinessFields.EXPIRE_TIME, expireDate);
        //附言
        builder.set(EntPayBusinessFields.MEMO, MapUtil.getString(transaction, Transaction.SUBJECT));
        //支付异步回调信息
        //通知地址
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String gatewayNotifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.set(EntPayBusinessFields.NOTIFY_URL, buildNotifyUrl(extendedParams, subPayway, gatewayNotifyUrl));

        String commonSwitch = MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.COMMON_SWITCH);
        if (UpayUtil.isEntPaySupportProfit(commonSwitch)) {
            //支持分账
            builder.set(EntPayBusinessFields.PROFIT_ALLOCATION_FLAG, EntPayConstants.API_PROFIT_ALLOCATION);
        }

        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        if (Order.SUB_PAYWAY_QRCODE == subPayway) {
            gatewayUrl += QRCODE_PAY_PATH;
        } else if (Order.SUB_PAYWAY_MINI == subPayway) {
            gatewayUrl += MP_PAY_PATH;
        } else if (Order.SUB_PAYWAY_APP == subPayway) {
            gatewayUrl += APP_PAY_PATH;
        } else if (Order.SUB_PAYWAY_H5 == subPayway) {
            gatewayUrl += H5_PAY_PATH;
        }
        //解析extended透传给支付通道，包括单品信息等
        carryOverExtendedParams(extendedParams, builder, EntPayBusinessFields.PRECREATE_ALLOWED_FIELDS);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, EntPayClient.METHOD_POST, builder.build(), tradeParams, 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call entpay precarete", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        //请求成功
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
            String paymentId = MapUtil.getString(result, EntPayResponseFields.PAYMENT_ID);
            return redirect(context, paymentId);
        } else {
            setTransactionContextErrorInfo(result, context, OP_PRECREATE);
            return Workflow.RC_TRADE_CANCELED;
        }
    }

    public String redirect(TransactionContext context, String paymentId) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        EntPayRequestBuilder builder = new EntPayRequestBuilder();
        //平台支付单号
        builder.set(EntPayBusinessFields.PAYMENT_ID, paymentId);

        String redirectUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REDIRECT);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(redirectUrl, EntPayClient.METHOD_POST, builder.build(), tradeParams, 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call entpay precarete redirect", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return Workflow.RC_IOEX;
        }

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        //响应码
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);

        //请求成功
        if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
            Map<String, Object> payRequest = new HashMap<>();
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int subPayway = MapUtil.getIntValue(transaction, Order.SUB_PAYWAY);
            if (Order.SUB_PAYWAY_QRCODE == subPayway) {
                payRequest = MapUtil.getMap(result, EntPayResponseFields.STATIC_QRCODE);
            } else if (Order.SUB_PAYWAY_MINI == subPayway) {
                payRequest = MapUtil.getMap(result, EntPayResponseFields.MINI_PROGRAM);
            } else if (Order.SUB_PAYWAY_APP == subPayway) {
                payRequest = MapUtil.getMap(result, EntPayResponseFields.APP_H5);
            } else if (Order.SUB_PAYWAY_H5 == subPayway) {
                payRequest = MapUtil.getMap(result, EntPayResponseFields.WX_H5);
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, payRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();

        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT != type) {
            return null;
        }

        //默认直接再查询一遍
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    private Map<String, Object> buildNotifyUrl(Map<String, Object> extended, int subPayway, String gatewayNotifyUrl) {

        Map<String, Object> notifyUrl = new HashMap<>();
        notifyUrl.put(EntPayBusinessFields.SERVER_NOTIFY_URL, gatewayNotifyUrl);
        if (Order.SUB_PAYWAY_MINI == subPayway || Order.SUB_PAYWAY_APP == subPayway || Order.SUB_PAYWAY_H5 == subPayway) {
            Map<String, Object> frontCallBackUrl = MapUtil.getMap(extended, EntPayBusinessFields.FRONT_CALLBACK_URL);
            if (MapUtil.isNotEmpty(frontCallBackUrl)) {
                notifyUrl.put(EntPayBusinessFields.FRONT_CALLBACK_URL, frontCallBackUrl);
            }
        }
        return notifyUrl;
    }

    private void carryOverExtendedParams(Map<String, Object> extended, EntPayRequestBuilder builder, Set<String> allowedFields) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();
            if ((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key))) {
                continue;
            }
            if (value != null) {
                builder.set(key, value);
            }
        }
    }

    protected Map<String, Object> retryIfNetworkException(String requestUrl, String method, Map<String, Object> request, Map<String, Object> tradeParams, int times, String logFlag) throws Exception {
        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_PRIVATE_KEY);
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_PUBLIC_KEY);
        //证书序列号
        String platformPrivateCertSerialNo = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_PRIVATE_SERIAL_NO);
        String tbepSerialNumber = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_PUBLIC_SERIAL_NO);
        //微企付平台的平台帐号
        String platformId = MapUtil.getString(tradeParams, TransactionParam.ENTPAY_PLATFORM_ID);

        return retryIfNetworkException(() -> entPayClient.call(requestUrl, method, request, platformId, platformPrivateCertSerialNo, getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), tbepSerialNumber), logger, times, logFlag, "entpay");
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);//业务返回码
        map.put(BusinessV2Fields.CODE, httpCode);//返回状态码
        boolean isSuccess = HttpConstant.HTTP_CODE_SUCCESS == httpCode;

        String errorMsg = "";
        if (!isSuccess) {
            Map<String, Object> error = MapUtil.getMap(result, EntPayResponseFields.ERROR);
            errorMsg = MapUtil.getString(error, EntPayResponseFields.ERROR_DESC);
            Map<String, Object> detail = MapUtil.getMap(error, EntPayResponseFields.ERROR_DETAIL);
            String path = MapUtil.getString(detail, EntPayResponseFields.ERROR_DETAIL_PATH);
            String msg = MapUtil.getString(detail, EntPayResponseFields.ERROR_DETAIL_MSG);
            if (!StringUtil.empty(path)) {
                errorMsg   = String.format("%s %s", errorMsg, path);
            }
            if (!StringUtil.empty(msg)) {
                errorMsg   = String.format("%s %s", errorMsg, msg);
            }
            map.put(BusinessV2Fields.MSG, errorMsg);
        }

        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, String.valueOf(httpCode), errorMsg);
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        //微企付付款方信息
        Map<String, Object> payer = MapUtil.getMap(result, EntPayResponseFields.PAYER);
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            //微企付openId，pay_status为SUCCEEDED且非分享付时返回
            String payerId = MapUtil.getString(payer, EntPayResponseFields.PAYER_ID);
            if (!StringUtil.empty(payerId)) {
                transaction.put(Transaction.BUYER_UID, payerId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            //微企付openId，pay_status为SUCCEEDED且非分享付时返回
            String payerId = MapUtil.getString(payer, EntPayResponseFields.PAYER_ID);
            if (!StringUtil.empty(payerId)) {
                transaction.put(Transaction.BUYER_LOGIN, payerId);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            //微企付支付单号
            String paymentId = MapUtil.getString(result, EntPayResponseFields.PAYMENT_ID);
            if (!StringUtil.empty(paymentId)) {
                transaction.put(Transaction.TRADE_NO, paymentId);
            }
        }
    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        //支付金额
        long buyerPaidAmount = MapUtil.getLongValue(result, EntPayResponseFields.AMOUNT);
        if (MapUtil.getLongValue(transaction, Transaction.PAID_AMOUNT, 0) == 0) {
            transaction.put(Transaction.PAID_AMOUNT, buyerPaidAmount);
        }
        if (MapUtil.getLongValue(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0) {
            transaction.put(Transaction.RECEIVED_AMOUNT, buyerPaidAmount);
        }

        //微企付付款方信息
        Map<String, Object> payer = MapUtil.getMap(result, EntPayResponseFields.PAYER);
        if (MapUtil.isNotEmpty(payer)) {
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYER_INFO_PATH, payer);
        }

        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, getEntPayPayments(result));
    }

    private List<Map<String, Object>> getEntPayPayments(Map<String, Object> result) {
        long buyerPaidAmount = MapUtil.getLongValue(result, EntPayResponseFields.AMOUNT);
        List<Map<String, Object>> payments = new ArrayList<>();
        //微企付付款方信息
        Map<String, Object> payer = MapUtil.getMap(result, EntPayResponseFields.PAYER);
        //付款银行
        String payerBankType = MapUtil.getString(payer, EntPayResponseFields.PAYER_BANK_TYPE);
        Map<String, Object> payment;
        if (!StringUtil.empty(payerBankType)) {
            String type;
            if (payerBankType.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX)) {
                type = Payment.TYPE_BANKCARD_CREDIT;
            } else if (payerBankType.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX)) {
                type = Payment.TYPE_BANKCARD_DEBIT;
            } else {
                type = payerBankType.toUpperCase();
            }
            payment = MapUtil.hashMap(
                    Transaction.PAYMENT_TYPE, type,
                    Transaction.PAYMENT_ORIGIN_TYPE, payerBankType,
                    Transaction.PAYMENT_AMOUNT, buyerPaidAmount
            );
        } else {
            //此处无获取到付款方的信息，默认为银行转账
            payment = MapUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_BANKACCOUNT,
                    Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_BANKACCOUNT,
                    Transaction.PAYMENT_AMOUNT, buyerPaidAmount
            );
        }

        payments.add(payment);

        return payments;
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

}
