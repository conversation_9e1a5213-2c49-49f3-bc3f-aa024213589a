package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

import java.util.Map;

@ServiceProvicerPriority(priority = 2)
public class LklUnionPayWeixinWapOrMiniServiceProvider extends UnionPayWeixinWapOrMiniServiceProvider {
    public static final String NAME = "provider.lkl.unionpay.weixin.wapOrMini";
    private static final String DEFAULT_CERT_ID = "**********";
    
    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI)) ? true : false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LAKALA_UNION_PAY;
    }
    
    @Override
    public Map<String, Object> call(Map<String, Object> config, String serviceUrl, Map<String, Object> request,String opFlag) throws MpayException, MpayApiNetworkError {
        removeIllegalFields(request);
        return client.call(serviceUrl, MapUtil.getString(config, TransactionParam.SIGN_TYPE, WeixinClient.SIGN_TYPE_UNIONPAY), getPrivateKeyContent((String) config.get(TransactionParam.LAKALA_UNION_PAY_PRIVATE_KEY)), null, request);
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHANNEL_ID, config.get(TransactionParam.LAKALA_UNION_PAY_CHANNEL_ID));
        builder.set(ProtocolFields.SIGN_TYPE, MapUtil.getString(config, TransactionParam.SIGN_TYPE, WeixinConstants.SIGN_TYPE_RSA2));
        builder.set(ProtocolFields.CERT_ID, MapUtil.getString(config, TransactionParam.LAKALA_UNION_PAY_CERT_ID, DEFAULT_CERT_ID));
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_SUB_MCH_ID));

        //小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_MINI == subPayway){
            String miniSubAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            if(!StringUtil.empty(miniSubAppId)){
                builder.set(ProtocolFields.SUB_APP_ID, miniSubAppId);
            }
        }

        setTerminalInfo(context, MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), config, builder);
        return builder;
    }
}
