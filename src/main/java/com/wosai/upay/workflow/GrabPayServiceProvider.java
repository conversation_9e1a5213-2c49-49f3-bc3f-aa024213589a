package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.grabpay.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/***
 * @ClassName: GrabPayServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/9 2:24 PM
 */
public class GrabPayServiceProvider extends AbstractServiceProvider{
    public static final Logger logger = LoggerFactory.getLogger(GrabPayServiceProvider.class);

    public static final String NAME = "provider.grabpay";
    public static final String URL_MOCA = ".moca";

    @Autowired
    private GrabPayClient client;

    public GrabPayServiceProvider() {
        dateFormat =  new SafeSimpleDateFormat("E, dd MMM yyyy HH:mm:ss z");
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        return !Objects.isNull(tradeParams);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.GRABPAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        //根据币种区分越南和非越南
        String currency = getTradeCurrency(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        if (TransactionParam.UPAY_CURRENCY_VND.equals(currency)){
            url = ApolloConfigurationCenterUtil.getProviderGateway(Strings.concat(getName(), URL_MOCA), OP_PAY);
        }

        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PRIVATE_KEY);
        String partnerId = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PARTNER_ID);

        GrabPayRequestBuilder builder = getDefaultRequestBuilder(tradeParams, currency, GrabPayConstants.CONTENT_TYPE);
        //交易金额， 越南盾处理，上送单位为元
        String amount = MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT);
        if (TransactionParam.UPAY_CURRENCY_VND.equals(currency) && amount.length() > 2) {
            amount = amount.substring(0, amount.length() - 2);
        }
        builder.bodySet(GrabPayBusinessFields.AMOUNT, Integer.parseInt(amount));
        //交易流水号
        builder.bodySet(GrabPayBusinessFields.PARTNER_TX_ID, transaction.get(Transaction.ORDER_SN));
        //条码
        builder.bodySet(GrabPayBusinessFields.CODE, extraParams.get(Transaction.BARCODE));
        //附加信息 amountBreakdown - 金额明细
        builder.bodySet(GrabPayBusinessFields.ADDITIONAL_INFO, Collections.singletonList(GrabPayConstants.AMOUNT_BREAKDOWN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, GrabPayConstants.POST, builder.build(), partnerId, getPrivateKeyContent(privateKey), 1, OP_PAY);
        } catch (Exception e) {
            logger.error("failed to call grab pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //根据币种区分越南和非越南
        String currency = getTradeCurrency(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);
        if (TransactionParam.UPAY_CURRENCY_VND.equals(currency)){
            url = ApolloConfigurationCenterUtil.getProviderGateway(Strings.concat(getName(), URL_MOCA), OP_CANCEL);
        }

        url = url + "/" +  MapUtil.getString(transaction, Transaction.ORDER_SN) + "/" + OP_CANCEL;
        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PRIVATE_KEY);
        String partnerId = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PARTNER_ID);

        GrabPayRequestBuilder builder = getDefaultRequestBuilder(tradeParams, currency, GrabPayConstants.CONTENT_TYPE);
        //商户请求流水号
        builder.bodySet(GrabPayBusinessFields.ORIGINAL_PARTNER_TX_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, GrabPayConstants.PUT, builder.build(), partnerId, getPrivateKeyContent(privateKey), 1, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call grab cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }

        return buildCancelResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);

        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PRIVATE_KEY);
        String partnerId = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PARTNER_ID);

        //根据币种区分越南和非越南
        String currency = getTradeCurrency(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        if (TransactionParam.UPAY_CURRENCY_VND.equals(currency)){
            url = ApolloConfigurationCenterUtil.getProviderGateway(Strings.concat(getName(), URL_MOCA), OP_QUERY);
        }

        GrabPayRequestBuilder builder = getDefaultRequestBuilder(tradeParams, currency, GrabPayConstants.CONTENT_TYPE_GET);
        String opFlag = OP_QUERY;

        //查询类型 交易查询：P2M ；退款查询：Refund
        builder.bodySet(GrabPayBusinessFields.TX_TYPE, GrabPayConstants.P2M);
        if (Transaction.TYPE_REFUND == type){
            opFlag = OP_REFUND_QUERY;
            builder.bodySet(GrabPayBusinessFields.TX_TYPE, GrabPayConstants.REFUND);
        }
        builder.bodySet(GrabPayBusinessFields.PARTNER_TX_ID, MapUtil.getString(transaction, Transaction.TSN));

        url = url + "/" + MapUtil.getString(transaction, Transaction.TSN);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, GrabPayConstants.GET, builder.build(), partnerId, getPrivateKeyContent(privateKey), 1, opFlag);
        } catch (Exception e) {
            logger.error("failed to call grab query", e);
            setTransactionContextErrorInfo(context, opFlag, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, opFlag);

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PRIVATE_KEY);
        String partnerId = BeanUtil.getPropString(tradeParams, TransactionParam.GRABPAY_PARTNER_ID);

        //根据币种区分越南和非越南
        String currency = getTradeCurrency(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        if (TransactionParam.UPAY_CURRENCY_VND.equals(currency)){
            url = ApolloConfigurationCenterUtil.getProviderGateway(Strings.concat(getName(), URL_MOCA), OP_REFUND);
        }

        url = url + "/" + MapUtil.getString(transaction, Transaction.ORDER_SN) + "/" + OP_REFUND;
        GrabPayRequestBuilder builder = getDefaultRequestBuilder(tradeParams, currency, GrabPayConstants.CONTENT_TYPE);
        //退款金额，越南盾处理，上送单位为元
        String amount = MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT);
        if (TransactionParam.UPAY_CURRENCY_VND.equals(currency) && amount.length() > 2) {
            amount = amount.substring(0, amount.length() - 2);
        }
        builder.bodySet(GrabPayBusinessFields.AMOUNT, Integer.parseInt(amount));
        //退款流水号
        builder.bodySet(GrabPayBusinessFields.PARTNER_TX_ID, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, GrabPayConstants.PUT, builder.build(), partnerId, getPrivateKeyContent(privateKey), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call grab refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("暂不支持预下单");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String respCode = MapUtil.getString(result, GrabPayResponseFields.RESPONSE_CODE);//注：只有失败的时候会返回此code

        if (StringUtils.isEmpty(respCode)){
            String status = MapUtil.getString(result, GrabPayResponseFields.STATUS);
            if (Objects.equals(GrabPayConstants.STATUS_SUCCESS, status)) {
                setTradeNoBuyerInfoIfExists(result, context);
                resolveFund(context);
                return Workflow.RC_PAY_SUCCESS;
            }

            // 明确失败
            if (GrabPayConstants.STATUS_FAILED.equals(status) || GrabPayConstants.STATUS_BAD_DEBT.equals(status)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            // 支付中
            if (GrabPayConstants.STATUS_PENDING.equals(status) || GrabPayConstants.STATUS_UNKNOWN.equals(status)) {
                return Workflow.RC_IN_PROG;
            }
        }
        return Workflow.RC_ERROR;
    }

    private String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        String respCode = MapUtil.getString(result, GrabPayResponseFields.RESPONSE_CODE);//注：只有失败的时候会返回此code

        if (!StringUtils.isEmpty(respCode)) {
            String code = MapUtil.getString(result, GrabPayResponseFields.CODE);
            String devMessage = MapUtil.getString(result, GrabPayResponseFields.DEV_MESSAGE);
            //撤单转退款
            if (Objects.equals(GrabPayConstants.CODE_CANCEL_NOT_SUPPORTED, code)
                    && Objects.equals(GrabPayConstants.MSG_CANCEL_NOT_SUPPORTED, devMessage)){
                String refundResult = refund(context);
                return Workflow.RC_REFUND_SUCCESS.equals(refundResult) ? Workflow.RC_CANCEL_SUCCESS : refundResult;
            }
        }
        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        int type = MapUtil.getIntValue(context.getTransaction(), Transaction.TYPE);

        if (Transaction.TYPE_PAYMENT != type && Transaction.TYPE_REFUND != type){
            throw new UnsupportedOperationException("暂不支持非支付和退款流水的查询");
        }

        String respCode = MapUtil.getString(result, GrabPayResponseFields.RESPONSE_CODE);//注：只有失败的时候会返回此code

        if (StringUtils.isEmpty(respCode)) {
            String status = MapUtil.getString(result, GrabPayResponseFields.STATUS);

            if (Objects.equals(GrabPayConstants.STATUS_SUCCESS, status)) {
                setTradeNoBuyerInfoIfExists(result, context);
                if (Transaction.TYPE_PAYMENT == type) {
                    resolveFund(context);
                    return Workflow.RC_PAY_SUCCESS;
                } else {
                    resolveRefundFund(context);
                    return Workflow.RC_REFUND_SUCCESS;
                }
            } else if (GrabPayConstants.STATUS_FAILED.equals(status) || GrabPayConstants.STATUS_BAD_DEBT.equals(status)) {
                // 明确失败
                return Transaction.TYPE_PAYMENT == type ? Workflow.RC_TRADE_CANCELED : Workflow.RC_ERROR;
            } else if (GrabPayConstants.STATUS_PENDING.equals(status) || GrabPayConstants.STATUS_UNKNOWN.equals(status)) {
                // 处理中
                return Transaction.TYPE_PAYMENT == type ? Workflow.RC_IN_PROG : Workflow.RC_RETRY;
            }
        }
        return Workflow.RC_ERROR;
    }

    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String respCode = MapUtil.getString(result, GrabPayResponseFields.RESPONSE_CODE);//注：只有失败的时候会返回此code

        if (StringUtils.isEmpty(respCode)) {
            String status = BeanUtil.getPropString(result, GrabPayResponseFields.STATUS);
            if (Objects.equals(GrabPayConstants.STATUS_SUCCESS, status)) {
                //退款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                context.getTransaction().put(Transaction.TRADE_NO, MapUtil.getString(result, GrabPayResponseFields.TX_ID));//通道退款订单号
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            } else if (Objects.equals(GrabPayConstants.STATUS_PENDING, status) || Objects.equals(GrabPayConstants.STATUS_UNKNOWN, status)) {
                //须进行退款查询，从而确定最终的退款情况
                return query(context);
            }
        }

        return Workflow.RC_ERROR;
    }

    private void resolveFund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments  = new ArrayList<>();
        }else{
            payments.clear();
        }

        //交易金额
        long transactionAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, transactionAmount,
                Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_WALLET_GRABPAY,
                Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_GRABPAY));

        extraOutFields.put(Transaction.PAYMENTS, payments);
        transaction.put(Transaction.PAID_AMOUNT, transactionAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);
    }

    private void resolveRefundFund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments  = new ArrayList<>();
        }else{
            payments.clear();
        }
        //本次退款总额
        long refundAmountTotal = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, refundAmountTotal,
                Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_WALLET_GRABPAY,
                Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_GRABPAY));

        extraOutFields.put(Transaction.PAYMENTS, payments);
        transaction.put(Transaction.PAID_AMOUNT, refundAmountTotal);
        transaction.put(Transaction.RECEIVED_AMOUNT, refundAmountTotal);
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> data, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        //grabpay 通道侧流水号
        String txId = MapUtil.getString(data, GrabPayResponseFields.TX_ID);

        if(!StringUtil.empty(txId)){
            if(StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, txId);
            }
            if(StringUtil.empty(MapUtil.getString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, txId);
            }
        }
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
    }

    private Map<String, Object> retryIfNetworkException(String url, String httpMethod, Map<String, Object> request, String partnerId, String privateKey, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(url, httpMethod, request, partnerId, privateKey);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in foxconn {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String respCode = MapUtil.getString(result, GrabPayResponseFields.RESPONSE_CODE, GrabPayConstants.CODE_SUCCESS);
        String code = MapUtil.getString(result, GrabPayResponseFields.CODE);
        String msg = StringUtils.EMPTY;
        String status = MapUtil.getString(result, GrabPayResponseFields.STATUS);
        String errMsg = MapUtil.getString(result, GrabPayResponseFields.ERR_MSG);

        if (Objects.equals(respCode, GrabPayConstants.CODE_BAD_REQUEST)) {
            msg = StringUtils.isEmpty(code) ? MapUtil.getString(result, GrabPayResponseFields.ARG) : MapUtil.getString(result, GrabPayResponseFields.DEV_MESSAGE);
        } else if (Objects.equals(respCode, GrabPayConstants.CODE_UNAUTHORIZED)) {
            msg = MapUtil.getString(result, GrabPayResponseFields.MESSAGE);
        } else if (Objects.equals(respCode, GrabPayConstants.CODE_NOT_FOUND)
                || Objects.equals(respCode, GrabPayConstants.CODE_CONFLICT)
                || Objects.equals(respCode, GrabPayConstants.CODE_INTERNAL_SERVER_ERROR)) {
            msg = MapUtil.getString(result, GrabPayResponseFields.REASON);
        } else if (!StringUtils.isEmpty(status)) {
            msg = errMsg;
        }

        map.put(GrabPayResponseFields.RESPONSE_CODE, respCode);
        map.put(GrabPayResponseFields.CODE, code);
        map.put(GrabPayResponseFields.ERR_MSG, msg);
        map.put(GrabPayResponseFields.STATUS, status);

        com.wosai.upay.util.MapUtil.removeNullValues(map);

        BeanUtil.setNestedProperty(context.getTransaction(),
                UpayUtil.getProviderErrorInfoKey(key),
                map);
        String terminalCategory = MapUtil.getString(MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        boolean isMergeMessage = StringUtil.empty(terminalCategory);
        if(Objects.equals(respCode, GrabPayConstants.CODE_SUCCESS) && Objects.equals(GrabPayConstants.STATUS_SUCCESS, status)) {
            // 支付通道返回成功，清空原先设置的UpayBizError
            Map bizErrorCode = (Map) BeanUtil.getProperty(context.getTransaction(), Transaction.BIZ_ERROR_CODE);
            if (null != bizErrorCode && bizErrorCode.containsKey(key)){
                bizErrorCode.remove(key);
            }
        }else {
            UpayBizError bizError = UpayBizError.getBizErrorByField(key, BeanUtil.getPropInt(context.getTransaction(), Transaction.PAYWAY), !StringUtil.empty(code) ? code : respCode , msg);

            String path = UpayUtil.getBizErrorCodeKey(key);
            if (bizError != null && !UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(bizError.getStandardName())){
                BeanUtil.setNestedProperty(context.getTransaction(), path, bizError);
            } else {
                BeanUtil.setNestedProperty(context.getTransaction(), path,
                        UpayBizError.unexpectedProviderError(StringUtil.empty(msg)
                                ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : msg, isMergeMessage));
            }
        }
    }

    public GrabPayRequestBuilder getDefaultRequestBuilder(Map<String,Object> config, String currency, String contentType) {
        GrabPayRequestBuilder requestBuilder = new GrabPayRequestBuilder();
        requestBuilder.bodySet(GrabPayProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bodySet(GrabPayBusinessFields.GRAB_ID, MapUtil.getString(config, TransactionParam.GRABPAY_MERCHANT_ID));
        requestBuilder.bodySet(GrabPayBusinessFields.TERMINAL_ID, MapUtil.getString(config, TransactionParam.GRABPAY_TERMINAL_ID));
        requestBuilder.bodySet(GrabPayBusinessFields.CURRENCY, currency);
        requestBuilder.headSet(GrabPayProtocolFields.CONTENT_TYPE, contentType);
        Date now = new Date(System.currentTimeMillis());
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String gmtDateTime = dateFormat.format(now);
        requestBuilder.headSet(GrabPayProtocolFields.DATE, gmtDateTime);
        return requestBuilder;
    }

}
