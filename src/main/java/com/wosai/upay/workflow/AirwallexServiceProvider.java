package com.wosai.upay.workflow;

import com.google.common.collect.Maps;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.airwallex.*;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.util.Des3Utils;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;

import static com.wosai.upay.util.ProviderUtil.*;

/**
 * Airwallex (空中云汇) payment service provider implementation
 */
@Slf4j
public class AirwallexServiceProvider extends AbstractServiceProvider {

    // Provider name
    public static final String NAME = "provider.airwallex";

    // Dependencies
    @Autowired
    private AirwallexClient airwallexClient;

    @Resource
    AirwallexAccessTokenCache tokenCache;

    public AirwallexServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(AirwallexConstants.DATE_TIME_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_AIRWALLEX;
    }

    /**
     * Determines if this service provider can handle the transaction
     *
     * @param transaction Transaction data
     * @return true if this provider can handle the transaction, false otherwise
     */
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return null!=getTradeParams(transaction);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.AIRWALLEX_TRADE_PARAMS);
    }

    /**
     * Process a payment request with Airwallex
     *
     * @param context Transaction context containing payment details
     * @param resume  Whether this is a resumed transaction
     * @return Workflow result code indicating success, failure, or in-progress status
     */
    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        // Build request headers and payment intent request
        Map<String, String> headers = buildHeaders(transaction,OP_PAY);

        Map<String, Object> paymentIntentRequest = buildPaymentIntentRequest(context);

        // Execute payment request
        Map<String, Object> result;
        try {
            result = airwallexClient.execute(
                    AirwallexConstants.AirwallexRequestMethod.POST,
                    ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY),
                    AirwallexConstants.AIRWALLEX_HTTP_CONTENT_TYPE_JSON,
                    headers,
                    paymentIntentRequest);
        } catch (Exception ex) {
            logger.error("Failed to call Airwallex payment API", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        // Process response
        if (result == null || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        // Process response
        setTransactionContextErrorInfo(result, context, OP_PAY);

        updateTransactionCommonInfo(transaction, result);

        // Check payment status
        String status = MapUtil.getString(result, AirwallexResponseFields.STATUS);

        if (PaymentStatusConstants.isSuccessful(status)) {
            // Payment successful
            updateTransactionPaymentInfo(transaction, result);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (PaymentStatusConstants.isProcessing(status)) {
            return Workflow.RC_IN_PROG;
        }

        // Payment is still processing
        return Workflow.RC_ERROR;
    }

    /**
     * 更新付款信息
     *
     * @param transaction
     * @param result
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> result) {
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, result, AirwallexResponseFields.UPDATED_AT, object -> parseTimeString((String) object));
        updateMapIfResponseNotNull(transaction, Transaction.PAID_AMOUNT, result, AirwallexResponseFields.CAPTURED_AMOUNT,object -> StringUtils.yuan2cents((String) object));
        //设置通道源流水号
        Map lastPaymentAttempt = MapUtils.getMap (result,AirwallexResponseFields.LATEST_PAYMENT_ATTEMPT) ;
        String channelTradeNo = MapUtil.getString(lastPaymentAttempt, AirwallexResponseFields.PAYMENT_METHOD_TRANSACTION_ID);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (!StringUtils.isEmpty(channelTradeNo)) {
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
        }
    }

    /**
     * 更新通用字段
     *
     * @param transaction
     * @param result
     */
    protected void updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> result) {
        // 解析Airwallex订单号
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, result, AirwallexResponseFields.ID);
        // 解析用户信息
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID, result, AirwallexResponseFields.CUSTOMER_ID);
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String code = MapUtil.getString(result, AirwallexResponseFields.CODE);
        String message = MapUtil.getString(result, AirwallexResponseFields.MESSAGE);
        String provider_original_response_code = MapUtil.getString(result, AirwallexResponseFields.PROVIDER_ORIGINAL_RESPONSE_CODE);

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(AirwallexResponseFields.CODE, code);
        map.put(AirwallexResponseFields.MESSAGE, message);
        map.put(AirwallexResponseFields.PROVIDER_ORIGINAL_RESPONSE_CODE, provider_original_response_code);
        String status = MapUtil.getString(result, AirwallexResponseFields.STATUS);
        if (!StringUtils.isEmpty(status)){
            map.put(BusinessV2Fields.STATUS_CODE, status);//返回订单状态码
        }

        setTransactionContextErrorInfo(context.getTransaction(), key, map, PaymentStatusConstants.isSuccessful(status), code, message);
    }



    /**
     * Build HTTP headers for Airwallex API requests
     *
     * @param transaction transaction
     * @return Map of HTTP headers
     */
    private Map<String, String> buildHeaders(Map<String, Object> transaction) {
       return buildHeaders(transaction,null);
    }

    /**
     * Build HTTP headers for Airwallex API requests
     *
     * @param transaction transaction
     * @return Map of HTTP headers
     */
    private Map<String, String> buildHeaders(Map<String, Object> transaction,String op) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        // Get Airwallex credentials
        String apiVersion = MapUtil.getString(tradeParams, TransactionParam.AIRWALLEX_API_VERSION);
        String clientId = MapUtils.getString(tradeParams, TransactionParam.AIRWALLEX_CLIENT_ID);
        String apiKey = MapUtils.getString(tradeParams, TransactionParam.AIRWALLEX_API_KEY);
        String accountId = MapUtil.getString(tradeParams, TransactionParam.AIRWALLEX_PROVIDER_MCH_ID);

        //get token and cache
        String token = tokenCache.getAccessToken(clientId, apiKey, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_TOKEN));

        Map<String, String> headers = Maps.newHashMapWithExpectedSize(5);
        if (OP_PAY.equals(op)){
            String terminalIp= genTerminalInfo(transaction).getIp();
            String terminalId = getTerminalId(transaction);
            headers.put(AirwallexRequestFields.X_CARD_PRESENT_TERMINAL_IP, terminalIp);
            headers.put(AirwallexRequestFields.X_CARD_PRESENT_TERMINAL_ID, terminalId);
        }
        headers.put(AirwallexRequestFields.X_API_VERSION, apiVersion);
        headers.put(AirwallexRequestFields.AUTHORIZATION, AirwallexConstants.AUTH_PREFIX + token);
        headers.put(AirwallexRequestFields.X_ON_BEHALF_OF, accountId);
        return headers;
    }

    /**
     * Build payment intent request according to Airwallex API requirements
     *
     * @param context Transaction context
     * @return Payment intent request
     */
    private Map<String, Object> buildPaymentIntentRequest(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        // Create payment intent request
        Map<String, Object> request = new HashMap<>();

        // Set request ID for idempotency
        String requestId = MapUtil.getString(transaction, Transaction.TSN);
        request.put(AirwallexRequestFields.REQUEST_ID, requestId);

        // Set merchant order ID
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        request.put(AirwallexRequestFields.MERCHANT_ORDER_ID, orderSn);

        // Set amount and currency
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        request.put(AirwallexRequestFields.AMOUNT,StringUtils.cents2yuan( amount));
        // Set currency (default to CNY if not specified)
        String currency = getTradeCurrency(transaction);
        request.put(AirwallexRequestFields.CURRENCY, currency);

        // Set descriptor (merchant name that appears on customer's statement)
        String subject = MapUtil.getString(transaction, Transaction.SUBJECT);
        request.put(AirwallexRequestFields.DESCRIPTOR, subject);

        // Add funds split data if available
//        addFundsSplitData(request, tradeParams);

        // Add device data
        addDeviceData(request, context);

        // Add payment method
        Map<String, Object> paymentMethod = buildPaymentMethod(context);
        request.put(AirwallexRequestFields.PAYMENT_METHOD, paymentMethod);

        // Add payment method options
        Map<String, Object> paymentMethodOptions = buildPaymentMethodOptions();
        request.put(AirwallexRequestFields.PAYMENT_METHOD_OPTIONS, paymentMethodOptions);

        return request;
    }


    /**
     * Add device data to the request
     *
     * @param request The request to add device data to
     * @param context Transaction context
     */
    private void addDeviceData(Map<String, Object> request, TransactionContext context) {
        Map<String, Object> deviceData = new HashMap<>();
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());

        String ipAddress = terminalInfo.getIp();

        deviceData.put(AirwallexRequestFields.IP_ADDRESS, ipAddress);
        request.put(AirwallexRequestFields.DEVICE_DATA, deviceData);
    }

    /**
     * Build payment method object for card-present transactions
     *
     * @param context Transaction context
     * @return Payment method object
     */
    private Map<String, Object> buildPaymentMethod(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTENDED_PARAMS, new HashMap<>());


        // Create payment method object
        Map<String, Object> paymentMethod = new HashMap<>();
        paymentMethod.put(AirwallexRequestFields.TYPE, AirwallexRequestFields.PaymentMethodType.CARD_PRESENT);

        // Create card-present object
        Map<String, Object> cardPresent = new HashMap<>();

        // Set PAN entry mode (default to chip)
        String panEntryMode = MapUtil.getString(extendedParams, "pan_entry_mode",
                AirwallexRequestFields.CardPresent.PanEntryMode.CHIP);
        cardPresent.put(AirwallexRequestFields.CardPresent.PAN_ENTRY_MODE, panEntryMode);

        if (AirwallexRequestFields.CardPresent.PanEntryMode.MANUAL_ENTRY.equals(panEntryMode)){
            cardPresent.put(AirwallexRequestFields.CardPresent.NUMBER,  MapUtil.getString(extendedParams, "number"));
        }

        // Set fallback flag
        boolean fallback = MapUtil.getBoolean(extendedParams, "fallback", false);
        cardPresent.put(AirwallexRequestFields.CardPresent.FALLBACK, String.valueOf(fallback));

        // Set track data if available
        addTrackData(cardPresent, extendedParams);

        // Set cardholder verification method
        String cvm = MapUtil.getString(extendedParams, AirwallexRequestFields.CardPresent.CARDHOLDER_VERIFICATION_METHOD,
                AirwallexRequestFields.CardPresent.CardholderVerificationMethod.ONLINE_PIN);
        cardPresent.put(AirwallexRequestFields.CardPresent.CARDHOLDER_VERIFICATION_METHOD, cvm);

        // Set encrypted PIN if available
        String encryptedPin = MapUtil.getString(extendedParams, AirwallexRequestFields.CardPresent.ENCRYPTED_PIN);
        if (!StringUtils.isEmpty(encryptedPin)) {
            cardPresent.put(AirwallexRequestFields.CardPresent.ENCRYPTED_PIN, Des3Utils.encrypt(ApolloConfigurationCenterUtil.getAirwallexTpk(),encryptedPin) );
        }

        // Set EMV tags if available
        String emvTags = MapUtil.getString(extendedParams, AirwallexRequestFields.CardPresent.EMV_TAGS);
        if (!StringUtils.isEmpty(emvTags)) {
            cardPresent.put(AirwallexRequestFields.CardPresent.EMV_TAGS, emvTags);
        }

        // Set terminal info
        Map<String, Object> terminalInfo = buildTerminalInfo(context);
        cardPresent.put(AirwallexRequestFields.CardPresent.TERMINAL_INFO, terminalInfo);

        // Add card-present object to payment method
        paymentMethod.put(AirwallexRequestFields.CardPresent.CARD_PRESENT, cardPresent);

        return paymentMethod;
    }

    /**
     * Add track data to the card present object
     *
     * @param cardPresent    The card present object
     * @param extendedParams Extended parameters containing track data
     */
    private void addTrackData(Map<String, Object> cardPresent, Map<String, Object> extendedParams) {
        String track1 = MapUtil.getString(extendedParams, AirwallexRequestFields.CardPresent.TRACK1);
        String track2 = MapUtil.getString(extendedParams, AirwallexRequestFields.CardPresent.TRACK2);

        if (!StringUtils.isEmpty(track1)) {
            cardPresent.put(AirwallexRequestFields.CardPresent.TRACK1, track1);
        }

        if (!StringUtils.isEmpty(track2)) {
            cardPresent.put(AirwallexRequestFields.CardPresent.TRACK2, track2);
        }
    }

    /**
     * Build terminal info object
     *
     * @param context Transaction context
     * @return Terminal info object
     */
    private Map<String, Object> buildTerminalInfo(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTENDED_PARAMS, new HashMap<>());

        // Create terminal info object
        Map<String, Object> terminalInfo = new HashMap<>();

        // Set terminal ID
        String terminalId = getTerminalId(transaction);
        if (!StringUtils.isEmpty(terminalId)) {
            terminalInfo.put(AirwallexRequestFields.TerminalInfo.TERMINAL_ID, terminalId);
        } else {
            logger.error("Terminal ID is missing");
        }

        // Set PIN entry capability
        String pinEntryCapability = MapUtil.getString(extendedParams, AirwallexRequestFields.TerminalInfo.PIN_ENTRY_CAPABILITY,
                AirwallexRequestFields.TerminalInfo.PinEntryCapability.SOFTWARE_BASED);
        terminalInfo.put(AirwallexRequestFields.TerminalInfo.PIN_ENTRY_CAPABILITY, pinEntryCapability);

        // Set supported PAN entry modes
        String[] supportedPanEntryModes = getSupportedPanEntryModes(extendedParams);
        terminalInfo.put(AirwallexRequestFields.TerminalInfo.SUPPORTED_PAN_ENTRY_MODES, supportedPanEntryModes);

        // Set mobile device flag
        terminalInfo.put(AirwallexRequestFields.TerminalInfo.MOBILE_DEVICE, "true");

        // Set embedded reader flag
        terminalInfo.put(AirwallexRequestFields.TerminalInfo.USE_EMBEDDED_READER, "true");

        return terminalInfo;
    }

    /**
     * Get terminal ID from context
     *
     * @param transaction Transaction
     * @return Terminal ID
     */
    private String getTerminalId(Map<String, Object> transaction ) {
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);
        TerminalInfo sqbTerminal = genTerminalInfo(transaction);
        String terminalId = MapUtil.getString(extraParams, AirwallexRequestFields.TerminalInfo.TERMINAL_ID);
        //优先返回接口传入的terminalId
        return StringUtils.isEmpty(terminalId) ? sqbTerminal.getId() : terminalId;
    }

    /**
     * Get supported PAN entry modes
     *
     * @param extendedParams Extended parameters
     * @return Array of supported PAN entry modes
     */
    private String[] getSupportedPanEntryModes(Map<String, Object> extendedParams) {
        // Check if custom PAN entry modes are provided
        Object customModes = extendedParams.get("supported_pan_entry_modes");
        if (customModes instanceof String[]) {
            return (String[]) customModes;
        }

        // Default supported PAN entry modes
        return new String[]{
                AirwallexRequestFields.CardPresent.PanEntryMode.MAGSTRIPE,
                AirwallexRequestFields.CardPresent.PanEntryMode.CHIP,
                AirwallexRequestFields.CardPresent.PanEntryMode.CONTACTLESS_CHIP
        };
    }

    /**
     * Build payment method options
     *
     * @return Payment method options
     */
    private Map<String, Object> buildPaymentMethodOptions() {
        // Create payment method options object
        Map<String, Object> paymentMethodOptions = new HashMap<>();

        // Create card options
        Map<String, Object> cardOptions = new HashMap<>();

        // Set authorization type
        String authType = AirwallexRequestFields.CardOptions.AuthorizationType.FINAL_AUTH;
        cardOptions.put(AirwallexRequestFields.CardOptions.AUTHORIZATION_TYPE, authType);

        // Set auto capture flag
        cardOptions.put(AirwallexRequestFields.CardOptions.AUTO_CAPTURE, "true");

        // Add card options to payment method options
        paymentMethodOptions.put(AirwallexRequestFields.CardOptions.CARD, cardOptions);

        return paymentMethodOptions;
    }


    @Override
    public String cancel(TransactionContext context) {
        // Airwallex doesn't support cancellation of payments
        throw new UnsupportedOperationException("cancel is not supported");
    }

    /**
     * Query the status of a payment
     *
     * @param context Transaction context
     * @return Workflow result code indicating success, failure, or in-progress status
     */
    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        // Get query params
        Map<String, Object> requestParams =new HashMap<>();
        requestParams.put(AirwallexRequestFields.MERCHANT_ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        // Build request headers
        Map<String, String> headers = buildHeaders(transaction);

        // Execute query request
        Map<String, Object> result;
        try {
            result = airwallexClient.execute(
                    AirwallexConstants.AirwallexRequestMethod.GET,
                    ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY),
                    AirwallexConstants.AIRWALLEX_HTTP_CONTENT_TYPE_JSON,
                    headers,
                    requestParams);
        } catch (Exception ex) {
            logger.error("Failed to call Airwallex query API", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        Object resultItems = MapUtils.getObject(result, "items");
        if (null == resultItems) {
            return Workflow.RC_IN_PROG;
        }
        // 返回查询结果
        ArrayList<Map<String, Object>> items = (ArrayList<Map<String, Object>>) resultItems;
        Optional<Map<String, Object>> curRequestResult = items.stream().filter(x -> x.get("request_id").equals(MapUtil.getString(transaction, Transaction.TSN))).findFirst();
        if (!curRequestResult.isPresent()){
            return Workflow.RC_IN_PROG;
        }
        Map<String, Object> resultItem = curRequestResult.get();

        updateTransactionCommonInfo(transaction, resultItem);
        // Process response
        String status = MapUtil.getString(resultItem, AirwallexResponseFields.STATUS);

        if (PaymentStatusConstants.isSuccessful(status)) {
            updateTransactionPaymentInfo(transaction, resultItem);
            return Workflow.RC_PAY_SUCCESS;
        }

        if (PaymentStatusConstants.isProcessing(status)) {
            // Payment is still processing
            return Workflow.RC_IN_PROG;
        }
        // Payment failed or cancelled
        return Workflow.RC_TRADE_CANCELED; // Changed from RC_PAY_SUCCESS to RC_PAY_FAIL to correctly indicate failure

    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        } else {
            return doRefund(context);
        }
    }

    /**
     * Query the status of a refund
     *
     * @param context Transaction context
     * @return Workflow result code indicating success, failure, or in-progress status
     */
    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        // Get refund ID
        Map<String, Object> requestParams =new HashMap<>();
        requestParams.put(AirwallexRequestFields.Refund.PAYMENT_INTENT_ID,  MapUtil.getString(order, Order.TRADE_NO));

        // Build request headers
        Map<String, String> headers = buildHeaders(transaction);

        // Execute refund query request
        Map<String, Object> result;
        try {

            result = airwallexClient.execute(
                    AirwallexConstants.AirwallexRequestMethod.GET,
                    ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_QUERY),
                    AirwallexConstants.AIRWALLEX_HTTP_CONTENT_TYPE_JSON,
                    headers,
                    requestParams);
        } catch (Exception ex) {
            logger.error("Failed to call Airwallex refund query API", ex);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
            return Workflow.RC_ERROR;
        }
        if (result == null) {
            return Workflow.RC_ERROR;
        }
        // Process response
        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
        Object resultItems = MapUtils.getObject(result, "items");
        if (null == resultItems) {
            return Workflow.RC_IN_PROG;
        }
        // 返回查询结果
        ArrayList<Map<String, Object>> items = (ArrayList<Map<String, Object>>) resultItems;
        Optional<Map<String, Object>> curRequestResult = items.stream().filter(x -> x.get("request_id").equals(MapUtil.getString(transaction, Transaction.TSN))).findFirst();
        if (!curRequestResult.isPresent()){
            return Workflow.RC_RETRY;
        }
        Map<String, Object> resultItem = curRequestResult.get();

        updateTransactionCommonInfo(transaction, resultItem);
        // Check refund status
        String status = MapUtil.getString(resultItem, AirwallexResponseFields.STATUS);

        if (RefundStatusConstants.isSuccessful(status)) {
            // Refund succeeded
            updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, resultItem, AirwallexResponseFields.UPDATED_AT, object -> parseTimeString((String) object));
            updateTransactionRefundInfo(context,resultItem);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (RefundStatusConstants.isFailed(status)) {
            // Refund failed
            return Workflow.RC_SYS_ERROR;
        } else {
            // Refund is still processing
            return Workflow.RC_RETRY;
        }
    }

    private String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);

        Map<String, Object> order = context.getOrder();

        // Get payment intent ID (original transaction ID)
        String paymentIntentId = MapUtil.getString(order, Order.TRADE_NO);

        // Get refund amount
        long refundAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        // Build refund request
        Map<String, Object> refundRequest = buildRefundRequest(transaction, paymentIntentId, refundAmount);

        // Build request headers
        Map<String, String> headers = buildHeaders(transaction);

        // Execute refund request
        Map<String, Object> result;
        try {
            result = airwallexClient.execute(
                    AirwallexConstants.AirwallexRequestMethod.POST,
                    ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND),
                    AirwallexConstants.AIRWALLEX_HTTP_CONTENT_TYPE_JSON,
                    headers,
                    refundRequest);
        } catch (Exception ex) {
            logger.error("Failed to call Airwallex refund API", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        } finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        // Process response
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        updateTransactionCommonInfo(transaction, result);

        // Check refund status
        String status = MapUtil.getString(result, AirwallexResponseFields.STATUS);

        if (RefundStatusConstants.isSuccessful(status)) {
            // Refund succeeded
            updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, result, AirwallexResponseFields.UPDATED_AT, object -> parseTimeString((String) object));
            updateTransactionRefundInfo(context, result);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (RefundStatusConstants.isFailed(status)) {
            // Refund failed
            return Workflow.RC_SYS_ERROR;
        } else {
            // Refund is still processing
            return Workflow.RC_RETRY;
        }
    }

    /**
     * 更新退款信息
     * @param context
     * @param result
     */
    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> result){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    /**
     * Build refund request according to Airwallex API requirements
     *
     * @param transaction     Transaction data
     * @param paymentIntentId Original payment intent ID
     * @param refundAmount    Refund amount
     * @return Refund request
     */
    private Map<String, Object> buildRefundRequest(Map<String, Object> transaction, String paymentIntentId,
                                                   long refundAmount) {
        // Create refund request
        Map<String, Object> request = new HashMap<>();

        // Set request ID for idempotency
        String requestId = MapUtil.getString(transaction, Transaction.TSN);
        request.put(AirwallexRequestFields.REQUEST_ID, requestId);

        // Set payment intent ID
        request.put(AirwallexRequestFields.Refund.PAYMENT_INTENT_ID, paymentIntentId);

        // Set amount
        request.put(AirwallexRequestFields.Refund.AMOUNT,StringUtils.cents2yuan(refundAmount));

        return request;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        // Airwallex doesn't support precreate
        throw new UnsupportedOperationException("Precreate is not supported");
    }

    @Override
    public String explainNotification(Map<String, Object> notification) {
        TransactionContext context = (TransactionContext) notification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT == type) {
            //默认直接再查询一遍
            return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
        } else if (Transaction.TYPE_REFUND == type) {
            //默认直接再查询一遍
            return Workflow.RC_REFUND_SUCCESS.equals(refundQuery(context)) ? Workflow.RC_REFUND_SUCCESS : null;
        } else {
            log.info("airwallex 未知回调类型");
            return null;
        }
    }

    @Override
    public String depositQuery(TransactionContext context) {
        throw new UnsupportedOperationException("Deposit query is not supported");
    }

    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("Deposit freeze is not supported");
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("Deposit pre-freeze is not supported");
    }

    @Override
    public String depositCancel(TransactionContext context) {
        throw new UnsupportedOperationException("Deposit cancel is not supported");
    }

    @Override
    public String depositConsume(TransactionContext context) {
        throw new UnsupportedOperationException("Deposit consume is not supported");
    }
}
