package com.wosai.upay.workflow;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created by jianfree on 30/6/16.
 */
@WorkflowPriority(priority = 5)
public class QRCodeCIBBankWorkflow extends QRCodeWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(QRCodeCIBBankWorkflow.class);

    public static final String NAME = "generic.qrcode.cibbank.workflow";
    public static   long[] delays = {10000, 10000, 10000, 30000, 34000, 80000, 90000 };
    public static   long[] cancelDelays = {200, 1500, 5000};

    public QRCodeCIBBankWorkflow(){
        super(delays, cancelDelays);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        int provider = MapUtil.getIntValue(transaction, Transaction.PROVIDER);
        return (Order.PROVIDER_CIBBANK == provider || Order.PROVIDER_CITICBANK == provider || Order.PROVIDER_CIBGZBANK == provider)
                && (subPayway == Order.SUB_PAYWAY_QRCODE || subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI)
                && MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
