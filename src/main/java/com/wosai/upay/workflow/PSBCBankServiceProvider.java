package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.psbcbank.*;
import com.wosai.mpay.api.unionqrcode.UnionPayQRCodeConstants;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.*;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.PSBCBankSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description PSBCBankServiceProvider
 * @Date 2021/4/7 4:42 PM
 */
public abstract class PSBCBankServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(PSBCBankServiceProvider.class);

    protected String b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE + "m";
    protected String defaultTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE + "m";//csb 二维码支付,超时时间
    protected long b2cTimeExpire = B2C_TIME_EXPIRE_MINUTE * 60 * 1000;
    protected long defaultTimeExpire = DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
    protected String notifyHost;
    protected String notifyUrl;
    protected static final int NOTIFY_URL_LIMIT = 200; //邮储交易通知地址长度限制200

    private static final String OP_PRECREATE_CLOSE = "precreate.close";
    private static final String OP_PRECREATE_QUERY = "precreate.query";
    private static final String OP_PRECREATE_REFUND = "precreate.refund";
    private static final String OP_PRECREATE_REFUND_QUERY = "precreate.refund.query";

    @Autowired
    protected PSBCBankClient psbcBankClient;

    protected DateFormat df = new SimpleDateFormat(PSBCBankConstants.DATE_TIME_FORMAT);

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.PSBCBANK_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider(){
        return Order.PROVIDER_PSBCBANK;
    }

    public abstract PSBCRequestBuilder getPayRequestBuilder(TransactionContext context);

    public abstract PSBCRequestBuilder getPreCreateRequestBuilder(TransactionContext context);

    public abstract PSBCRequestBuilder getRefundRequestBuilder(TransactionContext context);

    public abstract PSBCRequestBuilder getRefundQueryRequestBuilder(TransactionContext context);

    public abstract PSBCRequestBuilder getQueryRequestBuilder(TransactionContext context);

    public abstract PSBCRequestBuilder getCloseRequestBuilder(TransactionContext context);

    public abstract String resolveNotification(Map<String, Object> providerNotification);

    public abstract Map<String, Object> call(String gatewayUrl, Map<String, Object> request, String privateKey, String sm2Pass) throws MpayException, MpayApiNetworkError, JsonProcessingException;

    public abstract void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key);

    public abstract String buildPayResult(Map<String, Object> result, TransactionContext context);

    public abstract String buildPreCreateResult(Map<String, Object> result, TransactionContext context);

    public abstract String buildRefundResult(Map<String, Object> result, TransactionContext context);

    public abstract String buildQueryResult(Map<String, Object> result, TransactionContext context);

    public abstract String buildCloseResult(Map<String, Object> result, TransactionContext context);

    public String buildRefundQueryResult(Map<String, Object> result, TransactionContext context) {

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.REFUND_ERROR_LIST.contains(respCode)){
            return Workflow.RC_ERROR;
        }

        String txnFlag = BeanUtil.getPropString(result, PSBCResponseFields.TXN_FLAG);//交易标识
        String txnSta = BeanUtil.getPropString(result, PSBCResponseFields.TXN_STA);//交易状态
        if ((PSBCBankConstants.TXN_FLAG_PARTIAL_REFUNDED.equals(txnFlag) || PSBCBankConstants.TXN_FLAG_ALL_REFUNDED.equals(txnFlag)) && PSBCBankConstants.TXN_STA_SUCCESS.equals(txnSta)){
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            //由于退款查询只返回该笔退款流水是否成功，无其他信息，故获取不到通道侧和支付源侧的退款相关信息
            updateTransactionChannelPayment(context);
            return Workflow.RC_REFUND_SUCCESS;
        }

        return Workflow.RC_ERROR;
    }

    //更新支付通道的支付明细，以便能准确计算商户免充值，以及结算金额等信息
    private void updateTransactionChannelPayment(TransactionContext context){

        Map<String,Object> order = context.getOrder();
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME));

        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT) || BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_CANCEL){
            //全额退款或者撤单
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款
            AbstractServiceProvider.resolveRefundFundByPercent(transaction, payTransaction);
            // 设置received_amount和paid_amount
            long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
            transaction.put(Transaction.PAID_AMOUNT, effectiveAmount);
        }
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        String terminalSn = (String) BeanUtil.getNestedProperty(transaction, String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_SN));
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        //密钥
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        PSBCRequestBuilder builder = getPayRequestBuilder(context);
        // 手续费费率
        builder.set(PSBCBusinessFields.ACTIV_RATE, MapUtil.getString(tradeParams, TransactionParam.FEE_RATE));
        //termID
        builder.set(PSBCBusinessFields.TERM_ID, terminalSn.length() <= 8 ? terminalSn : terminalSn.substring(terminalSn.length() - 8));
        String termIp = StringUtils.isNotBlank(BeanUtil.getPropString(extraParams, Transaction.SQB_IP)) ? BeanUtil.getPropString(extraParams, Transaction.SQB_IP) : BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP);
        Map<String, Object> termInfo = CollectionUtil.hashMap(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS, PSBCBusinessFields.TERM_INFO_SERIAL_NUM, terminalSn,
                PSBCBusinessFields.TERM_INFO_TERM_IP, termIp, PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
        addPoiOrBaseStation(context, termInfo, extraParams);
        //259号文改造
        addTermDeviceId(termInfo, transaction);
        //termInfo
        builder.set(PSBCBusinessFields.TERM_INFO, JSON.toJSONString(termInfo));
        //信用卡交易标识
        limitCredit(builder, transaction);
        //回调通知,被扫不需要回调
        builder.set(PSBCBusinessFields.WHETHER_NOTIFY, PSBCBankConstants.DENY_NOTIFY);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_WEIXIN == payway){
            carryOverExtendedParams(extendedParams, builder, WeixinConstants.PAY_ALLOWED_FIELDS);
        } else if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway){
            carryOverExtendedParams(extendedParams, builder);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            carryOverExtendedParams(extendedParams, builder, UnionPayQRCodeConstants.PAY_ALLOWED_FIELDS);
        }

        Map<String,Object> result;
        try {
            result = retryIfNetworkException(tradeParams, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), secretKey, sm2Pass, 1, OP_PAY);
            setTransactionContextErrorInfo(result, context, OP_PAY);
        }catch (Exception ex) {
            logger.error("failed to call psbc pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError || ex instanceof JsonProcessingException) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }

        return buildPayResult(result, context);
    }


    protected Map<String,Object> retryIfNetworkException(Map<String, Object>  config, String gatewayUrl, Map<String, Object> request, String secretKey, String sm2Pass, int retryTimes, String logFlag) throws Exception{
        return retryIfNetworkException(()->call(gatewayUrl, request, secretKey, sm2Pass), logger, retryTimes, logFlag, "psbcbank");
    }

    public void limitCredit(PSBCRequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction,Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(PSBCBusinessFields.IS_CREDIT, 1);
        }else {
            builder.set(PSBCBusinessFields.IS_CREDIT, 0);
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        //密钥
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        PSBCRequestBuilder builder = getCloseRequestBuilder(context);
        //请求方交易流水号或者订单号
        builder.set(PSBCBusinessFields.REQ_TRACE_ID, BeanUtil.getPropString(transaction, Transaction.TSN) + "C");
        //原请求方订单号或流水号
        builder.set(PSBCBusinessFields.ORG_REQ_TRACE_ID, BeanUtil.getPropString(context.getOrder(), Order.SN));
        //原请求方交易时间
        builder.set(PSBCBusinessFields.ORG_REQ_DATE, df.format(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));

        String opFlag = ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE;
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_UNIONPAY == payway && (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay)) {
            opFlag = OP_PRECREATE_CLOSE;
        }

        Map<String,Object> result;
        try {
            result = retryIfNetworkException(tradeParams, ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag), builder.build(), secretKey, sm2Pass, 1, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE);
        } catch (Exception e) {
            logger.error("failed to call psbc close", e);
            setTransactionContextErrorInfo(context, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE);

        return buildCloseResult(result, context);
    }


    @Override
    public String refund(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String terminalSn = (String) BeanUtil.getNestedProperty(transaction, String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_SN));
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);

        //密钥
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        PSBCRequestBuilder builder = getRefundRequestBuilder(context);
        //termID
        builder.set(PSBCBusinessFields.TERM_ID, terminalSn.length() <= 8 ? terminalSn : terminalSn.substring(terminalSn.length() - 8));
        String termIp = StringUtils.isNotBlank(BeanUtil.getPropString(extraParams, Transaction.SQB_IP)) ? BeanUtil.getPropString(extraParams, Transaction.SQB_IP) : BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP);
        Map<String, Object> termInfo = CollectionUtil.hashMap(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS, PSBCBusinessFields.TERM_INFO_SERIAL_NUM, terminalSn,
                PSBCBusinessFields.TERM_INFO_TERM_IP, termIp,  PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
        //259号文改造
        addTermDeviceId(termInfo, transaction);
        //termInfo
        builder.set(PSBCBusinessFields.TERM_INFO, JSON.toJSONString(termInfo));
        //请求方交易流水号或者订单号
        builder.set(PSBCBusinessFields.REQ_TRACE_ID, BeanUtil.getPropString(transaction, Transaction.TSN) );
        //原请求方订单号或流水号
        builder.set(PSBCBusinessFields.ORG_REQ_TRACE_ID, BeanUtil.getPropString(context.getOrder(), Order.SN));
        //原请求方交易时间,不需要精确时间
        builder.set(PSBCBusinessFields.ORG_REQ_DATE, df.format(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));

        String opFlag = OP_REFUND;
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_UNIONPAY == payway && (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay)) {
            opFlag = OP_PRECREATE_REFUND;
        }

        Map<String,Object> result;
        try {
            result = retryIfNetworkException(tradeParams, ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag), builder.build(), secretKey, sm2Pass, 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call psbc refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);
    }

    // 退款查询
    protected String doRefundQuery(TransactionContext context){

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());

        PSBCRequestBuilder builder = getRefundQueryRequestBuilder(context);
        //密钥
        String key = MapUtil.getString(config, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = MapUtil.getString(config, TransactionParam.PSBCBANK_SM2_PASS);

        String opFlag = OP_REFUND_QUERY;
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_UNIONPAY == payway && (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay)) {
            opFlag = OP_PRECREATE_REFUND_QUERY;
        }

        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag), builder.build(), secretKey, sm2Pass, 1, "refund.query");
        } catch (Exception e) {
            logger.error("failed to call psbc refund query", e);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, e);
        }
        if(result == null){
            throw new UpayBizException("refund query fail result is null");
        }
        return buildRefundQueryResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> result = doQuery(context);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();

        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String terminalSn = (String) BeanUtil.getNestedProperty(transaction, String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_SN));
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        //密钥
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        PSBCRequestBuilder builder = getPreCreateRequestBuilder(context);
        // 手续费费率
        builder.set(PSBCBusinessFields.ACTIV_RATE,  MapUtil.getString(tradeParams, TransactionParam.FEE_RATE));
        //termID
        builder.set(PSBCBusinessFields.TERM_ID, terminalSn.length() <= 8 ? terminalSn : terminalSn.substring(terminalSn.length() - 8));
        String termIp = StringUtils.isNotBlank(BeanUtil.getPropString(extraParams, Transaction.SQB_IP)) ? BeanUtil.getPropString(extraParams, Transaction.SQB_IP) : BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP);
        Map<String, Object> termInfo = CollectionUtil.hashMap(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS, PSBCBusinessFields.TERM_INFO_SERIAL_NUM, terminalSn,
                PSBCBusinessFields.TERM_INFO_TERM_IP, termIp,  PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
        //259号文改造
        addTermDeviceId(termInfo, transaction);
        //termInfo
        builder.set(PSBCBusinessFields.TERM_INFO, JSON.toJSONString(termInfo));
        //回调通知
        builder.set(PSBCBusinessFields.WHETHER_NOTIFY, PSBCBankConstants.NEED_NOTIFY);
        //回调地址
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(PSBCBusinessFields.BACK_URL, notifyUrl);
        }
        //信用卡交易标识
        limitCredit(builder, transaction);
        builder.set(PSBCBusinessFields.CODE_STA, ApolloConfigurationCenterUtil.getPSBCWapCodeFlag());//码牌标识

        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_WEIXIN == payway){
            carryOverExtendedParams(extendedParams, builder, WeixinConstants.PAY_ALLOWED_FIELDS);
        } else if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway){
            carryOverExtendedParams(extendedParams, builder);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            carryOverExtendedParams(extendedParams, builder, UnionPayQRCodeConstants.WAP_ALLOWED_FIELDS);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(tradeParams, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE), builder.build(), secretKey, sm2Pass, 1, OP_PRECREATE);
        } catch (Exception e) {
            logger.error("failed to call psbc precreate", e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildPreCreateResult(result, context);
    }

    protected Map<String,Object> doQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();

        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        //密钥
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        PSBCRequestBuilder builder = getQueryRequestBuilder(context);

        //原请求方订单号或流水号
        builder.set(PSBCBusinessFields.ORG_REQ_TRACE_ID, BeanUtil.getPropString(transaction, Transaction.TSN));
        //原请求方交易时间,不需要精确时间
        builder.set(PSBCBusinessFields.ORG_REQ_DATE, df.format(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));

        String opFlag = OP_QUERY;
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_UNIONPAY == payway && (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay)) {
            opFlag = OP_PRECREATE_QUERY;
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(tradeParams, ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag), builder.build(), secretKey, sm2Pass, 1, OP_QUERY);
        } catch (Exception e) {
            logger.error("failed to call psbc query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return null;
        }
        return result;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        String secretKey = getPrivateKeyContent(key);

        boolean isPass = false;
        try {
            isPass = PSBCBankSignature.verifySign(providerNotification, secretKey, sm2Pass);
        } catch (Exception e) {
            logger.warn("【邮储银行回调验签】验签异常, 异常栈: ", e);
        }

        logger.info("验签结果: {}" , isPass);
        if (context.isFakeRequest() || isPass) {
            String result = resolveNotification(providerNotification);
            logger.info("回调结果：{}", result);
            return result;
        }

        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    /**
     * 公共请求参数
     * @param context
     */
    protected PSBCRequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String reqSysId = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_REQ_SYS_ID);
        String platformId = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_PLATFORM_ID);
        String certNum = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_CERT_NUM);
        String channelId = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_PROVIDER_CHANNEL_ID);
        String mchtNo = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_PROVIDER_MCH_ID);

        //发起渠道终端, 默认"机构接入渠道"
        requestBuilder.set(PSBCBusinessFields.SOURCE_ID, PSBCBankConstants.SOURCE_INSTITUTE_ACCESS);
        //请求方系统代码
        requestBuilder.set(PSBCBusinessFields.REQ_SYS_ID, reqSysId);
        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, BeanUtil.getPropString(transaction, Transaction.TSN));
        //请求方自定义字段，响应时原样返回
        requestBuilder.set(PSBCBusinessFields.REQ_RESERVED, "reqReserved");
        //外包机构号，由统一收单系统分配
        requestBuilder.set(PSBCBusinessFields.PLATFORM_ID, platformId);
        //CFCA证书序列号
        requestBuilder.set(PSBCBusinessFields.CERT_NUM, certNum);
        //渠道ID，标识交易发起渠道
        requestBuilder.set(PSBCBusinessFields.CHANNEL_ID, channelId);
        //邮储分配的商户号
        requestBuilder.set(PSBCBusinessFields.MCHT_NO, mchtNo);
        return requestBuilder;

    }

    private void addTermDeviceId(Map<String,Object> termInfo, Map<String,Object> transaction){
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String deviceId = MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        if(StringUtil.isNotBlank(deviceId)){
            termInfo.put(PSBCBusinessFields.DEVICE_ID, deviceId);
        }
    }

    @Override
    protected int getNotifyUrlLimit(){
        return NOTIFY_URL_LIMIT;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    protected String getNonValue(String... values){
        String result = "";
        for (String value : values){
            if (StringUtils.isNotBlank(result)){
                break;
            }
            result = value;
        }
        return result;
    }

    protected static void carryOverExtendedParams(Map<String, Object> extended, PSBCRequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            //邮储不支持detail
            if((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || UpayConstant.DETAIL.equals(key)){
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                try {
                    builder.bizSet(key, value instanceof String ? value : objectMapper.writeValueAsString(value));
                } catch (JsonProcessingException e) {
                    logger.error("process extend fields fail: " + e.getMessage(), e);
                }
            }
        }
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, PSBCRequestBuilder builder) {
        boolean formal = builder.getRequest().containsKey(ProtocolV2Fields.APP_AUTH_TOKEN);
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            //邮储不支持goods_detail
            if (UpayConstant.GOODS_DETAIL.equals(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if(!formal){
                //seller_id 只允许直连交易可以透传
                if(TransactionParam.ALIPAY_SELLER_ID.equals(key)){
                    continue;
                }
            }
            if (BusinessV2Fields.EXTEND_PARAMS.equals(key)) {
                String extend = String.format("%s.%s", PSBCBusinessFields.REQ_CONTENT, BusinessV2Fields.EXTEND_PARAMS);
                Map extendParams = null;
                try {
                    extendParams = (Map) MapUtils.getNestedProperty(builder.build(), extend);
                } catch (BuilderException e) {
                    e.printStackTrace();
                }
                if (extendParams == null) {
                    extendParams = new HashMap<>();
                    builder.bizSet(BusinessV2Fields.EXTEND_PARAMS,extendParams);
                }
                if(value == null) continue;
                if (value instanceof Map) {
                    Map valueMap = (Map) value;
                    valueMap.remove(BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID);
                    extendParams.putAll(valueMap);
                }
            } else if (value != null) {
                builder.bizSet(key, value);
            }
        }
    }

    //邮储通道经纬度和基站信息
    public void addPoiOrBaseStation(TransactionContext context, Map<String, Object> termInfo, Map<String, Object> extraParams) {
        String baseStation = BeanUtil.getPropString(extraParams, Transaction.SQB_STATION);
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());

        if (terminalInfo.isSendStandardPoi()) {
            Map<String, Object> poi = MapUtil.hashMap(Transaction.LATITUDE, terminalInfo.getStandardFormatLatitude(), Transaction.LONGITUDE, terminalInfo.getStandardFormatLongitude());
            termInfo.putAll(poi);
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        } else if (StringUtil.isNotBlank(baseStation)) {
            //baseStation
            termInfo.put(PSBCBusinessFields.TERM_INFO_BASE_STATION, baseStation.replaceAll(",", "-"));
        }
    }
}
