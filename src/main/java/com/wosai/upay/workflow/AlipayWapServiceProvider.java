package com.wosai.upay.workflow;

import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
 import java.util.Date;
import java.util.Map;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV1Client;
import com.wosai.mpay.api.alipay.AlipayV1Methods;
import com.wosai.mpay.api.alipay.BusinessV1Fields;
import com.wosai.mpay.api.alipay.ProtocolV1Fields;
import com.wosai.mpay.api.alipay.RequestV1Builder;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

public class AlipayWapServiceProvider extends DirectAlipayV2ServiceProvider {
    public static final String NAME = "provider.alipay.wap";

    private static final SafeSimpleDateFormat CLOSE_DATE_FORMAT = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm");

    private long closeOrderIn = 120000L;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_ALIPAY == MapUtil.getIntValue(transaction, Transaction.PAYWAY)
                && (Order.SUB_PAYWAY_WAP == subPayway ||Order.SUB_PAYWAY_MINI == subPayway)
                && TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(getTradeCurrency(transaction))) {

            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ALIPAY_WAP_TRADE_PARAMS);
    }
    
    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String,Object>)transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV1Builder builder = new RequestV1Builder();
        
        builder.set(ProtocolV1Fields.INPUT_CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_WAP_CREATE_DIRECT_PAY_BY_USER);
        builder.set(BusinessV1Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessV1Fields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        builder.set(BusinessV1Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessV1Fields.TOTAL_FEE, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessV1Fields.SELLER_ID, config.get(TransactionParam.ALIPAY_WAP_PARTNER)+"");
        builder.set(BusinessV1Fields.PAYMENT_TYPE, "1");
//        builder.set(BusinessV1Fields.IT_B_PAY, formatTimeString(System.currentTimeMillis() + 90000));
        builder.set(BusinessV1Fields.IT_B_PAY, CLOSE_DATE_FORMAT.format(new Date(System.currentTimeMillis() + closeOrderIn)));

        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(ProtocolV1Fields.NOTIFY_URL, notifyUrl);
        }
        for(Map.Entry<String, Object> entry: extended.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null) {
                builder.set(key, value.toString());
            }
        }
        String wapGateway = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP);

        try {
        			
            Map<String, String> request = builder.build();
            AlipayV1Client client = new AlipayV1Client(AlipayConstants.CHARSET_UTF8);
            String query = client.buildQuery(wapGateway, BeanUtil.getPropString(config, TransactionParam.PARTNER),
                                             BeanUtil.getPropString(config, TransactionParam.APP_KEY),
                                             request);

            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap("gateway", wapGateway,
                                                                                   "query", query,
                                                                                   "url", String.format("%s?%s", wapGateway, query)));
            return Workflow.RC_CREATE_SUCCESS;
        }catch(BuilderException e) {
            return Workflow.RC_PROTOCOL_ERROR;
        }catch(MpayException e){
            return Workflow.RC_IOEX;
        }catch(MpayApiNetworkError e) {
            return Workflow.RC_IOEX;
        }
    }

}
