package com.wosai.upay.workflow;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;


import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.api.weixin.hkv3.BusinessFields;
import com.wosai.mpay.api.weixin.hkv3.RequestBuilder;
import com.wosai.mpay.api.weixin.hkv3.ResponseFields;
import com.wosai.mpay.api.weixin.hkv3.WeixinConstants;
import com.wosai.mpay.api.weixin.hkv3.WeixinHKV3Client;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class WeixinHKV3WapOrMiniServiceProvider extends WeixinHKV3ServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(WeixinHKV3WapOrMiniServiceProvider.class);
    public static final String NAME = "provider.weixin.hkv3.wapOrMini";
    public static final Map<Integer, String> PRECREATE_SUB_PAYWAY_TRADE_TYPE = MapUtil.hashMap(Order.SUB_PAYWAY_WAP , WeixinConstants.TRADE_TYPE_JSAPI,
            Order.SUB_PAYWAY_MINI, WeixinConstants.TRADE_TYPE_JSAPI);

    public WeixinHKV3WapOrMiniServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat(WeixinConstants.DATE_TIME_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if(tradeParams == null 
                || !TransactionParam.WEIXIN_VERSION_V3.equals(com.wosai.pantheon.util.MapUtil.getString(tradeParams, TransactionParam.WEIXIN_VERSION))
                || !MapUtil.getBooleanValue(tradeParams, TransactionParam.WEIXIN_V3_OVERSEAS, true)){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_WEIXIN
                && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (subPayway == Order.SUB_PAYWAY_WAP) {
            return getTradeParams(transaction, TransactionParam.WEIXIN_WAP_TRADE_PARAMS);
        } else if(subPayway == Order.SUB_PAYWAY_MINI) {
            return getTradeParams(transaction, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
        }
        return null;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context, true);
        builder.set(BusinessFields.DESCRIPTION, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        builder.set(BusinessFields.TRADE_TYPE, PRECREATE_SUB_PAYWAY_TRADE_TYPE.get(subPayway));
        long timeStart = System.currentTimeMillis();
        long timeEnd = System.currentTimeMillis() + DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
        builder.set(BusinessFields.TIME_START, formatTimeString(timeStart));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(timeEnd));
        builder.set(BusinessFields.MERCHANT_CATEGORY_CODE, MapUtil.getString(config, TransactionParam.MERCHANT_WECHAT_INDUSTRY));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(BusinessFields.TOTAL, transaction.get(Transaction.EFFECTIVE_AMOUNT), BusinessFields.CURRENCY, getTradeCurrency(transaction)));
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        if(isUserOfSubAppid(extended, config, MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY))){
            builder.set(BusinessFields.PAYER, MapUtil.hashMap(BusinessFields.SUB_OPEN_ID, payerUid));
        }else{
            builder.set(BusinessFields.PAYER, MapUtil.hashMap(BusinessFields.SP_OPEN_ID, payerUid));
        }
        handlerCustomizedSwitch(builder, config);
        carryOverExtendedParams((Map)transaction.get(Transaction.EXTENDED_PARAMS), builder, WeixinConstants.PRECREATE_ALLOWED_FIELDS);
        if (ApolloConfigurationCenterUtil.getIsWeiXinHXSceneInfo()) {
            Map sceneInfo = getSceneInfo(transaction, extended);
            builder.set(BusinessFields.SCENE_INFO, sceneInfo);
        }
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction , OP_WAP), WeixinHKV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_WAP);
        } catch (Exception ex) {
            logger.error("failed to call weixin precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_WAP);
        if (StringUtil.isNotEmpty(code)){
            return Workflow.RC_ERROR;
        }
        //预下单成功
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        String subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String miniSubAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        if (Order.SUB_PAYWAY_MINI == subPayway && !StringUtil.isEmpty(miniSubAppid)){
            subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        }else if(subPayway == Order.SUB_PAYWAY_WAP) {
            subAppid = com.wosai.pantheon.util.MapUtil.getString(config, TransactionParam.WEIXIN_APP_ID);
        }
        // 优先使用上送的sub_app_id
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, ProtocolFields.SUB_APP_ID);
        if(!StringUtil.isEmpty(sendSubAppId)) {
            subAppid = sendSubAppId;
        }
        
        wapRequest.put(WapFields.APP_ID, subAppid);
        wapRequest.put(WapFields.TIME_STAMP, System.currentTimeMillis()/1000 +"");
        wapRequest.put(WapFields.NONCE_STR, getNonceStr());
        wapRequest.put(WapFields.SIGN_TYPE, RsaSignature.KEY_RSA);
        wapRequest.put(WapFields.PACKAGE, StringUtils.join(WapFields.PREPAY_ID, "=", result.get(ResponseFields.PREPAY_ID)));
        String signParams = wapRequest.get(WapFields.APP_ID)+ "\n" + wapRequest.get(WapFields.TIME_STAMP) + "\n" + wapRequest.get(WapFields.NONCE_STR) + "\n" +  wapRequest.get(WapFields.PACKAGE) + "\n";
        try {
            String signKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.WEIXIN_CERT_CONFIG_KEY));
            String paySign = RsaSignature.sign(signParams, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
            wapRequest.put(WapFields.PAY_SIGN, paySign);
        } catch (MpayException e) {
            return Workflow.RC_ERROR;
        }
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    private String getNonceStr(){
        return new Random().nextLong() + "";
    }

    /**
     * 判断是否是sub_appid的用户
     * @param extendedParams
     * @param config
     * @return
     */
    private boolean isUserOfSubAppid(Map<String,Object> extendedParams, Map<String,Object> config, int subPayway){
        String subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String subAppSecret = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_SECRET);
        if(subPayway == Order.SUB_PAYWAY_MINI){
            String extendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extendedParams, ProtocolFields.SUB_APP_ID);
            String appId = com.wosai.pantheon.util.MapUtil.getString(config, TransactionParam.WEIXIN_APP_ID, "");
            if(appId.equals(extendSubAppId)){
                //上送的与appid值一致，说明是用服务商级别配置的支付小程序获取到的用户信息
                return false;
            }else{
                return true;
            }
        }else if(StringUtil.isNotEmpty(subAppid) && StringUtil.isNotEmpty(subAppSecret)){
            return true;
        }else{
            return false;
        }
    }
}
