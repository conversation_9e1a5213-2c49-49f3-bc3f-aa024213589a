package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by wujianwei on 2018/5/29.
 */
public abstract class AlipayV2WapServiceProvider extends AlipayV2ServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(AlipayV2WapServiceProvider.class);

    private static final SafeSimpleDateFormat expireTimeFormate = new SafeSimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);

    private static String ALIPAY_H5_V2_RETUEN_URL = String.format("%s.%s", Transaction.EXTENDED_PARAMS, Transaction.RETURN_URL);
    private static String SDK_PRE_FREEZE = "alipay-sdk-java-4.8.10.ALL"; // 支付宝SDK版本号，用户预授权下单

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_H5 == subPayway||Order.SUB_PAYWAY_APP == subPayway){
            return h5OrAppPrecreate(context);
        }
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_CREATE);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.SUBJECT, transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.BODY, transaction.get(Transaction.BODY));
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,transaction.get(Transaction.OPERATOR));
        builder.bizSet(BusinessV2Fields.TIMEOUT_EXPRESS, defaultTimeoutExpress);
        String sellerId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SELLER_ID);
        if(!StringUtil.empty(sellerId)){
            builder.bizSet(BusinessV2Fields.SELLER_ID, sellerId);
        }
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        if (payerUid != null) {
            builder.bizSet(BusinessV2Fields.BUYER_ID, payerUid);
        }
        //设置支付宝小程序appid
        if(subPayway == Order.SUB_PAYWAY_MINI){
            setAlipayOpAppIdInfo(config, extended, builder);
        }
        handlerCustomizedSwitch(builder,transaction, authInfo);//一些定制化参数，如是否禁用信用卡，是否传商户自己门店号等。
        carryOverExtendedParams(extended, builder);
        int provider = BeanUtil.getPropInt(transaction, Transaction.PROVIDER);
        // 设置拉卡拉万码银行间连参数
        if(TradeConfigService.PROVIDER_LKLWANMA == provider){
            builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(BusinessV2Fields.MERCHANT_ID,BeanUtil.getPropString(config, TransactionParam.ALIPAY_PID)));
        }

        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, url, request, 1, OP_PRECREATE);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 wap precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            // 支付宝预下单超时后，没有返回支付宝订单信息时，但仍然有可能支付成功，需要进行查单
            if(ex instanceof MpayApiSendError && ((MpayApiSendError)ex).getCause() instanceof SocketTimeoutException) {
                return Workflow.RC_CREATE_SUCCESS;
            }
            return Workflow.RC_IOEX;
        }
        if(result == null){
            return Workflow.RC_IOEX;
        }

        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_SYS_ERROR;
        }else if(!AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_ERROR;
        }
        //预下单成功
        String tradeNo = getRealTradeNo(provider, result);
        transaction.put(Transaction.TRADE_NO, tradeNo);
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        wapRequest.put(WapV2Fields.TRADE_NO, tradeNo);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    /**
     * 设置支付宝小程序相关信息
     */
    private void setAlipayOpAppIdInfo(Map<String,Object> config, Map<String,Object> extended, RequestV2Builder builder){
        String opAppId = MapUtil.getString(extended, TransactionParam.ALIPAY_OP_APP_ID);
        if(StringUtils.isEmpty(opAppId)){
            opAppId = MapUtil.getString(config, TransactionParam.ALIPAY_OP_APP_ID);
        }
        if(!StringUtils.isEmpty(opAppId)){
            builder.bizSet(BusinessV2Fields.OP_APP_ID, opAppId);
            builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_JSAPI_PAY);
            // 处理 payer_uid 是普通的 buyer_id （2088101117955611） 还是 buyer_open_id （01501o8f93I3nJAGB1jG4ONxtxV25DCN3Gec3uggnC4CJU0）,
            Map<String, Object> bizContent = builder.getBizContent();
            String buyerUid = (String) bizContent.get(BusinessV2Fields.BUYER_ID);
            // 支付宝 buyer_id 为 16 位
            if(buyerUid != null && !buyerUid.matches("\\d{16}")){
                builder.bizSet(BusinessV2Fields.OP_BUYER_OPEN_ID, buyerUid);
                bizContent.remove(BusinessV2Fields.BUYER_ID);
            }
        }
    }

    private String h5OrAppPrecreate(TransactionContext context) {
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestV2Builder builder = getAlipayV2Builder(context);
        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        if(subPayway == Order.SUB_PAYWAY_H5){
            Object returnUrl = BeanUtil.getNestedProperty(transaction, ALIPAY_H5_V2_RETUEN_URL);
            if (returnUrl != null && !StringUtil.empty(String.valueOf(returnUrl))) {
                builder.set(ProtocolV2Fields.RETURN_URL, String.valueOf(returnUrl));
            }
        }
        builder.set(ProtocolV2Fields.METHOD, subPayway == Order.SUB_PAYWAY_H5?AlipayV2Methods.ALIPAY_TRADE_WAP_PAY:AlipayV2Methods.ALIPAY_TRADE_APP_PAY);
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.SUBJECT, transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.BODY, transaction.get(Transaction.BODY));
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,transaction.get(Transaction.OPERATOR));
        builder.bizSet(ProtocolV2Fields.TIME_EXPIRE, expireTimeFormate.format(new Date(BeanUtil.getPropLong(transaction, DaoConstants.CTIME)+ UpayConstant.MILLISECOND_OF_SECOND * 244)));

        if (isDirect()) {
            builder.bizSet(BusinessV2Fields.ALIPAY_STORE_ID
                    , BeanUtil.getPropString(config, TransactionParam.ALIPAY_STORE_ID));
        }

        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        if (payerUid != null) {
            builder.bizSet(BusinessV2Fields.BUYER_ID, payerUid);
        }
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        handlerCustomizedSwitch(builder,transaction, authInfo);//一些定制化参数，如是否禁用信用卡，是否传商户自己门店号等。
        carryOverExtendedParams(extended, builder);
        Map<String,String> request;
        String result;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
            return Workflow.RC_IOEX;
        }
        try {
            String signType = BeanUtil.getPropString(config, ProtocolV2Fields.SIGN_TYPE);
            if(AlipayConstants.SIGN_TYPE_RSA2.equals(signType)){
                result = client.buildRequestParams(AlipayConstants.SIGN_TYPE_RSA2, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
            }else{
                result = client.buildRequestParams(AlipayConstants.SIGN_TYPE_RSA, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
            }
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 h5OrApp precreate", ex);
            return Workflow.RC_IOEX;
        }
        if(result == null){
            return Workflow.RC_IOEX;
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> wapRequest = new HashMap<>();
        if (subPayway == Order.SUB_PAYWAY_H5) {
            String h5PayRedirectUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP) + "?" + result;
            wapRequest.put(WapV2Fields.H5_PAY_REDIRECT_URL, h5PayRedirectUrl);
        } else {
            wapRequest.put(WapV2Fields.TRADE_INFO, result);
        }
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }
    
    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.getBizContent().remove(BusinessV2Fields.EXTEND_PARAMS);
        String notifyUrl = getNotifyUrl(notifyHost, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_PREFREEZE), context, NOTIFY_DEPOSIT);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_APP_FREEZE);
        Map<String, Object> authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));

        Map extraParam = (Map) BeanUtil.getProperty(builder.getBizContent(), BusinessV2Fields.EXTRA_PARAM);
        if (MapUtils.isEmpty(extraParam)) {
            extraParam = CollectionUtil.hashMap();
        }
        extraParam.put(BusinessV2Fields.CATEGORY
                , BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_CATEGORY));

        if(config.containsKey(TransactionParam.ALIPAY_SERVICE_ID)) {
            extraParam.put(BusinessV2Fields.SERVICE_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_SERVICE_ID));
        }
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, JsonUtil.toJsonStr(extraParam));
        builder.bizSet(BusinessV2Fields.ORDER_TITLE, (String) transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, (String)transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, (String)transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.PAY_TIMEOUT, defaultTimeoutExpress);
        String payerUid = BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_ID);
        if (payerUid != null) {
            builder.bizSet(BusinessV2Fields.PAYEE_USER_ID, payerUid);
        }
        builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_PRE_AUTH_ONLINE);
        
//        builder.bizSet(BusinessV2Fields.ENABLE_PAY_CHANNELS, DEPOSIT_DEFAULT_ENABLE_PAY_CHANNELS);
        carryOverExtendedParams(extendedParams, builder);

        Map<String,String> request = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        String responseBodyStr = "";
        try {
            request = builder.build();
            request.put(AlipayConstants.ALIPAY_SDK, SDK_PRE_FREEZE);
            request.put(AlipayConstants.FORMAT, AlipayConstants.FORMAT_JSON);
            AlipayV2NewClient.preProcess(AlipayV2NewClient.SIGN_TYPE_ALIPAY, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
            for(String key : request.keySet()) {
                request.put(key, URLEncoder.encode(request.get(key), AlipayConstants.CHARSET_UTF8));
            }
            responseBodyStr = RsaSignature.getSignCheckContent(request);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit freeze", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_PREFREEZE, ex);
            return Workflow.RC_IOEX;
        }
        //预下单成功
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

        Map<String, Object> wapRequest = new HashMap<String, Object>();
        wapRequest.put(WapV2Fields.ORDER_STR, responseBodyStr);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    private boolean isDirect() {
        return this instanceof DirectAlipayV2WapServiceProvider;
    }
}
