package com.wosai.upay.workflow;

import java.util.Map;


public interface MpayServiceProvider {
    String OP_PAY = "pay";
    String OP_CANCEL = "cancel";
    String OP_QUERY = "query";
    String OP_REFUND = "refund";

    String OP_NOTIFY = "notify";
    String OP_REFUND_V2 = "refundv2";
    String OP_PRECREATE = "precreate";
    String OP_CONFIRM_PAY = "confirmPay"; //确认支付
    String OP_DEPOSIT_FREEZE = "deposit.freeze";
    String OP_DEPOSIT_PREFREEZE = "deposit.prefreeze";
    String OP_DEPOSIT_UNFREEZE = "deposit.unfreeze";
    String OP_DEPOSIT_CANCEL = "deposit.cancel";
    String OP_DEPOSIT_QUERY = "deposit.query";
    String OP_DEPOSIT_CONSUME = "deposit.consume";
    String OP_DEPOSIT_REFUND = "deposit.refund";
    String OP_DEPOSIT_REFUND_QUERY = "deposit.refund.query";
    String OP_DEPOSIT_SYNC = "deposit.sync";
    String OP_REFUND_QUERY = "refund.query";
    String OP_CANCEL_QUERY = "cancel.query";
    String OP_CLOSE = "close";
    String OP_WAP = "wap";
    String OP_REDIRECT = "redirect";

    String OP_DEPOSIT_AUTH_APPLY = "deposit.auth.apply";
    String OP_DEPOSIT_AUTH_QUERY = "deposit.auth.query";
    String OP_DEPOSIT_AUTH_TERMINATE = "deposit.auth.terminate";
    String OP_TOKEN = "token";

    String NOTIFY_DEFAULT_CLIENT= "client";
    String NOTIFY_ALIPAY= "alipay";
    String NOTIFY_ALIPAY_DEBIT= "alipayDebit"; //支付宝代扣回调
    String NOTIFY_ALIPAY_NUCC = "alipayNucc";
    String NOTIFY_BESTPAY_NUCC = "bestPayNucc";
    String NOTIFY_ALIPAY_UNIONPAY = "alipayUnionpay";
    String NOTIFY_ALIPAY_WAP = "alipaywap";
    String NOTIFY_ALIPAY_NUCC_WAP = "alipayNuccWap";
    String NOTIFY_ALIPAY_UNIONPAY_WAP = "alipayUnionpayWap";

    String GET_SM4_KEY = "getSm4Key";
    String NOTIFY_WEIXIN = "weixin";
    String NOTIFY_WEIXIN_NUCC = "weixinNucc";
    String NOTIFY_WEIXIN_UNIONPAY = "weixinUnionpay";
    String NOTIFY_WEIXIN_WAP = "weixinwap";
    String NOTIFY_WEIXIN_NUCC_WAP = "weixinNuccWap";
    String NOTIFY_WEIXIN_UNIONPAY_WAP = "weixinUnionpayWap";
    String NOTIFY_UNIONPAY_OPEN = "unionpayOpen";
    String NOTIFY_UNIONPAY_ONLINE = "unionpayOnline";
    String NOTIFY_CIBBANK = "cibbank";
    String NOTIFY_CITICBANK = "citicbank";
    String NOTIFY_SWIFTPASS = "swiftpass";
    String NOTIFY_CMCC = "cmcc";
    String NOTIFY_BESTPAY= "bestpay";
    String NOTIFY_BESTPAY_V2= "bestpayv2";
    String NOTIFY_ALIPAY_TL= "alipayTL";
    String NOTIFY_ALIPAY_WAP_TL= "alipayTLWap";
    String NOTIFY_WEIXIN_TL = "weixinTL";
    String NOTIFY_WEIXIN_WAP_TL = "weixinTLWap";
    String NOTIFY_UNIONPAY_TL = "unionpayTL";
    String NOTIFY_WEIXINV3 = "weixinV3";
    String NOTIFY_DEPOSIT = "deposit";
    String NOTIFY_CHINAUMS = "chinaums";
    String NOTIFY_CMB = "cmbPay";
    String NOTIFY_PSBCBANK = "psbcbank";
    String NOTIFY_CGBBANK = "cgbbank";
    String NOTIFY_HXBANK = "hxbank";
    String NOTIFY_ICBCBANK = "icbcbank";
    String NOTIFY_PREPAID = "prepaid";
    String NOTIFY_LAKALA_OPEN_V3 = "lakalaOpenV3";
    String NOTIFY_WEIXIN_HKV3 = "weixinHKV3";
    String NOTIFY_TL_SYB = "tlSyb";
    String NOTIFY_FOUYOU = "fuyou";
    String NOTIFY_BOCOM = "bocom";
    String NOTIFY_UNIONPAY_HAIKE = "unionpayHaike";
    String NOTIFY_ENTPAY = "entpay";
    String NOTIFY_CMBCBANK = "cmbcbank";
    String NOTIFY_SPDB = "pufa";
    String NOTIFY_JYCARD = NOTIFY_DEFAULT_CLIENT;
    String NOTIFY_JSB = "jsb";//江苏银行


    String NOTIFY_LZCCB = "lzccb";//泸州银行
    String NOTIFY_TLS2P = "tls2p"; // 通联 apple pay
    String NOTIFY_ZTKX = "ztkx";//中投科信
    String NOTIFY_YOP = "yop";//易宝
    String NOTIFY_AIRWALLEX = "airwallex";//airwallex
    String NOTIFY_PSBC = "psbc";//邮储银行山西分行
    String NOTIFY_PKX_AIRPORT = "pkxairport";//首都机场
    String NOTIFY_XZX = "xzx";//新中新
    String NOTIFY_HOPE_EDU = "hopeedu";//院校通
    String NOTIFY_GUOTONG = "guotong";//国通
    String NOTIFY_MACAU_PASS = "macaupass";//澳门通
    String NOTIFY_WECARD = "wecard";//腾讯微卡

    String getName();
    Integer getProvider();
    boolean canHandle(Map<String, Object> transaction);
    Map<String, Object> getTradeParams(Map<String, Object> transaction);
    String pay(TransactionContext context, boolean resume);
    String cancel(TransactionContext context);
    String query(TransactionContext context);
    String refund(TransactionContext context);
    String precreate(TransactionContext context, boolean resume);
    String explainNotification(Map<String, Object> providerNotification);
    String depositQuery(TransactionContext context);
    String depositFreeze(TransactionContext context, boolean resume);
    String depositPreFreeze(TransactionContext context, boolean resume);
    String depositCancel(TransactionContext context);
    String depositConsume(TransactionContext context);

    default String depositSync(TransactionContext context) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    default Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    default Map<String, Object> confirmPay(Map<String, Object> request, Map<String, Object> transaction) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    default String refundQuery(TransactionContext context) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }
}
