package com.wosai.upay.workflow;


import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


import javax.net.ssl.SSLContext;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.mpay.util.*;

import com.wosai.mpay.util.Base64;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.model.ProfitSharing;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.util.*;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

/**
 * Created by wujianwei on 2018/5/24.
 */
public abstract class WeixinServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(DirectWeixinServiceProvider.class);
    public static final String WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE = "CFT";
    public static final String WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX = "DEBIT";
    public static final String WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX = "CREDIT";
    public static final String WEIXIN_PAYMENT_OTHERS = "OTHERS";
    public static final String VERSION_ONE = "1.0";
    public static final String PROMOTION_DETAIL_TYPE_COUPON = "COUPON"; //代金券，需要走结算资金的 充值型代金券
    public static final String PROMOTION_DETAIL_TYPE_DISCOUNT = "DISCOUNT"; //优惠券，不走结算资金的免 充值型优惠券
    protected long defaultTimeExpire = DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
    protected long b2cTimeExpire = B2C_TIME_EXPIRE_MINUTE * 60 * 1000;

    public static String NO_AUTH_MESSAGE = "特约子商户商户号未授权服务商的产品权限";


    protected static final int NOTIFY_URL_LIMIT = 256;

    protected static final String KEY_PROFIT_SHARING_FLAG = Transaction.EXTRA_PARAMS + "." + Transaction.PROFIT_SHARING + "." + ProfitSharing.SHARING_FLAG;
    protected static final String KEY_STORE_CLIENT_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_CLIENT_SN);


    protected static final String KEY_STORE_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_SN);

    protected static final String KEY_STORE_NAME = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_NAME);

    protected static final String KEY_STORE_AREA_CODE = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.DISTRICT_CODE);


    protected static final String WX_FIELD_SCENE_STORE_INFO = String.format("%s.%s", BusinessFields.SCEND_INFO_STORE_INFO, BusinessFields.SCENE_INFO_ID);
    protected static final String WX_FIELD_SCENE_STORE_INFO_NAME = String.format("%s.%s", BusinessFields.SCEND_INFO_STORE_INFO, BusinessFields.SCENE_INFO_NAME);
    protected static final String WX_FIELD_SCENE_STORE_INFO_AREA_CODE = String.format("%s.%s", BusinessFields.SCEND_INFO_STORE_INFO,BusinessFields.SCENE_INFO_AREA_CODE);


    private static final Pattern WEIXIN_SPECIAL_HK_PATTERN = Pattern.compile("15\\d{14,18}");

    protected String notifyHost;

    protected int retryTimes = 3;

    @Autowired
    protected WeixinClient client;


    protected ConcurrentHashMap<String, SSLContext> sslContextMap = new ConcurrentHashMap<String, SSLContext>();

    public WeixinServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(WeixinConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_FEE, BusinessFields.FEE_TYPE, BusinessFields.REFUND_FEE, BusinessFields.REFUND_FEE_TYPE));
    }

    public abstract Map<String,Object> call(Map<String, Object>  config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError;

    /**
     * 获取默认的requestBuilder，设置请求默认值。
     * @param context
     * @return
     */
    public abstract  RequestBuilder getDefaultRequestBuilder(TransactionContext context);


    @Override
    public String pay(TransactionContext context,  boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(ProtocolFields.VERSION, VERSION_ONE);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        if(TradeConfigService.PROVIDER_NUCC != BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
            builder.set(BusinessFields.DETAIL, transaction.get(Transaction.BODY));
        }
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.AUTH_CODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(System.currentTimeMillis() + b2cTimeExpire));
        handlerCustomizedSwitch(builder,transaction);
        String currency = getTradeCurrency(transaction);
        builder.set(BusinessFields.FEE_TYPE, currency);
        if(!TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)) {
            handlerOverseasIndustry(extended, config);
        }
        setDiscountInfo(builder,context);
        setProfitSharing(transaction, builder, BusinessFields.PROFIT_SHARING, WeixinConstants.PROFIT_SHARING_YES);
        String merchantId = (String) transaction.getOrDefault(Transaction.MERCHANT_ID, "");
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if (ApolloConfigurationCenterUtil.getWxNotSharingMerchantSet().contains(merchantId) &&
                (subPayWay == SubPayway.H5.getCode() || subPayWay == SubPayway.APP.getCode())) {
            builder.set(BusinessFields.PROFIT_SHARING, WeixinConstants.PROFIT_SHARING_NO);
        }
        setDefaultAttachInSomeProvider(builder, transaction);
        carryOverExtendedParams(extended, builder, WeixinConstants.PAY_ALLOWED_FIELDS);
        //设置场景字段的顺序一定要在carryOverExtendedParams方法之后
        setSceneInfo(builder, transaction, extended);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), retryTimes, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call weixin pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }

        if(StringUtil.empty(returnCode)){
            return Workflow.RC_IN_PROG;
        }
        setTradeNoBuyerInfoIfExists(result, context);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            String returnMsg = BeanUtil.getPropString(result, ResponseFields.RETURN_MSG);
            if (WeixinConstants.RETURN_MSG_SYSTEM_ERROR.equals(returnMsg)){
                return Workflow.RC_IN_PROG;
            } else if (this instanceof DirectWeixinServiceProvider && WeixinConstants.RETURN_FAIL_MESSAGE.contains(returnMsg)) {
                return Workflow.RC_TRADE_CANCELED;
            } else {
                return Workflow.RC_PROTOCOL_ERROR;
            }
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_ORDER_PAID.equals(errCode)){
                return Workflow.RC_IN_PROG;
            }
            if(WeixinConstants.RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST.contains(errCode)){
                return Workflow.RC_TRADE_CANCELED;
            }
            if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_FAIL_LIST.contains(errCode)){
                return Workflow.RC_TRADE_CANCELED;
            }else if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST.contains(errCode)) {
                return Workflow.RC_IN_PROG;
            }else if(WeixinConstants.RESULT_ERROR_TRADE_ERROR.equals(errCode)){
                String errCodeDes = (String)result.get(ResponseFields.ERR_CODE_DES); //错误代码
                if(WeixinConstants.TRADE_ERROR_FAIL_MESSAGE.contains(errCodeDes)) {
                    return Workflow.RC_TRADE_CANCELED; 
                }
            }else {
                return Workflow.RC_ERROR;
            }
        }

        if(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            boolean success = true;
            //刷卡交易应答报文增加交易状态(trade_state)字段，当return_code 、result_code、 trade_state都为SUCCESS的时收单机构才可认为交易成功
            Integer provider = getProvider();
            if(provider != null && (provider == Order.PROVIDER_UNIONPAY || provider == Order.PROVIDER_DIRECT_UNIONPAY || provider == Order.PROVIDER_TL || provider == Order.PROVIDER_LAKALA_UNION_PAY || provider == Order.PROVIDER_HAIKE_UNION_PAY)){
                String tradeState = (String) result.get(ResponseFields.TRADE_STATE);
                if(!WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)){
                    success = false;
                }
            }
            if(success){
                //付款成功
                setOverseasInfoWhenPay(context.getTransaction(), result);
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return Workflow.RC_IN_PROG;

    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> result;
        boolean needRefund = false;
        if(Order.SUB_PAYWAY_BARCODE == subPayWay){
            try {
                result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), builder.build(), retryTimes, OP_CANCEL);
            } catch (Exception ex) {
                logger.error("failed to call weixin cancel", ex);
                setTransactionContextErrorInfo(context, OP_CANCEL, ex);
                return Workflow.RC_IOEX;
            }
            if (result == null) {
                return Workflow.RC_IOEX;
            }

            String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
            String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
            String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
            setTransactionContextErrorInfo(result, context, OP_CANCEL);
            if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }
            if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
                if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)){
                    return Workflow.RC_RETRY;
                }else if(WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(errCode)) {
                	// 支付请求返回“USERPAYING：用户支付中，需要输入密码”时，不再进行cancel操作，直接返回io异常，由勾兑服务进行处理
                	if(BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                		logger.warn("{}: provider cancel fail, return USERPAYING.", BeanUtil.getPropString(transaction, Transaction.TSN));
                		return Workflow.RC_IOEX;
                	}else {
                		return Workflow.RC_RETRY;
                	}
            	}else if(WeixinConstants.RESULT_ERROR_REVERSE_EXPIRE.equals(errCode)){
                    //超出撤单时限， 查单，如果付款成功，则退款
                    Map queryResult = doQuery(context);
                    String queryReturnCode = (String)queryResult.get(ResponseFields.RETURN_CODE);//返回状态码
                    String queryResultCode = (String)queryResult.get(ResponseFields.RESULT_CODE);//业务结果
                    String tradeState = (String) queryResult.get(ResponseFields.TRADE_STATE);
                    if(WeixinConstants.RETURN_CODE_SUCCESS.equals(queryReturnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(queryResultCode)){
                        if(WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)){
                            needRefund = true;
                        }else{
                            return Workflow.RC_CANCEL_SUCCESS;
                        }
                    }else{
                        return Workflow.RC_ERROR;
                    }
                }else{
                    return Workflow.RC_ERROR;
                }
            }else{
                return Workflow.RC_CANCEL_SUCCESS;
            }

        }else if(Order.SUB_PAYWAY_QRCODE == subPayWay || Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay || Order.SUB_PAYWAY_H5 == subPayWay || Order.SUB_PAYWAY_APP == subPayWay){
            try {
                result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE), builder.build(), retryTimes, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE);
            } catch (Exception ex) {
                logger.error("failed to call weixin close", ex);
                setTransactionContextErrorInfo(context, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE, ex);
                return Workflow.RC_IOEX;
            }
            if (result == null) {
                return Workflow.RC_IOEX;
            }
            String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
            String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
            String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
            setTransactionContextErrorInfo(result, context, OP_CANCEL);
            if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }
            if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
                if(WeixinConstants.RESULT_ERROR_CODE_ORDER_PAID.equals(errCode)){
                    needRefund = true;
                }else if(WeixinConstants.RESULT_ERROR_CODE_ORDER_CLOSED.equals(errCode)){
                    return Workflow.RC_CANCEL_SUCCESS;
                }else if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)
                        ||WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(errCode)){
                    return Workflow.RC_RETRY;
                }else{
                    return Workflow.RC_ERROR;
                }
            }else{
                return Workflow.RC_CANCEL_SUCCESS;
            }
        }
        if(needRefund){
            // 设置撤单转退款标识，由于撤单回调
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.CANCEL_TO_REFUND, true);

            long refundFee = BeanUtil.getPropLong(context.getOrder(), Order.EFFECTIVE_TOTAL);
            String rcFlag = doRefund(context, OP_CANCEL, refundFee);
            if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
                return Workflow.RC_CANCEL_SUCCESS;
            }else{
                return rcFlag;
            }
        }
        return Workflow.RC_ERROR;
    }



    private void setDiscountInfo(RequestBuilder builder,TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> config = getTradeParams(transaction);
        String goodsTag = BeanUtil.getPropString(config, TransactionParam.GOODS_TAG);
        if(StringUtils.empty(goodsTag)){
            return ;
        }
        //15开头的 尝试用device_info字段上送goods_tag信息 如果商户在扩展字段里device_info也上送了
        //那么会由扩展字段里的device_info覆盖这个字段
        if(WEIXIN_SPECIAL_HK_PATTERN.matcher(BeanUtil.getPropString(extraParams,Transaction.BARCODE)).matches()) {
            builder.set(ProtocolFields.DEVICE_INFO, goodsTag);
        }else{
            builder.set(BusinessFields.GOODS_TAG, goodsTag);
        }
    }

    /**
     * 设置分账参数
     * @param transaction
     * @param builder
     */
    public void setProfitSharing(Map<String,Object> transaction, RequestBuilder builder, String key, Object value){
        Map<String, Object> extraParams = com.wosai.pantheon.util.MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> profitSharing = com.wosai.pantheon.util.MapUtil.getMap(extraParams, Transaction.PROFIT_SHARING);
        String flag = com.wosai.pantheon.util.MapUtil.getString(profitSharing, ProfitSharing.SHARING_FLAG);
        String sharingApp = com.wosai.pantheon.util.MapUtil.getString(profitSharing, Transaction.PROFIT_SHARING_SHARING_APP, Transaction.SHARING_APP_PAY);
        if(ProfitSharing.SHARING_FLAG_ENABLE.equals(flag) && Transaction.SHARING_APP_PAY.equals(sharingApp)){
            builder.set(key, value);
        }

    }


    /**
     * 退款查询
     * @param context
     * @return
     */
    private Map<String, Object> reTryRefundQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY), builder.build(), retryTimes, ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin refund query", ex);
        }
        return result;
    }
    
    protected Map<String, Object> retryIfNetworkException(Map<String,Object> config, String url, Map<String,Object> request, int times, String opFlag) throws Exception {
        return retryIfNetworkException(()-> call(config, url, request, opFlag), logger, times, opFlag, "weixin");
    }

    protected String getPaySuccessFlag() {
        return Workflow.RC_PAY_SUCCESS;
    }

    protected int getTransactionPayType() {
        return Transaction.TYPE_PAYMENT;
    }

    @Override
    public String query(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> result = doQuery(context);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            //通讯标识为失败，重新查询
            return Workflow.RC_IN_PROG;
        }
        setTradeNoBuyerInfoIfExists(result, context);
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)){
                return Workflow.RC_IN_PROG;
            }else if(WeixinConstants.RESULT_ERROR_ORDER_NOT_EXIST.equals(errCode)){
            	// 下单成功后，再次查单返回“ORDERNOTEXIST”时，需要进行查单动作
            	Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction, 
            			UpayUtil.getProviderErrorInfoKey(
            					Order.SUB_PAYWAY_BARCODE == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) ? MpayServiceProvider.OP_PAY : MpayServiceProvider.OP_PRECREATE));
            	if(null == payResultMap 
            	        || WeixinConstants.RESULT_CODE_SUCCESS.equals(BeanUtil.getPropString(payResultMap, ResponseFields.RESULT_CODE))
            	        || WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(BeanUtil.getPropString(payResultMap, ResponseFields.ERR_CODE))){
            		return Workflow.RC_IN_PROG;
            	}else{
            		return Workflow.RC_ERROR;
            	}
            }else{
                return Workflow.RC_ERROR;
            }
        }
        String tradeState = (String) result.get(ResponseFields.TRADE_STATE);
        String rcFlag = Workflow.RC_ERROR;
        if(WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)){
            rcFlag = Workflow.RC_IN_PROG;
        }else if(WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState)){
            //跟微信的人确认过，b2c下，not_pay表明用户已经取消了付款。
            rcFlag = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
        }else if(WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)){
            rcFlag = getPaySuccessFlag();
            //付款成功
            setOverseasInfoWhenPay(context.getTransaction(), result);
            if(BeanUtil.getPropInt(transaction, Transaction.TYPE) == getTransactionPayType()){
                resolvePayFund(result, context);
            }
        }else if(WeixinConstants.TRADE_STATE_PAYERROR.equals(tradeState)
                    && WeixinConstants.TRADE_STATE_FAIL_DESC.equals(BeanUtil.getPropString(result, ResponseFields.TRADE_STATE_DESC))) {
            rcFlag = Workflow.RC_TRADE_CANCELED;
        }
        return rcFlag;
    }

    protected Map<String,Object> doQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(ProtocolFields.VERSION, VERSION_ONE);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        Map<String, Object> request = builder.build();
        resetSubAppIdWhenQuery(request, transaction);
        try {
            return retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), request, retryTimes, OP_QUERY);
        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            logger.error("failed to call weixin query", ex);
            return null;
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long refundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        // 预授权交易申请押金退款接口
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(BeanUtil.getPropBoolean(transaction, Transaction.KEY_IS_DEPOSIT) && subPayWay != Order.SUB_PAYWAY_MINI) {
            return doDepositRefund(context, OP_DEPOSIT_REFUND, refundFee);
        }else {
            return doRefund(context, OP_REFUND, refundFee);
        }
    }

    protected String doRefund(TransactionContext context, String operation, long refundFee) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(BeanUtil.getPropBoolean(transaction, Transaction.KEY_IS_DEPOSIT) && subPayWay == Order.SUB_PAYWAY_MINI) {
            builder.set(BusinessFields.TOTAL_FEE, BeanUtil.getPropLong(transaction, Transaction.KEY_DEPOSIT_CONSUME_EFFECTIVE_AMOUNT) + "");
            builder.set(BusinessFields.TRANSACTION_ID, order.get(Order.TRADE_NO));
        }else{
            builder.set(BusinessFields.TOTAL_FEE, order.get(Order.EFFECTIVE_TOTAL)+"");
            builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        }
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.REFUND_FEE, refundFee +  "");
        builder.set(BusinessFields.REFUND_FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.OP_USER_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        carryOverExtendedParams(extended, builder, WeixinConstants.REFUND_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
            if((this instanceof DirectWeixinServiceProvider 
                            || this instanceof DirectWeixinWapOrMiniServiceProvider 
                            || this instanceof DirectWeixinV3PreDepositServiceProvider)) {
                // 上送goods_detail时调用微信v2接口
                String detail = com.wosai.pantheon.util.MapUtil.getString(extended, BusinessFields.DETAIL);
                if(!StringUtils.isEmpty(detail) && detail.contains(BusinessFields.GOODS_DETAIL)) {
                    url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_V2);
                }
            }
            result = retryIfNetworkException(config, url, builder.build(), retryTimes, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call weixin refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            //异常进行重试
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, operation);
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        if(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //退款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ID));
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        }else if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }else{
            String errCodeDes = BeanUtil.getPropString(result, ResponseFields.ERR_CODE_DES, "");
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode) || WeixinConstants.RESULT_ERROR_FREQUENCY_LIMITED.equals(errCode) || WeixinConstants.RESULT_ERROR_BIZERR_NEED_RETRY.equals(errCode)){
                return Workflow.RC_RETRY;
            }else if(errCodeDes.contains("退款请求过于频繁")){
                return Workflow.RC_RETRY;
            }else {
                return Workflow.RC_ERROR;
            }
        }

    }
    
    protected String doDepositRefund(TransactionContext context, String operation, long refundFee) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.TOTAL_FEE, order.get(Order.EFFECTIVE_TOTAL)+"");
        builder.set(BusinessFields.REFUND_FEE, refundFee +  "");
        builder.set(BusinessFields.REFUND_FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.TRANSACTION_ID, order.get(Order.TRADE_NO));
        builder.set(BusinessFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_HMAC_SHA256);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_REFUND), builder.build(), retryTimes, OP_DEPOSIT_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit refund", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_REFUND, ex);
            //异常进行重试
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, operation);
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        if(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //退款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ID));
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        }else if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }else{
            String errCodeDes = BeanUtil.getPropString(result, ResponseFields.ERR_CODE_DES, "");
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode) || WeixinConstants.RESULT_ERROR_FREQUENCY_LIMITED.equals(errCode) || WeixinConstants.RESULT_ERROR_BIZERR_NEED_RETRY.equals(errCode)){
                return Workflow.RC_RETRY;
            }else if(errCodeDes.contains("退款请求过于频繁")){
                return Workflow.RC_RETRY;
            }else {
                return Workflow.RC_ERROR;
            }
        }

    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(ProtocolFields.VERSION, VERSION_ONE);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        if(TradeConfigService.PROVIDER_NUCC != BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
            builder.set(BusinessFields.DETAIL, transaction.get(Transaction.BODY));
        }
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        builder.set(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(System.currentTimeMillis() + defaultTimeExpire));
        handlerCustomizedSwitch(builder,transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        String currency = getTradeCurrency(transaction);
        builder.set(BusinessFields.FEE_TYPE, currency);
        if(!TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)) {
            handlerOverseasIndustry(extended, config);
        }
        setProfitSharing(transaction, builder, BusinessFields.PROFIT_SHARING, WeixinConstants.PROFIT_SHARING_YES);
        //限制未成年人交易
        setLimitPayer(builder, transaction);
        // 设置万码银行服务商参数
        setDefaultAttachInSomeProvider(builder, transaction);
        carryOverExtendedParams(extended, builder, WeixinConstants.PRECREATE_ALLOWED_FIELDS);
        //设置场景字段的顺序一定要在carryOverExtendedParams方法之后
        setSceneInfo(builder, transaction, extended);
        Map<String,Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call weixin precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }

        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)){
                return Workflow.RC_SYS_ERROR;
            }
            return Workflow.RC_ERROR;
        }
        //预下单成功
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.QRCODE, result.get(ResponseFields.CODE_URL));
        return Workflow.RC_CREATE_SUCCESS;

    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(getTransactionPayType() != type){
            return null;
        }
        String paySuccessFlag = getPaySuccessFlag();
        try{
            if(context.isFakeRequest() || validateSign(providerNotification, context)){
                boolean asExpected = true; //回调是否符合预期
                String returnCode = BeanUtil.getPropString(providerNotification, ResponseFields.RETURN_CODE);
                String resultCode = BeanUtil.getPropString(providerNotification, ResponseFields.RESULT_CODE);
                if(!(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode))){
                    asExpected = false;
                }
                String outTradeNo = BeanUtil.getPropString(providerNotification, ResponseFields.OUT_TRADE_NO);
                if(!BeanUtil.getPropString(transaction, Transaction.ORDER_SN).equals(outTradeNo)){
                    asExpected = false;
                }
                long totalFee = BeanUtil.getPropLong(providerNotification, ResponseFields.TOTAL_FEE);
                if(totalFee != BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)){
                    asExpected = false;
                }

                //直连与银联通道可以不用再继续查单
                Integer provider = getProvider();
                if(context.isFakeRequest() || (asExpected && (provider == null || provider == Order.PROVIDER_DIRECT_UNIONPAY || provider == Order.PROVIDER_TL || provider == Order.PROVIDER_LAKALA_UNION_PAY))) {
                    //success
                    processJsonStrings(providerNotification, provider);
                    setTradeNoBuyerInfoIfExists(providerNotification, context);
                    setOverseasInfoWhenPay(context.getTransaction(), providerNotification);
                    resolvePayFund(providerNotification, context);
                    return paySuccessFlag;
                }
            }
        }catch (Exception e){
            logger.error("process notify error ", e);
        }
        //默认还是再查询一遍
        return paySuccessFlag.equals(query(context)) ? paySuccessFlag : null;
    }

    protected boolean validateSign(Map<String, Object> providerNotification, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> config = getTradeParams(transaction);
        Integer provider = getProvider();
        if(provider == null){
            String weixinAppKey = BeanUtil.getPropString(config, TransactionParam.WEIXIN_APP_KEY);
            try {
                return WeixinSignature.checkIsSignValidFromResponse(providerNotification, weixinAppKey, WeixinConstants.CHARSET_UTF8);
            } catch (MpayException e) {
                logger.error("validate sign error", e);
                return false;
            }
        }else if(provider == Order.PROVIDER_DIRECT_UNIONPAY || provider == Order.PROVIDER_TL || provider == Order.PROVIDER_LAKALA_UNION_PAY){
            String publicKeyRsaId = BeanUtil.getPropString(config, TransactionParam.PUBLIC_KEY);
            String sign = BeanUtil.getPropString(providerNotification, ResponseFields.SIGN);
            String signType = MapUtils.getString(providerNotification, ProtocolFields.SIGN_TYPE);
            providerNotification.remove(ResponseFields.SIGN);
            try {
                if(WeixinClient.SIGN_TYPE_SM2.equals(signType)) {
                    String certId = MapUtils.getString(providerNotification, ProtocolFields.CERT_ID);
                    String content = RsaSignature.getSignCheckContent(providerNotification);
                    return SM2Util.getInstance().unionpayVerifySign(content, sign, serviceFacade.getRsaKeyDataById(publicKeyRsaId), certId, WeixinConstants.CHARSET_UTF8);
                }
                return RsaSignature.validateSign(RsaSignature.getSignCheckContent(providerNotification), sign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, serviceFacade.getRsaKeyDataById(publicKeyRsaId));
            } catch (Exception e) {
                logger.error("validate sign error", e);
                return false;
            }
        }
        return false;
    }

    protected void processJsonStrings(Map<String, Object> providerNotification, Integer provider) throws MpayException{
        if(providerNotification != null && providerNotification.containsKey(ResponseFields.PROMOTION_DETAIL)){
            String promotion = (String) providerNotification.get(ResponseFields.PROMOTION_DETAIL);
            if(!StringUtils.isEmpty(promotion)){
                try {
                    Object value = objectMapper.readValue(promotion, Object.class);
                    //银联返回报文不存在嵌套的promotion_detail, 所以在此修改为直连微信一样的嵌套的promotion_detail
                    if(provider != null && (provider == Order.PROVIDER_DIRECT_UNIONPAY || provider == Order.PROVIDER_TL || provider == Order.PROVIDER_LAKALA_UNION_PAY)){
                        Map<String,Object> newValue = new HashMap();
                        newValue.put(ResponseFields.PROMOTION_DETAIL, value);
                        value = newValue;
                    }
                    providerNotification.put(ResponseFields.PROMOTION_DETAIL, value);
                } catch (IOException e) {
                    logger.warn("failed to transform promotion_detail {}", e.getMessage());
                }
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String returnMsg = (String)result.get(ResponseFields.RETURN_MSG);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        String errCodeDes = (String)result.get(ResponseFields.ERR_CODE_DES); //错误代码
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RETURN_CODE, returnCode);
        map.put(ResponseFields.RETURN_MSG, returnMsg);
        map.put(ResponseFields.RESULT_CODE, resultCode);
        map.put(ResponseFields.ERR_CODE, errCode);
        map.put(ResponseFields.ERR_CODE_DES, errCodeDes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, 
                WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode),
                StringUtil.empty(errCode) ? resultCode: errCode,
                StringUtil.empty(errCodeDes) ? returnMsg : errCodeDes
            );
    }

    /**
     * 解析返回金额相关信息
     * @param context
     */
    public static   void resolvePayFund(Map<String, Object> result, TransactionContext context){
        if(result == null){
            return;
        }
        //免充值升级后的接口返回与以前的不一样
        List<Map<String,Object>> promotions = null;
        if(result.get(ResponseFields.PROMOTION_DETAIL)!= null && result.get(ResponseFields.PROMOTION_DETAIL) instanceof String) {
            String promotionDetailStr = (String) result.get(ResponseFields.PROMOTION_DETAIL);
            promotionDetailStr = promotionDetailStr.replaceAll("\\\\", "");
            if(!StringUtils.isEmpty(promotionDetailStr)) {
                Map promotionDetails = JsonUtil.jsonStrToObject(promotionDetailStr, Map.class);
                promotions = (List<Map<String, Object>>) BeanUtil.getProperty(promotionDetails, ResponseFields.PROMOTION_DETAIL);
                result.remove(ResponseFields.PROMOTION_DETAIL);
                BeanUtil.setNestedProperty(result, ResponseFields.RESPONSE_KEY_PROMOTION_DETAIL, promotions);
            }
        }else {
            promotions = (List<Map<String, Object>>) BeanUtil.getNestedProperty(result, ResponseFields.RESPONSE_KEY_PROMOTION_DETAIL);
        }
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL) && promotions != null){
            if(UpayUtil.isReturnProviderResponse(context.getTransaction())) {
                Map<String, Object> extraOutFields = com.wosai.pantheon.util.MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
                extraOutFields.put(Transaction.VOUCHER_DETAILS, promotions);
                // 代金券信息需要从优惠信息中获取
                List<Map<String, Object>> goodsDetails = promotions
                            .stream()
                            .filter(promotion -> Objects.nonNull(promotion.get(BusinessFields.GOODS_DETAIL)) && promotion.get(BusinessFields.GOODS_DETAIL) instanceof List)
                            .map(promotion -> (List<Map<String, Object>>)promotion.get(BusinessFields.GOODS_DETAIL))
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                extraOutFields.put(Transaction.GOODS_DETAILS, goodsDetails);
                context.getTransaction().put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            if(promotions != null){
                Map<String,Object> order = context.getOrder();
                Map<String, Object> transaction = context.getTransaction();

                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                if(payments == null || payments.isEmpty()){
                    extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(result));
                }
                long discountAmount = getSumAmountOfPromotionDetail(promotions);
                if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discountAmount != 0){
                    order.put(Order.TOTAL_DISCOUNT, discountAmount);
                    order.put(Order.NET_DISCOUNT, discountAmount);
                }
                long paidAmount = totalFee - discountAmount;
                if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0){
                    transaction.put(Transaction.PAID_AMOUNT, paidAmount);
                }
                long receiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for(Map<String,Object> promotion: promotions){
                    if(promotion.isEmpty()){
                        continue;
                    }
                    String type = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                    long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
//                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if(PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                        receiveAmount = receiveAmount - (amount - wxpayContribute -otherContribute);

                    }
                }
                if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
                }
            }
        }else{
            Map<String, Object> transaction = context.getTransaction();
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            long discount = BeanUtil.getPropLong(result, ResponseFields.COUPON_FEE, 0);
            long cashFee = totalFee - discount;
            transaction.put(Transaction.PAID_AMOUNT, cashFee);
            if(totalFee > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, totalFee);
            }
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
            if(payments == null || payments.isEmpty()){
                extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(result));
            }
            Map<String,Object> order = context.getOrder();
            if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discount != 0){
                order.put(Order.TOTAL_DISCOUNT, discount);
                order.put(Order.NET_DISCOUNT, discount);
            }
        }

    }

    /**
     * 解析退款返回金额信息
     * @param result
     * @param context
     */
    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        Map<String,Object> order = context.getOrder();
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        setOverseasInfoWhenRefund(transaction, result);
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            //目前只处理海外付款错误的情况
            long paidAmount;
            String currency=getTradeCurrency(transaction);
            if(currency != null && !TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)){
                long refundFee = BeanUtil.getPropLong(result, ResponseFields.REFUND_FEE);
                long couponRefundFee = BeanUtil.getPropLong(result, ResponseFields.COUPON_REFUND_FEE);
                paidAmount = refundFee - couponRefundFee;
            }else{
                paidAmount = BeanUtil.getPropLong(result, ResponseFields.CASH_REFUND_FEE);
            }
            if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0 && paidAmount != 0){
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }

            if(payments != null){
                List<Map<String,Object>> refundPayments = new ArrayList<>();
                for (int i = 0; i < payments.size(); i++) {
                    Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                    refundPayments.add(refundPayment);
                }

                //直连微信 会返回 coupon_refund_count coupon_xxx_$n 等字段, 但是网联银联不会返回，而是返回refund_details字段
                if(result.containsKey(ResponseFields.REFUND_DETAILS)){
                    List<Map<String,Object>> refundDetail = (List<Map<String, Object>>) result.get(ResponseFields.REFUND_DETAILS);
                    if(refundDetail != null){
                        for (int i = 0; i < refundDetail.size(); i++) {
                            Map<String,Object> detail = refundDetail.get(i);
                            String couponRefundId = BeanUtil.getPropString(detail, ResponseFields.REFUND_DETAILS_PROMOTION_ID);
                            long couponRefundFee = BeanUtil.getPropLong(detail, ResponseFields.REFUND_DETAILS_REFUND_AMOUNT);
                            for (int j = 0; j < refundPayments.size(); j++) {
                                Map<String,Object> refundPayment = refundPayments.get(j);
                                String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                                if(sourceId.equals(couponRefundId)){
                                    refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                                }
                            }
                        }
                    }
                }else{
                    int couponCount = BeanUtil.getPropInt(result, ResponseFields.COUPON_REFUND_COUNT);
                    for (int i = 0; i < couponCount; i++) {
                        String couponRefundId = BeanUtil.getPropString(result, ResponseFields.COUPON_REFUND_ID_PREFIX + "_" + i);
                        long couponRefundFee = BeanUtil.getPropLong(result, ResponseFields.COUPON_REFUND_FEE_PREFIX + "_" + i);
                        for (int j = 0; j < refundPayments.size(); j++) {
                            Map<String,Object> refundPayment = refundPayments.get(j);
                            String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                            if(sourceId.equals(couponRefundId)){
                                refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                            }
                        }
                    }
                    // 微信预授权退款返回参数中未明确定义优惠的退款明细，只有退款金额，需要进行处理
                    if(BeanUtil.getPropInt(payTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_FREEZE) {
                        long refundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                        if(0 != refundFee && null != payments && payments.size() > 0) {
                            long totalAmount = 0l;
                            for (Map<String, Object> payment : payments) {
                                totalAmount += BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
                            }
                            if(totalAmount != 0) {
                                refundPayments.clear();
                                for (int i = 0; i < payments.size(); i++) {
                                    Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                                    refundPayment.put(Transaction.PAYMENT_AMOUNT, Math.round((refundFee * 1d/totalAmount) * BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT)));
                                    refundPayments.add(refundPayment);
                                }
                            }
                        }
                    }
                }

                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(!Payment.TYPE_DISCOUNT_SET.contains(type)){
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, paidAmount);
                        break;
                    }
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                long settlementRefundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                        settlementRefundFee = settlementRefundFee - BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT);
                    }
                }
                if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefundFee != 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefundFee);
                }
            }

        }

    }

    @SuppressWarnings("unchecked")
    public static  List<Map<String,Object>> getWeixinPayments(Map<String,Object> result){
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            long cashFee = totalFee - getSumAmountOfPromotionDetail((List<Map<String, Object>>) BeanUtil.getNestedProperty(result, ResponseFields.RESPONSE_KEY_PROMOTION_DETAIL));
            String banktype = BeanUtil.getPropString(result, ResponseFields.BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
            List<Map<String,Object>> promotions = (List<Map<String, Object>>) BeanUtil.getNestedProperty(result, ResponseFields.RESPONSE_KEY_PROMOTION_DETAIL);
            if(promotions != null){
                for(Map<String,Object> promotion: promotions){
                    if(promotion.isEmpty()){
                        continue;
                    }
                    String type = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                    String promotionId = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                    long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
//                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    long channelAmount = wxpayContribute + otherContribute;
                    long mchAmount = amount - channelAmount;
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if(PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                        if(mchAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }else if(channelAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }
                    }else if(PROMOTION_DETAIL_TYPE_COUPON.equals(type)){
                        if(mchAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }else if(channelAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }

                    }
                }
            }
            return  payments;
        }else{
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            long discount = BeanUtil.getPropLong(result, ResponseFields.COUPON_FEE, 0);
            long cashFee = totalFee - discount;
            String banktype = BeanUtil.getPropString(result, ResponseFields.BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
            if(discount > 0){
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }
            return payments;
        }

    }

    public static  Map<String,Object> getWeixinPaymentByBanktype(String banktype, long amount){
        if(amount  <= 0 || StringUtil.empty(banktype)){
            return null;
        }else{
            String type = null;
            if(WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE.equals(banktype) || WEIXIN_PAYMENT_OTHERS.equals(banktype)){
                type = Payment.TYPE_WALLET_WEIXIN;
            }else if(banktype.endsWith(WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX)){
                type = Payment.TYPE_BANKCARD_CREDIT;
            }else if(banktype.endsWith(WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX)){
                type = Payment.TYPE_BANKCARD_DEBIT;
            }else if(!StringUtil.empty(banktype)){
                type = banktype.toUpperCase();
            }
            return CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, type,
                    Transaction.PAYMENT_ORIGIN_TYPE, banktype,
                    Transaction.PAYMENT_AMOUNT, amount
            );
        }
    }


    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        Map<String,Object> config = getTradeParams(transaction);
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        String subOpenId = BeanUtil.getPropString(result, ResponseFields.SUB_OPEN_ID);
        String openId = BeanUtil.getPropString(result, ResponseFields.OPEN_ID);
        String tradeNo = BeanUtil.getPropString(result, ResponseFields.TRANSACTION_ID);
        String timeEnd = BeanUtil.getPropString(result, ResponseFields.TIME_END);
        Map riskInfo = MapUtils.getMap(result, ResponseFields.RISK_INFO);
        if (MapUtils.isNotEmpty(riskInfo)) {
            extraOutFields.put(Transaction.RISK_INFO, riskInfo);
        }

        if(!StringUtil.empty(subOpenId)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
                transaction.put(Transaction.BUYER_UID, subOpenId);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_UID))){
                order.put(Order.BUYER_UID, subOpenId);
            }

        }
        if(StringUtil.empty(BeanUtil.getPropString(extraOutFields, Transaction.WEIXIN_APPID))){
            extraOutFields.put(Transaction.WEIXIN_APPID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
        }
        if(!StringUtil.empty(openId)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))){
                transaction.put(Transaction.BUYER_LOGIN, openId);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                order.put(Order.BUYER_LOGIN, openId);
            }
        }
        if(!StringUtil.empty(tradeNo)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.TRADE_NO))){
                order.put(Order.TRADE_NO, tradeNo);
            }
        }

        if(!StringUtil.empty(timeEnd)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME))){
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(timeEnd));
            }
        }
        if(result.containsKey(ResponseFields.SUB_IS_SUBSCRIBE) && UpayUtil.isReturnProviderResponse(context.getTransaction())) {
            extraOutFields.put(Transaction.SUB_IS_SUBSCRIBE, com.wosai.pantheon.util.MapUtil.getString(result, ResponseFields.SUB_IS_SUBSCRIBE));
        }
    }


    /**
     * 判断是否是sub_appid的用户
     * @param extendedParams
     * @param config
     * @return
     */
    public static boolean isUserOfSubAppid(Map<String,Object> extendedParams, Map<String,Object> config, int subPayway){
        String subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String subAppSecret = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_SECRET);
        if(subPayway == Order.SUB_PAYWAY_MINI){
            String extendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extendedParams, ProtocolFields.SUB_APP_ID);
            String appId = com.wosai.pantheon.util.MapUtil.getString(config, TransactionParam.WEIXIN_APP_ID, "");
            if(appId.equals(extendSubAppId)){
                //上送的与appid值一致，说明是用服务商级别配置的支付小程序获取到的用户信息
                return false;
            }else{
                return true;
            }
        }else if(!StringUtil.empty(subAppid) && !StringUtil.empty(subAppSecret)){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 计算微信总的优惠信息
     * @param promotions
     * @return
     */
    protected static long getSumAmountOfPromotionDetail(List<Map<String, Object>> promotions){
        long sum = 0;
        if(promotions != null){
            for(Map<String,Object> promotion: promotions){
                if(promotion.isEmpty()){
                    continue;
                }
                long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                sum = sum + amount;
            }
        }
        return sum;
    }


    /**
     * 银行服务商以一个子标识码接入大客户模式下,需要将大客户下的商户信息在请求支付接口中的 attach 中传递过来,以满足业务管理和风控要求
     *
     * @param transaction
     * @return
     */
    public static String getAttach(Map<String,Object> transaction){
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        String merchantSn = BeanUtil.getPropString(configSnapshot, TransactionParam.MERCHANT_SN);
        String merchantName = BeanUtil.getPropString(configSnapshot, TransactionParam.MERCHANT_NAME);
        //(utf-8编码下，一个中文对应3到4个字节) attach最大长度128字节
        //"bank_mch_name=&bank_mch_id="为27个字节, bank_mch_id最大为20个字节,暂时预留10个字节 剩下71个字节，可用于bank_mch_name
        if(merchantName.getBytes().length <= 71){
        	return StringUtils.join(BusinessFields.ATTACH_BANK_MCH_NAME, "=", merchantName, "&", BusinessFields.ATTACH_BANK_MCH_ID, "=", merchantSn);

        }else{
            StringBuilder newMerchantName = new StringBuilder();
            long newMerchantNameByteLength = 0;
            for(String s: merchantName.split("")){
                int length = s.getBytes().length;
                if(newMerchantNameByteLength + length <= 71){
                    newMerchantName.append(s);
                    newMerchantNameByteLength = newMerchantNameByteLength + length;
                }else{
                    break;
                }
            }
            return StringUtils.join(BusinessFields.ATTACH_BANK_MCH_NAME, "=", newMerchantName.toString(), "&", BusinessFields.ATTACH_BANK_MCH_ID, "=", merchantSn);
        }
    }



    protected void limitCredit(RequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction,Transaction.CONFIG_SNAPSHOT),TransactionParam.ALLOW_CREDIT_PAY,TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_PAY, WeixinConstants.NO_CREDIT);
        }
    }

    protected void handlerCustomizedSwitch(RequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY,TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_PAY, WeixinConstants.NO_CREDIT);
        }
        String clientStoreSn = (String) BeanUtil.getNestedProperty(transaction, Transaction.CONFIG_SNAPSHOT + "." + TransactionParam.STORE_CLIENT_SN);
        if(TransactionParam.USE_CLIENT_STORE_SN_YES == BeanUtil.getPropInt(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.USE_CLIENT_STORE_SN,TransactionParam.USE_CLIENT_STORE_SN_NO) && !StringUtil.empty(clientStoreSn)){
            Map storeInfo  =  CollectionUtil.hashMap(BusinessFields.SCENE_INFO_ID, clientStoreSn);
            try {
                builder.set(BusinessFields.SCENE_INFO,objectMapper.writeValueAsString(CollectionUtil.hashMap(BusinessFields.SCEND_INFO_STORE_INFO, storeInfo)));
            } catch (JsonProcessingException e) {
                logger.error("process scene_info fields fail: " + e.getMessage(), e);
            }

        }
        
    }
    
    protected void handlerOverseasIndustry(Map<String, Object> extended, Map<String, Object> config) {
        Map detailInfo = (Map) extended.get(BusinessFields.DETAIL);
        if(null == detailInfo) {
            detailInfo = new HashMap();
            extended.put(BusinessFields.DETAIL, detailInfo);
        }
        List<Map> goodsDetail = (List<Map>) detailInfo.get(BusinessFields.GOODS_DETAIL);
        if(null == goodsDetail) {
            goodsDetail = new ArrayList<Map>();
            detailInfo.put(BusinessFields.GOODS_DETAIL, goodsDetail);
        }
        if(goodsDetail.size() == 0) {
            goodsDetail.add(
                    CollectionUtil.hashMap(BusinessFields.WXPAY_GOODS_ID, BeanUtil.getPropString(config, TransactionParam.MERCHANT_WECHAT_INDUSTRY)));
        }else {
            for (Map detail : (List<Map>)detailInfo.get(BusinessFields.GOODS_DETAIL)) {
                if(null != detail)
                    detail.put(BusinessFields.WXPAY_GOODS_ID, BeanUtil.getPropString(config, TransactionParam.MERCHANT_WECHAT_INDUSTRY));
            }
        }
    }

    /**
     * 设置境外支付相关信息， 如果不是境外支付不设置。
     * 设置到transaction.extra_out_fields.overseas里面
     * @param transaction
     * @param payResult
     */
    protected  void setOverseasInfoWhenPay(Map<String,Object> transaction, Map<String,Object> payResult){
        if(!isOversea(transaction) || payResult == null || payResult.isEmpty()){
            return;
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        String currency = BeanUtil.getPropString(payResult, ResponseFields.CASH_FEE_TYPE);
        String transAmountCNY = BeanUtil.getPropString(payResult, ResponseFields.CASH_FEE);
        if (!StringUtil.empty(currency)) {
            overseas.put(TransactionParam.CURRENCY, currency);
        }
        if (!StringUtil.empty(transAmountCNY)) {
            overseas.put(TransactionParam.TRANS_AMOUNT_CNY, transAmountCNY);
        }
    }

    /**
     * 设置境外支付相关信息， 如果不是境外支付不设置。
     * 设置到transaction.extra_out_fields.overseas里面
     * @param transaction
     * @param refundResult
     */
    protected void setOverseasInfoWhenRefund(Map<String,Object> transaction, Map<String,Object> refundResult){
        if(!isOversea(transaction) || refundResult == null || refundResult.isEmpty()){
            return;
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        String currency = BeanUtil.getPropString(refundResult, ResponseFields.CASH_FEE_TYPE);
        String refundAmountCNY = BeanUtil.getPropString(refundResult, ResponseFields.CASH_REFUND_FEE);
        if (!StringUtil.empty(currency)) {
            overseas.put(TransactionParam.CURRENCY, currency);
        }
        if (!StringUtil.empty(refundAmountCNY)) {
            overseas.put(TransactionParam.REFUND_AMOUNT_CNY, refundAmountCNY);
        }
    }

    /**
     *  判断是否是境外支付，根据币种来判断
     * @param transaction
     * @return
     */
    private  boolean isOversea(Map<String,Object> transaction){
        String currency = getTradeCurrency(transaction);
        if(currency != null && !TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)){
            return true;
        }else{
            return false;
        }
    }

    public  SSLContext getSSLContext(String certConfigKey, String password){
        if(sslContextMap.get(certConfigKey) == null){
            synchronized (this){
                if(sslContextMap.get(certConfigKey) == null){
                    String data = getPrivateKeyContent(certConfigKey);
                    if(StringUtils.isEmpty(data)){
                        return null;
                    }
                    byte [] certData = Base64.decode(data);
                    SSLContext sslContext =  client.getSSLContext(certData, password);
                    sslContextMap.put(certConfigKey, sslContext);
                }
            }
        }
        return sslContextMap.get(certConfigKey);
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }



    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)){
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                try {
                    builder.set(key, value instanceof String ? value : objectMapper.writeValueAsString(value));
                } catch (JsonProcessingException e) {
                    logger.error("process extend fields fail: " + e.getMessage(), e);
                }
            }
        }
    }

    @Override
    protected int getNotifyUrlLimit(){
        return NOTIFY_URL_LIMIT;
    }
    
    /**
    *
    * 获取微信调用凭证
    * 
    * 注意：当前只有直连和银联能拿到支付秘钥，其它渠道需要先配置支付密钥后再才能使用
    * 
    * @param config
    * @param rawdata
    * @return
    */
   public Map<String, Object> getWxpayfaceAuthinfo(Map<String,Object> config, String rawdata){
       Map<String,Object> tradeConfig = (Map<String, Object>) config.get(TransactionParam.WEIXIN_TRADE_PARAMS);
       if (this instanceof DirectUnionPayWeixinServiceProvider) {
           tradeConfig = (Map<String, Object>) config.get(TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS);
       } else if(this instanceof LklUnionPayWeixinServiceProvider) {
           tradeConfig = (Map<String, Object>) config.get(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS);
       }
       RequestBuilder builder = new RequestBuilder();
       builder.set(BusinessFields.STORE_ID, config.get(TransactionParam.STORE_SN));
       builder.set(BusinessFields.STORE_NAME, config.get(TransactionParam.STORE_NAME));
       builder.set(BusinessFields.DEVICE_ID, config.get(TransactionParam.TERMINAL_SN));
       builder.set(BusinessFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_MD5);
       builder.set(BusinessFields.RAWDATA, rawdata);
       builder.set(BusinessFields.NOW, System.currentTimeMillis()/1000 + "");
       builder.set(ProtocolFields.APP_ID, tradeConfig.get(TransactionParam.WEIXIN_APP_ID));
       builder.set(ProtocolFields.SUB_APP_ID, tradeConfig.get(TransactionParam.WEIXIN_SUB_APP_ID));
       builder.set(ProtocolFields.MCH_ID, tradeConfig.get(TransactionParam.WEIXIN_MCH_ID));
       builder.set(ProtocolFields.SUB_MCH_ID, tradeConfig.get(TransactionParam.WEIXIN_SUB_MCH_ID));
       builder.set(ProtocolFields.VERSION, WeixinConstants.DEFAULT_VERSION);
       Map<String,Object> result;
       try {
           result = retryIfNetworkException(tradeConfig, ApolloConfigurationCenterUtil.getProviderGatewayConfig(DirectWeixinServiceProvider.NAME, ApolloConfigurationCenterUtil.GATEWAY_OP_WXPAYFACEAUTHINFO), builder.build(), retryTimes, ApolloConfigurationCenterUtil.GATEWAY_OP_WXPAYFACEAUTHINFO);
       }catch (Exception ex){
           logger.error("failed to call weixin getWxpayfaceAuthinfo", ex);
           return CollectionUtil.hashMap(ResponseFields.ERR_CODE_DES,ex.getMessage());
       }

       return result;
   }

    protected void setSceneInfo(RequestBuilder builder, Map<String, Object> transaction, Map<String, Object> extended) {
        String storeId = BeanUtil.getPropString(transaction, KEY_STORE_CLIENT_SN);
        //非微信直连或者门店号为空不作处理
        if (!(this instanceof DirectWeixinServiceProvider
                || this instanceof DirectWeixinWapOrMiniServiceProvider)
                || StringUtils.isEmpty(storeId)
        ) {
            return;
        }

        Object sceneInfoObj = BeanUtil.getProperty(extended, BusinessFields.SCENE_INFO);
        if (Objects.isNull(sceneInfoObj)) {
            sceneInfoObj = new HashMap<>();
        }
        if (sceneInfoObj instanceof String) {
            try {
                sceneInfoObj = JsonUtil.jsonStringToObject((String) sceneInfoObj, Map.class);
            } catch (Exception e) {
                logger.warn("微信场景参数字符串转JSON异常, 异常栈: ", e);
                return;
            }
        }

        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        boolean storeSceneSwitch = MapUtil.getBooleanValue(configSnapshot, Transaction.STORE_SCENE_SWITCH);
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if (storeSceneSwitch && subPayWay == Order.SUB_PAYWAY_BARCODE) {
            Object extendStoreId = BeanUtil.getNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO);
            if (Objects.isNull(extendStoreId) || String.valueOf(extendStoreId).length() == 0) {
                String storeSn = BeanUtil.getPropString(transaction, KEY_STORE_SN);
                BeanUtil.setNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO, storeSn);
            }

            Object extendStoreName = BeanUtil.getNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO_NAME);
            if (Objects.isNull(extendStoreName) || String.valueOf(extendStoreName).length() == 0) {
                String storeName = BeanUtil.getPropString(transaction, KEY_STORE_NAME);
                if (Objects.nonNull(storeName)) {
                    BeanUtil.setNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO_NAME, storeName);
                }
            }

            Object extendStoreAreaCode = BeanUtil.getNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO_AREA_CODE);
            if (Objects.isNull(extendStoreAreaCode) || String.valueOf(extendStoreAreaCode).length() == 0) {
                String storeAreaCode = BeanUtil.getPropString(transaction, KEY_STORE_AREA_CODE);
                if (Objects.nonNull(storeAreaCode) && StoreAddressUtil.loadStoreAddress().contains(storeAreaCode)) {
                    BeanUtil.setNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO_AREA_CODE, storeAreaCode);
                }
            }
            builder.set(BusinessFields.SCENE_INFO, JSON.toJSONString(sceneInfoObj));
        } else {
            Object extendStoreId = BeanUtil.getNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO);
            if (Objects.isNull(extendStoreId) || String.valueOf(extendStoreId).length() == 0) {
                BeanUtil.setNestedProperty(sceneInfoObj, WX_FIELD_SCENE_STORE_INFO, storeId);
                builder.set(BusinessFields.SCENE_INFO, JSON.toJSONString(sceneInfoObj));
            }
        }

    }

    /**
     * 设置默认的attach商户信息
     * @param builder
     * @param transaction
     */
   protected void setDefaultAttachInSomeProvider(RequestBuilder builder, Map<String, Object> transaction){
       int provider = BeanUtil.getPropInt(transaction, Transaction.PROVIDER);
       String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
       if(TradeConfigService.PROVIDER_LKLWANMA == provider
               || Order.PROVIDER_LAKALA_UNION_PAY == provider
               || TradeConfigService.PROVIDER_DIRECT_UNIONPAY == provider
               || Order.PROVIDER_HAIKE_UNION_PAY == provider) {
           if (GrayReleaseUtil.shouldEnable(ApolloConfigurationCenterUtil.getGraySendAttachPercent(provider), merchantId)) {
               builder.set(BusinessFields.ATTACH, getAttach(transaction));
           }
       }
   }

    /**
     * 设置未成年人交易限制
     * @param builder
     * @param transaction
     */
    protected void setLimitPayer(RequestBuilder builder, Map<String, Object> transaction){
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.EXTRA_PARAMS);

        boolean limitPayer = BeanUtil.getPropBoolean(configSnapshot, TransactionParam.LIMIT_PAYER);
        if (extraParams.containsKey(Transaction.SQB_LIMIT_PAYER_ADULT)) {
            limitPayer = BeanUtil.getPropBoolean(extraParams, Transaction.SQB_LIMIT_PAYER_ADULT);
        }
        if (limitPayer) {
            //限制未成年人交易
            builder.set(BusinessFields.LIMIT_PAYER, WeixinConstants.ADULT);
        }
    }

    protected void setTerminalInfo(TransactionContext context, Map<String, Object> configSnapshot, Map<String, Object> tradeParams, RequestBuilder builder) {
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> termInfo = CollectionUtil.hashMap(
                BusinessFields.TERM_INFO_DEVICE_TYPE, terminalInfo.getOrDefaultType(WeixinConstants.TERM_INFO_TERMINAL_TYPE)
                , BusinessFields.TERM_INFO_DEVICE_ID, terminalInfo.getId()
        );
        if(terminalInfo.getSerialNum() != null){
            termInfo.put(BusinessFields.TERM_INFO_SERIAL_NUM, terminalInfo.getSerialNum());
        }
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
            termInfo.put(BusinessFields.TERM_INFO_LOCATION, terminalInfo.getFormatLatitude() + "/" + terminalInfo.getFormatLongitude());
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }
        if (!terminalInfo.isOffset() && terminalInfo.isSendIp()) {
            termInfo.put(BusinessFields.TERM_INFO_DEVICE_IP, terminalInfo.getIp());
        }

        builder.set(BusinessFields.MERCHANT_NAME, getSubMerchantName(tradeParams, transaction));
        builder.set(BusinessFields.CHANNEL_NAME, MapUtils.getString(configSnapshot
                , TransactionParam.CHANNEL_NAME));
        builder.set(BusinessFields.AREA_INFO, MapUtils.getString(configSnapshot
                , TransactionParam.DISTRICT_CODE));
        builder.set(BusinessFields.TERMINAL_INFO, JSON.toJSONString(termInfo));
    }

    /**
     * 移除不合法的字段，避免交易失败
     * @param request
     */
    protected void removeIllegalFields(Map<String, Object> request){
        //间连微信 会校验 detail字段的格式。 因为我们系统存储的基本上不是合法的detail格式。不合法的不上送
        Object detail = request.get(BusinessFields.DETAIL);
        // 由于有的商户之前有上传detail扩展参数，但是具体字段格式却不对，导致交易失败。 故下面还新增一个 love_feast 的限制条件，
        // 此值为智慧门店与微信商定的固定值，达到只有智慧门店的爱心餐才上送
        boolean detailIsLegal = detail instanceof String && ((String) detail).contains("love_feast") && ((String) detail).startsWith("{") && ((String) detail).endsWith("}");
        if(!detailIsLegal || !ApolloConfigurationCenterUtil.sendWeixinIndirectDetailField()){
            request.remove(BusinessFields.DETAIL);
        }
    }

    /**
     * 查单的时候，上送支付时候的sub_appid值
     * @param request
     * @param transaction
     */
    private void resetSubAppIdWhenQuery(Map<String,Object> request, Map<String,Object> transaction){
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        //不包含这个key， 说明支付请求没有记录相关值，此时不做处理
        if(!extraOutFields.containsKey(Transaction.WEIXIN_SUB_APPID)){
            return;
        }
        String subAppId = MapUtil.getString(extraOutFields, Transaction.WEIXIN_SUB_APPID);
        if(subAppId == null){
            request.remove(ProtocolFields.SUB_APP_ID);
        }else{
            request.put(ProtocolFields.SUB_APP_ID, subAppId);
        }
    }

}
