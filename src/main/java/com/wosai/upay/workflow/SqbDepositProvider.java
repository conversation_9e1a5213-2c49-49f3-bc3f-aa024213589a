package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.PaymentUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * Description: 收钱吧预授权
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/10/25
 */
public class SqbDepositProvider implements MpayServiceProvider {

    private static final Logger logger = LoggerFactory.getLogger(SqbDepositProvider.class);

    @Resource
    private DataRepository dataRepository;
    @Resource
    private WorkflowManager workflowManager;
    @Resource
    private GatewaySupportService gatewaySupportService;
    /**
     * 流水类型转换
     */
    Map<Integer, Integer> transactionTypeMapping = MapUtil.hashMap(
            Transaction.TYPE_DEPOSIT_FREEZE, Transaction.TYPE_PAYMENT,
            Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_PAYMENT,
            Transaction.TYPE_DEPOSIT_CANCEL, Transaction.TYPE_REFUND
    );

    @Override
    public String query(TransactionContext context) {
        return context.getServiceProvider().query(context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Integer payType = MapUtil.getInteger(transaction, Transaction.TYPE);
        try {
            BeanUtil.setNestedProperty(transaction, Transaction.DEPOSIT_CALLER, Transaction.DEPOSIT_CALLER_CONSUME_REFUND);
            //针对海科匹配问题
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEPOSIT, false);
            MpayServiceProvider provider = context.getServiceProvider();
            transaction.put(Transaction.TYPE, payType);
            return provider.refund(context);
        } finally {
            transaction.remove(Transaction.DEPOSIT_CALLER);
            BeanUtil.setNestedProperty(transaction, Transaction.TYPE, payType);
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEPOSIT, true);
        }
    }

    @Override
    public String depositQuery(TransactionContext context) {
        return doExecute(context, () -> {
            String result = workflowManager.matchServiceProvider(context.getTransaction()).query(context);
            if (Objects.equals(result, Workflow.RC_PAY_SUCCESS)) {
                result = Workflow.RC_PAY_SUCCESS;
            } else if (Objects.equals(result, Workflow.RC_IN_PROG)) {
                result = Workflow.RC_IN_PROG;
            } else if (Objects.equals(result, Workflow.RC_TRADE_CANCELED)) {
                result = Workflow.RC_TRADE_CANCELED;
            } else {
                logger.info("depositQuery convert error. state:{}", result);
                result = Workflow.RC_ERROR;
            }
            return result;
        });
    }

    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        return doExecute(context, () -> {
            //设置交易类型
            Map<String, Object> transaction = context.getTransaction();
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_DEPOSIT_PAY_TYPE, Transaction.TYPE_PAYMENT);
            String result = workflowManager.matchServiceProvider(context.getTransaction()).pay(context, resume);
            if (Objects.equals(result, Workflow.RC_PAY_SUCCESS)) {
                result = Workflow.RC_PAY_SUCCESS;
            } else if (Objects.equals(result, Workflow.RC_IN_PROG)) {
                result = Workflow.RC_IN_PROG;
            } else if (Objects.equals(result, Workflow.RC_TRADE_CANCELED)) {
                result = Workflow.RC_TRADE_CANCELED;
            } else if (Objects.equals(result, Workflow.RC_TRADE_DISCARD)) {
                result = Workflow.RC_TRADE_DISCARD;
            } else {
                logger.info("depositFreeze convert error. state:{}", result);
                result = Workflow.RC_ERROR;
            }
            return result;
        });
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        return doExecute(context, () -> {
            Map<String, Object> transaction = context.getTransaction();
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_DEPOSIT_PAY_TYPE, Transaction.TYPE_PAYMENT);
            String result = workflowManager.matchServiceProvider(context.getTransaction()).precreate(context, resume);
            if (Objects.equals(result, Workflow.RC_CREATE_SUCCESS)) {
                result = Workflow.RC_CREATE_SUCCESS;
            } else if (Objects.equals(result, Workflow.RC_PAY_SUCCESS)) {
                result = Workflow.RC_CREATE_SUCCESS;
            } else if (Objects.equals(result, Workflow.RC_TRADE_CANCELED)) {
                result = Workflow.RC_TRADE_CANCELED;
            } else if (Objects.equals(result, Workflow.RC_TRADE_DISCARD)) {
                result = Workflow.RC_TRADE_DISCARD;
            } else {
                logger.info("depositPreFreeze convert error. state:{}", result);
                result = Workflow.RC_ERROR;
            }
            return result;
        });
    }

    @Override
    public String depositCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Integer depositType = MapUtil.getInteger(transaction, Transaction.TYPE);
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        Integer payType = Transaction.TYPE_REFUND;
        //金额小于0走撤单模式
        if (effectiveAmount <= 0L) {
            payType = Transaction.TYPE_CANCEL;
        }
        //设置交易类型
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_DEPOSIT_PAY_TYPE, payType);
        try {
            BeanUtil.setNestedProperty(transaction, Transaction.DEPOSIT_CALLER, Transaction.DEPOSIT_CALLER_CANCEL);
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEPOSIT, false);
            transaction.put(Transaction.TYPE, payType);
            String result = Workflow.RC_ERROR;
            // 退款
            if (Objects.equals(Transaction.TYPE_REFUND, payType)) {
                result = workflowManager.matchServiceProvider(transaction).refund(context);
                if (Objects.equals(result, Workflow.RC_REFUND_SUCCESS)) {
                    result = Workflow.RC_CANCEL_SUCCESS;
                } else if (Objects.equals(result, Workflow.RC_RETRY)) {
                    result = Workflow.RC_RETRY;
                } else {
                    logger.info("depositCancel convert error.payType:{},state:{}", payType, result);
                    result = Workflow.RC_ERROR;
                }
            }
            // 撤单
            else if (Objects.equals(Transaction.TYPE_CANCEL, payType)) {
                result = workflowManager.matchServiceProvider(transaction).cancel(context);
                if (Objects.equals(result, Workflow.RC_CANCEL_SUCCESS)) {
                    result = Workflow.RC_CANCEL_SUCCESS;
                } else if (Objects.equals(result, Workflow.RC_RETRY)) {
                    result = Workflow.RC_RETRY;
                } else {
                    logger.info("depositCancel convert error.payType:{},state:{}", payType, result);
                    result = Workflow.RC_ERROR;
                }
            }
            return result;
        } finally {
            transaction.remove(Transaction.DEPOSIT_CALLER);
            transaction.put(Transaction.TYPE, depositType);
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEPOSIT, true);
        }
    }

    @Override
    public String depositConsume(TransactionContext context) {
        Map<String, Object> consumeTransaction = context.getTransaction();

        Map<String, Object> freezeTransaction = getFreezeTransaction(context.getOrder());
        //冻结金额
        long freezeAmount = MapUtil.getLongValue(freezeTransaction, Transaction.EFFECTIVE_AMOUNT);
        //完成金额
        long consumeAmount = MapUtil.getLongValue(consumeTransaction, Transaction.EFFECTIVE_AMOUNT);
        //退款金额
        long refundAmount = freezeAmount - consumeAmount;
        //回写退款金额
        BeanUtil.setNestedProperty(consumeTransaction, Transaction.KEY_DEPOSIT_REFUND_EFFECTIVE_AMOUNT, refundAmount);
        //将预授权完成的payments给置空
        BeanUtil.setNestedProperty(consumeTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, new ArrayList<>());
        //无差额
        if (refundAmount <= NumberUtils.LONG_ZERO) {
            return Workflow.RC_CONSUME_SUCCESS;
        }
        //设置交易类型
        BeanUtil.setNestedProperty(consumeTransaction, Transaction.KEY_DEPOSIT_PAY_TYPE, Transaction.TYPE_REFUND);
        Integer depositType = MapUtil.getInteger(consumeTransaction, Transaction.TYPE);
        return doExecute(context, () -> {
            String result;
            try {
                consumeTransaction.put(Transaction.DEPOSIT_CALLER, Transaction.DEPOSIT_CALLER_CONSUME_REFUND);
                consumeTransaction.put(Transaction.TYPE, Transaction.TYPE_REFUND);
                consumeTransaction.put(Transaction.EFFECTIVE_AMOUNT, refundAmount);
                //差额退款操作
                result = workflowManager.matchServiceProvider(consumeTransaction).refund(context);
                if (Objects.equals(result, Workflow.RC_REFUND_SUCCESS)) {
                    result = Workflow.RC_CONSUME_SUCCESS;
                } else if (Objects.equals(result, Workflow.RC_RETRY)) {
                    result = Workflow.RC_RETRY;
                } else {
                    result = Workflow.RC_ERROR;
                }
            } finally {
                consumeTransaction.remove(Transaction.DEPOSIT_CALLER);
                //完成后需重新恢复金额
                consumeTransaction.put(Transaction.TYPE, depositType);
                consumeTransaction.put(Transaction.EFFECTIVE_AMOUNT, consumeAmount);
            }
            return result;
        });
    }

    private String doExecute(TransactionContext context, Supplier<String> supplier) {
        Map<String, Object> transaction = context.getTransaction();
        Integer depositType = MapUtil.getInteger(transaction, Transaction.TYPE);
        Integer payType = transactionTypeMapping.get(depositType);
        try {
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEPOSIT, false);
            transaction.put(Transaction.TYPE, payType);
            return supplier.get();
        } finally {
            transaction.put(Transaction.TYPE, depositType);
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEPOSIT, true);
        }
    }

    private Map<String, Object> getFreezeTransaction(Map<String, Object> order) {
        final String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        Map<String, Object> freezeTransaction = dataRepository.getFreezeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
        if (Objects.isNull(freezeTransaction)) {
            freezeTransaction = gatewaySupportService.getTransactionByClientTsn(merchantId, BeanUtil.getPropString(order, Order.SN), BeanUtil.getPropString(order, Order.CLIENT_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        if (Objects.isNull(freezeTransaction)) {
            throw new UpayBizException("预授权交易流水不存在");
        }
        return freezeTransaction;
    }


    //-----------------　无需实现的方法　-----------------------
    @Override
    public String getName() {
        return null;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return false;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String cancel(TransactionContext context) {
        return null;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    @Override
    public String depositSync(TransactionContext context) {
        return null;
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        return null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return null;
    }
}
