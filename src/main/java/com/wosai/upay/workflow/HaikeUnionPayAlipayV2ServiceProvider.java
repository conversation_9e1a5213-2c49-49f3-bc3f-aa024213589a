package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/***
 * @ClassName: HaikeUnionPayAlipayV2ServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/11 10:26 AM
 */
@ServiceProvicerPriority(priority = 3)
public class HaikeUnionPayAlipayV2ServiceProvider extends UnionPayAlipayV2ServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(HaikeUnionPayAlipayV2ServiceProvider.class);
    public static final String NAME = "provider.haike.unionpay.alipay.v2";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.HAIKE_UNION_PAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_HAIKE_UNION_PAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        long effectiveTotal = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        //退款和 交易完成后的撤单直接走海科通道
        if (Transaction.TYPE_REFUND == type || (Transaction.TYPE_CANCEL == type && effectiveTotal > 0)) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if((Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE)) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = new RequestV2Builder();

        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(config,TransactionParam.HAIKE_UNION_PAY_ALIPAY_APP_ID));
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayV2NewClient.SIGN_TYPE_SM2);
        builder.set(ProtocolV2Fields.CERT_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_CERT_ID));
        String sysProviderId = BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_SYS_PROVIDER_ID);

        String alipaySubMchId = BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_ALIPAY_SUB_MCH_ID);
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(
                BusinessV2Fields.SUB_MERCHANT_ID, alipaySubMchId,
                BusinessV2Fields.MERCHANT_NAME, getSubMerchantName(config, transaction)
        ));

        Map extraParam = CollectionUtil.hashMap(
                BusinessV2Fields.SECONDARY_MERCHANT_ID, alipaySubMchId
        );
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, extraParam);

        if(!StringUtil.empty(sysProviderId)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, sysProviderId
            ));
            builder.bizSet(BusinessV2Fields.ORG_PID, sysProviderId);
            extraParam.put(BusinessV2Fields.REQUEST_ORG_ID, sysProviderId);

            if (BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                builder.bizSet(BusinessV2Fields.REQUEST_ORG_PID, sysProviderId);
            }
        }

        setTerminalInfo(context, MapUtils.getMap(transaction, Transaction.CONFIG_SNAPSHOT), builder);

        return builder;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String gatewayUrl, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        return client.call(gatewayUrl, AlipayV2NewClient.SIGN_TYPE_HAIKE, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_ACCESS_KEY), request);
    }

}
