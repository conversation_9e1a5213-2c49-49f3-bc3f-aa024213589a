package com.wosai.upay.workflow;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.fake.FakeClient;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.util.Digest;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.net.GatewayUrl;
import com.wosai.net.UpstreamPeerContextHolder;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Semaphore;

import static com.wosai.upay.util.UpayUtil.isFormalByConfig;
public abstract class AbstractServiceProvider implements MpayServiceProvider, InitializingBean {
	public static final String KEY_CURRENCY = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.CURRENCY);
	public static final String KEY_PAYER_UID = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.PAYER_UID);
	protected static final String DEFAULT_NOTIFY_PATH = "upay/v2/notify/";
    protected static final String DEFAULT_COMMON_NOTIFY_PATH = "upay/v2/notify/trade/";
	protected static final String URL_INTERVAL = "/";
	protected static final int DEFAULT_TIME_EXPIRE_MINUTE = 4; // 订单默认过期时间设定为4分钟
	protected static final int B2C_TIME_EXPIRE_MINUTE = 1; // b2c过期时间设定为1分钟
    protected static final int DEFAULT_NOTIFY_URL_LIMIT = 255;
    private static final String UPAY_ORDER_NUMBER_INTERVAL= "&";
    private static final String NOTIFY_URL_MD5_KEY = "011f2909-22a7-4623-923d-e3cee76fd411";
    private static final int notifyDefaultProductFlag = 0;
    private static final String INVALID_POI_PREFIX = "0"; //无效的poi信息前缀，以0开头

    public static final String WAP_PAY_REQUEST_ALIPAY_UNION_PAY_QRCODE_REDIRECT_URL = "redirectUrl";
    public static final String WAP_PAY_REQUEST_ALIPAY_TRADE_NO = "tradeNO";


    public static final Logger logger = LoggerFactory.getLogger(AbstractServiceProvider.class);

    private static final Map<String,Integer> notifyProductFlagMap = new LinkedHashMap(){{
        //根据put顺序来确定转换的优先级
        put(ProductFlagEnum.DBB.getCode(), 3);
        put(ProductFlagEnum.JJZ_WM.getCode(), 4);
        put(ProductFlagEnum.JJZ_ZQ.getCode(), 5);
        put(ProductFlagEnum.UFOOD.getCode(), 2);
        put(ProductFlagEnum.MARKET_PROGRAM_DISCOUNT.getCode(), 1);
        put(ProductFlagEnum.DEPOSIT_TYPE_SQB.getCode(), 6);
        put(ProductFlagEnum.DEPOSIT_TYPE_PAY_SOURCE.getCode(), 7);
    }};


    //TODO 如果新增了provider, 请在下面添加对应的配置
    private static final Map<String,String> providerNotifyFlags = CollectionUtil.hashMap(
            AlipayIntlServiceProvider.NAME, null,
            AlipayOverseasServiceProvider.NAME, NOTIFY_ALIPAY,
            AlipayV1ServiceProvider.NAME, NOTIFY_ALIPAY,
            AlipayWapServiceProvider.NAME, NOTIFY_ALIPAY_WAP,
            BestpayServiceProvider.NAME, NOTIFY_BESTPAY,
            BestpayV2ServiceProvider.NAME, NOTIFY_BESTPAY_V2,
            CIBBankServiceProvider.NAME, NOTIFY_SWIFTPASS,
            CITICBankServiceProvider.NAME, NOTIFY_SWIFTPASS,
            DefaultSwiftPassServiceProvider.NAME, NOTIFY_SWIFTPASS,
            CMCCServiceProvider.NAME, NOTIFY_CMCC,
            ChinaumsServiceProvider.NAME, null,
            ChinaumsV1ServiceProvider.NAME, NOTIFY_CHINAUMS,
            DirectAlipayV2ServiceProvider.NAME, NOTIFY_ALIPAY,
            DirectAlipayV2WapServiceProvider.NAME, NOTIFY_ALIPAY_WAP,
            DirectAlipayV2PreDepositServiceProvider.NAME, NOTIFY_ALIPAY,
            DirectUnionPayAlipayV2ServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY,
            DirectUnionPayAlipayV2WapServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY_WAP,
            DirectUnionPayWeixinServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY,
            DirectUnionPayWeixinWapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY_WAP,
            LklUnionPayAlipayV2ServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY,
            LklUnionPayAlipayV2WapServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY_WAP,
            LklUnionPayWeixinServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY,
            LklUnionPayWeixinWapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY_WAP,
            HaikeUnionPayAlipayV2ServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY,
            HaikeUnionPayAlipayV2WapServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY_WAP,
            HaikeUnionWeixinServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY,
            HaikeUnionPayWeixinWapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY_WAP,
            HaikeUnionPayUnionQRCodeServiceProvider.NAME, NOTIFY_UNIONPAY_HAIKE,
            DirectWeixinServiceProvider.NAME, NOTIFY_WEIXIN,
            DirectWeixinWapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_WAP,
            DirectWeixinV2CycleServiceProvider.NAME, NOTIFY_WEIXIN,
            DirectWeixinV2CycleMerchantServiceProvider.NAME, NOTIFY_WEIXIN,
            GiftCardProvider.NAME, null,
            LakalaServiceProvider.NAME, null,
            NuccAlipayV2ServiceProvider.NAME, NOTIFY_ALIPAY_NUCC,
            NuccAlipayV2WapServiceProvider.NAME, NOTIFY_ALIPAY_NUCC_WAP,
            NuccBestPayServiceProvider.NAME, NOTIFY_BESTPAY_NUCC,
            NuccWeixinServiceProvider.NAME, NOTIFY_WEIXIN_NUCC,
            NuccWeixinWapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_NUCC_WAP,
            UnionPayAlipayV2ServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY,
            UnionPayAlipayV2WapServiceProvider.NAME, NOTIFY_ALIPAY_UNIONPAY_WAP,
            UnionPayOnlineServiceProvider.NAME, NOTIFY_UNIONPAY_ONLINE,
            UnionPayOpenServiceProvider.NAME, NOTIFY_UNIONPAY_OPEN,
            UnionPayWeixinServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY,
            UnionPayWeixinWapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_UNIONPAY_WAP,
            WeixinHKServiceProvider.NAME, null,
            TLUnionPayAlipayV2ServiceProvider.NAME,NOTIFY_ALIPAY_TL,
            TLUnionPayAlipayV2WapServiceProvider.NAME,NOTIFY_ALIPAY_WAP_TL,
            TLUnionPayWeixinServiceProvider.NAME,NOTIFY_WEIXIN_TL,
            TLUnionPayWeixinWapOrMiniServiceProvider.NAME,NOTIFY_WEIXIN_WAP_TL,
            TLUnionPayUnionQRCodeServiceProvider.NAME,NOTIFY_UNIONPAY_TL,
            PSBCBankAlipayServiceProvider.NAME, NOTIFY_PSBCBANK,
            PSBCBankWeixinServiceProvider.NAME, NOTIFY_PSBCBANK,
            PSBCBankUnionpayServiceProvider.NAME, NOTIFY_PSBCBANK,
            CmbServiceProvider.NAME, NOTIFY_CMB,
            DirectWeixinV3PreDepositServiceProvider.NAME,NOTIFY_WEIXINV3,
            DirectWeixinV3PartnerPreDepositServiceProvider.NAME,NOTIFY_WEIXINV3,
            DirectWeixinV3PreDepositPalmServiceProvider.NAME,NOTIFY_WEIXINV3,
            CGBBankServiceProvider.NAME, NOTIFY_CGBBANK,
            HXBankServiceProvider.NAME, NOTIFY_HXBANK,
            ICBCBankServiceProvider.NAME, NOTIFY_ICBCBANK,
            PrepaidCardProvider.NAME, NOTIFY_PREPAID,
            LakalaOpenV3BankCardServiceProvider.NAME,NOTIFY_LAKALA_OPEN_V3,
            WeixinHKV3ServiceProvider.NAME, NOTIFY_WEIXIN_HKV3,
            WeixinHKV3WapOrMiniServiceProvider.NAME, NOTIFY_WEIXIN_HKV3,
            TLSybServiceProvider.NAME, NOTIFY_TL_SYB,
            FuyouServiceProvider.NAME, NOTIFY_FOUYOU,
            BOCOMServiceProvider.NAME, NOTIFY_BOCOM,
            LakalaOpenV3ServiceProvider.NAME, NOTIFY_LAKALA_OPEN_V3,
            EntPayServiceProvider.NAME, NOTIFY_ENTPAY,
            SPDBServiceProvider.NAME, NOTIFY_SPDB,
            CMBCBankServiceProvider.NAME, NOTIFY_CMBCBANK,
            JyCardProvider.NAME, NOTIFY_JYCARD,
            JSBBankProvider.NAME, NOTIFY_JSB,
            LZCCBServiceProvider.NAME, NOTIFY_LZCCB,
            PartnerWeixinV3WapOrMiniServiceProvider.NAME, NOTIFY_WEIXINV3,
            TlS2PServiceProvider.NAME, NOTIFY_TLS2P,
            YopServiceProvider.NAME, NOTIFY_YOP,
            PkxAirportMiniServiceProvider.NAME, NOTIFY_PKX_AIRPORT,
            XZXServiceProvider.NAME, NOTIFY_XZX,
            HopeEduServiceProvider.NAME, NOTIFY_HOPE_EDU,
            GuotongServiceProvider.NAME, NOTIFY_GUOTONG,
            MacauPassServiceProvider.NAME, NOTIFY_MACAU_PASS,
            AirwallexServiceProvider.NAME, NOTIFY_AIRWALLEX,
            ZTKXProvider.NAME, NOTIFY_ZTKX,
            PSBCServiceProvider.NAME, NOTIFY_PSBC,
            WecardServiceProvider.NAME, NOTIFY_WECARD
    );

    @Autowired
    protected WorkflowManager workflowManager;
    @Autowired
    protected ExternalServiceFacade serviceFacade;
    @Autowired
    protected DataRepository dataRepository;
    @Autowired
    protected GatewaySupportService gatewaySupportService;
    @Resource
    protected FakeClient fakeClient;

    protected SafeSimpleDateFormat dateFormat;

    public static  ObjectMapper objectMapper = new ObjectMapper();

    protected Set<String> extendedFilterFields;

    protected Semaphore concurrencySemaphore = new Semaphore(5); // b2c交易延迟走查询最大并发数

    @Override
    public void afterPropertiesSet() throws Exception {
        workflowManager.addServiceProvider(this);
    }

    /**
     * 解析日期字符串为timestamp
     * @param timeString
     * @return
     */
    public Long parseTimeString(String timeString){
        try {
            return dateFormat.parse(timeString).getTime();
        } catch (Exception e) {
            return null;
        }
    }

    public String formatTimeString(long time) {
        return dateFormat.format(new Date(time));
    }

    /**
     * 初始化一些transaction的值，如果是blob的字段，如果值为空，则初始化为map
     * 方便程序后续的处理
     * @param transaction
     */
    public  void initTransactionSomeValue(Map<String, Object> transaction) {
        if (transaction.get(Transaction.CONFIG_SNAPSHOT) == null) {
            transaction.put(Transaction.CONFIG_SNAPSHOT, new HashMap<String, Object>());
        }
        if (transaction.get(Transaction.EXTRA_PARAMS) == null) {
            transaction.put(Transaction.EXTRA_PARAMS, new HashMap<String, Object>());
        }
        if (transaction.get(Transaction.EXTRA_OUT_FIELDS) == null) {
            transaction.put(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        }
        if (transaction.get(Transaction.EXTENDED_PARAMS) == null) {
            transaction.put(Transaction.EXTENDED_PARAMS, new HashMap<String, Object>());
        }
    }

    /**
     * 获取交易货币类型
     * @param transaction
     * @return
     */
    public static String getTradeCurrency(Map<String, Object> transaction){
        String currency = (String) BeanUtil.getNestedProperty(transaction, KEY_CURRENCY);
        if(StringUtil.empty(currency)){
            currency = TransactionParam.UPAY_DEFAULT_CURRENCY_CNY;
        }
        return currency;
    }

    @Override
    public String depositCancel(TransactionContext context) {
        return null;
    }

    @Override
    public String depositConsume(TransactionContext context) {
        return null;
    }

    @Override
    public String depositQuery(TransactionContext context) {
        return null;
    }

    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        return null;
    }

    /**
     * 获取回调地址
     * @param notifyHost
     * @param payUrl
     * @param context
     * @return
     */
    public String getNotifyUrl(String notifyHost, String payUrl, TransactionContext context){
        return getNotifyUrl(notifyHost, payUrl, context, null);
    }

    /**
     * 获取回调地址
     * @param notifyHost
     * @param payUrl
     * @param context
     * @return
     */
    public String getNotifyUrl(String notifyHost, String payUrl, TransactionContext context, String suffix){
        String providerNotify = StringUtils.join(DEFAULT_NOTIFY_PATH, providerNotifyFlags.get(getName()));
        if(suffix != null) {
            providerNotify = StringUtils.join(providerNotify, URL_INTERVAL, suffix);
        }
        String result =  connectNotifyUrl(ApolloConfigurationCenterUtil.getNotifyHost(notifyHost, payUrl), providerNotify, context);
        return result;
    }

    /**
     * 获取固定回调地址, 最后不能以 / 结尾！！！！
     * @param notifyHost
     * @return
     */
    public String getCommonNotifyUrl(String notifyHost){
        String notifyFlag = providerNotifyFlags.get(getName());
        String suffix = StringUtils.join(DEFAULT_COMMON_NOTIFY_PATH, notifyFlag);
        if (!notifyHost.endsWith(URL_INTERVAL)) {
            notifyHost = StringUtils.join(notifyHost, URL_INTERVAL);
        }
        notifyHost = StringUtils.join(notifyHost, suffix);
        return notifyHost;
    }

    /**
     * 获取回调地址
     * @param notifyHost
     * @param context
     * @return
     */
    public String getNotifyUrl(String notifyHost, TransactionContext context){
        String notifyFlag = providerNotifyFlags.get(getName());
        return connectNotifyUrl(notifyHost, StringUtils.join(DEFAULT_NOTIFY_PATH, notifyFlag), context);
    }

    protected String connectNotifyUrl(String notifyHost,String suffix, TransactionContext context) {
        if (!notifyHost.endsWith(URL_INTERVAL)) {
            notifyHost = StringUtils.join(notifyHost, URL_INTERVAL);
        }
        notifyHost = StringUtils.join(notifyHost, suffix, URL_INTERVAL);
        if (context.getTerminalOrStoreSn() != null) {
            try {
                String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
                if (!StringUtils.empty(upayOrderNumber)) {
                    String url = notifyHost + upayOrderNumber + URL_INTERVAL;
                    String notifyUrl;
                    Map<String, Object> transaction = context.getTransaction();
                    String tsn = BeanUtil.getPropString(transaction, Transaction.TSN);
                    String payway = BeanUtil.getPropString(transaction, Transaction.PAYWAY);
                    String terminalId = BeanUtil.getPropString(transaction, Transaction.TERMINAL_ID);
                    String subpayway = BeanUtil.getPropString(transaction, Transaction.SUB_PAYWAY);
                    String ctime = BeanUtil.getPropString(transaction, DaoConstants.CTIME);
                    String amount = BeanUtil.getPropString(transaction, Transaction.ORIGINAL_AMOUNT);
                    //tcp_modified 为true 且 原始订单金额不等于上送订单金额 设置为有喔噻优惠
                    String tcpModified = BeanUtil.getPropBoolean(context.getOrder(), Order.TCP_MODIFIED, false) && !amount.equals(BeanUtil.getPropString(transaction, Transaction.EFFECTIVE_AMOUNT)) ? "1" : "0";
                    String productFlag = BeanUtil.getPropString(context.getTransaction(), Transaction.PRODUCT_FLAG, "");
                    StringBuilder extraInfoSb = new StringBuilder();
                    @SuppressWarnings("unchecked")
                    Map<String, Object> configSnapshot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
                    extraInfoSb.append(tsn)
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(payway)
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_SN))
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(terminalId)
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(subpayway)
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(ctime)
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(amount)
                            .append(UPAY_ORDER_NUMBER_INTERVAL).append(tcpModified);

                    int prodFlagNum = notifyDefaultProductFlag;
                    String sqbVoice = BeanUtil.getPropString(transaction, Transaction.KEY_SQB_VOICE);
                    if(!StringUtil.empty(sqbVoice)){
                        //如果商户有上送语音播报标识，那么用商户上送的
                        prodFlagNum = Integer.parseInt(sqbVoice);
                    }else{
                        for(String code: notifyProductFlagMap.keySet()){
                            if(productFlag.contains(code)){
                                prodFlagNum = notifyProductFlagMap.get(code);
                                break;
                            }
                        }
                    }
                    extraInfoSb.append(UPAY_ORDER_NUMBER_INTERVAL).append(prodFlagNum);

                    //MD5只保留五位
                    String authorization = Digest.md5((extraInfoSb.toString() + getPrivateKeyContent(NOTIFY_URL_MD5_KEY)).getBytes()).substring(0,5);
                    String appendInfo = authorization + UPAY_ORDER_NUMBER_INTERVAL + extraInfoSb.toString();
                    notifyUrl = url + URLEncoder.encode(appendInfo, UpayConstant.CHARSET_UTF8);
                    //支付宝会再次urlEncode 此时有可能长度超出 导致下单失败
                    if (URLEncoder.encode(notifyUrl,UpayConstant.CHARSET_UTF8).length() <= getNotifyUrlLimit()) {
                        return notifyUrl;
                    } else {
                        return url;
                    }
                }
                return notifyHost;
            } catch (Exception e) {
                return notifyHost;
            }
        } else {
            return notifyHost;
        }
    }

    protected int getNotifyUrlLimit() {
        return DEFAULT_NOTIFY_URL_LIMIT;
    }

    /**
     * 通过调用支付通道时发生的异常，以及返回的结果来判断通道是否正常
     * @param e
     * @param result
     * @return
     */
    public boolean providerIsOk(Exception e, Map<String,Object> result){
        if(e != null){
            //出现异常时， 只有网络异常以及返回的结果未知时才当做通道有问题
            if(e instanceof MpayApiUnknownResponse || e instanceof MpayApiNetworkError){
                return false;
            }else{
                return true;
            }
        }
        if(result == null || result.isEmpty()){
            return false;
        }
        return true;
    }

    public String getPrivateKeyContent(String rsaKeyId){
        return serviceFacade.getRsaKeyDataById(rsaKeyId);
    }

    /**
     *
     * 查询支付/预授权消费流水，包含历史订单
     *
     * @param transaction
     * @return
     */
    protected Map<String,Object> getPayOrConsumerTransaction(Map<String,Object> transaction, long orderCtime) {
        String merchantId = BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        //收钱吧预授权
        if (Objects.equals(BeanUtil.getPropString(transaction, Transaction.KEY_DEPOSIT_TYPE), TransactionParam.DEPOSIT_SQB)) {
            Map<String,Object> payTransaction;
            String depositCaller = BeanUtil.getPropString(transaction, Transaction.DEPOSIT_CALLER);
            //预授权撤销 - 走退款
            if (Objects.equals(depositCaller, Transaction.DEPOSIT_CALLER_CANCEL)) {
                payTransaction = dataRepository.getFreezeTransactionByOrderSn(merchantId, orderSn);
                //流水被归档时查历史交易
                if (Objects.isNull(payTransaction)) {
                    Map<String, Object> order = gatewaySupportService.getOrderBySn(merchantId, orderSn, null, DataPartitionConst.RECENT_6M);
                    payTransaction = gatewaySupportService.getTransactionByClientTsn(
                            merchantId, orderSn,
                            BeanUtil.getPropString(order, com.wosai.profit.sharing.model.upay.Order.CLIENT_SN),
                            MapUtil.getLongValue(order, DaoConstants.CTIME)
                    );
                }
            }
            //预授权完成后退款
            else if (Objects.equals(depositCaller, Transaction.DEPOSIT_CALLER_CONSUME_REFUND)) {
                payTransaction = dataRepository.getConsumeTransactionByOrderSn(merchantId, orderSn);
                //流水被归档时查历史交易
                if (Objects.isNull(payTransaction)) {
                    payTransaction = gatewaySupportService.getPayOrConsumerTransaction(merchantId, orderSn, orderCtime);
                }
            } else {
                throw new UnsupportedOperationException("未知的收钱吧预授权退款调用类型");
            }
            return payTransaction;
        }
        Map<String,Object> payTransaction = null;
        if(!BeanUtil.getPropBoolean(transaction, Transaction.KEY_IS_DEPOSIT)) {
            payTransaction = dataRepository.getPayTransactionByOrderSn(merchantId, orderSn);
        } else {
            payTransaction = dataRepository.getConsumeTransactionByOrderSn(merchantId, orderSn);
        }
        if(null == payTransaction) {
            payTransaction = gatewaySupportService.getPayOrConsumerTransaction(merchantId, orderSn, orderCtime);
        }
        return payTransaction;
    }

    public void clearBizErrorCode(Map<String,Object> transaction){
        transaction.put(Transaction.BIZ_ERROR_CODE, null);
    }

    protected Map<String, Object> call(String url, Map<String,Object> config, Map<String, Object> request) throws Exception {return null;}

    /**
     *
     * @param gatewayUrl
     * @param times
     * @param opFlag
     * @param providerFlag weixin or alipay ....
     * @return
     * @throws Exception
     */
    public Map<String,Object> retryIfNetworkException(GatewayUrl gatewayUrl, Map<String,Object> keyConfig, Map<String, Object> request, String requestFormat, String contentType, Logger logger, int times, String opFlag, String providerFlag) throws Exception{
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            Map<String,Object> result = null;
            exception = null;
            try {
                //是否需要跳过加解密，走fakeClient
                if (gatewayUrl.isSkipEnAndDecrypt()) {
                    result = fakeClient.call(gatewayUrl.getUrl(), request, contentType, requestFormat);
                } else {
                    result = call(gatewayUrl.getUrl(), keyConfig, request);
                }
                return result;
            } catch (Exception e) {
                exception = e;
                if(e instanceof MpayApiNetworkError){
                    logger.warn("encountered ioex in {} {}", providerFlag, opFlag, e);
                }else{
                    throw e;
                }
            }finally {
                //disable后不再统计网络调用情况, 达到upstream只根据配置的权重来进行负载均衡的效果, 没有disable的话，才进行网络调用情况的统计
                if(!ApolloConfigurationCenterUtil.isProviderGatewayAutoDisable()){
                    UpstreamPeerContextHolder.finish(providerIsOk(exception, result));
                }
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    /**
     *
     * @param clientCall
     * @param times
     * @param opFlag
     * @param providerFlag weixin or alipay ....
     * @return
     * @throws Exception
     */
    public Map<String,Object> retryIfNetworkException(Callable<Map<String,Object>> clientCall, Logger logger, int times, String opFlag, String providerFlag) throws Exception{
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            Map<String,Object> result = null;
            exception = null;
            try {
                result = clientCall.call();
                return result;
            } catch (Exception e) {
                exception = e;
                if(e instanceof MpayApiNetworkError){
                    logger.warn("encountered ioex in {} {}", providerFlag, opFlag, e);
                }else{
                    throw e;
                }
            }finally {
                //disable后不再统计网络调用情况, 达到upstream只根据配置的权重来进行负载均衡的效果, 没有disable的话，才进行网络调用情况的统计
                if(!ApolloConfigurationCenterUtil.isProviderGatewayAutoDisable()){
                    UpstreamPeerContextHolder.finish(providerIsOk(exception, result));
                }
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    /**
     * 根据比例来计算退款时支付通道的退款明细
     * @param transaction
     * @param payOrConsumerTransaction 原始的支付流水
     */
    public static void resolveRefundFundByPercent(Map<String,Object> transaction, Map<String,Object> payOrConsumerTransaction) {
        //上送通道总额
        long effectiveTotal = BeanUtil.getPropLong(payOrConsumerTransaction, Transaction.EFFECTIVE_AMOUNT, 0);
        //本次退款总额
        long refundAmountTotal = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        //退款比例
        double refundPercent = refundAmountTotal * 1.0 / effectiveTotal;
        if(payOrConsumerTransaction.get(Transaction.PAID_AMOUNT) != null){
            long payPaidAmount = BeanUtil.getPropLong(payOrConsumerTransaction, Transaction.PAID_AMOUNT);
            long refundPaidAmount = Math.round(payPaidAmount * refundPercent);
            transaction.put(Transaction.PAID_AMOUNT, refundPaidAmount);
        }
        if(payOrConsumerTransaction.get(Transaction.RECEIVED_AMOUNT) != null){
            long payReceivedAmount = BeanUtil.getPropLong(payOrConsumerTransaction, Transaction.RECEIVED_AMOUNT);
            long refundReceivedAmount = Math.round(payReceivedAmount * refundPercent);
            transaction.put(Transaction.RECEIVED_AMOUNT, refundReceivedAmount);
        }

        List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payOrConsumerTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if (CollectionUtils.isEmpty(payments)) {
            return;
        }
        List<Map<String,Object>> refundPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        if (refundPayments == null) {
            refundPayments = new ArrayList<>();
        }
        long sumPaymentAmount = payments.stream().mapToLong(payment->BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT)).sum();
        long remainRefundPaymentAmount = Math.round(sumPaymentAmount * refundPercent);
        for (int i = 0; i < payments.size(); i++) {
            Map<String,Object> payment = payments.get(i);
            long amount = BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
            boolean isLastPayment = (i == payments.size() - 1);
            long refundAmount = isLastPayment ? remainRefundPaymentAmount : Math.round(amount * refundPercent);
            Map<String, Object> refundPayment = new HashMap<>(payment);
            refundPayment.put(Transaction.PAYMENT_AMOUNT, refundAmount);
            refundPayments.add(refundPayment);
            remainRefundPaymentAmount = remainRefundPaymentAmount - refundAmount;
        }

        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
    }

    /**
     * 获取支付通道返回的真实订单号，银联通道的需要特殊处理
     * @param provider
     * @param result
     * @return
     */
    public static String getRealTradeNo(Integer provider, Map<String,Object> result){
        String tradeNo = BeanUtil.getPropString(result, BusinessV2Fields.TRADE_NO);
        return getAlipayRealTradeNo(provider, tradeNo);
    }

    /**
     * 获取支付通道返回的真实订单号，银联通道的需要特殊处理
     * @param provider
     * @param tradeNo
     * @return
     */
    public static String getAlipayRealTradeNo(Integer provider, String tradeNo){
        if(provider == null || tradeNo == null || tradeNo.isEmpty()){
            return tradeNo;
        }
        if (provider == TradeConfigService.PROVIDER_UNIONPAY || provider == TradeConfigService.PROVIDER_DIRECT_UNIONPAY || provider == TradeConfigService.PROVIDER_UNIONPAY_TL
                || provider == TradeConfigService.PROVIDER_PSBCBANK || provider == TradeConfigService.PROVIDER_HXBANK || provider == Order.PROVIDER_LAKALA_UNION_PAY
                || provider == TradeConfigService.PROVIDER_CMB || provider == TradeConfigService.PROVIDER_CCB || provider == Order.PROVIDER_HAIKE_UNION_PAY) {
            //银联支付宝，报文返回的tradeNo字段，前2位是银联的的“分套信息”，需要去掉，否则门店码交易会报  ALI38110,订单不存在。
            tradeNo = tradeNo.substring(2);
        }
        return tradeNo;
    }

    protected boolean isTodayTransaction(Map<String, Object> transaction) {
        long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
        long currentTimestamp = System.currentTimeMillis();
        long dayOfStart = DateTimeUtil.getOneDayStart(currentTimestamp);
        long dayOfEnd = DateTimeUtil.getOneDayEnd(currentTimestamp);
        return ctime >= dayOfStart && ctime <= dayOfEnd;
    }

    protected TerminalInfo genTerminalInfo(Map<String, Object> transaction) {
        Map config = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        TerminalInfo terminalInfo = new TerminalInfo(MapUtil.getString(config, TransactionParam.TRADE_EXT_TERM_ID), MapUtil.getMap(config, TransactionParam.TRADE_EXT_TERM_INFO));
        String ip = MapUtil.getString(extraParams, Transaction.SQB_IP);
        String clientIp = MapUtil.getString(extraParams, Transaction.CLIENT_IP);
        //ip优先取用户IP，再取终端上报ip, 兜底localhost ip
        terminalInfo.setIp(StringUtil.empty(ip) ? (StringUtil.empty(clientIp) ? UpayUtil.getLocalHostIp() : clientIp) : ip);

        String longitude = null;
        String latitude = null;

        //优先取终端定位
        Map poi = MapUtil.getMap(extraParams, Transaction.POI);
        if (MapUtil.isNotEmpty(poi)) {
            longitude = MapUtil.getString(poi, Transaction.LONGITUDE);
            latitude = MapUtil.getString(poi, Transaction.LATITUDE);
        }

        boolean needCheckPoiAgain = false;
        boolean isDefaultPoi = false;
        //上送poi无效, 兜底取 门店poi > 商户poi ; config中的经纬度已经是优先取门店，其次是商户
        if (isPoiInfoInvalid(longitude) || isPoiInfoInvalid(latitude)) {
            longitude = MapUtil.getString(config, TransactionParam.LONGITUDE);
            latitude = MapUtil.getString(config, TransactionParam.LATITUDE);
            //兜底的门店poi也可能无效，需要再次检查
            needCheckPoiAgain = true;
            isDefaultPoi = true;
        }

        setPoiInfo(terminalInfo, longitude, latitude, needCheckPoiAgain, isDefaultPoi);

        updateTerminalInfo(terminalInfo, transaction);
        return terminalInfo;
    }

    /**
     * 检查poi信息是否正确，如果正确则设置
     *
     * @param terminalInfo
     * @param longitude
     * @param latitude
     * @param needCheckPoi 是否需要检查poi
     * @return
     */
    private boolean setPoiInfo(TerminalInfo terminalInfo, String longitude, String latitude, boolean needCheckPoi) {
        return setPoiInfo(terminalInfo, longitude, latitude, needCheckPoi, false);
    }

    /**
     * 检查poi信息是否正确，如果正确则设置
     *
     * @param terminalInfo
     * @param longitude
     * @param latitude
     * @param needCheckPoi 是否需要检查poi
     * @return
     */
    private boolean setPoiInfo(TerminalInfo terminalInfo, String longitude, String latitude, boolean needCheckPoi, boolean isDefaultPoi) {
        if (needCheckPoi && (isPoiInfoInvalid(longitude) || isPoiInfoInvalid(latitude))) {
            logger.info("POI invalid! longitude={}, latitude={}", longitude, latitude);
            return false;

        }
        if (!org.apache.commons.lang3.StringUtils.startsWithAny(longitude, "+", "-")) {
            longitude = "+" + longitude;
        }
        if (!org.apache.commons.lang3.StringUtils.startsWithAny(latitude, "+", "-")) {
            latitude = "+" + latitude;
        }
        terminalInfo.setLongitude(longitude);
        terminalInfo.setLatitude(latitude);
        terminalInfo.setDefaultPoi(isDefaultPoi);
        return true;
    }


    /**
     * 判断经纬度数据是否无效
     * 截止2024.3.13日，线上poi异常数据分为以下几类
     * 详情见：https://confluence.wosai-inc.com/pages/viewpage.action?pageId=864060072
     * 1. poi数据为null
     * 2. poi数据以0开头：0或0.0或0.000000
     * 3. poi数据包含字母：4.9E-324 (一般是未获取到定位导致)
     * isPoiInfoInvalid目前仅针对以上情况做判断
     *
     * @param longitudeOrLatitude
     * @return
     */
    @VisibleForTesting
    protected boolean isPoiInfoInvalid(String longitudeOrLatitude) {
        return org.apache.commons.lang3.StringUtils.isEmpty(longitudeOrLatitude)
                || longitudeOrLatitude.startsWith(INVALID_POI_PREFIX)
                || CharacterUtil.containsAlphabetic(longitudeOrLatitude);
    }


    @SneakyThrows
    private void updateTerminalInfo(TerminalInfo terminalInfo, Map<String,Object> transaction){
        //开关没有打开不做处理
        if(!ApolloConfigurationCenterUtil.isSendMerchantPoi()){
            return;
        }
        // 不是b2c的交易不做处理
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(subPayway != Order.SUB_PAYWAY_BARCODE){
            return;
        }
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        //  不是拉卡拉的交易不做处理
        int clearanceProvider = BeanUtil.getPropInt(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        if(clearanceProvider != TransactionParam.CLEARANCE_PROVIDER_LKL || isFormalByConfig(configSnapshot)){
            return;
        }
        // 商户层级没有配置，则不处理
        Map<String,Object> extra = MapUtil.getMap(configSnapshot, Store.EXTRA);
        String offsetString = MapUtil.getString(extra, "offsetInfo");
        if(StringUtils.isEmpty(offsetString)){
            return;
        }
        Map poi = JsonUtil.jsonStringToObject(offsetString, Map.class);
        String longitude = MapUtil.getString(poi, Transaction.LONGITUDE);
        String latitude = MapUtil.getString(poi, Transaction.LATITUDE);
        if (!setPoiInfo(terminalInfo, longitude, latitude, true)) {
            return;
        }
        terminalInfo.setOffset(true);
        BeanUtil.setNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.IS_OFFSET, true);
    }

    public boolean overFilterField(String field) {
        return extendedFilterFields != null && !extendedFilterFields.isEmpty() && extendedFilterFields.contains(field);
    }

    protected Map<String, Object> getTradeParams(Map<String, Object> transaction, String tradeParamsKey){
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        return (Map<String, Object>) configSnapshot.get(tradeParamsKey);
    }

    protected void setTransactionContextErrorInfo(TransactionContext context, String key, Exception ex) {
        setTransactionContextErrorInfo(context.getTransaction(), key, ex);
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> transaction, String key, Exception ex) {
        Map<String, Object> providerErrorInfo = MapUtil.getMap(transaction, Transaction.PROVIDER_ERROR_INFO);
        if (providerErrorInfo == null) {
            providerErrorInfo = new HashMap<String, Object>();
            transaction.put(Transaction.PROVIDER_ERROR_INFO, providerErrorInfo);
        }
        providerErrorInfo.put(key, CollectionUtil.hashMap("exception", StringUtils.join(ex.getClass(), ":", ex.getMessage())));
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> transaction, String op, Map<String, Object> recordResult, boolean isSuccess, String providerCode, String providerMessage) {
        // 设置通道返回信息
        com.wosai.upay.util.MapUtil.removeNullValues(recordResult);
        String terminalCategory = MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        boolean isMergeMessage = StringUtil.empty(terminalCategory);
        Map<String, Object> providerErrorInfo = MapUtil.getMap(transaction, Transaction.PROVIDER_ERROR_INFO);
        if (providerErrorInfo == null) {
            providerErrorInfo = new HashMap<String, Object>();
            transaction.put(Transaction.PROVIDER_ERROR_INFO, providerErrorInfo);
        }
        providerErrorInfo.put(op, recordResult);

        // 设置转换后的收钱吧异常信息
        Map<String, Object> bizErrorCode = MapUtil.getMap(transaction, Transaction.BIZ_ERROR_CODE);
        if (isSuccess) {
            // 支付通道返回成功，清空原先设置的UpayBizError
            if (bizErrorCode != null) {
                bizErrorCode.remove(op);
            }
        } else {
            if (bizErrorCode == null) {
                bizErrorCode = new HashMap<String, Object>();
                transaction.put(Transaction.BIZ_ERROR_CODE, bizErrorCode);
            }

            UpayBizError bizError = UpayBizError.getBizErrorByField(op, MapUtil.getIntValue(transaction, Transaction.PAYWAY), providerCode, providerMessage);
            if (bizError != null && !UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(bizError.getStandardName())) {
                bizErrorCode.put(op, bizError);
            } else {
                bizErrorCode.put(op, UpayBizError.unexpectedProviderError(StringUtil.empty(providerMessage) ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : providerMessage, isMergeMessage));
            }
        }
    }

    /**
     * 收钱吧产品标识是否匹配， 用于商户有相同参数，但是使用了通道的多种产品 优先级 上送 -> 交易参数
     * @param transaction
     * @param tradeParams
     * @param productCode
     * @param matchNull 如果没有上送，交易参数也没有配置，是否当做匹配
     * @return
     */
    public boolean matchProductCode(Map<String, Object> transaction, Map<String, Object> tradeParams, String productCode, boolean matchNull){
        String code = BeanUtil.getPropString(transaction, Transaction.KEY_SQB_PRODUCT_CODE);
        if(code == null){
            code = BeanUtil.getPropString(tradeParams, TransactionParam.PRODUCT_CODE);
        }
        return (code == null && matchNull) || (code != null && Objects.equals(code, productCode));
    }

    /**
     * 收钱吧产品标识是否匹配， 用于商户有相同参数，但是使用了通道的多种产品 优先级 上送 -> 交易参数
     * @param transaction
     * @param tradeParams
     * @param productCode
     * @return
     */
    public boolean matchProductCode(Map<String, Object> transaction, Map<String, Object> tradeParams, String productCode){
        return matchProductCode(transaction, tradeParams, productCode, false);
    }


    /**
     * 根据支付通道返回的微信 promotion_details 构建 payments
     * @param promotions
     * @param payments
     * @return
     */
    public Pair<Long, Long> buildWeixinPaymentsByPromotions(List<Map<String, Object>> promotions, List<Map<String, Object>> payments) {
        long couponSum = 0;
        long discountChanelMchTotal = 0; // 商户免充值不结算给商户
        if (CollectionUtils.isNotEmpty(promotions)) {
            for (Map<String,Object> promotion : promotions) {
                if (promotion.isEmpty()) {
                    continue;
                }
                String type = MapUtil.getString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                String promotionId = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                long channelAmount = wxpayContribute + otherContribute;
                long mchAmount = amount - channelAmount;
                //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)) {
                    if(mchAmount > 0) {
                        payments.add(
                                CollectionUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, mchAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                        discountChanelMchTotal += mchAmount;
                    }
                    if (channelAmount > 0) {
                        payments.add(
                                CollectionUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, channelAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }
                } else if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)) {
                    if (mchAmount > 0) {
                        payments.add(
                                CollectionUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, mchAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }
                    if (channelAmount > 0) {
                        payments.add(
                                CollectionUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, channelAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }

                }
                couponSum += amount;
            }
        }

        return Pair.of(couponSum, discountChanelMchTotal);
    }

    /**
     * 商户子商户号报备名称
     * @param tradeParams
     * @param transaction
     * @return
     */
    public String getSubMerchantName(Map<String,Object> tradeParams, Map<String,Object> transaction){
        String merchantName = MapUtil.getString(tradeParams, TransactionParam.TRADE_PARAMS_MERCHANT_NAME);
        if(StringUtil.empty(merchantName)){
            merchantName = MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.MERCHANT_NAME);
        }
        return merchantName;
    }


    /**
     * 云闪付b2c交易是异步的，但是由于b2c交易直接返回RC_IN_PROG的话，终端组会播报"需要用户输入密码"
     * 为了避免这种情况，针对b2c交易，休眠3秒之后，走查询，再返回给状态机
     * @param context
     * @return
     */
    public String unionPayB2cTradeProcess(TransactionContext context) {

        //由于海科是对接的银联的异步应答模式， 故均处理为需要进一步查询订单的实际状态
        boolean haikeUnionB2cTradeDelayForQuery = ApolloConfigurationCenterUtil.getUnionB2cTradeDelayForQuery();
        if (haikeUnionB2cTradeDelayForQuery && concurrencySemaphore.tryAcquire()) {
            try {
                return unionPayB2cTradeDelayForQuery(context);
            } finally {
                concurrencySemaphore.release(); // 释放许可
            }
        }

        return Workflow.RC_IN_PROG;
    }


    private String unionPayB2cTradeDelayForQuery(TransactionContext context) {

        try {
            long delayMills = ApolloConfigurationCenterUtil.getUnionB2cTradeDelayForQueryMills();
            Thread.sleep(delayMills);
        } catch (InterruptedException e) {
            logger.error("union bsc trade sleep error!");
            return Workflow.RC_IN_PROG;
        }

        //休眠之后走查单
        String queryResult = query(context);
        if (Workflow.RC_PAY_SUCCESS.equals(queryResult)) {
            return queryResult;
        }
        return Workflow.RC_IN_PROG;
    }
}
