package com.wosai.upay.workflow;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.net.ssl.SSLContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.hk.BusinessFields;
import com.wosai.mpay.api.weixin.hk.ProtocolFields;
import com.wosai.mpay.api.weixin.hk.RequestBuilder;
import com.wosai.mpay.api.weixin.hk.ResponseFields;
import com.wosai.mpay.api.weixin.hk.WeixinConstants;
import com.wosai.mpay.api.weixin.hk.WeixinHKClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.Base64;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class WeixinHKServiceProvider extends AbstractServiceProvider {
	private static final Logger logger = LoggerFactory.getLogger(WeixinHKServiceProvider.class);
    public static final String NAME = "provider.weixin.hk";
    protected ConcurrentHashMap<String, SSLContext> sslContextMap = new ConcurrentHashMap<String, SSLContext>();
    @Autowired
    private WeixinHKClient client;
    private static int retryTimes = 3;
    
    public WeixinHKServiceProvider(){
    	this.dateFormat = new SafeSimpleDateFormat(WeixinConstants.DATE_TIME_FORMAT);
    }
    
    @Override
	public String getName() {
		return NAME;
	}

	@Override
	public boolean canHandle(Map<String, Object> transaction) {
		if(getTradeParams(transaction) == null){
            return false;
        }
        int payway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        String currency = getTradeCurrency(transaction);
        return (payway == Order.PAYWAY_WEIXIN_HK 
        			&& (subPayway == Order.SUB_PAYWAY_BARCODE)
        			&& TransactionParam.UPAY_CURRENCY_HKD.equals(currency)) ? true : false;
	}

	@Override
	public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
	    return getTradeParams(transaction, TransactionParam.WEIXIN_TRADE_PARAMS);
	}

	@Override
	public Integer getProvider() {
	    return null;
	}

	@Override
	public String pay(TransactionContext context, boolean resume) {
		Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.SPBILL_CREATE_IP, getLocalhostIp());
        builder.set(BusinessFields.AUTH_CODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        builder.set(ProtocolFields.DEVICE_INFO, config.get(TransactionParam.GOODS_TAG));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), (String) config.get(TransactionParam.WEIXIN_APP_KEY), getSSLContext(context), builder.build(), retryTimes, "pay");
        } catch (Exception ex) {
            logger.error("failed to call weixin pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if (context.getApiVer() == 1) {
        	transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        if(StringUtil.empty(returnCode)){
            return Workflow.RC_IN_PROG;
        }
        setTradeNoBuyerInfoIfExists(result, context);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            String returnMsg = BeanUtil.getPropString(result, ResponseFields.RETURN_MSG);
            if("SYSTEM ERROR".equals(returnMsg)){
                return Workflow.RC_IN_PROG;
            }else{
                return Workflow.RC_PROTOCOL_ERROR;
            }
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.ERR_CODE_ORDER_IN_PROD.equals(errCode)){
                return Workflow.RC_IN_PROG;
            }
            if(WeixinConstants.RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST.contains(errCode)){
                return Workflow.RC_TRADE_CANCELED;
            }
            if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_FAIL_LIST.contains(errCode)){
                return Workflow.RC_TRADE_CANCELED;
            }else if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST.contains(errCode)) {
                return Workflow.RC_IN_PROG;
            }else {
                return Workflow.RC_ERROR;
            }
        }
        //付款成功
        resolvePayFund(result, context);
        return Workflow.RC_PAY_SUCCESS;

	}
	
	@Override
	public String cancel(TransactionContext context) {
		Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), (String) config.get(TransactionParam.WEIXIN_APP_KEY), getSSLContext(context), builder.build(), retryTimes, "cancel");
        } catch (Exception ex) {
            logger.error("failed to call weixin cancel", ex);
            setTransactionContextErrorInfo(context, "cancel", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.ERR_CODE_SYSTEM_ERROR.equals(errCode)
                    || WeixinConstants.ERR_CODE_ORDER_IN_PROD.equals(errCode)
                    || WeixinConstants.ERR_CODE_UNKNOWN_ORDER_STATUS.equals(errCode)){
                return Workflow.RC_RETRY;
            }else{
                return Workflow.RC_ERROR;
            }
        }else{
            return Workflow.RC_CANCEL_SUCCESS;
        }
	}
	
	protected String doRefund(TransactionContext context, String operation, long refundFee) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.TOTAL_FEE, order.get(Order.EFFECTIVE_TOTAL)+"");
        builder.set(BusinessFields.REFUND_FEE, refundFee +  "");
        builder.set(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.OP_USER_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), (String) config.get(TransactionParam.WEIXIN_APP_KEY), getSSLContext(context), builder.build(), retryTimes, "refund");
        } catch (Exception ex) {
            logger.error("failed to call weixin refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            //异常进行重试
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        setTransactionContextErrorInfo(result, context, operation);
        if (context.getApiVer() == 1) {
        	transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        if(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            //退款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ID));
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        }else if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }else{
            return Workflow.RC_ERROR;
        }

    }

	@Override
	public String query(TransactionContext context) {
		Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> result = doQuery(context);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            //通讯标识为失败，重新查询
            return Workflow.RC_IN_PROG;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.ERR_CODE_SYSTEM_ERROR.equals(errCode)){
                return Workflow.RC_IN_PROG;
            }else{
                return Workflow.RC_ERROR;
            }
        }
        String tradeState = (String) result.get(ResponseFields.TRADE_STATE);
        String rcFlag = Workflow.RC_ERROR;
        if(WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)){
            rcFlag = Workflow.RC_IN_PROG;
        }else if(WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState)){
            //跟微信的人确认过，b2c下，not_pay表明用户已经取消了付款。
            rcFlag = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
        }else if(WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)){
            rcFlag = Workflow.RC_PAY_SUCCESS;
            //付款成功
            if(BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT){
                resolvePayFund(result, context);
            }
        }
        return rcFlag;
	}
	
	protected Map<String,Object> doQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        try {
            return retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), (String) config.get(TransactionParam.WEIXIN_APP_KEY), getSSLContext(context), builder.build(), retryTimes, "query");
        } catch (Exception ex) {
            logger.error("failed to call weixin query", ex);
            return null;
        }
    }

	@Override
	public String refund(TransactionContext context) {
		Map<String, Object> transaction = context.getTransaction();
        long refundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        return doRefund(context, OP_REFUND, refundFee);
	}

	@Override
	public String precreate(TransactionContext context, boolean resume) {
		return null;
	}

	@Override
	public String explainNotification(Map<String, Object> providerNotification) {
		return null;
	}
	
	protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> config = getTradeParams(transaction);
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        String timeEnd = BeanUtil.getPropString(result, ResponseFields.TIME_END);
        String currency = BeanUtil.getPropString(result, ResponseFields.FEE_TYPE);
        String transAmountCNY = BeanUtil.getPropString(result, ResponseFields.CASH_FEE);
        if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
            String subOpenId = BeanUtil.getPropString(result, ResponseFields.SUB_OPEN_ID);
            if(!StringUtil.empty(subOpenId)){
                transaction.put(Transaction.BUYER_UID, subOpenId);
            }
        }
        if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))){
            String openId = BeanUtil.getPropString(result, ResponseFields.OPEN_ID);
            if(!StringUtil.empty(openId)){
                transaction.put(Transaction.BUYER_LOGIN, openId);
            }
        }
        if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
            String tradeNo = BeanUtil.getPropString(result, ResponseFields.TRANSACTION_ID);
            if(!StringUtil.empty(tradeNo)){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
        if(StringUtil.empty(BeanUtil.getPropString(extraOutFields, Transaction.WEIXIN_APPID))){
            extraOutFields.put(Transaction.WEIXIN_APPID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
        }

        if(!StringUtil.empty(timeEnd)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME))){
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(timeEnd));
            }
        }
        
        if (!StringUtil.empty(currency)) {
            overseas.put(TransactionParam.CURRENCY, currency);
        }
        if (!StringUtil.empty(transAmountCNY)) {
            overseas.put(TransactionParam.TRANS_AMOUNT_CNY, transAmountCNY);
        }
    }
	
	 /**
     * 解析返回金额相关信息
     * @param context
     */
    public static   void resolvePayFund(Map<String, Object> result, TransactionContext context){
        if(result == null){
            return;
        }
        //免充值升级后的接口返回与以前的不一样
        String promotionPath = ResponseFields.PROMOTION_DETAIL + "." + ResponseFields.PROMOTION_DETAIL;
        List<Map<String,Object>> promotions = (List<Map<String, Object>>) BeanUtil.getNestedProperty(result, promotionPath);
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL) && promotions != null){
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            if(promotions != null){
                Map<String,Object> order = context.getOrder();
                Map<String, Object> transaction = context.getTransaction();

                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                if(payments == null || payments.isEmpty()){
                    extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(result));
                }
                long discountAmount = getSumAmountOfPromotionDetail(promotions);
                if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discountAmount != 0){
                    order.put(Order.TOTAL_DISCOUNT, discountAmount);
                    order.put(Order.NET_DISCOUNT, discountAmount);
                }
                long paidAmount = totalFee - discountAmount;
                if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0){
                    transaction.put(Transaction.PAID_AMOUNT, paidAmount);
                }
                long receiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for(Map<String,Object> promotion: promotions){
                    if(promotion.isEmpty()){
                        continue;
                    }
                    String type = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                    long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if(DirectWeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                        receiveAmount = receiveAmount - (amount - wxpayContribute -otherContribute);

                    }
                }
                if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
                }
            }
        }else{
            Map<String, Object> transaction = context.getTransaction();
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            long discount = BeanUtil.getPropLong(result, ResponseFields.COUPON_FEE, 0);
            long cashFee = totalFee - discount;
            transaction.put(Transaction.PAID_AMOUNT, cashFee);
            if(totalFee > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, totalFee);
            }
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
            if(payments == null || payments.isEmpty()){
                extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(result));
            }
            Map<String,Object> order = context.getOrder();
            if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discount != 0){
                order.put(Order.TOTAL_DISCOUNT, discount);
                order.put(Order.NET_DISCOUNT, discount);
            }
        }

    }
    
    @SuppressWarnings("unchecked")
    public static  List<Map<String,Object>> getWeixinPayments(Map<String,Object> result){
        String promotionPath = ResponseFields.PROMOTION_DETAIL + "." + ResponseFields.PROMOTION_DETAIL;
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            long cashFee = totalFee - getSumAmountOfPromotionDetail((List<Map<String, Object>>) BeanUtil.getNestedProperty(result, promotionPath));
            String banktype = BeanUtil.getPropString(result, ResponseFields.BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
            List<Map<String,Object>> promotions = (List<Map<String, Object>>) BeanUtil.getNestedProperty(result, promotionPath);
            if(promotions != null){
                for(Map<String,Object> promotion: promotions){
                    if(promotion.isEmpty()){
                        continue;
                    }
                    String type = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                    String promotionId = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                    long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    long channelAmount = wxpayContribute + otherContribute;
                    long mchAmount = amount - channelAmount;
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if(DirectWeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                        if(mchAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }else if(channelAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }
                    }else if(DirectWeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)){
                        if(mchAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }else if(channelAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }

                    }
                }
            }
            return  payments;
        }else{
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TOTAL_FEE, 0);
            long discount = BeanUtil.getPropLong(result, ResponseFields.COUPON_FEE, 0);
            long cashFee = totalFee - discount;
            String banktype = BeanUtil.getPropString(result, ResponseFields.BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
            if(discount > 0){
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }
            return payments;
        }

    }
    
    /**
     * 计算微信总的优惠信息
     * @param promotions
     * @return
     */
    protected static long getSumAmountOfPromotionDetail(List<Map<String, Object>> promotions){
        long sum = 0;
        if(promotions != null){
            for(Map<String,Object> promotion: promotions){
                if(promotion.isEmpty()){
                    continue;
                }
                long amount = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                sum = sum + amount;
            }
        }
        return sum;
    }
    
    public static  Map<String,Object> getWeixinPaymentByBanktype(String banktype, long amount){
        if(amount  <= 0 || StringUtil.empty(banktype)){
            return null;
        }else{
            String type = null;
            if(DirectWeixinServiceProvider.WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE.equals(banktype)){
                type = Payment.TYPE_WALLET_WEIXIN;
            }else if(banktype.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX)){
                type = Payment.TYPE_BANKCARD_CREDIT;
            }else if(banktype.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX)){
                type = Payment.TYPE_BANKCARD_DEBIT;
            }else if(!StringUtil.empty(banktype)){
                type = banktype.toUpperCase();
            }
            return CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, type,
                    Transaction.PAYMENT_ORIGIN_TYPE, banktype,
                    Transaction.PAYMENT_AMOUNT, amount
            );
        }
    }
    
    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String returnMsg = (String)result.get(ResponseFields.RETURN_MSG);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        String errCodeDes = (String)result.get(ResponseFields.ERR_CODE_DES); //错误代码
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RETURN_CODE, returnCode);
        map.put(ResponseFields.RETURN_MSG, returnMsg);
        map.put(ResponseFields.RESULT_CODE, resultCode);
        map.put(ResponseFields.ERR_CODE, errCode);
        map.put(ResponseFields.ERR_CODE_DES, errCodeDes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode),
                StringUtil.empty(errCode) ? resultCode: errCode, 
                StringUtil.empty(errCodeDes) ? returnMsg : errCodeDes);
    }
    
    protected SSLContext getSSLContext(TransactionContext context){
        Map<String, Object> config = getTradeParams(context.getTransaction());
        String certConfigKey = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_CONFIG_KEY);
        String password = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_PASSWORD);
        if(sslContextMap.get(certConfigKey) == null){
            synchronized (this){
                if(sslContextMap.get(certConfigKey) == null){
                    byte [] certData = Base64.decode(getPrivateKeyContent(certConfigKey));
                    SSLContext sslContext =  client.getSSLContext(certData, password);
                    sslContextMap.put(certConfigKey, sslContext);
                }
            }
        }
        return sslContextMap.get(certConfigKey);
    }
    
    /**
     * 获取默认的requestBuilder，设置请求默认值。
     * @param context
     * @return
     */
    protected   RequestBuilder getDefaultRequestBuilder(TransactionContext context){
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        return builder;
    }
    
    protected static String getLocalhostIp(){
        try {
            InetAddress address = InetAddress.getLocalHost();
            return address.getHostAddress().toString();
        } catch (UnknownHostException e) {
            return "127.0.0.1";
        }
    }
    
    protected Map<String, Object> retryIfNetworkException(String url, String key, SSLContext sslContext, Map<String,Object> request, int times, String logFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                return client.call(url, key, sslContext, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in weixin {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    /**
     * 解析退款返回金额信息
     * @param result
     * @param context
     */
    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        Map<String,Object> order = context.getOrder();
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        String merchantId = BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        String currency = BeanUtil.getPropString(result, ResponseFields.FEE_TYPE);
        String refundAmountCNY = BeanUtil.getPropString(result, ResponseFields.CASH_REFUND_FEE);
        if(!StringUtil.empty(currency)){
            overseas.put(TransactionParam.CURRENCY,currency);
        }
        if(!StringUtil.empty(refundAmountCNY)){
            overseas.put(TransactionParam.REFUND_AMOUNT_CNY,refundAmountCNY);
        }
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            long cashRefundFee = BeanUtil.getPropLong(result, ResponseFields.CASH_REFUND_FEE);
            if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0 && cashRefundFee != 0){
                transaction.put(Transaction.PAID_AMOUNT, cashRefundFee);
            }

            if(payments != null){
                List<Map<String,Object>> refundPayments = new ArrayList<>();
                for (int i = 0; i < payments.size(); i++) {
                    Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                    refundPayments.add(refundPayment);
                }
                int couponCount = BeanUtil.getPropInt(result, ResponseFields.COUPON_REFUND_COUNT);
                for (int i = 0; i < couponCount; i++) {
                    String couponRefundId = BeanUtil.getPropString(result, ResponseFields.COUPON_REFUND_ID_PREFIX + "_" + i);
                    String couponRefundFee = BeanUtil.getPropString(result, ResponseFields.COUPON_REFUND_FEE_PREFIX + "_" + i);
                    for (int j = 0; j < refundPayments.size(); j++) {
                        Map<String,Object> refundPayment = refundPayments.get(j);
                        String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                        if(sourceId.equals(couponRefundId)){
                            refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                        }
                    }
                }
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_WALLET_WEIXIN.equals(type) || Payment.TYPE_BANKCARD_CREDIT.equals(type) || Payment.TYPE_BANKCARD_DEBIT.equals(type)){
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, cashRefundFee);
                        break;
                    }
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                long settlementRefundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                        settlementRefundFee = settlementRefundFee - BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT);
                    }
                }
                if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefundFee != 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefundFee);
                }
            }

        }

    }
}
