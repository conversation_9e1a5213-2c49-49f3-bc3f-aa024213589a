package com.wosai.upay.workflow;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;

import com.wosai.data.bean.BeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.chinaums.BusinessFields;
import com.wosai.mpay.api.chinaums.ChinaumsConstants;
import com.wosai.mpay.api.chinaums.ProtocolFields;
import com.wosai.mpay.api.chinaums.RequestBuilder;
import com.wosai.mpay.api.chinaums.ResponseFields;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;


/**
 * 
 * 银商免密支付
 * 
 */
public class ChinaumsEPayServiceProvider extends ChinaumsV1ServiceProvider{
    private static Logger logger = LoggerFactory.getLogger(ChinaumsEPayServiceProvider.class);
    public static final String NAME = "provider.chinaums.epay";
    private static final String GATEWAY_UNION_DEPOSIT_CONSUME = "union.deposit.consume";
    private static final String GATEWAY_UNION_REFUND = "union.refund";
    private static final String GATEWAY_UNION_REFUND_QUERY = "union.refund.query";
    private static final String GATEWAY_UNION_QUERY = "union.query";

    public ChinaumsEPayServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_AMOUNT, BusinessFields.REFUND_AMOUNT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        // 当前只支持银联云闪付小程序支付
        if(payway != Order.PAYWAY_UNIONPAY || subPayway != Order.SUB_PAYWAY_MINI) {
            return false;
        }
        Map<String, Object> tradeParams = getTradeParams(transaction);
        boolean isVersionOne = Objects.equals(ChinaumsV1ServiceProvider.TRADE_PARAMS_SQB_VERSION_1, BeanUtil.getPropString(tradeParams, TransactionParam.CHINAUMS_SQB_VERSION));
        if(isVersionOne){
            return false;
        }
        if (Objects.isNull(tradeParams) || !UpayUtil.isFormalByTradeParams(tradeParams)) {
            return false;
        }
        return true;
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        // 添加免密支付标识
        extraOutFields.put(Transaction.ENTRUST_PAY, true);
        // 添加mock返回
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(ResponseFields.REDIRECTURL, "entrust pay"));
        // 免密支付从产品层面考虑会包装成预授权，在预授权时直接返回成功
        return Workflow.RC_CREATE_SUCCESS;
    }

    public String depositConsume(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), GATEWAY_UNION_DEPOSIT_CONSUME);

        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.MER_ORDER_ID, getChinaumsMerOrderId(tradeParams, (String)transaction.get(Transaction.TSN)));
        builder.set(BusinessFields.ORDER_DESC, context.getOrder().get(Order.SUBJECT));
        builder.set(BusinessFields.TOTAL_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        carryOverExtendedParams(extendedParams, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_DEPOSIT_CONSUME);
        } catch (Exception e) {
            logger.error("failed to call chinaums entrust consume", e);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CONSUME, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_CONSUME);

        return buildDepositConsumeResult(result, context);
    }

    private String buildDepositConsumeResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        //明确成功
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            String status = MapUtil.getString(result, ResponseFields.STATUS);
            if(ChinaumsConstants.STATUS_TRADE_SUCCESS.equals(status)) {
                resolvePayFund(result, context);
                return Workflow.RC_CONSUME_SUCCESS;
            }else if(ChinaumsConstants.STATUS_WAIT_BUYER_PAY.equals(status)) {
                return depositQuery(context);
            }
            return Workflow.RC_CONSUME_FAIL;
        }else if(ChinaumsConstants.RESP_CODE_V1_DUP_ORDER.equals(responseCode)) {
            return depositQuery(context);
        }else if(ChinaumsConstants.RESP_CODE_V1_PAY_FAIL_SET.contains(responseCode)) {
            return Workflow.RC_CONSUME_FAIL;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), GATEWAY_UNION_REFUND);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        // 退款请求单号要取预授权完成成功流水单号，非订单号
        Map<String, Object> consumeTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        builder.set(BusinessFields.MER_ORDER_ID, getChinaumsMerOrderId(tradeParams, (String)consumeTransaction.get(Transaction.TSN)));
        builder.set(BusinessFields.REFUND_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.REFUND_ORDER_ID, getChinaumsMerOrderId(tradeParams, (String)transaction.get(Transaction.TSN)));
        
        carryOverExtendedParams(extendedParams, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call chinaums entrust refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            if(e instanceof MpayApiSendError || e instanceof MpayApiReadError) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context);
    }

    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            String status = MapUtil.getString(result, ResponseFields.REFUND_STATUS);
            if(ChinaumsConstants.REFUND_STATUS_SUCCESS.equals(status)) {
                resolveRefundFund(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            }else if(ChinaumsConstants.REFUND_STATUS_UNKNOWN.equals(status) 
                    || ChinaumsConstants.REFUND_STATUS_PROCESSING.equals(status)) {
                return Workflow.RC_IN_PROG;
            }
        }else if(ChinaumsConstants.RESP_CODE_V1_PAY_FAIL_SET.contains(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }else if(ChinaumsConstants.RESP_CODE_V1_DUP_ORDER.equals(responseCode)) {
            return refundQuery(context);
        }
        return Workflow.RC_ERROR;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), GATEWAY_UNION_REFUND_QUERY);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.MER_ORDER_ID, getChinaumsMerOrderId(tradeParams, (String)transaction.get(Transaction.TSN)));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_REFUND_QUERY);
        } catch (Exception e) {
            logger.error("failed to call chinaums entrust refund query", e);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, e);
            if(e instanceof MpayApiSendError || e instanceof MpayApiReadError) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
        return buildRefundQueryResult(result, context);
    }

    private String buildRefundQueryResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            String status = MapUtil.getString(result, ResponseFields.REFUND_STATUS);
            if(ChinaumsConstants.REFUND_STATUS_SUCCESS.equals(status)) {
                resolveRefundFund(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            }else if(ChinaumsConstants.STATUS_NEW_ORDER.equals(status) 
                    || ChinaumsConstants.REFUND_STATUS_UNKNOWN.equals(status)
                    || ChinaumsConstants.REFUND_STATUS_PROCESSING.equals(status)) {
                return Workflow.RC_IN_PROG;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String depositQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        // 免密支付从产品层面考虑会包装成预授权，在预授权查询时直接返回成功
        if(Transaction.TYPE_DEPOSIT_FREEZE == MapUtil.getIntValue(transaction, Transaction.TYPE)) {
            return Workflow.RC_PAY_SUCCESS;
        }
        
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), GATEWAY_UNION_QUERY);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.MER_ORDER_ID, getChinaumsMerOrderId(tradeParams, (String)transaction.get(Transaction.TSN)));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_DEPOSIT_QUERY);
        } catch (Exception e) {
            logger.error("failed to call chinaums entrust deposit query", e);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_QUERY, e);
            if(e instanceof MpayApiSendError || e instanceof MpayApiReadError) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_QUERY);
        return buildDepositQueryResult(result, context);
    }

    private String buildDepositQueryResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            String status = MapUtil.getString(result, ResponseFields.STATUS);
            if(ChinaumsConstants.STATUS_TRADE_SUCCESS.equals(status)) {
                resolvePayFund(result, context);
                return Workflow.RC_CONSUME_SUCCESS;
            }else if(ChinaumsConstants.STATUS_NEW_ORDER.equals(status) 
                    || ChinaumsConstants.STATUS_WAIT_BUYER_PAY.equals(status)
                    || ChinaumsConstants.STATUS_UNKNOWN.equals(status)) {
                return Workflow.RC_IN_PROG;
            }else if(ChinaumsConstants.STATUS_TRADE_CLOSED.equals(status)) {
                return Workflow.RC_TRADE_CANCELED;
            }
        }else if(ChinaumsConstants.RESP_CODE_V1_PAY_FAIL_SET.contains(responseCode) || ChinaumsConstants.RESP_CODE_V1_NO_ORDER.equals(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context){
        String fininshTime = result.containsKey(ResponseFields.PAY_TIME) ? (String)result.get(ResponseFields.PAY_TIME) : (String)result.get(ResponseFields.RESPONSE_TIMESTAMP);
        String buyerId = MapUtil.getString(result, ResponseFields.BUYER_ID);
        String tradeNo = MapUtil.getString(result, ResponseFields.SEQ_ID);

        context.getOrder().put(Order.BUYER_UID, buyerId);
        context.getOrder().put(Order.TRADE_NO, tradeNo);

        context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(fininshTime));
        context.getTransaction().put(Transaction.BUYER_UID, buyerId);
        context.getTransaction().put(Transaction.TRADE_NO, tradeNo);
        context.getTransaction().put(Transaction.PAID_AMOUNT, context.getTransaction().get(Transaction.EFFECTIVE_AMOUNT));
        context.getTransaction().put(Transaction.RECEIVED_AMOUNT, context.getTransaction().get(Transaction.EFFECTIVE_AMOUNT));
    }

    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        context.getTransaction().put(Transaction.TRADE_NO, MapUtil.getString(result, ResponseFields.SEQ_ID));
        context.getTransaction().put(Transaction.PAID_AMOUNT, context.getTransaction().get(Transaction.EFFECTIVE_AMOUNT));
        context.getTransaction().put(Transaction.RECEIVED_AMOUNT, context.getTransaction().get(Transaction.EFFECTIVE_AMOUNT));
    }

    @Override
    public String depositCancel(TransactionContext context) {
        context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        return Workflow.RC_CANCEL_SUCCESS;
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(Map<String, Object> config, Map<String, Object> transaction) {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ProtocolFields.MID, config.get(TransactionParam.CHINAUMS_MCH_CODE));
        requestBuilder.set(ProtocolFields.TID, config.get(TransactionParam.CHINAUMS_TERM_CODE));
        requestBuilder.set(BusinessFields.REQUEST_TIMESTAMP, formatTimeString(System.currentTimeMillis()));
        return requestBuilder;
    }

    private void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            builder.set(key, value);
        }
    }

    private String getChinaumsMerOrderId(Map<String, Object> tradeParams, String tsn) {
        String source = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_SOURCE, "");
        return source + tsn;
    }
}
