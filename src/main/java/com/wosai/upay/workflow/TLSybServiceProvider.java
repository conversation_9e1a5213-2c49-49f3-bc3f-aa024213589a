package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.fuyou.FuyouBusinessFields;
import com.wosai.mpay.api.fuyou.FuyouConstants;
import com.wosai.mpay.api.fuyou.FuyouRequestBuilder;
import com.wosai.mpay.api.tl.syb.*;
import com.wosai.mpay.api.unionpayopen.UnionPayOpenConstants;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class TLSybServiceProvider extends AbstractServiceProvider {

    public static final String NAME = "provider.tl.syb";

    public static final Logger logger = LoggerFactory.getLogger(TLSybServiceProvider.class);
    public String notifyHost;
    private int retryTimes = 3;

    private static final Set<Integer> CAN_HANDLER_PAYWAY = new HashSet<>(Arrays.asList(Payway.ALIPAY.getCode(), Payway.ALIPAY2.getCode(), Payway.WEIXIN.getCode(), Payway.UNIONPAY.getCode()));
    private static final String UNION_PAY_QRCODE_REDIRECT_URL = "redirectUrl";
    private static final String UNION_PAY_APP_END = "CloudPay";
    private static final String TIME_FORMAT = "yyyyMMddHHmmss";

    /**
     * 收银宝的其他类型的type 可能是余额 99-其他（花呗/余额等）
     */
    private static final Map<String, String> WX_ACCT_TYPE_MAPPING = MapUtil.hashMap(
            SybConstants.ACCT_TYPE_CREDIT_VALUE, WeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX,
            SybConstants.ACCT_TYPE_DEBIT_VALUE, WeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX,
            SybConstants.ACCT_TYPE_OTHER_VALUE, WeixinServiceProvider.WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE
    );

    /**
     * payway+sub_payway对应paytype的类型
     */
    private static final Map<String, String> PAY_TYPE_MAPPING = MapUtil.hashMap(
            Payway.ALIPAY2.getCode() + "-" + Order.SUB_PAYWAY_QRCODE, SybConstants.PAY_TYPE_A01,
            Payway.ALIPAY2.getCode() + "-" + Order.SUB_PAYWAY_WAP, SybConstants.PAY_TYPE_A02,
            Payway.ALIPAY2.getCode() + "-" + Order.SUB_PAYWAY_MINI, SybConstants.PAY_TYPE_A02,
            Payway.WEIXIN.getCode() + "-" + Order.SUB_PAYWAY_QRCODE, SybConstants.PAY_TYPE_W01,
            Payway.WEIXIN.getCode() + "-" + Order.SUB_PAYWAY_WAP, SybConstants.PAY_TYPE_W02,
            Payway.WEIXIN.getCode() + "-" + Order.SUB_PAYWAY_MINI, SybConstants.PAY_TYPE_W06,
            Payway.UNIONPAY.getCode() + "-" + Order.SUB_PAYWAY_QRCODE, SybConstants.PAY_TYPE_U01,
            Payway.UNIONPAY.getCode() + "-" + Order.SUB_PAYWAY_WAP, SybConstants.PAY_TYPE_U02
    );

    /**
     * wap支付的类型 需要传acct
     */
    private static final Set<String> JS_PAY_TYPE = new HashSet<>(Arrays.asList(SybConstants.PAY_TYPE_A02, SybConstants.PAY_TYPE_W02, SybConstants.PAY_TYPE_U02));

    /**
     * 允许透传的参数
     */
    public static final Map<Integer,Set<String>> EXTEND_PARAMS_FIELDS = MapUtil.hashMap(
            Payway.ALIPAY2.getCode(), new HashSet<>(Arrays.asList(
                    SybConstants.EXTEND_PARAMS_KEY_ALIPAY_FOOD_ORDER_TYPE,
                    SybConstants.EXTEND_PARAMS_KEY_ALIPAY_EXT_USER_INFO,
                    SybConstants.EXTEND_PARAMS_KEY_ALIPAY_INDUSTRY_REFLUX_INFO,
                    SybConstants.EXTEND_PARAMS_KEY_ALIPAY_SYS_SERVICE_PROVIDER_ID,
                    SybConstants.EXTEND_PARAMS_KEY_ALIPAY_CARD_TYPE,
                    SybConstants.EXTEND_PARAMS_KEY_SPECIFIED_SELLER_NAME,
                    SybConstants.EXTEND_PARAMS_KEY_DYNAMIC_TOKEN_OUT_BIZ_NO)),

            Payway.WEIXIN.getCode(), new HashSet<>(Arrays.asList(
                    SybConstants.EXTEND_PARAMS_KEY_WEIXIN_DEVICE_INFO,
                    SybConstants.EXTEND_PARAMS_KEY_WEIXIN_ATTACH,
                    SybConstants.EXTEND_PARAMS_KEY_WEIXIN_STORE_INFO)),
            Payway.UNIONPAY.getCode(), new HashSet<>(Arrays.asList(
                    SybConstants.EXTEND_PARAMS_KEY_UNIONPAY_UPTERMNO)
    ));


    public TLSybServiceProvider() {
        super.dateFormat = new SafeSimpleDateFormat(SybConstants.DATA_FORMAT);
    }

    @Autowired
    TlSybClient tlSybClient;

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_TL_SYB;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return CAN_HANDLER_PAYWAY.contains(payway) && getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.TL_SYB_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, String> request = buildPayRequest(context, tradeParams);
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(OP_PAY, tradeParams, request, 1);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            logger.error("failed to call syb pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, OP_PAY, e);
            logger.error("encountered ioex in syb pay", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        setTradeNoBuyerInfoIfExists(result, context, OP_PAY);
        if (SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            if (SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)) {
                setTransactionContextIfPaySuccess(result, context, tradeParams, OP_PAY);
                int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
                resolvePayFundMap.get(payway).resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            } else if (SybConstants.TRX_STATUS_PROCESSING_1.equalsIgnoreCase(trxStatus) || SybConstants.TRX_STATUS_PROCESSING_2.equalsIgnoreCase(trxStatus)) {
                return Workflow.RC_IN_PROG;
            }else {
                String errmsg = MapUtil.getString(result, ResponseFields.ERRMSG, "");
                if(errmsg.equals(SybConstants.DUPLICATE_TRANSACTION_ERROR_MSG)){
                    return Workflow.RC_IN_PROG;
                }
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String cancel(TransactionContext context) {
        String result = refund(context);
        return result == Workflow.RC_REFUND_SUCCESS ? Workflow.RC_CANCEL_SUCCESS : result;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, String> request = buildQueryRequest(context, tradeParams);
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        String op = type == Transaction.TYPE_REFUND ? OP_REFUND_QUERY : OP_QUERY;
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(OP_QUERY, tradeParams, request, retryTimes);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, op, ex);
            logger.error("failed to call syb query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, op, e);
            logger.error("encountered ioex in syb query", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, op);
        if (type == Transaction.TYPE_PAYMENT) {
            setTradeNoBuyerInfoIfExists(result, context, OP_PAY);
        } else if (type == Transaction.TYPE_REFUND) {
            setTradeNoBuyerInfoIfExists(result, context, OP_REFUND_QUERY);
        }
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        if (SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            if (SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)) {
                int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
                if (type == Transaction.TYPE_PAYMENT) {
                    setTransactionContextIfPaySuccess(result, context, tradeParams, OP_PAY);
                    resolvePayFundMap.get(payway).resolvePayFund(result, context);
                    return Workflow.RC_PAY_SUCCESS;
                } else if (type == Transaction.TYPE_REFUND) {
                    setTransactionContextIfPaySuccess(result, context, tradeParams, OP_REFUND_QUERY);
                    resolveRefundFundMap.get(payway).resolveRefundFund(result, context, getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME)));
                    return Workflow.RC_REFUND_SUCCESS;
                }
            } else if (SybConstants.TRX_STATUS_PROCESSING_1.equalsIgnoreCase(trxStatus) || SybConstants.TRX_STATUS_PROCESSING_2.equalsIgnoreCase(trxStatus)) {
                return type == Transaction.TYPE_PAYMENT ? Workflow.RC_IN_PROG : Workflow.RC_RETRY;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        boolean tlSwitchSybFlag = MapUtil.getBooleanValue(configSnapshot, UpayConstant.TL_SWITCH_SYB_FLAG, false);
        Map<String, String> request = new HashMap<>();
        String op = OP_REFUND;
        if (tlSwitchSybFlag) {
            request = buildRefundRequestV2(context, tradeParams);
            op = OP_REFUND_V2;
        }else {
            request = buildRefundRequest(context, tradeParams);
        }
        Map<String, Object> result = null;
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery){
            return query(context);
        }
        try {
            result = retryIfNetworkException(op, tradeParams, request, 1);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, op, ex);
            logger.error("failed to call syb refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, op, e);
            logger.error("encountered ioex in syb refund", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, op);
        setTradeNoBuyerInfoIfExists(result, context, op);
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        if (SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            if (SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)) {
                setTransactionContextIfPaySuccess(result, context, tradeParams, op);
                int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
                resolveRefundFundMap.get(payway).resolveRefundFund(result, context, getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME)));
                return Workflow.RC_REFUND_SUCCESS;
            } else if (SybConstants.TRX_STATUS_PROCESSING_1.equalsIgnoreCase(trxStatus) || SybConstants.TRX_STATUS_PROCESSING_2.equalsIgnoreCase(trxStatus)) {
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return Workflow.RC_RETRY;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, String> request = buildPreCreateRequest(context, tradeParams);
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(OP_PRECREATE, tradeParams, request, retryTimes);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            logger.error("failed to call syb precreate", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            logger.error("encountered ioex in syb precreate", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        if (SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            if (SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)
                    || SybConstants.TRX_STATUS_PROCESSING_1.equalsIgnoreCase(trxStatus)
                    || SybConstants.TRX_STATUS_PROCESSING_2.equalsIgnoreCase(trxStatus)) {
                return returnAndSetTransactionPrecreateSuccess(result, context, tradeParams);
            }
        }
        return Workflow.RC_ERROR;
    }

    @SneakyThrows
    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return query(context);
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, String> request = buildQueryUnionUserIdRequest(transaction, tradeParams);
        Map<String,Object> result = null;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY, tradeParams, request, retryTimes);
        } catch (MpayException ex) {
            logger.error("failed to call syb union query user", ex);
            return null;
        } catch (MpayApiNetworkError e) {
            logger.error("encountered ioex in syb union query user", e);
            return null;
        }
        String userId = MapUtil.getString(result, ResponseFields.ACCT);
        if(StringUtils.isEmpty(userId)){
            return null;
        }
        return CollectionUtil.hashMap(com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID, userId);
    }

    private Map<String, String> buildQueryUnionUserIdRequest(Map<String, Object> transaction, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        setCommonRequest(builder, config);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        builder.set(BusinessFields.AUTHCODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        builder.set(BusinessFields.AUTH_TYPE, SybConstants.AUTH_TYPE_UNION);
        String appUpIdentifier = MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER);
        if(!StringUtils.isEmpty(appUpIdentifier)){
            //对方接口规定如果是云闪付app 则不上送IDENTIFY字段。
            //收钱吧 如果是云闪付支付，前端会上送appUpIdentifier参数并以CloudPay结尾
            if(!appUpIdentifier.endsWith(UNION_PAY_APP_END)){
                builder.set(BusinessFields.IDENTIFY, appUpIdentifier);
            }
        }
        return builder.build();
    }

    protected String returnAndSetTransactionPrecreateSuccess(Map<String, Object> result, TransactionContext context, Map<String, Object> config) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        String trxId = MapUtil.getString(result, ResponseFields.TRX_ID);
        String payInfo = MapUtil.getString(result, ResponseFields.PAY_INFO);
        BeanUtil.setProperty(transaction, Transaction.TRADE_NO, trxId);
        BeanUtil.setProperty(context.getOrder(), Order.TRADE_NO, trxId);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //预下单成功
        if (payway == Order.PAYWAY_WEIXIN) {
            if (subPayway == Order.SUB_PAYWAY_QRCODE) {
                extraOutFields.put(Transaction.QRCODE, payInfo);
            } else if (subPayway == Order.SUB_PAYWAY_MINI || subPayway == Order.SUB_PAYWAY_WAP) {
                Map<String,Object> payInfoMap = JacksonUtil.toBeanQuietly(payInfo, Map.class);
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(WapFields.APP_ID, MapUtil.getString(payInfoMap, WapFields.APP_ID));
                wapRequest.put(WapFields.TIME_STAMP, MapUtil.getString(payInfoMap,WapFields.TIME_STAMP));
                wapRequest.put(WapFields.NONCE_STR, MapUtil.getString(payInfoMap, WapFields.NONCE_STR));
                wapRequest.put(WapFields.SIGN_TYPE, MapUtil.getString(payInfoMap, WapFields.SIGN_TYPE));
                wapRequest.put(WapFields.PACKAGE, MapUtil.getString(payInfoMap, WapFields.PACKAGE));
                wapRequest.put(WapFields.PAY_SIGN, MapUtil.getString(payInfoMap, WapFields.PAY_SIGN));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            } else {
                return Workflow.RC_ERROR;
            }
        } else if (payway == Order.PAYWAY_ALIPAY2) {
            if (subPayway == Order.SUB_PAYWAY_QRCODE) {
                extraOutFields.put(Transaction.QRCODE, payInfo);
            } else if (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI) {
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(WapV2Fields.TRADE_NO, payInfo);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            } else {
                return Workflow.RC_ERROR;
            }
        } else if(payway == Order.PAYWAY_UNIONPAY){
            Map<String, Object> wapRequest = new HashMap<String, Object>();
            wapRequest.put(UNION_PAY_QRCODE_REDIRECT_URL, MapUtil.getString(result, ResponseFields.PAY_INFO));
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        }
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        return Workflow.RC_CREATE_SUCCESS;
    }


    private Map<String, String> buildPayRequest(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        setCommonRequest(builder, config);
        setFqRequestIfUseFq(builder, context);
        setBenefitDetail(builder, context, config);
        setExtendParams(builder, context);
        builder.set(BusinessFields.TRXAMT, MapUtil.getString(transaction,Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.REQSN, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.AUTHCODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        builder.set(BusinessFields.BODY, MapUtil.getString(order, Order.SUBJECT));
        builder.set(BusinessFields.REMARK, MapUtil.getString(order, Order.BODY));
        builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        if (MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Payway.WEIXIN.getCode()) {
            String subAppId = MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID);
            if(!StringUtils.isEmpty(subAppId)) {
                builder.set(BusinessFields.SUB_APPID, subAppId);
            }
        }
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        Map<String, String> map = MapUtil.hashMap(
                BusinessFields.TERMNO, StringUtils.isEmpty(terminalInfo.getId()) ? "00000001" : terminalInfo.getId(),
                BusinessFields.LONGITUDE, terminalInfo.getSybFormatLongitude(),
                BusinessFields.LATITUDE, terminalInfo.getSybFormatLatitude(),
                BusinessFields.DEVICETYPE, terminalInfo.getOrDefaultType(SybConstants.DEFAULT_DEVICE_TYPE));
        BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        if(terminalInfo.getSerialNum() != null){
            map.put(BusinessFields.TERMSN, terminalInfo.getSerialNum());
        }
        builder.set(BusinessFields.TERMIINFO, JacksonUtil.toJsonString(map));
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(payway == Order.PAYWAY_WEIXIN){
            builder.set(BusinessFields.GOODS_TAG, MapUtil.getString(config, TransactionParam.GOODS_TAG));
        }else if(payway == Order.PAYWAY_ALIPAY2){
            builder.set(BusinessFields.CHNLSTOREID, MapUtil.getString(config, TransactionParam.ALIPAY_STORE_ID));
        }else if (payway == Order.PAYWAY_UNIONPAY) {
            String pnrInsIdCd = MapUtil.getString(config, TransactionParam.UNION_PAY_OPEN_PNR_INS_ID_CD);
            if (!StringUtil.empty(pnrInsIdCd)) {
                builder.set(BusinessFields.UNPID, pnrInsIdCd);
            }
        }
        //借贷标识
        limitCredit(builder, transaction);
        return builder.build();
    }


    private Map<String, String> buildQueryRequest(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        setCommonRequest(builder, config);
        builder.set(BusinessFields.REQSN, MapUtil.getString(transaction, Transaction.TSN));
        return builder.build();
    }

    private Map<String, String> buildRefundRequest(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        setCommonRequest(builder, config);
        setBenefitDetail(builder, context, config);
        builder.set(BusinessFields.TRXAMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.REQSN, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.OLDREQSN, MapUtil.getString(order, Order.SN));
        builder.set(BusinessFields.OLDTRXID, MapUtil.getString(order, Order.TRADE_NO));
        return builder.build();
    }

    private Map<String, String> buildRefundRequestV2(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        setCommonRequest(builder, config);
        builder.set(BusinessFields.VERSION, BusinessFields.VERSION_REFUND_V2);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        builder.set(BusinessFields.ISV_CUSID, MapUtil.getString(config, TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID));
        if (payway == Payway.ALIPAY2.getCode()) {
            builder.set(BusinessFields.SUB_MCHID, MapUtil.getString(config, TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID));
            builder.set(BusinessFields.CHNLTYPE, BusinessFields.CHNLTYPE_ALIPAY);
        } else if (payway == Payway.WEIXIN.getCode()) {
            builder.set(BusinessFields.SUB_MCHID, MapUtil.getString(config, TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID));
            builder.set(BusinessFields.CHNLTYPE, BusinessFields.CHNLTYPE_WEIXIN);
            builder.set(BusinessFields.CHANNEL_ID, MapUtil.getString(config, TransactionParam.UNION_PAY_CHANNEL_ID));
        } else if (payway == Payway.UNIONPAY.getCode()) {
            builder.set(BusinessFields.SUB_MCHID, MapUtil.getString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_ID));
            builder.set(BusinessFields.ORIG_ORDER_TIME, DateUtil.formatDate(new Date(MapUtil.getLongValue(order, DaoConstants.CTIME)), TIME_FORMAT));
        }
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME));
        long payOriginalAmount = BeanUtil.getPropLong(payTransaction, Transaction.ORIGINAL_AMOUNT);
        long refundOriginalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        long refundEffectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long payTransactionPayerAmount = FeeUtil.calculatePayerAmount(payTransaction);
        long refundAmount = Math.round(payTransactionPayerAmount * (refundOriginalAmount / new Double(payOriginalAmount)));
        builder.set(BusinessFields.TOTAL_AMOUNT, payOriginalAmount + "");
        builder.set(BusinessFields.REFUND_AMOUNT, refundEffectiveAmount + "");
        builder.set(BusinessFields.REFUND_CUS_AMOUNT, refundAmount + "");
        builder.set(BusinessFields.REFUND_FEE, MapUtil.getString(config, TransactionParam.FEE));
        builder.set(BusinessFields.REFUND_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.OUT_TRADE_NO, MapUtil.getString(payTransaction, Transaction.TSN));
        return builder.build();
    }

    private Map<String, String> buildPreCreateRequest(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        setCommonRequest(builder, config);
        setBenefitDetail(builder, context, config);
        setFqRequestIfUseFq(builder, context);
        builder.set(BusinessFields.TRXAMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.REQSN, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.BODY, MapUtil.getString(order, Order.SUBJECT));
        builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        setExtendParams(builder, context);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(payway == Order.PAYWAY_WEIXIN){
            builder.set(BusinessFields.GOODS_TAG, MapUtil.getString(config, TransactionParam.GOODS_TAG));
        }else if(payway == Order.PAYWAY_ALIPAY2){
            builder.set(BusinessFields.CHNLSTOREID, MapUtil.getString(config, TransactionParam.ALIPAY_STORE_ID));
        } else if (payway == Order.PAYWAY_UNIONPAY) {
            String pnrInsIdCd = MapUtil.getString(config, TransactionParam.UNION_PAY_OPEN_PNR_INS_ID_CD);
            if (!StringUtil.empty(pnrInsIdCd)) {
                builder.set(BusinessFields.UNPID, pnrInsIdCd);
            }
        }
        String key = payway + "-" + subPayway;
        String payType = PAY_TYPE_MAPPING.get(key);
        builder.set(BusinessFields.PAYTYPE, payType);
        String appId = null;
        String acct = null;
        String miniSubAppId = null;
        if (JS_PAY_TYPE.contains(payType)) {
            if (payway == Order.PAYWAY_WEIXIN) {
                if (subPayway == Order.SUB_PAYWAY_WAP) {
                    appId = MapUtil.getString(config, TransactionParam.WEIXIN_SUB_APP_ID);
                    acct = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
                }
            } else if (payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_UNIONPAY) {
                acct = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
            }
        }else if(payType.equals(SybConstants.PAY_TYPE_W06)) {
            miniSubAppId = MapUtil.getString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            acct = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        }
        if (payType.equals(SybConstants.PAY_TYPE_U02)) {
            String termIp = !StringUtils.isEmpty(BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP)) ? BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP) : UpayUtil.getLocalHostIp();
            builder.set(BusinessFields.CUSIP, termIp);
        }
        if (!StringUtils.isEmpty(appId)) {
          builder.set(BusinessFields.SUB_APPID, appId);
        }
        if (!StringUtils.isEmpty(miniSubAppId)) {
            builder.set(BusinessFields.SUB_APPID, miniSubAppId);
        }
        // 优先使用上送的sub_app_id
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, ProtocolFields.SUB_APP_ID);
        if(!StringUtil.empty(sendSubAppId)) {
            builder.set(BusinessFields.SUB_APPID, sendSubAppId);
        }
        if(!StringUtils.isEmpty(acct)){
            builder.set(BusinessFields.ACCT, acct);
        }
        //借贷标识
        limitCredit(builder, transaction);
        return builder.build();
    }

    private void setCommonRequest(RequestBuilder builder, Map<String, Object> config) {
        builder.set(BusinessFields.APPID, MapUtil.getString(config, TransactionParam.TL_SYB_APP_Id));
        builder.set(BusinessFields.CUSID, MapUtil.getString(config, TransactionParam.TL_SYB_CUS_ID));
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        builder.set(BusinessFields.ORGID, MapUtil.getString(config, TransactionParam.TL_SYB_ORG_ID));
    }

    @SneakyThrows
    private void setBenefitDetail(RequestBuilder builder, TransactionContext context, Map<String, Object> config) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> extendParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        if (extendParams == null || extendParams.isEmpty()){
            return;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Order.PAYWAY_WEIXIN) {
            Map detailMap = MapUtil.getMap(extendParams, UpayConstant.DETAIL);
            if (detailMap != null && detailMap.containsKey(UpayConstant.GOODS_DETAIL)) {
                builder.set(BusinessFields.BENEFITDEATIL, objectMapper.writeValueAsString(detailMap));
            }
        } else if (payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_ALIPAY) {
            Object detailList = MapUtil.getObject(extendParams, UpayConstant.GOODS_DETAIL);
            if (detailList != null && detailList instanceof List) {
                builder.set(BusinessFields.BENEFITDEATIL, objectMapper.writeValueAsString(detailList));
            }
        } else if (payway == Order.PAYWAY_UNIONPAY) {
            if (extendParams.containsKey(UpayConstant.DETAIL)) {
                Map detailMap = MapUtil.getMap(extendParams, UpayConstant.DETAIL);
                if (detailMap != null && detailMap.containsKey(UpayConstant.SYB_UNION_PAY_GOODS_INFO)) {
                    //upayUtil里并没有订单信息,这边还要自己设置
                    detailMap.put(UpayConstant.SYB_UNION_PAY_ORDER_INFO, CollectionUtil.hashMap(
                            UpayConstant.SYB_UNION_PAY_ORDER_INFO_TITLE, MapUtil.getString(order, Order.SUBJECT),
                            UpayConstant.SYB_UNION_PAY_ORDER_INFO_DESCRIPTION, MapUtil.getString(order, Order.BODY)
                    ));
                    builder.set(BusinessFields.BENEFITDEATIL, objectMapper.writeValueAsString(detailMap));
                }
            }
        }
    }

    private void setExtendParams(RequestBuilder builder, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        Set<String> keys = EXTEND_PARAMS_FIELDS.get(payway);
        Map<String,Object> extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);
        if (extendedParams != null && !extendedParams.isEmpty()) {
            Map<String,Object> copyExtendMap = new HashMap<>();
            for (Map.Entry<String, Object> extendedParamsEntry : extendedParams.entrySet()) {
                if (keys.contains(extendedParamsEntry.getKey()) && extendedParamsEntry.getValue() != null) {
                    copyExtendMap.put(extendedParamsEntry.getKey(),extendedParamsEntry.getValue());
                }
            }
            //需要放string字符串
            builder.set(BusinessFields.EXTENDPARAMS, JacksonUtil.toJsonString(copyExtendMap));
        }
    }

    protected Map<String, Object> retryIfNetworkException(String logFlag, Map<String, Object> config, Map<String, String> request, int times) throws MpayException, MpayApiNetworkError {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), logFlag);
        String appId = MapUtil.getString(config, TransactionParam.APP_ID);
        String privateKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.TL_SYB_PRIVATE_KEY));
        MpayApiNetworkError tex = null;
        for (int i = 0; i < times; ++i) {
            try {
                return tlSybClient.call(url, appId, privateKey, request);
            } catch (MpayApiNetworkError ex) {
                tex = ex;
                logger.warn("encountered ioex in tl syb {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw tex;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String retCode = MapUtils.getString(result, ResponseFields.RET_CODE, "");//返回的响应码
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS, "");
        String retMsg = MapUtils.getString(result, ResponseFields.RET_MSG, "");//响应描述
        String errmsg = MapUtil.getString(result, ResponseFields.ERRMSG, "");
        if(!com.wosai.pantheon.util.StringUtil.isEmpty(errmsg)){
            errmsg = errmsg.replaceAll("[\\s\\d[a-zA-Z]]", "");
        }

        map.put(ResponseFields.RET_CODE, retCode);//返回状态码
        map.put(ResponseFields.RET_MSG, retMsg);//返回信息
        map.put(ResponseFields.TRX_STATUS, trxStatus);
        map.put(ResponseFields.ERRMSG, errmsg);
        setTransactionContextErrorInfo(context.getTransaction(),key, map, SybConstants.RET_CODE_SUCCESS.equals(retCode) && SybConstants.TRX_STATUS_SUCCESS.equals(trxStatus), retCode, errmsg);
    }

    protected void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String op){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        //收银宝查单时会 如果没有支付成功返回一个默认值acct为000000 需要
        String acct = MapUtil.getString(result, ResponseFields.ACCT);
        if (!StringUtils.isEmpty(acct)) {
            if (op.equals(OP_PAY)) {
                if (!Objects.equals(acct, BeanUtil.getPropString(order, Order.BUYER_UID))) {
                    order.put(Order.BUYER_UID, acct);
                }
                if (!Objects.equals(acct, BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                    order.put(Order.BUYER_LOGIN, acct);
                }
            }
            if (!Objects.equals(acct, BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                transaction.put(Transaction.BUYER_UID, acct);
            }
            if (!Objects.equals(acct, BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
                transaction.put(Transaction.BUYER_LOGIN, acct);
            }
        }
        String trxId = MapUtil.getString(result, ResponseFields.TRX_ID);
        if (!StringUtils.isEmpty(trxId)) {
            if (op.equals(OP_PAY)) {
                order.put(Order.TRADE_NO, trxId);
            }
            transaction.put(Transaction.TRADE_NO, trxId);
        }
        String channelTrxId = MapUtil.getString(result, ResponseFields.CHANNEL_TRX_ID);
        if (!StringUtils.isEmpty(channelTrxId)) {
            Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTrxId);
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
    }

    protected void setTransactionContextIfPaySuccess(Map<String, Object> result, TransactionContext context, Map<String, Object> tradeParams,String op) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        String retCode = MapUtils.getString(result, ResponseFields.RET_CODE);
        if (SybConstants.RET_CODE_SUCCESS.equals(retCode)) {
            String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
            if (SybConstants.TRX_STATUS_SUCCESS.equals(trxStatus)) {
                String finishTime = MapUtil.getString(result, ResponseFields.FIN_TIME);
                if (!StringUtils.isEmpty(finishTime)) {
                    Long finishTimeL = parseTimeString(finishTime);
                    transaction.put(Transaction.FINISH_TIME, finishTimeL);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, finishTimeL);
                }
                String fee = MapUtil.getString(result, ResponseFields.FEE);
                if (!StringUtils.isEmpty(fee)) {
                    tradeParams.put(TransactionParam.FEE, Long.parseLong(fee));
                }
            }
        }

    }

    protected void setFqRequestIfUseFq(RequestBuilder builder, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);
        if (!StringUtils.isEmpty(productFlag)) {
            Map<String, Object> extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);
            Map<String, Object> extendParams = MapUtil.getMap(extendedParams, BusinessV2Fields.EXTEND_PARAMS);
            if (productFlag.contains(ProductFlag.HUABEI.getCode())) {
                if (extendParams.containsKey(TransactionParam.HB_FQ_NUM)) {
                    builder.set(BusinessFields.FQNUM, MapUtil.getString(extendParams, TransactionParam.HB_FQ_NUM));
                }
            } else if (productFlag.contains(ProductFlag.CREDIT_CARD_INSTALMENT.getCode())) {
                builder.set(BusinessFields.FQNUM, MapUtil.getString(extendParams, TransactionParam.FQ_NUM) + "-cc");
            }
        }
    }


    @FunctionalInterface
    interface ResolveRefundFund {
        void resolveRefundFund(Map<String, Object> sybResult, TransactionContext context, Map<String, Object> payTransaction);
    }

    @FunctionalInterface
    interface ResolvePayFund {
        void resolvePayFund(Map<String, Object> sybResult, TransactionContext context);
    }

    private static Map<Integer, ResolvePayFund> resolvePayFundMap = new HashMap<>();
    private static Map<Integer, ResolveRefundFund> resolveRefundFundMap = new HashMap<>();


    static {
        //支付完成支付宝的payment处理方式 其他逻辑于AlipayV2ServiceProvider一致，由于没有返回buyer_pay_amount、receipt_amount属性，所以这边重写了一份
        //这边的buyer_pay_amount 与 receipt_amount 都用接口中返回的trxamt。
        resolvePayFundMap.put(Payway.ALIPAY2.getCode(), (sybResult, context) -> {
            try {
                Map<String, Object> transaction = context.getTransaction();
                Map<String, Object> order = context.getOrder();
                long totalAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                long trxAmount = MapUtil.getLongValue(sybResult, ResponseFields.TRX_AMT);
                if (trxAmount > totalAmount) {
                    //容错
                    trxAmount = totalAmount;
                }
                long discount = totalAmount - trxAmount;
                long discountChannelMchAmount = discount;
                if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0) {
                    transaction.put(Transaction.PAID_AMOUNT, trxAmount);
                }
                if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0) {
                    transaction.put(Transaction.RECEIVED_AMOUNT, trxAmount);
                }
                if (BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l) {
                    order.put(Order.TOTAL_DISCOUNT, discount);
                    order.put(Order.NET_DISCOUNT, discount);
                }

                String channelData = MapUtil.getString(sybResult, ResponseFields.CHANNEL_DATA);
                Map<String, Object> channelDataMap = JacksonUtil.toBeanQuietly(channelData, Map.class);

                if (channelDataMap != null && !channelDataMap.isEmpty()) {
                    Object tradeFundBill = BeanUtil.getNestedProperty(channelDataMap, BusinessV2Fields.FUND_BILL_LIST);
                    if (tradeFundBill instanceof String && !StringUtil.empty((String) tradeFundBill)) {
                        try {
                            tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                            tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
                        } catch (IOException e) {
                            logger.warn("parse fundBillList error", e);
                        }
                    }
                    List<Map<String, Object>> tradeFundBills = new ArrayList();
                    if (tradeFundBill instanceof List) {
                        tradeFundBills.addAll((List<Map<String, Object>>) tradeFundBill);
                    } else if (tradeFundBill instanceof Map) {
                        tradeFundBills.add((Map<String, Object>) tradeFundBill);
                    }
                    if (tradeFundBills.isEmpty()) {
                        return;
                    }
                    List<Map<String, Object>> payments = AlipayV2ServiceProvider.getAlipayV2Payments(tradeFundBills, totalAmount, discountChannelMchAmount);
                    BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
                    //设置花呗分期标志
                    if (payments != null) {
                        for (Map<String, Object> payment : payments) {
                            if (Payment.TYPE_CUSTOM_ALIPAY_HUABEI.equals(BeanUtil.getPropString(payment, Transaction.TYPE, ""))) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
                                if (extendedParams != null) {
                                    Object extendParamsObject = extendedParams.get(BusinessV2Fields.EXTEND_PARAMS);
                                    if ((extendParamsObject instanceof Map)) {
                                        @SuppressWarnings("unchecked")
                                        Map<String, Object> extendParams = (Map<String, Object>) extendParamsObject;
                                        if (extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM) && extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT)) {
                                            BeanUtil.setNestedProperty(transaction, AlipayV2ServiceProvider.TRANSACTION_CHANNEL_HB_FQ_PATH, true);
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    }

                    if (UpayUtil.isReturnProviderResponse(transaction)) {
                        Map<String, Object> extraOutFields = com.wosai.pantheon.util.MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
                        String discountGoodsDetail = com.wosai.pantheon.util.MapUtil.getString(channelDataMap, BusinessV2Fields.DISCOUNT_GOODS_DETAIL);
                        if (!StringUtils.empty(discountGoodsDetail)) {
                            extraOutFields.put(Transaction.GOODS_DETAILS, JsonUtil.jsonStrToObject(discountGoodsDetail, List.class));
                        }
                        extraOutFields.put(Transaction.VOUCHER_DETAILS, channelDataMap.get(BusinessV2Fields.VOUCHER_DETAIL_LIST));
                        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                    }
                } else {
                    Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                    List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                    String acctType = MapUtil.getString(sybResult, ResponseFields.ACCT_TYPE, "");
                    String payment = Payment.TYPE_WALLET_ALIPAY;
                    if (acctType.equals(SybConstants.ACCT_TYPE_DEBIT_VALUE)) {
                        payment = Payment.TYPE_BANKCARD_DEBIT;
                    } else if (acctType.equals(SybConstants.ACCT_TYPE_CREDIT_VALUE)) {
                        payment = Payment.TYPE_BANKCARD_CREDIT;
                    }
                    payments.add(
                            CollectionUtil.hashMap(Transaction.PAYMENT_TYPE, payment,
                                    Transaction.PAYMENT_ORIGIN_TYPE, acctType,
                                    Transaction.PAYMENT_AMOUNT, trxAmount)
                    );
                    if (discount != 0) {
                        payments.add(
                                CollectionUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                        Transaction.PAYMENT_ORIGIN_TYPE, null,
                                        Transaction.PAYMENT_AMOUNT, discount
                                )
                        );
                    }
                    if (payments == null || payments.isEmpty()) {
                        extraOutFields.put(Transaction.PAYMENTS, payments);
                    }
                }
            } catch (Exception e) {
                logger.warn("alipay pay payment set error", e);
            }
        });
        //支付完成微信的payment处理方式
        resolvePayFundMap.put(Payway.WEIXIN.getCode(), ((sybResult, context) -> {
            try {
                Map<String, Object> transaction = context.getTransaction();
                Map<String, Object> order = context.getOrder();
                String channelData = MapUtil.getString(sybResult, ResponseFields.CHANNEL_DATA);
                Map<String, Object> channelDataMap = null;
                if(!StringUtils.isEmpty(channelData)) {
                    channelDataMap = JacksonUtil.toBeanQuietly(channelData, Map.class);
                }
                //免充值升级后的接口返回与以前的不一样
                List<Map<String, Object>> promotions = null;
                if (channelDataMap != null && channelDataMap.get(com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL) != null && channelDataMap.get(com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL) instanceof String) {
                    String promotionDetailStr = (String) channelDataMap.get(com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL);
                    promotionDetailStr = promotionDetailStr.replaceAll("\\\\", "");
                    if (!StringUtils.isEmpty(promotionDetailStr)) {
                        promotions = JacksonUtil.toBeanQuietly(promotionDetailStr, List.class);
                        channelDataMap.remove(com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL);
                        BeanUtil.setNestedProperty(channelDataMap, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL, promotions);
                    }
                }
                if (channelDataMap != null && channelDataMap.containsKey(com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL) && promotions != null) {
                    if (UpayUtil.isReturnProviderResponse(context.getTransaction())) {
                        Map<String, Object> extraOutFields = com.wosai.pantheon.util.MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
                        extraOutFields.put(Transaction.VOUCHER_DETAILS, promotions);
                        // 代金券信息需要从优惠信息中获取
                        List<Map<String, Object>> goodsDetails = promotions
                                .stream()
                                .filter(promotion -> Objects.nonNull(promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL)) && promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL) instanceof List)
                                .map(promotion -> (List<Map<String, Object>>) promotion.get(com.wosai.mpay.api.weixin.BusinessFields.GOODS_DETAIL))
                                .flatMap(Collection::stream)
                                .collect(Collectors.toList());
                        extraOutFields.put(Transaction.GOODS_DETAILS, goodsDetails);
                        context.getTransaction().put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                    }
                    //取订单原金额
                    long totalFee = BeanUtil.getPropLong(sybResult, ResponseFields.INIT_AMT, 0);
                    if (promotions != null) {
                        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                        List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                        if (payments == null || payments.isEmpty()) {
                            extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(sybResult, channelDataMap));
                        }
                        long discountAmount = WeixinServiceProvider.getSumAmountOfPromotionDetail(promotions);
                        if (BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discountAmount != 0) {
                            order.put(Order.TOTAL_DISCOUNT, discountAmount);
                            order.put(Order.NET_DISCOUNT, discountAmount);
                        }
                        long paidAmount = totalFee - discountAmount;
                        if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0) {
                            transaction.put(Transaction.PAID_AMOUNT, paidAmount);
                        }
                        long receiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                        for (Map<String, Object> promotion : promotions) {
                            if (promotion.isEmpty()) {
                                continue;
                            }
                            String type = BeanUtil.getPropString(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_TYPE);
                            long amount = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_AMOUNT);
//                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                            long wxpayContribute = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                            long otherContribute = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                            //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                            if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)) {
                                receiveAmount = receiveAmount - (amount - wxpayContribute - otherContribute);

                            }
                        }
                        if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0) {
                            transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
                        }
                    }
                } else {
                    long totalFee = BeanUtil.getPropLong(sybResult, ResponseFields.INIT_AMT, 0);
                    long trxAmt = BeanUtil.getPropLong(sybResult, ResponseFields.TRX_AMT, 0);

                    long discount = totalFee - trxAmt;
                    transaction.put(Transaction.PAID_AMOUNT, trxAmt);
                    if (totalFee > 0) {
                        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee);
                    }
                    Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                    List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                    if (payments == null || payments.isEmpty()) {
                        extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(sybResult, channelDataMap));
                    }
                    if (BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discount != 0) {
                        order.put(Order.TOTAL_DISCOUNT, discount);
                        order.put(Order.NET_DISCOUNT, discount);
                    }
                }
            } catch (Exception e) {
                logger.warn("weixin pay payment set error", e);
            }
        }));
        //支付完成通联云闪付的payment处理方式
        resolvePayFundMap.put(Payway.UNIONPAY.getCode(), ((sybResult, context) -> {
            try {
                Map<String, Object> transaction = context.getTransaction();
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                long settlementAmount = BeanUtil.getPropLong(sybResult, ResponseFields.TRX_AMT);
                Map<String, Object> channelDataMap = MapUtil.getMap(sybResult, ResponseFields.CHANNEL_DATA);
                if (channelDataMap == null) {
                    String channelData = MapUtil.getString(sybResult, ResponseFields.CHANNEL_DATA);
                    if (!StringUtils.isEmpty(channelData)) {
                        channelDataMap = JacksonUtil.toBeanQuietly(channelData, Map.class);
                    }
                }
                extraOutFields.put(Transaction.SETTLEMENT_AMOUNT, settlementAmount);
                List<Map<String, Object>> payments = new ArrayList<>();
                List<Map<String, Object>> couponInfo = null;

                if (channelDataMap != null && channelDataMap.get(com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO) != null && channelDataMap.get(com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO) instanceof String) {
                    String couponInfoStr = (String) channelDataMap.get(com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO);
                    couponInfoStr = couponInfoStr.replaceAll("\\\\", "");
                    if (!StringUtils.isEmpty(couponInfoStr)) {
                        couponInfo = JacksonUtil.toBeanQuietly(couponInfoStr, List.class);
                        channelDataMap.remove(com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO);
                        BeanUtil.setNestedProperty(channelDataMap, com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO, couponInfo);
                    }
                }
                long couponSum = 0;
                if (couponInfo != null) {
                    for (Map<String, Object> coupon : couponInfo) {
                        String spnsrId = BeanUtil.getPropString(coupon, com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO_SPNSR_ID); //出资方
                        long amount = BeanUtil.getPropLong(coupon, com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO_OFFST_AMT);
                        String couponId = BeanUtil.getPropString(coupon, com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO_ID);
                        String couponType = BeanUtil.getPropString(coupon, com.wosai.mpay.api.unionpayopen.ResponseFields.COUPON_INFO_TYPE);
                        couponSum = couponSum + amount;
                        //注意银联接口返回不能准确的区分商户优惠是否是免充值，默认当做是免充值的优惠。如果后续此通道对接了微信交易，需要特别注意。
                        String paymentType;
                        if (UnionPayOpenConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)) {
                            paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                        } else {
                            paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                        }
                        payments.add(CollectionUtil.hashMap(
                                Transaction.PAYMENT_AMOUNT, amount,
                                Transaction.PAYMENT_SOURCE, couponId,
                                Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                                Transaction.PAYMENT_TYPE, paymentType
                        ));
                    }
                }
                long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                long amount = effectiveAmount - couponSum;
                if (amount > 0) {
                    String paymentType = null;
                    String acctType = MapUtil.getString(sybResult, ResponseFields.ACCT_TYPE);
                    if (!StringUtils.isEmpty(acctType)) {
                        if (SybConstants.ACCT_TYPE_CREDIT_VALUE.equals(acctType)) {
                            paymentType = Payment.TYPE_BANKCARD_CREDIT;
                        } else if (SybConstants.ACCT_TYPE_DEBIT_VALUE.equals(acctType)) {
                            paymentType = Payment.TYPE_BANKCARD_DEBIT;
                        } else {
                            paymentType = Payment.TYPE_OTHERS;
                        }
                        payments.add(CollectionUtil.hashMap(
                                Transaction.PAYMENT_AMOUNT, amount,
                                Transaction.PAYMENT_ORIGIN_TYPE, acctType,
                                Transaction.PAYMENT_TYPE, paymentType
                                )
                        );
                    }
                }
                List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                if (oldPayments == null || oldPayments.isEmpty()) {
                    if (payments != null && !payments.isEmpty()) {
                        extraOutFields.put(Transaction.PAYMENTS, payments);
                    }
                }
                if (!StringUtil.empty(BeanUtil.getPropString(sybResult, ResponseFields.TRX_AMT))) {
                    long payAmt = BeanUtil.getPropLong(sybResult, ResponseFields.TRX_AMT);
                    transaction.put(Transaction.PAID_AMOUNT, payAmt);
                }
            } catch (Exception e) {
                logger.warn("unionpay pay payment set error", e);
            }
        }));

        //退款成功支付宝的处理payment逻辑
        resolveRefundFundMap.put(Payway.ALIPAY2.getCode(), ((sybResult, context, payTransaction) -> {
            try {
                Map<String, Object> order = context.getOrder();
                Map<String, Object> transaction = context.getTransaction();
                if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
                    //全额退款
                    PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
                } else {
                    //部分退款，根据返回的refund_detail_item_list来计算
                    String channelData = MapUtil.getString(sybResult, ResponseFields.CHANNEL_DATA);
                    long refundAmount = com.wosai.pantheon.util.MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                    long receivedAmount = MapUtil.getLongValue(sybResult, ResponseFields.TRX_AMT, refundAmount);
                    long paidAmount = receivedAmount;
                    long mchDiscountAmount = refundAmount - receivedAmount;
                    Map<String, Object> channelDataMap = JacksonUtil.toBeanQuietly(channelData, Map.class);
                    if (channelDataMap != null && !channelDataMap.isEmpty()) {
                        Object tradeFundBill = BeanUtil.getNestedProperty(channelDataMap, BusinessV2Fields.REFUND_DETAIL_ITEM_LIST);
                        if (tradeFundBill instanceof String && !StringUtil.empty((String) tradeFundBill)) {
                            try {
                                tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                                tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
                            } catch (IOException e) {
                                logger.warn("parse refund_detail_item_list error", e);
                            }
                        }
                        List<Map<String, Object>> tradeFundBills = new ArrayList();
                        if (tradeFundBill instanceof List) {
                            tradeFundBills.addAll((List<Map<String, Object>>) tradeFundBill);
                        } else if (tradeFundBill instanceof Map) {
                            tradeFundBills.add((Map<String, Object>) tradeFundBill);
                        }
                        //如果有返回资金信息，使用资金信息里的金额做计算出消费者实际付的钱
                        if (!tradeFundBills.isEmpty()) {
                            long tmpPaidAmount = 0;
                            long tmpMchDiscountAmount = 0;
                            for (Map<String, Object> bill : tradeFundBills) {
                                String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL) ? BusinessV2Fields.FUND_CHANNEL : BusinessV2Fields.FUNDCHANNEL);
                                long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                                if (!AlipayV2ServiceProvider.consumerDiscount.contains(fundChannel)) {
                                    tmpPaidAmount += amount;
                                }
                                if(AlipayV2ServiceProvider.FC_MDISCOUNT.equals(fundChannel) || AlipayV2ServiceProvider.FC_TMARKETING.equals(fundChannel)){
                                    tmpMchDiscountAmount += amount;
                                }
                            }
                            paidAmount = tmpPaidAmount;
                            mchDiscountAmount = tmpMchDiscountAmount;
                            receivedAmount = refundAmount - mchDiscountAmount;
                        }
                        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, AlipayV2ServiceProvider.getAlipayV2Payments(tradeFundBills, refundAmount, mchDiscountAmount));
                    } else {
                        //如果没有返回渠道的信息 自己填
                        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                        List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
                        String acctType = MapUtil.getString(sybResult, ResponseFields.ACCT_TYPE, "");
                        String payment = Payment.TYPE_WALLET_ALIPAY;
                        if (acctType.equals(SybConstants.ACCT_TYPE_DEBIT_VALUE)) {
                            payment = Payment.TYPE_BANKCARD_DEBIT;
                        } else if (acctType.equals(SybConstants.ACCT_TYPE_CREDIT_VALUE)) {
                            payment = Payment.TYPE_BANKCARD_CREDIT;
                        }
                        payments.add(
                                CollectionUtil.hashMap(Transaction.PAYMENT_TYPE, payment,
                                        Transaction.PAYMENT_ORIGIN_TYPE, acctType,
                                        Transaction.PAYMENT_AMOUNT, receivedAmount)
                        );
                        if (payments == null || payments.isEmpty()) {
                            extraOutFields.put(Transaction.PAYMENTS, payments);
                        }
                    }
                    if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0) {
                        transaction.put(Transaction.PAID_AMOUNT, paidAmount);
                    }
                    if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0) {
                        transaction.put(Transaction.RECEIVED_AMOUNT, receivedAmount);
                    }
                }
            } catch (Exception e) {
                logger.warn("alipay refund payment set error", e);
            }
        }));
        //退款成功微信的处理payment逻辑
        resolveRefundFundMap.put(Payway.WEIXIN.getCode(), ((sybResult, context, payTransaction) -> {
            try {
                Map<String, Object> order = context.getOrder();
                Map<String, Object> transaction = context.getTransaction();
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                if (extraOutFields == null) {
                    extraOutFields = new HashMap<>();
                    transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                }
                if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
                    //全额退款
                    PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
                } else {
                    //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
                    List<Map<String, Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
                    long paidAmount = MapUtil.getLongValue(sybResult, ResponseFields.TRX_AMT);
                    if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0 && paidAmount != 0) {
                        transaction.put(Transaction.PAID_AMOUNT, paidAmount);
                    }

                    if (payments != null) {
                        List<Map<String, Object>> refundPayments = new ArrayList<>();
                        for (int i = 0; i < payments.size(); i++) {
                            Map<String, Object> refundPayment = (Map<String, Object>) ((HashMap) payments.get(i)).clone();
                            refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                            refundPayments.add(refundPayment);
                        }
                        String channelData = MapUtil.getString(sybResult, ResponseFields.CHANNEL_DATA);
                        Map<String, Object> channelDataMap = JacksonUtil.toBeanQuietly(channelData, Map.class);

                        //直连微信 会返回 coupon_refund_count coupon_xxx_$n 等字段, 但是网联银联不会返回，而是返回refund_details字段
                        if (channelDataMap != null && channelDataMap.containsKey(com.wosai.mpay.api.weixin.ResponseFields.REFUND_DETAILS)) {
                            Object refundDetailObj = channelDataMap.get(com.wosai.mpay.api.weixin.ResponseFields.REFUND_DETAILS);
                            List<Map<String, Object>> refundDetail = null;
                            if (refundDetailObj instanceof String) {
                                String refundDetailStr = (String) channelDataMap.get(com.wosai.mpay.api.weixin.ResponseFields.REFUND_DETAILS);
                                refundDetailStr = refundDetailStr.replaceAll("\\\\", "");
                                if (!StringUtils.isEmpty(refundDetailStr)) {
                                    refundDetail = JacksonUtil.toBeanQuietly(refundDetailStr, List.class);
                                    channelDataMap.remove(com.wosai.mpay.api.weixin.ResponseFields.REFUND_DETAILS);
                                    BeanUtil.setNestedProperty(channelDataMap, com.wosai.mpay.api.weixin.ResponseFields.RESPONSE_KEY_PROMOTION_DETAIL, refundDetail);
                                }
                            } else if (refundDetailObj instanceof List) {
                                refundDetail = (List<Map<String, Object>>) refundDetailObj;
                            }
                            if (refundDetail != null) {
                                for (int i = 0; i < refundDetail.size(); i++) {
                                    Map<String, Object> detail = refundDetail.get(i);
                                    String couponRefundId = BeanUtil.getPropString(detail, com.wosai.mpay.api.weixin.ResponseFields.REFUND_DETAILS_PROMOTION_ID);
                                    long couponRefundFee = BeanUtil.getPropLong(detail, com.wosai.mpay.api.weixin.ResponseFields.REFUND_DETAILS_REFUND_AMOUNT);
                                    for (int j = 0; j < refundPayments.size(); j++) {
                                        Map<String, Object> refundPayment = refundPayments.get(j);
                                        String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                                        if (sourceId.equals(couponRefundId)) {
                                            refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                                        }
                                    }
                                }
                            }
                        }

                        for (int j = 0; j < refundPayments.size(); j++) {
                            Map<String, Object> refundPayment = refundPayments.get(j);
                            String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                            if (!Payment.TYPE_DISCOUNT_SET.contains(type)) {
                                refundPayment.put(Transaction.PAYMENT_AMOUNT, paidAmount);
                                break;
                            }
                        }
                        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                        //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                        long settlementRefundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                        for (int j = 0; j < refundPayments.size(); j++) {
                            Map<String, Object> refundPayment = refundPayments.get(j);
                            String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                            if (Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)) {
                                settlementRefundFee = settlementRefundFee - BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT);
                            }
                        }
                        if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefundFee != 0) {
                            transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefundFee);
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("weixin refund payment set error", e);
            }
        }));
        //退款成功云闪付的处理payment逻辑
        resolveRefundFundMap.put(Payway.UNIONPAY.getCode(), ((sybResult, context, payTransaction) -> {
            try {
                Map<String, Object> transaction = context.getTransaction();
                //上送通道总额
                long effectiveTotal = BeanUtil.getPropLong(payTransaction, Transaction.EFFECTIVE_AMOUNT, 0);
                //本次退款总额
                long refundAmountTotal = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
                //退款比例
                double refundPercent = refundAmountTotal * 1.0 / effectiveTotal;
                if (payTransaction.get(Transaction.PAID_AMOUNT) != null) {
                    long payPaidAmount = BeanUtil.getPropLong(payTransaction, Transaction.PAID_AMOUNT);
                    long refundPaidAmount = Math.round(payPaidAmount * refundPercent);
                    transaction.put(Transaction.PAID_AMOUNT, refundPaidAmount);
                }
                if (payTransaction.get(Transaction.RECEIVED_AMOUNT) != null) {
                    long payReceivedAmount = BeanUtil.getPropLong(payTransaction, Transaction.RECEIVED_AMOUNT);
                    long refundReceivedAmount = Math.round(payReceivedAmount * refundPercent);
                    transaction.put(Transaction.RECEIVED_AMOUNT, refundReceivedAmount);
                }

                List<Map<String, Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
                if (CollectionUtils.isEmpty(payments)) {
                    return;
                }
                List<Map<String, Object>> refundPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
                if (refundPayments == null) {
                    refundPayments = new ArrayList<>();
                }
                long sumPaymentAmount = payments.stream().mapToLong(payment -> BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT)).sum();
                long remainRefundPaymentAmount = Math.round(sumPaymentAmount * refundPercent);
                for (int i = 0; i < payments.size(); i++) {
                    Map<String, Object> payment = payments.get(i);
                    long amount = BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
                    boolean isLastPayment = (i == payments.size() - 1);
                    long refundAmount = isLastPayment ? remainRefundPaymentAmount : Math.round(amount * refundPercent);
                    Map<String, Object> refundPayment = new HashMap<>(payment);
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, refundAmount);
                    refundPayments.add(refundPayment);
                    remainRefundPaymentAmount = remainRefundPaymentAmount - refundAmount;
                }

                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
            } catch (Exception e) {
                logger.warn("unionpay refund payment set error", e);
            }
        }));
    }

    public static List<Map<String, Object>> getWeixinPayments(Map<String, Object> sybResult, Map<String, Object> channelDataMap) {

        if (channelDataMap != null && channelDataMap.containsKey(com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL)) {
            long totalFee = BeanUtil.getPropLong(sybResult, ResponseFields.INIT_AMT, 0);
            long cashFee = totalFee - WeixinServiceProvider.getSumAmountOfPromotionDetail((List<Map<String, Object>>) BeanUtil.getNestedProperty(channelDataMap, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL));
            String acctType = BeanUtil.getPropString(sybResult, ResponseFields.ACCT_TYPE);
            String bankType = WX_ACCT_TYPE_MAPPING.getOrDefault(acctType, acctType);
            List<Map<String, Object>> payments = new ArrayList<>();
            Map<String, Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(bankType, cashFee);
            if (payment != null) {
                payments.add(payment);
            }
            List<Map<String, Object>> promotions = (List<Map<String, Object>>) BeanUtil.getNestedProperty(channelDataMap, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL);
            if (promotions != null) {
                for (Map<String, Object> promotion : promotions) {
                    if (promotion.isEmpty()) {
                        continue;
                    }
                    String type = BeanUtil.getPropString(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_TYPE);
                    String promotionId = BeanUtil.getPropString(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                    long amount = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_AMOUNT);
//                    long merchantContribute = BeanUtil.getPropLong(promotion, ResponseFields.PROMOTION_DETAIL_MERCHANT_CONTRIBUTE);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    long channelAmount = wxpayContribute + otherContribute;
                    long mchAmount = amount - channelAmount;
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)) {
                        if (mchAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        } else if (channelAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }
                    } else if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)) {
                        if (mchAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        } else if (channelAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }

                    }
                }
            }
            return payments;
        } else {
            long totalFee = BeanUtil.getPropLong(sybResult, ResponseFields.INIT_AMT, 0);
            long trxAmt = BeanUtil.getPropLong(sybResult, ResponseFields.TRX_AMT, 0);
            long discount = totalFee - trxAmt;
            String acctType = BeanUtil.getPropString(sybResult, ResponseFields.ACCT_TYPE);
            String bankType = WX_ACCT_TYPE_MAPPING.getOrDefault(acctType, acctType);
            List<Map<String, Object>> payments = new ArrayList<>();
            Map<String, Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(bankType, trxAmt);
            if (payment != null) {
                payments.add(payment);
            }
            if (discount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }
            return payments;
        }

    }


    /**
     * 设置借贷标识
     *
     * @param builder
     * @param transaction
     */
    private void limitCredit(RequestBuilder builder, Map transaction) {
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))) {
            int payWay = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            // 该字段暂时只对微信支付和支付宝有效
            if (Order.PAYWAY_ALIPAY == payWay || Order.PAYWAY_ALIPAY2 == payWay || Order.PAYWAY_WEIXIN == payWay) {
                builder.set(BusinessFields.LIMIT_PAY, FuyouConstants.LIMIT_PAY_NO_CREDIT);
            }
        }
    }
}
