package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.alipay.overseas.*;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class AlipayOverseasServiceProvider extends AbstractServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(AlipayOverseasServiceProvider.class);
    public static final String NAME = "provider.alipay.overseas";
    private static final String RESPONSE_ALIPAY = String.format("%s.%s", ResponseFields.RESPONSE, ResponseFields.ALIPAY);
    private SafeSimpleDateFormat wapDateFormat = new SafeSimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);

    @Autowired
    private AlipayOverseasClient client;

    protected String notifyHost;


    private String defaultTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE + "m";  //C扫B订单自动关闭时间
    private String b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE + "m";         //B扫C订单自动关闭时间
    
    @Override
    public String getName() {
        return NAME;
    }
    
    public AlipayOverseasServiceProvider(){
        //支付宝境外支付接口返回的alipay_pay_time格式为yyyyMMddhhmmss
        this.dateFormat = new SafeSimpleDateFormat(AlipayOverseasConstants.DATE_TIME_FORMAT);
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Order.PAYWAY_ALIPAY == MapUtil.getIntValue(transaction, Transaction.PAYWAY)) {
            if (getTradeParams(transaction) == null) {
                return false;
            }
            String currency = getTradeCurrency(transaction);
            return !TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency);
        }
        return false;
    }

    @Override
    public Map<String,Object> getTradeParams(Map<String, Object> transaction){
        String tradeParamsKey;
        switch (BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY)) {
            case Order.SUB_PAYWAY_BARCODE:
            case Order.SUB_PAYWAY_QRCODE:
                tradeParamsKey = TransactionParam.ALIPAY_V1_TRADE_PARAMS;
                break;
            default:
                tradeParamsKey = TransactionParam.ALIPAY_WAP_TRADE_PARAMS;
        }
        return getTradeParams(transaction, tradeParamsKey);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE, AlipayOverseasConstants.SERVICE_NAME_PAY_BARCODE);
        String partnerId = BeanUtil.getPropString(config, TransactionParam.PARTNER);
        if (!StringUtil.empty(partnerId)) {
            builder.set(BusinessFields.ALIPAY_SELLER_ID, partnerId);
        }
        
        builder.set(BusinessFields.TRANS_NAME, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.PARTNER_TRANS_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.CURRENCY, getTradeCurrency(transaction));
        builder.set(BusinessFields.TRANS_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessFields.BUYER_IDENTITY_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        builder.set(BusinessFields.IDENTITY_CODE_TYPE, AlipayOverseasConstants.IDENTITY_CODE_TYPE_BARCODE);
        builder.set(BusinessFields.BIZ_PRODUCT, AlipayOverseasConstants.BIZ_PRODUCT_OVERSEAS_MBARCODE_PAY);
        builder.set(BusinessFields.IT_B_PAY, b2cTimeoutExpress);
        
        //todo 服务商自己创建和管理的二级商户 待完善
        Map<String, Object> configSnapShot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.extendInfoSet(BusinessFields.SECONDARY_MCH_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.MERCHANT_SN));
        builder.extendInfoSet(BusinessFields.SECONDARY_MCH_NAME, BeanUtil.getPropString(configSnapShot, TransactionParam.MERCHANT_NAME));
        builder.extendInfoSet(BusinessFields.SECONDARY_MCH_INDUSTRY, BeanUtil.getPropString(config, TransactionParam.MERCHANT_ALIPAY_INDUSTRY,TransactionParam.MERCHANT_DEFAULT_ALIPAY_INDUSTRY));
        builder.extendInfoSet(BusinessFields.STORE_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_CLIENT_SN, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_SN)));
        builder.extendInfoSet(BusinessFields.STORE_NAME, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_NAME));
        builder.extendInfoSet(BusinessFields.TERMINAL_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.TERMINAL_SN));

        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay overseas request builder error ", e);
        }
        try {
            result = retryIfNetworkException(OP_PAY, config, request, 1);
        } catch (Exception ex) {
            logger.error("failed to call alipay overseas pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if (result == null) {
            return Workflow.RC_IN_PROG;
        }
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            //业务请求失败
            return Workflow.RC_ERROR;
        } else {
            if (AlipayOverseasConstants.RESP_BIZ_CODE_UNKNOW.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为未知
                return Workflow.RC_IN_PROG;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_FAIL.equalsIgnoreCase(resultCode) || AlipayOverseasConstants.RESP_BIZ_CODE_FAILED.equalsIgnoreCase(resultCode)) {
                String errorCode = BeanUtil.getPropString(response, ResponseFields.ERROR);
                //业务请求成功, result_code 为失败
                if(AlipayOverseasConstants.PROTOCAL_ERR_CODE_LISTS.contains(errorCode) || AlipayOverseasConstants.PAY_FAIL_ERR_CODE_LISTS.contains(errorCode)) {
                    return Workflow.RC_TRADE_CANCELED;
                }
                return Workflow.RC_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_SUCCESS.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为成功 付款成功
                setTradeRespInfo2Trans(response, transaction);
                logger.debug("  transaction   {}", transaction);
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }
    
    private void setTradeRespInfo2Trans(Map<String, Object> result, Map<String, Object> transaction) {
        transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(result, ResponseFields.ALIPAY_BUYER_USER_ID));
        transaction.put(Transaction.TRADE_NO, BeanUtil.getPropString(result, ResponseFields.ALIPAY_TRANS_ID));
        transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(result, ResponseFields.ALIPAY_BUYER_LOGIN_ID));
        transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, ResponseFields.ALIPAY_PAY_TIME)));
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        String exchangeRate = BeanUtil.getPropString(result, ResponseFields.EXCHANGE_RATE);
        String transAmountCNY = BeanUtil.getPropString(result, ResponseFields.TRANS_AMOUNT_CNY);
        String refundAmountCNY = BeanUtil.getPropString(result, ResponseFields.REFUND_AMOUNT_CNY);
        String paymentInst = BeanUtil.getPropString(result, ResponseFields.PAYMENT_INST);
        if(overseas.get(TransactionParam.CURRENCY)==null){
            String currency = getCurrencyByPaymentInst(paymentInst);
            overseas.put(TransactionParam.CURRENCY, currency);
        }
        if (!StringUtil.empty(exchangeRate)) {
            overseas.put(TransactionParam.EXCHANGE_RATE, exchangeRate);
        }
        if (!StringUtil.empty(transAmountCNY)) {
            overseas.put(TransactionParam.TRANS_AMOUNT_CNY, transAmountCNY);
        }
        if (!StringUtil.empty(refundAmountCNY)) {
            overseas.put(TransactionParam.REFUND_AMOUNT_CNY, refundAmountCNY);
        }
        if (!StringUtil.empty(paymentInst)) {
            overseas.put(TransactionParam.PAYMENT_INST, paymentInst);
        }
    }

    private String getCurrencyByPaymentInst(String paymentInst){
        if(BusinessFields.PAYMENT_INST_ALIPAYCN.equals(paymentInst)){
            return  TransactionParam.UPAY_DEFAULT_CURRENCY_CNY;
        }else if(BusinessFields.PAYMENT_INST_ALIPAYHK.equals(paymentInst)){
            return TransactionParam.UPAY_CURRENCY_HKD;
        }
        return null;
    }
    
    protected Map<String, Object> retryIfNetworkException(String op, Map<String, Object> tradeParams, Map<String, String> request, int retryTimes) throws Exception {
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), op);
        String appId = MapUtil.getString(tradeParams, TransactionParam.PARTNER);
        String signType = MapUtil.getString(tradeParams, TransactionParam.SIGN_TYPE, AlipayOverseasConstants.SIGN_TYPE_MD5);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.APP_KEY);
        if (AlipayOverseasConstants.SIGN_TYPE_RSA2.equals(signType) || AlipayOverseasConstants.SIGN_TYPE_RSA.equals(signType)){
            appKey = getPrivateKeyContent(appKey);
        }
        Exception exception = null;
        for (int i = 0; i < retryTimes; ++i) {
            try {
                return client.call(gatewayUrl, appId, signType, appKey, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in alipay overseas {}", op, ex);
            }
        }
        logger.error("still network i/o error after retrying {} times.", retryTimes);
        throw exception;
    }
    
    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE, AlipayOverseasConstants.SERVICE_NAME_CANCEL);
        builder.set(BusinessFields.TIMESTAMP, new Date().getTime() + "");
        builder.set(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        
        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay overseas request builder error ", e);
        }
        try {
            result = retryIfNetworkException(OP_CANCEL, config, request, 3);
        } catch (Exception ex) {
            logger.error("failed to call alipay overseas cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_RETRY;
        }
        
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            //业务请求失败
            return Workflow.RC_ERROR;
        } else {
            if (AlipayOverseasConstants.RESP_BIZ_CODE_UNKNOW.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为未知。进行重试，如果重试多次还是unknow联系支付宝。
                return Workflow.RC_RETRY;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_FAIL.equalsIgnoreCase(resultCode) || AlipayOverseasConstants.RESP_BIZ_CODE_FAILED.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为失败
                return Workflow.RC_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_SUCCESS.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为成功 撤单成功
                return Workflow.RC_CANCEL_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }
    
    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE, AlipayOverseasConstants.SERVICE_NAME_QUERY);
        builder.set(BusinessFields.PARTNER_TRANS_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        
        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay overseas request builder error ", e);
        }
        try {
            result = retryIfNetworkException(OP_QUERY, config, request, 3);
        } catch (Exception ex) {
            logger.error("failed to call alipay overseas query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            //业务请求失败
            return Workflow.RC_ERROR;
        } else {
            if (AlipayOverseasConstants.RESP_BIZ_CODE_UNKNOW.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为未知
                return Workflow.RC_IN_PROG;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_FAIL.equalsIgnoreCase(resultCode) || AlipayOverseasConstants.RESP_BIZ_CODE_FAILED.equalsIgnoreCase(resultCode)) {
            	// 预下单创建的订单是在用户扫码之后创建的，在创建之前返回“TRADE_NOT_EXIST”
            	String detailErrorCode = BeanUtil.getPropString(response, ResponseFields.DETAIL_ERROR_CODE);
            	if(BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_QRCODE 
            			&& AlipayOverseasConstants.RESP_QUERY_ERROR_CODE_TRADE_NOT_EXIST.equalsIgnoreCase(detailErrorCode)){
            		return Workflow.RC_IN_PROG;
                }
                //业务请求成功, result_code 为失败
                return Workflow.RC_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_SUCCESS.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为成功 查询成功
                String transStatus = BeanUtil.getPropString(response, ResponseFields.ALIPAY_TRANS_STATUS);
                if (AlipayOverseasConstants.TRANS_STATUS_WAIT_BUYER_PAY.equals(transStatus)) {
                    return Workflow.RC_IN_PROG;
                } else if (AlipayOverseasConstants.TRANS_STATUS_TRADE_SUCCESS.equals(transStatus)) {
                    setTradeRespInfo2Trans(response, transaction);
                    return Workflow.RC_PAY_SUCCESS;
                } else if (AlipayOverseasConstants.TRANS_STATUS_TRADE_CLOSED.equals(transStatus)) {
                    return Workflow.RC_ERROR;
                }
            }
        }
        return Workflow.RC_ERROR;
    }
    
    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE, AlipayOverseasConstants.SERVICE_NAME_REFUND);
        builder.set(BusinessFields.PARTNER_TRANS_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.PARTNER_REFUND_TRANS_ID, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessFields.REFUND_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessFields.CURRENCY, getTradeCurrency(transaction));
        builder.set(BusinessFields.REFUND_IS_SYNC, AlipayOverseasConstants.REFUND_IS_SYNC_YES);
        
        Map<String, String> request = null;
        Map<String, Object> result = null;
        
        try {
            request = builder.build();
            result = retryIfNetworkException(OP_REFUND, config, request, 3);
        } catch (Exception ex) {
            logger.error("failed to call alipay overseas refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常重试
            return Workflow.RC_RETRY;
        }
        
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            //业务请求失败
            return Workflow.RC_ERROR;
        } else {
            if (AlipayOverseasConstants.RESP_BIZ_CODE_UNKNOW.equalsIgnoreCase(resultCode)) {
                //业务请求成功, 返回结果 result_code 为未知。 用相同的refund_id，特别是部分退款的情况下进行重试。
                return Workflow.RC_RETRY;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_FAIL.equalsIgnoreCase(resultCode) || AlipayOverseasConstants.RESP_BIZ_CODE_FAILED.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为失败
                return Workflow.RC_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_SUCCESS.equalsIgnoreCase(resultCode)) {
                setTradeRespInfo2Trans(response, transaction);
                return Workflow.RC_REFUND_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }
    
    /**
     *
     * @param context
     * @param resume
     * @return
     */
    @Override
    public String precreate(TransactionContext context, boolean resume) {
        int subPayway = com.wosai.pantheon.util.MapUtil.getIntValue(context.getTransaction(), Transaction.SUB_PAYWAY);
        return subPayway == Order.SUB_PAYWAY_QRCODE ? c2bPrecreate(context, resume) : wapPrecreate(context, resume);
    }

    /**
    *
    * @param context
    * @param resume
    * @return
    */
    private String c2bPrecreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE, AlipayOverseasConstants.SERVICE_NAME_PAY_PRECREATE);
    
        String notifyUrl = getNotifyUrl(notifyHost,context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        builder.set(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.TIMESTAMP, System.currentTimeMillis()+"");
        builder.set(BusinessFields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT, ""));
        builder.set(BusinessFields.PRODUCT_CODE, AlipayOverseasConstants.OVERSEAS_MBARCODE_PAY);
        builder.set(BusinessFields.TOTAL_FEE, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessFields.CURRENCY, getTradeCurrency(transaction));
        builder.set(BusinessFields.TRANS_CURRENCY, getTradeCurrency(transaction));
        builder.set(BusinessFields.IT_B_PAY, defaultTimeoutExpress);
    
        //todo 服务商自己创建和管理的二级商户 待完善
        Map<String, Object> configSnapShot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.MERCHANT_SN));
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_NAME, BeanUtil.getPropString(configSnapShot, TransactionParam.MERCHANT_NAME));
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_INDUSTRY, BeanUtil.getPropString(config, TransactionParam.MERCHANT_ALIPAY_INDUSTRY,TransactionParam.MERCHANT_DEFAULT_ALIPAY_INDUSTRY));
        builder.extendParamsSet(BusinessFields.STORE_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_SN));
        builder.extendParamsSet(BusinessFields.STORE_NAME, BeanUtil.getPropString(configSnapShot, TransactionParam.STORE_NAME));
        builder.extendParamsSet(BusinessFields.TERMINAL_ID, BeanUtil.getPropString(configSnapShot, TransactionParam.TERMINAL_SN));
        // 境外商户的系统服务商ID可以为空，后台会自动根据商户进行匹配
        builder.extendParamsSet(BusinessFields.SYS_SERVICE_PROVIDER_ID, "");
        
        builder.set(BusinessFields.BIZ_TYPE, AlipayOverseasConstants.BIZ_TYPE_OVERSEA_SHOP_QRCODE);
        
        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay overseas request builder error ", e);
        }
        try {
    		result = retryIfNetworkException(OP_PRECREATE, config, request, 1);
        } catch (Exception ex) {
            logger.error("failed to call alipay overseas pay", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            //业务请求失败
            return Workflow.RC_ERROR;
        } else {
            if (AlipayOverseasConstants.RESP_BIZ_CODE_UNKNOW.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为未知
                return Workflow.RC_SYS_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_FAIL.equalsIgnoreCase(resultCode) || AlipayOverseasConstants.RESP_BIZ_CODE_FAILED.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为失败
                return Workflow.RC_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_SUCCESS.equalsIgnoreCase(resultCode)) {
                
                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                extraOutFields.put(Transaction.QRCODE, response.get(BusinessFields.QR_CODE));
                return Workflow.RC_CREATE_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    private String wapPrecreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE, AlipayOverseasConstants.SERVICE_NAME_ACQUIRE_CREATE);
        builder.set(ProtocolFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        builder.set(ProtocolFields.TIMESTAMP, wapDateFormat.format(new Date()));
        builder.set(BusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessFields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.PRODUCT_CODE, AlipayOverseasConstants.BIZ_PRODUCT_OVERSEAS_MBARCODE_PAY);
        builder.set(BusinessFields.TOTAL_FEE, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessFields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        builder.set(BusinessFields.CURRENCY, getTradeCurrency(transaction));
        builder.set(BusinessFields.TRANS_CURRENCY, getTradeCurrency(transaction));
        builder.set(BusinessFields.IT_B_PAY, defaultTimeoutExpress);
        builder.set(BusinessFields.BUYER_ID, BeanUtil.getPropString(transaction.get(Transaction.EXTRA_PARAMS), Transaction.PAYER_UID));

        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_ID, BeanUtil.getPropString(configSnapshot, TransactionParam.MERCHANT_SN));
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_NAME, BeanUtil.getPropString(configSnapshot, TransactionParam.MERCHANT_NAME));
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_INDUSTRY, BeanUtil.getPropString(config, TransactionParam.MERCHANT_ALIPAY_INDUSTRY,TransactionParam.MERCHANT_DEFAULT_ALIPAY_INDUSTRY));
        builder.extendParamsSet(BusinessFields.STORE_ID, BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_SN));
        builder.extendParamsSet(BusinessFields.STORE_NAME, BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_NAME));
        builder.extendParamsSet(BusinessFields.TERMINAL_ID, BeanUtil.getPropString(configSnapshot, TransactionParam.TERMINAL_SN));

        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipay overseas request builder error ", e);
        }
        try {
            result = retryIfNetworkException(OP_WAP, config, request, 1);
        } catch (Exception ex) {
            logger.error("failed to call alipay overseas wap", ex);
            setTransactionContextErrorInfo(context, OP_WAP, ex);
            return Workflow.RC_IOEX;
        }
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        setTransactionContextErrorInfo(result, context, OP_WAP);
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            //业务请求失败
            return Workflow.RC_ERROR;
        } else {
            if (AlipayOverseasConstants.RESP_BIZ_CODE_UNKNOW.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为未知
                return Workflow.RC_SYS_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_FAIL.equalsIgnoreCase(resultCode) || AlipayOverseasConstants.RESP_BIZ_CODE_FAILED.equalsIgnoreCase(resultCode)) {
                //业务请求成功, result_code 为失败
                return Workflow.RC_ERROR;
            } else if (AlipayOverseasConstants.RESP_BIZ_CODE_SUCCESS.equalsIgnoreCase(resultCode)) {
                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(WapV2Fields.TRADE_NO, response.get(BusinessFields.TRADE_NO));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                return Workflow.RC_CREATE_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

   /**
     *
     * @param providerNotification
     * @return
     */
    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return query(context);
    }
    
    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String isSuccess = BeanUtil.getPropString(result, ResponseFields.IS_SUCCESS);
        Map response = (Map) BeanUtil.getNestedProperty(result, RESPONSE_ALIPAY);
        String resultCode = BeanUtil.getPropString(response, ResponseFields.RESULT_CODE);
        String errorCode = null;
        
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.IS_SUCCESS, isSuccess);
        if (AlipayOverseasConstants.RESP_CODE_FAIL.equalsIgnoreCase(isSuccess)) {
            errorCode = BeanUtil.getPropString(result, ResponseFields.ERROR);
            map.put(ResponseFields.ERROR, errorCode);
        } else {
            errorCode = BeanUtil.getPropString(response, ResponseFields.ERROR);
            map.put(ResponseFields.ERROR, errorCode);
        }
        map.put(ResponseFields.RESULT_CODE, resultCode);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, StringUtil.empty(errorCode), errorCode, "");
    }
    
    private boolean isTradeNotExist(String errorCode) {
        if (AlipayOverseasConstants.RESP_CANCEL_ERROR_CODE_TRADE_NOT_EXIST.equals(errorCode)
                    || AlipayOverseasConstants.RESP_QUERY_ERROR_CODE_TRADE_NOT_EXIST.equals(errorCode)
                    || AlipayOverseasConstants.RESP_REFUND_ERROR_CODE_TRADE_NOT_EXIST.equals(errorCode)) {
            return true;
        }
        return false;
    }
    
    private boolean isRefundAmountNotValid(String errorCode) {
        if (AlipayOverseasConstants.RESP_REFUND_ERROR_CODE_REFUND_AMT_RESTRICTION.equals(errorCode) || AlipayOverseasConstants.RESP_REFUND_ERROR_CODE_REQUEST_AMOUNT_EXCEED.equals(errorCode)) {
            return true;
        }
        return false;
    }
    
    private boolean isBarcodeInvalid(String errorde) {
        if (AlipayOverseasConstants.RESP_PAY_ERROR_CODE_SOUNDWAVE_PARSER_FAIL.equals(errorde) || AlipayOverseasConstants.RESP_PAY_ERROR_CODE_BUYER_NOT_EXIST.equals(errorde)) {
            return true;
        }
        return false;
    }
    
    private boolean isBuyerBalanceNotEnough(String errorCode) {
        if (AlipayOverseasConstants.RESP_PAY_ERROR_CODE_BUYER_BALANCE_NOT_ENOUGH.equals(errorCode) || AlipayOverseasConstants.RESP_PAY_ERROR_CODE_BUYER_BANKCARD_BALANCE_NOT_ENOUGH.equals(errorCode)) {
            return true;
        }
        return false;
    }
    
    private boolean isOperationHasNoPrivilege(String errorCode) {
        if (AlipayOverseasConstants.RESP_ERROR_CODE_HAS_NO_PRIVILEGE.equals(errorCode)) {
            return true;
        }
        return false;
    }
    
    private boolean isBuyerPaymentOverLimit(String errorCode) {
        if (AlipayOverseasConstants.RESP_PAY_ERROR_CODE_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR.equals(errorCode)
                    || AlipayOverseasConstants.RESP_PAY_ERROR_CODE_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR.equals(errorCode)
                    || AlipayOverseasConstants.RESP_PAY_ERROR_CODE_TOTAL_FEE_EXCEED.equals(errorCode)) {
            return true;
        }
        return false;
    }
    
    private boolean isSellerBalanceNotEnough(String errorCode) {
        if (AlipayOverseasConstants.RESP_REFUND_ERROR_CODE_SELLER_BALANCE_NOT_ENOUGH.equals(errorCode)) {
            return true;
        }
        return false;
    }
    

    
    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

}