package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 
 * 银联微信支付,与UnionPayWeixinServiceProvider不同之处在于此provider直连银联微信，不走拉卡拉前置
 * 
 */
@ServiceProvicerPriority(priority = 1)
public class LklUnionPayWeixinServiceProvider extends UnionPayWeixinServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(LklUnionPayWeixinServiceProvider.class);
    public static final String NAME = "provider.lkl.unionpay.weixin";
    private static final String DEFAULT_CERT_ID = "**********";


    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LAKALA_UNION_PAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE)) ? true : false;
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHANNEL_ID, config.get(TransactionParam.LAKALA_UNION_PAY_CHANNEL_ID));
        builder.set(ProtocolFields.SIGN_TYPE, MapUtil.getString(config, TransactionParam.SIGN_TYPE, WeixinConstants.SIGN_TYPE_RSA2));
        builder.set(ProtocolFields.CERT_ID, MapUtil.getString(config, TransactionParam.LAKALA_UNION_PAY_CERT_ID, DEFAULT_CERT_ID));
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.LAKALA_UNION_PAY_WEIXIN_SUB_MCH_ID));

        setTerminalInfo(context, MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), config, builder);
        return builder;
    }
    
    @Override
    public Map<String, Object> call(Map<String, Object>  config, String serviceUrl, Map<String, Object> request, String logFlag) throws MpayException, MpayApiNetworkError {
        removeIllegalFields(request);
        if(ApolloConfigurationCenterUtil.GATEWAY_OP_WXPAYFACEAUTHINFO.equals(logFlag)) {
            return client.call(serviceUrl, WeixinClient.SIGN_TYPE_WEIXIN, (String)config.get(TransactionParam.WEIXIN_APP_KEY), null, request);
        }else {
            return client.call(serviceUrl, MapUtil.getString(config, TransactionParam.SIGN_TYPE, WeixinClient.SIGN_TYPE_UNIONPAY), getPrivateKeyContent((String)config.get(TransactionParam.LAKALA_UNION_PAY_PRIVATE_KEY)), null, request);
        }
    }
}
