package com.wosai.upay.workflow;

import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.NuccBestPay.NuccBestPayBusinessFields;
import com.wosai.mpay.api.NuccBestPay.NuccBestPayClient;
import com.wosai.mpay.api.NuccBestPay.NuccBestPayConstants;
import com.wosai.mpay.api.NuccBestPay.NuccBestPayResponseFields;
import com.wosai.mpay.api.bestpay.BestpayClient;
import com.wosai.mpay.api.bestpay.BestpayConstants;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.BestpaySignature;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.UUIDGenerator;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

@ServiceProvicerPriority(priority = 1)
public class NuccBestPayServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(NuccBestPayServiceProvider.class);

    public static final String NAME = "provider.nucc.bestpay";

    protected int retryTimes = 3;

    protected String notifyHost;

    private static final String EXTRA_OUT_NUCC_BESTPAY_BATCH_ID = String.format("%s.%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.NUCC_BESTPAY, NuccBestPayResponseFields.BATCH_ID);


    @Autowired
    protected ExternalServiceFacade facade;


    @Autowired
    private NuccBestPayClient nuccBestPayClient;


    public NuccBestPayServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(NuccBestPayConstants.DATE_TIME_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }


    private Map<String,Object> call(Map<String, Object> config, String serviceUrl, String msgTy, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
         String privateKey = getPrivateKeyContent(config);
         String signSN = BeanUtil.getPropString(config, TransactionParam.NUCC_PAY_BESTPAY_SIGN_SN);
         return nuccBestPayClient.call(serviceUrl, msgTy, privateKey, signSN, null, request);
    }


    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return (payWay == Order.PAYWAY_BESTPAY) ? true : false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.NUCC_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(NuccBestPayBusinessFields.TRX_DT_TM, formatTimeString((Long)order.get(DaoConstants.CTIME)));
        builder.set(NuccBestPayBusinessFields.IDC_FLAG, BeanUtil.getPropString(config, TransactionParam.NUCC_IDC_FLAG, "20"));
        builder.set(NuccBestPayBusinessFields.TRX_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(NuccBestPayBusinessFields.BAR_CD, extraParams.get(Transaction.BARCODE));
        builder.set(NuccBestPayBusinessFields.TRX_AMT, getTradeCurrency(transaction) + StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(NuccBestPayBusinessFields.DESCRIPTION, transaction.get(Transaction.SUBJECT)+"");
        builder.set(NuccBestPayBusinessFields.TRX_TYPE_CD, NuccBestPayConstants.SUB_PAYWAY_BARCODE);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(NuccBestPayBusinessFields.NOTIFY_URL, notifyUrl);
        }
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.set(NuccBestPayBusinessFields.SCENE_INFO
                , CollectionUtil.hashMap(NuccBestPayBusinessFields.STORE_ID, configSnapshot.get(TransactionParam.STORE_SN)
                        , NuccBestPayBusinessFields.OPERATOR_ID, configSnapshot.get(TransactionParam.TERMINAL_SN)
                        , NuccBestPayBusinessFields.DEVICE_ID, configSnapshot.get(TransactionParam.TERMINAL_SN))
                );
        builder.set(NuccBestPayBusinessFields.ACCEPT_EXTENSION,"<GoodsInfo>" + transaction.get(Transaction.SUBJECT)+"</GoodsInfo>");

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, NuccBestPayConstants.PAY, builder.build(), retryTimes, OP_PAY);

        } catch (Exception ex) {
            logger.error("failed to call nucc best pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String sysRtnCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.SYS_RTN_CD);
        String bizStsCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.BIZ_STS_CD);
        String batchId = BeanUtil.getPropString(result, NuccBestPayResponseFields.BATCH_ID);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setBatchId(transaction, batchId);
        setTradeNoBuyerInfoIfExists(result, context);
        if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd) && NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(bizStsCd)){
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, NuccBestPayResponseFields.TRX_FINISH_TM)));
            return Workflow.RC_PAY_SUCCESS;
        } else if (NuccBestPayConstants.BIZ_STATE_IN_PROG.equals(bizStsCd)
                || NuccBestPayConstants.BIZ_STATE_UN_KNOW.equals(bizStsCd)
                || NuccBestPayConstants.BIZ_TRANS_NO_ALREADY_EXISTS.equals(bizStsCd)) {
            return Workflow.RC_IN_PROG;
        }else if(NuccBestPayConstants.PAY_FAIL_SYS_RETURN_CODE.contains(sysRtnCd) || NuccBestPayConstants.PAY_FAIL_BIZ_RETURN_CODE.contains(bizStsCd)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_ERROR;
    }

    private void setBatchId(Map<String, Object> transaction, String batchId) {
        if(StringUtils.isEmpty(batchId)){
            return;
        }
        Map<String, Object> payExtraOutFields = (Map<String, Object>)transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(CollectionUtils.isEmpty(payExtraOutFields)){
            payExtraOutFields = Maps.newConcurrentMap();
        }
        payExtraOutFields.put(Transaction.NUCC_BESTPAY, CollectionUtil.hashMap(NuccBestPayResponseFields.BATCH_ID, batchId));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, payExtraOutFields);
    }


    public static void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        if(!CollectionUtils.isEmpty(result)){
            String tradeNo = BeanUtil.getPropString(result, NuccBestPayResponseFields.TRX_ID);
            if(StringUtils.isEmpty(tradeNo)){
                tradeNo = BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_TRX_ID);
            }
            if(!StringUtil.empty(tradeNo)){
                Map<String,Object> order = context.getOrder();
                Map<String, Object> transaction = context.getTransaction();
                if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                    transaction.put(Transaction.TRADE_NO, tradeNo);
                }
                if(StringUtil.empty(BeanUtil.getPropString(order, Order.TRADE_NO))){
                    order.put(Order.TRADE_NO, tradeNo);
                }
            }
        }


    }


    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String returnCode = (String)result.get(NuccBestPayResponseFields.SYS_RTN_CD);//返回状态码
        String returnMsg = (String)result.get(NuccBestPayResponseFields.SYS_RTN_DESC);
        String resultCode = (String)result.get(NuccBestPayResponseFields.BIZ_STS_CD);//业务结果
        String resultDes = (String)result.get(NuccBestPayResponseFields.BIZ_STS_DESC); //错误代码
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(NuccBestPayResponseFields.SYS_RTN_CD, returnCode);
        map.put(NuccBestPayResponseFields.SYS_RTN_DESC, returnMsg);
        map.put(NuccBestPayResponseFields.BIZ_STS_CD, resultCode);
        map.put(NuccBestPayResponseFields.BIZ_STS_DESC, resultDes);
        
        boolean isSuccess = NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(returnCode) 
                && (NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(resultCode) || NuccBestPayConstants.BIZ_STATE_IN_PROG.equals(resultCode));
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, StringUtil.empty(resultCode) ? returnCode: resultCode, StringUtil.empty(resultDes) ? returnMsg : resultDes);
    }


    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        transaction.put(Transaction.RECEIVED_AMOUNT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0));

    }

    protected Map<String, Object> retryIfNetworkException(Map<String,Object> config, String url, String msgTy, Map<String,Object> request, int times, String logFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                return call(config, url, msgTy, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in nucc bestpay {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        // 翼支付接口的撤单接口只能将异常订单进行关闭，无法做到对正常订单进行撤单/退款。
        if(BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_PAID) {
            String result = refund(context);
            return Workflow.RC_REFUND_SUCCESS.equals(result) ? Workflow.RC_CANCEL_SUCCESS : result;
        }
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(NuccBestPayBusinessFields.ORI_TRX_DT_TM, formatTimeString((Long) order.get(DaoConstants.CTIME)));
        builder.set(NuccBestPayBusinessFields.ORI_IDC_FLAG, BeanUtil.getPropString(config, TransactionParam.NUCC_IDC_FLAG, "20"));
        builder.set(NuccBestPayBusinessFields.ORI_TRX_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.set(NuccBestPayBusinessFields.ACCEPT_EXTENSION
                ,"<OperatorId>" + configSnapshot.get(TransactionParam.TERMINAL_SN) +"</OperatorId><OriBatchId>"+ MapUtils.getNestedProperty(transaction, EXTRA_OUT_NUCC_BESTPAY_BATCH_ID)+"</OriBatchId>");
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), NuccBestPayConstants.CANCEL, builder.build(), retryTimes, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call nucc best pay cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String sysRtnCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.SYS_RTN_CD);
        String bizStsCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.BIZ_STS_CD);
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd) && (NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(bizStsCd)
                || NuccBestPayConstants.BIZ_ORI_TRANS_NOT_EXISTS.equals(bizStsCd)
                || NuccBestPayConstants.BIZ_ORI_TRANS_FAIL.equals(bizStsCd))){
            return Workflow.RC_CANCEL_SUCCESS;
        } else if (NuccBestPayConstants.BIZ_STATE_IN_PROG.equals(bizStsCd) || NuccBestPayConstants.BIZ_STATE_UN_KNOW.equals(bizStsCd)) {
            return Workflow.RC_RETRY;
        }else if(!NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        }else
            return Workflow.RC_ERROR;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> order = context.getOrder();
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(NuccBestPayBusinessFields.ORI_TRX_DT_TM,formatTimeString((Long)order.get(DaoConstants.CTIME)));
        builder.set(NuccBestPayBusinessFields.ORI_IDC_FLAG, BeanUtil.getPropString(config, TransactionParam.NUCC_IDC_FLAG, "20"));
        builder.set(NuccBestPayBusinessFields.ORI_TRX_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), NuccBestPayConstants.NORMAL_QUERY, builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call nucc best query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        String oriSysRtnCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_SYS_RTN_CD);
        String oriBizStsCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_BIZ_STS_CD);
        String batchId = BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_BATCH_ID);
        String oriTrxStatus =  BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_TRX_STATUS);
        setTradeNoBuyerInfoIfExists(result, context);
        if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(oriSysRtnCd) 
                && (NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(oriBizStsCd) 
                        || (NuccBestPayConstants.ORI_TRX_STATUS_SUCCESS.equals(oriTrxStatus) && NuccBestPayConstants.BIZ_STATE_IN_PROG.equals(oriBizStsCd)) )){
            setBatchId(transaction, batchId);
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_TRX_FINISH_TM)));
            return Workflow.RC_PAY_SUCCESS;
        } else if (!NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(oriSysRtnCd)
                || NuccBestPayConstants.BIZ_STATE_IN_PROG.equals(oriBizStsCd)
                || NuccBestPayConstants.BIZ_STATE_UN_KNOW.equals(oriBizStsCd)
                || NuccBestPayConstants.BIZ_FAIL.equals(oriBizStsCd)) {
            setBatchId(transaction, batchId);
            return Workflow.RC_IN_PROG;
        } else if(NuccBestPayConstants.BIZ_ORI_TRANS_NOT_EXISTS.equals(oriBizStsCd)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_ERROR;
    }

    /**
     * 退款查询
     *
     * @param context
     * @return
     */
    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        Object mrchntId = builder.build().remove(NuccBestPayBusinessFields.MRCHNT_ID);
        builder.set(NuccBestPayBusinessFields.ORI_TRX_ORDER_NO, transaction.get(Transaction.TSN));
        builder.set(NuccBestPayBusinessFields.ORI_FORWARD_TRX_ORDER_NO, order.get(Order.SN));
        builder.set(NuccBestPayBusinessFields.ORI_TRX_DT_TM, formatTimeString((Long) transaction.get(DaoConstants.CTIME)));
        builder.set(NuccBestPayBusinessFields.ORI_IDC_FLAG, BeanUtil.getPropString(config, TransactionParam.NUCC_IDC_FLAG, "20"));
        builder.set(NuccBestPayBusinessFields.ACCEPT_EXTENSION, "<AcceptExtension>"+mrchntId+"</AcceptExtension>");

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY), NuccBestPayConstants.REFUND_QUERY, builder.build(), retryTimes, "refund query");
        } catch (Exception ex) {
            logger.error("failed to call nucc bestpay refund query", ex);
            setTransactionContextErrorInfo(context, "refund query", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String oriSysRtnCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_SYS_RTN_CD);
        String oriBizStsCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_BIZ_STS_CD);
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(oriSysRtnCd) && NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(oriBizStsCd)){
            //退款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME,parseTimeString(BeanUtil.getPropString(result, NuccBestPayResponseFields.ORI_TRX_FINISH_TM)));
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        } else {
            return Workflow.RC_ERROR;
        }

    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        Object mrchntId = builder.build().remove(NuccBestPayBusinessFields.MRCHNT_ID);
        Map<String, Object> config = getTradeParams(transaction);
        builder.set(NuccBestPayBusinessFields.TRX_DT_TM, formatTimeString((Long) transaction.get(DaoConstants.CTIME)));
        builder.set(NuccBestPayBusinessFields.TRX_ORDER_NO, transaction.get(Transaction.TSN));
        builder.set(NuccBestPayBusinessFields.ORI_TRX_ORDER_NO, order.get(Order.SN));
        builder.set(NuccBestPayBusinessFields.ORI_TRXDT_TM, formatTimeString((Long)order.get(DaoConstants.CTIME)));

        builder.set(NuccBestPayBusinessFields.TRX_AMT, getTradeCurrency(transaction) + StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(NuccBestPayBusinessFields.ORI_TRX_AMT,getTradeCurrency(transaction) + StringUtils.cents2yuan(BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL)));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.set(NuccBestPayBusinessFields.NOTIFY_URL, notifyUrl);
        builder.set(NuccBestPayBusinessFields.ORI_IDC_FLAG, BeanUtil.getPropString(config, TransactionParam.NUCC_IDC_FLAG, "20"));
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.set(NuccBestPayBusinessFields.ACCEPT_EXTENSION, "<BestpayMctNo>" + mrchntId + "</BestpayMctNo><OperatorId>"+ configSnapshot.get(TransactionParam.TERMINAL_SN) +"</OperatorId><OriBatchId>"+ MapUtils.getNestedProperty(transaction, EXTRA_OUT_NUCC_BESTPAY_BATCH_ID)+"</OriBatchId>");

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, NuccBestPayConstants.REFUND, builder.build(), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call nucc best refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        String sysRtnCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.SYS_RTN_CD);

        String bizStsCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.BIZ_STS_CD);

        setTransactionContextErrorInfo(result, context, OP_REFUND);
        if(!NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        } else if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd) && NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(bizStsCd)){
            //退款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, NuccBestPayResponseFields.TRX_FINISH_TM)));
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (NuccBestPayConstants.BIZ_STATE_IN_PROG.equals(bizStsCd) || NuccBestPayConstants.BIZ_STATE_UN_KNOW.equals(bizStsCd)) {
            return refundQuery(context);
        }else{
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(NuccBestPayBusinessFields.IDC_FLAG, BeanUtil.getPropString(config, TransactionParam.NUCC_IDC_FLAG, "20"));
        builder.set(NuccBestPayBusinessFields.TRX_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(NuccBestPayBusinessFields.TRX_AMT, getTradeCurrency(transaction) + StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(NuccBestPayBusinessFields.DESCRIPTION, transaction.get(Transaction.SUBJECT)+"");
        builder.set(NuccBestPayBusinessFields.TRX_TYPE_CD, NuccBestPayConstants.SUB_PAYWAY_QRCODE);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.set(NuccBestPayBusinessFields.SCENE_INFO
                , CollectionUtil.hashMap(NuccBestPayBusinessFields.STORE_ID, configSnapshot.get(TransactionParam.STORE_SN)
                        , NuccBestPayBusinessFields.OPERATOR_ID, configSnapshot.get(TransactionParam.TERMINAL_SN)
                        , NuccBestPayBusinessFields.DEVICE_ID, configSnapshot.get(TransactionParam.TERMINAL_SN))
                );
        builder.set(NuccBestPayBusinessFields.ACCEPT_EXTENSION,"<GoodsInfo>"+transaction.get(Transaction.SUBJECT)+"</GoodsInfo>");
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(NuccBestPayBusinessFields.NOTIFY_URL, notifyUrl);
        }
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, NuccBestPayConstants.PRE_CREATE, builder.build(), retryTimes, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call nucc best precareate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String sysRtnCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.SYS_RTN_CD);
        String bizStsCd = BeanUtil.getPropString(result, NuccBestPayResponseFields.BIZ_STS_CD);
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd) && NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(bizStsCd)){
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(CollectionUtils.isEmpty(extraOutFields)){
                extraOutFields = Maps.newHashMap();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            Map wapPayRequest = CollectionUtil.hashMap(com.wosai.mpay.api.bestpay.BusinessFields.SERVICE, "mobile.security.pay",
                    com.wosai.mpay.api.bestpay.BusinessFields.MERCHANT_ID.toUpperCase(), "3178032169661049",
                    com.wosai.mpay.api.bestpay.BusinessFields.MERCHANT_PWD.toUpperCase(), "123456",
                    com.wosai.mpay.api.bestpay.BusinessFields.BACK_MERCHANT_URL, notifyUrl,
                    com.wosai.mpay.api.bestpay.BusinessFields.SIGN_TYPE, "MD5",
                    com.wosai.mpay.api.bestpay.BusinessFields.ORDER_SEQ.toUpperCase(), BeanUtil.getPropString(result, NuccBestPayResponseFields.TRX_SESSION_ID),
                    com.wosai.mpay.api.bestpay.BusinessFields.ORDER_REQ_TRANSEQ.toUpperCase(), BeanUtil.getPropString(transaction, Transaction.TSN),
                    com.wosai.mpay.api.bestpay.BusinessFields.ORDER_TIME, DateUtil.formatDate(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)),"yyyyMMddHHmmss"),
                    com.wosai.mpay.api.bestpay.BusinessFields.ORDER_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)),
                    com.wosai.mpay.api.bestpay.BusinessFields.CURTYPE, "RMB",
                    com.wosai.mpay.api.bestpay.BusinessFields.PRODUCT_ID.toUpperCase(), BestpayConstants.PRODUCT_ID_04,
                    com.wosai.mpay.api.bestpay.BusinessFields.PRODUCT_DESC.toUpperCase(), BeanUtil.getPropString(transaction, Transaction.SUBJECT),
                    com.wosai.mpay.api.bestpay.BusinessFields.PRODUCT_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)),
                    com.wosai.mpay.api.bestpay.BusinessFields.ATTACH_AMOUNT, "0",
                    com.wosai.mpay.api.bestpay.BusinessFields.BUSI_TYPE.toUpperCase(), "04",
                    com.wosai.mpay.api.bestpay.BusinessFields.SWTICH_ACC, "true",
                    com.wosai.mpay.api.bestpay.BusinessFields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT),
                    com.wosai.mpay.api.bestpay.BusinessFields.OTHER_FLOW, "01");
            try {

                wapPayRequest.put(com.wosai.mpay.api.bestpay.ProtocolFields.SIGN, BestpaySignature.getSign(wapPayRequest, BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_KEY, ""), BestpayClient.getSignatureColumns(BestpayConstants.METHOD_WAP_H5_PAY)));
            } catch (MpayException ex) {
            logger.error("failed to call nucc best precareate", ex);
            return Workflow.RC_IOEX;
        }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapPayRequest);

            return Workflow.RC_CREATE_SUCCESS;
        }else if(!NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        }else if(!NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(bizStsCd)){
            return Workflow.RC_SYS_ERROR;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(Transaction.TYPE_PAYMENT == type){
            String sysRtnCd = BeanUtil.getPropString(providerNotification, NuccBestPayResponseFields.SYS_RTN_CD);
            String bizStsCd = BeanUtil.getPropString(providerNotification, NuccBestPayResponseFields.BIZ_STS_CD);
            if(NuccBestPayConstants.SYS_RTN_CODE_SUCCESS.equals(sysRtnCd) && NuccBestPayConstants.BIZ_STATE_CODE_SUCCESS.equals(bizStsCd)){
                return Workflow.RC_PAY_SUCCESS.equals(query(context))?Workflow.RC_PAY_SUCCESS:null;
            }
        }
        return null;
    }


    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(NuccBestPayBusinessFields.ACCEPT_PARTNER_ID, config.get(TransactionParam.NUCC_CHANNEL_ID));
        builder.set(NuccBestPayBusinessFields.ACCOUNT_PARTNER_ID, config.get(TransactionParam.NUCC_PARENT_MERCHANT_ID));
        builder.set(NuccBestPayBusinessFields.MRCHNT_ID, config.get(TransactionParam.NUCC_SP_MCH_ID));
        builder.set(NuccBestPayBusinessFields.TRX_DT_TM, formatTimeString(System.currentTimeMillis()));
        builder.set(NuccBestPayBusinessFields.REQ_ID, UUIDGenerator.getUUID());
        return builder;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_NUCC;
    }


    public String getPrivateKeyContent(Map<String,Object> config){
        String rsaKeyId = (String) config.get(TransactionParam.NUCC_PRIVATE_KEY);
        return facade.getRsaKeyDataById(rsaKeyId);
    }


}
