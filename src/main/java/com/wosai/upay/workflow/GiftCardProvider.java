package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.giftCard.*;
import com.wosai.mpay.api.lakala.open.ResponseFields;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.*;

public class GiftCardProvider extends AbstractServiceProvider {

    @Autowired
    private GiftCardClient giftCardClient;

    public static final String NAME = "provider.gift.card";

    protected String b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE + "m";

    private static final Logger logger = LoggerFactory.getLogger(GiftCardProvider.class);

    protected int retryTimes = 3;

    private static final String FC_COUPON = "COUPON";
    private static final String FC_DISCOUNT = "DISCOUNT";
    private static final String FC_PCARD = "PCARD";
    private static final String FC_MCARD = "MCARD";
    private static final String FC_ECARD = "ECARD";
    private static final String FC_MDISCOUNT = "MDISCOUNT";
    private static final String FC_MCOUPON = "MCOUPON";
    private static final String FC_BANKCARD = "BANKCARD";
    //fundType
    private static final String FT_DEBIT_CARD = "DEBIT_CARD";
    private static final String FT_CREDIT_CARD = "CREDIT_CARD";

    private static final String FC_GIFTCARDACCOUNT = "GIFTCARDACCOUNT";
    private static final String FC_POINT = "FC_POINT";

    public GiftCardProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_AMOUNT, BusinessFields.TRANS_CURRENCY, BusinessFields.REFUND_AMOUNT, BusinessFields.REFUND_CURRENCY));
    }

    @Override
    public String getName() {
        return NAME;
    }

    private static Set<String> consumerDiscount = CollectionUtil.hashSet(
            FC_DISCOUNT, FC_MDISCOUNT, FC_COUPON
    );

    @SuppressWarnings("unchecked")
    public static final Map<String, Object> fundChannelPayment = CollectionUtil.hashMap(
            FC_COUPON, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_HONGBAO_CHANNEL,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_COUPON
            ),
            FC_GIFTCARDACCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_GIFTCARDACCOUNT
            ),
            FC_POINT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CUSTOM_ALIPAY_POINT,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_POINT
            ),
            FC_DISCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_DISCOUNT
            ),
            FC_PCARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CARD_CHANNEL_MCH_PRE,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_PCARD
            ),
            FC_ECARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CARD_CHANNEL_MCH_PRE,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_ECARD
            ),
            FC_MCARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CARD_CHANNEL_MCH_BALANCE,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_MCARD
            ),
            FC_MDISCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_MDISCOUNT
            ),
            FC_MCOUPON, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_HONGBAO_CHANNEL_MCH,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_MCOUPON
            ),
            FC_BANKCARD + "." + FT_CREDIT_CARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_BANKCARD_CREDIT,
                    Transaction.PAYMENT_ORIGIN_TYPE, FT_CREDIT_CARD
            ),
            FC_BANKCARD + "." + FT_DEBIT_CARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_BANKCARD_DEBIT,
                    Transaction.PAYMENT_ORIGIN_TYPE, FT_DEBIT_CARD
            )
    );

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (payway == Order.PAYWAY_GIFT_CARD || payway == Order.PAYWAY_WELFARE_CARD) && subPayway == Order.SUB_PAYWAY_BARCODE && getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.GIFT_CARD_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> config = getTradeParams(transaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        @SuppressWarnings("unchecked")
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder requestBuilder = getDefaultGiftCardRequestBuilder(config);
        requestBuilder.set(ProtocolFields.METHOD, GiftCardMethods.GIFTCARD_REDEEM);
        requestBuilder.bizSet(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        requestBuilder.bizSet(BusinessFields.TOTAL_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        requestBuilder.bizSet(BusinessFields.SCENE, GiftCardConstant.SCENE_BAR_CODE);
        requestBuilder.bizSet(BusinessFields.AUTH_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        requestBuilder.bizSet(BusinessFields.SUBJECT, transaction.get(Transaction.SUBJECT));
        requestBuilder.bizSet(BusinessFields.TRANS_CURRENCY, getTradeCurrency(transaction));
        requestBuilder.bizSet(BusinessFields.BODY, transaction.get(Transaction.BODY));
        requestBuilder.bizSet(BusinessFields.OPERATOR_ID, transaction.get(Transaction.OPERATOR));
        requestBuilder.bizSet(BusinessFields.STORE_ID, configSnapShot.get(TransactionParam.STORE_SN));
        requestBuilder.bizSet(BusinessFields.MERCHANT_ID, configSnapShot.get(TransactionParam.MERCHANT_SN));
        requestBuilder.bizSet(BusinessFields.TERMINAL_ID, configSnapShot.get(TransactionParam.TERMINAL_SN));
        requestBuilder.bizSet(BusinessFields.TIMEOUT_EXPRESS, b2cTimeoutExpress);
        carryOverExtendedParams(extendedParams, requestBuilder);
        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = requestBuilder.build();
        } catch (BuilderException e) {
            logger.error("giftCard request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), request, 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call giftcard pay", ex);
            setTransactionContextErrorInfo(context, "pay", ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        result = (Map<String, Object>) result.get(ProtocolFields.GIFTCARD_REDEEM_RESPONSE);
        if (result == null) {
            return Workflow.RC_IN_PROG;
        }

        String returnCode = (String) result.get(BusinessFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setTradeNoBuyerInfoIfExists(result,context);
        if(GiftCardConstant.RETURN_CODE_FAIL.equals(returnCode)){
            String subCode = BeanUtil.getPropString(result, BusinessV2Fields.SUB_CODE);
            //相关状态直接设置为canceled的状态
            if(GiftCardConstant.PAY_FAIL_ERR_CODE_LISTS.contains(subCode)){
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_ERROR;
        }if (GiftCardConstant.RETURN_CODE_UNKNOW.equals(returnCode)) {
            //业务出现未知错误或者系统异常
            return Workflow.RC_IN_PROG;
        } else if(GiftCardConstant.RETURN_CODE_INPROG.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        } else if (GiftCardConstant.RETURN_CODE_SUCCESS.equals(returnCode)) {
            //付款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessFields.GMT_PAYMENT)));
            resolvePayFund(context.getOrder(), transaction, result);
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    private Map<String, Object> retryIfNetworkException(Map<String, Object> config, String gatewayUrl, Map<String, String> request, int retryTimes, String logFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < retryTimes; ++i) {
            try {
                return giftCardClient.call(gatewayUrl, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in giftcard {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", retryTimes));
        throw exception != null ? exception : new Exception("unknown exception");
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> configSnapshot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        initTransactionSomeValue(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultGiftCardRequestBuilder(config);
        builder.set(ProtocolFields.METHOD, GiftCardMethods.GIFTCARD_REDEEM_CANCEL);
        builder.bizSet(BusinessFields.TERMINAL_ID, configSnapshot.get(TransactionParam.TERMINAL_SN));
        builder.bizSet(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        carryOverExtendedParams(extendedParams, builder);
        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("giftCard request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), request, retryTimes, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call giftCard cancel", ex);
            setTransactionContextErrorInfo(context, "cancel", ex);
            return Workflow.RC_RETRY;
        }
        result = (Map<String, Object>) result.get(ProtocolFields.GIFTCARD_REDEEM_CANCEL_RESPONSE);
        if (result == null) {
            return Workflow.RC_RETRY;
        }
        String returnCode = (String) result.get(BusinessFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        if (GiftCardConstant.RETURN_CODE_FAIL.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (GiftCardConstant.RETURN_CODE_UNKNOW.equals(returnCode)) {
            return Workflow.RC_RETRY;
        } else if (GiftCardConstant.RETURN_CODE_SUCCESS.equals(returnCode)) {
            String action = (String) result.get(BusinessFields.ACTION);
            if (GiftCardConstant.ACTION_CLOSE.equals(action) || GiftCardConstant.ACTION_REFUND.equals(action)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        initTransactionSomeValue(transaction);
        RequestBuilder requestBuilder = getDefaultGiftCardRequestBuilder(config);
        requestBuilder.set(ProtocolFields.METHOD, GiftCardMethods.GIFTCARD_REDEEM_QUERY);
        requestBuilder.bizSet(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        requestBuilder.bizSet(BusinessFields.TERMINAL_ID, configSnapShot.get(TransactionParam.TERMINAL_SN));
        Map<String, String> request = null;
        Map<String, Object> result;
        try {
            request = requestBuilder.build();
        } catch (BuilderException e) {
            logger.error("giftCard request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), request, retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call giftCard query", ex);
            setTransactionContextErrorInfo(context, "query", ex);
            //连接错误，继续轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        result = (Map<String, Object>) result.get(ProtocolFields.GIFTCARD_REDEEM_QUERY_RESPONSE);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String) result.get(BusinessFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result,context);
        if (GiftCardConstant.RETURN_CODE_FAIL.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (GiftCardConstant.RETURN_CODE_UNKNOW.equals(returnCode)) {
            return Workflow.RC_IN_PROG;
        } else if(GiftCardConstant.RETURN_CODE_INPROG.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        } else if (GiftCardConstant.RETURN_CODE_SUCCESS.equals(returnCode)) {
            String tradeStatus = (String) result.get(BusinessFields.TRADE_STATUS);
            if (GiftCardConstant.TRADE_STATUS_WAIT_BUYER_PAY.equals(tradeStatus)) {
                return Workflow.RC_IN_PROG;
            } else if (GiftCardConstant.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || GiftCardConstant.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus)) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessFields.SEND_PAY_DATE)));
                resolvePayFund(context.getOrder(), transaction, result);
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> configSnapShot = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        @SuppressWarnings("unchecked")
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultGiftCardRequestBuilder(config);
        builder.set(ProtocolFields.METHOD, GiftCardMethods.GIFTCARD_REDEEM_REFUND);
        builder.bizSet(BusinessFields.OUT_REQUEST_NO, transaction.get(Transaction.TSN));
        builder.bizSet(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessFields.REFUND_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessFields.REFUND_CURRENCY, getTradeCurrency(transaction));
        builder.bizSet(BusinessFields.STORE_ID, configSnapShot.get(TransactionParam.STORE_SN));
        builder.bizSet(BusinessFields.MERCHANT_ID, configSnapShot.get(TransactionParam.MERCHANT_SN));
        builder.bizSet(BusinessFields.TERMINAL_ID, configSnapShot.get(TransactionParam.TERMINAL_SN));
        carryOverExtendedParams(extended, builder);
        Map<String, String> request = null;
        Map<String, Object> result = null;
        try {
            request = builder.build();
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), request, retryTimes, "refund");
        } catch (Exception ex) {
            logger.error("failed to call giftCard refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            //异常重试
            return Workflow.RC_RETRY;
        }
        result = (Map<String, Object>) result.get(ProtocolFields.GIFTCARD_REFUND_RESPONSE);

        String returnCode = (String) result.get(BusinessFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        if (GiftCardConstant.RETURN_CODE_FAIL.equals(returnCode)) {
            return Workflow.RC_ERROR;
        } else if (GiftCardConstant.RETURN_CODE_UNKNOW.equals(returnCode)) {
            //服务不可用  重试
            return Workflow.RC_RETRY;
        } else if (GiftCardConstant.RETURN_CODE_SUCCESS.equals(returnCode)) {
            setTradeNoBuyerInfoIfExists(result,context);
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessFields.GMT_REFUND_PAY)));
            resolveRefundFund(context.getOrder(), context.getTransaction(), result);
            return Workflow.RC_REFUND_SUCCESS;

        } else {
            return Workflow.RC_ERROR;
        }
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        if (StringUtils.isEmpty(MapUtil.getString(order, Order.BUYER_UID))) {
            String buyerUid = MapUtil.getString(result, BusinessFields.BUYER_USER_ID);
            if (!StringUtils.isEmpty(buyerUid)) {
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerUid = MapUtil.getString(result, BusinessFields.BUYER_USER_ID);
            if (!StringUtils.isEmpty(buyerUid)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerUid);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(result, BusinessFields.TRADE_NO);
            if (!StringUtils.isEmpty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UpayBizException("暂不支持");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if (type == Transaction.TYPE_PAYMENT) {
            //通知木有NOTIFY_ACTION_TYPE字段
            String tradeStatus = BeanUtil.getPropString(providerNotification, BusinessFields.TRADE_STATUS);
            boolean paySuccess = (GiftCardConstant.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || GiftCardConstant.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus));
            if (paySuccess) {
                return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
            }
        }
        return null;
    }

    private RequestBuilder getDefaultGiftCardRequestBuilder(Map<String, Object> config) {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ProtocolFields.APP_ID, BeanUtil.getPropString(config, TransactionParam.GIFT_CARD_APP_ID));
        requestBuilder.set(ProtocolFields.CHARSET, GiftCardConstant.CHARSET_UTF8);
        requestBuilder.set(ProtocolFields.SIGN_TYPE, BeanUtil.getPropString(config, TransactionParam.GIFT_CARD_SIGN_TYPE));
        return requestBuilder;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.bizSet(key, value);
            }
        }
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String returnCode = (String) result.get(BusinessFields.CODE);//返回状态码
        String returnMsg = (String) result.get(BusinessFields.MSG);//返回信息
        String errCode = (String) result.get(BusinessFields.SUB_CODE); //错误代码
        String errCodeDes = (String) result.get(BusinessFields.SUB_MSG);//错误代码描述
        map.put(BusinessFields.CODE, returnCode);//返回状态码
        map.put(BusinessFields.MSG, returnMsg);//返回信息
        map.put(BusinessFields.SUB_CODE, errCode);//错误代码
        map.put(BusinessFields.SUB_MSG, errCodeDes);//错误代码描述
        setTransactionContextErrorInfo(context.getTransaction(), key, map,
                GiftCardConstant.RETURN_CODE_SUCCESS.equals(returnCode), 
                StringUtil.empty(errCode) ? returnCode : errCode, 
                StringUtil.empty(errCodeDes) ? returnMsg : errCodeDes);
    }

    /**
     * 解析处理用户支付金额信息
     *
     * @param order       订单
     * @param transaction 流水
     * @param result      结果
     */
    @SuppressWarnings("unchecked")
    private static void resolvePayFund(Map<String, Object> order, Map<String, Object> transaction, Map<String, Object> result) {
        Object tradeFundBill = BeanUtil.getNestedProperty(result, BusinessFields.FUND_BILL_LIST);
        if (tradeFundBill instanceof String && !StringUtil.empty((String) tradeFundBill)) {
            try {
                tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
            } catch (IOException e) {
                logger.warn("parse fundBillList error", e);
            }
        }
        List<Map<String, Object>> tradeFundBills = new ArrayList<>();
        if (tradeFundBill instanceof List) {
            tradeFundBills.addAll((List<Map<String, Object>>) tradeFundBill);
        } else if (tradeFundBill instanceof Map) {
            tradeFundBills.add((Map<String, Object>) tradeFundBill);
        }

        long paid = 0;
        long discount = 0;
        long received = 0;
        String accountId = null;
        for (Map<String, Object> bill : tradeFundBills) {
            String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessFields.FUND_CHANNEL) ? BusinessFields.FUND_CHANNEL : BusinessFields.FUNDCHANNEL);
            long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessFields.AMOUNT));
            if (consumerDiscount.contains(fundChannel)) {
                discount += amount;
            }
            if (!consumerDiscount.contains(fundChannel)) {
                paid += amount;
            }
            if (!FC_MDISCOUNT.equals(fundChannel) && !FC_MCOUPON.equals(fundChannel)) {
                received += amount;
            }
            if(FC_ECARD.equals(fundChannel)){
                accountId = MapUtils.getString(bill, BusinessFields.ACCOUNT_ID);
            }
        }
        Map extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap();
        }
        if (!StringUtil.empty(accountId)) {
            extraOutFields.put(Transaction.WELFARE_ACCOUNT_ID, accountId);
            BeanUtil.setNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0 && paid != 0) {
            transaction.put(Transaction.PAID_AMOUNT, paid);
        }
        if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0 && received != 0) {
            transaction.put(Transaction.RECEIVED_AMOUNT, received);
        }
        if (BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0L && discount != 0) {
            order.put(Order.TOTAL_DISCOUNT, discount);
            order.put(Order.NET_DISCOUNT, discount);
        }
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, getGiftCardPayments(tradeFundBills));
    }

    @SuppressWarnings("unchecked")
    private void resolveRefundFund(Map<String, Object> order, Map<String, Object> transaction, Map<String, Object> result) {
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(order, DaoConstants.CTIME));
        if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        } else {
            //部分退款，根据返回的refund_detail_item_list来计算
            Object tradeFundBill = BeanUtil.getNestedProperty(result, BusinessFields.REFUND_DETAIL_ITEM_LIST);
            if (tradeFundBill instanceof String && !StringUtil.empty((String) tradeFundBill)) {
                try {
                    tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                    tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
                } catch (IOException e) {
                    logger.warn("parse refund_detail_item_list error", e);
                }
            }
            List<Map<String, Object>> tradeFundBills = new ArrayList();
            if (tradeFundBill instanceof List) {
                tradeFundBills.addAll((List<Map<String, Object>>) tradeFundBill);
            } else if (tradeFundBill instanceof Map) {
                tradeFundBills.add((Map<String, Object>) tradeFundBill);
            }

            long paid = 0;
            long received = 0;
            for (Map<String, Object> bill : tradeFundBills) {
                String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessFields.FUND_CHANNEL) ? BusinessFields.FUND_CHANNEL : BusinessFields.FUNDCHANNEL);
                logger.debug("fundChannel {}", fundChannel);
                long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessFields.AMOUNT));
                if (!consumerDiscount.contains(fundChannel)) {
                    paid += amount;
                }
                if (!FC_MDISCOUNT.equals(fundChannel) && !FC_MCOUPON.equals(fundChannel)) {
                    received += amount;
                }
            }
            if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0 && paid != 0) {
                transaction.put(Transaction.PAID_AMOUNT, paid);
            }
            if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0 && received != 0) {
                transaction.put(Transaction.RECEIVED_AMOUNT, received);
            }
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, getGiftCardPayments(tradeFundBills));
        }
    }

    @SuppressWarnings("unchecked")
    private static List<Map<String, Object>> getGiftCardPayments(List<Map<String, Object>> fundBillList) {
        List<Map<String, Object>> payments = new ArrayList<>();
        for (Map<String, Object> bill : fundBillList) {
            String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessFields.FUND_CHANNEL) ? BusinessFields.FUND_CHANNEL : BusinessFields.FUNDCHANNEL);
            String fundType = BeanUtil.getPropString(bill, bill.containsKey(BusinessFields.FUND_TYPE) ? BusinessFields.FUND_TYPE : BusinessFields.FUNDTYPE);
            long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessFields.AMOUNT));
            amount = Math.abs(amount);
            Map payment = (Map) fundChannelPayment.get(fundChannel);
            if (payment == null) {
                payment = (Map) fundChannelPayment.get(fundChannel + "." + fundType);
            }
            if (payment == null) {
                payment = CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, null,
                        Transaction.PAYMENT_ORIGIN_TYPE, fundChannel
                );
            } else {
                payment = (Map) ((HashMap) payment).clone();
            }
            payment.put(Transaction.PAYMENT_AMOUNT, amount);
            payments.add(payment);
        }
        return payments;
    }

    @Override
    public Integer getProvider() {
        return null;
    }
}
