package com.wosai.upay.workflow;

import avro.shaded.com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.sodexo.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2019-07-17 Time: 15:42
 */
public class SodexoServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(SodexoServiceProvider.class);
    public static final String NAME = "provider.sodexo";
    private static final String BARCODE = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.BARCODE);
    private static final String DATE_TIME_FORMATTER = "yyyyMMddHHmmss";


    @Autowired
    private SodexoClient client;
    @Autowired
    protected SodexoTokenCache sodexoTokenCache;

    public SodexoServiceProvider() {
        dateFormat = new SafeSimpleDateFormat(DATE_TIME_FORMATTER);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }

        return MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_SODEXO
                && MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.SODEXO_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.TRANS_TYPE, SodexoConstants.PAY_TRANS_TYPE);
        builder.set(BusinessFields.SUBMIT_TIME, formatTimeString(System.currentTimeMillis()));
        builder.set(BusinessFields.CLIENT_TRACE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.BARCODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.ACTUAL_AMOUNT
                , centsToYuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(builder.build(), url
                    , queryToken(tradeParams), 1, OP_PAY);
        } catch (Exception e) {
            logger.error("failed to call sodexo pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, transaction);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        if (needRefund(context.getOrder(), transaction)) {
            return cancelToRefundProcess(context);
        }

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);

        Map<String, Object> params = buildRefundOrCancelParams(context.getOrder(), transaction);
        params.put(BusinessFields.TRANS_TYPE, SodexoConstants.CANCEL_TRANS_TYPE);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(params, url
                    , queryToken(getTradeParams(transaction)), 3, OP_CANCEL);
        } catch (Exception e) {
            logger.error("failed to call sodexo cancel", e);
            setTransactionContextErrorInfo(context, OP_CANCEL, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);

        builder.set(BusinessFields.TRANS_TYPE, SodexoConstants.PAY_QUERY_TRANS_TYPE);
        builder.set(BusinessFields.CLIENT_TRACE_NO, transaction.get(Transaction.TSN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(builder.build(), url
                    , queryToken(tradeParams), 3, OP_QUERY);
        } catch (Exception e) {
            logger.error("failed to call sodexo query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);

        Map<String, Object> params = buildRefundOrCancelParams(context.getOrder(), transaction);
        params.put(BusinessFields.TRANS_TYPE, SodexoConstants.REFUND_TRANS_TYPE);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(params, url
                    , queryToken(getTradeParams(transaction)), 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call sodexo refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, transaction);
    }

    private String buildRefundResult(Map<String, Object> result, Map<String, Object> transaction) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);

        if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_SUCCESS)) {
            resolveRefundFund(transaction);
            return Workflow.RC_REFUND_SUCCESS;
        }

        if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_REFUND_FAIL_ORI_TRADE_REFUNDED)) {
            int subPayway = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
            //wap支付拆出来，做失败处理
            if (subPayway == Order.SUB_PAYWAY_MINI) {
                return Workflow.RC_ERROR;
            } else {
                resolveRefundFund(transaction);
                return Workflow.RC_REFUND_SUCCESS;
            }
        }

        tokenInvalidProcess(result, getTradeParams(transaction));

        return Workflow.RC_ERROR;
    }

    private void resolveRefundFund(Map<String, Object> transaction) {
        //本次退款总额
        long refundAmountTotal = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0);

        transaction.put(Transaction.PAID_AMOUNT, refundAmountTotal);
        transaction.put(Transaction.RECEIVED_AMOUNT, refundAmountTotal);
    }


    private String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String returnCode = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_SUCCESS)
                || Objects.equals(returnCode, SodexoConstants.RESP_CODE_CANCEL_FAIL_ORI_TRADE_CANCELED)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_CANCEL_FAIL_TRADE_NO_NOT_FOUND)) {
            return cancelToRefundProcess(context);
        }
        tokenInvalidProcess(result, getTradeParams(context.getTransaction()));

        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();

        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String returnCode = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        //code:0000，响应成功
        if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_SUCCESS)) {

            String transStatus = BeanUtil.getPropString(result, ResponseFields.TRANS_STATUS, StringUtils.EMPTY);
            if (SodexoConstants.TRANS_STATUS_SUCCESS.equalsIgnoreCase(transStatus)) {
                resolvePayFund(result, transaction, order);
                return Workflow.RC_PAY_SUCCESS;
            }
            if (SodexoConstants.TRANS_STATUS_PENDING.equalsIgnoreCase(transStatus)) {
                return Workflow.RC_IN_PROG;
            }
        }else if(Objects.equals(returnCode, SodexoConstants.RESP_CODE_SYSTEM_ERR)) {
            return Workflow.RC_IN_PROG;
        } 
        // wap支付，需要不停查单
        else if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_TRADE_NO_NOT_FOUND)
                && BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_MINI) {
            return Workflow.RC_IN_PROG;
        }

        //token失效
        if (tokenInvalidProcess(result, getTradeParams(transaction))) {
            return Workflow.RC_IN_PROG;
        }

        return Workflow.RC_TRADE_CANCELED;
    }


    protected RequestBuilder buildBaseRequestBuilder(Map<String, Object> tradeParams) {
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.VERSION, SodexoConstants.VERSION);
        builder.set(BusinessFields.TRANS_TIME, formatTimeString(System.currentTimeMillis()));
        builder.set(ProtocolFields.MID, tradeParams.get(TransactionParam.SODEXO_MID));
        builder.set(ProtocolFields.TID, tradeParams.get(TransactionParam.SODEXO_TID));

        return builder;
    }

    protected Map<String, Object> retryIfNetworkException(Map<String, Object> request, String url, String token
            , int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(url, token, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in sodexo {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private String buildPayResult(Map<String, Object> result, Map<String, Object> transaction) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String returnCode = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        if (Objects.equals(returnCode, SodexoConstants.RESP_CODE_SUCCESS)) {
            return Workflow.RC_IN_PROG;
        }
        if (SodexoConstants.TRADE_CANCEL_CODE.contains(returnCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        tokenInvalidProcess(result, getTradeParams(transaction));

        return Workflow.RC_ERROR;
    }

    private void resolvePayFund(Map<String, Object> result, Map<String, Object> transaction, Map<String, Object> order) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return;
        }

        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.CHANNEL_TRADE_NO
                , BeanUtil.getPropString(result, ResponseFields.HOST_TRACE_NO));
        //交易金额
        long transactionAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        //三方单号
        String tradeNo = BeanUtil.getPropString(result, ResponseFields.HOST_TRACE_NO);

        String hostTimeStr = BeanUtil.getPropString(result, ResponseFields.HOST_TIME);
        long channelFinishTime;
        if (StringUtils.isEmpty(hostTimeStr)) {
            channelFinishTime = System.currentTimeMillis();
        } else {
            channelFinishTime = parseTimeString(hostTimeStr);
        }

        order.put(Order.TRADE_NO, tradeNo);

        //设置payments
        Map<String, Object> payment = MapUtils.hashMap(
                    Payment.TYPE, Payment.TYPE_WALLET_SODEXO,
                    Payment.ORIGIN_TYPE, Payment.TYPE_WALLET_SODEXO,
                    Transaction.PAYMENT_AMOUNT, transactionAmount
                );
        extraOutFields.put(Transaction.PAYMENTS, Lists.newArrayList(payment));

        transaction.put(Transaction.PAID_AMOUNT, transactionAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, transactionAmount);
        transaction.put(Transaction.TRADE_NO, tradeNo);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String respCode = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        String respMsg = BeanUtil.getPropString(result, ResponseFields.RETURN_MESSAGE);
        map.put(ResponseFields.RETURN_CODE, respCode);
        map.put(ResponseFields.RETURN_MESSAGE, respMsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, Objects.equals(respCode, SodexoConstants.RESP_CODE_SUCCESS), respCode, respMsg);
    }

    protected boolean tokenInvalidProcess(Map<String, Object> result, Map<String, Object> tradeParams) {
        String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        if (Objects.equals(message, SodexoConstants.TOKEN_INVALID_MESSAGE)) {
            refreshToken(tradeParams);
            return true;
        }
        return false;
    }

    protected String queryToken(Map<String, Object> tradeParams) {
        String clientId = BeanUtil.getPropString(tradeParams, TransactionParam.SODEXO_CLIENT_ID);
        String clientSecret = BeanUtil.getPropString(tradeParams, TransactionParam.SODEXO_CLIENT_SECRET);

        return sodexoTokenCache.getAccessToken(clientId, clientSecret
                , ApolloConfigurationCenterUtil.getProviderGateway(getName()
                        , ApolloConfigurationCenterUtil.GATEWAY_OP_AUTH));
    }

    protected void refreshToken(Map<String, Object> tradeParams) {
        String clientId = BeanUtil.getPropString(tradeParams, TransactionParam.SODEXO_CLIENT_ID);
        String clientSecret = BeanUtil.getPropString(tradeParams, TransactionParam.SODEXO_CLIENT_SECRET);

        sodexoTokenCache.refreshToken(clientId, clientSecret
                , ApolloConfigurationCenterUtil.getProviderGateway(getName()
                        , ApolloConfigurationCenterUtil.GATEWAY_OP_AUTH));
    }

    private Map<String, Object> buildRefundOrCancelParams(Map<String,Object> order, Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(order, DaoConstants.CTIME));

        String expenseToken = getExpenseToken(payTransaction);

        RequestBuilder builder = buildBaseRequestBuilder(tradeParams);
        builder.set(BusinessFields.SUBMIT_TIME, formatTimeString(System.currentTimeMillis()));
        builder.set(BusinessFields.CLIENT_TRACE_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.CLIENT_TRACE_ORG_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.BARCODE, BeanUtil.getNestedProperty(payTransaction, BARCODE));
        builder.set(BusinessFields.ACTUAL_AMOUNT
                , centsToYuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        if (Objects.nonNull(expenseToken)) {
            builder.set(BusinessFields.EXPENSE_TOKEN, expenseToken);
        }
        return builder.build();
    }

    private double centsToYuan(long amount) {
        return Double.parseDouble(StringUtils.cents2yuan(amount));
    }

    private boolean needRefund(Map<String, Object> order, Map<String, Object> transaction) {
        Map<String, Object> payTrans = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(order, DaoConstants.CTIME));

        long channelFinishTime = BeanUtil.getPropLong(payTrans, Transaction.CHANNEL_FINISH_TIME);
        if (channelFinishTime == 0) {
            return false;
        }

        long todayStartTimestamp = DateTimeUtil.getOneDayStart(System.currentTimeMillis());

        return channelFinishTime < todayStartTimestamp;
    }

    private String cancelToRefundProcess(TransactionContext context) {
        String result = refund(context);
        if (Workflow.RC_REFUND_SUCCESS.equals(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        return result;
    }

    protected String getExpenseToken(Map<String, Object> transaction) {
        return null;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }
}
