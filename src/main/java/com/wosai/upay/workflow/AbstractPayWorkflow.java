package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.FeeRateProcessor;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public abstract class AbstractPayWorkflow extends Workflow {

	public static final List<String> CONFIG_SNAPSHOT_REDUCE_KEYS = Arrays.asList(TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, 
            TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS,
            TransactionParam.IS_PROTECT_PAYER_PRIVACY, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG,
            TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER,
            TransactionParam.USE_CLIENT_STORE_SN, TransactionParam.DEPOSIT, 
            TransactionParam.ALIPAY_HUABEI_STATUS, TransactionParam.ALIPAY_HUABEI_LIMIT, 
            TransactionParam.ALIPAY_HUABEI_PARAMS, TransactionParam.IS_SENT_STORE_ID,
            TransactionParam.MERCHANT_BANKCARD_SINGLE_MAX_LIMIT,TransactionParam.MERCHANT_BANKCARD_DAY_MAX_LIMIT,TransactionParam.MERCHANT_BANKCARD_MONTH_MAX_LIMIT,
            TransactionParam.PARAMS_BANKCARD_FEE, TransactionParam.CHANNEL_LADDER_FEE_RATES, TransactionParam.ALIPAY_CREDIT_PARAMS
    );
	
	public static final Set<String> EXTRA_OUT_FIELDS_WAP_PAY_REQUEST_REDUCE_KEYS = CollectionUtil.hashSet(
	        // 直连银联微信 
	        "TimeStamp","PaySign","SignType","NonceStr", "PartnerId", "Package", 
	        // 直连微信
	        "timeStamp", "paySign", "signType", "nonceStr", 
	        // 支付宝app支付
	        "trade_info",  
	        // 支付宝线上资金预授权
	        "orderStr",
	        // 支付宝h5支付
	        "h5_pay_redirect_url", 
	        // 网联翼支付
	        "SERVICE", "MERCHANTID", "MERCHANTPWD", "BACKMERCHANTURL", "SIGNTYPE", "ORDERSEQ", "ORDERREQTRANSEQ",
	        "ORDERTIME", "ORDERAMOUNT", "CURTYPE", "PRODUCTID", "PRODUCTDESC", "PRODUCTAMOUNT",
	        "ATTACHAMOUNT", "BUSITYPE", "SWTICHACC", "SUBJECT", "OTHERFLOW", "SIGN",
	        // 银联开发平台-银联云闪付
	        "redirectUrl"
    );
	
    private static final Logger logger = LoggerFactory.getLogger(AbstractPayWorkflow.class);

    protected String query(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_QUERY), () -> {
            String result = context.getServiceProvider().query(context);
            logger.debug("TID {} query method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

	protected String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        String path = UpayUtil.getBizErrorCodeKey(MpayServiceProvider.OP_PAY);
        Object bizError = BeanUtil.getNestedProperty(transaction, path);
        if(context.isRCExpire()){
        	BeanUtil.setNestedProperty(transaction, path, UpayBizError.fromCode(UpayBizError.TRADE_TIMEOUT.getCode()));
        }else{
	        if (bizError == null) {
	        	if(BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) != Order.SUB_PAYWAY_BARCODE){
	        		bizError = BeanUtil.getNestedProperty(transaction, UpayUtil.getBizErrorCodeKey(MpayServiceProvider.OP_PRECREATE));
	        	}
	        	
	        	if(bizError == null || (bizError instanceof UpayBizError 
						        			&& (StringUtils.empty(((UpayBizError) bizError).getName())  
						            				|| StringUtils.empty(((UpayBizError) bizError).getStandardName()) 
						            				|| StringUtils.empty(((UpayBizError) bizError).getMessage())))){
	                BeanUtil.setNestedProperty(transaction, path, UpayBizError.fromCode(UpayBizError.TRADE_TIMEOUT.getCode()));
			    }else{
			    	BeanUtil.setNestedProperty(transaction, path, bizError);
			    }
	        }else if(bizError instanceof UpayBizError 
	        		&& (StringUtils.empty(((UpayBizError) bizError).getName())  
	        				|| StringUtils.empty(((UpayBizError) bizError).getStandardName()) 
	        				|| StringUtils.empty(((UpayBizError) bizError).getMessage()))){
	            BeanUtil.setNestedProperty(transaction, path, UpayBizError.fromCode(UpayBizError.TRADE_TIMEOUT.getCode()));
            }
        }
        
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_CANCEL), () -> {
            String result = context.getServiceProvider().cancel(context);
            logger.debug("TID {} cancel method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @SuppressWarnings("unchecked")
    @Override
    public String finish(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        int type =  BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE);
        order.put(Order.STATUS, type == Transaction.TYPE_PAYMENT ? Order.STATUS_PAID : Order.STATUS_DEPOSIT_FREEZED);
        transaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
        Map<String,Object> tradeParams = context.getServiceProvider().getTradeParams(transaction);
        // 重新计算手续费
        resetTradeFeeRate(transaction, tradeParams);
        // 重新设置境外手续费
        reCalculateOverseasFeeByPayerCurrency(transaction, tradeParams);
        // 重新设置额度减免手续费
        FeeRateProcessor.setPayQuotaFee(transaction);

        // 重新设置SPDB交易手续费
        FeeUtil.resetSPDBTradeFee(transaction, tradeParams);
        //银行卡刷卡参数初始化
        UpayUtil.initWalletAccountType(transaction);

        //重新设置tradeApp
        UpayUtil.resetTradeApp(transaction);

        Object effectiveAmount = transaction.get(Transaction.EFFECTIVE_AMOUNT);
        if (transaction.get(Transaction.PAID_AMOUNT) == null) {
            transaction.put(Transaction.PAID_AMOUNT, effectiveAmount);
        }
        if (transaction.get(Transaction.RECEIVED_AMOUNT) == null) {
            transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
        }
        PaymentUtil.updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(order, transaction);
        reduceTransaction(transaction);
        // warning 为了防止付款人信息相关字段超出数据库字段长度，导致数据库更新失败的问题，现做截取，超出字段长度的舍弃掉。
        truncTransactionOrOrderBuyerInfo(transaction);
        truncTransactionOrOrderBuyerInfo(order);
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.PAID_AMOUNT, transaction.get(Transaction.PAID_AMOUNT),
                                                                        Transaction.RECEIVED_AMOUNT, transaction.get(Transaction.RECEIVED_AMOUNT),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.PRODUCT_FLAG, transaction.get(Transaction.PRODUCT_FLAG),
                                                                        Transaction.CONFIG_SNAPSHOT, transaction.get(Transaction.CONFIG_SNAPSHOT));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.ITEMS, order.get(Order.ITEMS),
                                                                  Order.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                  Order.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                  Order.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                  Order.TOTAL_DISCOUNT, order.get(Order.TOTAL_DISCOUNT),
                                                                  Order.NET_DISCOUNT, order.get(Order.NET_DISCOUNT),
                                                                  Order.STATUS, order.get(Order.STATUS));
        
        if (BeanUtil.getPropBoolean(transaction, Transaction.KEY_IN_HBASE, false)){
            transaction.putAll(transactionUpdates);
            order.putAll(orderUpdates);
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION) + 1);
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
            orderUpdates = null;
            
            try {
                Map<String, Object> extraOut = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
                extraOut.remove(Transaction.IN_HBASE);
                transaction.put(DaoConstants.MTIME, System.currentTimeMillis());
                transaction.put(DaoConstants.VERSION, MapUtil.getIntValue(transaction, DaoConstants.VERSION) + 1);
                upayOrderService.updateTransaction(transaction);
            }catch (Exception e) {
                logger.error("update transaction hbase data error, data = {} , ex= {}", transaction, e);
            }
            transactionUpdates = null;
        }
        finishTransaction(context, orderUpdates, transactionUpdates);
        
        //fake相关数据不需要写消息队列
        if(context.isFakeRequest()) return null;
        notifyClient(context);
        
        logger.debug("TID {} finished", context.getTid());
        return null;
    }
    
    protected String safeAbort(TransactionContext context) {
        Map<String, Object> order = context.getOrder();

        order.put(Order.NET_ORIGINAL, 0L);
        order.put(Order.NET_EFFECTIVE, 0L);
        order.put(Order.NET_DISCOUNT, 0L);
        PaymentUtil.updateOrderPaymentsNetAmountToZero((List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH));
        close(context, BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE) == Transaction.TYPE_PAYMENT ? Order.STATUS_CANCELED : Order.STATUS_DEPOSIT_CANCELED);
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        logger.debug("TID {} order pay aborted", context.getTid());
        return null;
    }

    protected String safeClose(TransactionContext context) {
        Map<String, Object> order = context.getOrder();

        order.put(Order.NET_ORIGINAL, 0L);
        order.put(Order.NET_EFFECTIVE, 0L);
        order.put(Order.NET_DISCOUNT, 0L);
        PaymentUtil.updateOrderPaymentsNetAmountToZero((List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH));
        close(context, BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE) == Transaction.TYPE_PAYMENT ? Order.STATUS_PAY_CANCELED : Order.STATUS_DEPOSIT_FREEZE_CANCELED);
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        notifyClient(context);
        logger.debug("TID {} order pay failed & canceled", context.getTid());
        return null;
    }

    protected String unsafeClose(TransactionContext context) {
        close(context, BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE) == Transaction.TYPE_PAYMENT ? Order.STATUS_PAY_ERROR : Order.STATUS_DEPOSIT_FREEZE_ERROR);
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        notifyClient(context);
        logger.debug("TID {} order pay failed & closed with unexpected error", context.getTid());
        return null;
    }

    protected String queryExpireClose(TransactionContext context) {
        // 更新流水中的标识，设置订单需要进行立即勾兑
        Map extoutFields = (Map) context.getTransaction().get(Transaction.EXTRA_OUT_FIELDS);
        if(null == extoutFields){
            extoutFields = new HashMap<>();
            context.getTransaction().put(Transaction.EXTRA_OUT_FIELDS, extoutFields);
        }
        extoutFields.put(Transaction.QUERY_EXPIRE, Boolean.TRUE);
        
        close(context, BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE) == Transaction.TYPE_PAYMENT ? Order.STATUS_PAY_ERROR : Order.STATUS_DEPOSIT_FREEZE_ERROR, Transaction.STATUS_ERROR_RECOVERY);
        context.getTransaction().put(Transaction.STATUS, Transaction.STATUS_ERROR_RECOVERY);
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        notifyClient(context);
        logger.debug("TID {} order query expire closed", context.getTid());
        return null;
    }
    
    @SuppressWarnings("unchecked")
    private void close(TransactionContext context, int orderStatus, Integer transactionStatus) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        // warning 为了防止付款人信息相关字段超出数据库字段长度，导致数据库更新失败的问题，现做截取，超出字段长度的舍弃掉。
        truncTransactionOrOrderBuyerInfo(transaction);
        truncTransactionOrOrderBuyerInfo(order);

        order.put(Order.STATUS, orderStatus);

        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME));
        
        if(orderStatus == Order.STATUS_PAY_CANCELED || orderStatus == Order.STATUS_DEPOSIT_FREEZE_CANCELED 
                || orderStatus == Order.STATUS_CANCELED || orderStatus == Order.STATUS_DEPOSIT_CANCELED) {
            reduceTransaction(transaction);
            transactionUpdates.put(Transaction.CONFIG_SNAPSHOT, transaction.get(Transaction.CONFIG_SNAPSHOT));
        }
        Map extraParam = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        if (extraParam != null && extraParam.containsKey(Transaction.CANCEL_TYPE)) {
            transactionUpdates.put(Transaction.EXTRA_PARAMS, extraParam);
        }
        if(null != transactionStatus) {
            transactionUpdates.put(Transaction.STATUS, transactionStatus);
        }

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.TRADE_NO, order.get(Order.TRADE_NO),
                                                                  Order.STATUS, order.get(Order.STATUS),
                                                                  Order.NET_ORIGINAL, order.get(Order.NET_ORIGINAL),
                                                                  Order.NET_EFFECTIVE, order.get(Order.NET_EFFECTIVE),
                                                                  Order.ITEMS, order.get(Order.ITEMS),
                                                                  Order.NET_DISCOUNT, order.get(Order.NET_DISCOUNT));

        repository.getTransactionDao().updatePart(transactionUpdates);
        repository.getOrderDao().updatePart(orderUpdates);
        if(orderStatus == Order.STATUS_PAY_ERROR || orderStatus == Order.STATUS_DEPOSIT_FREEZE_ERROR) {
            tradeCacheService.updateTradeCache(context.getTerminalOrStoreSn(), BeanUtil.getPropString(context.getOrder(), Order.CLIENT_SN), orderUpdates, transactionUpdates);
        }

    }
    @SuppressWarnings("unchecked")
    private void close(TransactionContext context, int orderStatus) {
        close(context, orderStatus, MapUtil.getInteger(context.getTransaction(), Transaction.STATUS));
    }

    public static void truncTransactionOrOrderBuyerInfo(Map<String,Object> orderOrTransaction){
        String buyerUid = BeanUtil.getPropString(orderOrTransaction, Transaction.BUYER_UID);
        if(buyerUid != null && buyerUid.length() > UpayConstant.MAX_LENGHT_OF_BUYER_UID_COLUMN){
            orderOrTransaction.put(Transaction.BUYER_UID, buyerUid.substring(0, UpayConstant.MAX_LENGHT_OF_BUYER_UID_COLUMN));
        }
        String buyerLogin = BeanUtil.getPropString(orderOrTransaction, Transaction.BUYER_LOGIN);
        if(buyerLogin != null && buyerLogin.length() > UpayConstant.MAX_LENGHT_OF_BUYER_LOGIN_COLUMN){
            orderOrTransaction.put(Transaction.BUYER_LOGIN, buyerLogin.substring(0, UpayConstant.MAX_LENGHT_OF_BUYER_LOGIN_COLUMN));
        }
    }

    private void reduceTransaction(Map transaction) {
        synchronized (transaction) {
            reduceConfigSnapshot(null != transaction ? (Map)transaction.get(Transaction.CONFIG_SNAPSHOT): null);
            reduceExtraOutFields(null != transaction ? (Map)transaction.get(Transaction.EXTRA_OUT_FIELDS): null);
        }
    }

    private void reduceConfigSnapshot(Map object) {
        if(null == object) {
            return;
        }
        for (String key : CONFIG_SNAPSHOT_REDUCE_KEYS) {
            object.remove(key);
        }
    }

    private void reduceExtraOutFields(Map object) {
        if(null == object) {
            return;
        }
        if(null != object.get(Transaction.WAP_PAY_REQUEST) && object.get(Transaction.WAP_PAY_REQUEST) instanceof Map) {
            Map<String,Object> wapPayReqeust = (Map<String,Object>) object.get(Transaction.WAP_PAY_REQUEST);
            for (Object wapKey : wapPayReqeust.keySet().toArray()) {
                if(EXTRA_OUT_FIELDS_WAP_PAY_REQUEST_REDUCE_KEYS.contains(wapKey)) {
                    wapPayReqeust.remove(wapKey);
                }
            }
        }
    }

    private void resetChannelFee(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        boolean needReCalculateFee = false;
        // 交易使用了商户免充值优惠
        if (UpayUtil.isChannelNotTopUp(transaction)) {
            needReCalculateFee = true;
        }
        boolean useChannelFee = false;
        // 商户使用了资金渠道或资金渠道阶梯费率
        if (tradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null || tradeParams.get(TransactionParam.CHANNEL_LADDER_FEE_RATES) != null) {
            useChannelFee = true;
        }
        if (!needReCalculateFee && !useChannelFee) {
            return;
        }
        // 计算免充值优惠
        if (needReCalculateFee) {
            if (!useChannelFee) {
                tradeParams.put(TransactionParam.FEE, FeeUtil.reCalculatePayOrPrecreateFee(transaction));
            }
            int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            if(ApolloConfigurationCenterUtil.isMchChannelCouponSubsidy() && !UpayUtil.isFormal(transaction) 
                    && (payway == Order.PAYWAY_WEIXIN || payway == Order.PAYWAY_WEIXIN_HK)){
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_IS_MCH_CHANNEL_COUPON_SUBSIDY, true);
            }
        }
        // 计算资金通道费率
        if (useChannelFee) {
            resetChannelFeeRate(transaction, tradeParams);
        }
    }

    /**
     * 重置手续费
     *
     * @param transaction
     * @param tradeParams
     */
    public void resetTradeFeeRate(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        // 通道返回的手续费
        Long realTradeFee = MapUtil.getLong(extraOutFields, Transaction.REAL_TRADE_FEE);
        // 以通道侧返回的手续费为主
        if (Objects.nonNull(realTradeFee)) {
            // 更新前收钱吧侧计算得出手续费
            Long beforeFee = MapUtil.getLong(tradeParams, TransactionParam.FEE);
            logger.info("以通道侧返回的手续费为准 tsn={},fee=[{}->{}],provider={}",
                    BeanUtil.getPropString(transaction, Transaction.TSN), beforeFee, realTradeFee,
                    BeanUtil.getPropString(transaction, Transaction.PROVIDER));
            BeanUtil.setNestedProperty(tradeParams, TransactionParam.FEE, realTradeFee);
        } else {
            // 重新计算手续费
            resetChannelFee(transaction, tradeParams);
        }
    }

    private void resetChannelFeeRate(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        Map<String, Map<String, Object>> bankcardFeeMap = (Map<String, Map<String, Object>>) tradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE);
        Map<String, List<Map<String, Object>>> channelLadderFeeRate = (Map<String, List<Map<String, Object>>>) tradeParams.get(TransactionParam.CHANNEL_LADDER_FEE_RATES);
        if (bankcardFeeMap == null && channelLadderFeeRate == null) {
            return;
        }
        // 银行卡交易参数获取顺序 dcc/edc > credit/(debit/others)
        List<String> findTypeKey = new ArrayList<>();
        String wildCardType = UpayUtil.getWildCardType(transaction);
        if (!StringUtils.isEmpty(wildCardType)) {
            findTypeKey.add(wildCardType);
        }
        if (UpayUtil.isCreditPay(transaction)) {
            findTypeKey.add(TransactionParam.PARAMS_BANKCARD_FEE_CREDIT);
        } else {
            // debit和others 只会出现一种
            findTypeKey.add(TransactionParam.PARAMS_BANKCARD_FEE_DEBIT);
            findTypeKey.add(TransactionParam.PARAMS_BANKCARD_FEE_OTHERS);
        }
        Map<String, Object> feeRateMap = null;
        if (channelLadderFeeRate != null) {
            // 从资金渠道阶梯费率中找出需要使用的费率
            List<Map<String, Object>> useLadderFeeRates = null;
            for (String typeKey : findTypeKey) {
                if ((useLadderFeeRates = (List<Map<String, Object>>) channelLadderFeeRate.get(typeKey)) != null) {
                    break;
                }
            }
            if (com.wosai.pantheon.util.CollectionUtil.isEmpty(useLadderFeeRates)) {
                logger.warn("资金渠道阶梯费率配置错误");
                return;
            }
            long originalTotal = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            String feeRate = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE);
            long min = 0;
            long max = 0;
            for (Map<String, Object> ladderFeeRate : useLadderFeeRates) {
                min = Math.round(MapUtil.getDouble(ladderFeeRate, TransactionParam.LADDER_FEE_RATE_MIN, 0.00) * 100) ;
                max = Math.round(MapUtil.getDouble(ladderFeeRate, TransactionParam.LADDER_FEE_RATE_MAX, 0.00) * 100);
                max = (0 == max) ? Long.MAX_VALUE : max;
                if(originalTotal > min && originalTotal <= max){
                    feeRate = BeanUtil.getPropString(ladderFeeRate, TransactionParam.FEE_RATE);
                    break;
                }
            }
            logger.info("ordersn = {}, ladder_fee_rates = {}", BeanUtil.getPropString(transaction, Transaction.ORDER_SN), useLadderFeeRates);
            feeRateMap = MapUtil.hashMap(TransactionParam.FEE, feeRate);

        } else {
            for (String typeKey : findTypeKey) {
                if ((feeRateMap = (Map<String, Object>) bankcardFeeMap.get(typeKey)) != null) {
                    break;
                }
            }
            if (MapUtil.isEmpty(feeRateMap)) {
                logger.warn("资金渠道费率配置错误");
                return;
            }
        }
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean useMaxFeeFlag = false;
        //获取对应卡类型的封顶手续费配置
        long maxFee = BeanUtil.getPropLong(feeRateMap, TransactionParam.PARAMS_BANKCARD_FEE_MAX, -1);
        //获取对应卡类型的费率
        String feeRate = BeanUtil.getPropString(feeRateMap, TransactionParam.FEE);
        //修改费率
        BeanUtil.setProperty(tradeParams, TransactionParam.FEE_RATE, feeRate);

        //富友不应该由我们再去反推手续费
        if (MapUtil.getLongValue(transaction, Transaction.PROVIDER) == Provider.FUYOU.getCode()) {
            return;
        }

        String feeRateOriginal = feeRate;
        long fee = FeeUtil.reCalculatePayOrPrecreateFee(transaction, feeRate);
        //获取按照费率计算出来的手续费
        //有封顶费率时，可能需要重新计算手续费
        if (maxFee != -1) {
            if (fee > maxFee) {
                //按照封顶费率计算的话， original记录原始的卡类型费率
                feeRate = FeeUtil.calculateFeeRate(transaction, maxFee);
                fee = maxFee;
                useMaxFeeFlag = true;
            }
        }
        //如果有额度包，重新计算相关费率值
        String decreaseFeeRate = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE);
        if (!StringUtils.isEmpty(decreaseFeeRate)) {
            //如果有额度包， original记录原始的卡类型费率
            BeanUtil.setProperty(tradeParams, TransactionParam.FEE_RATE_ORIGINAL, feeRateOriginal);
            long actFeeRate = StringUtils.yuan2cents(feeRate) - StringUtils.yuan2cents(decreaseFeeRate);
            if (actFeeRate <= 0) {
                // 修改费率
                BeanUtil.setProperty(tradeParams, TransactionParam.FEE_RATE, "0.00");
                BeanUtil.setProperty(extraOutFields, Transaction.QUOTA_FEE_RATE, feeRate);
                fee = 0;
            } else {
                BeanUtil.setProperty(tradeParams, TransactionParam.FEE_RATE, StringUtils.cents2yuan(actFeeRate));
                //减免计算后的手续费
                fee = FeeUtil.reCalculatePayOrPrecreateFee(transaction);
                //计算优惠的手续费
                long decreaseFee = FeeUtil.reCalculatePayOrPrecreateFee(transaction, decreaseFeeRate);
                if(useMaxFeeFlag){
                    //修补小数点存在的误差
                    long allFee = fee + decreaseFee;
                    if(allFee < maxFee){
                        fee += (maxFee - allFee);
                    }
                }
            }
        }
        tradeParams.put(TransactionParam.FEE, fee);
        // 重新设置额度减免手续费
        FeeRateProcessor.setPayQuotaFee(transaction);
    }

    /**
     * 使用消费支付币种重新计算交易手续费
     * 
     * @param transaction
     * @param tradeParams
     */
    private void reCalculateOverseasFeeByPayerCurrency(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        Map<String, Object> channelWalletFeeRates = MapUtil.getMap(tradeParams, TransactionParam.CHANNEL_CURRENCY_FEE_RATES);
        if (MapUtil.isNotEmpty(channelWalletFeeRates)) {
            Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
            Map<String, Object> overseas = MapUtil.getMap(extraOutFields, Transaction.OVERSEAS);
            String payerCurrency = MapUtil.getString(overseas, Transaction.CURRENCY);
            String feeRate = BeanUtil.getPropString(channelWalletFeeRates, payerCurrency);
            if (!StringUtil.empty(feeRate) && !Objects.equals(feeRate, MapUtil.getString(tradeParams, TransactionParam.FEE_RATE))) {
                tradeParams.put(TransactionParam.FEE_RATE, feeRate);
                tradeParams.put(TransactionParam.FEE, FeeUtil.reCalculatePayOrPrecreateFee(transaction));
            }
        }
    }
}
