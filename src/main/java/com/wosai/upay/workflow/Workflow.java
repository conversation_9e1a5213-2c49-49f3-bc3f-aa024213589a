package com.wosai.upay.workflow;

import com.wosai.constant.ProductFlagEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.Machine;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.dao.EventLog;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.*;
import com.wosai.upay.transaction.model.Payment;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.wallet.constant.ProviderWalletAccountTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;


public abstract class Workflow implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(Workflow.class);
    private static final int LOG_PARTITION_COUNT = 2;
    public static final int DB_ERROR_RETRY_TIME = 2;
    private static final String KEY_EXT_NOTIFY_URL = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.NOTIFY_URL);
    private static final String KEY_CONFIG_NOTIFY_URL = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, Transaction.NOTIFY_URL);

    public static final StateLabel CREATED = new StateLabel(Transaction.STATUS_CREATED, "CREATED");
    public static final StateLabel SUCCESS = new StateLabel(Transaction.STATUS_SUCCESS, "SUCCESS");
    public static final StateLabel FAIL_CANCELED = new StateLabel(Transaction.STATUS_FAIL_CANCELED, "FAIL_CANCELED");
    public static final StateLabel ABORTED = new StateLabel(Transaction.STATUS_ABORTED, "ABORTED");
    public static final StateLabel QUERY_EXPIRE = new StateLabel(Transaction.STATUS_QUERY_EXPIRE, "QUERY_EXPIRE");
    
    public static final StateLabel FAIL_PROTOCOL_1 = new StateLabel(Transaction.STATUS_FAIL_PROTOCOL_1, "FAIL_PROTOCOL_1");
    public static final StateLabel FAIL_IO_1 = new StateLabel(Transaction.STATUS_FAIL_IO_1, "FAIL_IO_1");
    public static final StateLabel FAIL_PROTOCOL_2 = new StateLabel(Transaction.STATUS_FAIL_PROTOCOL_2, "FAIL_PROTOCOL_2");
    public static final StateLabel FAIL_IO_2 = new StateLabel(Transaction.STATUS_FAIL_IO_2, "FAIL_IO_2");
    public static final StateLabel FAIL_PROTOCOL_3 = new StateLabel(Transaction.STATUS_FAIL_PROTOCOL_3, "FAIL_PROTOCOL_3");
    public static final StateLabel FAIL_IO_3 = new StateLabel(Transaction.STATUS_FAIL_IO_3, "FAIL_IO_3");
    
    public static final StateLabel IN_PROG = new StateLabel(Transaction.STATUS_IN_PROG, "IN_PROG");
    public static final StateLabel ERROR_RECOVERY = new StateLabel(Transaction.STATUS_ERROR_RECOVERY, "ERROR_RECOVERY");
    public static final StateLabel ABORTING = new StateLabel(Transaction.STATUS_ABORTING, "ABORTING");
    public static final StateLabel PRE_SUCCESS = new StateLabel(Transaction.STATUS_PRE_SUCCESS, "PRE_SUCCESS");
    public static final StateLabel FAIL_ERROR = new StateLabel(Transaction.STATUS_FAIL_ERROR, "FAIL_ERROR");
    public static final StateLabel CANCEL_ERROR = new StateLabel(Transaction.STATUS_CANCEL_ERROR, "CANCEL_ERROR");
    public static final StateLabel REFUND_ERROR = new StateLabel(Transaction.STATUS_REFUND_ERROR, "REFUND_ERROR");
    public static final StateLabel CONSUME_ERROR = new StateLabel(Transaction.STATUS_CONSUME_ERROR, "CONSUME_ERROR");



    public static final String RC_PAY_SUCCESS = "pay_success";
    public static final String RC_ERROR = "error";
    public static final String RC_SYS_ERROR = "sys_error";
    public static final String RC_IOEX = "ioex";
    public static final String RC_IN_PROG = "in_prog";
    public static final String RC_PROTOCOL_ERROR = "protocol_error";
    public static final String RC_EXPIRE = "expire";
    public static final String RC_CANCEL_SUCCESS = "cancel_success";
    public static final String RC_CREATE_SUCCESS = "create_success";
    public static final String RC_REFUND_SUCCESS = "refund_success";
    public static final String RC_ABORT = "abort";
    public static final String RC_RETRY = "retry";
    public static final String RC_TRADE_NOT_EXISTS = "trade_not_exists";
    public static final String RC_TRADE_CANCELED = "trade_canceled";
    public static final String RC_CONSUME_SUCCESS = "consume_success";
    public static final String RC_CONSUME_FAIL = "consume_fail";
    public static final String RC_TRADE_DISCARD = "trade_discard";


    protected Machine machine;

    @Autowired
    protected DataRepository repository;
    
    @Autowired
    protected UuidGenerator uuidGenerator;

    @Autowired
    private WorkflowManager workflowManager;
    
    @Autowired
    protected AmqpFacade amqpFacade;
    
    @Autowired
    protected ClientNotifier clientNotifier;

    @Autowired
    protected PaymentSumService paymentSumService;
    
    @Autowired
    protected TradeCacheService tradeCacheService;
    
    @Autowired
    protected UpayOrderService upayOrderService;
    
    @Autowired
    protected GatewaySupportService gatewaySupportService;
    @Autowired
    protected DepositProvider depositProvider;
    @Autowired
    protected ExternalServiceFacade externalServiceFacade;



    @Override
    public void afterPropertiesSet() throws Exception {
        workflowManager.addWorkflow(this);
    }

    public abstract String getName();
    public abstract boolean canHandle(Map<String, Object> transaction);
    
    public Machine getMachine() {
        return machine;
    }
    
    public WorkflowManager getManager() {
        return workflowManager;
    }
    @SuppressWarnings("unchecked")
    public void persistState(TransactionContext context, boolean isEnd) {
        Map<String, Object> transaction = context.getTransaction();
        StateLabel current = context.getCurrentStateLabel();
        Exception exception = null;
        // Transaction.STATUS_QUERY_EXPIRE 只是个临时状态，不需要将此状态变更到DB中
        if (current == CREATED || (MapUtil.getIntValue(transaction, Transaction.STATUS) != current.getId() && current.getId() != Transaction.STATUS_QUERY_EXPIRE)){
            int lastTransactionStatus = MapUtil.getIntValue(transaction, Transaction.STATUS);
            Map transactionUpdate = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                    Transaction.MERCHANT_ID, MapUtil.getString(transaction, Transaction.MERCHANT_ID),
                    Transaction.BIZ_ERROR_CODE,BeanUtil.getProperty(transaction, Transaction.BIZ_ERROR_CODE),
                    Transaction.STATUS, current.getId());
            // 状态机处于结束状态时，不变更流水状态，由最后的变更任务来写入
            if (!isEnd) {
                for (int i = 0; i < DB_ERROR_RETRY_TIME; i++) {
                    try{
                        repository.getTransactionDao().updatePart(transactionUpdate);
                        exception = null;
                        break;
                    }catch (Exception e){
                        exception = e;
                        logger.error(String.format("persistState error, tid: %s, status: %d, error: %s", context.getTid(), current.getId(), e.getMessage()), e);
                    }
                }
            }
            transaction.put(Transaction.STATUS, current.getId());
            
            // 设置缓存
            //   1、b2c返回支付中或下单异常（非明确失败的场景）
            //   2、非b2c返回支付中
            // 更新缓存
            //   1、之前有设置缓存
            // 删除缓存
            //   1、交易成功后，请求有提前返回动作（订单处于支付中时，会提前返回）
            //   2、交易经过多次查询或主动撤单后，订单明确失败
            int type = MapUtil.getIntValue(context.getTransaction(), Transaction.TYPE);
            if(type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_DEPOSIT_FREEZE) {
                if(current == PRE_SUCCESS) {
                    // 支付返回交易成功
                    // 支付通道下单后接口提前返回，之前有设置缓存，需要清理缓存
                    if(context.isCache()) {
                        tradeCacheService.removeTradeCache(context.getTerminalOrStoreSn(), MapUtil.getString(transaction, Transaction.CLIENT_TSN), MapUtil.getString(transaction, Transaction.ORDER_SN));
                    }
                    
                }else if(lastTransactionStatus == Transaction.STATUS_CREATED) {
                    // 支付通道下单后，流水状态不是明确失败时，需要设置缓存
                    if(current != FAIL_CANCELED && current != ABORTED) {
                        tradeCacheService.putTradeCache(context.getTerminalOrStoreSn(), MapUtil.getString(transaction, Transaction.CLIENT_TSN), context);
                    }
                    
                } else if (current == FAIL_CANCELED || current == ABORTED){
                	// 查单/主动撤单后，交易明确失败，需要清理缓存
                    if(context.isCache()) {
                        tradeCacheService.removeTradeCache(context.getTerminalOrStoreSn(), MapUtil.getString(transaction, Transaction.CLIENT_TSN), MapUtil.getString(transaction, Transaction.ORDER_SN));
                    }
                    
                } else if(context.isCache()){
                    // 查单后交易仍处于中间状态，需要更新缓存
                    tradeCacheService.updateTradeCache(context.getTerminalOrStoreSn(), MapUtil.getString(transaction, Transaction.CLIENT_TSN), null, transactionUpdate);
                }
            }
        }
        if(exception != null){
            throw new RuntimeException(exception.getCause());
        }
        
        if (context.getCurrentState().isEnd()) {
        	transaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
        }
    }
    
    public boolean matchState(TransactionContext context) {
        String merchantId = MapUtil.getString(context.getTransaction(), Transaction.MERCHANT_ID);
        Map<String, Object> existing = repository.getTransactionDao().filter(Criteria.where(DaoConstants.ID).is(context.getTid()).with(Transaction.MERCHANT_ID).is(merchantId), CollectionUtil.hashSet(Transaction.STATUS)).fetchOne();
        int statusNow = MapUtil.getIntValue(existing, Transaction.STATUS);
        return context.getCurrentStateLabel().getId() == statusNow;
    }

    public abstract String explainNotification(TransactionContext context, Map<String, Object> notification);

    public abstract String finish(TransactionContext context);


    @SuppressWarnings("unchecked")
    protected void finishTransaction(TransactionContext context,
                                     Map<String, Object> orderUpdates,
                                     Map<String, Object> transactionUpdates) {
        Integer provider = context.getServiceProvider().getProvider();
        boolean disableLog = Objects.equals(provider, Order.PROVIDER_FUYOU) || context.asyncWalletLog();
        finishTransaction0(context, orderUpdates, transactionUpdates, disableLog);
    }

    /**
     * 流水完成处理
     *
     * @param context            　上下文
     * @param orderUpdates       　订单待更新信息
     * @param transactionUpdates 　流水待更新信息
     * @param disableLog         　是否不记log
     */
    @SuppressWarnings("unchecked")
    protected void finishTransaction0(TransactionContext context,
                                     Map<String, Object> orderUpdates,
                                     Map<String, Object> transactionUpdates,
                                     boolean disableLog) {
        Map<String, Object> transaction = context.getTransaction();
        retrySaveFinishTransaction(context, orderUpdates, transactionUpdates, disableLog);
        //处理商户、门店限额
        processPaymentSum(context);

        //fake相关数据不需要写消息队列
        if(context.isFakeRequest()) return;
        try{
            amqpFacade.notifyScoreService(context.getOrder(), transaction);
        }catch(Exception ex){
            logger.error("TID {} failed to notify score-service over amqp", context.getTid(), ex);
        }
    }

    private void processPaymentSum(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = context.getServiceProvider().getTradeParams(transaction);
        String storeId = (String)transaction.get(Transaction.STORE_ID);
        String merchantId = (String)transaction.get(Transaction.MERCHANT_ID);
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        String subPayway = MapUtil.getString(transaction, Transaction.SUB_PAYWAY);
        long finishTime = MapUtil.getLongValue(transaction, Transaction.FINISH_TIME);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(config, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        int provider = MapUtil.getIntValue(transaction, Transaction.PROVIDER);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        List<PaymentSumService.Increment> increments = new ArrayList<>();

        long amount = MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT);
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_DEPOSIT_FREEZE == type || Transaction.TYPE_DEPOSIT_CANCEL == type) {
            //预授权和预授权撤销不计入限额
            return;
        }
        if (type == Transaction.TYPE_CANCEL || type == Transaction.TYPE_REFUND) {
            amount = -amount;
        }

        if (liquidationNextDay) {
            //累计银行卡限额
            if (payway == Order.PAYWAY_BANKCARD) {
                if (provider == TradeConfigService.LAKALA_UNION_PAY_V3) {
                    List<PaymentSumService.Increment> bankcardPaymentSumIncrements = Arrays.asList(
                            new PaymentSumService.Increment(PaymentSumService.DAILY_LIMIT_BANKCARD + merchantId, finishTime, amount, PaymentSumService.TYPE_DAY),
                            new PaymentSumService.Increment(PaymentSumService.MONTH_LIMIT_BANKCARD + merchantId, finishTime, amount, PaymentSumService.TYPE_MONTH)
                    );
                    increments.addAll(bankcardPaymentSumIncrements);
                }
            } else {
                //累计扫码门店 商户限额
                List<PaymentSumService.Increment> dailyPaymentSumIncrements = Arrays.asList(
                        new PaymentSumService.Increment(storeId, finishTime, amount),
                        new PaymentSumService.Increment(merchantId, finishTime, amount));
                increments.addAll(dailyPaymentSumIncrements);

            }
        }
        //累计 payway 限额
        if (liquidationNextDay) {
            List<PaymentSumService.Increment> dailyPaymentSumIncrements = Arrays.asList(
                    new PaymentSumService.Increment(StringUtils.join(merchantId, ":", payway, ":"), finishTime, amount),
                    new PaymentSumService.Increment(StringUtils.join(merchantId, ":", payway, ":", subPayway), finishTime, amount)
            );
            increments.addAll(dailyPaymentSumIncrements);

            Map<String, Object> extraParams = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_PARAMS);
            String sqbWalletName = MapUtil.getString(extraParams, Transaction.SQB_WALLET_NAME);

            //云闪付境外，累计限额
            if ((payway == Payway.UNIONPAY.getCode() && ApolloConfigurationCenterUtil.getUnionOverseasWallet().contains(sqbWalletName))) {
                increments.add(new PaymentSumService.Increment(StringUtils.join(merchantId, ":", payway +":" + TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT), finishTime, amount));
            }
        }
        //累计信用限额
        try {
            Map<String, Object> extraOutField = (Map<String, Object>) MapUtil.getObject(transaction, com.wosai.upay.transaction.model.Transaction.EXTRA_OUT_FIELDS);
            List payments = (List) MapUtil.getObject(extraOutField, com.wosai.upay.transaction.model.Transaction.PAYMENTS);
            String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG, "");
            boolean isCredit = isCreditTransaction(payments, productFlag);
            if (isCredit) {
                //商户日信用限额
                String merchantDayCreditLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_DAILY_MAX_CREDIT_LIMIT_TRANS);
                handleCreditTransaction(merchantDayCreditLimit, PaymentSumService.DAILY_LIMIT_PREFIX + merchantId, PaymentSumService.TYPE_DAY, amount, extraOutField, increments, Transaction.IS_FIRST_MORE_THAN_CREDIT_DAY_LIMIT);

                //商户月信用限额
                String merchantMonthCreditLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_MONTHLY_MAX_CREDIT_LIMIT_TRANS);
                handleCreditTransaction(merchantMonthCreditLimit, PaymentSumService.MONTHLY_LIMIT_PREFIX + merchantId, PaymentSumService.TYPE_MONTH, amount, extraOutField, increments, Transaction.IS_FIRST_MORE_THAN_CREDIT_MONTH_LIMIT);

                //商户payway 日/月 信用限额
                String dayKey = "";
                String monthKey = "";
                int creditPayway = payway;
                //支付宝payway两个值，底层只存2
                if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
                    creditPayway = Order.PAYWAY_ALIPAY2;
                    dayKey = Transaction.IS_FIRST_MORE_THAN_ALIPAY_CREDIT_DAY_LIMIT;
                    monthKey = Transaction.IS_FIRST_MORE_THAN_ALIPAY_CREDIT_MONTH_LIMIT;
                } else if (payway == Order.PAYWAY_WEIXIN) {
                    dayKey = Transaction.IS_FIRST_MORE_THAN_WECHAT_CREDIT_DAY_LIMIT;
                    monthKey = Transaction.IS_FIRST_MORE_THAN_WECHAT_CREDIT_MONTH_LIMIT;
                }

                Map paywayDayCreditLimits = MapUtil.getMap(configSnapshot, TransactionParam.PAYWAY_DAY_CREDIT_LIMITS);
                String paywayDayCreditLimit = MapUtil.getString(paywayDayCreditLimits, creditPayway + "");
                handleCreditTransaction(paywayDayCreditLimit, PaymentSumService.DAILY_LIMIT_PAYWAY_PREFIX + merchantId + "|" + creditPayway, PaymentSumService.TYPE_DAY, amount, extraOutField, increments, dayKey);

                Map paywayMonthCreditLimits = MapUtil.getMap(configSnapshot, TransactionParam.PAYWAY_MONTH_CREDIT_LIMITS);
                String paywayMonthCreditLimit = MapUtil.getString(paywayMonthCreditLimits, creditPayway + "");
                handleCreditTransaction(paywayMonthCreditLimit, PaymentSumService.MONTHLY_LIMIT_PAYWAY_PREFIX + merchantId + "|" + creditPayway, PaymentSumService.TYPE_MONTH, amount, extraOutField, increments, monthKey);
            }
        } catch (Exception e) {
            logger.error("deal with credit trade error, reason:", e);
        }

        String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP);
        if (Objects.equals(tradeApp, ApolloConfigurationCenterUtil.getPhonePosTradeAppId())) {
            increments.add(new PaymentSumService.Increment(PaymentSumService.DAILY_LIMIT_PHONE_POS_PREFIX + merchantId, finishTime, amount, PaymentSumService.TYPE_DAY));
        }
        //交易限额累加
        try {
            paymentSumService.increment(increments);
        } catch (Exception e) {
            logger.error("TID {} failed to write redis", context.getTid(), e);
        }
    }

    /**
     * 保存完成交易流水
     *
     * @param context
     * @param orderUpdates
     * @param transactionUpdates
     * @param disableLog         不写log
     */
    public void retrySaveFinishTransaction(TransactionContext context, Map<String, Object> orderUpdates, Map<String, Object> transactionUpdates, boolean disableLog) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = context.getServiceProvider().getTradeParams(context.getTransaction());
        Map<String, Object> log = null;
        if (isWrittenLog(context, disableLog)) {
            log = buildLogMap(context, transaction, config);
        }
        Exception exception = null;
        for (int i = 0; i < DB_ERROR_RETRY_TIME; i++) {
            try {
                repository.finishTransaction(orderUpdates, transactionUpdates, log, i);
                exception = null;
                break;
            }catch (Exception e){
                exception = e;
                logger.error(String.format("finishTransaction error, tid: %s, status: %d, error: %s", context.getTid(), Transaction.STATUS_SUCCESS, e.getMessage()), e);
            }
        }
        if(exception != null){
            throw new RuntimeException(exception.getCause());
        }
    }

    /**
     * 生成余额日志
     *
     * @param context
     * @param transaction
     * @param tradeParams
     * @return
     */
    private Map<String, Object> buildLogMap(TransactionContext context, Map<String, Object> transaction, Map<String, Object> tradeParams) {
        Map<String, Object> log;
        List<Map<String,Object>> wosaiPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH);
        List<Map<String,Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        List<Map<String,Object>> payments = new ArrayList<>();
        if(wosaiPayments != null && wosaiPayments.size() > 0){
            payments.addAll(wosaiPayments);
        }
        if(channelPayments != null && channelPayments.size() > 0){
            payments.addAll(channelPayments);
        }
        int walletAccountType = BeanUtil.getPropInt(transaction, Transaction.KEY_WALLET_ACCOUNT_TYPE, ProviderWalletAccountTypeEnum.DEFAULT.getValue());
        log = CollectionUtil.hashMap(DaoConstants.ID, transaction.get(Transaction.TSN),
                EventLog.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                EventLog.PARTITION, Math.abs(transaction.get(Transaction.MERCHANT_ID).hashCode()%LOG_PARTITION_COUNT),
                EventLog.TYPE, transaction.get(Transaction.TYPE),
                EventLog.PROCESSED, false,
                EventLog.PAYLOAD, CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                        Transaction.TYPE,  transaction.get(Transaction.TYPE),
                        Transaction.ORIGINAL_AMOUNT, transaction.get(Transaction.ORIGINAL_AMOUNT),
                        Transaction.EFFECTIVE_AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT),
                        Transaction.PAYMENTS, payments,
                        Transaction.PAID_AMOUNT, transaction.get(Transaction.PAID_AMOUNT),
                        Transaction.RECEIVED_AMOUNT, transaction.get(Transaction.RECEIVED_AMOUNT),
                        Transaction.STORE_ID, transaction.get(Transaction.STORE_ID),
                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                        TransactionParam.FEE_RATE, tradeParams.get(TransactionParam.FEE_RATE),
                        TransactionParam.FEE, tradeParams.get(TransactionParam.FEE),
                        TransactionParam.LIQUIDATION_NEXT_DAY, true,
                        com.wosai.upay.wallet.model.EventLog.ACCOUNT_TYPE, walletAccountType, //账户类型
                        TransactionParam.CLEARANCE_PROVIDER, ((Map) transaction.get(Transaction.CONFIG_SNAPSHOT)).get(TransactionParam.CLEARANCE_PROVIDER)));

        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
            log.put(Transaction.TYPE, Transaction.TYPE_PAYMENT);
        }

        Boolean isMchChannelCouponSubsidy =  (Boolean) BeanUtil.getNestedProperty(transaction, Transaction.KEY_IS_IS_MCH_CHANNEL_COUPON_SUBSIDY);
        if(isMchChannelCouponSubsidy != null){
            BeanUtil.setNestedProperty(log, EventLog.PAYLOAD + "." + EventLog.PAYLOAD_IS_MCH_CHANNEL_COUPON_SUBSIDY, isMchChannelCouponSubsidy);
        }
        return log;
    }

    //判断是否是信用交易。
    private boolean isCreditTransaction(List<Map> payments, String productFlag) {
        if (payments == null || payments.size() == 0) {
            return false;
        }
        for (Map payment : payments) {
            String originType = MapUtil.getString(payment, Payment.ORIGIN_TYPE, "");
            String type = MapUtil.getString(payment, Payment.TYPE, "");
            //信用卡
            if (type.equals(Payment.TYPE_BANKCARD_CREDIT)) {
                return true;
            } else if (type.equals(Payment.TYPE_CUSTOM_ALIPAY_HUABEI) || originType.equals(AlipayV2ServiceProvider.FC_PCREDIT)) {
                return true;
            } else if ((type.equals(Payment.TYPE_WALLET_ALIPAY) || originType.equals(AlipayV2ServiceProvider.FC_ALIPAYACCOUNT) )
                    && (productFlag.contains(ProductFlagEnum.HUABEI.getCode()) || productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode()))) {
                return true;
            }
        }
        return false;
    }

    private void handleCreditTransaction(String merchantCreditLimit, String bizTypePrefix, int type, long amount, Map extraOutField, List<PaymentSumService.Increment> increments, String key) {
        if (!Objects.isNull(merchantCreditLimit)) {
            long creditLimit = StringUtils.yuan2cents(merchantCreditLimit);
            long todayCreditTotal = paymentSumService.get(bizTypePrefix, System.currentTimeMillis(), type);
            //之前没超过，之后超过，那么就需要发送商户日志
            if (todayCreditTotal + amount >= creditLimit && todayCreditTotal < creditLimit) {
                //今日小于 流水新增字段，下游发送商户日志和app日志。
                extraOutField.put(key, true);
            }
            //redis累加
            increments.add(new PaymentSumService.Increment(bizTypePrefix, System.currentTimeMillis(), amount, type));
        }
    }

    /**
     * 是否记余额log
     *
     * @param context    上下文
     * @param disableLog 强制不写log
     * @return
     */
    private boolean isWrittenLog(TransactionContext context, boolean disableLog) {
        if (disableLog) {
            return false;
        }
        Map<String, Object> transaction = context.getTransaction();
        int transType = MapUtil.getIntValue(transaction, Transaction.TYPE);
        //预授权、授授权撤销不记log
        if (transType == Transaction.TYPE_DEPOSIT_FREEZE || transType == Transaction.TYPE_DEPOSIT_CANCEL) {
            return false;
        }
        MpayServiceProvider serviceProvider = context.getServiceProvider();
        Map<String, Object> tradeParams = serviceProvider.getTradeParams(transaction);
        return BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
    }

    protected void notifyClient(TransactionContext context) {
        if(context.isFakeRequest()) return;
        Map<String, Object> transaction = context.getTransaction();
        String notifyUrl = BeanUtil.getPropString(transaction, KEY_EXT_NOTIFY_URL);
        if (notifyUrl != null) {
            clientNotifier.notify(notifyUrl, context);
        }else{
            notifyUrl = BeanUtil.getPropString(transaction, KEY_CONFIG_NOTIFY_URL);
            int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
            if (notifyUrl != null && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI || subPayway == Order.SUB_PAYWAY_H5)) {
                clientNotifier.notify(notifyUrl, context);
            }
        }
    }
}
