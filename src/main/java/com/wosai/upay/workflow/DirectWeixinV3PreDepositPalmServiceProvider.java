package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.MapUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

import java.util.Map;
import java.util.Objects;


/**
 * 微信刷掌预售权支付
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2023/8/14.
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 2)
public class DirectWeixinV3PreDepositPalmServiceProvider extends DirectWeixinV3PreDepositServiceProvider{

    public static final String NAME = "provider.weixin.wapOrMini.v3.palm";

    public static final String PATH_OUT_ORDER_NO = "out-order-no";
    public static final String PATH_CLOSE = "close";
    public static final String PATH_COMPLETE = "complete";

    @Override
    public boolean isPalmService() {
        return true;
    }

    @Override
    public String getName() {
        return NAME;
    }

    public boolean canHandle(Map<String, Object> transaction) {
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(!Transaction.TYPE_DEPOSIT_SET.contains(type)){
            return false;
        }
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if (subPayway != Order.SUB_PAYWAY_MINI && subPayway != Order.SUB_PAYWAY_APP && subPayway != Order.SUB_PAYWAY_H5) {
            return false;
        }
        Map<String, Object> tradeParams = getTradeParams(transaction);
        //区分商户和服务商, 默认商户
        boolean modeMatch = TransactionParam.SERVICE_MODE_MERCHANT == BeanUtil.getPropInt(tradeParams, TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_MERCHANT);
        return Objects.nonNull(tradeParams) && modeMatch && matchProductCode(transaction, tradeParams, TransactionParam.SQB_PRODUCT_CODE_PALM_SERVICE);
    }

    @Override
    public String depositSync(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        //采用关单接口来实现同步, 设置固定取消原因
        extended.put(BusinessFields.REASON, WeixinConstants.REASON_PAYMENT_RECEIVED);
        String result = this.depositCancel(context);
        return Workflow.RC_CANCEL_SUCCESS.equals(result) ? Workflow.RC_CONSUME_SUCCESS : Workflow.RC_CONSUME_FAIL;
    }

    @Override
    protected RequestBuilder buildPreFreezeRequest(TransactionContext context, Map<String, Object> transaction, Map<String, Object> config) {
        RequestBuilder builder = getDefaultRequestBuilder(context);
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(BusinessFields.OPEN_ID, payerUid);
        return builder;
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        //刷掌对接口参数限制比较严格，不能多传每个接口未定义的字段。
        RequestBuilder builder = new RequestBuilder();
        return builder;
    }

    @Override
    public String getDepositQueryUrl(Map<String, Object> transaction) {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_QUERY);
        String orderSn = MapUtils.getString(transaction, Transaction.ORDER_SN);
        return url + URL_INTERVAL + PATH_OUT_ORDER_NO + URL_INTERVAL + orderSn;

    }

    public String getDepositCancelUrl(Map<String, Object> transaction){
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CANCEL);
        return url + URL_INTERVAL + PATH_CLOSE;
    }

    /**
     * 获取完成url
     * @param transaction
     * @return
     */
    public String getDepositConsumeUrl(Map<String, Object> transaction){
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CONSUME);
        return url + URL_INTERVAL + PATH_COMPLETE;
    }

}
