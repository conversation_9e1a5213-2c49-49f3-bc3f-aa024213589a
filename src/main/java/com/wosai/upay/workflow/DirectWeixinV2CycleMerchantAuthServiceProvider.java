package com.wosai.upay.workflow;

import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @description 微信周期代扣款 商户模式签约服务
 * @link <a href="https://pay.weixin.qq.com/doc/v2/merchant/**********">微信周期代扣费签约相关文档</a>
 * @date 2025-05-10
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 2)
public class DirectWeixinV2CycleMerchantAuthServiceProvider extends DirectWeixinV2CyclePartnerAuthServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(DirectWeixinV2CycleMerchantAuthServiceProvider.class);
    public static final String NAME = "provider.weixin.cycle.v2.merchant.auth";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean isMatch(Map<String, Object> request, Map<String, Object> tradeConfig) {
        //是否商户模式
        Map<String, Object> tradeParams = getSubPaywayTradeParams(request, tradeConfig);
        int serviceMode = MapUtil.getIntValue(tradeParams, TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_PARTNER);
        return TransactionParam.SERVICE_MODE_MERCHANT == serviceMode;
    }

    @Override
    protected Map<String, Object> getDefaultRequestParams(Map<String, Object> tradeConfig) {
        Map<String, Object> requestParams = new HashMap<>(8);
        requestParams.put(ProtocolFields.MCH_ID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_SUB_MCH_ID));
        return requestParams;
    }

    @Override
    protected void processExtendedParams(Map<String, Object> extended) {
        //对外接口要兼容商户和服务商模式。在商户模式下，上传给微信的参数，要将sub_app_id改为app_id
        String subAppId = (String) extended.remove(ProtocolFields.SUB_APP_ID);
        extended.put(ProtocolFields.APP_ID, subAppId);
    }
}
