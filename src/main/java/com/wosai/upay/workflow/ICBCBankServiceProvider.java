package com.wosai.upay.workflow;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Charsets;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.icbc.*;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/***
 * @ClassName: ICBCBankServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 6:41 PM
 */
public class ICBCBankServiceProvider extends AbstractServiceProvider{

    public static final Logger logger = LoggerFactory.getLogger(ICBCBankServiceProvider.class);
    public static final String NAME = "provider.icbcbank";
    private static final String OP_PRECREATE_QUERY = "precreate.query";
    private static final String OP_PRECREATE_REFUND = "precreate.refund";
    private static final String OP_PRECREATE_REFUND_QUERY = "precreate.refund.query";
    private static final String SUB_APPID = "sub_appid";
    private String notifyHost;
    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(ICBCConstants.DATE_SIMPLE_FORMAT);
    private static final SafeSimpleDateFormat dateTimeSimpleFormat = new SafeSimpleDateFormat(ICBCConstants.DATE_TIME_SIMPLE_FORMAT);
    private static final long defaultTimeExpire = DEFAULT_TIME_EXPIRE_MINUTE * 60;

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    public ICBCBankServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(ICBCConstants.DATE_TIME_FORMAT_WITH_T);
        extendedFilterFields = new HashSet<String>(Arrays.asList(ICBCBusinessFields.ORDER_AMT, ICBCBusinessFields.FEE_TYPE, ICBCBusinessFields.TOTAL_FEE));
    }

    @Autowired
    protected ICBCClient icbcClient;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_ICBCBANK;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return !Objects.isNull(getTradeParams(transaction));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ICBC_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PRIVATE_KEY);
        String icbcPublicKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PUBLIC_KEY);

        ICBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //二维码
        builder.bizContentSet(ICBCBusinessFields.QR_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        //商户订单号
        builder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //使用订单ctime, 否则在跨日临界点会出现 请求日期是20231220， 但是订单生成日期是20231219 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        //日期 yyMMdd
        builder.bizContentSet(ICBCBusinessFields.TRADE_DATE, dateSimpleFormat.format(new Date(orderCtime)));
        //时间 HHmmss
        builder.bizContentSet(ICBCBusinessFields.TRADE_TIME, dateTimeSimpleFormat.format(new Date(orderCtime)));
        //订单金额
        builder.bizContentSet(ICBCBusinessFields.ORDER_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        if (Order.PAYWAY_WEIXIN == payway) {
            //sub_appid
            builder.bizContentSet(ICBCBusinessFields.SUB_APP_ID, MapUtil.getString(tradeParams, TransactionParam.ICBC_WX_SUB_APPID));
        }
        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(icbcPublicKey), 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call icbcbank pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayAndQueryResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        int subpayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PRIVATE_KEY);
        String icbcPublicKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PUBLIC_KEY);

        ICBCRequestBuilder builder = getDefaultRequestBuilder(context);
        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        //商户订单号
        builder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //查询根据 subpayway 区分被扫和主扫查询
        if (Order.SUB_PAYWAY_BARCODE == subpayway) {
            long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
            //日期 yyMMdd
            builder.bizContentSet(ICBCBusinessFields.TRADE_DATE, dateSimpleFormat.format(new Date(orderCtime)));
        } else {
            //操作标志
            builder.bizContentSet(ICBCBusinessFields.DEAL_FLAG, ICBCConstants.DEAL_FLAG_QUERY);
            //商户在工行API平台的APPID
            String appId = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_APP_ID);
            builder.bizContentSet(ICBCBusinessFields.ICBC_APPID, appId);
            serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE_QUERY);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(serviceUrl, builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(icbcPublicKey), 1, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call icbcbank query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildPayAndQueryResult(result, context);
    }

    private String buildPayAndQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Map<String, Object> transaction = context.getTransaction();
        int subpayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        String returnCode = MapUtils.getString(result, ICBCResponseFields.RETURN_CODE);//返回的响应码
        String payStatus = MapUtils.getString(result, ICBCResponseFields.PAY_STATUS);//交易结果标志

        setTradeNoBuyerInfoIfExists(result, context);
        if (ICBCConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
            //查询根据 subpayway 区分被扫和主扫查询
            if (Order.SUB_PAYWAY_BARCODE == subpayway) {
                if (ICBCConstants.PAY_STATUS_SUCCESS.equals(payStatus)) {
                    //付款成功
                    context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                    resolvePayFund(context, result);
                    return Workflow.RC_PAY_SUCCESS;
                } else if (ICBCConstants.PAY_STATUS_PROCESSING.equals(payStatus)) {
                    return Workflow.RC_IN_PROG;
                }
            } else {
                if (ICBCConstants.PRECREATE_PAY_STATUS_SUCCESS.equals(payStatus)) {
                    //付款成功
                    context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                    resolvePayFund(context, result);
                    return Workflow.RC_PAY_SUCCESS;
                } else if (ICBCConstants.PRECREATE_PAY_STATUS_UNKNOWN.equals(payStatus)) {
                    return Workflow.RC_IN_PROG;
                }
            }
            return Workflow.RC_TRADE_CANCELED;
        } else if (ICBCConstants.PROTOCOL_ERROR_CODE_LIST.contains(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (ICBCConstants.UNKNOWN_ERROR_CODE_LIST.contains(returnCode)) {
            //业务出现未知错误或者系统异常或者请求重试需要继续查询
            return Workflow.RC_IN_PROG;
        }else if (ICBCConstants.FAIL_ERROR_CODE_LIST.contains(returnCode)){
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        int subpayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PRIVATE_KEY);
        String icbcPublicKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PUBLIC_KEY);

        ICBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        //退款根据 subpayway 区分被扫和主扫退款
        if (Order.SUB_PAYWAY_BARCODE == subpayway) {
            //退款编号
            builder.bizContentSet(ICBCBusinessFields.REJECT_NO, MapUtil.getString(transaction, Transaction.TSN));
            //退款金额，单位：分
            builder.bizContentSet(ICBCBusinessFields.REJECT_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
            //退货的发起人员信息 (可选)
            builder.bizContentSet(ICBCBusinessFields.OPER_ID, MapUtil.getString(transaction, Transaction.OPERATOR));
        } else {
            //商户在工行API平台的APPID
            String appId = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_APP_ID);
            builder.bizContentSet(ICBCBusinessFields.ICBC_APPID, appId);
            //退货流水号
            builder.bizContentSet(ICBCBusinessFields.OUTTRX_SERIAL_NO, MapUtil.getString(transaction, Transaction.TSN));
            //退款金额
            builder.bizContentSet(ICBCBusinessFields.RET_TOTAL_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
            //交易币种，目前只支持人民币，送001
            builder.bizContentSet(ICBCBusinessFields.TRNSC_CCY, ICBCConstants.FEE_TYPE_CNY);
            serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE_REFUND);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(serviceUrl, builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(icbcPublicKey), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call icbcbank refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundAndQueryResult(result, context, OP_REFUND);
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        int subpayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PRIVATE_KEY);
        String icbcPublicKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PUBLIC_KEY);

        ICBCRequestBuilder builder = getDefaultRequestBuilder(context);

        //商户订单号
        builder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        String serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        //退款查询根据 subpayway 区分被扫和主扫退款查询
        if (Order.SUB_PAYWAY_BARCODE == subpayway) {
            //退款编号
            builder.bizContentSet(ICBCBusinessFields.REJECT_NO, MapUtil.getString(transaction, Transaction.TSN));
        } else {
            //退货流水号
            builder.bizContentSet(ICBCBusinessFields.OUTTRX_SERIAL_NO, MapUtil.getString(transaction, Transaction.TSN));
            serviceUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE_REFUND_QUERY);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(serviceUrl, builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(icbcPublicKey), 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call icbcbank refund query", e);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, e);
            if(e instanceof MpayApiSendError || e instanceof MpayApiReadError) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
        return buildRefundAndQueryResult(result, context, OP_REFUND_QUERY);
    }

    public String buildRefundAndQueryResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        int subpayway = MapUtil.getIntValue(context.getTransaction(), Transaction.SUB_PAYWAY);
        //响应码
        String returnCode = MapUtils.getString(result, ICBCResponseFields.RETURN_CODE);//返回的响应码
        if (ICBCConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
            //被扫退款和退款查询根据 returnCode 判断退款结果； 主扫退款和退款查询根据 returnCode 和 pay_status 判断退款结果
            if (Order.SUB_PAYWAY_BARCODE == subpayway) {
                //退款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                context.getTransaction().put(Transaction.TRADE_NO, MapUtils.getString(result, ICBCResponseFields.ORDER_ID));//商户通道退款订单号
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            } else {
                if (OP_REFUND.equals(op)){
                    return refundQuery(context);
                }
                String payStatus = MapUtils.getString(result, ICBCResponseFields.PAY_STATUS);//交易结果标志
                if (ICBCConstants.PRECREATE_REFUND_STATUS_SUCCESS.equals(payStatus)) {
                    //退款成功
                    context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                    context.getTransaction().put(Transaction.TRADE_NO, MapUtils.getString(result, ICBCResponseFields.ORDER_ID));//商户通道退款订单号
                    resolveRefundFund(context);
                    return Workflow.RC_REFUND_SUCCESS;
                } else if (ICBCConstants.PRECREATE_REFUND_STATUS_UNKNOWN.equals(payStatus)) {
                    return Workflow.RC_IN_PROG;
                }
            }
        } else if (ICBCConstants.PROTOCOL_ERROR_CODE_LIST.contains(returnCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (ICBCConstants.UNKNOWN_ERROR_CODE_LIST.contains(returnCode)) {
            //业务出现未知错误或者系统异常或者请求重试需要继续查询
            if (OP_REFUND.equals(op)){
                return refundQuery(context);
            }
            return Workflow.RC_IN_PROG;
        }else if (ICBCConstants.FAIL_ERROR_CODE_LIST.contains(returnCode)){
            return Workflow.RC_ERROR;
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_APP_ID);
        String merId = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_MER_ID);
        String merPrtclNo = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_MER_PRTCL_NO);
        String termIp = !StringUtils.isEmpty(BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP)) ? BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP) : UpayUtil.getLocalHostIp();

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PRIVATE_KEY);
        String icbcPublicKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PUBLIC_KEY);

        ICBCRequestBuilder builder = getDefaultRequestBuilder(context);

        //商户订单号
        builder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, MapUtils.getString(transaction, Transaction.ORDER_SN));
        //收单产品协议编号 若收单产品协议编号为空，则使用商户、部门编号代替
        builder.bizContentSet(ICBCBusinessFields.MER_PRTCL_NO, StringUtils.isEmpty(merPrtclNo) ? merId : merPrtclNo);
        //使用订单ctime, 否则在跨日临界点会出现 请求日期是20231220， 但是订单生成日期是20231219 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        //交易日期时间，格式为yyyy-MM-dd'T'HH:mm:ss
        builder.bizContentSet(ICBCBusinessFields.ORIG_DATE_TIME, dateFormat.format(new Date(orderCtime)));
        //订单有效时间 秒数
        builder.bizContentSet(ICBCBusinessFields.EXPIRE_TIME, String.valueOf(defaultTimeExpire));
        //设备号
        builder.bizContentSet(ICBCBusinessFields.DEVICE_INFO, BeanUtil.getPropString(transaction, Transaction.KEY_TERMINAL_SN));
        //商品描述
        builder.bizContentSet(ICBCBusinessFields.BODY, MapUtils.getString(transaction, Transaction.SUBJECT));
        //交易币种
        builder.bizContentSet(ICBCBusinessFields.FEE_TYPE, ICBCConstants.FEE_TYPE_CNY);
        //用户端IP
        //由于工行暂时不支持ipv6,故上送127.0.0.1
        if (ApolloConfigurationCenterUtil.cutICBCIpv6() && termIp.length() > 16) {
            termIp = "127.0.0.1";
        }
        builder.bizContentSet(ICBCBusinessFields.SPBILL_CREATE_IP, termIp);
        //订单金额，单位为分
        builder.bizContentSet(ICBCBusinessFields.TOTAL_FEE, MapUtils.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        //商户在工行API平台的APPID
        builder.bizContentSet(ICBCBusinessFields.ICBC_APPID, appId);
        //异步通知商户URL
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.bizContentSet(ICBCBusinessFields.MER_URL, notifyUrl);
        //通知类型
        builder.bizContentSet(ICBCBusinessFields.NOTIFY_TYPE, ICBCConstants.NOTIFY_TYPE_HS);
        //结果发送类型
        builder.bizContentSet(ICBCBusinessFields.RESULT_TYPE, ICBCConstants.RESULT_TYPE_SUCCESS_NOTIFY);

        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        if (Order.PAYWAY_ALIPAY2 == payway) {
            //支付方式
            builder.bizContentSet(ICBCBusinessFields.PAY_MODE, ICBCConstants.PAY_MODE_ZFB);
            //收单接入方式
            builder.bizContentSet(ICBCBusinessFields.ACCESS_TYPE, ICBCConstants.ACCESS_TYPE_ZFB);
            //第三方用户标识
            builder.bizContentSet(ICBCBusinessFields.UNION_ID, payerUid);
        } else if (Order.PAYWAY_WEIXIN == payway) {
            int subpayway = MapUtil.getIntValue(context.getOrder(), Order.SUB_PAYWAY);
            //微信开放平台注册的APPID
            builder.bizContentSet(ICBCBusinessFields.SHOP_APPID, Order.SUB_PAYWAY_WAP == subpayway ? MapUtil.getString(tradeParams, TransactionParam.ICBC_WX_SUB_APPID) : MapUtil.getString(tradeParams, TransactionParam.ICBC_WX_MINI_SUB_APPID));
            //支付方式
            builder.bizContentSet(ICBCBusinessFields.PAY_MODE, ICBCConstants.PAY_MODE_WX);
            //收单接入方式
            builder.bizContentSet(ICBCBusinessFields.ACCESS_TYPE, Order.SUB_PAYWAY_WAP == subpayway ? ICBCConstants.ACCESS_TYPE_WX_SUB_APP : ICBCConstants.ACCESS_TYPE_WX_MINI);
            //第三方用户标识
            builder.bizContentSet(ICBCBusinessFields.OPEN_ID, payerUid);
        }

        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder);
        //由于工商银行未返回，故我们自己取
        transaction.put(Transaction.BUYER_UID, payerUid);
        context.getOrder().put(Order.BUYER_UID, payerUid);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE), builder.build(), getPrivateKeyContent(sqbPrivateKey), getPrivateKeyContent(icbcPublicKey), 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call icbcbank precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildPrecreateResult(result, context);
    }

    private String buildPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        String returnCode = MapUtils.getString(result, ICBCResponseFields.RETURN_CODE);//返回的响应码
        //明确成功
        if (ICBCConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if(Order.PAYWAY_ALIPAY2 == payway) {
                String tradeNo = MapUtils.getString(result, ICBCResponseFields.ZFB_DATA_PACKAGE);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            } else if(Order.PAYWAY_WEIXIN == payway) {
                String data = MapUtils.getString(result, ICBCResponseFields.WX_DATA_PACKAGE);
                Map<String, Object> wxDataPackage = wxDataFormatConvert(data);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wxDataPackage);
            }

            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    @SneakyThrows
    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String sqbPrivateKey = MapUtil.getString(tradeParams, TransactionParam.ICBC_PRIVATE_KEY);

        String notifyBody = MapUtil.getString(providerNotification, "icbcNotify");
        long type = MapUtil.getLongValue(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }
        //解析出 biz_content value
        String contentStr = analysisBizContent(notifyBody);

        if (!StringUtils.isEmpty(contentStr)){
            Map<String, Object> content = JsonUtil.jsonStringToObject(contentStr, Map.class);
            String returnCode = MapUtil.getString(content, ICBCResponseFields.RETURN_CODE);
            if (returnCode.equals(ICBCConstants.RETURN_CODE_SUCCESS)) {

                //回调通知响应
                Map<String, Object> response = new HashMap<>();
                Map<String, Object> bizContent = new HashMap<>();
                response.put(ICBCProtocolFields.RESPONSE_BIZ_CONTENT, bizContent);
                bizContent.put(ICBCProtocolFields.MSG_ID, MapUtil.getString(content, ICBCProtocolFields.MSG_ID));
                bizContent.put(ICBCResponseFields.RETURN_CODE, ICBCConstants.RETURN_CODE_SUCCESS);
                bizContent.put(ICBCResponseFields.RETURN_MSG, ICBCConstants.NOTIFY_SUCCESS);
                response.put(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
                String retSigned = null;
                try {
                    retSigned = RsaSignature.icbcSign(null, response, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, getPrivateKeyContent(sqbPrivateKey));
                } catch (Exception e) {
                    logger.error("加签异常", e);
                }

                response.put(ICBCProtocolFields.SIGN, retSigned);
                providerNotification.put("response", JsonUtil.toJsonStr(response));
            }
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);

        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
            String buyerUid = "";
            String tpCustId = MapUtils.getString(result, ICBCResponseFields.TP_CUST_ID); //第三方支付机构的客户编号
            String subOpenId = MapUtils.getString(result, ICBCResponseFields.SUB_OPEN_ID); //微信子用户标识
            if (payway == Order.PAYWAY_ALIPAY2) {
                buyerUid = tpCustId;
            } else if (payway == Order.PAYWAY_WEIXIN) {
                buyerUid = subOpenId;
            }
            if (!StringUtil.empty(buyerUid)) {
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }

        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
            String logonId = "";
            String buyerLogonId = MapUtils.getString(result, ICBCResponseFields.BUYER_LOGON_ID); //支付宝账号
            String tpCustId = MapUtils.getString(result, ICBCResponseFields.TP_CUST_ID); //第三方支付机构的客户编号
            if (payway == Order.PAYWAY_ALIPAY2) {
                logonId = buyerLogonId;
            } else if (payway == Order.PAYWAY_WEIXIN) {
                logonId = tpCustId;
            }
            if (!StringUtil.empty(logonId)) {
                transaction.put(Transaction.BUYER_LOGIN, logonId);
            }
        }

        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String orderId = MapUtils.getString(result, ICBCResponseFields.ORDER_ID); //行内系统订单号
            if (!StringUtils.isEmpty(orderId)) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(BeanUtil.getPropString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String tpOrderId = MapUtils.getString(result, ICBCResponseFields.TP_ORDER_ID); //第三方订单号
            String thirdTradeNo = MapUtils.getString(result, ICBCResponseFields.THIRD_TRADE_NO); //第三方订单号 主扫返回
            String channelTradeNo = !StringUtils.isEmpty(tpOrderId) ? tpOrderId : thirdTradeNo;
            if (context.isAlipay() && !StringUtils.isEmpty(channelTradeNo) && channelTradeNo.length() > 2) {
                channelTradeNo = channelTradeNo.substring(2);
            }
            if (!StringUtils.isEmpty(channelTradeNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
            }
        }
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_UNIONPAY) {
            resolveOtherPayFund(result, context);
        }
        if (payway == Order.PAYWAY_WEIXIN) {
            resolveWeixinPayFund(result, context);
        }
    }

    private void resolveWeixinPayFund(Map<String,Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long couponSum = 0;
        long discountChanelMchTotal = 0; // 商户免充值不结算给商户
        long paidAmt = MapUtil.getLongValue(result, ICBCResponseFields.PAYMENT_AMT); //用户实际支付金额
        List<Map<String,Object>> payments = new ArrayList<Map<String,Object>>();
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            List<Map<String,Object>> promotions = getPromotions(result);
            Pair<Long, Long> discount = buildWeixinPaymentsByPromotions(promotions, payments);
            couponSum = discount.getLeft();
            discountChanelMchTotal = discount.getRight();
        } else {
            long totalDiscountAmount = MapUtil.getLongValue(result, ICBCResponseFields.TOTAL_DISC_AMT);
            long merDiscountAmount = MapUtil.getLongValue(result, ICBCResponseFields.MER_DISC_AMT);
            long channelDiscountAmount  = totalDiscountAmount - merDiscountAmount;
            if (merDiscountAmount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, merDiscountAmount
                        )
                );
                discountChanelMchTotal += merDiscountAmount;
            }

            if (channelDiscountAmount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                        )
                );
                couponSum += channelDiscountAmount;
            }
        }

        long totalFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long cashFee = totalFee - couponSum - discountChanelMchTotal;
        if (cashFee > 0) {
            String banktype = BeanUtil.getPropString(result, ICBCResponseFields.BANK_TYPE);
            Map<String,Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
        }

        Map<String,Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if(couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum + discountChanelMchTotal);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum + discountChanelMchTotal);
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee - discountChanelMchTotal);
    }

    private void resolveOtherPayFund(Map<String,Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long paidAmt = MapUtil.getLongValue(result, ICBCResponseFields.PAYMENT_AMT); //用户实际支付金额

        long totalDiscountAmount = MapUtil.getLongValue(result, ICBCResponseFields.TOTAL_DISC_AMT);
        long merDiscountAmount = MapUtil.getLongValue(result, ICBCResponseFields.MER_DISC_AMT);
        long channelDiscountAmount  = totalDiscountAmount - merDiscountAmount;
        List<Map<String,Object>> payments = new ArrayList<Map<String,Object>>();
        if (merDiscountAmount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, merDiscountAmount
                    )
            );
        }

        if (channelDiscountAmount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                    )
            );
        }

        long amount = effectiveAmount - totalDiscountAmount;
        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (amount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));

        }

        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if(effectiveAmount - paidAmt > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, totalDiscountAmount);
            context.getOrder().put(Order.NET_DISCOUNT, totalDiscountAmount);
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount - channelDiscountAmount);
    }

    private List<Map<String, Object>> getPromotions(Map<String,Object> result) {
        Object promotionsStr = MapUtil.getObject(result, ResponseFields.PROMOTION_DETAIL);
        List<Map<String, Object>> promotionList = new ArrayList<>();
        if (promotionsStr instanceof List) {
            promotionList = (List<Map<String, Object>>) promotionsStr;
        } else if (promotionsStr instanceof String) {
            if (StringUtils.isEmpty(String.valueOf(promotionsStr))) {
                promotionsStr = "[]";
            }
            promotionList =  JsonUtil.jsonStrToObject(String.valueOf(promotionsStr), List.class);
        }

        return promotionList;
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    private Map<String, Object> buildPayment(long amount, String fundChannel){
        Map<String, Object> payment = (Map) AlipayV2ServiceProvider.fundChannelPayment.get(fundChannel);
        payment.put(Transaction.PAYMENT_AMOUNT, amount);
        return payment;
    }

    private void carryOverExtendedParams(Map<String, Object> extended, ICBCRequestBuilder builder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (SUB_APPID.equals(key)) {
                // weixin sub_appid转换
                builder.bizContentSet(ICBCBusinessFields.SUB_APP_ID, value);
                builder.bizContentSet(ICBCBusinessFields.SHOP_APPID, value);
                continue;
            }
            //如果设置了单品优惠，由于工行传值不同需要特殊处理
            try {
                if (UpayConstant.DETAIL.equals(key) && value instanceof Map && ((Map) value).get(UpayConstant.GOODS_DETAIL) != null) {
                    builder.bizContentSet(UpayConstant.DETAIL, objectMapper.writeValueAsString(((Map) value).get(UpayConstant.GOODS_DETAIL)));
                    continue;
                } else if (UpayConstant.GOODS_DETAIL.equals(key) && value != null) {
                    builder.bizContentSet(UpayConstant.DETAIL, objectMapper.writeValueAsString(value));
                    continue;
                }
            } catch (JsonProcessingException e) {
                logger.error("process extend fields fail: " + e.getMessage(), e);
            }
            builder.bizContentSet(key, value);
        }
    }

    public void setTransactionContextErrorInfo(TransactionContext context, String key, Exception ex) {
        BeanUtil.setNestedProperty(context.getTransaction(),
                UpayUtil.getProviderErrorInfoKey(key),
                CollectionUtil.hashMap(
                        ICBCResponseFields.RETURN_CODE, null,
                        ICBCResponseFields.RETURN_MSG, StringUtils.join(ex.getClass(), ":", ex.getMessage())
                )
        );
    }

    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String returnCode = MapUtils.getString(result, ICBCResponseFields.RETURN_CODE);//返回的响应码
        String returnMsg = MapUtils.getString(result, ICBCResponseFields.RETURN_MSG);//响应描述

        map.put(ICBCResponseFields.RETURN_CODE, returnCode);//返回状态码
        map.put(ICBCResponseFields.RETURN_MSG, returnMsg);//返回信息
        com.wosai.upay.util.MapUtil.removeNullValues(map);
        BeanUtil.setNestedProperty(context.getTransaction(),
                UpayUtil.getProviderErrorInfoKey(key),
                map);

        String path = UpayUtil.getBizErrorCodeKey(key);
        String terminalCategory = MapUtil.getString(MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        boolean isMergeMessage = StringUtil.empty(terminalCategory);
        if (ICBCConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
            // 支付通道返回成功，清空原先设置的UpayBizError
            Map bizErrorCode = MapUtils.getMap(context.getTransaction(), Transaction.BIZ_ERROR_CODE);
            if (null != bizErrorCode && bizErrorCode.containsKey(key)) {
                bizErrorCode.remove(key);
            }
        } else {
            UpayBizError bizError = UpayBizError.getBizErrorByField(key, BeanUtil.getPropInt(context.getTransaction(), Transaction.PAYWAY), returnCode, returnMsg);

            if (bizError != null && !UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(bizError.getStandardName())) {
                BeanUtil.setNestedProperty(context.getTransaction(), path, bizError);
            } else {
                BeanUtil.setNestedProperty(context.getTransaction(), path,
                        UpayBizError.unexpectedProviderError(StringUtil.empty(returnMsg) ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : returnMsg, isMergeMessage));
            }
        }
    }

    private Map<String, Object> retryIfNetworkException(String serviceUrl, Map<String, Object> request, String secretKey, String hxkey, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            return icbcClient.call(serviceUrl, request, secretKey, hxkey);
        }
        logger.error("still network i/o error after retrying {} times, op is {}", times, opFlag);

        throw exception;
    }

    /**
     * 公共请求参数
     *
     * @param context
     */
    protected ICBCRequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_APP_ID);
        String merId = BeanUtil.getPropString(tradeParams, TransactionParam.ICBC_MER_ID);

        //开发者ID
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, appId);
        //分配的商户号
        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, merId);

        return requestBuilder;

    }

    /**
     * 主扫微信数据包格式转换
     *
     * @param data
     * @return
     */
    private Map<String, Object> wxDataFormatConvert(String data){
        Map<String, Object> wxData = JsonUtil.jsonStrToObject(data, Map.class);
        Map<String, Object> result = new HashMap<>();
        result.put(WapFields.APP_ID, MapUtil.getString(wxData, ICBCResponseFields.APPID));
        result.put(WapFields.PACKAGE, MapUtil.getString(wxData, ICBCResponseFields.PACKAGE));
        result.put(WapFields.NONCE_STR, MapUtil.getString(wxData, ICBCResponseFields.NONCESTR));
        result.put(WapFields.SIGN_TYPE, MapUtil.getString(wxData, ICBCResponseFields.SIGNTYPE));
        result.put(WapFields.PAY_SIGN, MapUtil.getString(wxData, ICBCResponseFields.SIGN));
        result.put(WapFields.TIME_STAMP, MapUtil.getString(wxData, ICBCResponseFields.TIMESTAMP));
        return result;
    }

    /**
     * 解析 bizContent
     *
     * @param notifyBody
     * @return
     */
    private String analysisBizContent(String notifyBody) {
        if (StringUtils.isEmpty(notifyBody)) {
            return StringUtils.EMPTY;
        }
        // 提取biz_content pair
        String[] array = org.apache.commons.lang.StringUtils.split(notifyBody, "&");
        String bizContent = null;
        for (String pair : array) {
            if (org.apache.commons.lang.StringUtils.startsWith(pair, ICBCProtocolFields.BIZ_CONTENT)) {
                bizContent = pair;
                break;
            }
        }
        if (org.apache.commons.lang.StringUtils.isBlank(bizContent)) {
            return StringUtils.EMPTY;
        }
        // 取biz_content value
        bizContent = org.apache.commons.lang.StringUtils.substringAfter(bizContent, ICBCProtocolFields.BIZ_CONTENT + "=");
        // url解码
        return cn.hutool.core.net.URLDecoder.decode(bizContent, Charsets.UTF_8);
    }
}