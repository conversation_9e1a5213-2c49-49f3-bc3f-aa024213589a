package com.wosai.upay.workflow;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.ExternalServiceException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.util.*;

import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING;
import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING_MESSAGE;

/**
 * <AUTHOR>
 * @description 微信周期代扣款 服务商模式
 * @link <a href="https://pay.weixin.qq.com/doc/v2/partner/**********">微信周期代扣款文档</a>
 * @date 2025-04-27
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class DirectWeixinV2CycleServiceProvider extends WeixinServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(DirectWeixinV2CycleServiceProvider.class);
    public static final String NAME = "provider.weixin.cycle.v2";

    protected static final Map<String, String> REPLACE_TAGS = MapUtil.hashMap(OP_DEPOSIT_PREFREEZE, BusinessFields.CONTRACT_ID);

    /**
     * 微信子支付方式对应的交易参数key
     */
    private static final Map<Integer, String> WECHAT_SUB_PAYWAY_TRADE_PARAMS_KEY_MAP = MapUtil.hashMap(
            Order.SUB_PAYWAY_WAP, TransactionParam.WEIXIN_WAP_TRADE_PARAMS,
            Order.SUB_PAYWAY_MINI, TransactionParam.WEIXIN_MINI_TRADE_PARAMS,
            Order.SUB_PAYWAY_APP, TransactionParam.WEIXIN_APP_TRADE_PARAMS,
            Order.SUB_PAYWAY_H5, TransactionParam.WEIXIN_H5_TRADE_PARAMS
    );

    public DirectWeixinV2CycleServiceProvider() {
        extendedFilterFields = new HashSet<>(Collections.singletonList(Transaction.SQB_PRODUCT_CODE));
    }

    /**
     * 微信子支付方式对应的签约请求path
     */
    protected static final Map<Integer, String> AUTH_PATH_OF_SUB_PAYWAY = MapUtil.hashMap(
            Order.SUB_PAYWAY_WAP, "entrustweb",
            Order.SUB_PAYWAY_APP, "preentrustweb",
            Order.SUB_PAYWAY_H5, "h5entrustweb"
    );

    @Resource
    private WeixinV3Client weixinV3Client;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> call(Map<String, Object> config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError {
        SSLContext ssl = null;
        if (OP_CANCEL.equals(opFlag) || OP_REFUND.equals(opFlag)) {
            String certConfigKey = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_CONFIG_KEY);
            String password = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_PASSWORD);
            ssl = getSSLContext(certConfigKey, password);
        }
        return client.call(serviceUrl, WeixinClient.SIGN_TYPE_WEIXIN, (String) config.get(TransactionParam.WEIXIN_APP_KEY), ssl, request);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        int subPayway = MapUtils.getInteger(transaction, Transaction.SUB_PAYWAY);
        String paramsKey = WECHAT_SUB_PAYWAY_TRADE_PARAMS_KEY_MAP.get(subPayway);
        return getTradeParams(transaction, paramsKey);
    }

    protected int getServiceMode() {
        return TransactionParam.SERVICE_MODE_PARTNER;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (!Transaction.TYPE_DEPOSIT_SET.contains(type) && Transaction.TYPE_REFUND != type) {
            return false;
        }

        if (Order.PAYWAY_WEIXIN != MapUtil.getIntValue(transaction, Transaction.PAYWAY)) {
            return false;
        }

        //校验是否微信周期代扣款产品
        String productCode = BeanUtil.getPropString(transaction, Transaction.KEY_SQB_PRODUCT_CODE);
        if (!TransactionParam.SQB_PRODUCT_CODE_WEIXIN_CYCLE.equals(productCode)) {
            return false;
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);
        if (null == tradeParams) {
            return false;
        }

        if (tradeParams.containsKey(TransactionParam.WEIXIN_VERSION)) {
            logger.warn("微信周期代扣款不支持微信v3版本");
            return false;
        }

        // 检查是服务商模式还是商户模式
        int serviceMode = MapUtil.getIntValue(tradeParams, TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_PARTNER);
        return getServiceMode() == serviceMode;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    protected String getPaySuccessFlag() {
        return Workflow.RC_CONSUME_SUCCESS;
    }

    protected int getTransactionPayType() {
        return Transaction.TYPE_DEPOSIT_CONSUME;
    }

    @Override
    @SuppressWarnings("unchecked")
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        String opFlag = OP_DEPOSIT_PREFREEZE;
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String merchantId = MapUtils.getString(transaction, Transaction.MERCHANT_ID);
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> extended = MapUtils.getMap(transaction, Transaction.EXTENDED_PARAMS);
        boolean delayTrade = BeanUtil.getPropBoolean(extended, Transaction.DELAY_TRADE);
        //实时扣款，直接返回成功
        if (!delayTrade) {
            logger.info("delayTrade is false, merchantId={}, tsn={}", merchantId, tsn);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, Collections.emptyMap());
            return Workflow.RC_CREATE_SUCCESS;
        }

        //周期扣款，先进行预扣费通知
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> requestParams = buildPreFreezeRequest(extended, config);
        String serverUrl = getServerUrl(opFlag, extended);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(serverUrl, config, requestParams, opFlag);
        } catch (Exception ex) {
            logger.error("failed to call weixinV2Cycle deposit preFreeze", ex);
            setTransactionContextErrorInfo(context, opFlag, ex);
            return Workflow.RC_IOEX;
        }

        if (null == result) {
            return Workflow.RC_ERROR;
        }

        setTransactionContextErrorInfo(context, result, opFlag);

        boolean isSuccess = MapUtils.getIntValue(result, HttpConstant.HTTP_CODE) == HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE;
        if (!isSuccess) {
            //如果错误码是RESULT_ERROR_CODE_RESOURCE_ALREADY_EXISTS, 说明已经成功发送预扣费通知, 无需重复调用
            String errorCode = MapUtils.getString(result, ResponseFields.CODE);
            isSuccess = WeixinConstants.RESULT_ERROR_CODE_RESOURCE_ALREADY_EXISTS.equals(errorCode);
        }

        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, Collections.emptyMap());
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String depositConsume(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = MapUtils.getMap(transaction, Transaction.EXTENDED_PARAMS);
        String opFlag = OP_DEPOSIT_CONSUME;

        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, context.getOrder().get(Order.EFFECTIVE_TOTAL));
        builder.set(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_PAP);
        builder.set(BusinessFields.BODY, MapUtils.getString(transaction, Transaction.SUBJECT));
        //extended透传给支付通道
        carryDepositOverExtendedParams(extended, builder);

        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }

        String serverUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, serverUrl, builder.build(), retryTimes, opFlag);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit consume", ex);
            setTransactionContextErrorInfo(context, opFlag, ex);
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, opFlag);
        return buildConsumeResult(result);
    }

    private String buildConsumeResult(Map<String, Object> result) {
        Triple<Boolean, String, String> responseStatus = processWeixinResult(result);
        if (responseStatus.getLeft()) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String depositQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtils.getIntValue(transaction, Transaction.TYPE);
        if (type == Transaction.TYPE_DEPOSIT_FREEZE) {
            //微信周期代扣款的预授权是假的预授权，实际上是调用微信预扣费通知接口给商户发送预扣费通知，所以不能进行真正的查单，无法改变流水状态。
            //因此直接根据流水状态来返回对应结果。只要流水状态是处理中，就认为已处理成功
            int status = MapUtils.getIntValue(transaction, Transaction.STATUS);
            if (status == Transaction.STATUS_IN_PROG || status == Transaction.STATUS_SUCCESS) {
                return Workflow.RC_PAY_SUCCESS;
            }
            return Workflow.RC_ERROR;
        }

        if (type == Transaction.TYPE_DEPOSIT_CONSUME) {
            //支付查单
            return query(context);
        }

        logger.error("微信周期代扣款查单, 不支持的交易类型, type={}, tsn={}", type, MapUtils.getString(context.getTransaction(), Transaction.TSN));
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        long refundFee = MapUtils.getLongValue(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT);
        return doRefund(context, OP_REFUND, refundFee);
    }

    /**
     * 获取默认的requestBuilder，设置请求默认值。
     *
     * @param context
     * @return
     */
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());

        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, MapUtils.getString(config, TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.MCH_ID, MapUtils.getString(config, TransactionParam.WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, MapUtils.getString(config, TransactionParam.WEIXIN_SUB_MCH_ID));
        return builder;
    }

    //extended透传给支付通道
    protected void carryDepositOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if (MapUtils.isEmpty(extended)) {
            return;
        }

        extended.forEach((key, value) -> {
            if (overFilterField(key) || Objects.isNull(value)) {
                return;
            }
            if (key.equals(Transaction.CLIENT_IP)) {
                builder.set(BusinessFields.SPBILL_CREATE_IP, value);
            } else {
                builder.set(key, value);
            }
        });
    }

    /**
     * 构建预授权请求参数
     *
     * @param tradeConfig
     * @return
     */
    protected Map<String, Object> buildPreFreezeRequest(Map<String, Object> extended, Map<String, Object> tradeConfig) {
        Map<String, Object> requestParams = new HashMap<>(8);
        requestParams.put(BusinessFields.SP_APPID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_APP_ID));
        requestParams.put(BusinessFields.SP_MCH_ID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_MCH_ID));
        requestParams.put(ProtocolFields.SUB_MCHID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_SUB_MCH_ID));

        requestParams.put(ProtocolFields.SUB_APP_ID, MapUtils.getString(extended, ProtocolFields.SUB_APP_ID));
        requestParams.put(BusinessFields.DEDUCT_DURATION, MapUtils.getMap(extended, BusinessFields.DEDUCT_DURATION));
        requestParams.put(BusinessFields.ESTIMATED_AMOUNT, MapUtils.getMap(extended, BusinessFields.ESTIMATED_AMOUNT));
        return requestParams;
    }

    protected void setTransactionContextErrorInfo(TransactionContext context, Map<String, Object> result, String key) {
        int httpCode = MapUtils.getIntValue(result, HttpConstant.HTTP_CODE);
        String code = MapUtils.getString(result, ResponseFields.CODE);
        String message = MapUtils.getString(result, ResponseFields.MESSAGE);
        Map<String, Object> map = new HashMap<>();
        map.put(HttpConstant.HTTP_CODE, httpCode);
        map.put(ResponseFields.CODE, code);
        map.put(ResponseFields.MESSAGE, message);
        boolean isSuccess = httpCode == HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE;
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, code, message);
    }

    protected String getWeixinMchIdByServiceMode(Map<String, Object> config) {
        return MapUtil.getString(config, TransactionParam.WEIXIN_MCH_ID);
    }

    private Map<String, Object> retryIfNetworkException(String url, Map<String, Object> config, Map<String, Object> request, String opFlag) {
        try {
            return new RetryUtil<Map<String, Object>>()
                    .retry(new RetryUtil.TimingStrategy.Builder().setRetry(2, 50, 1.0).build())
                    .method(() -> {
                        try {
                            String mchId = getWeixinMchIdByServiceMode(config);
                            String serialNo = MapUtil.getString(config, TransactionParam.WEIXIN_SERIAL_NO);
                            String signKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.WEIXIN_PRIVATE_KEY_V3));
                            return weixinV3Client.call(url, WeixinV3Client.METHOD_POST, mchId, serialNo, signKey, request);
                        } catch (MpayException | MpayApiNetworkError e) {
                            logger.error("{}: 调用微信v3接口异常: opFlag={}, request={}, error={}",
                                    NAME, opFlag, JacksonUtil.toJsonString(request), e.getMessage());
                            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
                        }
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull)
                    .execute();
        } catch (Exception e) {
            logger.error("{}: 请求微信v3接口异常: opFlag={}, request={}, error={}",
                    NAME, opFlag, JacksonUtil.toJsonString(request), e.getMessage());
            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }
    }

    private String getServerUrl(String opFlag, Map<String, Object> params) {
        String serverUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag);
        if (REPLACE_TAGS.containsKey(opFlag)) {
            String key = REPLACE_TAGS.get(opFlag);
            return serverUrl.replace(key, MapUtil.getString(params, key));
        }
        return serverUrl;
    }

    /**
     * 获取子支持方式对应的交易参数
     *
     * @param request     请求参数
     * @param tradeConfig 交易配置
     * @return 交易参数
     */
    protected Map<String, Object> getSubPaywayTradeParams(Map<String, Object> request, Map<String, Object> tradeConfig) {
        int payway = MapUtils.getIntValue(request, UpayService.PAYWAY);
        int subPayway = MapUtils.getIntValue(request, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_MINI);

        if (Order.PAYWAY_WEIXIN != payway) {
            logger.warn("微信周期代扣款商户模式签约相关, 查询交易参数, 只支持微信, 本次请求的payway={}", payway);
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }

        String paramsKey = WECHAT_SUB_PAYWAY_TRADE_PARAMS_KEY_MAP.get(subPayway);
        Map<String, Object> tradeParams = MapUtils.getMap(tradeConfig, paramsKey);
        if (MapUtils.isEmpty(tradeParams)) {
            logger.warn("微信周期代扣款商户模式签约相关, 未查到subPayway对应的交易参数, payway={}, subPayway={}", payway, subPayway);
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        return tradeParams;
    }

    @Override
    public String depositCancel(TransactionContext context) {
        //微信周期代扣款功能是普通的支付，只是借用了预授权的模式，因此不支持真正的预授权撤销，理论上不会走到这里。如果真走到这里，无条件返回失败
        logger.warn("微信周期代扣款不支持预授权撤销, tsn={}", MapUtils.getString(context.getTransaction(), Transaction.TSN));
        return Workflow.RC_ERROR;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("微信周期代扣款不支持预下单");
    }

    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("微信周期代扣款不支持B2C预授权");
    }

    /**
     * 处理微信响应结果，包含通信状态和业务状态的检查
     *
     * @param result 微信返回的结果
     * @return Triple对象，包含：是否成功(Boolean)、错误码(String)、错误描述(String)
     */
    protected Triple<Boolean, String, String> processWeixinResult(Map<String, Object> result) {
        // 检查通信状态
        String returnCode = MapUtils.getString(result, ResponseFields.RETURN_CODE);
        if (!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)) {
            String returnMsg = MapUtils.getString(result, ResponseFields.RETURN_MSG);
            return Triple.of(false, returnCode, returnMsg);
        }

        // 检查业务状态
        String resultCode = MapUtils.getString(result, ResponseFields.RESULT_CODE);
        if (!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
            String errorCode = MapUtils.getString(result, ResponseFields.ERR_CODE);
            String errorCodeDesc = MapUtils.getString(result, ResponseFields.ERR_CODE_DES);
            return Triple.of(false, errorCode, errorCodeDesc);
        }

        // 通信和业务都成功
        return Triple.of(true, null, null);
    }
}
