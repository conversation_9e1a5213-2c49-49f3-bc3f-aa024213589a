package com.wosai.upay.workflow;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.lakala.open.BusinessFields;
import com.wosai.mpay.api.lakala.open.LakalaConstants;
import com.wosai.mpay.api.lakala.open.LakalaOpenClient;
import com.wosai.mpay.api.lakala.open.RequestBuilder;
import com.wosai.mpay.api.lakala.open.ResponseFields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;

public class LakalaOpenServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(LakalaOpenServiceProvider.class);
    public static final String NAME = "provider.lakala.open";
    public SafeSimpleDateFormat format = new SafeSimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);

    private int retryTimes = 3;

    public LakalaOpenServiceProvider() {
        dateFormat = format;
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.AMOUNT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LAKALA;
    }

    @Autowired
    protected LakalaOpenClient client;

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> config = getTradeParams(transaction);
        if(config == null){
            return false;
        }
        return config.containsKey(TransactionParam.LAKALA_APP_ID);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LAKALA_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.reqDataSet(BusinessFields.AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.reqDataSet(BusinessFields.AUTHCODE, extraParams.get(Transaction.BARCODE));
        builder.reqDataSet(BusinessFields.ORDER_ID, transaction.get(Transaction.TSN));
        builder.reqDataSet(BusinessFields.SUBJECT, transaction.get(Transaction.SUBJECT));
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(OP_PAY, config, request, 1);
        } catch (MpayException ex) {
            logger.error("failed to call lakala open pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in lakala open pay", ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RET_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        if (LakalaConstants.RET_CODE_BPS_SUCCESS.equals(returnCode)) {
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }else if(LakalaConstants.RET_CODE_LABS_PAY_IN_PROG.equals(returnCode) 
                    || LakalaConstants.RET_CODE_BPS_IN_PROG.equals(returnCode)
                    || LakalaConstants.RET_CODE_LABS_UNKNOWN.equals(returnCode)
                    || LakalaConstants.RET_CODE_LABS_SYSTEM_ERROR.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.reqDataSet(BusinessFields.ORDER_ID, transaction.get(Transaction.TSN));
        builder.reqDataSet(BusinessFields.ORN_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_CANCEL, config, request, retryTimes);
        } catch (MpayException ex) {
            logger.error("failed to call lakala open cancel", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            return Workflow.RC_RETRY;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RET_CODE);
        if (LakalaConstants.RET_CODE_BPS_SUCCESS.equals(returnCode)) {
            Map<String, Object> respData = MapUtil.getMap(result, ResponseFields.RESP_DATA);
            String cancelCode = MapUtil.getString(respData, ResponseFields.RESP_RET_CODE);
            if(LakalaConstants.RET_CODE_BBS_SUCCESS.equals(cancelCode) 
                    || LakalaConstants.RET_CODE_LABS_SUCCESS.equals(cancelCode)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
        }else if(LakalaConstants.RET_CODE_BPS_ORDER_NOT_EXISTS.equals(returnCode)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }


    protected Map<String, Object> retryIfNetworkException(String logFlag, Map<String, Object> config, Map<String,Object> request, int times) throws MpayException, MpayApiNetworkError {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), logFlag);
        String appId = MapUtil.getString(config, TransactionParam.LAKALA_APP_ID);
        String privateKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.LAKALA_PRIVATE_KEY));
        String serialNo = MapUtil.getString(config, TransactionParam.LAKALA_SERIAL_NO);
        MpayApiNetworkError tex = null;
        for (int i = 0; i< times; ++i) {
            try {
                
                return client.call(url, appId, serialNo, privateKey, request);
            } catch (MpayApiNetworkError ex) {
                tex = ex;
                logger.warn("encountered ioex in lakala open {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw tex;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.reqDataSet(BusinessFields.ORN_ORDER_ID, transaction.get(Transaction.TSN));

        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_QUERY, config, request, retryTimes);
        } catch (MpayException ex) {
            logger.error("failed to call lakala open query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            return Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RET_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        if (LakalaConstants.RET_CODE_BPS_SUCCESS.equals(returnCode)) {
            Map<String, Object> respData = MapUtil.getMap(result, ResponseFields.RESP_DATA);
            String tradeState = MapUtil.getString(respData, ResponseFields.TRADE_STATE);
            if(LakalaConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            }else if(LakalaConstants.TRADE_STATE_CLOSE.equals(tradeState) 
                    || LakalaConstants.TRADE_STATE_FAIL.equals(tradeState)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_IN_PROG;
        }else if(LakalaConstants.RET_CODE_BPS_PAY_FAIL.equals(returnCode) 
                    || LakalaConstants.RET_CODE_BPS_PRECREATE_FAIL.equals(returnCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }else if(LakalaConstants.RET_CODE_BPS_IN_PROG.equals(returnCode)
                    || LakalaConstants.RET_CODE_LABS_PAY_IN_PROG.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }
    
    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.reqDataSet(BusinessFields.REFUND_ORDER_ID, transaction.get(Transaction.TSN));
        builder.reqDataSet(BusinessFields.ORN_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        builder.reqDataSet(BusinessFields.AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_REFUND, config, request, 1);
        } catch (MpayException ex) {
            logger.error("failed to call lakala refund,start refund query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in lakala refund,start refund query", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RET_CODE);
        if (LakalaConstants.RET_CODE_BPS_SUCCESS.equals(returnCode)) {
            Map<String, Object> respData = MapUtil.getMap(result, ResponseFields.RESP_DATA);
            String refundCode = MapUtil.getString(respData, ResponseFields.RESP_RET_CODE);
            if(LakalaConstants.RET_CODE_BBS_SUCCESS.equals(refundCode) 
                    || LakalaConstants.RET_CODE_LABS_SUCCESS.equals(refundCode)) {
                resolveRefundFund(respData, context);
                return Workflow.RC_REFUND_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("预下单暂不支持");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }


    /**
     * 获取默认的requestBuilder，设置请求默认值。
     *
     * @param context
     * @return
     */
    protected RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.termExtInfoSet(BusinessFields.TERM_SN, MapUtil.getString((Map)context.getTransaction().get(Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_SN));
        Map<String,Object> extraParams = (Map<String, Object>) context.getTransaction().get(Transaction.EXTRA_PARAMS);
        String ip = BeanUtil.getPropString(extraParams,Transaction.CLIENT_IP);
        if(StringUtils.isEmpty(ip)){
            ip = UpayUtil.getLocalHostIp();
        }
        builder.termExtInfoSet(BusinessFields.TERM_IP, ip);
        Map<String, Object> poi = MapUtil.getMap(extraParams, Transaction.POI);
        if(poi != null && !poi.isEmpty()) {
            builder.termExtInfoSet(BusinessFields.TERM_LOC, MapUtil.getString(poi, Transaction.LATITUDE) + "," + MapUtil.getString(poi, Transaction.LONGITUDE));
        }

        builder.reqDataSet(BusinessFields.MERC_ID, config.get(TransactionParam.LAKALA_MERC_ID));
        builder.reqDataSet(BusinessFields.TERM_NO, config.get(TransactionParam.LAKALA_TERM_ID));
        return builder;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();

            if (value != null) {
                builder.reqDataSet(key, value);
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RET_CODE);
        String message = BeanUtil.getPropString(result, ResponseFields.RET_MSG);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RET_CODE, responseCode);//返回状态码
        map.put(ResponseFields.RET_MSG, message);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, LakalaConstants.RET_CODE_BPS_SUCCESS.equals(responseCode), responseCode, message);
    }

    /**
     * 设置订单号
     * @param result
     * @param context
     */
    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String, Object> respData = MapUtil.getMap(result, ResponseFields.RESP_DATA);
        if(respData == null || respData.isEmpty()){
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        String buyerUid = MapUtil.getString(respData, ResponseFields.OPEN_ID);
        String tradeNo = MapUtil.getString(respData, ResponseFields.LKL_ORDER_ID, MapUtil.getString(respData, ResponseFields.LKL_ORDER_NO));
        String channelTradeNo = MapUtil.getString(respData, ResponseFields.WE_ORDER_NO);
        if(!StringUtils.isEmpty(buyerUid)){
            if(StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_UID))){
                order.put(Order.BUYER_UID, buyerUid);
            }
            if(StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }
        if(!StringUtils.isEmpty(tradeNo)){
            if(StringUtils.isEmpty(BeanUtil.getPropString(order, Order.TRADE_NO))){
                order.put(Order.TRADE_NO, tradeNo);
            }
            if(StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
        if(!StringUtils.isEmpty(channelTradeNo)){
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(extraOutFields ==  null){
                extraOutFields = new HashMap<>();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
        }
        
        String channelFinishTime = MapUtil.getString(respData, ResponseFields.TRADE_TIME);
        if(StringUtil.empty(channelFinishTime)) {
            channelFinishTime = MapUtil.getString(respData, ResponseFields.PAY_TIME);
        }
        if(!StringUtil.empty(channelFinishTime)) {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(channelFinishTime));
        }else {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        }
    }

    /**
     * 解析返回金额相关信息
     * @param context
     */
    private void resolvePayFund(Map<String, Object> result, TransactionContext context){
        Map<String, Object> respData = MapUtil.getMap(result, ResponseFields.RESP_DATA);
        if(respData == null || respData.isEmpty()){
            return;
        }
        long amount = MapUtil.getLongValue(respData, ResponseFields.AMOUNT);
        long settlementTotalFee = MapUtil.getLongValue(respData, ResponseFields.SETTLEMENT_TOTAL_FEE, amount);
        Map<String, Object> transaction = context.getTransaction();

        transaction.put(Transaction.PAID_AMOUNT, settlementTotalFee);
        transaction.put(Transaction.RECEIVED_AMOUNT, amount);
    }

    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        //拉卡拉退款不返回具体的退款明细，规则为先退优惠金额，再退用户实付金额
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        long refundAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        long netDiscount = BeanUtil.getPropLong(order, Order.NET_DISCOUNT, 0);
        long paidAmount  = 0;
        long receiveAmount = 0;
        if(refundAmount <= netDiscount){
            paidAmount = 0;
            receiveAmount = refundAmount;
        }else{
            paidAmount = refundAmount - netDiscount;
            receiveAmount = refundAmount;
        }
        
        transaction.put(Transaction.PAID_AMOUNT, paidAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        transaction.put(Transaction.TRADE_NO, MapUtil.getString(result, ResponseFields.REFUND_LKL_ORDER_NO));
    }
}
