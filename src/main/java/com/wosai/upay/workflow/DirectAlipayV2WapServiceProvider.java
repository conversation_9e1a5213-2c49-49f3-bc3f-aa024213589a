package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV2NewClient;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.alipay.RequestV2Builder;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

import java.util.Map;
import java.util.Objects;

/**
 * Created by jianfree on 25/4/16.
 */
@ServiceProvicerPriority(priority = 4)
public class DirectAlipayV2WapServiceProvider extends AlipayV2WapServiceProvider {
    public static final String NAME = "provider.alipay.wap.v2";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if ((Order.PAYWAY_ALIPAY == payway|| Order.PAYWAY_ALIPAY2 == payway )
                && (Order.SUB_PAYWAY_WAP == subPayway
                || Order.SUB_PAYWAY_H5 == subPayway
                || Order.SUB_PAYWAY_APP == subPayway
                || Order.SUB_PAYWAY_MINI == subPayway)) {
            Map<String, Object> tradeParams = getTradeParams(transaction);

            String sqbProductCode = null;
            if (Order.SUB_PAYWAY_MINI == subPayway) {
                sqbProductCode = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.SQB_PRODUCT_CODE);

                if (StringUtils.isEmpty(sqbProductCode)) {
                    sqbProductCode = MapUtil.getString(tradeParams, TransactionParam.ALIPAY_PRODUCT_CODE);
                }
            }

            return Objects.nonNull(tradeParams) && StringUtils.isEmpty(sqbProductCode);
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        int subPayWay = MapUtil.getIntValue(transaction,Transaction.SUB_PAYWAY);
        String paramsKey;
        if(subPayWay == Order.SUB_PAYWAY_H5){
            paramsKey = TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS;
        }else if(subPayWay == Order.SUB_PAYWAY_APP){
            paramsKey = TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS;
        }else{
            paramsKey = TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS;
        }
        return getTradeParams(transaction, paramsKey);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        String signType = BeanUtil.getPropString(config, TransactionParam.SIGN_TYPE);
        if (StringUtils.isEmpty(signType)) {
            signType = AlipayConstants.SIGN_TYPE_RSA;
        }
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(config,TransactionParam.APP_ID));
        builder.set(ProtocolV2Fields.CHARSET,AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, signType);
        String alipayPid = BeanUtil.getPropString(config, TransactionParam.ALIPAY_PID);
        if(TradeConfigService.PROVIDER_LKLWANMA == BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
            alipayPid = BeanUtil.getPropString(config, TransactionParam.LAKALA_WNAMA_SYS_PROVIDER_ID);
        }
        if(!StringUtil.empty(alipayPid)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, alipayPid
            ));
        }
        return builder;
    }

    @Override
    public Map<String, Object> call(Map<String, Object> config, String gatewayUrl, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        String signType = BeanUtil.getPropString(config, ProtocolV2Fields.SIGN_TYPE);
        if (AlipayConstants.SIGN_TYPE_RSA2.equals(signType)) {
            return client.call(gatewayUrl, AlipayConstants.SIGN_TYPE_RSA2, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
        } else {
            return client.call(gatewayUrl, AlipayV2NewClient.SIGN_TYPE_ALIPAY, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
        }
    }

}
