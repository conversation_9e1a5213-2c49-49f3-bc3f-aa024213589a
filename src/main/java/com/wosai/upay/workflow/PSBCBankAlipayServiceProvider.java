package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.api.psbcbank.PSBCBankConstants;
import com.wosai.mpay.api.psbcbank.PSBCBusinessFields;
import com.wosai.mpay.api.psbcbank.PSBCRequestBuilder;
import com.wosai.mpay.api.psbcbank.PSBCResponseFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Description PSBCBankAlipayServiceProvider
 * @Date 2021/4/9 9:34 AM
 */
@ServiceProvicerPriority(priority = 2)
public class PSBCBankAlipayServiceProvider extends PSBCBankServiceProvider {

    public static final String NAME = "provider.psbcbank.alipay";
    //fundChannel
    public static final String FC_COUPON = "COUPON";
    public static final String FC_DISCOUNT = "DISCOUNT";
    public static final String FC_MDISCOUNT = "MDISCOUNT";
    public static final String FC_MCOUPON = "MCOUPON";
    public static final String FC_TMARKETING = "TMARKETING";

    public static Set<String> consumerDiscount = CollectionUtil.hashSet(
            FC_DISCOUNT, FC_MDISCOUNT, FC_COUPON, FC_TMARKETING
    );

    @Override
    public String getName() {
        return NAME;
    }


    public PSBCBankAlipayServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessV2Fields.TOTAL_AMOUNT));
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_ALIPAY
                || payway == Order.PAYWAY_ALIPAY2;
    }

    @Override
    public PSBCRequestBuilder getPayRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);

        //pabcbank 的 builder
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_ALIPAY_ORDER_PAY);
        builder = getDefaultAlipayV2RequestBuilder(builder, context);
        builder.bizSet(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_TRADE);
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //支付场景
        builder.bizSet(BusinessV2Fields.SCENE, PSBCBankConstants.SCENE_BAR_CODE);
        //支付授权码
        builder.bizSet(BusinessV2Fields.AUTH_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        //订单标题
        builder.bizSet(BusinessV2Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //订单总金额，单位为元
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //订单描述
        builder.bizSet(BusinessV2Fields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        //商户操作员编号
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, BeanUtil.getPropString(transaction, Transaction.OPERATOR));
        //商户门店编号
        builder.bizSet(BusinessV2Fields.STORE_ID, BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_SN));
        //间连支付宝商户号
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, Collections.singletonMap(BusinessV2Fields.MERCHANT_ID,BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_ALIPAY_SUB_MCH_ID)));
        //最晚付款时间
        builder.bizSet(BusinessV2Fields.TIMEOUT_EXPRESS, b2cTimeoutExpress);
        return builder;
    }

    @Override
    public PSBCRequestBuilder getPreCreateRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        //pabcbank 的 builder
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_ALIPAY_CREATE_ORDER);
        builder = getDefaultAlipayV2RequestBuilder(builder, context);
        builder.bizSet(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_CREATE);
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //订单总金额，单位为元
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //订单标题
        builder.bizSet(BusinessV2Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //订单描述
        builder.bizSet(BusinessV2Fields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        //买家支付宝唯一用户号
        builder.bizSet(BusinessV2Fields.BUYER_ID, BeanUtil.getPropString(extraParams, Transaction.PAYER_UID));
        //商户操作员编号
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, BeanUtil.getPropString(transaction, Transaction.OPERATOR));
        //间连支付宝商户号
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, Collections.singletonMap(BusinessV2Fields.MERCHANT_ID,BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_ALIPAY_SUB_MCH_ID)));
        builder.bizSet(BusinessV2Fields.TIMEOUT_EXPRESS, defaultTimeoutExpress);
        return builder;
    }

    @Override
    public PSBCRequestBuilder getRefundRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_ALIPAY_REFUND_ORDER);
        builder = getDefaultAlipayV2RequestBuilder(builder, context);
        builder.bizSet(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_REFUND);
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //退款金额
        builder.bizSet(BusinessV2Fields.REFUND_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //标识一次退款请求，需要保证唯一
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //商户操作员编号
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, BeanUtil.getPropString(transaction, Transaction.OPERATOR));
        //商户门店编号
        builder.bizSet(BusinessV2Fields.STORE_ID, BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_SN));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getRefundQueryRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_ALIPAY_REFUND_QUERY_ORDER);

        builder = getDefaultAlipayV2RequestBuilder(builder, context);
        builder.bizSet(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_FASTPAY_REFUND_QUERY);
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //标识一次退款请求，需要保证唯一
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //收单机构(例如银行）在支付宝的pid
        builder.bizSet(BusinessV2Fields.ORG_PID, BeanUtil.getPropString(tradeParams, TransactionParam.PSBC_ALIPAY_ORG_PROVIDER_ID));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getQueryRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_ALIPAY_QUERY_ORDER);
        builder = getDefaultAlipayV2RequestBuilder(builder, context);
        builder.bizSet(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_QUERY);
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getCloseRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_ALIPAY_CLOSE_ORDER);

        builder = getDefaultAlipayV2RequestBuilder(builder, context);
        builder.bizSet(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_CLOSE);
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //商户操作员编号
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, BeanUtil.getPropString(transaction, Transaction.OPERATOR));

        return builder;
    }

    @Override
    public String resolveNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        String content = BeanUtil.getPropString(providerNotification, PSBCBusinessFields.REQ_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        if(type == Transaction.TYPE_PAYMENT) {
            String tradeStatus = BeanUtil.getPropString(respContent, BusinessV2Fields.TRADE_STATUS);
            boolean paySuccess = (AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus));

            if (paySuccess) {
                String tradeNo = AlipayV2ServiceProvider.getRealTradeNo(BeanUtil.getPropInt(transaction, Transaction.PROVIDER), respContent);
                String orderNo = BeanUtil.getPropString(respContent, BusinessV2Fields.OUT_TRADE_NO);
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(respContent, BusinessV2Fields.GMT_PAYMENT)));
                AlipayV2ServiceProvider.resolvePayFund(context.getOrder(), context.getTransaction(), respContent);
                transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(respContent, BusinessV2Fields.BUYER_ID));
                transaction.put(Transaction.TRADE_NO, orderNo);
                if (StringUtil.empty(BeanUtil.getPropString(context.getOrder(), Order.TRADE_NO))) {
                    context.getOrder().put(Order.TRADE_NO, orderNo);
                }
                transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(respContent, BusinessV2Fields.BUYER_LOGON_ID));
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tradeNo);
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String secretKey, String sm2Pass) throws MpayException, MpayApiNetworkError, JsonProcessingException {
        return psbcBankClient.call(serviceUrl, request, secretKey, sm2Pass);
    }

    public void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);//返回的响应码
        String respDesc = BeanUtil.getPropString(result, PSBCResponseFields.RESP_DESC);//响应描述

        String content = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        String returnCode = BeanUtil.getPropString(respContent, BusinessV2Fields.CODE);//支付宝返回状态码
        String returnMsg = BeanUtil.getPropString(respContent, BusinessV2Fields.MSG);//支付宝返回信息
        String errCode = BeanUtil.getPropString(respContent, BusinessV2Fields.SUB_CODE); //支付宝错误代码
        String errCodeDes = BeanUtil.getPropString(respContent, BusinessV2Fields.SUB_MSG);//支付宝错误代码描述
        map.put(PSBCResponseFields.RESP_CODE, respCode);//返回状态码
        map.put(PSBCResponseFields.RESP_DESC, respDesc);//返回信息
        map.put(BusinessV2Fields.CODE, returnCode);//支付宝返回状态码
        map.put(BusinessV2Fields.MSG, returnMsg);//支付宝返回信息
        map.put(BusinessV2Fields.SUB_CODE, errCode);//支付宝错误代码
        map.put(BusinessV2Fields.SUB_MSG, errCodeDes);//支付宝错误代码描述
        setTransactionContextErrorInfo(context.getTransaction(), key, map, AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode), getNonValue(errCode, returnCode, respCode), getNonValue(errCodeDes, returnMsg, respDesc));
    }

    @Override
    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        setTradeNoBuyerInfo(result, context);
        //响应码
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_TRADE_CANCELED;
        }
        if (PSBCBankConstants.RESP_CODE_PAYING.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_PROCESSING.equalsIgnoreCase(respCode)
                || PSBCBankConstants.RESP_CODE_ERROR.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR.equalsIgnoreCase(respCode)
                ||PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR_2.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_TIMEOUT.equalsIgnoreCase(respCode)){
            return Workflow.RC_IN_PROG;
        }
        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equalsIgnoreCase(respCode)){
            return Workflow.RC_TRADE_CANCELED;
        }
        Map<String, Object> transaction = context.getTransaction();
        String content = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String returnCode = BeanUtil.getPropString(respContent, BusinessV2Fields.CODE);//支付宝返回状态码

            if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
                //业务处理失败
                String subCode = BeanUtil.getPropString(respContent, BusinessV2Fields.SUB_CODE);
                if (AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.contains(subCode)) {
                    return Workflow.RC_TRADE_CANCELED;
                }
                return Workflow.RC_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
                //业务出现未知错误或者系统异常
                return Workflow.RC_IN_PROG;
            } else if (AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)) {
                //业务处理中
                return Workflow.RC_IN_PROG;
            } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
                //邮储 code-10000 标识付款成功
                AlipayV2ServiceProvider.resolvePayFund(context.getOrder(), context.getTransaction(), respContent);
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.REFUND_ERROR_LIST.contains(respCode)){
            return Workflow.RC_ERROR;
        }
        String content = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String returnCode = BeanUtil.getPropString(respContent, BusinessV2Fields.CODE);//返回状态码
            if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode) || AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
                return Workflow.RC_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
                transaction.put(Transaction.BUYER_UID, respContent.get(BusinessV2Fields.BUYER_USER_ID));
                transaction.put(Transaction.TRADE_NO, BeanUtil.getPropString(result, PSBCResponseFields.ORDER_NO));
                transaction.put(Transaction.BUYER_LOGIN, respContent.get(BusinessV2Fields.BUYER_LOGON_ID));
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(respContent, BusinessV2Fields.GMT_REFUND_PAY)));
                resolveRefundFund(context.getOrder(), transaction, respContent);
                return Workflow.RC_REFUND_SUCCESS;
            }
        }else {
            return doRefundQuery(context);
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);
        setTradeNoBuyerInfo(result, context);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.QUERY_ERROR_LIST.contains(respCode)){
            return Workflow.RC_ERROR;
        }

        //原订单未成功需要继续轮训 / 交易正在处理中
        if (PSBCBankConstants.RESP_CODE_CLOSE_PAY_ERROR.equals(respCode) || PSBCBankConstants.RESP_CODE_PROCESSING.equals(respCode)){
            return Workflow.RC_IN_PROG;
        }

        //code:0000，响应成功
        if (Objects.equals(respCode, PSBCBankConstants.RESP_CODE_SUCCESS)) {

            String content = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CONTENT);
            Map respContent = JSONObject.parseObject(content, Map.class);
            if (MapUtils.isNotEmpty(respContent)) {
                String returnCode = BeanUtil.getPropString(respContent, BusinessV2Fields.CODE);//支付宝返回状态码
                String errCode = BeanUtil.getPropString(respContent, BusinessV2Fields.SUB_CODE); //支付宝错误代码
                if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
                    if (AlipayConstants.RESULT_CODE_V2_QUERY_TRADE_NOT_EXIST.equals(errCode)) {
                        // 下单成功后，再次查单返回“ACQ.TRADE_NOT_EXIST”时，需要进行查单动作
                        Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction,
                                UpayUtil.getProviderErrorInfoKey(
                                        Order.SUB_PAYWAY_BARCODE == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) ? MpayServiceProvider.OP_PAY : MpayServiceProvider.OP_PRECREATE));
                        if (null == payResultMap
                                || AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(BeanUtil.getPropString(payResultMap, BusinessV2Fields.CODE))
                                || AlipayConstants.V2_RETURN_CODE_INPROG.equals(BeanUtil.getPropString(payResultMap, BusinessV2Fields.CODE))) {
                            return Workflow.RC_IN_PROG;
                        } else {
                            return Workflow.RC_ERROR;
                        }
                    }
                    return Workflow.RC_ERROR;
                } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
                    return Workflow.RC_IN_PROG;
                } else if (AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)) {
                    return Workflow.RC_IN_PROG;
                } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
                    String tradeStatus = BeanUtil.getPropString(respContent, PSBCResponseFields.TRADE_STATUS, StringUtils.EMPTY);
                    //交易支付成功
                    if (AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equalsIgnoreCase(tradeStatus) || AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equalsIgnoreCase(tradeStatus)) {
                        AlipayV2ServiceProvider.resolvePayFund(context.getOrder(), context.getTransaction(), respContent);
                        return Workflow.RC_PAY_SUCCESS;
                    }
                    if (PSBCBankConstants.ALIPAY_WAIT_BUYER_PAY.equalsIgnoreCase(tradeStatus)) {
                        return Workflow.RC_IN_PROG;
                    }
                }
            }
        }

        return Workflow.RC_ERROR;
    }


    @Override
    public String buildPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();

        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.RESP_CODE_PAYING.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_PROCESSING.equalsIgnoreCase(respCode)
                || PSBCBankConstants.RESP_CODE_ERROR.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR.equalsIgnoreCase(respCode)
                ||PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR_2.equalsIgnoreCase(respCode) || PSBCBankConstants.RESP_CODE_TIMEOUT.equalsIgnoreCase(respCode)){
            return Workflow.RC_ERROR;
        }
        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equalsIgnoreCase(respCode)){
            return Workflow.RC_TRADE_CANCELED;
        }
        String content = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String returnCode = BeanUtil.getPropString(respContent, BusinessV2Fields.CODE);//支付宝返回状态码

            if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
                return Workflow.RC_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
                return Workflow.RC_SYS_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {

                //预下单成功
                String tradeNo = AlipayV2ServiceProvider.getRealTradeNo(BeanUtil.getPropInt(transaction, Transaction.PROVIDER), respContent);
                transaction.put(Transaction.TRADE_NO, tradeNo);
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(WapV2Fields.TRADE_NO, tradeNo);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                return Workflow.RC_CREATE_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildCloseResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        String respDesc = BeanUtil.getPropString(result, PSBCResponseFields.RESP_DESC);////响应码

        boolean needRefund = false;
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.RESP_CODE_ORDER_SUCCESS.equals(respCode)){
            if ("该订单交易已成功".equals(respDesc)){
                needRefund = true;
            }
        }
        if (PSBCBankConstants.QUERY_ERROR_LIST.contains(respCode)){
            return Workflow.RC_ERROR;
        }
        String content = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String returnCode = BeanUtil.getPropString(respContent, BusinessV2Fields.CODE);//支付宝返回状态码
            if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
                return Workflow.RC_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
                return Workflow.RC_RETRY;
            } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
        }
        if(needRefund){
            String rcFlag = refund(context);
            if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
                return Workflow.RC_CANCEL_SUCCESS;
            }else{
                return rcFlag;
            }
        }
        return Workflow.RC_ERROR;
    }

    public void resolveRefundFund(Map<String, Object> order, Map<String, Object> transaction, Map<String, Object> result){
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, BeanUtil.getPropLong(order, DaoConstants.CTIME));
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款，根据返回的refund_detail_item_list来计算
            Object tradeFundBill = BeanUtil.getNestedProperty(result,BusinessV2Fields.REFUND_DETAIL_ITEM_LIST);
            List<Map<String, Object>> tradeFundBills = new ArrayList();
            if (tradeFundBill instanceof List) {
                tradeFundBills.addAll((List<Map<String, Object>>)tradeFundBill);
            }
            long refundAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            long receivedAmount = 0L;
            long discountChannelMchAmount = 0L;
            long paidAmount = 0L;
            if(result.containsKey(BusinessV2Fields.SEND_BACK_FEE)) {
                receivedAmount = StringUtils.yuan2cents(BeanUtil.getPropString(result, BusinessV2Fields.SEND_BACK_FEE));

                discountChannelMchAmount = refundAmount - receivedAmount;

                for (Map<String, Object> bill: tradeFundBills ) {
                    String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL)?BusinessV2Fields.FUND_CHANNEL:BusinessV2Fields.FUNDCHANNEL);
                    long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                    if(!consumerDiscount.contains(fundChannel)){
                        paidAmount += amount;
                    }
                }
            }else {
                logger.debug("alipay response send_back_fee not exits");
                for (Map<String, Object> bill: tradeFundBills ) {
                    String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL)?BusinessV2Fields.FUND_CHANNEL:BusinessV2Fields.FUNDCHANNEL);
                    long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                    if(!consumerDiscount.contains(fundChannel)){
                        paidAmount += amount;
                    }
                    if(!FC_MDISCOUNT.equals(fundChannel) && !FC_MCOUPON.equals(fundChannel) && !FC_TMARKETING.equals(fundChannel)){
                        receivedAmount += amount;
                    }else {
                        discountChannelMchAmount += amount;
                    }
                }
            }
            if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0){
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }
            if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, receivedAmount);
            }
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, AlipayV2ServiceProvider.getAlipayV2Payments(tradeFundBills, refundAmount, discountChannelMchAmount));
        }
    }

    /**
     * alipay 公共请求参数
     * @param context
     */
    private PSBCRequestBuilder getDefaultAlipayV2RequestBuilder(PSBCRequestBuilder builder, TransactionContext context) {
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        builder.bizSet(ProtocolV2Fields.APP_ID, "");
        builder.bizSet(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.bizSet(ProtocolV2Fields.SIGN_TYPE, "");
        builder.bizSet(ProtocolV2Fields.CERT_ID, "");
        //收单机构(例如银行）在支付宝的pid
        builder.bizSet(BusinessV2Fields.ORG_PID, BeanUtil.getPropString(tradeParams, TransactionParam.PSBC_ALIPAY_ORG_PROVIDER_ID));
        return builder;

    }

    private  void setTradeNoBuyerInfo(Map<String, Object> result, TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

        String content = MapUtil.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map respContent = JSONObject.parseObject(content, Map.class);

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerUserId = MapUtil.getString(respContent, BusinessV2Fields.BUYER_USER_ID);
            if (!StringUtil.empty(buyerUserId)) {
                transaction.put(Transaction.BUYER_UID, buyerUserId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerLogonId = MapUtil.getString(respContent, BusinessV2Fields.BUYER_LOGON_ID);
            if (!StringUtil.empty(buyerLogonId)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerLogonId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String orderNo = MapUtil.getString(result, PSBCResponseFields.ORDER_NO);
            if (!StringUtil.empty(orderNo)) {
                transaction.put(Transaction.TRADE_NO, orderNo);
            }
        }

        String sendPayDate = MapUtil.getString(respContent, BusinessV2Fields.SEND_PAY_DATE);
        if (!StringUtil.empty(sendPayDate)) {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(sendPayDate));
        }

        if (StringUtil.empty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String tradeChannelNo = MapUtil.getString(respContent, BusinessV2Fields.TRADE_NO);
            if (Objects.nonNull(tradeChannelNo) && tradeChannelNo.length() > 2) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tradeChannelNo.substring(2));
            }
        }
    }
}
