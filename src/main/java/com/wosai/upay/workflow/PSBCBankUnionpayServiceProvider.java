package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.psbcbank.PSBCBankConstants;
import com.wosai.mpay.api.psbcbank.PSBCBusinessFields;
import com.wosai.mpay.api.psbcbank.PSBCRequestBuilder;
import com.wosai.mpay.api.psbcbank.PSBCResponseFields;
import com.wosai.mpay.api.unionqrcode.BusinessFields;
import com.wosai.mpay.api.unionqrcode.ProtocolFields;
import com.wosai.mpay.api.unionqrcode.ResponseFields;
import com.wosai.mpay.api.unionqrcode.UnionPayQRCodeConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;

import java.io.IOException;
import java.util.*;

/***
 * @ClassName: PSBCBankUnionpayServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/2/7 2:49 PM
 */
@ServiceProvicerPriority(priority = 1)
public class PSBCBankUnionpayServiceProvider extends PSBCBankServiceProvider{

    public static final String NAME = "provider.psbcbank.unionpay";

    private static final int DEFAULT_TIME_EXPIRE_SECOND = 4 * 60 ; // 订单默认过期时间设定为4分钟

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_UNIONPAY;
    }

    public PSBCBankUnionpayServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
    }

    @Override
    public Map<String,Object> queryUserInfo(Map<String,Object> transaction) {

        Map<String,Object> tradeParams = getTradeParams(transaction);
        Map<String,Object> extraParams = MapUtils.getMap(transaction, Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            return null;
        }

        //密钥
        String key = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SECRET_KEY);
        String secretKey = getPrivateKeyContent(key);
        String sm2Pass = BeanUtil.getPropString(tradeParams, TransactionParam.PSBCBANK_SM2_PASS);
        TransactionContext context = new TransactionContext(null, transaction);
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);

        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_QUERY_USER_ID);

        builder.bizSet(PSBCBusinessFields.VERSION, "1.0.0");
        //交易类型
        builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_QUERY_USER_ID);
        //授权码
        builder.bizSet(PSBCBusinessFields.USER_AUTH_CODE, MapUtils.getString(extraParams, Transaction.USER_AUTH_CODE));
        //银联支付标识
        builder.bizSet(PSBCBusinessFields.APP_UP_IDENTIFIER, MapUtils.getString(extraParams, Transaction.APP_UP_IDENTIFIER));


        Map<String,Object> result;
        try {
            result = retryIfNetworkException(tradeParams, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY), builder.build(), secretKey, sm2Pass, 1, OP_QUERY);
        }catch (Exception ex) {
            logger.error("failed to call psbc queryUserInfo", ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return CollectionUtil.hashMap(PSBCResponseFields.RESP_DESC, ex.getMessage());
        }

        if (MapUtils.isEmpty(result)) {
            return null;
        }
        //响应码
        String respCd = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);

        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equals(respCd)) {
            return null;
        }

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String respCode = MapUtils.getString(respContent, ResponseFields.RESP_CODE);//云闪付返回状态码

            if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(respCode)) {
                return CollectionUtil.hashMap(BusinessFields.USER_ID, MapUtils.getString(respContent, BusinessFields.USER_ID));
            }
        }
        return null;
    }

    @Override
    public PSBCRequestBuilder getPayRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);

        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PAY);

        getDefaultUnionpayRequestBuilder(builder, context);
        //交易类型
        builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE);
        //C2B 码
        builder.bizSet(BusinessFields.QR_NO, MapUtils.getString(extraParams, Transaction.BARCODE));
        //商户订单号
        builder.bizSet(BusinessFields.ORDER_NO, MapUtils.getString(transaction, Transaction.ORDER_SN));
        //订单总金额，单位为分
        builder.bizSet(BusinessFields.TXN_AMT, MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        //订单时间
        builder.bizSet(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtils.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        //交易币种, 156
        builder.bizSet(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        //地区信息
        builder.bizSet(BusinessFields.AREA_INFO, MapUtils.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        String prnInsIdCd = MapUtils.getString(tradeParams, TransactionParam.PSBC_UNION_PAY_PNR_INS_ID_CD);
        if (!StringUtils.empty(prnInsIdCd)){
            //云闪付交易上送服务商机构标识 "C1000001"
            builder.bizSet(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
        }
        return builder;
    }

    @Override
    public PSBCRequestBuilder getPreCreateRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        //pabcbank 的 builder
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        //交易码
        builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE);
        getDefaultUnionpayRequestBuilder(builder, context);
        //交易类型
        builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_WAP);
        //订单号
        builder.bizSet(BusinessFields.ORDER_NO, MapUtils.getString(transaction, Transaction.ORDER_SN));
        //订单类型 , 10
        builder.bizSet(BusinessFields.ORDER_TYPE, UnionPayQRCodeConstants.ORDER_TYPE_COMMON_CONSUME);
        //订单时间
        builder.bizSet(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtils.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        //支付有效时间
        builder.bizSet(BusinessFields.PAYMENT_VALID_TIME, DEFAULT_TIME_EXPIRE_SECOND + "");
        //订单接收超时时间
        builder.bizSet(BusinessFields.ORDERTIMEOUT, dateFormat.format(new Date(MapUtils.getLongValue(context.getOrder(), DaoConstants.CTIME) + DEFAULT_TIME_EXPIRE_SECOND * 1000)));
        //订单描述
        builder.bizSet(BusinessFields.ORDER_DESC, MapUtils.getString(transaction, Transaction.SUBJECT));
        //持卡人IP
        builder.bizSet(BusinessFields.CUSTOMER_IP, UpayUtil.getLocalHostIp());
        //地区信息
        builder.bizSet(BusinessFields.AREA_INFO, MapUtils.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        //交易金额
        builder.bizSet(BusinessFields.TXN_AMT, MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        //交易币种
        builder.bizSet(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        builder.bizSet(BusinessFields.QRCODE_TYPE, UnionPayQRCodeConstants.QRCODE_TYPE_STATIC);
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        if (payerUid != null) {
            //用户开放标识
            builder.bizSet(BusinessFields.USER_ID, payerUid);
        }
        String prnInsIdCd = MapUtils.getString(tradeParams, TransactionParam.PSBC_UNION_PAY_PNR_INS_ID_CD);
        if (!StringUtils.empty(prnInsIdCd)){
            //云闪付交易上送服务商机构标识 "C1000001"
            builder.bizSet(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
        }
        return builder;
    }

    @Override
    public PSBCRequestBuilder getRefundRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //交易码
        if (Order.SUB_PAYWAY_BARCODE == subPayWay) {
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_REFUND);
            //交易类型
            builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE_REFUND);
        } else if (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay) {
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_REFUND);
            //交易类型
            builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_REFUND);
        }
        getDefaultUnionpayRequestBuilder(builder, context);
        //原始订单号
        builder.bizSet(BusinessFields.ORIG_ORDER_NO, MapUtils.getString(transaction, Transaction.ORDER_SN));
        //原始订单时间
        builder.bizSet(BusinessFields.ORIG_ORDER_TIME, dateFormat.format(new Date(MapUtils.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        //订单号
        builder.bizSet(BusinessFields.ORDER_NO, MapUtils.getString(transaction, Transaction.TSN));
        //订单时间
        builder.bizSet(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtils.getLongValue(transaction,DaoConstants.CTIME))));
        //交易币种
        builder.bizSet(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        //交易金额
        builder.bizSet(BusinessFields.TXN_AMT, MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT) + "");

        return builder;
    }

    @Override
    public PSBCRequestBuilder getRefundQueryRequestBuilder(TransactionContext context) {
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayWay) {
            //交易码
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_REFUND_QUERY);
        } else if (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay) {
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_REFUND_QUERY);
        }
        return builder;
    }

    @Override
    public PSBCRequestBuilder getQueryRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayWay) {
            //交易码
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_QUERY);
            //交易类型
            builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE_QUERY);
        } else if (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay) {
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_QUERY);
            //交易类型
            builder.bizSet(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_QUERY);
        }
        getDefaultUnionpayRequestBuilder(builder, context);
        //订单号
        builder.bizSet(BusinessFields.ORDER_NO, MapUtils.getString(transaction, Transaction.TSN));
        //订单时间
        builder.bizSet(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtils.getLongValue(context.getTransaction(), DaoConstants.CTIME))));

        return builder;
    }

    @Override
    public PSBCRequestBuilder getCloseRequestBuilder(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        PSBCRequestBuilder builder = getDefaultRequestBuilder(context);
        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //交易码
        if (Order.SUB_PAYWAY_BARCODE == subPayWay) {
            //交易码
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_CLOSE);
        } else if (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay) {
            //交易码
            builder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_CLOSE);
        }

        getDefaultUnionpayRequestBuilder(builder, context);

        return builder;
    }

    @Override
    public String resolveNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtils.getIntValue(transaction, Transaction.TYPE);
        if(type == Transaction.TYPE_PAYMENT) {
            return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
        }
        return null;
    }

    @Override
    public Map<String, Object> call(String gatewayUrl, Map<String, Object> request, String privateKey, String sm2Pass) throws MpayException, MpayApiNetworkError, JsonProcessingException {
        return psbcBankClient.call(gatewayUrl, request, privateKey, sm2Pass);
    }

    @Override
    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String respCd = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);//返回的响应码
        String respDesc = MapUtils.getString(result, PSBCResponseFields.RESP_DESC);//响应描述

        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);

        String responseCode = MapUtils.getString(respContent, ResponseFields.RESP_CODE, "");
        String responseMsg = MapUtils.getString(respContent, ResponseFields.RESP_MSG);
        String originalResponseCode = MapUtils.getString(respContent, ResponseFields.ORIG_RESP_CODE, "");
        String originalResponseMsg = MapUtils.getString(respContent, ResponseFields.ORIG_RESP_MSG);
        map.put(PSBCResponseFields.RESP_CODE, respCd);//返回状态码
        map.put(PSBCResponseFields.RESP_DESC, respDesc);//返回信息
        map.put(ResponseFields.RESP_CODE, responseCode);
        map.put(ResponseFields.RESP_MSG, responseMsg);
        map.put(ResponseFields.ORIG_RESP_CODE, originalResponseCode);
        map.put(ResponseFields.ORIG_RESP_MSG, originalResponseMsg);
        String codes = responseCode + originalResponseCode;
        boolean isSuccess = UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(codes) || (UnionPayQRCodeConstants.RESP_CODE_SUCCESS + UnionPayQRCodeConstants.RESP_CODE_SUCCESS).equals(codes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, StringUtil.empty(originalResponseCode) ? responseCode : originalResponseCode, StringUtil.empty(originalResponseMsg) ? responseMsg : originalResponseMsg);
    }

    @Override
    public String buildPayResult(Map<String, Object> result, TransactionContext context) {

        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        setTradeNoBuyerInfoIfExists(respContent, context);
        //响应码
        String respCd = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);

        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCd)){
            return Workflow.RC_TRADE_CANCELED;
        }
        if (PSBCBankConstants.RESP_CODE_PAYING.equalsIgnoreCase(respCd) || PSBCBankConstants.RESP_CODE_PROCESSING.equalsIgnoreCase(respCd)
                || PSBCBankConstants.RESP_CODE_ERROR.equalsIgnoreCase(respCd) || PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR.equalsIgnoreCase(respCd)
                ||PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR_2.equalsIgnoreCase(respCd) || PSBCBankConstants.RESP_CODE_TIMEOUT.equalsIgnoreCase(respCd)){
            return Workflow.RC_IN_PROG;
        }
        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equalsIgnoreCase(respCd)){
            return Workflow.RC_TRADE_CANCELED;
        }
        if (MapUtils.isNotEmpty(respContent)) {
            String respCode = MapUtils.getString(respContent, ResponseFields.RESP_CODE);//云闪付返回状态码

            if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(respCode)) {
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolvePayFund(respContent, context);
                return Workflow.RC_PAY_SUCCESS;
            }
            if (UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(respCode)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();

        String respCd = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.RESP_CODE_PAYING.equalsIgnoreCase(respCd) || PSBCBankConstants.RESP_CODE_PROCESSING.equalsIgnoreCase(respCd)
                || PSBCBankConstants.RESP_CODE_ERROR.equalsIgnoreCase(respCd) || PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR.equalsIgnoreCase(respCd)
                ||PSBCBankConstants.RESP_CODE_THIRD_PART_SYSTEM_ERROR_2.equalsIgnoreCase(respCd) || PSBCBankConstants.RESP_CODE_TIMEOUT.equalsIgnoreCase(respCd)){
            return Workflow.RC_ERROR;
        }
        if (!PSBCBankConstants.RESP_CODE_SUCCESS.equalsIgnoreCase(respCd)){
            return Workflow.RC_TRADE_CANCELED;
        }
        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String respCode = BeanUtil.getPropString(respContent, ResponseFields.RESP_CODE);
            if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(respCode)) {
                //success
                Map<String, Object> wapRequest = new HashMap<String, Object>() {{
                    put(ResponseFields.REDIRECTURL, BeanUtil.getPropString(respContent, ResponseFields.REDIRECTURL));
                }};
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                return Workflow.RC_CREATE_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCd = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.REFUND_ERROR_LIST.contains(respCd)){
            return Workflow.RC_ERROR;
        }
        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        if (MapUtils.isNotEmpty(respContent)) {
            String respCode = MapUtils.getString(respContent, ResponseFields.RESP_CODE);
            switch (respCode){
                case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                    resolveRefundFund(respContent, context);
                    return Workflow.RC_REFUND_SUCCESS;
                case UnionPayQRCodeConstants.RESP_CODE_SYSTEM_ERROR:
                case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                    return Workflow.RC_RETRY;
                default:
                    return Workflow.RC_ERROR;
            }
        } else {
            return doRefundQuery(context);
        }
    }

    @Override
    public String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();
        int transactionType = MapUtils.getIntValue(transaction, Transaction.TYPE);
        String respCd = MapUtils.getString(result, PSBCResponseFields.RESP_CODE);
        String content = MapUtils.getString(result, PSBCResponseFields.RESP_CONTENT);
        Map<String, Object> respContent = JSONObject.parseObject(content, Map.class);
        setTradeNoBuyerInfoIfExists(respContent, context);
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.QUERY_ERROR_LIST.contains(respCd)){
            return Workflow.RC_ERROR;
        }

        //原订单未成功需要继续轮训
        if (PSBCBankConstants.RESP_CODE_CLOSE_PAY_ERROR.equals(respCd) || PSBCBankConstants.RESP_CODE_PROCESSING.equals(respCd)){
            return Workflow.RC_IN_PROG;
        }

        //code:0000，响应成功
        if (Objects.equals(respCd, PSBCBankConstants.RESP_CODE_SUCCESS)) {
            if (MapUtils.isNotEmpty(respContent)) {
                String respCode = MapUtils.getString(respContent, ResponseFields.RESP_CODE);
                String originRespCode = MapUtils.getString(respContent, ResponseFields.ORIG_RESP_CODE, "");
                if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(respCode)) {
                    switch (originRespCode) {
                        case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                            resolvePayFund(respContent, context);
                            return Workflow.RC_PAY_SUCCESS;
                        case UnionPayQRCodeConstants.RESP_CODE_ORDER_UNKNOWN:
                        case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                        case UnionPayQRCodeConstants.RESP_CODE_TRADE_NOT_EXIST:
                        case UnionPayQRCodeConstants.RESP_CODE_TRADE_NOT_EXIST_OR_ERROR:
                        case UnionPayQRCodeConstants.RESP_CODE_TRADE_OPERATE_LIMIT:
                            return Workflow.RC_IN_PROG;
                        default:
                            if (Transaction.TYPE_PAYMENT == transactionType && UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(respCode)) {
                                return Workflow.RC_TRADE_CANCELED;
                            }
                            return Workflow.RC_ERROR;
                    }
                } else if (UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(respCode)
                        && !UnionPayQRCodeConstants.RESP_CODE_TRADE_RISK_FAIL.equals(respCode)
                        && !UnionPayQRCodeConstants.RESP_CODE_NOT_IN_BUSINESS_TIME.equals(respCode)) {
                    //如果是因为非风控或者限流导致的查单失败，则返回错误
                    return Workflow.RC_ERROR;
                }
            }
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String buildCloseResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCd = BeanUtil.getPropString(result, PSBCResponseFields.RESP_CODE);////响应码
        String respDesc = BeanUtil.getPropString(result, PSBCResponseFields.RESP_DESC);////响应码

        boolean needRefund = false;
        if (PSBCBankConstants.PROTOCOL_ERROR_LIST.contains(respCd)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if (PSBCBankConstants.RESP_CODE_ORDER_SUCCESS.equals(respCd)){
            if ("该订单交易已成功".equals(respDesc)){
                needRefund = true;
            }
        }
        if (PSBCBankConstants.QUERY_ERROR_LIST.contains(respCd)){
            return Workflow.RC_ERROR;
        }
        //云闪付根据 respCd 判断关闭订单状态
        if (Objects.equals(respCd, PSBCBankConstants.RESP_CODE_SUCCESS)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        if(needRefund){
            String rcFlag = refund(context);
            if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
                return Workflow.RC_CANCEL_SUCCESS;
            }else{
                return rcFlag;
            }
        }
        return Workflow.RC_ERROR;
    }

    /**
     * unionpay 公共请求参数
     * @param context
     */
    private PSBCRequestBuilder getDefaultUnionpayRequestBuilder(PSBCRequestBuilder builder, TransactionContext context) {

        builder.bizSet(PSBCBusinessFields.VERSION, "1.0.0");

        return builder;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        //邮储付款人信息是密文，无法获取具体信息

        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = BeanUtil.getPropString(result, ResponseFields.VOUCHER_NUM);
            if (!StringUtil.empty(tradeNo)) {

                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    /**
     * 解析返回金额相关信息
     *
     * @param context
     */
    public static void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
        }
        List<Map<String, Object>> payments = new ArrayList<>();
        String couponInfo = MapUtils.getString(result, ResponseFields.COUPON_INFO);
        List<Map<String, Object>> couponList = null;
        if(!StringUtil.empty(couponInfo)){
            try {
                couponList = objectMapper.readValue(Base64.getDecoder().decode(couponInfo), List.class);
            } catch (IOException e) {
                logger.warn("parse coupon info error", e);
            }
        }

        long couponSum = 0;
        if (CollectionUtils.isNotEmpty(couponList)) {
            for (Map<String, Object> coupon : couponList) {
                String spnsrId = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_SPNSR_ID); //出资方
                long amount = BeanUtil.getPropLong(coupon, ResponseFields.COUPON_INFO_OFFST_AMT);
                String couponId = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_ID);
                String couponType = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_TYPE);
                couponSum = couponSum + amount;
                String paymentType;
                if (UnionPayQRCodeConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)) {
                    paymentType = UnionPayQRCodeConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                } else {
                    paymentType = UnionPayQRCodeConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                }
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, amount,
                        Transaction.PAYMENT_SOURCE, couponId,
                        Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                        Transaction.PAYMENT_TYPE, paymentType
                ));
            }
        }
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            //此处无法获取payerInfo信息，故不能区分卡类型， 默认是银行卡
            payments.add(CollectionUtil.hashMap(
                            Transaction.PAYMENT_AMOUNT, amount,
                            Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_BANKCARD,
                            Transaction.PAYMENT_TYPE, Payment.TYPE_BANKCARD
                    )
            );
        }
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (!StringUtil.empty(BeanUtil.getPropString(result, ResponseFields.PAY_AMT))) {
            long payAmt = BeanUtil.getPropLong(result, ResponseFields.PAY_AMT);
            transaction.put(Transaction.PAID_AMOUNT, payAmt);
        }
    }

    public void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), MapUtils.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }
}
