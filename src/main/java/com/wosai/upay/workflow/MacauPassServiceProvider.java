package com.wosai.upay.workflow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.macaupass.MacauPassClient;
import com.wosai.mpay.api.macaupass.MacauPassRequestBuilder;
import com.wosai.mpay.api.macaupass.constants.MacauPassProtocolFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassRequestFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassResponseFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassServiceConstants;
import com.wosai.mpay.api.macaupass.enums.*;
import com.wosai.mpay.api.macaupass.utils.MacauPassUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.ExternalServiceException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING;
import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING_MESSAGE;
import static com.wosai.upay.util.ProviderUtil.getOrDefault;
import static com.wosai.upay.util.ProviderUtil.updateMapIfResponseNotNull;

/**
 * @version 1.0
 * @author: yuhai
 * @program: upay-gateway
 * @className MacaupassServiceProvider
 * @description:
 * @create: 2025-04-29 09:21
 **/
@Slf4j
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class MacauPassServiceProvider extends AbstractServiceProvider{
    protected static final Logger logger = LoggerFactory.getLogger(MacauPassServiceProvider.class);

    protected  static final String EMPTY_STR = "";

    protected static final int BODY_SIZE = 64;

    protected static final String CURRENCY_MOP = "MOP";

    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    @Resource
    private MacauPassClient macauPassClient;

    public static final String NAME = "provider.macaupass";



    protected static final Map<String, String> RESPONSE_PAY_CHANNEL_TYPE_PAYMENT_TYPE_CONFIG = CollectionUtil.hashMap(
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.MPAY, Payment.TYPE_WALLET_MPAY,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.ALIPAY, Payment.TYPE_WALLET_ALIPAY,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.WECHAT, Payment.TYPE_WALLET_WEIXIN,

            // 暂无对应，直接拿响应填入
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.TFPAY, MacauPassResponseFieldsConstants.PayChannelTypeConstants.TFPAY,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.CGB, MacauPassResponseFieldsConstants.PayChannelTypeConstants.CGB,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.BOC, MacauPassResponseFieldsConstants.PayChannelTypeConstants.BOC,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.ICBC, MacauPassResponseFieldsConstants.PayChannelTypeConstants.ICBC,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.UEPAY, MacauPassResponseFieldsConstants.PayChannelTypeConstants.UEPAY,
            MacauPassResponseFieldsConstants.PayChannelTypeConstants.LUSO, MacauPassResponseFieldsConstants.PayChannelTypeConstants.LUSO
    );


    public MacauPassServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(MacauPassRequestFieldsConstants.TRANS_AMOUNT, MacauPassResponseFieldsConstants.REFUND_AMOUNT, MacauPassResponseFieldsConstants.AMOUNT));
        dateFormat = new SafeSimpleDateFormat(MacauPassProtocolFieldsConstants.DATE_SIMPLE_RESPONSE_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_MACAU_PASS;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {

        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String currency = getTradeCurrency(transaction);
        if (!CURRENCY_MOP.equals(currency)) {
            // 支付币种需要是澳门元
            logger.info("支付币种需要是澳门元 {}", tsn);
            return false;
        }
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.MACAU_PASS_TRADE_PARAMS);
    }

    /**
     * B扫C
     * @param context
     * @param resume
     * @return
     */
    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        String orgId = getOrgId(tradeParams);
        String service = MacauPassServiceConstants.TRADE_SPOTPAY;
        String channelType = MacauPassChannelTypeEnum.SYSTEM.getType();

        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String currency = getTradeCurrency(transaction);
        String transName = MapUtils.getString(transaction, Transaction.SUBJECT);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        String transAmount = com.wosai.mpay.util.StringUtils.cents2yuan(amount);
        String buyerIdentityCode = MapUtils.getString(extraParams, Transaction.BARCODE);
        String identityCodeType = MacauPassProtocolFieldsConstants.BARCODE;
        String transCreateTime = MacauPassUtil.formatRequestDate(new Date());
        Map<String, Object> extendParams = generateExtendParams(tradeParams);

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, channelType);
        requestBuilder.set(MacauPassRequestFieldsConstants.TRANS_NAME, transName);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, tsn);
        requestBuilder.set(MacauPassRequestFieldsConstants.CURRENCY, currency);
        requestBuilder.set(MacauPassRequestFieldsConstants.TRANS_AMOUNT, transAmount);
        requestBuilder.set(MacauPassRequestFieldsConstants.BUYER_IDENTITY_CODE, buyerIdentityCode);
        requestBuilder.set(MacauPassRequestFieldsConstants.IDENTITY_CODE_TYPE, identityCodeType);
        requestBuilder.set(MacauPassRequestFieldsConstants.TRANS_CREATE_TIME, transCreateTime);

        requestBuilder.set(MacauPassRequestFieldsConstants.EXTEND_PARAMS, extendParams);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(service, requestBuilder.build(), tradeParams);

        } catch (Exception ex) {
            logger.error("failed to call macau pass", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        if(!requestIsSuccess(result)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> data = MapUtil.getMap(result, MacauPassResponseFieldsConstants.DATA);
        if (null == data) {
            return Workflow.RC_ERROR;
        }
        updateTransactionCommonInfo(transaction, context.getOrder(), data, service);

        String status = MapUtil.getString(data, MacauPassResponseFieldsConstants.TRANS_STATUS);
        if(MacauPassTradeStatusEnum.isTradeCompleted(status)){
            if(MacauPassTradeStatusEnum.isTradeSuccess(status)){
                // success
                updateTransactionPaymentInfo(transaction, data);
                return Workflow.RC_PAY_SUCCESS;
            } else {
                // fail or close
                return Workflow.RC_TRADE_CANCELED;
            }
        } else if(MacauPassTradeStatusEnum.isTradeUnknow(status)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String cancel(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);

        String orgId = getOrgId(tradeParams);
        String service = MacauPassServiceConstants.TRADE_CANCEL;
        String channelType = MacauPassChannelTypeEnum.SYSTEM.getType();

        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, channelType);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, tsn);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(service, requestBuilder.build(), tradeParams);

        } catch (Exception ex) {
            logger.error("failed to call macau pass", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        if(!requestIsSuccess(result)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> data = MapUtil.getMap(result, MacauPassResponseFieldsConstants.DATA);
        if (null == data) {
            return Workflow.RC_ERROR;
        }
        updateTransactionCommonInfo(transaction, context.getOrder(), data, service);
        return Workflow.RC_CANCEL_SUCCESS;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);

        int type = MapUtils.getIntValue(transaction, Transaction.TYPE);
        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        String orgId = getOrgId(tradeParams);
        String service = MacauPassServiceConstants.TRADE_QUERY;
        String channelType = MacauPassChannelTypeEnum.SYSTEM.getType();

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, channelType);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, tsn);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(service, requestBuilder.build(), tradeParams);
        } catch (Exception ex) {
            logger.error("failed to call macau pass", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);

        if(!requestIsSuccess(result)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> data = MapUtil.getMap(result, MacauPassResponseFieldsConstants.DATA);
        if (null == data) {
            return Workflow.RC_ERROR;
        }
        updateTransactionCommonInfo(transaction, context.getOrder(), data, service);

        String status = MapUtil.getString(data, MacauPassResponseFieldsConstants.TRANS_STATUS);
        if(MacauPassTradeStatusEnum.isTradeCompleted(status)){
            if(MacauPassTradeStatusEnum.isTradeSuccess(status)){
                // success
                updateTransactionPaymentInfo(transaction, data);
                return Workflow.RC_PAY_SUCCESS;
            } else {
                // fail or close
                return Workflow.RC_TRADE_CANCELED;
            }
        } else if(MacauPassTradeStatusEnum.isTradeUnknow(status)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_IN_PROG;
    }


    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        // 该接口幂等
        return doRefund(context);
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);


        String orgId = getOrgId(tradeParams);
        String service = MacauPassServiceConstants.TRADE_REFUND;
        String channelType = MacauPassChannelTypeEnum.SYSTEM.getType();

        String orderSn = MapUtils.getString(transaction, Transaction.ORDER_SN);
        String tsn = MapUtils.getString(transaction, Transaction.TSN);

        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        String transAmount = com.wosai.mpay.util.StringUtils.cents2yuan(amount);

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, channelType);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, orderSn);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_REFUND_ID, tsn);
        requestBuilder.set(MacauPassRequestFieldsConstants.REFUND_AMOUNT, transAmount);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(service, requestBuilder.build(), tradeParams);
        } catch (Exception ex) {
            logger.error("failed to call macau pass", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        if(!requestIsSuccess(result)) {
            return Workflow.RC_RETRY;
        }

        Map<String, Object> data = MapUtil.getMap(result, MacauPassResponseFieldsConstants.DATA);
        if (null == data) {
            return Workflow.RC_RETRY;
        }
        // 正常返回响应编码即为成功。
        updateTransactionRefundInfo(context, data);

        return Workflow.RC_REFUND_SUCCESS;
    }

    /**
     * 请求澳门通接口
     * @param service
     * @param request
     * @param tradeParams
     * @return
     */
    private Map<String, Object> retryIfNetworkException(String service, Map<String, Object> request, Map<String, Object> tradeParams) {
        String gatewayUrl = getGatewayUrl();
        String privateKey = getPrivateKey(tradeParams);
        String publicKey = getPublicKey(tradeParams);
        try {
            return new RetryUtil<Map<String, Object>>()
                    .retry(new RetryUtil.TimingStrategy.Builder().setRetry(2, 50, 1.0).build())
                    .method(() -> {
                        try {
                            return macauPassClient.call(gatewayUrl, service, request, privateKey, publicKey);
                        } catch (MpayException | MpayApiNetworkError e) {
                            log.error("{}: 调用澳门通接口异常: method={}, request={}, error={}",
                                    NAME, service, JacksonUtil.toJsonString(request), e.getMessage());
                            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
                        }
                    })
                    .on(throwable -> true)
                    .until(Objects::nonNull)
                    .execute();
        } catch (Exception e) {
            log.error("{}: 调用澳门通接口异常: method={}, request={}, error={}",
                    NAME, service, JacksonUtil.toJsonString(request), e.getMessage());
            throw new ExternalServiceException(UPAY_PROVIDER_STATUS_LIMITING, UPAY_PROVIDER_STATUS_LIMITING_MESSAGE);
        }

    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);

        // 默认 C 扫 B,
        String service = MacauPassServiceConstants.TRADE_PRECREATE;
        boolean isJSAPI = false;
        if (SubPayway.WAP.getCode().equals(subPayway) || SubPayway.MINI.getCode().equals(subPayway)) {
            // 调 JS Api
            service = MacauPassServiceConstants.TRADE_CREATE;
            isJSAPI = true;
        }

        String orgId = getOrgId(tradeParams);
        String channelType = MacauPassChannelTypeEnum.SYSTEM.getType();

        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        String currency = getTradeCurrency(transaction);
        String subject = MapUtils.getString(transaction, Transaction.SUBJECT);


        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        String totalFee = com.wosai.mpay.util.StringUtils.cents2yuan(amount);
        String body = MapUtils.getString(transaction, Transaction.BODY);
        if (StringUtils.isEmpty(body)) {
            body = subject;
        }
        body = MacauPassUtil.cleanAndTruncate(body, BODY_SIZE);

        String notifyUrl = getNotifyUrl(notifyHost, context);
        Map<String, Object> extendParams = generateExtendParams(tradeParams);

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, channelType);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, tsn);
        requestBuilder.set(MacauPassRequestFieldsConstants.SUBJECT, subject);
        requestBuilder.set(MacauPassRequestFieldsConstants.TOTAL_FEE, totalFee);
        requestBuilder.set(MacauPassRequestFieldsConstants.BODY, body);
        requestBuilder.set(MacauPassRequestFieldsConstants.CURRENCY, currency);
        if (!StringUtils.isEmpty(notifyUrl)) {
            requestBuilder.set(MacauPassRequestFieldsConstants.NOTIFY_URL, notifyUrl);
        }
        requestBuilder.set(MacauPassRequestFieldsConstants.EXTEND_PARAMS, extendParams);

        if (isJSAPI) {
            // JSAPI 需要加入如下参数
            String payChannel = getPayChannel(payway);
            String productCode = getProductCode(payway, subPayway);
            String subAppId = getSubAppId(payway, productCode, tradeParams);
            String scene = getScene(payway, productCode);
            String userId = MapUtil.getString(extraParams, Transaction.PAYER_UID);

            requestBuilder.set(MacauPassRequestFieldsConstants.PAY_CHANNEL, payChannel);
            requestBuilder.set(MacauPassRequestFieldsConstants.PRODUCT_CODE, productCode);
            if (!StringUtils.isEmpty(subAppId)) {
                requestBuilder.set(MacauPassRequestFieldsConstants.SUB_APPID, subAppId);
            }
            if (!StringUtils.isEmpty(scene)) {
                requestBuilder.set(MacauPassRequestFieldsConstants.SCENE, scene);
            }
            requestBuilder.set(MacauPassRequestFieldsConstants.USER_ID, userId);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(service, requestBuilder.build(), tradeParams);

        } catch (Exception ex) {
            logger.error("failed to call macau pass", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        if(!requestIsSuccess(result)) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> data = MapUtil.getMap(result, MacauPassResponseFieldsConstants.DATA);
        if (null == data) {
            return Workflow.RC_ERROR;
        }
        updateTransactionCommonInfo(transaction, context.getOrder(), data, service);

        if (isJSAPI) {
            String payInfo = MapUtil.getString(data, MacauPassResponseFieldsConstants.PAY_INFO, EMPTY_STR);
            try {
                Map<String, Object> payInfoMap = JsonUtil.jsonStringToObject(payInfo, new TypeReference<Map<String, Object>>() {});

                if (Payway.ALIPAY.getCode().equals(payway) || Payway.ALIPAY2.getCode().equals(payway)) {
                    // 支付宝转义
                    String tradeNo = MapUtil.getString(payInfoMap, "tradeNo", "");
                    String tradeNO = MapUtil.getString(payInfoMap, "tradeNO", "");
                    if (StringUtils.isEmpty(tradeNO) && !StringUtils.isEmpty(tradeNo)) {
                        payInfoMap.put("tradeNO", tradeNo);
                    }
                }

                extraOutFields.put(Transaction.WAP_PAY_REQUEST, payInfoMap);
            } catch (Exception e) {
                logger.error("trans payInfo failed. payInfo: " + payInfo, e);
                return Workflow.RC_ERROR;
            }
        } else {
            String qrCode = MapUtil.getString(data, MacauPassResponseFieldsConstants.QR_CODE, EMPTY_STR);
            if (StringUtils.isEmpty(qrCode)) {
                logger.error("get qr code failed. qrCode: " + qrCode);
                return Workflow.RC_ERROR;
            }
            extraOutFields.put(Transaction.QRCODE, qrCode);
        }
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT){
            return null;
        }
        String status = query(context);
        if (Workflow.RC_PAY_SUCCESS.equals(status)) {
            return status;
        }
        return null;
    }

    /**
     * 更新付款信息
     * @param transaction
     * @param data
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> data){

        // 更新支付完成时间
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, data, MacauPassResponseFieldsConstants.PAY_TIME, object -> parseTimeString((String) object));

        // 更新实际支付金额
        updateMapIfResponseNotNull(transaction, Transaction.PAID_AMOUNT, data, MacauPassResponseFieldsConstants.BUYER_PAY_AMOUNT, object -> StringUtils.yuan2cents((String) object));

        // 计算支付明细
        Long effectiveAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);

        //获取用户实付金额
        Long paidAmount = MapUtil.getLong(transaction, Transaction.PAID_AMOUNT, effectiveAmount);

        List<Map<String,Object>> payments = new ArrayList<>();
        long couponSum = effectiveAmount - paidAmount;
        if(couponSum > 0){
            // 澳门通可能会有discount 和coupon 优惠, 统一算作discount优惠
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_AMOUNT, couponSum
                    )
            );
        }

        String payChannelType = MapUtil.getString(data, MacauPassResponseFieldsConstants.PAY_CHANNEL_TYPE, EMPTY_STR);
        String currency = MapUtil.getString(data, MacauPassResponseFieldsConstants.CURRENCY, EMPTY_STR);
        String paymentType  = RESPONSE_PAY_CHANNEL_TYPE_PAYMENT_TYPE_CONFIG.getOrDefault(payChannelType, payChannelType);

        if(paidAmount > 0){
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, paymentType,
                            Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                            Transaction.PAYMENT_AMOUNT, paidAmount,
                            Transaction.CURRENCY, currency
                    )
            );
        }
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }

    /**
     * 更新退款信息
     * @param context
     * @param data
     */
    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> data){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);

        // 解析 澳門通系統的交易流水ID
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, data, MacauPassResponseFieldsConstants.TRANS_ID);
        updateMapIfResponseNotNull(extraOutFields, Transaction.TRADE_NO, data, MacauPassResponseFieldsConstants.TRANS_ID);

        // 解析实际退款金额,元
        updateMapIfResponseNotNull(extraOutFields, Transaction.EFFECTIVE_REFUND_AMOUNT, data, MacauPassResponseFieldsConstants.USER_PAY_AMOUNT);

        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }


    /**
     * 更新通用字段
     * @param transaction
     * @param data
     */
    protected void updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> order, Map<String, Object> data, String service) {

        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);

        String payChannelType = MapUtil.getString(data, MacauPassResponseFieldsConstants.PAY_CHANNEL_TYPE, EMPTY_STR);
        if (!(MacauPassServiceConstants.TRADE_CREATE.equals(service)
                && MacauPassResponseFieldsConstants.PayChannelTypeConstants.ALIPAY.equals(payChannelType))) {
            // 解析 澳門通系統的交易流水ID, JSAPI支付时，alipay channel返回的trans_id不能作为trade_no, 要以查单返回的为准
            // 解析支付源订单号, 使用澳门通的trans_id 作为唯一标识填入 trade_no
            updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, data, MacauPassResponseFieldsConstants.TRANS_ID);
            updateMapIfResponseNotNull(extraOutFields, Transaction.TRADE_NO, data, MacauPassResponseFieldsConstants.TRANS_ID);
            updateMapIfResponseNotNull(order, Transaction.TRADE_NO, data, MacauPassResponseFieldsConstants.TRANS_ID);
        }

        updateMapIfResponseNotNull(extraOutFields, Transaction.CHANNEL_TRANS_NO, data, MacauPassResponseFieldsConstants.TRADE_NO);

        // 解析用户信息
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID, data, MacauPassResponseFieldsConstants.BUYER_USER_ID);
    }


    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {

        Map<String, String> resultCodeMap = MacauPassUtil.getResultCodeMap(result);
        String systemErrorCode = resultCodeMap.get(MacauPassResponseFieldsConstants.IS_SUCCESS);
        String error = resultCodeMap.get(MacauPassResponseFieldsConstants.ERROR);
        String resultCode = resultCodeMap.get(MacauPassResponseFieldsConstants.RESULT_CODE);
        String resultMsg = resultCodeMap.get(MacauPassResponseFieldsConstants.RESULT_MSG);

        Boolean isSuccess = MacauPassSystemErrorEnum.isSuccess(systemErrorCode) && MacauPassErrorCodeEnum.isSuccess(resultCode);

        Map<String, Object> dataResult = new HashMap<>();
        dataResult.put(MacauPassResponseFieldsConstants.RESULT_CODE, resultCode);
        dataResult.put(MacauPassResponseFieldsConstants.RESULT_MSG, resultMsg);

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(MacauPassResponseFieldsConstants.IS_SUCCESS, systemErrorCode);
        map.put(MacauPassResponseFieldsConstants.ERROR, error);
        map.put(MacauPassResponseFieldsConstants.DATA, dataResult);

        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess,
                getOrDefault(resultCode, systemErrorCode), resultMsg);
    }


    /**
     * 生成 ExtendParams
     * @param tradeParams
     * @return
     */
    protected Map<String, Object> generateExtendParams(Map<String, Object> tradeParams) {
        Map<String, Object> params = new HashMap<>();

        String subMerchantId = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_SUB_MERCHANT_ID);
        String subMerchantName = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_SUB_MERCHANT_NAME);
        String subMerchantIndustry = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_SUB_MERCHANT_INDUSTRY);
        String storeName = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_STORE_NAME);
        String storeId = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_STORE_ID);
        String terminalId = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_TERMINAL_ID);

        if (!StringUtils.isEmpty(subMerchantId)) {
            params.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_ID, subMerchantId);
        }
        if (!StringUtils.isEmpty(subMerchantName)) {
            params.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_NAME, subMerchantName);
        }
        if (!StringUtils.isEmpty(subMerchantIndustry)) {
            params.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_INDUSTRY, subMerchantIndustry);
        }
        if (!StringUtils.isEmpty(storeName)) {
            params.put(MacauPassRequestFieldsConstants.STORE_NAME, storeName);
        }
        if (!StringUtils.isEmpty(storeId)) {
            params.put(MacauPassRequestFieldsConstants.STORE_ID, storeId);
        }
        if (!StringUtils.isEmpty(terminalId)) {
            params.put(MacauPassRequestFieldsConstants.TERMINAL_ID, terminalId);
        }
        return params;
    }

    /**
     * 判断请求是否成功
     * @param result
     * @return
     */
    protected boolean requestIsSuccess(Map<String, Object> result) {
        Map<String, String> resultCodeMap = MacauPassUtil.getResultCodeMap(result);
        String resultCode = resultCodeMap.get(MacauPassResponseFieldsConstants.RESULT_CODE);
        String isSuccess = resultCodeMap.get(MacauPassResponseFieldsConstants.IS_SUCCESS);
        return MacauPassSystemErrorEnum.isSuccess(isSuccess) && MacauPassErrorCodeEnum.isSuccess(resultCode);
    }


    /**
     * 获取pay channel
     * @param payway
     * @return
     */
    protected String getPayChannel(int payway) {
        if (Payway.ALIPAY.getCode().equals(payway) || Payway.ALIPAY2.getCode().equals(payway)) {
            return MacauPassPayChannelEnum.ALIPAY.getType();
        } else if (Payway.WEIXIN.getCode().equals(payway)) {
            return MacauPassPayChannelEnum.WECHAT.getType();
        } else if (Payway.MACAU_PASS.getCode().equals(payway)) {
            return MacauPassPayChannelEnum.MPAY.getType();
        } else {
            throw new ExternalServiceException(UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMIT, UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMIT_MESSAGE);
        }
    }

    /**
     * 获取 sub app id
     * @param payway
     * @return
     */
    protected String getSubAppId(int payway, String productCode, Map<String, Object> tradeParams) {
        String subAppId = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_WEIXIN_SUB_APPID);

        if (Payway.WEIXIN.getCode().equals(payway)) {
            return subAppId;
        } else if (Payway.MACAU_PASS.getCode().equals(payway) && MacauPassProductCodeEnum.MINIAPP.getCode().equals(productCode)) {
            return subAppId;
        }
        return EMPTY_STR;
    }

    /**
     * 获取 scene
     * @param payway
     * @return
     */
    protected String getScene(int payway, String productCode) {

        if (Payway.MACAU_PASS.getCode().equals(payway) && MacauPassProductCodeEnum.MINIAPP.getCode().equals(productCode)) {
            return MacauPassSceneEnum.ONLINE.getCode();
        }
        return EMPTY_STR;
    }

    /**
     * 获取 product code
     * @param payway
     * @return
     */
    protected String getProductCode(int payway, int subpayway) {
        if (Payway.MACAU_PASS.getCode().equals(payway) && SubPayway.MINI.getCode().equals(subpayway)) {
            return MacauPassProductCodeEnum.MINIAPP.getCode();
        } else {
            return MacauPassProductCodeEnum.JSAPI.getCode();
        }
    }

    /**
     * 获取OrgId
     * @param tradeParams
     * @return
     */
    protected String getOrgId(Map<String, Object> tradeParams) {
        return MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_ORG_ID);
    }

    /**
     * 获取私钥
     * @param tradeParams
     * @return
     */
    protected String getPrivateKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_PRIVATE_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    /**
     * 获取公钥
     * @param tradeParams
     * @return
     */
    protected String getPublicKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.MACAU_PASS_PUBLIC_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    /**
     * 获取网关地址
     * @return
     */
    protected  String getGatewayUrl() {
        return ApolloConfigurationCenterUtil.getProviderGateway(getName(), "");
    }
}
