package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.cmcc.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * Created by lihebin on 03/04/2018.
 */
public class CMCCServiceProvider  extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(CMCCServiceProvider.class);
    protected String notifyHost;
    protected String expireMinute = 60 * 24 + ""; //1 day
    protected static final String NAME = "provider.cmcc";
    protected int retryTimes = 3;

    @Autowired
    protected CMCCWalletClient client;


    public CMCCServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(ProtocolFields.DATE_FORMAT);
    }


    @Override
    public String getName() {
        return NAME;
    }


    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_CMCC == MapUtil.getIntValue(transaction, Transaction.PAYWAY)
                && Order.SUB_PAYWAY_WAP != subPayway
                && Order.SUB_PAYWAY_QRCODE != subPayway) {
            if (getTradeParams(transaction) != null) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    protected Map<String, Object> retryIfNetworkException(Map<String, Object> config, Map<String, Object> request, int times, String opFlag, String orderSn) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return call(request, opFlag, config);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in cmcc {}", opFlag, ex);
                request.put(BusinessFields.REQUEST_ID, orderSn + System.currentTimeMillis());
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }


    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extended = (Map<String, Object>) BeanUtil.getProperty(transaction,Transaction.EXTENDED_PARAMS);
        changeTradeParamsFromExtended(extended, transaction);
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);

        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARACTER_SET, ProtocolFields.CHARSET_UTF8_TYPE);
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if (upayOrderNumber != null) {
            builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        }
        builder = getDefaultRequestBuilder(context, builder);
        builder.set(BusinessFields.REQUEST_ID, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessFields.TYPE, CMCCWalletConfig.PAY_TYPE);
        builder.set(BusinessFields.VERSION, ProtocolFields.PAY_VER);

        builder.set(BusinessFields.AMOUNT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.CURRENCY, ProtocolFields.FEE_TYPE_RMB);
        long ctime = BeanUtil.getPropLong(transaction, DaoConstants.CTIME);
        builder.set(BusinessFields.ORDER_DATE, this.dateFormat.format(new Date(ctime)));
        builder.set(BusinessFields.ORDER_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.PERIOD, ProtocolFields.PERIOD_NUM);
        builder.set(BusinessFields.PERIOD_UNIT, ProtocolFields.PERIOD_UNIT);
        builder.set(BusinessFields.PRODUCT_NAME, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.RESERVED_1, BeanUtil.getPropString(transaction, Transaction.REFLECT));
        builder.set(BusinessFields.USER_TOKEN, BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.EXTRA_PARAMS),Transaction.BARCODE));
        builder.set(BusinessFields.COU_FLAG, ProtocolFields.FLAG_TRUE_STATUS);
        builder.set(BusinessFields.VCH_FLAG, ProtocolFields.FLAG_FALSE_STATUS);
        builder.set(BusinessFields.CASH_FLAG, ProtocolFields.FLAG_FALSE_STATUS);
        builder.set(BusinessFields.PIK_FLAG, ProtocolFields.FLAG_FALSE_STATUS);
        builder.set(BusinessFields.POI_FLAG, ProtocolFields.FLAG_FALSE_STATUS);
        builder.set(BusinessFields.OPR_ID, BeanUtil.getPropString(transaction, Transaction.OPERATOR));

        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = call(request, OP_PAY, config);
        } catch (MpayException e) {
            logger.warn("encountered in cmcc {}", OP_PAY, e);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            logger.warn("encountered ioex in cmcc {}", OP_PAY, e);
            return Workflow.RC_IN_PROG;
        } catch (UnsupportedEncodingException e) {
            logger.warn("encountered ioex in cmcc {}", OP_PAY, e);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        String code = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if (!CMCCWalletConstants.RET_CODE_SUCCESS.equals(code)) {
            if (CMCCWalletConstants.RET_CODE_ORDER_IN_PROGRESS.equals(code) || CMCCWalletConstants.RET_CODE_SYSTEM_ERROR.equals(code) || CMCCWalletConstants.RET_CODE_DATA_COM_ERROR.equals(code)) {
                return Workflow.RC_IN_PROG;
            }else if(CMCCWalletConstants.PAY_FAIL_ERR_CODE_LISTS.contains(code)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_ERROR;
        }
        List payments = resolvePayFund(result, context);
        Map extraOutFields = CollectionUtil.hashMap(Transaction.PAYMENTS, payments);
        transaction.put(Transaction.TRADE_NO, BeanUtil.getPropString(result, ResponseFields.ORDER_ID));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        return Workflow.RC_PAY_SUCCESS;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARACTER_SET, ProtocolFields.CHARSET_UTF8_TYPE);
        builder = getDefaultRequestBuilder(context, builder);
        builder.set(BusinessFields.REQUEST_ID, BeanUtil.getPropString(transaction, Transaction.TSN) + System.currentTimeMillis());
        builder.set(BusinessFields.TYPE, CMCCWalletConfig.CANCEL_TYPE);
        builder.set(BusinessFields.VERSION, ProtocolFields.OTHER_VER);

        builder.set(BusinessFields.OREQUEST_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        Date date = new Date(BeanUtil.getPropLong(transaction, DaoConstants.CTIME));
        builder.set(BusinessFields.OORDER_DATE, this.dateFormat.format(date));
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, request, retryTimes, OP_CANCEL, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        } catch (Exception ex) {
            logger.error("failed to call cmcc cancel", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        if (!CMCCWalletConstants.RET_CODE_SUCCESS.equals(code)) {
            if (CMCCWalletConstants.RET_CODE_SYSTEM_ERROR.equals(code) || CMCCWalletConstants.RET_CODE_DATA_COM_ERROR.equals(code)) {
                return Workflow.RC_RETRY;
            }
            if (CMCCWalletConstants.CANCEL_ORDER_BEGIN_1.equals(code) || CMCCWalletConstants.CANCEL_ORDER_BEGIN_2.equals(code)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_CANCEL_SUCCESS;
    }

    @Override
    public String query(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> result;
        builder = getDefaultRequestBuilder(context, builder);
        builder.set(BusinessFields.REQUEST_ID, BeanUtil.getPropString(transaction, Transaction.TSN) + System.currentTimeMillis());
        builder.set(BusinessFields.TYPE, CMCCWalletConfig.OLD_QUERY_TYPE);
        builder.set(BusinessFields.VERSION, ProtocolFields.OTHER_VER);
        builder.set(BusinessFields.ORDER_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));

        Map<String, Object> request = builder.build();
        try {
            result = retryIfNetworkException(config, request, retryTimes, OP_QUERY, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        } catch (Exception ex) {
            logger.error("failed to call cmcc query", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if (!CMCCWalletConstants.RET_CODE_SUCCESS.equals(code)) {
            if (CMCCWalletConstants.RET_CODE_SYSTEM_ERROR.equals(code) || CMCCWalletConstants.RET_CODE_DATA_COM_ERROR.equals(code)) {
                return Workflow.RC_IN_PROG;
            }
            if (CMCCWalletConstants.QUERY_FAIL_ERR_CODE_LISTS.contains(code)) {
                return Workflow.RC_ERROR;
            }
            return Workflow.RC_ERROR;
        }
        String orderStatus = BeanUtil.getPropString(result, ResponseFields.STATUS);
        switch (orderStatus) {
            case CMCCWalletConstants.ORDER_STATUS_SUCCESS_QUERY:
                resolvePayFund(result, context);

                return Workflow.RC_PAY_SUCCESS;
            case CMCCWalletConstants.ORDER_STATUS_PAY_OVERDUE:
            case CMCCWalletConstants.ORDER_STATUS_CLOSED:
            case CMCCWalletConstants.ORDER_STATUS_CANCLE:
                return Workflow.RC_CANCEL_SUCCESS;
            case CMCCWalletConstants.ORDER_STATUS_WFPAYMENT:
                return Workflow.RC_IN_PROG;
            case CMCCWalletConstants.ORDER_STATUS_REFUND:
            case CMCCWalletConstants.ORDER_STATUS_P_REFUND:
                return Workflow.RC_REFUND_SUCCESS;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        builder = getDefaultRequestBuilder(context, builder);
        builder.set(BusinessFields.REQUEST_ID, BeanUtil.getPropString(transaction, Transaction.TSN) + System.currentTimeMillis());
        builder.set(BusinessFields.TYPE, CMCCWalletConfig.REFUND_TYPE);
        builder.set(BusinessFields.VERSION, ProtocolFields.OTHER_VER);
        builder.set(BusinessFields.ORDER_ID, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.AMOUNT, BeanUtil.getPropString(transaction, Transaction.EFFECTIVE_AMOUNT));
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = call(request, OP_REFUND, config);
        } catch (Exception e) {
            logger.warn("encountered ioex in cmcc {}", OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        if (!CMCCWalletConstants.RET_CODE_SUCCESS.equals(code)) {
            if (CMCCWalletConstants.RET_CODE_SYSTEM_ERROR.equals(code) || CMCCWalletConstants.RET_CODE_DATA_COM_ERROR.equals(code)) {
                return Workflow.RC_RETRY;
            }
            if (CMCCWalletConstants.REFUND_FAIL_ERR_CODE_LISTS.contains(code)) {
                return Workflow.RC_ERROR;
            }
            return Workflow.RC_ERROR;
        }
        String refundStatus = BeanUtil.getPropString(result, ResponseFields.STATUS);
        if (CMCCWalletConstants.SUCCESS.equals(refundStatus)) {
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        } else {
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        String status = BeanUtil.getPropString(providerNotification, ResponseFields.STATUS);
        if (CMCCWalletConstants.SUCCESS.equals(status) && type == Transaction.TYPE_PAYMENT) {
            return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
        }
        return null;
    }


    /**
     * 根据加密方式进行请求
     *
     * @param request
     * @param opFlag
     * @param config
     * @return
     * @throws MpayApiNetworkError
     * @throws MpayException
     * @throws UnsupportedEncodingException
     */
    private Map<String, Object> call(Map<String, Object> request, String opFlag, Map<String, Object> config) throws MpayApiNetworkError, MpayException, UnsupportedEncodingException {
        //rsa和md5处理
        String signType = BeanUtil.getPropString(request, BusinessFields.SIGN_TYPE);
        String signKey;
        if (ProtocolFields.SIGN_TYPE_RSA.equals(signType)){
            signKey = getPrivateKeyContent(BeanUtil.getPropString(config, TransactionParam.CMCC_MERCHANT_KEY));
        }else{
            signKey = BeanUtil.getPropString(config, TransactionParam.CMCC_MERCHANT_KEY);
        }
        Map<String, Object> result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag),signType, signKey, request, opFlag);

        return result;
    }

    private List resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long totalAmount = BeanUtil.getPropLong(result, ResponseFields.AMOUNT);
        long coupAmt = BeanUtil.getPropLong(result, ResponseFields.COUP_AMT);
        long vchAmt = BeanUtil.getPropLong(result, ResponseFields.VCH_AMT);
        long cashAmt = BeanUtil.getPropLong(result, ResponseFields.CASH_AMT);
        long couponAmt = 0;
        long poiBonAmt = 0;
        if (BeanUtil.getPropString(result, ResponseFields.COUPON_AMT) != null) {
            couponAmt = StringUtils.yuan2cents(BeanUtil.getPropString(result, ResponseFields.COUPON_AMT));
        }
        if (BeanUtil.getPropString(result, ResponseFields.POI_BON_AMT) != null) {
            poiBonAmt = StringUtils.yuan2cents(BeanUtil.getPropString(result, ResponseFields.POI_BON_AMT));
        }
        List<Map> payments = new ArrayList();
        if (coupAmt != 0){
            Map coupAmtMap = CollectionUtil.hashMap(
                    Payment.TYPE, Payment.TYPE_HONGBAO_CHANNEL_MCH,
                    Payment.ORIGIN_TYPE, ResponseFields.COUP_AMT,
                    Payment.AMOUNT, coupAmt
            );
            payments.add(coupAmtMap);
        }
        if (vchAmt != 0){
            Map vchAmtMap = CollectionUtil.hashMap(
                    Payment.TYPE, Payment.TYPE_HONGBAO_CHANNEL,
                    Payment.ORIGIN_TYPE, ResponseFields.VCH_AMT,
                    Payment.AMOUNT, vchAmt
            );
            payments.add(vchAmtMap);
        }
        if (cashAmt != 0){
            Map cashAmtMap = CollectionUtil.hashMap(
                    Payment.TYPE, Payment.TYPE_WALLET_CMCC_WALLET,
                    Payment.ORIGIN_TYPE, ResponseFields.CASH_AMT,
                    Payment.AMOUNT, cashAmt
            );
            payments.add(cashAmtMap);
        }
        if (couponAmt != 0){
            Map couponAmtMap = CollectionUtil.hashMap(
                    Payment.TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                    Payment.ORIGIN_TYPE, ResponseFields.COUPON_AMT,
                    Payment.AMOUNT, couponAmt
            );
            payments.add(couponAmtMap);
        }
        if (poiBonAmt != 0){
            Map poiBonAmtMap = CollectionUtil.hashMap(
                    Payment.TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                    Payment.ORIGIN_TYPE, ResponseFields.POI_BON_AMT,
                    Payment.AMOUNT, poiBonAmt
            );
            payments.add(poiBonAmtMap);
        }
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        transaction.put(Transaction.PAID_AMOUNT, totalAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalAmount);
        transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(result, ResponseFields.MOBILE));
        return payments;


    }


    private void resolveRefundFund(Map<String, Object> result, TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        long refundAmount = BeanUtil.getPropLong(result, ResponseFields.AMOUNT);
        Map<String, Object> order = context.getOrder();
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        } else {
            transaction.put(Transaction.PAID_AMOUNT, refundAmount);
            transaction.put(Transaction.RECEIVED_AMOUNT, refundAmount);
        }
        transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.PAY_NO));
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
    }


    /**
     * 获取默认的requestbuilder:如果config中存在signType为RS，即是RSA加密；反之默认为MD5加密
     *
     * @param context
     * @return
     */
    private RequestBuilder getDefaultRequestBuilder(TransactionContext context, RequestBuilder builder) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        builder.set(BusinessFields.MERCHANT_ID, config.get(TransactionParam.CMCC_MERCHANT_ID));
        builder.set(BusinessFields.SIGN_TYPE, ProtocolFields.SIGN_TYPE_MD5);
        if (ProtocolFields.SIGN_TYPE_RSA.equals(BeanUtil.getPropString(config, TransactionParam.SIGN_TYPE))){
            builder.set(BusinessFields.SIGN_TYPE, ProtocolFields.SIGN_TYPE_RSA);
        }
        return builder;
    }


    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CMCC_TRADE_PARAMS);
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String code = BeanUtil.getPropString(result, ResponseFields.RETURN_CODE);
        String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RETURN_CODE, code);
        map.put(ResponseFields.MESSAGE, message);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, CMCCWalletConstants.RET_CODE_SUCCESS.equals(code), code, message);
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }




    private void changeTradeParamsFromExtended(Map<String, Object> extended, Map<String,Object> transaction) {
        if (extended == null || extended.isEmpty()){
            return;
        }
        Map<String, Object> cmccConfig = getTradeParams(transaction);
        boolean formal = !BeanUtil.getPropBoolean(cmccConfig,TransactionParam.LIQUIDATION_NEXT_DAY,true);
        if (!formal) {
            return;
        }
        String extendedMerchantId = BeanUtil.getPropString(extended,BusinessFields.MERCHANT_ID);
        if (!StringUtil.empty(extendedMerchantId)) {
            cmccConfig.put(TransactionParam.CMCC_MERCHANT_ID, extendedMerchantId);
        }
    }
}
