package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.lzccb.*;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.CharacterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

public class LZCCBServiceProvider extends AbstractServiceProvider {


    public static final Logger logger = LoggerFactory.getLogger(LZCCBServiceProvider.class);

    @Autowired
    private LZCCBClient lzccbClient;

    @Autowired
    private LZCCBTokenCache lzccbTokenCache;

    private String notifyHost;

    public static final String NAME = "provider.lzccb";

    protected static final int NOTIFY_URL_LIMIT = 200;

    protected long b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE;

    protected long c2bTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE;


    private static final SafeSimpleDateFormat dateTimeFormatSimple = new SafeSimpleDateFormat(LZCCBConstant.YYYYMMDDHHMMSS);

    private static final SafeSimpleDateFormat respdateTimeFormatSimple = new SafeSimpleDateFormat(LZCCBConstant.YYYY_MM_DD_HHMMSS);


    public LZCCBServiceProvider() {
        super.dateFormat = new SafeSimpleDateFormat(LZCCBConstant.YYYYMMDD);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LZCCB;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LZCCB_UP_TRADE_PARAMS);
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }


    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        // 门店号
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        LZCCBRequestBuilder lzccbRequestBuilder = getRequestBuilder(tradeParams);
        String providerStoreSn = MapUtil.getString(tradeParams, TransactionParam.LZCCB_PROVIDER_STORE_SN);
        tradeParams.put(LZCCBRequestFields.REQ_METHOD, LZCCBConstant.POST);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.STORE_ID, providerStoreSn);
        // 机具号
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        String terminalId = MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_ID, terminalId);

        // 收钱吧订单号
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, orderSn);

        // 交易类型
        lzccbRequestBuilder.setBody(LZCCBRequestFields.METHOD, LZCCBConstant.METHOD_MZS);

        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NOTICE_URL, notifyUrl);

        // 授权码
        lzccbRequestBuilder.setBody(LZCCBRequestFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));

        // ip地址信息
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REQ_IP, terminalInfo.getIp());

        // 交易金额
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TOTAL_AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));

        // 商品名
        lzccbRequestBuilder.setBody(LZCCBRequestFields.GOODS_NAME, CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT)));

        // 交易结束时间
        lzccbRequestBuilder.setBody(LZCCBRequestFields.DEADLINE, b2cTimeoutExpress);

        // 请求添加终端信息
        addTerminalInfo(terminalInfo, lzccbRequestBuilder);

        // 将参数投传到支付源
        carryOverExtendedParams(extendedParams, lzccbRequestBuilder);
        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, tradeParams, lzccbRequestBuilder.build(), FakeConstant.JSON_FORMAT, LZCCBConstant.CONTENT_TYPE, logger, 1, OP_PAY, getName());
        } catch (Exception e) {
            logger.error("call lzccb pay error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        // 门店号
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        LZCCBRequestBuilder lzccbRequestBuilder = getRequestBuilder(tradeParams);
        tradeParams.put(LZCCBRequestFields.REQ_METHOD, LZCCBConstant.POST);
        String providerStoreSn = MapUtil.getString(tradeParams, TransactionParam.LZCCB_PROVIDER_STORE_SN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.STORE_ID, providerStoreSn);
        // 机具号
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        String terminalId = MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_ID, terminalId);


        // 收钱吧订单号
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, orderSn);

        // 交易类型
        lzccbRequestBuilder.setBody(LZCCBRequestFields.METHOD, LZCCBConstant.METHOD_WXMINAPP);

        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NOTICE_URL, notifyUrl);

        // ip地址信息
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REQ_IP, terminalInfo.getIp());

        // 交易金额
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TOTAL_AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));

        // 商品名
        lzccbRequestBuilder.setBody(LZCCBRequestFields.GOODS_NAME, CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT)));

        // 交易结束时间
        lzccbRequestBuilder.setBody(LZCCBRequestFields.DEADLINE, c2bTimeoutExpress);

        // 用户id
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);


        //支付方式
        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);

        int subPayway = MapUtil.getIntValue(transaction, Order.SUB_PAYWAY);

        // 业务的额外参数
        HashMap<String, Object> bussinessParams = new HashMap<>();
        if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            //支付宝预下单
            lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, LZCCBConstant.USER_AGENT_ALIPAY);
            bussinessParams.put(LZCCBRequestFields.OPEN_ID, payerUid);
            bussinessParams.put(LZCCBRequestFields.SUB_APP_ID, payerUid);
        } else if (Order.PAYWAY_WEIXIN == payway) {
            //微信预下单
            lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, LZCCBConstant.USER_AGENT_WEIXIN);
            bussinessParams.put(LZCCBRequestFields.OPEN_ID, payerUid);
            String subAppId = "";
            if (subPayway == Order.SUB_PAYWAY_MINI) {
                subAppId = MapUtils.getString(tradeParams, TransactionParam.LZCCB_WECHAT_MINI_SUB_APP_ID);
            } else {
                subAppId = MapUtils.getString(tradeParams, TransactionParam.LZCCB_WECHAT_SUB_APP_ID);
            }
            bussinessParams.put(LZCCBRequestFields.SUB_APP_ID, subAppId);
        }
        // 业务参数
        String params = "";
        try {
            params = JsonUtil.objectToJsonString(bussinessParams);
        } catch (Exception e) {
            logger.error("bussinessParams to json error: {}", e.getMessage(), e);
        }
        lzccbRequestBuilder.setBody(LZCCBRequestFields.BUSINESS_PARAMS, params);
        // 将参数投传到支付源
        carryOverExtendedParams(extendedParams, lzccbRequestBuilder);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PRECREATE);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, tradeParams, lzccbRequestBuilder.build(), FakeConstant.JSON_FORMAT, LZCCBConstant.CONTENT_TYPE, logger, 1, OP_PRECREATE, getName());
        } catch (Exception e) {
            logger.error("call lzccb precreate error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildPrecreateResult(result, context);
    }


    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        // 门店号
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        LZCCBRequestBuilder lzccbRequestBuilder = getRequestBuilder(tradeParams);
        tradeParams.put(LZCCBRequestFields.REQ_METHOD, LZCCBConstant.POST);
        String providerStoreSn = MapUtil.getString(tradeParams, TransactionParam.LZCCB_PROVIDER_STORE_SN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.STORE_ID, providerStoreSn);
        // 机具号
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        String terminalId = MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_ID, terminalId);

        // 收钱吧订单号
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, orderSn);

        // 订单创建日期
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.ORDER_DATE, formatTimeString(orderCtime));

        //支付方式
        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, LZCCBConstant.USER_AGENT_ALIPAY);
        } else if (Order.PAYWAY_WEIXIN == payway) {
            lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, LZCCBConstant.USER_AGENT_WEIXIN);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, LZCCBConstant.USER_AGENT_UNIONPAY);
        }

        // 退款金额
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REFUND_AMOUNT, amount);

        // 退款请求订单号，标识一次退款请求
        String transactionSn = MapUtil.getString(transaction, Transaction.TSN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_REQUEST_NO, transactionSn);

        // ip地址信息
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REQ_IP, terminalInfo.getIp());

        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NOTICE_URL, notifyUrl);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, tradeParams, lzccbRequestBuilder.build(), FakeConstant.JSON_FORMAT, LZCCBConstant.CONTENT_TYPE, logger, 1, OP_REFUND, getName());
        } catch (Exception e) {
            logger.error("call lzccb refund error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context, OP_REFUND);
    }


    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("泸州银行不支持订单撤销");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        LZCCBRequestBuilder lzccbRequestBuilder = getRequestBuilder(tradeParams);
        tradeParams.put(LZCCBRequestFields.REQ_METHOD, LZCCBConstant.POST);
        // 订单创建日期
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.ORDER_DATE, formatTimeString(orderCtime));

        // 用交易流水号查询
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, tsn);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_QUERY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(gatewayUrl, tradeParams, lzccbRequestBuilder.build(), FakeConstant.JSON_FORMAT, LZCCBConstant.CONTENT_TYPE, logger, 1, OP_QUERY, getName());
        } catch (Exception e) {
            logger.error("call lzccb query error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        return buildQueryResult(result, context, OP_QUERY);
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        logger.info("泸州银行回调通知");
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT == type) {
            return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
        }
        return null;
    }

    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map data = MapUtils.getMap(result, LZCCBResponseFields.DATA);
        setTradeNoBuyerInfoIfExists(data, context, OP_PAY);
        String code = MapUtils.getString(result, LZCCBResponseFields.CODE, "");//返回的响应码
        if (Objects.equals(code, LZCCBConstant.SUCCESS_CODE)) {
            String status = MapUtils.getString(data, LZCCBResponseFields.STATUS);
            if (Objects.equals(status, LZCCBConstant.PAYING) || Objects.equals(status, LZCCBConstant.UNPAID)) {
                return Workflow.RC_IN_PROG;
            } else if (Objects.equals(status, LZCCBConstant.SUCCESS)) {
                // 支付宝b2c会直接返回成功,防止显示输入密码，走查询逻辑
                return query(context);
            } else if (Objects.equals(status, LZCCBConstant.FAIL) || Objects.equals(status, LZCCBConstant.CLOSE)) {
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        return Workflow.RC_ERROR;
    }

    public String buildPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map data = MapUtils.getMap(result, LZCCBResponseFields.DATA);
        setTradeNoBuyerInfoIfExists(data, context, OP_PRECREATE);
        String code = MapUtils.getString(result, LZCCBResponseFields.CODE, "");//返回的响应码
        if (Objects.equals(code, LZCCBConstant.SUCCESS_CODE)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            Map wcPayData = new HashMap();
            try {
                wcPayData = JsonUtil.jsonStringToObject(MapUtils.getString(data, LZCCBResponseFields.WCPAY_DATA), Map.class);
            } catch (Exception e) {
                logger.error("微信支付宝返回参数解析错误: {}", e.getMessage(), e);
            }
            if (payway == Order.PAYWAY_WEIXIN) {
                //微信预下单参数构建
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wcPayData);
            } else if (payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_ALIPAY) {
                // 支付宝预下单构建
                String tradeNo = MapUtils.getString(wcPayData, LZCCBResponseFields.ALI_PAY_TRADE_NO); //支付单号
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            }
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_TRADE_CANCELED;
    }

    public String buildRefundResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map data = MapUtils.getMap(result, LZCCBResponseFields.DATA);
        String code = MapUtils.getString(result, LZCCBResponseFields.CODE, "");//返回的响应码
        setTradeNoBuyerInfoIfExists(data, context, op);
        if (Objects.equals(code, LZCCBConstant.SUCCESS_CODE)) {
            String statu = MapUtils.getString(data, LZCCBResponseFields.STATU, "");//返回的状态信息
            if (Objects.equals(statu, LZCCBConstant.SUCCESS)) {
                long channelFinishTime = getChannelTime(data, LZCCBResponseFields.GMT_REFUND_PAY, respdateTimeFormatSimple);
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                resolveRefundFund(context);
                setTransactionFeeIfPaySuccess(data, context.getTransaction());
                return Workflow.RC_REFUND_SUCCESS;
            } else {
                //须进行退款查询，从而确定最终的退款情况
                return query(context);
            }
        }
        return Workflow.RC_ERROR;

    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String code = MapUtils.getString(result, LZCCBResponseFields.CODE, "");//返回的响应码
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getInteger(transaction, Transaction.TYPE);
        if (Objects.equals(code, LZCCBConstant.SUCCESS_CODE)) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) MapUtils.getObject(result, LZCCBResponseFields.DATA);
            if (CollectionUtils.isEmpty(dataList)) {
                return Workflow.RC_ERROR;
            }
            Map<String, Object> data = dataList.get(0);
            setTradeNoBuyerInfoIfExists(data, context, op);
            String oderStatus = MapUtils.getString(data, LZCCBResponseFields.ORDER_STATUS);
            String refundStatus = MapUtils.getString(data, LZCCBResponseFields.REFUND_STATUS);
            if (Transaction.TYPE_PAYMENT == type) {
                if (Objects.equals(oderStatus, LZCCBConstant.SUCCESS)) {
                    long channelFinishTime = getChannelTime(data, LZCCBResponseFields.TRANS_COMPLETE_TIME, respdateTimeFormatSimple);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                    resolveQueryFund(context, data);
                    setTransactionFeeIfPaySuccess(data, context.getTransaction());
                    return Workflow.RC_PAY_SUCCESS;
                } else {
                    return Workflow.RC_IN_PROG;
                }
            } else if (Transaction.TYPE_REFUND == type) {
                if (Objects.equals(refundStatus, LZCCBConstant.ALLREFUND)) {
                    long channelFinishTime = getChannelTime(data, LZCCBResponseFields.TRANS_COMPLETE_TIME, respdateTimeFormatSimple);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                    resolveRefundFund(context);
                    setTransactionFeeIfPaySuccess(data, context.getTransaction());
                    return Workflow.RC_REFUND_SUCCESS;
                } else {
                    return Workflow.RC_IN_PROG;
                }
            }
        }
        return Workflow.RC_ERROR;
    }


    public LZCCBRequestBuilder getRequestBuilder(Map<String, Object> tradeParams) {
        LZCCBRequestBuilder lzccbRequestBuilder = new LZCCBRequestBuilder();
        String providerMchId = MapUtil.getString(tradeParams, TransactionParam.LZCCB_PROVIDER_MCH_ID);
        String sm2PKey = MapUtil.getString(tradeParams, TransactionParam.LZCCB_SM2_PKEY);
        String signKey = MapUtil.getString(tradeParams, TransactionParam.LZCCB_SM3_KEY);
        String date = dateTimeFormatSimple.format(new Date());
        String sm4Url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), GET_SM4_KEY);
        String sm4Key = lzccbTokenCache.getAccessToken(sm2PKey, 0, sm4Url, signKey, providerMchId, date);
        // 将数据加密密钥加入交易参数
        tradeParams.put(LZCCBRequestFields.SM4_KEY, sm4Key);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.MER_ID, providerMchId);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.KEY_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ALG_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ACCEPT, LZCCBConstant.ACCEPT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.CONTENT_TYPE_PROPERTY, LZCCBConstant.CONTENT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.TIME_STAMP, date);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NO, providerMchId);
        return lzccbRequestBuilder;
    }

    public Map<String, Object> call(String url, Map<String, Object> config, Map<String, Object> request) throws Exception {
        String sm4key = MapUtil.getString(config, LZCCBRequestFields.SM4_KEY);
        String signKey = MapUtil.getString(config, TransactionParam.LZCCB_SM3_KEY);
        String method = MapUtil.getString(config, LZCCBRequestFields.REQ_METHOD);
        return lzccbClient.call(request, url, sm4key, signKey, method);
    }

    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String code = MapUtils.getString(result, LZCCBResponseFields.CODE, "");//返回的响应码
        String msg = MapUtils.getString(result, LZCCBResponseFields.MSG, "");//返回的响应信息
        Map data = MapUtils.getMap(result, LZCCBResponseFields.DATA);
        String status = MapUtils.getString(data, LZCCBResponseFields.STATU, "");//返回的状态字段
        String statusMsg = MapUtils.getString(data, LZCCBResponseFields.STATU_MSG, "");//返回的状态描述
        map.put(LZCCBResponseFields.CODE, code);
        map.put(LZCCBResponseFields.MSG, msg);
        map.put(LZCCBResponseFields.STATU, status);
        map.put(LZCCBResponseFields.STATU_MSG, statusMsg);
        boolean isSuccess = false;
        if (Objects.equals(code, LZCCBConstant.SUCCESS_CODE)) {
            isSuccess = true;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, StringUtils.empty(status) ? code : status, StringUtils.empty(statusMsg) ? msg : statusMsg);
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String orderId = null;
            // 预下单平台订单号字段
            orderId = MapUtil.getString(result, LZCCBResponseFields.TRX_NO);
            if (StringUtils.isEmpty(orderId)) {
                orderId = MapUtils.getString(result, LZCCBResponseFields.TRADE_NO);
            }
            if (!StringUtils.isEmpty(orderId)) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
        }
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(BeanUtil.getPropString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String tpOrderId = null;
            if (Objects.equals(op, OP_PRECREATE) && (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway)) {
                //支付宝预下单返回付款返回
                Map wcPayData = new HashMap();
                try {
                    wcPayData = JsonUtil.jsonStringToObject(MapUtils.getString(result, LZCCBResponseFields.WCPAY_DATA), Map.class);
                } catch (Exception e) {
                    logger.error("微信支付宝返回参数解析错误: {}", e.getMessage(), e);
                }
                tpOrderId = MapUtils.getString(wcPayData, LZCCBResponseFields.ALI_PAY_TRADE_NO); //第三方订单号
            }
            if (!StringUtils.isEmpty(tpOrderId)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tpOrderId);
            }
        }
    }


    private static long getChannelTime(Map<String, Object> result, String timeKey, SafeSimpleDateFormat simpleDateFormat) {
        long channelFinishTime = System.currentTimeMillis();
        //付款成功
        try {
            channelFinishTime = simpleDateFormat.parse(MapUtils.getString(result,
                    timeKey)).getTime();
        } catch (Exception e) {

        }
        return channelFinishTime;
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        long totalAmount = StringUtils.yuan2cents(MapUtils.getString(result, LZCCBResponseFields.TOTAL_AMOUNT));// 总金额
        long receiptAmount = StringUtils.yuan2cents(MapUtils.getString(result, LZCCBResponseFields.RECEIPT_AMOUNT));// 实收金额
        long discount = totalAmount - receiptAmount;
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (receiptAmount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, receiptAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));
        }

        if (discount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, discount
                    )
            );
        }
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (discount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
        }
        transaction.put(Transaction.PAID_AMOUNT, receiptAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiptAmount);

    }

    private void resolveQueryFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        long tranAmt = 0L;
        long effectiveAmount = MapUtils.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        if (StringUtils.isEmpty(MapUtils.getString(result, LZCCBResponseFields.TRADE_AMOUNT))) {
            tranAmt = effectiveAmount;
        } else {
            tranAmt = StringUtils.yuan2cents(MapUtils.getString(result, LZCCBResponseFields.TRADE_AMOUNT)); //交易金额
        }
        transaction.put(Transaction.PAID_AMOUNT, tranAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    // 记录手续费
    private void setTransactionFeeIfPaySuccess(Map<String, Object> result, Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String fee = MapUtil.getString(result, LZCCBResponseFields.PLAT_INCOME);
        if (!StringUtils.isEmpty(fee)) {
            long feeAmount = StringUtils.yuan2cents(fee);
            tradeParams.put(TransactionParam.FEE, feeAmount);
        }
    }

    private void carryOverExtendedParams(Map<String, Object> extended, LZCCBRequestBuilder lzccbRequestBuilder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();
            if (LZCCBConstant.SUB_APPID.equals(key) && value != null) {
                Map<String, Object> businessParamsMap = new HashMap<>();
                Map<String, Object> requestBody = lzccbRequestBuilder.getBody();
                String businessParams = MapUtil.getString(requestBody, LZCCBRequestFields.BUSINESS_PARAMS);
                if (!StringUtils.isEmpty(businessParams)) {
                    try {
                        businessParamsMap = JsonUtil.jsonStringToObject(businessParams, Map.class);
                    } catch (Exception e) {
                        logger.error("业务参数解析错误: {}", e.getMessage(), e);
                    }
                    if (businessParamsMap != null && businessParamsMap.containsKey(LZCCBRequestFields.SUB_APP_ID)) {
                        businessParamsMap.put(LZCCBRequestFields.SUB_APP_ID, value);
                        String params = "";
                        try {
                            params = JsonUtil.objectToJsonString(businessParamsMap);
                        } catch (Exception e) {
                            logger.error("bussinessParams to json error: {}", e.getMessage(), e);
                        }
                        lzccbRequestBuilder.setBody(LZCCBRequestFields.BUSINESS_PARAMS, params);
                        continue;
                    }
                }
            }
            lzccbRequestBuilder.setBody(key, value);
        }
    }

    private static void addTerminalInfo(TerminalInfo terminalInfo, LZCCBRequestBuilder lzccbRequestBuilder) {
        HashMap<String, String> terminalInfoMap = new HashMap<>();
        terminalInfoMap.put(LZCCBRequestFields.DEVICE_TYPE, UpayConstant.BARCODE_PAYMENT_SUPPLEMENT_ACCEPTANCE_TERMINAL);
        terminalInfoMap.put(LZCCBRequestFields.TERMINAL_IP, terminalInfo.getIp());
        try {
            lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_INFO, JsonUtil.objectToJsonString(terminalInfoMap));
        } catch (Exception e) {
            logger.error("终端信息转换json失败", e);
        }
    }
}
