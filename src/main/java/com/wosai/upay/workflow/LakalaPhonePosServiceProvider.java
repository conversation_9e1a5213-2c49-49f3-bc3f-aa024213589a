package com.wosai.upay.workflow;

import com.wosai.mpay.api.lakala.open.BusinessFields;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@ServiceProvicerPriority(priority = 1)
public class LakalaPhonePosServiceProvider extends LakalaOpenV3BankCardServiceProvider{
    public static final String NAME = "provider.lakala.openv3.phone.pos";

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP);
        if (payway == Order.PAYWAY_BANKCARD && subPayway == Order.SUB_PAYWAY_QRCODE) {
            Map<String, Object> params = getTradeParams(transaction);
            return params != null && getTradeCurrency(transaction).equals(TransactionParam.UPAY_DEFAULT_CURRENCY_CNY) && Objects.equals(tradeApp, ApolloConfigurationCenterUtil.getPhonePosTradeAppId());
        }
        return false;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String query(TransactionContext context) {
        return super.query(context);
    }

    @Override
    protected Map<String, Object> getTradeParams(Map<String, Object> transaction, String tradeParamsKey) {
        return super.getTradeParams(transaction, tradeParamsKey);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        setMockTransactionPrecreateSuccess(context);
        return Workflow.RC_CREATE_SUCCESS;
    }

    protected void setMockTransactionPrecreateSuccess(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        wapRequest.put(BusinessFields.M_MERCHANT, MapUtil.getString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID));
        wapRequest.put(BusinessFields.M_CUST_ID, MapUtil.getString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID));
        wapRequest.put(BusinessFields.M_OUT_TRADER_NO, MapUtil.getString(transaction, Transaction.TSN));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
    }


    @Override
    public String refund(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持退款");
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持撤单");
    }

}
