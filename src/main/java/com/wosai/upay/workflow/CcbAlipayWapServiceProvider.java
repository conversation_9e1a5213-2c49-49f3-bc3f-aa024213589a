package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.ccb.BusinessFields;
import com.wosai.mpay.api.ccb.CcbConstants;
import com.wosai.mpay.api.ccb.RequestBuilder;
import com.wosai.mpay.api.ccb.ResponseFields;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2021/8/25 Time: 3:06 下午
 */
public class CcbAlipayWapServiceProvider extends CcbWapServiceProvider {

    public static final String NAME = "provider.ccb.wap.alipay";
    public static final DateTimeFormatter CCB_ALIPAY_TIMEOUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (payway == Order.PAYWAY_ALIPAY
                    || payway == Order.PAYWAY_ALIPAY2)
                && (subPayway == Order.SUB_PAYWAY_WAP
                    || subPayway == Order.SUB_PAYWAY_MINI);
    }

    @Override
    protected String buildPrepaidResult(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_OUT_FIELDS);

        Map<String, Object> jsResult =  MapUtil.getMap(result, ResponseFields.JSAPI);
        String tradeNo = MapUtil.getString(jsResult, ResponseFields.TRADE_NO);
        if (StringUtils.isEmpty(tradeNo)) {
            return Workflow.RC_ERROR;
        }
        transaction.put(Transaction.TRADE_NO, tradeNo);
        order.put(Order.TRADE_NO, tradeNo);

        Map<String, Object> wapRequest = new HashMap();
        wapRequest.put(WapV2Fields.TRADE_NO, tradeNo);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    protected void buildWapParams(RequestBuilder builder, Map<String, Object> transaction) {
        long payAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String buyerId = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        builder.set(BusinessFields.TRADE_TYPE, CcbConstants.TRADE_TYPE_JS);
        builder.set(BusinessFields.TX_CODE, CcbConstants.ALIPAY_TX_CODE);
        builder.set(BusinessFields.USERID, buyerId);
        builder.set(BusinessFields.PAYMENT, com.wosai.mpay.util.StringUtils.cents2yuan(payAmount));
        builder.set(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.CUR_CODE, CcbConstants.CUR_CODE_RMB);
        builder.set(BusinessFields.TIMEOUT,  LocalDateTime.now().plusMinutes(DEFAULT_TIME_EXPIRE_MINUTE).format(CCB_ALIPAY_TIMEOUT_FORMATTER));
    }

    protected void resolvePayFund(Map<String, Object> result, TransactionContext context) {}
}
