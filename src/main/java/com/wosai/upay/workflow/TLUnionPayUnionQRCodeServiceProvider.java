package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.unionqrcode.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.*;


/**
 * Created by wkx
 */
public class TLUnionPayUnionQRCodeServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(TLUnionPayUnionQRCodeServiceProvider.class);

    private static final int DEFAULT_TIME_EXPIRE_SECOND = 4 * 60 ; // 订单默认过期时间设定为4分钟

    public static final String NAME = "provider.tl.unionpay.union.qrcode";

    protected static final int NOTIFY_URL_LIMIT = 128;

    private static final SafeSimpleDateFormat ORDER_TIME_SDF = new SafeSimpleDateFormat(UnionPayQRCodeConstants.ORDER_TIME_DATE_TIME_FORMAT);

    @Autowired
    private UnionPayQrCodeClient client;
    private static final int retryTimes = 3;
    private String notifyHost;

    public TLUnionPayUnionQRCodeServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.CURRENCY_CODE, BusinessFields.TXN_AMT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payWay = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payWay == Order.PAYWAY_UNIONPAY;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_TL;
    }

    private String getPayeeInfo(Map<String,Object> config){
        return  "{" +
                ProtocolFields.ID + "=" + BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_ID)+"&" +
                ProtocolFields.MER_CAT_CODE + "=" + BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_CAT_CODE)+"&" +
                ProtocolFields.NAME + "=" + BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_NAME)+"&" +
                ProtocolFields.TERM_ID + "=" + BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_TERM_ID)+
                "}";
    }

    @Override
    @SuppressWarnings("unchecked")
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder requestBuilder = getDefaultRequestBuilder(config);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        requestBuilder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE);
        requestBuilder.set(BusinessFields.QR_NO, extraParams.get(Transaction.BARCODE));
        requestBuilder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        requestBuilder.set(BusinessFields.TXN_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        requestBuilder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        requestBuilder.set(BusinessFields.PAYEE_INFO, getPayeeInfo(config));
        requestBuilder.set(BusinessFields.ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME))));
        Map<String, Object> configSnapshot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
        requestBuilder.set(BusinessFields.AREA_INFO, com.wosai.pantheon.util.MapUtil.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        if (extended != null) {
            carryOverExtendedParams(extended, requestBuilder, UnionPayQRCodeConstants.PAY_ALLOWED_FIELDS);
        }

        //259终端信息上送
        setTerminalInfo(context, requestBuilder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), requestBuilder.build(), 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open pay", ex);
            setTransactionContextErrorInfo(context, "pay", ex);
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(responseCode)) {
            setTradeNoBuyerInfoIfExists(result, context);
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        if(subPayway == Order.SUB_PAYWAY_BARCODE){
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE_CANCEL);
        }else{
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_CANCEL);
        }
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.TSN) + "");
        builder.set(BusinessFields.ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(transaction,DaoConstants.CTIME))));
        builder.set(BusinessFields.ORIG_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.ORIG_ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME))));
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.TXN_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.PAYEE_INFO, getPayeeInfo(config));
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), builder.build(), 1, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open cancel", ex);
            setTransactionContextErrorInfo(context, "cancel", ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        switch (responseCode){
            case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                //success
                return Workflow.RC_CANCEL_SUCCESS;
            case UnionPayQRCodeConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayQRCodeConstants.RESP_CODE_ORDER_UNKNOWN:
            case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                //retry
                return Workflow.RC_RETRY;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayway) {
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE_QUERY);
        } else {
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_QUERY);
        }
        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        builder.set(BusinessFields.PAYEE_INFO, getPayeeInfo(config));
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(context.getTransaction(), DaoConstants.CTIME))));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open query", ex);
            setTransactionContextErrorInfo(context, "query", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        String originResponseCode = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_CODE, "");
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(responseCode)) {
            switch (originResponseCode) {
                case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                    if (Transaction.TYPE_REFUND == transactionType) {
                        resolveRefundFund(result, context);
                        return Workflow.RC_REFUND_SUCCESS;
                    } else if (Transaction.TYPE_PAYMENT == transactionType) {
                        resolvePayFund(result, context);
                        setTradeNoBuyerInfoIfExists(result, context);
                        return Workflow.RC_PAY_SUCCESS;
                    } else if (Transaction.TYPE_CANCEL == transactionType){
                        return Workflow.RC_CANCEL_SUCCESS;
                    }
                case UnionPayQRCodeConstants.RESP_CODE_ORDER_UNKNOWN:
                case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                case UnionPayQRCodeConstants.RESP_CODE_TRADE_NOT_EXIST:
                case UnionPayQRCodeConstants.RESP_CODE_TRADE_NOT_EXIST_OR_ERROR:
                case UnionPayQRCodeConstants.RESP_CODE_TRADE_OPERATE_LIMIT:
                    return Workflow.RC_IN_PROG;
                default:
                    if (Transaction.TYPE_PAYMENT == transactionType && UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)) {
                        return Workflow.RC_TRADE_CANCELED;
                    }
                    return Workflow.RC_ERROR;
            }
        } else if (UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)
                && !UnionPayQRCodeConstants.RESP_CODE_TRADE_RISK_FAIL.equals(responseCode)
                && !UnionPayQRCodeConstants.RESP_CODE_NOT_IN_BUSINESS_TIME.equals(responseCode)) {
            //如果是因为非风控或者限流导致的查单失败，则返回错误
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        int subPayway = BeanUtil.getPropInt(transaction,Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_BARCODE == subPayway){
            builder.set(BusinessFields.REQ_TYPE,UnionPayQRCodeConstants.REQ_TYPE_BARCODE_REFUND);
        }else{
            builder.set(BusinessFields.REQ_TYPE,UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_REFUND);
        }
        builder.set(BusinessFields.PAYEE_INFO, getPayeeInfo(config));
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.TSN) + "");
        builder.set(BusinessFields.ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(transaction,DaoConstants.CTIME))));
        builder.set(BusinessFields.ORIG_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.ORIG_ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME))));
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.TXN_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        switch (responseCode){
            case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                resolveRefundFund(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            case UnionPayQRCodeConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                return Workflow.RC_RETRY;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_QRCODE == subPayway){
            throw new UnsupportedOperationException("不支持此支付方式");
        }else{
            return wapPreCreate(context);
        }
    }

    @SuppressWarnings("unchecked")
    private String wapPreCreate(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_WAP);
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORDER_TIME, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME))));
        builder.set(BusinessFields.ORDER_TYPE, UnionPayQRCodeConstants.ORDER_TYPE_COMMON_CONSUME);
        builder.set(BusinessFields.PAYEE_INFO, getPayeeInfo(config));
        builder.set(BusinessFields.PAYMENT_VALID_TIME, DEFAULT_TIME_EXPIRE_MINUTE + "");
        builder.set(BusinessFields.ORDER_DESC, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.CUSTOMER_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.PAYMENT_VALID_TIME, DEFAULT_TIME_EXPIRE_SECOND+"");
        builder.set(BusinessFields.ORDERTIMEOUT, ORDER_TIME_SDF.format(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME) + DEFAULT_TIME_EXPIRE_SECOND * 1000)));
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if (upayOrderNumber != null) {
            builder.set(BusinessFields.BACK_URL, getNotifyUrl(notifyHost, context));
        }
        Map<String, Object> configSnapshot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.set(BusinessFields.AREA_INFO, com.wosai.pantheon.util.MapUtil.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        builder.set(BusinessFields.TXN_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        if (payerUid != null) {
            builder.set(BusinessFields.USER_ID, payerUid);
        }
        carryOverExtendedParams(extended, builder, UnionPayQRCodeConstants.WAP_ALLOWED_FIELDS);

        //259终端信息上送
        setTerminalInfo(context, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP), builder.build(), 1, "wap create");
        } catch (Exception ex) {
            logger.error("failed to call unionpay open wap create", ex);
            setTransactionContextErrorInfo(context, "wap create", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(responseCode)) {
            //success
            Map<String, Object> wapRequest = new HashMap<String, Object>() {{
                put(ResponseFields.REDIRECTURL, BeanUtil.getPropString(result, ResponseFields.REDIRECTURL));
            }};
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT){
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    public Map<String, Object> retryIfNetworkException(Map<String,Object> config, String url, Map<String,Object> request, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                return client.call(url, getPrivateKeyContent((String)config.get(TransactionParam.UNION_PAY_TL_PRIVATE_KEY)), request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in unionpay online {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }


    @SuppressWarnings("unchecked")
    public void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        //设置付款人信息
        Map<String,Object> payerInfo = (Map<String, Object>) result.get(ResponseFields.PAYER_INFO);
        String accNo = BeanUtil.getPropString(payerInfo, ResponseFields.PAYER_INFO_ACC_NO);
        String name = BeanUtil.getPropString(payerInfo, ResponseFields.PAYER_INFO_NAME);
        //接口返回的名字没有脱敏，此处进行脱敏处理
        if(!StringUtils.empty(name)){
            name = "*" + name.substring(name.length() - 1);
        }
        if(!StringUtil.empty(accNo)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
                transaction.put(Transaction.BUYER_UID, accNo);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_UID))){
                order.put(Order.BUYER_UID, accNo);
            }
        }
        if(!StringUtil.empty(name)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))){
                transaction.put(Transaction.BUYER_LOGIN, name);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                order.put(Order.BUYER_LOGIN, name);
            }
        }
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        String tradeNo = BeanUtil.getPropString(result, ResponseFields.VOUCHER_NUM);
        if(!StringUtil.empty(tradeNo)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, tradeNo);
            }
        }
    }

    public void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    @SuppressWarnings("unchecked")
    public void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
        }
        List<Map<String, Object>> payments = new ArrayList<>();
        List<Map<String, Object>> couponInfo = (List<Map<String, Object>>) result.get(ResponseFields.COUPON_INFO);
        long couponSum = 0;
        if (couponInfo != null) {
            for (Map<String, Object> coupon : couponInfo) {
                String spnsrId = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_SPNSR_ID); //出资方
                long amount = BeanUtil.getPropLong(coupon, ResponseFields.COUPON_INFO_OFFST_AMT);
                String couponId = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_ID);
                String couponType = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_TYPE);
                couponSum = couponSum + amount;
                String paymentType;
                if (UnionPayQRCodeConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)) {
                    paymentType = UnionPayQRCodeConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                } else {
                    paymentType = UnionPayQRCodeConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                }
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, amount,
                        Transaction.PAYMENT_SOURCE, couponId,
                        Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                        Transaction.PAYMENT_TYPE, paymentType
                ));
            }
        }
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            String paymentType = null;
            Map<String, Object> payerInfo = (Map<String, Object>) BeanUtil.getProperty(result, ResponseFields.PAYER_INFO);
            String cardAttr = null;
            if (payerInfo != null) {
                cardAttr = BeanUtil.getPropString(payerInfo, ResponseFields.PAYER_INFO_CARD_ATTR);
                if (!StringUtils.isEmpty(cardAttr)) {
                    if (UnionPayQRCodeConstants.CARD_ATTR_CREDIT_CARD.equals(cardAttr)) {
                        paymentType = Payment.TYPE_BANKCARD_CREDIT;
                    } else if (UnionPayQRCodeConstants.CARD_ATTR_DEBIT_CARD.equals(cardAttr)) {
                        paymentType = Payment.TYPE_BANKCARD_DEBIT;
                    } else {
                        paymentType = Payment.TYPE_OTHERS;
                    }
                    payments.add(CollectionUtil.hashMap(
                            Transaction.PAYMENT_AMOUNT, amount,
                            Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                            Transaction.PAYMENT_TYPE, paymentType
                            )
                    );
                }
            }
        }
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (oldPayments == null || oldPayments.isEmpty()) {
            if (payments != null && !payments.isEmpty()) {
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
        }
        if (!StringUtil.empty(BeanUtil.getPropString(result, ResponseFields.PAY_AMT))) {
            long payAmt = BeanUtil.getPropLong(result, ResponseFields.PAY_AMT);
            transaction.put(Transaction.PAID_AMOUNT, payAmt);
        }
    }


    public static RequestBuilder getDefaultRequestBuilder(Map<String, Object> config){
        RequestBuilder builder = new RequestBuilder();
        builder.set(com.wosai.mpay.api.tl.BusinessFields.ORGID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_ORGID));
        builder.set(com.wosai.mpay.api.tl.BusinessFields.CUSID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_ID));
        builder.set(ProtocolFields.MER_ID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_ID));
        builder.set(ProtocolFields.TERM_ID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_TERM_ID));
        // 汉字*2+其它字符*1的长度不能大于40，长度超过20后直接截取
        String merName = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_NAME);
        if(!StringUtil.empty(merName) && merName.length() > 20) {
            merName = merName.substring(0, 20);
        }
        builder.set(ProtocolFields.MER_NAME, merName);
        
        builder.set(ProtocolFields.MER_CAT_CODE,BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_MCH_CAT_CODE));
        String prnInsIdCd = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_UNION_PNR_INS_ID_CD);
        if (!StringUtils.empty(prnInsIdCd)){
            //通联云闪付交易上送服务商机构标识 "C1000001"
            builder.set(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
        }
        return builder;
    }

    public void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key) || !allowedFields.contains(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null && !TransactionParam.UNION_PAY_TL_UNION_MCH_ID.equals(key)) {
                builder.set(key, value + "");
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String responseCode = BeanUtil.getPropString(result, com.wosai.mpay.api.unionpayonline.ResponseFields.RESP_CODE, "");
        String responseMsg = BeanUtil.getPropString(result, com.wosai.mpay.api.unionpayonline.ResponseFields.RESP_MSG);
        String originalResponseCode = BeanUtil.getPropString(result, com.wosai.mpay.api.unionpayonline.ResponseFields.ORIG_RESP_CODE, "");
        String originalResponseMsg = BeanUtil.getPropString(result, com.wosai.mpay.api.unionpayonline.ResponseFields.ORIG_RESP_MSG);

        Map<String, Object> map = new LinkedHashMap<>();
        map.put(ResponseFields.RESP_CODE, responseCode);
        map.put(ResponseFields.RESP_MSG, responseMsg);
        map.put(ResponseFields.ORIG_RESP_CODE, originalResponseCode);
        map.put(ResponseFields.ORIG_RESP_MSG, originalResponseMsg);
        String codes = responseCode + originalResponseCode;
        boolean isSuccess = UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(codes) || (UnionPayQRCodeConstants.RESP_CODE_SUCCESS + UnionPayQRCodeConstants.RESP_CODE_SUCCESS).equals(codes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, StringUtil.empty(originalResponseCode) ? responseCode : originalResponseCode, StringUtil.empty(originalResponseMsg) ? responseMsg : originalResponseMsg);
    }

    protected void setTerminalInfo(TransactionContext context, RequestBuilder builder) {
        if (!ApolloConfigurationCenterUtil.isSendTLUnionPay259Params()) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        Map<String, Object> termInfo = CollectionUtil.hashMap(
            BusinessFields.DEVICE_TYPE, UnionPayQRCodeConstants.DEVICE_TYPE_11
        );
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendUnionpayPoi()) {
            termInfo.put(BusinessFields.LONGITUDE, terminalInfo.getUnionpayFormatLongitude());
            termInfo.put(BusinessFields.LATITUDE, terminalInfo.getUnionpayFormatLatitude());
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }

        String termId = terminalInfo.getId();
        if (Objects.nonNull(termId) && termId.length() > 0) {
            builder.set(ProtocolFields.TERM_ID, termId);
        }
        builder.set(BusinessFields.TERM_INFO, buildUnionSubDomainParams(termInfo));
    }

    public String getNotifyHost() {
        return notifyHost;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    private String buildUnionSubDomainParams(Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        StringBuilder temp = new StringBuilder("{");
        params.forEach((k, v) -> {
            if (temp.length() > 1) {
                temp.append("&");
            }
            temp.append(k).append("=").append(v);
        });
        temp.append("}");
        return Base64.encode(temp.toString().getBytes(StandardCharsets.UTF_8));
    }

}
