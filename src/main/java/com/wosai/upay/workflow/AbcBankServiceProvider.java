package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.abc.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;


/**
 * <AUTHOR>
 * @Date 2024/1/17、19:06
 * 农行服务商通道
 **/

public class AbcBankServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(AbcBankServiceProvider.class);

    @Autowired
    ABCClient abcClient;


    @Override
    public String getName() {
        return ABCConstant.NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_ABCBANK;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return !Objects.isNull(getTradeParams(transaction));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ABC_UP_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        RequestBuilder requestBuilder = buildPayRequest(context);
        String gateway = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        String publicKey = MapUtil.getString(tradeParams, TransactionParam.ABC_PUBLIC_KEY);

        Map<String, Object> result;
        try {
            String rsaKey = getPrivateKeyContent(publicKey);
            result = retryIfNetworkException(requestBuilder, gateway, rsaKey, 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call abc bank pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayAndQueryResult(result, context);

    }


    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("农行通道暂不支持撤单");

    }

    @Override
    public String query(TransactionContext context) {
        RequestBuilder requestBuilder = buildCommonRequest(context);
        String gateway = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);

        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        String publicKey = MapUtil.getString(tradeParams, TransactionParam.ABC_PUBLIC_KEY);

        Map<String, Object> result;
        try {
            String rsaKey = getPrivateKeyContent(publicKey);
            result = retryIfNetworkException(requestBuilder, gateway, rsaKey, 3, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call abc bank query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildPayAndQueryResult(result, context);

    }

    @Override
    public String refund(TransactionContext context) {
        RequestBuilder requestBuilder = buildRefundRequest(context);
        String gateway = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);

        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        String publicKey = MapUtil.getString(tradeParams, TransactionParam.ABC_PUBLIC_KEY);

        Map<String, Object> result;
        try {
            String rsaKey = getPrivateKeyContent(publicKey);
            result = retryIfNetworkException(requestBuilder, gateway, rsaKey, 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call abc bank refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("农行通道暂不支持预下单");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }


    public RequestBuilder buildCommonRequest(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        String countno = MapUtil.getString(tradeParams, ABCRequestFields.COUNTNO);
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ABCRequestFields.PAYMODE, ABCConstant.QRCODE);
        //默认为查询
        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.QUERY);
        requestBuilder.set(ABCRequestFields.COUNTNO, countno);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        requestBuilder.set(ABCRequestFields.TERMINAL_SERIALNO, tsn);

        long amount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        String orderAmount = StringUtils.cents2yuan(amount);
        requestBuilder.set(ABCRequestFields.AMOUNT, orderAmount);

        String tradeTime = DateUtil.formatDate(new Date(), ABCConstant.DATE_SIMPLE_FORMAT);
        String tradeTime2 = DateUtil.formatDate(new Date(), ABCConstant.DATE_TIME_SIMPLE_FORMAT);
        requestBuilder.setPlatformField(ABCRequestFields.REQ_DATE, tradeTime);
        requestBuilder.setPlatformField(ABCRequestFields.REQ_TIME, tradeTime2);
        requestBuilder.setPlatformField(ABCRequestFields.PROV_CODE, ABCConstant.AREA_CODE);
        requestBuilder.setPlatformField(ABCRequestFields.SRC_AREA_CODE, ABCConstant.AREA_CODE);
        requestBuilder.setPlatformField(ABCRequestFields.ACCESS_TYPE, ABCConstant.ACCESS_TYPE);

        return requestBuilder;
    }

    public RequestBuilder buildPayRequest(TransactionContext context) {
        RequestBuilder requestBuilder = buildCommonRequest(context);
        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.PAY);

        //b2c 支付 加上二维码
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        requestBuilder.set(ABCRequestFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        return requestBuilder;
    }

    public RequestBuilder buildRefundRequest(TransactionContext context) {
        RequestBuilder requestBuilder = buildCommonRequest(context);

        Map<String, Object> transaction = context.getTransaction();
        String orderSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        requestBuilder.set(ABCRequestFields.ORIG_TERMINAL_SERIALNO, orderSn);
        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.REFUND);

        return requestBuilder;
    }


    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String returnCode = MapUtils.getString(result, ABCResponseFields.RESPCODE);//返回的响应码
        String returnMsg = MapUtils.getString(result, ABCResponseFields.RESULT);//响应描述

        map.put(ABCResponseFields.RESPCODE, returnCode);//返回状态码
        map.put(ABCResponseFields.RESULT, returnMsg);//返回信息
        boolean isSuccess = false;
        //查询 退款 支付 00 都是为成功
        if (Objects.equals(returnCode, ABCConstant.RESP_SUCCESS)) {
            isSuccess = true;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, returnCode, returnMsg);
    }

    private String buildPayAndQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String returnCode = MapUtils.getString(result, ABCResponseFields.RESPCODE);//返回的响应码
        setTradeNoBuyerInfoIfExists(result, context);
        if (ABCConstant.RESP_SUCCESS.equals(returnCode)) {
            //付款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            resolvePayFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        } else if(ABCConstant.NEED_QUERY.contains(returnCode)){
            return Workflow.RC_IN_PROG;
        } else {
            return Workflow.RC_TRADE_CANCELED;
        }
    }


    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String returnCode = MapUtils.getString(result, ABCResponseFields.RESPCODE);//返回的响应码

        if (ABCConstant.RESP_SUCCESS.equals(returnCode)) {
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            context.getTransaction().put(Transaction.TRADE_NO, MapUtils.getString(result, ABCResponseFields.TRANSACTION_ID));//商户通道退款订单号
            resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
            return Workflow.RC_REFUND_SUCCESS;

        } else {
            //没有退款中的状态，直接返回rc_error
            return Workflow.RC_ERROR;
        }
    }


    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        //交易金额
        String amountStr = MapUtil.getString(result, ABCResponseFields.AMOUNT);
        long amount = StringUtils.yuan2cents(amountStr);

        //实付金额
        String payAmountStr = MapUtil.getString(result, ABCResponseFields.PAYAMOUNT);
        long payAmount = StringUtils.yuan2cents(payAmountStr);
        long channelDiscountAmount = amount - payAmount;

        Long effectiveAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();
        if (channelDiscountAmount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                    )
            );
        }

        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (payAmount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, payAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }

        if (effectiveAmount - payAmount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, channelDiscountAmount);
            context.getOrder().put(Order.NET_DISCOUNT, channelDiscountAmount);
        }
        transaction.put(Transaction.PAID_AMOUNT, payAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, payAmount);
    }


    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        String orderId = MapUtils.getString(result, ABCResponseFields.ORDERID); //行内系统订单号
        String tpOrderId = MapUtils.getString(result, ABCResponseFields.TRANSACTION_ID); //第三方订单号
        String userId = MapUtils.getString(result, ABCResponseFields.USER_ID); //农行用户id


        if (!StringUtil.empty(userId)) {
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                transaction.put(Transaction.BUYER_UID, userId);
            }
            if (StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_UID))) {
                order.put(Order.BUYER_UID, userId);
            }
        }

        if (!StringUtils.isEmpty(orderId)) {
            if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
            if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, orderId);
            }
        }
        if (!StringUtils.isEmpty(tpOrderId)) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (extraOutFields == null) {
                extraOutFields = new HashMap<>();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            if (context.isAlipay() && tpOrderId.length() > 2) {
                tpOrderId = tpOrderId.substring(2);
            }
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tpOrderId);
        }

    }

    private Map<String, Object> retryIfNetworkException(RequestBuilder requestBuilder, String gateway, String rsaKey, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                Map result = abcClient.call(requestBuilder, gateway, rsaKey, ABCConstant.HEADERS);
                return result;
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in abc {}", opFlag, ex);
            }

        }
        logger.error("still network i/o error after retrying {} times, op is {}", times, opFlag);

        throw exception;
    }


}
