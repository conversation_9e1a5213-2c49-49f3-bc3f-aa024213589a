package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

import javax.annotation.Resource;
import java.util.Map;

/***
 * @ClassName: HaikeUnionPayWeixinWapOrMiniServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/11 9:59 PM
 */
@ServiceProvicerPriority(priority = 2)
public class HaikeUnionPayWeixinWapOrMiniServiceProvider extends UnionPayWeixinWapOrMiniServiceProvider {

    public static final String NAME = "provider.haike.unionpay.weixin.wapOrMini";

    @Resource
    private HaikeUnionPayServiceProvider haikeUnionPayServiceProvider;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.HAIKE_UNION_PAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_HAIKE_UNION_PAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        long effectiveTotal = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        //退款和 交易完成后的撤单直接走海科通道
        if (Transaction.TYPE_REFUND == type || (Transaction.TYPE_CANCEL == type && effectiveTotal > 0)) {
            return false;
        }

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(Order.PAYWAY_WEIXIN == payway && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI)) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHANNEL_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_CHANNEL_ID));
        builder.set(ProtocolFields.CERT_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_CERT_ID));
        builder.set(ProtocolFields.SIGN_TYPE, WeixinClient.SIGN_TYPE_SM2);
        builder.set(ProtocolFields.APP_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_WEIXIN_SUB_MCH_ID));

        //小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_MINI == subPayway){
            String miniSubAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            if(!StringUtil.empty(miniSubAppId)){
                builder.set(ProtocolFields.SUB_APP_ID, miniSubAppId);
            }
        }

        setTerminalInfo(context, MapUtils.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), config, builder);
        return builder;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String serviceUrl, Map<String, Object> request, String logFlag) throws MpayException, MpayApiNetworkError {
        removeIllegalFields(request);
        return client.call(serviceUrl, WeixinClient.SIGN_TYPE_HAIKE, BeanUtil.getPropString(config, TransactionParam.HAIKE_UNION_PAY_ACCESS_KEY), null, request);
    }

}
