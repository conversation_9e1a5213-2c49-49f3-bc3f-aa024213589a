package com.wosai.upay.workflow;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.InvalidBarcodeException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.net.ssl.SSLContext;

/**
 * Created by jianfree on 17/11/15.
 */
@ServiceProvicerPriority(priority = 1)
public class DirectWeixinServiceProvider extends WeixinServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(DirectWeixinServiceProvider.class);
    public static final String NAME = "provider.weixin";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String serviceUrl, Map<String, Object> request, String opFlag) throws MpayException, MpayApiNetworkError {
        SSLContext ssl = null;
        if(OP_CANCEL.equals(opFlag) || OP_REFUND.equals(opFlag) || OP_DEPOSIT_CONSUME.equals(opFlag) 
                || OP_DEPOSIT_CANCEL.equals(opFlag) || OP_DEPOSIT_REFUND.equals(opFlag)) {
            String certConfigKey = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_CONFIG_KEY);
            String password = BeanUtil.getPropString(config, TransactionParam.WEIXIN_CERT_PASSWORD);
            ssl = getSSLContext(certConfigKey, password);
        }
        String signType = WeixinClient.SIGN_TYPE_WEIXIN;
        if(OP_DEPOSIT_FREEZE.equals(opFlag) || OP_DEPOSIT_QUERY.equals(opFlag) || OP_DEPOSIT_CONSUME.equals(opFlag) 
                || OP_DEPOSIT_CANCEL.equals(opFlag) || OP_DEPOSIT_REFUND.equals(opFlag)) {
            signType = WeixinClient.SIGN_TYPE_HMAC_SHA256;
        }
        return client.call(serviceUrl, signType, (String) config.get(TransactionParam.WEIXIN_APP_KEY), ssl, request);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.WEIXIN_TRADE_PARAMS);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if(tradeParams == null || tradeParams.containsKey(TransactionParam.WEIXIN_VERSION)){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE) && matchProductCode(transaction, tradeParams, null, true);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    /**
     * 获取默认的requestBuilder，设置请求默认值。
     * @param context
     * @return
     */
    public  RequestBuilder getDefaultRequestBuilder(TransactionContext context){
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        //微信直连正式的交易，小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
        //对接了万码，门店码支付的sub_app_id为weixin_sub_appid, 小程序的sub_app_id为weixin_mini_sub_appid
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_MINI == subPayway){
            String miniSubAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            if(!StringUtil.empty(miniSubAppId)){
                builder.set(ProtocolFields.SUB_APP_ID, miniSubAppId);
            }
        }
        return builder;
    }


    @SuppressWarnings("unchecked")
    public Map<String,Object> queryOpenIdByBarcode(Map<String,Object> config,String dynamicId){
        Map<String,Object> tradeConfig=(Map<String, Object>) config.get(TransactionParam.WEIXIN_TRADE_PARAMS);
        if(tradeConfig==null){
        	throw new InvalidBarcodeException(UpayErrorScenesConstant.INVALID_BARCODE_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_BARCODE_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        RequestBuilder builder=new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, tradeConfig.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, tradeConfig.get(TransactionParam.WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, tradeConfig.get(TransactionParam.WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, tradeConfig.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        builder.set(BusinessFields.AUTH_CODE,dynamicId);
        Map<String,Object> result;
        try {
            result = retryIfNetworkException(tradeConfig, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_OPENID_BARCODE_QUERY), builder.build(), retryTimes, "queryOpenIdByBarCode");
        }catch (Exception ex){
            logger.error("failed to call weixin queryOpenIdByBarCode", ex);
            return CollectionUtil.hashMap(ResponseFields.ERR_CODE_DES,ex.getMessage());
        }
        return result;
    }
    
    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.DEPOSIT, WeixinConstants.DEPOSIT_YES);
        builder.set(BusinessFields.BODY, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.DETAIL, transaction.get(Transaction.BODY));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        builder.set(BusinessFields.AUTH_CODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(System.currentTimeMillis() + b2cTimeExpire));
        builder.set(BusinessFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_HMAC_SHA256);
        handlerCustomizedSwitch(builder,transaction);
        carryOverExtendedParams(extended, builder, WeixinConstants.PAY_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_FREEZE), builder.build(), retryTimes, OP_DEPOSIT_FREEZE);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit freeze", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_FREEZE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_FREEZE);
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }

        if(StringUtil.empty(returnCode)){
            return Workflow.RC_IN_PROG;
        }
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            String returnMsg = BeanUtil.getPropString(result, ResponseFields.RETURN_MSG);
            if("SYSTEM ERROR".equals(returnMsg)){
                return Workflow.RC_IN_PROG;
            }else{
                return Workflow.RC_PROTOCOL_ERROR;
            }
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_ORDER_PAID.equals(errCode)){
                return Workflow.RC_IN_PROG;
            }
            if(WeixinConstants.RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST.contains(errCode)){
                return Workflow.RC_PROTOCOL_ERROR;
            }
            if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_FAIL_LIST.contains(errCode)){
                return Workflow.RC_ERROR;
            }else if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST.contains(errCode)) {
                return Workflow.RC_IN_PROG;
            }else {
                return Workflow.RC_ERROR;
            }
        }

        if(WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode) && WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            setTradeNoBuyerInfoIfExists(result, context);
            setOverseasInfoWhenPay(context.getTransaction(), result);
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_IN_PROG;

    }
    
    @Override
    public String depositQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_HMAC_SHA256);
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_QUERY), builder.build(), retryTimes, OP_DEPOSIT_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit query", ex);
        }
    
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_QUERY);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            //通讯标识为失败，重新查询
            return Workflow.RC_IN_PROG;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)){
                return Workflow.RC_IN_PROG;
            }else if(WeixinConstants.RESULT_ERROR_ORDER_NOT_EXIST.equals(errCode)){
                // 下单成功后，再次查单返回“ORDERNOTEXIST”时，需要进行查单动作
                Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction, 
                        UpayUtil.getProviderErrorInfoKey(
                                Order.SUB_PAYWAY_BARCODE == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) ? MpayServiceProvider.OP_DEPOSIT_FREEZE : MpayServiceProvider.OP_PRECREATE));
                if(null == payResultMap 
                        || WeixinConstants.RESULT_CODE_SUCCESS.equals(BeanUtil.getPropString(payResultMap, ResponseFields.RESULT_CODE))
                        || WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(BeanUtil.getPropString(payResultMap, ResponseFields.ERR_CODE))){
                    return Workflow.RC_IN_PROG;
                }else{
                    return Workflow.RC_ERROR;
                }
            }else{
                return Workflow.RC_ERROR;
            }
        }
        String tradeState = (String) result.get(ResponseFields.TRADE_STATE);
        String rcFlag = Workflow.RC_ERROR;
        if(WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)){
            rcFlag = Workflow.RC_IN_PROG;
        }if(WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState)){
            //跟微信的人确认过，b2c下，not_pay表明用户已经取消了付款。
            rcFlag = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
        }else if(WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)){
            rcFlag = Workflow.RC_PAY_SUCCESS;
            //付款成功
            setTradeNoBuyerInfoIfExists(result, context);
            setOverseasInfoWhenPay(context.getTransaction(), result);
            if(BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_FREEZE){
                resolvePayFund(result, context);
            }
        }else if(WeixinConstants.TRADE_STATE_CONSUMED.equals(tradeState)) {
            rcFlag = Workflow.RC_CONSUME_SUCCESS;
        }else if(WeixinConstants.TRADE_STATE_SETTLING.equals(tradeState)) {
            rcFlag = Workflow.RC_RETRY;
        }
        return rcFlag;
    }
    
    @Override
    public String depositCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_HMAC_SHA256);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CANCEL), builder.build(), retryTimes, OP_DEPOSIT_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit cancel", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_CANCEL);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode)){
                return Workflow.RC_RETRY;
            }else if(WeixinConstants.RESULT_ERROR_CODE_USER_PAYING.equals(errCode)) {
                // 支付请求返回“USERPAYING：用户支付中，需要输入密码”时，不再进行cancel操作，直接返回io异常，由勾兑服务进行处理
                if(BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                    logger.warn("{}: provider cancel fail, return USERPAYING.", BeanUtil.getPropString(transaction, Transaction.TSN));
                    return Workflow.RC_IOEX;
                }else {
                    return Workflow.RC_RETRY;
                }
            }
        }else{
            return Workflow.RC_CANCEL_SUCCESS;
        }

        return Workflow.RC_ERROR;
    }
    
    @Override
    public String depositConsume(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TOTAL_FEE, context.getOrder().get(Order.EFFECTIVE_TOTAL)+"");
        builder.set(BusinessFields.CONSUME_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        builder.set(BusinessFields.SIGN_TYPE, WeixinConstants.SIGN_TYPE_HMAC_SHA256);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CONSUME), builder.build(), retryTimes, OP_DEPOSIT_CONSUME);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit consume", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CONSUME, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        String returnCode = (String)result.get(ResponseFields.RETURN_CODE);//返回状态码
        String resultCode = (String)result.get(ResponseFields.RESULT_CODE);//业务结果
        String errCode = (String)result.get(ResponseFields.ERR_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_CONSUME);
        if(!WeixinConstants.RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(!WeixinConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
            if(WeixinConstants.RESULT_ERROR_CODE_SYSTEM_ERROR.equals(errCode) 
                    || WeixinConstants.TRADE_STATE_SETTLING.equals(errCode)){
                String queryResult = depositQuery(context);
                if(Workflow.RC_CONSUME_SUCCESS.equals(queryResult)) {
                    resolveConsumeFund(result, context);
                }
                return Workflow.RC_PAY_SUCCESS.equals(queryResult) ? Workflow.RC_IN_PROG: queryResult;
            }else if(WeixinConstants.DEPOSIT_CONSUMER_FAIL_LIST.contains(errCode)) {
                return Workflow.RC_CONSUME_FAIL;
            }
        }else{
            resolveConsumeFund(result, context);
            return Workflow.RC_CONSUME_SUCCESS;
        }

        return Workflow.RC_ERROR;
    }
    
    
    /**
     * 解析退款返回金额信息
     * @param result
     * @param context
     */
    private void resolveConsumeFund(Map<String, Object> result, TransactionContext context){
        Map<String,Object> order = context.getOrder();
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        setOverseasInfoWhenRefund(transaction, result);
        String merchantId = BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        Map<String,Object> payTransaction = dataRepository.getFreezeTransactionByOrderSn(merchantId, orderSn);
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额消费
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分消费 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            if(payments != null){
                List<Map<String,Object>> consumePayments = new ArrayList<>();
                // 微信预授权退款返回参数中未明确定义优惠的退款明细，只有退款金额，需要进行处理
                long consumeFee = BeanUtil.getPropLong(result, BusinessFields.CONSUME_FEE);
                long totalFee = BeanUtil.getPropLong(result, BusinessFields.TOTAL_FEE);
                if(consumeFee != totalFee && 0 != totalFee && null != payments && payments.size() > 0) {
                    long totalAmount = 0l;
                    for (Map<String, Object> payment : payments) {
                        totalAmount += BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
                    }
                    if(totalAmount != 0) {
                        for (int i = 0; i < payments.size(); i++) {
                            Map<String,Object> consumePayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                            consumePayment.put(Transaction.PAYMENT_AMOUNT, Math.round((consumeFee * 1d/totalFee) * BeanUtil.getPropLong(consumePayment, Transaction.PAYMENT_AMOUNT)));
                            consumePayments.add(consumePayment);
                        }
                    }
                }else if(null != payments) {
                    consumePayments.addAll(payments);
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, consumePayments);
            }

        }

    }
    
}
