package com.wosai.upay.workflow;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.lakala.BusinessFields;
import com.wosai.mpay.api.lakala.LakalaClient;
import com.wosai.mpay.api.lakala.LakalaConstants;
import com.wosai.mpay.api.lakala.RequestBuilder;
import com.wosai.mpay.api.lakala.ResponseFields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.TsnGenerator;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;


/**
 * Created by maoyu
 */
public class LakalaServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(LakalaServiceProvider.class);
    public static final String NAME = "provider.lakala";
    public SafeSimpleDateFormat format = new SafeSimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
    
    private Map paywayPayChannelType = CollectionUtil.hashMap(
            Order.PAYWAY_ALIPAY2 + "", LakalaConstants.PAY_WAY_ALIPAY,
            Order.PAYWAY_WEIXIN + "", LakalaConstants.PAY_WAY_WEIXIN,
            Order.PAYWAY_LKLWALLET+"",LakalaConstants.PAY_WAY_LKLPAY,
            Order.PAYWAY_BAIFUBAO + "", LakalaConstants.PAY_WAY_BAIDU,
            Order.PAYWAY_LKL_UNIONPAY + "",LakalaConstants.PAY_WAY_UQRCODEPAY
    );

    private String CANCEL_RESPONSE_CODE_MESSAGE_ORDER_NOT_EXISTS = "010025" + "记录不存在";
    private String CANCEL_RESPONSE_CODE_MESSAGE_ORDER_FAIL = "010012" + "无效交易";
    private String CANCEL_RESPONSE_CODE_MESSAGE_TRANSACTION_NOT_EXISTS = "100016" + "查无原交易流水";

    @Autowired
    private TsnGenerator tsnGenerator;

    private int retryTimes = 3;

    public LakalaServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TXNAMT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LAKALA;
    }

    @Autowired
    protected LakalaClient client;

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> config = getTradeParams(transaction);
        if(config == null){
            return false;
        }else{
            return config.containsKey(TransactionParam.LAKALA_COMPORT_CODE);
        }
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LAKALA_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_BSC);
        builder.set(BusinessFields.REQLOGNO, transaction.get(Transaction.ORDER_SN));
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);
        builder.set(BusinessFields.TXNAMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.AUTHCODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.GOODSTAG, config.get(TransactionParam.GOODS_TAG));
        builder.set(BusinessFields.ORDERINFO, transaction.get(Transaction.SUBJECT));
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_BSC_PAY, request);
        } catch (MpayException ex) {
            logger.error("failed to call lakala pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in lakala pay", ex);
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setTradeNoBuyerInfoIfExists(result, context);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode)) {
            //付款成功
        	resolvePayFund(result, context);
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            return Workflow.RC_PAY_SUCCESS;
        } else if (LakalaConstants.TRADE_STATE_USERPAYING.equals(returnCode)
                ||LakalaConstants.REQUEST_LOG_NO_DUPLICATE.equals(returnCode)) {
            return Workflow.RC_IN_PROG;
        } else if(LakalaConstants.PAY_FAIL_ERR_CODE_LISTS.contains(returnCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Order.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayway) {
            return bscCancel(context);
        }
        return csbCancel(context);
    }


    public String bscCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_BSC_CANCEL);//Y
        builder.set(BusinessFields.REQLOGNO,  BeanUtil.getPropString(transaction, Transaction.TSN));//Y
        builder.set(BusinessFields.ORNREQLOGNO, transaction.get(Transaction.ORDER_SN));//Y
        builder.set(BusinessFields.REQTYPE, LakalaConstants.CANCEL_REQ_TYPE_USER_CANCEL);//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_BSC_CANCEL), 
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_BSC_CANCEL, request, retryTimes, "bsc cancel");
        } catch (MpayException ex) {
            logger.error("failed to call lakala bsc cancel", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode) || LakalaConstants.TRADE_CLOSE.equals(returnCode)) {
            return Workflow.RC_CANCEL_SUCCESS;
        } else if(CANCEL_RESPONSE_CODE_MESSAGE_ORDER_NOT_EXISTS.equals(returnCode + message) 
                    || CANCEL_RESPONSE_CODE_MESSAGE_ORDER_FAIL.equals(returnCode + message)
                    || CANCEL_RESPONSE_CODE_MESSAGE_TRANSACTION_NOT_EXISTS.equals(returnCode + message)){
            return Workflow.RC_CANCEL_SUCCESS;
        } else {
            return Workflow.RC_RETRY;
        }
    }

    public String csbCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_CSB_CANCEL);//Y
        builder.set(BusinessFields.REQLOGNO, BeanUtil.getPropString(transaction, Transaction.TSN));//Y
        builder.set(BusinessFields.ORNREQLOGNO, transaction.get(Transaction.ORDER_SN));//Y
        builder.set(BusinessFields.REQTYPE, LakalaConstants.CANCEL_REQ_TYPE_POS_TIMEOUT);//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);//Y
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_CSB_CANCEL),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_CSB_CANCEL, request, retryTimes, "csb cancel");
        } catch (MpayException ex) {
            logger.error("failed to call lakala csb cancel", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode) || LakalaConstants.TRADE_CLOSE.equals(returnCode)) {
            return Workflow.RC_CANCEL_SUCCESS;
        } else {
            return Workflow.RC_RETRY;
        }
    }


    protected Map<String, Object> retryIfNetworkException(String url, String key, int payMethod, Map<String,Object> request, int times, String logFlag) throws MpayException {
        for (int i = 0; i< times; ++i) {
            try {
                return client.call(url, key, payMethod, request);
            } catch (MpayApiNetworkError ex) {
                logger.warn("encountered ioex in lakala {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        return null;
    }

    /**
     * 拉卡拉接口较为BT,查询类接口都要求每次发起请求的时候请求号唯一
     * @param url
     * @param key
     * @param payMethod
     * @param builder
     * @param times
     * @param logFlag
     * @return
     * @throws MpayException
     */
    protected Map<String, Object> retryWithDiffReqTradeNoIfNetworkException(String url, String key, int payMethod, RequestBuilder builder, int times, String logFlag) throws MpayException{
        if (LakalaConstants.METHOD_BSC_PAY != payMethod && LakalaConstants.METHOD_REFUND != payMethod){
            for (int i = 0; i< times; ++i) {
                try {
                    builder.set(BusinessFields.REQLOGNO, tsnGenerator.nextSn());
                    return client.call(url, key, payMethod, builder.build());
                } catch (MpayApiNetworkError ex) {
                    logger.warn("encountered ioex in lakala {}", logFlag, ex);
                }
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        return null;

    }

    /**
     * 主动查询接口
     *
     * @param context
     * @return
     */
    public String queryPositively(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_QUERY_POSITIVE);
        builder.set(BusinessFields.REQLOGNO, tsnGenerator.nextSn());//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);
        builder.set(BusinessFields.MERORDERNO, BeanUtil.getPropString(extraOutFields, Transaction.CHANNEL_TRADE_NO));
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_POSITIVELYQUERY),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_POSITIVE_QUERY, request, retryTimes, "positive query");
        } catch (MpayException ex) {
            logger.error("failed to call lakala positive query", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode)) {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String) result.get(ResponseFields.TXNTM)));
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    public String queryNormaly(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_QUERY);
        builder.set(BusinessFields.ORNREQLOGNO, transaction.get(Transaction.ORDER_SN));//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);//Y
        builder.set(BusinessFields.REQLOGNO, tsnGenerator.nextSn());//Y
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_QUERY, request, retryTimes, OP_QUERY);
        } catch (MpayException ex) {
            logger.error("failed to call lakala query", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);

        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode)) {
            //付款成功
        	resolvePayFund(result, context);
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String) result.get(ResponseFields.TXNTM)));
            return Workflow.RC_PAY_SUCCESS;
        } else if (LakalaConstants.TRADE_STATE_USERPAYING.equals(returnCode)
                ||LakalaConstants.REQUEST_LOG_NO_DUPLICATE.equals(returnCode)) {
            return Workflow.RC_IN_PROG;
        } else if(LakalaConstants.TRADE_FAIL.equals(returnCode) 
                || LakalaConstants.TRADE_CLOSE.equals(returnCode)) {
            // 已和拉卡拉确认 TRADE_FAIL 和 TRADE_CLOSE 明确支付失败
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String query(TransactionContext context) {
        return queryNormaly(context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_REFUND);
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);//Y
        builder.set(BusinessFields.REQLOGNO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.ORNREQLOGNO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TXNAMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_REFUND, request);//拉卡拉退款接口不具有幂等性,重复退款会报错
        } catch (MpayException ex) {
            logger.error("failed to call lakala refund,start refund query", ex);
            return refundQuery(context);
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in lakala refund,start refund query", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        setTradeNoBuyerInfoIfExists(result, context);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode)) {
            //退款成功
        	resolveRefundFund(result, context);
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            return Workflow.RC_REFUND_SUCCESS;
        } else {
            return refundQuery(context);
        }
    }


    /**
     * 退款查询
     *
     * @param context
     * @return
     */
    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_REFUND_QUERY);//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);//Y
        builder.set(BusinessFields.REQLOGNO, tsnGenerator.nextSn());//Y
        builder.set(BusinessFields.ORNREQLOGNO, transaction.get(Transaction.TSN));//Y
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;

        try {
            result = retryWithDiffReqTradeNoIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_REFUND_QUERY, builder, retryTimes, "refund query");
        } catch (MpayException ex) {
            logger.error("failed to call lakala refund query", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode)) {
            //退款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.PAYORDERID));
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, BeanUtil.getPropString(result, ResponseFields.MERORDERNO));
            return Workflow.RC_REFUND_SUCCESS;
        } else {
            return Workflow.RC_ERROR;
        }

    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Order.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_WAP == subPayway) {
            //门店码支付
            return wapPay(context, resume);
        }
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_CSB);//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);//Y
        builder.set(BusinessFields.REQLOGNO, transaction.get(Transaction.ORDER_SN));//Y
        builder.set(BusinessFields.TXNAMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");//Y
        builder.set(BusinessFields.TRADETYPE, LakalaConstants.TRADE_TYPE_NATIVE);//Y
        builder.set(BusinessFields.PRODUCTID,"wosai");//Y
        builder.set(BusinessFields.OPENID,"");//Y
        builder.set(BusinessFields.ORDERINFO, transaction.get(Transaction.SUBJECT));
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_CSB_PAY, request, 1, OP_PRECREATE);
        } catch (MpayException ex) {
            logger.error("failed to call lakala precareate", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        setTradeNoBuyerInfoIfExists(result, context);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode) || LakalaConstants.TRADE_STATE_USERPAYING.equals(returnCode)) {
            //预下单成功
            if(context.getApiVer() == 1){
                int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
                String qrcode = BeanUtil.getPropString(result, ResponseFields.AUTHCODE);
                if(payway == Order.PAYWAY_WEIXIN){
                    transaction.put(Transaction.PROVIDER_RESPONSE, CollectionUtil.hashMap(
                            PayResponse.CODE_URL,qrcode
                    ));
                }
            }
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.QRCODE, result.get(ResponseFields.AUTHCODE));
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {

        return null;
    }

    public String wapPay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_CSB);//Y
        String tradeTime = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, tradeTime);//Y
        builder.set(BusinessFields.REQLOGNO, transaction.get(Transaction.ORDER_SN));//Y
        builder.set(BusinessFields.TXNAMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");//Y
        builder.set(BusinessFields.TRADETYPE, LakalaConstants.TRADE_TYPE_JSAPI);//Y
        builder.set(BusinessFields.ORDERINFO, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.PRODUCTID,"wosai");//Y
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        if (payerUid != null) {
            builder.set(BusinessFields.OPENID, payerUid);
        }
        carryOverExtendedParams(extended, builder);
        Map<String, Object> request = builder.build();
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP),
            		(String) config.get(TransactionParam.LAKALA_SECRET), LakalaConstants.METHOD_CSB_PAY, request, 1, "wap precareate");
        } catch (MpayException ex) {
            logger.error("failed to call lakala wap precareate", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        setTradeNoBuyerInfoIfExists(result, context);
        String returnCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
        if (LakalaConstants.TRADE_STATUS_SUCC.equals(returnCode)|| LakalaConstants.TRADE_STATE_USERPAYING.equals(returnCode)) {
            //预下单成功
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);

                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                Map<String, Object> wapRequest = new HashMap<String, Object>();
            if (payway == Order.PAYWAY_WEIXIN){
                String extRpData = BeanUtil.getPropString(result,ResponseFields.EXTRPDATA);
                if(StringUtils.isEmpty(extRpData)){
                    return Workflow.RC_ERROR;
                }
                /**<extRpData>appId=wxd9c866ad31c3c6da|nonceStr=ExMAtz0FQVQkxAOn|signType=MD5|timeStamp=1464338036|package=prepay_id=wx201605271633563efc72728d0583214570|paySign=F21D80FD5411EAAD2A11ED7BDD0E6318|</extRpData>**/
                String[] wapRequestData = extRpData.split("\\|");
                for(String item : wapRequestData){
                    String[] ite = item.split("=",2);
                    wapRequest.put(ite[0],ite[1]);
                }
            }else if (payway == Order.PAYWAY_LKLWALLET){
                String prepayId = BeanUtil.getPropString(result,ResponseFields.PREPAYID);
                wapRequest.put(ResponseFields.PREPAYID,prepayId);
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);

            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }


    /**
     * 获取默认的requestBuilder，设置请求默认值。
     *
     * @param context
     * @return
     */
    protected RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.COMPORGCODE, config.get(TransactionParam.LAKALA_COMPORT_CODE));//Y
        int payway = BeanUtil.getPropInt(context.getOrder(), Order.PAYWAY);
        builder.set(BusinessFields.PAYCHLTYP, paywayPayChannelType.get(payway + ""));//Y
        builder.set(BusinessFields.MERCID, config.get(TransactionParam.LAKALA_MERC_ID));//Y
        builder.set(BusinessFields.TERMID, config.get(TransactionParam.LAKALA_TERM_ID));//Y
        builder.set(BusinessFields.SUBAPPID, config.get(TransactionParam.LAKALA_WEXIN_SUB_APPID));

        return builder;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.set(key, value);
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
    	String responseCode = BeanUtil.getPropString(result, ResponseFields.RESPONSECODE);
    	String message = BeanUtil.getPropString(result, ResponseFields.MESSAGE);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RESPONSECODE, responseCode);//返回状态码
        map.put(ResponseFields.MESSAGE, message);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, 
                LakalaConstants.TRADE_STATUS_SUCC.equals(responseCode) || LakalaConstants.TRADE_STATE_USERPAYING.equals(responseCode), 
                responseCode, 
                message);
    }

    /**
     * 设置订单号
     * @param result
     * @param context
     */
    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        if(result == null || result.isEmpty()){
            return;
        }
        Map transaction = context.getTransaction();
        Map order = context.getOrder();
        String buyerUid = BeanUtil.getPropString(result, ResponseFields.USERID);
        String tradeNo = BeanUtil.getPropString(result, ResponseFields.MERORDERNO); //拉卡拉订单号
        String channelTradeNo = BeanUtil.getPropString(result, ResponseFields.PAYORDERID);//微信订单号
        if(!StringUtils.isEmpty(buyerUid)){
            if(StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_UID))){
                order.put(Order.BUYER_UID, buyerUid);
            }
            if(StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }
        if(!StringUtils.isEmpty(tradeNo)){
            if(StringUtils.isEmpty(BeanUtil.getPropString(order, Order.TRADE_NO))){
                order.put(Order.TRADE_NO, tradeNo);
            }
            if(StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
        if(!StringUtils.isEmpty(channelTradeNo)){
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(extraOutFields ==  null){
            	extraOutFields = new HashMap<>();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
        }
    }
    
    /**
     * 解析返回金额相关信息
     * @param context
     */
    private void resolvePayFund(Map<String, Object> result, TransactionContext context){
        if(result == null){
            return;
        }
        String mrkInfo = BeanUtil.getPropString(result, ResponseFields.MRKINFO);
        if(!StringUtils.isEmpty(mrkInfo) && StringUtils.isNumeric(mrkInfo)){
        	long discount = new BigDecimal(mrkInfo).longValue();
            Map<String, Object> transaction = context.getTransaction();
            long totalFee = BeanUtil.getPropLong(result, ResponseFields.TXNAMT, 0);
            long cashFee = totalFee - discount;
            transaction.put(Transaction.PAID_AMOUNT, cashFee);
            if(totalFee > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, totalFee);
            }
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
            if(payments == null || payments.isEmpty()){
            	payments = new ArrayList<Map<String,Object>>();
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                    Transaction.PAYMENT_ORIGIN_TYPE, null,
                    Transaction.PAYMENT_AMOUNT, discount
            ));
            Map<String,Object> order = context.getOrder();
            if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l && discount != 0){
                order.put(Order.TOTAL_DISCOUNT, discount);
                order.put(Order.NET_DISCOUNT, discount);
            }
        }
    }
    
    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        //拉卡拉退款不返回具体的退款明细，规则为先退优惠金额，再退用户实付金额
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        long refundAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        long netDiscount = BeanUtil.getPropLong(order, Order.NET_DISCOUNT, 0);
        long paidAmount  = 0;
        long receiveAmount = 0;
        if(refundAmount <= netDiscount){
            paidAmount = 0;
            receiveAmount = refundAmount;
        }else{
            paidAmount = refundAmount - netDiscount;
            receiveAmount = refundAmount;
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
    }
}
