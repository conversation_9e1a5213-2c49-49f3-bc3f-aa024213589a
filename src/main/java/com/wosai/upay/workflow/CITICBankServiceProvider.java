package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.cibbank.BusinessFields;
import com.wosai.mpay.api.cibbank.CIBBankConstants;
import com.wosai.mpay.api.cibbank.ProtocolFields;
import com.wosai.mpay.api.cibbank.RequestBuilder;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created by jianfree on 15/8/16.
 */
public class CITICBankServiceProvider extends AbstractSwiftPassServiceProvider {
    public static final String NAME = "provider.citicbank";

    public CITICBankServiceProvider(){
        this.logger = LoggerFactory.getLogger(CITICBankServiceProvider.class);
    }


    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider(Map<String, Object> transaction) {
        return Order.PROVIDER_CITICBANK;
    }

    public Integer getProvider() {
        return Order.PROVIDER_CITICBANK;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CITICBANK_TRADE_PARAMS);
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(String service, Map<String, Object> config){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARSET, CIBBankConstants.CHARSET_UTF8);
        builder.set(ProtocolFields.MCH_ID, BeanUtil.getPropString(config, TransactionParam.CITICBANK_MCH_ID));
        builder.set(ProtocolFields.SIGN_TYPE, CIBBankConstants.SIGN_TYPE_MD5);
        builder.set(ProtocolFields.VERSION, CIBBankConstants.VERSION);
        builder.set(ProtocolFields.SERVICE, service);
        String agentNo = BeanUtil.getPropString(config, TransactionParam.CITICBANK_SIGN_AGENT_NO);
        String agentKey = BeanUtil.getPropString(config, TransactionParam.CITICBANK_SIGN_AGENT_KEY);
        if(!StringUtil.empty(agentNo) && !StringUtil.empty(agentKey)){
            builder.set(ProtocolFields.SIGN_AGENT_NO, agentNo);
        }else{
            String groupNo = BeanUtil.getPropString(config, TransactionParam.CITICBANK_GROUP_NO);
            String groupKey = BeanUtil.getPropString(config, TransactionParam.CITICBANK_GROUP_KEY);
            if(!StringUtil.empty(groupNo) && !StringUtil.empty(groupKey)){
                builder.set(ProtocolFields.GROUP_NO, groupNo);
            }
        }
        String sysProviderId = BeanUtil.getPropString(config, TransactionParam.CITICBANK_SYS_PROVIDER_ID);
        if(!StringUtil.empty(sysProviderId)){
            builder.set(BusinessFields.SYS_PROVIDER_ID, sysProviderId);
        }
        return builder;
    }
    @Override
    public  String getSignKey(Map<String,Object> config){
        String agentNo = BeanUtil.getPropString(config, TransactionParam.CITICBANK_SIGN_AGENT_NO);
        String agentKey = BeanUtil.getPropString(config, TransactionParam.CITICBANK_SIGN_AGENT_KEY);
        if(!StringUtil.empty(agentNo) && !StringUtil.empty(agentKey)){
            return agentKey;
        }else{
            String groupNo = BeanUtil.getPropString(config, TransactionParam.CITICBANK_GROUP_NO);
            String groupKey = BeanUtil.getPropString(config, TransactionParam.CITICBANK_GROUP_KEY);
            if(!StringUtil.empty(groupNo) && !StringUtil.empty(groupKey)){
                return groupKey;
            }else{
                return BeanUtil.getPropString(config, TransactionParam.CITICBANK_MCH_KEY);
            }
        }
    }

    @Override
    public String getRefundUserId(Map<String, Object> config) {
        return BeanUtil.getPropString(config, TransactionParam.CITICBANK_MCH_ID);
    }


}
