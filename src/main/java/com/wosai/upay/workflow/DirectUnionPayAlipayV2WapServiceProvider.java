package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@ServiceProvicerPriority(priority = 1)
public class DirectUnionPayAlipayV2WapServiceProvider extends UnionPayAlipayV2WapServiceProvider{
    public static final Logger logger = LoggerFactory.getLogger(DirectUnionPayAlipayV2WapServiceProvider.class);
    public static final String NAME = "provider.direct.unionpay.alipay.wap.v2";
    private static final String DEFAULT_CERT_ID = "**********";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_DIRECT_UNIONPAY;
    }


    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(type == Transaction.TYPE_REFUND && (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI)) {
            return getTradeParams(transaction) != null && getTradeCurrency(transaction).equals(TransactionParam.UPAY_DEFAULT_CURRENCY_CNY);
        }
        return false;
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(config,TransactionParam.UNION_PAY_DIRECT_ALIPAY_APP_ID));
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, MapUtil.getString(config, TransactionParam.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA2));
        builder.set(ProtocolV2Fields.CERT_ID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_DIRECT_CERT_ID, DEFAULT_CERT_ID));
        String sysProviderId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_DIRECT_SYS_PROVIDER_ID);

        String alipaySubMchId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID);
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(
                BusinessV2Fields.SUB_MERCHANT_ID, alipaySubMchId,
                BusinessV2Fields.MERCHANT_NAME, getSubMerchantName(config, transaction)
        ));

        Map extraParam = CollectionUtil.hashMap(
                BusinessV2Fields.SECONDARY_MERCHANT_ID, alipaySubMchId
        );
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, extraParam);

        if(!StringUtil.empty(sysProviderId)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, sysProviderId
            ));
            builder.bizSet(BusinessV2Fields.ORG_PID, sysProviderId);
            extraParam.put(BusinessV2Fields.REQUEST_ORG_ID, sysProviderId);

            if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                builder.bizSet(BusinessV2Fields.REQUEST_ORG_PID, sysProviderId);
            }
        }

        setTerminalInfo(context, MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), builder);

        return builder;
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), MpayServiceProvider.OP_DEPOSIT_PREFREEZE);

        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.getBizContent().remove(BusinessV2Fields.EXTEND_PARAMS);
        String notifyUrl = getNotifyUrl(notifyHost, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_PREFREEZE), context, NOTIFY_DEPOSIT);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_APP_FREEZE);
        builder.bizSet(BusinessV2Fields.AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));

        Map extraParam = (Map) BeanUtil.getProperty(builder.getBizContent(), BusinessV2Fields.EXTRA_PARAM);
        if (Objects.isNull(extraParam)) {
            extraParam = CollectionUtil.hashMap();
        }
        extraParam.put(BusinessV2Fields.CATEGORY
                , BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_CATEGORY));

        if(config.containsKey(TransactionParam.ALIPAY_SERVICE_ID)) {
            extraParam.put(BusinessV2Fields.SERVICE_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_SERVICE_ID));
        }
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, JsonUtil.toJsonStr(extraParam));
        builder.bizSet(BusinessV2Fields.ORDER_TITLE, transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.PAY_TIMEOUT, defaultTimeoutExpress);
        builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_PRE_AUTH_ONLINE);
        carryOverExtendedParams(extendedParams, builder);
        Map<String,String> request = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("unionpay alipay request builder error ", e);
        }

        Map<String,Object> result;
        try {
            result = retryIfNetworkException(config, url, request, 1, OP_DEPOSIT_PREFREEZE);
        }catch (Exception ex) {
            logger.error("failed to call unionpay alipay deposit prefreeze", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_PREFREEZE, ex);
            return Workflow.RC_IOEX;
        }
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String returnCode = (String) result.get(BusinessV2Fields.CODE);
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_PREFREEZE);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_SYS_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            String responseBodyStr = MapUtil.getString(result, BusinessV2Fields.ORDER_STR);
            //预下单成功
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            Map<String, Object> wapRequest = new HashMap<String, Object>();
            wapRequest.put(WapV2Fields.ORDER_STR, responseBodyStr);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }
}
