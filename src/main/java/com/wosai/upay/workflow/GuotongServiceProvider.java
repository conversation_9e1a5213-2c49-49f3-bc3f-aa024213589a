package com.wosai.upay.workflow;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.guotong.*;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.NumberUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.util.ProviderUtil.*;

/**
 * 国通支付
 */
public class GuotongServiceProvider extends AbstractServiceProvider {
    protected static final Logger logger = LoggerFactory.getLogger(GuotongServiceProvider.class);

    protected String notifyHost;

    @Autowired
    private GuotongClient client;

    public static final String NAME = "provider.guotong";

    private static final Map<String, String> RESPONSE_CARD_TYPE_PAYMENT_TYPE_CONFIG = CollectionUtil.hashMap(
            GuotongConstants.CARD_TYPE_DEBIT, Payment.TYPE_BANKCARD_DEBIT,
            GuotongConstants.CARD_TYPE_CREDIT, Payment.TYPE_BANKCARD_CREDIT,
            GuotongConstants.CARD_TYPE_PREPAID, Payment.TYPE_BANKCARD_PREPAID,
            GuotongConstants.CARD_TYPE_OTHER, Payment.TYPE_OTHERS
    );

    private static final Map<Integer, String> PAYMENT_PAYWAY_MAPPING = CollectionUtil.hashMap(
            Payway.ALIPAY.getCode(), GuotongConstants.PAY_WAY_ALIPAY,
            Payway.ALIPAY2.getCode(), GuotongConstants.PAY_WAY_ALIPAY,
            Payway.WEIXIN.getCode(), GuotongConstants.PAY_WAY_WECHAT,
            Payway.UNIONPAY.getCode(), GuotongConstants.PAY_WAY_UNION
    );

    private static final Map<Integer, String> REFUND_TAG_MAPPING = CollectionUtil.hashMap(
            Payway.ALIPAY.getCode(), GuotongConstants.TAG_ALIPAY,
            Payway.ALIPAY2.getCode(), GuotongConstants.TAG_ALIPAY,
            Payway.WEIXIN.getCode(), GuotongConstants.TAG_WECHAT,
            Payway.UNIONPAY.getCode(), GuotongConstants.TAG_UNION
    );


    public GuotongServiceProvider() {
        dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_GUOTONG;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.GUOTONG_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        GuotongRequestBuilder builder = buildCommonRequestParams(config);
        builder.set(GuotongProtocolFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN)); // 订单号，需唯一
        builder.set(GuotongProtocolFields.TXAMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));// 交易金额，单位分
        builder.set(GuotongBusinessFields.CODE, MapUtil.getString(extraParams, Transaction.BARCODE)); // 通知地址
        builder.set(GuotongBusinessFields.TYPE, GuotongConstants.TYPE_SMART_POS); // 设备类型，C-PC端
        // 设置可选参数
        builder.set(GuotongBusinessFields.DRIVE_NO, MapUtil.getString(transaction, Transaction.DEVICE_ID)); // 字母或数字
        builder.set(GuotongBusinessFields.REMARK, MapUtil.getString(transaction, Transaction.REMARK)); // 备注
        builder.set(GuotongBusinessFields.TITLE, MapUtil.getString(transaction, Transaction.SUBJECT)); // 商品标题
        builder.set(GuotongBusinessFields.LIMIT_PAY, getLimitCredit(transaction, GuotongConstants.LIMIT_PAY_NO_LIMIT, GuotongConstants.LIMIT_PAY_NO_CREDIT)); // 0-无限制，1-不能使用信用卡
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        builder.set(GuotongBusinessFields.TRADING_IP, terminalInfo.getIp()); // 商户端终端设备 IP 地址
        builder.set(GuotongBusinessFields.LATITUDE, MapUtil.getString(transaction, Transaction.LATITUDE));
        builder.set(GuotongBusinessFields.LONGITUDE, MapUtil.getString(transaction, Transaction.LONGITUDE));
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.set(GuotongBusinessFields.ASYNC_NOTIFY, notifyUrl); //支付完成回调通知
        // 设置银联259号文
        setTerminalInfo(context.getTransaction(), builder);
        // 解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder.build());
        // 设置商户品信息
        setB2cGoodsDetails(transaction, builder);
        Map<String, Object> result;
        try {
            result = client.call(url, builder.build(), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.GUOTONG_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call guotong pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        updateTransactionCommonInfo(transaction, result);
        String resultCode = MapUtil.getString(result, GuotongResponseFields.CODE);
        Map<String, Object> resultData = MapUtil.getMap(result, GuotongResponseFields.DATA);
        if (MapUtil.isEmpty(resultData)) {
            return Workflow.RC_IOEX;
        }
        // 支付成功
        if (GuotongConstants.CODE_SUCCESS.equals(resultCode)) {
            // 优惠信息与查询兼容处理
            String promotionDetail = MapUtil.getString(resultData, GuotongResponseFields.PROMOTION_DETAIL);
            if (StringUtils.isNotEmpty(promotionDetail)) {
                try {
                    List<Map<String, Object>> promotionDetailList = JsonUtil.jsonStringToObject(promotionDetail, new TypeReference<List<Map<String, Object>>>() {
                    });
                    resultData.put(GuotongResponseFields.PROMOTION_DETAIL_LIST, promotionDetailList);
                } catch (MpayException e) {
                    logger.error("failed to parse promotion detail", e);
                }
            }
            resolvePayFund(context, resultData);
            return Workflow.RC_PAY_SUCCESS;
        } else if (GuotongConstants.CODE_PROCESSING.equals(resultCode)) {
            return Workflow.RC_IN_PROG;
        }
        // fail
        return Workflow.RC_TRADE_CANCELED;
    }


    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持撤单");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, false);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        updateTransactionCommonInfo(transaction, result);
        String resultCode = MapUtil.getString(result, GuotongResponseFields.CODE);
        Map<String, Object> resultData = MapUtil.getMap(result, GuotongResponseFields.DATA);
        if (MapUtil.isEmpty(resultData)) {
            return Workflow.RC_IOEX;
        }
        // 成功
        if (GuotongConstants.CODE_SUCCESS.equals(resultCode)) {
            // 设置优惠
            resolvePayFund(context, resultData);
            return Workflow.RC_PAY_SUCCESS;
        } else if (GuotongConstants.CODE_PROCESSING.equals(resultCode)) {
            return Workflow.RC_IN_PROG;
        } else if (GuotongConstants.CODE_ORDER_CLOSED.equals(resultCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        // fail
        return Workflow.RC_ERROR;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, true);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String resultCode = MapUtil.getString(result, GuotongResponseFields.CODE);
        Map<String, Object> resultData = MapUtil.getMap(result, GuotongResponseFields.DATA);
        String orderStatus = MapUtil.getString(resultData, GuotongResponseFields.ORDER_STATUS);
        if (MapUtil.isEmpty(resultData)) {
            return Workflow.RC_IOEX;
        }

        if (GuotongConstants.isRefundSuccess(resultCode, orderStatus)) {
            // success
            updateTransactionRefundInfo(context, resultData);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (GuotongConstants.isRefundFailed(resultCode, orderStatus)) {
            return Workflow.RC_SYS_ERROR;
        }
        return Workflow.RC_RETRY;
    }


    // doQuery
    protected Map<String, Object> doQuery(TransactionContext context, boolean isRefundQuery) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        GuotongRequestBuilder builder = buildCommonRequestParams(config);
        Long finishTime = MapUtil.getLong(transaction, Transaction.CHANNEL_FINISH_TIME);
        if (finishTime == null) {
            finishTime = MapUtil.getLong(transaction, DaoConstants.CTIME);
        }
        String originTradeDate = LocalDateTimeUtil.format(LocalDateTimeUtil.of(finishTime), DatePattern.PURE_DATE_PATTERN);
        // 查询参数
        if (isRefundQuery) {
            builder.set(GuotongBusinessFields.OLD_T_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
            builder.set(GuotongBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
            builder.set(GuotongBusinessFields.ORIGIN_TRADE_DATE, originTradeDate);
        } else {
            builder.set(GuotongBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
            builder.set(GuotongBusinessFields.ORDER_TIME, originTradeDate);
        }
        String queryFlag = isRefundQuery ? OP_REFUND_QUERY : OP_QUERY;
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), queryFlag);
        Map<String, Object> result = null;
        try {
            result = client.call(url, builder.build(), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.GUOTONG_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call guotong doQuery tsn={}, queryFlag={}", MapUtil.getString(transaction, Transaction.TSN), queryFlag, ex);
            setTransactionContextErrorInfo(context, queryFlag, ex);
            return null;
        }
        setTransactionContextErrorInfo(result, context, queryFlag);
        return result;
    }


    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        } else {
            return doRefund(context);
        }
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        GuotongRequestBuilder builder = buildCommonRequestParams(config);
        builder.set(GuotongBusinessFields.OLD_T_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(GuotongBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(GuotongBusinessFields.REFUND_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(GuotongBusinessFields.TAG, REFUND_TAG_MAPPING.get(payway)); // 商户所在国通系统内商户号
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            result = client.call(url, builder.build(), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call guotong refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        } finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String resultCode = MapUtil.getString(result, GuotongResponseFields.CODE);
        Map<String, Object> resultData = MapUtil.getMap(result, GuotongResponseFields.DATA);
        String orderStatus = MapUtil.getString(resultData, GuotongResponseFields.ORDER_STATUS);
        if (MapUtil.isEmpty(resultData)) {
            return Workflow.RC_IOEX;
        }
        if (GuotongConstants.isRefundSuccess(resultCode, orderStatus)) {
            // success
            updateTransactionRefundInfo(context, resultData);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (GuotongConstants.isRefundFailed(resultCode, orderStatus)) {
            return Workflow.RC_SYS_ERROR;
        }
        return Workflow.RC_RETRY;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        GuotongRequestBuilder builder = buildCommonRequestParams(config);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);

        builder.set(GuotongProtocolFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN)); // 订单号，需唯一
        builder.set(GuotongProtocolFields.TXAMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));// 交易金额，单位分
        builder.set(GuotongBusinessFields.OPENID, MapUtil.getString(extraParams, Transaction.PAYER_UID)); // 微信用户标识
        builder.set(GuotongBusinessFields.PAY_WAY, PAYMENT_PAYWAY_MAPPING.get(payway)); // 支付方式：1.微信支付（小程序）
        builder.set(GuotongBusinessFields.OUT_TIME, DEFAULT_TIME_EXPIRE_MINUTE); // 过期时间
        // 设置可选参数
        builder.set(GuotongBusinessFields.DRIVE_NO, MapUtil.getString(transaction, Transaction.DEVICE_ID)); // 字母或数字
        builder.set(GuotongBusinessFields.REMARK, MapUtil.getString(transaction, Transaction.REMARK)); // 备注
        builder.set(GuotongBusinessFields.TITLE, MapUtil.getString(transaction, Transaction.SUBJECT)); // 商品标题
        builder.set(GuotongBusinessFields.LIMIT_PAY, getLimitCredit(transaction, GuotongConstants.LIMIT_PAY_NO_LIMIT, GuotongConstants.LIMIT_PAY_NO_CREDIT)); // 0-无限制，1-不能使用信用卡
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        builder.set(GuotongBusinessFields.IP, terminalInfo.getIp()); // 商户端终端设备 IP 地址
        builder.set(GuotongBusinessFields.LATITUDE, MapUtil.getString(transaction, Transaction.LATITUDE));
        builder.set(GuotongBusinessFields.LONGITUDE, MapUtil.getString(transaction, Transaction.LONGITUDE));
        if (payway == Order.PAYWAY_WEIXIN) {
            String wxSubAppId = null;
            if (subPayway == Order.SUB_PAYWAY_MINI) {
                wxSubAppId = MapUtil.getString(config, TransactionParam.GUOTONG_WEIXIN_MINI_SUB_APP_ID);
            }
            if (StringUtils.isEmpty(wxSubAppId)) {
                wxSubAppId = MapUtil.getString(config, TransactionParam.GUOTONG_WEIXIN_SUB_APP_ID);
            }
            builder.set(GuotongBusinessFields.WX_APPID, wxSubAppId); // 微信支付必传
        }
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.set(GuotongBusinessFields.ASYNC_NOTIFY, notifyUrl);
        carryOverExtendedParams(extendedParams, builder.build());
        //设置商户品信息
        setC2bGoodsDetails(transaction, builder);
        Map<String, Object> result;
        try {
            result = client.call(url, builder.build(), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call guotong precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        updateTransactionCommonInfo(transaction, result);
        String resultCode = MapUtil.getString(result, GuotongResponseFields.CODE);
        Map<String, Object> resultData = MapUtil.getMap(result, GuotongResponseFields.DATA);
        if (!GuotongConstants.CODE_SUCCESS.equals(resultCode)) {
            return Workflow.RC_ERROR;
        }
        // 可支付标识
        String getprepayid = getOrDefault(MapUtil.getString(resultData, GuotongResponseFields.GET_PREPAY_ID),
                MapUtil.getString(resultData, GuotongResponseFields.GETPREPAYID));
        if (!GuotongConstants.YES.equals(getprepayid)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        if (MapUtil.isEmpty(resultData)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        try {
            Map<String, Object> wapRequest = new HashMap<String, Object>();
            if (payway == Order.PAYWAY_WEIXIN) {
                wapRequest.put(WapFields.APP_ID, MapUtil.getString(resultData, GuotongResponseFields.JSAPI_APPID));
                wapRequest.put(WapFields.TIME_STAMP, MapUtil.getString(resultData, GuotongResponseFields.JSAPI_TIMESTAMP));
                wapRequest.put(WapFields.NONCE_STR, MapUtil.getString(resultData, GuotongResponseFields.JSAPI_NONCESTR));
                wapRequest.put(WapFields.PACKAGE, MapUtil.getString(resultData, GuotongResponseFields.JSAPI_PACKAGE));
                wapRequest.put(WapFields.SIGN_TYPE, MapUtil.getString(resultData, GuotongResponseFields.JSAPI_SIGN_TYPE));
                wapRequest.put(WapFields.PAY_SIGN, MapUtil.getString(resultData, GuotongResponseFields.JSAPI_PAY_SIGN));
                if (StringUtil.empty(BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_MCH_ID))) {
                    wapRequest.put(WapFields.PARTNER_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
                } else {
                    wapRequest.put(WapFields.PARTNER_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
                }
            } else if (payway == Order.PAYWAY_ALIPAY2) {
                // string
                wapRequest.put(WAP_PAY_REQUEST_ALIPAY_TRADE_NO, MapUtil.getString(resultData, GuotongResponseFields.PREPAYID));
            } else if (payway == Order.PAYWAY_UNIONPAY) {
                String redirecturl = MapUtil.getString(resultData, GuotongResponseFields.REDIRECT_URL);
                wapRequest.put(WAP_PAY_REQUEST_ALIPAY_UNION_PAY_QRCODE_REDIRECT_URL, redirecturl);
            } else if (payway == Order.PAYWAY_JD) {
                wapRequest.put(Transaction.REDIRECT_URL, MapUtil.getString(result, GuotongResponseFields.PAY_URL));
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        } catch (Exception e) {
            logger.error("guotong process wap pay request error: " + e.getMessage(), e);
            return Workflow.RC_TRADE_CANCELED;
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    protected int getNotifyUrlLimit() {
        return DEFAULT_NOTIFY_URL_LIMIT;
    }

    private static GuotongRequestBuilder buildCommonRequestParams(Map<String, Object> config) {
        GuotongRequestBuilder builder = new GuotongRequestBuilder();
        builder.set(GuotongBusinessFields.AGET_ID, MapUtil.getString(config, TransactionParam.GUOTONG_AGENT_NO));
        builder.set(GuotongBusinessFields.CUST_ID, MapUtil.getString(config, TransactionParam.GUOTONG_PROVIDER_MCH_ID));
        builder.set(GuotongBusinessFields.TIME_STAMP, LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)); // 时间戳
        builder.set(GuotongBusinessFields.VERSION, GuotongConstants.DEFAULT_VERSION); // 版本号
        return builder;
    }

    /**
     * 填补扩展信息
     * @param extended
     * @param request
     */
    protected void carryOverExtendedParams(Map<String, Object> extended, Map<String, Object> request) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();
            // 银联二维码场景
            String providerPayway = MapUtil.getString(request, GuotongBusinessFields.PAY_WAY);
            if (Objects.equals(GuotongConstants.PAY_WAY_UNION, providerPayway) && Objects.equals(GuotongBusinessFields.QR_CODE, key)) {
                value = processUnionPayQRCode(value);
            }
            if (Objects.isNull(value)) {
                continue;
            }
            // 商品信息单独处理
            if (Objects.equals(key, UpayConstant.GOODS_DETAIL)
                    || Objects.equals(key, UpayConstant.DETAIL)) {
                continue;
            }
            request.put(key, value);
        }
    }

    private void setB2cGoodsDetails(Map<String, Object> transaction, GuotongRequestBuilder builder) {
        Map<String, Object> extended = null;
        try {
            extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
            if (Objects.isNull(extended)) {
                return;
            }
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            List goodsDetailList = (List) MapUtil.getObject(extended, UpayConstant.GOODS_DETAIL);
            if (Objects.isNull(goodsDetailList)) {
                goodsDetailList = (List) BeanUtil.getNestedProperty(extended, String.format("%s.%s", UpayConstant.DETAIL, UpayConstant.GOODS_DETAIL));
            }
            if (CollectionUtils.isEmpty(goodsDetailList)) {
                return;
            }
            if (payway == Order.PAYWAY_WEIXIN) {
                builder.set(GuotongBusinessFields.GOODS_INFO, MapUtil.hashMap(GuotongBusinessFields.GOODS_DETAIL, goodsDetailList));
            } else if (payway == Order.PAYWAY_ALIPAY2) {
                builder.set(GuotongBusinessFields.GOODS_DETAIL_LIST, goodsDetailList);
            }
        } catch (Exception e) {
            logger.error("setB2cGoodsDetails failed. extended={}", JacksonUtil.toJsonString(extended), e);
        }
    }

    private void setC2bGoodsDetails(Map<String, Object> transaction, GuotongRequestBuilder builder) {
        Map<String, Object> extended = null;
        try {
            extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
            if (Objects.isNull(extended)) {
                return;
            }
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            List goodsDetailList = (List) MapUtil.getObject(extended, UpayConstant.GOODS_DETAIL);
            if (Objects.isNull(goodsDetailList)) {
                goodsDetailList = (List) BeanUtil.getNestedProperty(extended, String.format("%s.%s", UpayConstant.DETAIL, UpayConstant.GOODS_DETAIL));
            }
            if (CollectionUtils.isEmpty(goodsDetailList)) {
                return;
            }
            if (payway == Order.PAYWAY_WEIXIN) {
                builder.set(GuotongBusinessFields.DETAIL, MapUtil.hashMap(GuotongBusinessFields.GOODS_DETAIL, goodsDetailList));
            } else if (payway == Order.PAYWAY_ALIPAY2) {
                builder.set(GuotongBusinessFields.ALIPAY_GOODS_DETAIL, goodsDetailList);
            }
        } catch (Exception e) {
            logger.error("setC2bGoodsDetails failed. extended={}", JacksonUtil.toJsonString(extended), e);
        }
    }

    /**
     * 处理银联二维码
     * 国通对qrcode有长度限制(256字符)，按照国通要求:
     * 1. 只传输码牌链接
     * 2. 不需要额外拼接
     * 3. 如果不传，国通侧有兜底逻辑处理
     *
     * @param qrCode 原始二维码URL
     * @return 处理后的简化URL
     */
    private String processUnionPayQRCode(Object qrCode) {
        try {
            return StringUtils.substringBefore((String) qrCode, "?");
        } catch (Exception e) {
            logger.error("处理银联二维码失败 qrCode={}", qrCode, e);
            return null;
        }
    }

    /**
     * 更新付款信息
     *
     * @param transaction
     * @param resultData
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> resultData) {
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, resultData, GuotongResponseFields.ORDER_TIME, object -> parseTimeString((String) object));
        updateMapIfResponseNotNull(transaction, Transaction.PAID_AMOUNT, resultData, GuotongResponseFields.TXAMT, object -> NumberUtil.toLong("" + object));
        // 设置手续费
        updateMapIfResponseNotNull(MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS), Transaction.REAL_TRADE_FEE,
                resultData, GuotongResponseFields.CUST_FEE, object -> NumberUtil.toLong("" + object));
        // 设置付款人在支付通道的用户ID
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID,
                resultData, GuotongResponseFields.OPEN_ID, object -> object);
    }

    /**
     * 更新退款信息
     *
     * @param context
     * @param resultData
     */
    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> resultData) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        //设置退款手续费
        updateMapIfResponseNotNull(extraOutFields, Transaction.REAL_TRADE_FEE, resultData,
                GuotongResponseFields.REFUND_FEE, object -> Math.abs(NumberUtil.toLong("" + object)));
        //设置退款时间
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, resultData,
                GuotongResponseFields.ORDER_TIME, object -> parseTimeString((String) object));
        resolveRefundFundByPercent(context.getTransaction(),
                getPayOrConsumerTransaction(context.getTransaction(), MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    /**
     * 更新通用字段
     *
     * @param transaction
     * @param result
     */
    protected void updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> result) {
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        // 解析订单号
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_TRADE_NO, result, GuotongResponseFields.ORDER_NO);
        // 解析微信支付宝支付源订单号
        updateMapIfResponseNotNull(extraOutFields, Transaction.CHANNEL_TRADE_NO, result, GuotongResponseFields.TORDER_NO);
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String resultCode = MapUtil.getString(result, GuotongResponseFields.CODE);
        String message = MapUtil.getString(result, GuotongResponseFields.MSG);
        Map<String, Object> recordResult = MapUtil.hashMap(
                GuotongResponseFields.CODE, resultCode,
                GuotongResponseFields.MSG, message
        );
        setTransactionContextErrorInfo(context.getTransaction(), key, recordResult
                , GuotongConstants.CODE_SUCCESS.contains(resultCode), resultCode, message);
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        Map<String, Object> config = getTradeParams(transaction);
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        GuotongRequestBuilder builder = buildCommonRequestParams(config);
        builder.set(GuotongBusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        builder.set(GuotongBusinessFields.PAY_CODE, MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER));
        builder.set(GuotongBusinessFields.ACCESS, GuotongConstants.UNION_USER_ID_DIRECT);
        builder.set(GuotongBusinessFields.IP, terminalInfo.getIp());
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY);
        Map<String, Object> result;
        try {
            result = client.call(url, builder.build(), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call guotong queryUserInfo", ex);
            return null;
        }
        Map resultData = MapUtil.getMap(result, GuotongResponseFields.DATA);
        if (MapUtil.isEmpty(resultData)) {
            return null;
        }
        return CollectionUtil.hashMap(com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID,
                MapUtil.getString(resultData, GuotongResponseFields.USER_ID));
    }

    /**
     * 处理支付优惠信息并设置到交易上下文中
     * 主要处理微信支付宝和银联三种支付方式的优惠信息
     *
     * @param context    交易上下文，包含交易信息和订单信息
     * @param resultData 支付结果数据
     */
    private void resolvePayFund(TransactionContext context, Map<String, Object> resultData) {
        Map<String, Object> transaction = context.getTransaction();
        String tsn = BeanUtil.getPropString(transaction, Transaction.TSN);
        // success
        updateTransactionPaymentInfo(transaction, resultData);
        List<Map<String, Object>> promotionDetails = (List<Map<String, Object>>) MapUtil.getObject(resultData, GuotongResponseFields.PROMOTION_DETAIL_LIST);
        if (CollectionUtils.isEmpty(promotionDetails)) {
            //无优惠时
            resolvePayOtherAmount(resultData, transaction);
            return;
        }
        // 转换优惠明细为支付信息
        List<Map<String, Object>> payments = promotionDetails.stream()
                .map(this::convertPromotionToPayment)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(payments)) {
            // 计算免充值优惠金额
            long discountMchAmount = payments.stream().filter(payment ->
                            Payment.TYPE_DISCOUNT_SET.contains(MapUtil.getString(payment, Transaction.PAYMENT_TYPE)))
                    .mapToLong(payment -> MapUtil.getLongValue(payment, Payment.AMOUNT)).sum();
            logger.info("tsn={} 免充值优惠金额={}", tsn, discountMchAmount);
            //交易金额
            long totalAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            long paidAmount = totalAmount - discountMchAmount;
            // 更新交易信息
            Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.PAYMENTS, payments);
            transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            resolvePayOtherAmount(resultData, transaction);
        }
    }

    private static void resolvePayOtherAmount(Map<String, Object> resultData, Map<String, Object> transaction) {
        List<Map<String, Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        long paidAmount = BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT);
        if (paidAmount <= 0L) {
            return;
        }
        if (CollectionUtils.isEmpty(payments)) {
            payments = new ArrayList<>();
        }
        String cardType = MapUtil.getString(resultData, GuotongResponseFields.CARD_TYPE);
        String bankName = MapUtil.getString(resultData, GuotongResponseFields.BANK_NAME);
        String bankCode = MapUtil.getString(resultData, GuotongResponseFields.BANK_CODE);
        String bank = getOrDefault(bankCode, bankName);
        String paymentType = RESPONSE_CARD_TYPE_PAYMENT_TYPE_CONFIG.get(cardType);
        if (paymentType == null) {
            paymentType = getDefaultPaymentType(MapUtil.getString(transaction, Transaction.PAYWAY));
        }
        payments.add(
                CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, paymentType,
                        Transaction.PAYMENT_ORIGIN_TYPE, getOrDefault(cardType, bank),
                        Transaction.PAYMENT_AMOUNT, paidAmount
                )
        );
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }

    /**
     * 将单个促销信息转换为支付信息
     * 支持折扣和优惠券两种类型的促销
     */
    protected Map<String, Object> convertPromotionToPayment(Map<String, Object> promotion) {
        String type = MapUtil.getString(promotion, GuotongResponseFields.TYPE);
        String promotionId = MapUtil.getString(promotion, GuotongResponseFields.PROMOTION_ID);
        long mchAmount = MapUtil.getLongValue(promotion, GuotongResponseFields.MERCHANT_CONTRIBUTE);
        long wxpayContribute = MapUtil.getLongValue(promotion, GuotongResponseFields.WXPAY_CONTRIBU);
        long otherContribute = MapUtil.getLongValue(promotion, GuotongResponseFields.OTHER_CONTRIBUTE);
        long channelAmount = wxpayContribute + otherContribute;

        // 根据促销类型创建对应的支付信息
        if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)) {
            return createDiscountPayment(type, promotionId, mchAmount, channelAmount);
        } else if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)) {
            return createCouponPayment(type, promotionId, mchAmount, channelAmount);
        }

        return null;
    }

    /**
     * 创建折扣类型的支付信息
     * 根据商户出资和渠道出资情况创建不同类型的支付信息
     */
    private Map<String, Object> createDiscountPayment(String type, String promotionId, long mchAmount, long channelAmount) {
        if (mchAmount > 0) {
            return createPaymentMap(Payment.TYPE_DISCOUNT_CHANNEL_MCH, type, mchAmount, promotionId);
        } else if (channelAmount > 0) {
            return createPaymentMap(Payment.TYPE_DISCOUNT_CHANNEL, type, channelAmount, promotionId);
        }
        return null;
    }

    /**
     * 创建优惠券类型的支付信息
     * 根据商户出资和渠道出资情况创建不同类型的支付信息
     */
    private Map<String, Object> createCouponPayment(String type, String promotionId, long mchAmount, long channelAmount) {
        if (mchAmount > 0) {
            return createPaymentMap(Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP, type, mchAmount, promotionId);
        } else if (channelAmount > 0) {
            return createPaymentMap(Payment.TYPE_DISCOUNT_CHANNEL, type, channelAmount, promotionId);
        }
        return null;
    }

    /**
     * 创建支付信息映射
     * 包含支付类型、来源类型、金额和促销ID等信息
     */
    private Map<String, Object> createPaymentMap(String paymentType, String originType, long amount, String promotionId) {
        return CollectionUtil.hashMap(
                Transaction.PAYMENT_TYPE, paymentType,
                Transaction.PAYMENT_ORIGIN_TYPE, originType,
                Transaction.PAYMENT_AMOUNT, amount,
                Transaction.PAYMENT_SOURCE, promotionId
        );
    }

    private void setTerminalInfo(Map<String, Object> transaction, GuotongRequestBuilder builder) {
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        builder.set(GuotongBusinessFields.ENCRYPT_RAND_NUM, MapUtil.getString(extendedParams, Transaction.TERMINAL_ENCRYPT_RAND_NUM));
        builder.set(GuotongBusinessFields.SECRET_TEXT, MapUtil.getString(extendedParams, Transaction.TERMINAL_SECRET_TEXT));
    }

    /**
     * 获取是否限制信用卡标识
     *
     * @param transaction
     * @param allow 允许
     * @param deny　禁卡
     * @return
     * @param <T>
     */
    protected static <T> T getLimitCredit(Map transaction, T allow, T deny) {
        // 是否允许信用卡交易
        final String KEY_ALLOW_CREDIT_PAY = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.ALLOW_CREDIT_PAY);
        String allowCreditPay = BeanUtil.getPropString(transaction, KEY_ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE);
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(allowCreditPay)) {
            return deny;
        } else {
            return allow;
        }
    }
}
