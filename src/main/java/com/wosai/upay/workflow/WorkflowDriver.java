package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.eventbus.Event;
import com.wosai.eventbus.EventBus;
import com.wosai.eventbus.EventBus.EventLoopMultiple;
import com.wosai.eventbus.EventListener;
import com.wosai.fsm.State;
import com.wosai.fsm.StateLabel;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.LanguageCaseHolder;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class WorkflowDriver implements InitializingBean, DisposableBean {
    private static final Logger logger = LoggerFactory.getLogger(WorkflowDriver.class);
    private EventBus eventBus;

    @Autowired
    private ExternalServiceFacade facade;

    @Autowired
    private UpayThreadPoolManager threadPoolManager;
    
    private static int DRDS_NODES = 32;
    private static List<EventLoopMultiple> multiples = new ArrayList<EventLoopMultiple>(DRDS_NODES);

    @Autowired
    public WorkflowDriver(EventBus eventBus) {
        this.eventBus = eventBus;
        this.eventBus.subscribe(TransactionStateStartEvent.NAME, new StateStartListener());
        this.eventBus.subscribe(TransactionStateResumeEvent.NAME, new StateResumeListener());
        this.eventBus.subscribe(TransactionStateTransitionEvent.NAME, new StateTransitionListener());
        
        // 计算每个rds用于交易的eventloop起始位置和个数
        if(eventBus.getConcurrency() >= DRDS_NODES) {
            int multiple = eventBus.getConcurrency() / DRDS_NODES;
            int moreEvent = eventBus.getConcurrency() % DRDS_NODES;
            if(multiple > 0) {
                int nextStart = 0;
                for (int idx = 0; idx < DRDS_NODES; idx++) {
                    int end = nextStart + multiple;
                    if(moreEvent > 0) {
                        moreEvent--;
                        end++;
                    }
                    multiples.add(new EventLoopMultiple(nextStart, (end - nextStart) , --end));
                    nextStart = ++end;
                }
            }
        }
    }

    private int getEventKey(TransactionContext context) {
        if(ApolloConfigurationCenterUtil.isEventbusMultiples() && multiples.size() > 0) {
            String id =  BeanUtil.getPropString(context.getTransaction(), Transaction.MERCHANT_ID);
            // 使用drds分片数进行分组，防止某个RDS故障导致所有执行线程出现delay
            int eventKey = id.hashCode() % DRDS_NODES;
            if (eventKey < 0){
              eventKey = -eventKey;
            }
            // 得到eventloop的起始和结束间隔区间
            EventLoopMultiple multiple = multiples.get(eventKey);
            if(multiple.getStart() == multiple.getEnd()) {
                return multiple.getStart();
            }else {
                return multiple.getStart() + (context.getOid().hashCode() % multiple.getCount());
            }
        } else {
            String id = context.getOid();
            int part = id.hashCode() % eventBus.getConcurrency();
            if (part < 0){
                part = -part;
            }
            return part;
        }
    }

    public TransactionContext start(TransactionContext context) {

    	logger.debug("TID {} enqueued start event for transaction context {}", context.getTid(), context);
        TransactionStateStartEvent startEvent = new TransactionStateStartEvent(getEventKey(context), context);
        eventBus.post(startEvent);

        return context;

    }

    public void resume(TransactionContext context) {

        TransactionStateResumeEvent resumeEvent = new TransactionStateResumeEvent(getEventKey(context), context);
        eventBus.post(resumeEvent);

    }

    public void notify(TransactionContext context, Map<String, Object> notification) {
        String result = context.getWorkflow().explainNotification(context, notification);
        eventBus.post(new TransactionStateTransitionEvent(getEventKey(context), context, result));
//        logger.debug("TID {} enqueued state transition event with result {} from notification {} in context {}.", context.getTid(), result, notification, context);
    }

    public void raise(TransactionContext context, String result) {
        eventBus.post(new TransactionStateTransitionEvent(getEventKey(context), context, result));
//        logger.debug("TID {} enqueued state transition event with result {} in context {}.", context.getTid(), result, context);
    }

    private void doStateAction(final int key, final TransactionContext context, final boolean resume) {
        final State state = context.getCurrentState();
        Runnable actionTask = new Runnable() {

            @Override
            public void run() {
                State currentState = context.getCurrentState();
                if(state.getLabel().getId() != currentState.getLabel().getId()){
                    logger.debug("TID {} unable to continue the workflow. maybe the workflow has done , {} -> {}", context.getTid(), state.getLabel().getName(), currentState.getLabel().getName());
                    return;
                }
                //交易语言参数保存在线程上线文中
                LanguageCaseHolder.setLanguageCase(MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT));
                boolean isFinal = state.isEnd();
                try {
                    String result = state.getAction().invoke(context, resume);
                    if (!isFinal) {
                        eventBus.post(new TransactionStateTransitionEvent(key, context, result));
                    }else{
                        context.setShouldReturn(true);
                        context.disappear();
                        logger.debug("TID {} enqueued end event for transaction context {}", context.getTid(), context);
                    }
                }catch(Throwable ex){
                    Map<String,Object> transaction = context.getTransaction();
                    int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
                    if((type == Transaction.TYPE_CANCEL || type == Transaction.TYPE_REFUND) && !UpayUtil.isFormal(transaction) && MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT) != 0) {
                        try {
                            facade.unfreezeWalletBalanceDeduction(MapUtil.getString(transaction, Transaction.MERCHANT_ID), MapUtil.getString(transaction, DaoConstants.ID));
                        } catch (Exception e) {
                            logger.error("upay wallet releaseDeductionLock invoking fail: " + e.getMessage(), e);
                        }
                    }
                    logger.error("TID {} unexpected exception in action. unable to continue the workflow.", context.getTid(), ex);
                    context.setShouldReturn(true);
                    context.setException(ex);
                    context.disappear();
                }finally {
                    LanguageCaseHolder.remove();
                }
            }
        };
        Integer provider = MapUtil.getInteger(context.getOrder(), Order.PROVIDER);
        if (context.isDelayed()) {
            long delay = context.getDelay();
            if (delay < 0){
                context.setRCExpire(true);
                String result = state.getGiveUpResult();
                if (!state.isEnd()) {
                    eventBus.post(new TransactionStateTransitionEvent(key, context, result));
                }
                logger.debug("TID {} give up after max retries and branch out with {}", context.getTid(), result);

            }else{
                threadPoolManager.getExecutor(context).schedule(RunnableWrapper.of(actionTask) , delay, TimeUnit.MILLISECONDS, provider);
                logger.debug("TID {} do action in {} ms", context.getTid(), delay);
            }
        }else{
            if (context.isInproc()) {
                actionTask.run();
                logger.debug("TID {} done action inproc", context.getTid());
            }else{
                threadPoolManager.getExecutor(context).submit(RunnableWrapper.of(actionTask), provider);
                logger.debug("TID {} do action in thread pool.", context.getTid());
            }
        }

    }

    public void branchAndDoStateAction(final int key, final TransactionContext context, String outcome, final boolean resume) {
        State state = context.getCurrentState();
        StateLabel newStateLabel = state.nextState(outcome);
        if(newStateLabel == null){
            logger.debug("TID {} unable to continue the workflow. maybe the state {} is the final state. outcome {}.", context.getTid(), state.getLabel().getName(), outcome);
            return;
        }
        context.setCurrentStateLabel(newStateLabel);
        context.getWorkflow().persistState(context, context.getCurrentState().isEnd());

        doStateAction(key, context, resume);
    }

    class StateStartListener implements EventListener {

        @Override
        public void handle(Event event) {
            TransactionStateStartEvent startEvent = (TransactionStateStartEvent)event;

            TransactionContext context = startEvent.getContext();

            doStateAction(event.getKey(), context, false);
        }
    }
    class StateResumeListener implements EventListener {

        @Override
        public void handle(Event event) {
            TransactionStateResumeEvent resumeEvent = (TransactionStateResumeEvent)event;
            TransactionContext context = resumeEvent.getContext();

            doStateAction(event.getKey(), context, true);
        }

    }
    class StateTransitionListener implements EventListener {

        @Override
        public void handle(Event event) {
            TransactionStateTransitionEvent transitionEvent = (TransactionStateTransitionEvent)event;
            TransactionContext context = transitionEvent.getContext();

//            if (context.getWorkflow().matchState(context)) {
//                branchAndDoStateAction(event.getKey(), context, transitionEvent.getOutcome(), false);
//            }else{
//                // silently ignore state transition whose base state does not match the persistence layer.
//            }
            branchAndDoStateAction(event.getKey(), context, transitionEvent.getOutcome(), false);

        }

    }
    @Override
    public void afterPropertiesSet() throws Exception {
        eventBus.start();
    }

    @Override
    public void destroy() throws Exception {
        eventBus.shutdown();
    }
}

