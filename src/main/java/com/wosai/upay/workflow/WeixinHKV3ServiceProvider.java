package com.wosai.upay.workflow;

import java.util.*;

import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.weixin.hkv3.BusinessFields;
import com.wosai.mpay.api.weixin.hkv3.ProtocolFields;
import com.wosai.mpay.api.weixin.hkv3.RequestBuilder;
import com.wosai.mpay.api.weixin.hkv3.ResponseFields;
import com.wosai.mpay.api.weixin.hkv3.WeixinConstants;
import com.wosai.mpay.api.weixin.hkv3.WeixinHKV3Client;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;

public class WeixinHKV3ServiceProvider extends AbstractServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(WeixinHKV3ServiceProvider.class);
    public static final String NAME = "provider.weixin.hkv3";

    protected static final String KEY_STORE_ClIENT_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_CLIENT_SN);
    protected static final String KEY_STORE_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_SN);

    protected static final String WX_HX3_SCENE_STORE_INFO_ID = String.format("%s.%s", BusinessFields.STORE_INFO, "id");


    private static final Map<String, String> REPLACE_TAGS = MapUtil.hashMap(OP_QUERY, "{out_trade_no}",
            OP_CLOSE, "{out_trade_no}", 
            OP_CANCEL, "{out_trade_no}");
    @Autowired
    private WeixinHKV3Client client;
    protected static int retryTimes = 3;
    protected String notifyHost;

    public WeixinHKV3ServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat(WeixinConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.AMOUNT));

    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if(tradeParams == null 
                || !TransactionParam.WEIXIN_VERSION_V3.equals(MapUtil.getString(tradeParams, TransactionParam.WEIXIN_VERSION))
                || !MapUtil.getBooleanValue(tradeParams, TransactionParam.WEIXIN_V3_OVERSEAS, true)){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_WEIXIN
                && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.WEIXIN_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context, true);
        builder.set(BusinessFields.DESCRIPTION, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_MICROPAY);
        builder.set(BusinessFields.MERCHANT_CATEGORY_CODE, MapUtil.getString(config, TransactionParam.MERCHANT_WECHAT_INDUSTRY));
        builder.set(BusinessFields.PAYER, MapUtil.hashMap(BusinessFields.AUTH_CODE, extraParams.get(Transaction.BARCODE)));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(BusinessFields.TOTAL, transaction.get(Transaction.EFFECTIVE_AMOUNT), BusinessFields.CURRENCY, getTradeCurrency(transaction)));
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        handlerCustomizedSwitch(builder, config);
        carryOverExtendedParams((Map)transaction.get(Transaction.EXTENDED_PARAMS), builder, WeixinConstants.PAY_ALLOWED_FIELDS);

        if (ApolloConfigurationCenterUtil.getIsWeiXinHXSceneInfo()) {
            Map sceneInfo = getSceneInfo(transaction, extended);
            builder.set(BusinessFields.SCENE_INFO, sceneInfo);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction , OP_PAY), WeixinHKV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call weixin pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setTradeNoBuyerInfoIfExists(result, context);
        if (StringUtil.isNotEmpty(code)){
            if(WeixinConstants.MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST.contains(code)){
                return Workflow.RC_IN_PROG;
            } else if(WeixinConstants.MICRO_PAY_RESULT_CODE_FAIL_LIST.contains(code)){
                return Workflow.RC_TRADE_CANCELED;
            } else {
                return Workflow.RC_ERROR;
            }
        }
        String tradeState = MapUtil.getString(result, ResponseFields.TRADE_STATE);
        if (WeixinConstants.TRADE_STATE_CLOSED.equals(tradeState) 
                || WeixinConstants.TRADE_STATE_REVOKED.equals(tradeState)) {
            return Workflow.RC_TRADE_CANCELED;
        } else if(WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState) 
                || WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)
                || WeixinConstants.TRADE_STATE_PAYERROR.equals(tradeState)) {
            return Workflow.RC_IN_PROG;
        } else if(WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
            //付款成功
            resolvePayFund(result, context, ResponseFields.DETAIL);
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }
    
    @Override
    public String cancel(TransactionContext context) {
        int subPayway = MapUtil.getIntValue(context.getOrder(), Order.SUB_PAYWAY);
        if (subPayway == Order.SUB_PAYWAY_BARCODE) {
            return doCancel(context);
        } else {
            return doClose(context);
        }
    }

    private String doCancel(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context, false);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction , OP_CANCEL), WeixinHKV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call weixin cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);//http返回状态码
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        if (StringUtil.isNotEmpty(code)) {
            if (WeixinConstants.CODE_SYSTEM_ERROR.equals(code)
                    || WeixinConstants.CODE_INVALID_REQUEST.equals(code)) {
                return Workflow.RC_RETRY;
            } else {
                if (WeixinConstants.CODE_REVERSE_EXPIRE.equals(code)) {
                    return cancelToRefund(context);
                }
                return Workflow.RC_ERROR;
            }
        } else if (HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE != httpCode){
            return Workflow.RC_ERROR;
        }
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        return Workflow.RC_CANCEL_SUCCESS;
    }

    private String doClose(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context, false);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction , OP_CLOSE), WeixinHKV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call weixin cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        int httpCode = MapUtil.getIntValue(result, HttpConstant.HTTP_CODE);//http返回状态码
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        if (StringUtil.isNotEmpty(code)) {
            if (WeixinConstants.CODE_SYSTEM_ERROR.equals(code)
                    || WeixinConstants.CODE_INVALID_REQUEST.equals(code)) {
                return Workflow.RC_RETRY;
            } else {
                if (WeixinConstants.CODE_ORDERPAID.equals(code)) {
                    return cancelToRefund(context);
                }
                return Workflow.RC_ERROR;
            }
        } else if (HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE != httpCode){
            return Workflow.RC_ERROR;
        }
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        return Workflow.RC_CANCEL_SUCCESS;
    }

    private String cancelToRefund(TransactionContext context) {
        String resultRc = doRefund(context, OP_CANCEL, MapUtil.getLongValue(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT));
        return Workflow.RC_REFUND_SUCCESS.equals(resultRc) ? Workflow.RC_CANCEL_SUCCESS : Workflow.RC_ERROR;
    }

    private String doRefund(TransactionContext context, String operation, long refundFee) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context, true);
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.OUT_REFUND_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(BusinessFields.REFUND, transaction.get(Transaction.EFFECTIVE_AMOUNT),
                        BusinessFields.TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                        BusinessFields.CURRENCY, getTradeCurrency(transaction)
                ));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction , OP_REFUND), WeixinHKV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call weixin refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常进行重试
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, operation);
        if (StringUtil.isNotEmpty(code)) {
            if (WeixinConstants.CODE_SYSTEM_ERROR.equals(code) 
                    || WeixinConstants.CODE_FREQUENCY_LIMITED.equals(code)) {
                return Workflow.RC_RETRY;
            } else {
                return Workflow.RC_ERROR;
            }
        }
        //退款成功
        long channelFinishTime = parseTimeString(MapUtil.getString(result, ResponseFields.CREATE_TIME));
        transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
        transaction.put(Transaction.TRADE_NO, result.get(ResponseFields.ID));
        resolveRefundFund(result, context);
        return Workflow.RC_REFUND_SUCCESS;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> result = doQuery(context);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        if(WeixinConstants.CODE_SYSTEM_ERROR.equals(code)){
            //通讯标识为失败，重新查询
            return Workflow.RC_IN_PROG;
        } else if(StringUtil.isNotEmpty(code)){
            return Workflow.RC_ERROR;
        }
        String tradeState = (String) result.get(ResponseFields.TRADE_STATE);
        String rcFlag = Workflow.RC_ERROR;
        if (WeixinConstants.TRADE_STATE_USERPAYING.equals(tradeState)){
            rcFlag = Workflow.RC_IN_PROG;
        } else if(WeixinConstants.TRADE_STATE_NOTPAY.equals(tradeState)){
            //跟微信的人确认过，b2c下，not_pay表明用户已经取消了付款。
            rcFlag = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
        } else if(WeixinConstants.TRADE_STATE_SUCCESS.equals(tradeState)){
            rcFlag = Workflow.RC_PAY_SUCCESS;
            //付款成功
            if(MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT){
                resolvePayFund(result, context, ResponseFields.PROMOTION_DETAIL);
            }
        }
        return rcFlag;
    }
    
    protected Map<String,Object> doQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context, false);
        try {
            return retryIfNetworkException(getUrl(transaction , OP_QUERY), WeixinHKV3Client.METHOD_GET, config, builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin query", ex);
            return null;
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long refundFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        return doRefund(context, OP_REFUND, refundFee);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context, true);
        builder.set(BusinessFields.DESCRIPTION, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        long timeStart = System.currentTimeMillis();
        long timeEnd = System.currentTimeMillis() + DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
        builder.set(BusinessFields.TIME_START, formatTimeString(timeStart));
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(timeEnd));
        builder.set(BusinessFields.MERCHANT_CATEGORY_CODE, MapUtil.getString(config, TransactionParam.MERCHANT_WECHAT_INDUSTRY));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(BusinessFields.TOTAL, transaction.get(Transaction.EFFECTIVE_AMOUNT), BusinessFields.CURRENCY, getTradeCurrency(transaction)));
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        handlerCustomizedSwitch(builder, config);
        carryOverExtendedParams((Map)transaction.get(Transaction.EXTENDED_PARAMS), builder, WeixinConstants.PRECREATE_ALLOWED_FIELDS);

        if (ApolloConfigurationCenterUtil.getIsWeiXinHXSceneInfo()) {
            Map sceneInfo = getSceneInfo(transaction, extended);
            builder.set(BusinessFields.SCENE_INFO, sceneInfo);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getUrl(transaction , OP_PRECREATE), WeixinHKV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call weixin precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if (StringUtil.isNotEmpty(code)){
            return Workflow.RC_ERROR;
        }
        //预下单成功
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.QRCODE, result.get(ResponseFields.CODE_URL));
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(Transaction.TYPE_PAYMENT != type){
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    protected  void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        // 支付者信息
        Map<String, Object> payer = MapUtil.getMap(result, ResponseFields.PAYER);

        String timeEnd = MapUtil.getString(result, ResponseFields.SUCCESS_TIME);
        if(StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))){
            String subOpenId = MapUtil.getString(payer, ResponseFields.SUB_OPEN_ID);
            if(StringUtil.isNotEmpty(subOpenId)){
                transaction.put(Transaction.BUYER_UID, subOpenId);
            }

        }
        if(StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))){
            String openId = MapUtil.getString(payer, ResponseFields.SP_OPENID);
            if(StringUtil.isNotEmpty(openId)){
                transaction.put(Transaction.BUYER_LOGIN, openId);
            }
        }
        if(StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))){
            String tradeNo = MapUtil.getString(result, ResponseFields.ID);
            if(StringUtil.isNotEmpty(tradeNo)){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
        if(StringUtil.isNotEmpty(timeEnd)){
            if(StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.CHANNEL_FINISH_TIME))){
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(timeEnd));
            }
        }
    }

    /**
     * 解析返回金额相关信息
     * @param context
     */
    private void resolvePayFund(Map<String, Object> result, TransactionContext context, String key){
        if(result == null){
            return;
        }
        Map<String, Object> amount = MapUtil.getMap(result, ResponseFields.AMOUNT);
        Map<String,Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        List<Map<String,Object>> details = (List<Map<String, Object>>) MapUtil.getObject(result, ResponseFields.DETAIL);
        List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if(payments == null || payments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(result));
        }
        if(CollectionUtil.isNotEmpty(details)){
            long discountAmount = getSumAmountOfPromotionDetail(details);
            if(MapUtil.getLongValue(order, Order.TOTAL_DISCOUNT, 0) == 0l && discountAmount != 0){
                order.put(Order.TOTAL_DISCOUNT, discountAmount);
                order.put(Order.NET_DISCOUNT, discountAmount);
            }
            long total = MapUtil.getLongValue(amount, ResponseFields.TOTAL, 0);
            long paidAmount = total - discountAmount;
            if(MapUtil.getLongValue(transaction, Transaction.PAID_AMOUNT) == 0){
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }
            long receiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            for(Map<String,Object> promotion: details){
                if(promotion.isEmpty()){
                    continue;
                }
                String type = MapUtil.getString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                long detailAmount = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                long wxpayContribute = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_WECHATPAY_CONTRIBUTE_AMOUNT);
                long otherContribute = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE_AMOUNT);
                //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                if(DirectWeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                    receiveAmount = receiveAmount - (detailAmount - wxpayContribute -otherContribute);

                }
            }
            if(MapUtil.getLongValue(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
            }
        }else{
            long total = MapUtil.getLongValue(amount, ResponseFields.TOTAL, 0);
            transaction.put(Transaction.PAID_AMOUNT, total);
            transaction.put(Transaction.RECEIVED_AMOUNT, total);
            long discount = total - total;
            if(MapUtil.getLongValue(order, Order.TOTAL_DISCOUNT, 0) == 0l && discount != 0){
                order.put(Order.TOTAL_DISCOUNT, discount);
                order.put(Order.NET_DISCOUNT, discount);
            }
        }
        // 设置境外支付信息
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        // 订单金额信息
        String payerCurrency = MapUtil.getString(amount, ResponseFields.PAYER_CURRENCY);
        // 汇率信息
        Map<String, Object> exchangeRate = MapUtil.getMap(amount, ResponseFields.EXCHANGE_RATE);
        Integer rate = MapUtil.getInteger(exchangeRate, ResponseFields.RATE);
        Long payerTotal = MapUtil.getLong(amount, ResponseFields.PAYER_TOTAL);
        if (!StringUtil.isEmpty(payerCurrency)) {
            overseas.put(Transaction.CURRENCY, payerCurrency);
        }
        if (payerTotal != null) {
            overseas.put(Transaction.PAYER_AMOUNT, payerTotal);
        }
        if (rate != null){
            overseas.put(Transaction.EXCHANGE_RATE, rate);
        }
    }
    
    @SuppressWarnings("unchecked")
    private List<Map<String,Object>> getWeixinPayments(Map<String,Object> result){
        List<Map<String, Object>> details = (List<Map<String, Object>>) MapUtil.getObject(result, ResponseFields.DETAIL);
        if(CollectionUtil.isNotEmpty(details)){
            Map<String, Object> amount = MapUtil.getMap(result, ResponseFields.AMOUNT);
            long total = MapUtil.getLongValue(amount, ResponseFields.TOTAL, 0);
            long cashFee = total - getSumAmountOfPromotionDetail(details);
            String bankType = MapUtil.getString(result, ResponseFields.BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = getWeixinPaymentByBanktype(bankType, cashFee);
            if(payment != null){
                payments.add(payment);
            }
            for(Map<String,Object> promotion: details){
                if(promotion.isEmpty()){
                    continue;
                }
                String type = MapUtil.getString(promotion, ResponseFields.PROMOTION_DETAIL_TYPE);
                String promotionId = MapUtil.getString(promotion, ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                long detailAmount = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                long wxpayContribute = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_WECHATPAY_CONTRIBUTE_AMOUNT);
                long otherContribute = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE_AMOUNT);
                long channelAmount = wxpayContribute + otherContribute;
                long mchAmount = detailAmount - channelAmount;
                //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                if(DirectWeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                    if(mchAmount > 0){
                        payments.add(
                                MapUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, mchAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }else if(channelAmount > 0){
                        payments.add(
                                MapUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, channelAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }
                }else if(DirectWeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)){
                    if(mchAmount > 0){
                        payments.add(
                                MapUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, mchAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }else if(channelAmount > 0){
                        payments.add(
                                MapUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, channelAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }

                }
            }
            return  payments;
        }else{
            Map<String, Object> amount = MapUtil.getMap(result, ResponseFields.AMOUNT);
            long total = MapUtil.getLongValue(amount, ResponseFields.TOTAL, 0);
            long payerTotal = MapUtil.getLongValue(amount, ResponseFields.TOTAL, 0);
            long discount = total - payerTotal;
            String banktype = MapUtil.getString(result, ResponseFields.BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = getWeixinPaymentByBanktype(banktype, payerTotal);
            if(payment != null){
                payments.add(payment);
            }
            if(discount > 0){
                payments.add(
                        MapUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }
            return payments;
        }

    }
    
    /**
     * 计算微信总的优惠信息
     * @param promotions
     * @return
     */
    private long getSumAmountOfPromotionDetail(List<Map<String, Object>> promotions){
        long sum = 0;
        if(promotions != null){
            for(Map<String,Object> promotion: promotions){
                if(promotion.isEmpty()){
                    continue;
                }
                long amount = MapUtil.getLongValue(promotion, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                sum = sum + amount;
            }
        }
        return sum;
    }

    private Map<String,Object> getWeixinPaymentByBanktype(String banktype, long amount){
        if(amount  <= 0 || StringUtil.isEmpty(banktype)){
            return null;
        }else{
            String type = null;
            if(DirectWeixinServiceProvider.WEIXIN_PAYMENT_WALLET_ORIGIN_TYPE.equals(banktype)){
                type = Payment.TYPE_WALLET_WEIXIN;
            }else if(banktype.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_CREDIT_SUFFIX)){
                type = Payment.TYPE_BANKCARD_CREDIT;
            }else if(banktype.endsWith(DirectWeixinServiceProvider.WEIXIN_PAYMENT_BANKCARD_DEBIT_SUFFIX)){
                type = Payment.TYPE_BANKCARD_DEBIT;
            }else if(!StringUtil.isEmpty(banktype)){
                type = banktype.toUpperCase();
            }
            return MapUtil.hashMap(
                    Transaction.PAYMENT_TYPE, type,
                    Transaction.PAYMENT_ORIGIN_TYPE, banktype,
                    Transaction.PAYMENT_AMOUNT, amount
            );
        }
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String code = (String)result.get(ResponseFields.CODE);//返回状态码
        String message = (String)result.get(ResponseFields.MESSAGE);//返回状态码
        Integer httpCode = MapUtil.getInteger(result, HttpConstant.HTTP_CODE);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        boolean isSuccess = com.wosai.pantheon.util.StringUtil.isEmpty(code) || (OP_CANCEL.equals(key) && httpCode == HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE);
        map.put(ResponseFields.CODE, isSuccess ? WeixinConstants.RESULT_CODE_SUCCESS : code);
        map.put(ResponseFields.MESSAGE, message);
        map.put(HttpConstant.HTTP_CODE, httpCode);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, code, message);
    }

    /**
     * 获取默认的requestBuilder，设置请求默认值。
     * @param context
     * @return
     */
    protected RequestBuilder getDefaultRequestBuilder(TransactionContext context, boolean sendAppid){
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        if (sendAppid) {
            builder.set(ProtocolFields.SP_APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
            builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.WEIXIN_SUB_APP_ID));
        }
        builder.set(ProtocolFields.SP_MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        return builder;
    }

    protected Map<String, Object> retryIfNetworkException(String url, String method, Map<String, Object> config , Map<String,Object> request, int times, String logFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                String mchId = MapUtil.getString(config, TransactionParam.WEIXIN_MCH_ID);
                String serialNo = MapUtil.getString(config, TransactionParam.WEIXIN_SERIAL_NO);
                String signKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.WEIXIN_CERT_CONFIG_KEY));
                return client.call(url, method, mchId, serialNo, signKey, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in weixin {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    /**
     * 解析退款返回金额信息
     * @param result
     * @param context
     */
    private void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        Map<String,Object> order = context.getOrder();
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String,Object> overseas = (Map<String, Object>) extraOutFields.get(Transaction.OVERSEAS);
        if(null == overseas){
            overseas = new HashMap<>();
            extraOutFields.put(Transaction.OVERSEAS, overseas);
        }
        // 订单金额信息
        Map<String, Object> amount = MapUtil.getMap(result, ResponseFields.AMOUNT);
        String payerCurrency = MapUtil.getString(amount, ResponseFields.PAYER_CURRENCY);
        Long payerTotal = MapUtil.getLong(amount, ResponseFields.PAYER_REFUND);
        Long rate = MapUtil.getLong(MapUtil.getMap(amount, ResponseFields.EXCHANGE_RATE), ResponseFields.RATE);
        if (!StringUtil.isEmpty(payerCurrency)) {
            overseas.put(Transaction.CURRENCY, payerCurrency);
        }
        if (payerTotal != null) {
            overseas.put(Transaction.PAYER_AMOUNT, payerTotal);
        }
        if (rate != null) {
            overseas.put(Transaction.EXCHANGE_RATE, rate);
        }
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if(MapUtil.getLongValue(order, Order.ORIGINAL_TOTAL) == MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            long payerRefund = MapUtil.getLongValue(result, ResponseFields.PAYER_REFUND);
            if(MapUtil.getLongValue(transaction, Transaction.PAID_AMOUNT) == 0 && payerRefund != 0){
                transaction.put(Transaction.PAID_AMOUNT, payerRefund);
            }

            if(payments != null){
                List<Map<String,Object>> refundPayments = new ArrayList<>();
                for (int i = 0; i < payments.size(); i++) {
                    Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                    refundPayments.add(refundPayment);
                }
                List<Map<String, Object>> details = (List<Map<String, Object>>) MapUtil.getObject(result, ResponseFields.DETAIL);
                if (CollectionUtil.isNotEmpty(details)) {
                    for (Map<String, Object> detail : details) {
                        String couponRefundId = MapUtil.getString(detail, ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                        String couponRefundFee = MapUtil.getString(detail, ResponseFields.PROMOTION_DETAIL_AMOUNT);
                        for (int j = 0; j < refundPayments.size(); j++) {
                            Map<String,Object> refundPayment = refundPayments.get(j);
                            String sourceId = MapUtil.getString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                            if(sourceId.equals(couponRefundId)){
                                refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                            }
                        }
                    }
                    
                }
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = MapUtil.getString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_WALLET_WEIXIN.equals(type)
                            || Payment.TYPE_BANKCARD_CREDIT.equals(type) 
                            || Payment.TYPE_BANKCARD_DEBIT.equals(type)){
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, payerRefund);
                        break;
                    }
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                long settlementRefundFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String,Object> refundPayment = refundPayments.get(j);
                    String type = MapUtil.getString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)){
                        settlementRefundFee = settlementRefundFee - MapUtil.getLongValue(refundPayment, Transaction.PAYMENT_AMOUNT);
                    }
                }
                if(MapUtil.getLongValue(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefundFee != 0){
                    transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefundFee);
                }
            }

        }
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key) || !allowedFields.contains(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.set(key, value);
            }
        }
    }

    protected Map getSceneInfo(Map<String, Object> transaction, Map<String, Object> extended) {
        Map sceneInfoObj = MapUtil.getMap(extended, BusinessFields.SCENE_INFO);
        if (MapUtil.isEmpty(sceneInfoObj)) {
            sceneInfoObj = new HashMap<>();
        }
        String extendStoreId = (String) BeanUtil.getNestedProperty(sceneInfoObj, WX_HX3_SCENE_STORE_INFO_ID);
        if (Objects.isNull(extendStoreId) || extendStoreId.length() == 0) {
            String storeClientSn = BeanUtil.getPropString(transaction, KEY_STORE_ClIENT_SN);
            if (StringUtil.isNotEmpty(storeClientSn)) {
                storeClientSn = storeClientSn.substring(0, Math.min(32, storeClientSn.length()));
                BeanUtil.setNestedProperty(sceneInfoObj, WX_HX3_SCENE_STORE_INFO_ID, storeClientSn);
            } else {
                String storeSn = BeanUtil.getPropString(transaction, KEY_STORE_SN);
                BeanUtil.setNestedProperty(sceneInfoObj, WX_HX3_SCENE_STORE_INFO_ID, storeSn);
            }
        }
        return sceneInfoObj;
    }


    protected void handlerCustomizedSwitch(RequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY,TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_PAY, WeixinConstants.NO_CREDIT);
        }
    }

    @Override
    protected int getNotifyUrlLimit() {
        return 100;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    protected String getUrl(Map<String, Object> transaction, String op) {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), op);
        if (REPLACE_TAGS.containsKey(op)) {
            return url.replace(REPLACE_TAGS.get(op), MapUtil.getString(transaction, Transaction.ORDER_SN));
        }
        return url;
    }
}
