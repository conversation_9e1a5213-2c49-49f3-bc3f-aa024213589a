package com.wosai.upay.workflow;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;

import com.wosai.constant.UpayConstant;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.bestpay.BestpayClient;
import com.wosai.mpay.api.bestpay.BestpayConstants;
import com.wosai.mpay.api.bestpay.BusinessFields;
import com.wosai.mpay.api.bestpay.ProtocolFields;
import com.wosai.mpay.api.bestpay.RequestBuilder;
import com.wosai.mpay.api.bestpay.ResponseFields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.BestpaySignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

public class BestpayServiceProvider extends AbstractServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(BestpayServiceProvider.class);

    private String notifyHost;
    public static final String NAME = "provider.bestpay";
    @Autowired
    private BestpayClient client;

    public BestpayServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(BestpayConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.ORDER_AMT, BusinessFields.PRODUCT_AMT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.PAYWAY_BESTPAY == MapUtil.getIntValue(transaction, Transaction.PAYWAY)
                && (Order.SUB_PAYWAY_BARCODE == subPayway || Order.SUB_PAYWAY_WAP == subPayway)) {
            Map tradeParams = getTradeParams(transaction);
            if(tradeParams != null && !TransactionParam.SIGN_TYPE_RSA.equals(MapUtil.getString(tradeParams, TransactionParam.SIGN_TYPE))){
                return true;
            }
        }
        return false;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);

        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(ProtocolFields.BACK_URL, notifyUrl);
        }
        builder.set(BusinessFields.BARCODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        builder.set(BusinessFields.ORDER_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.ORDER_REQ_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.CHANNEL, BestpayConstants.CHANNEL);
        builder.set(BusinessFields.BUSI_TYPE, BestpayConstants.BUSI_TYPE);
        builder.set(BusinessFields.ORDER_DATE, formatTimeString(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
        builder.set(BusinessFields.ORDER_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.PRODUCT_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.ATTACH_AMT, "0");
        SetSubMchIdAndLedgerDetail(builder, config, transaction);
        String storeId = BeanUtil.getPropString(config, TransactionParam.BESTPAY_STORE_ID);
        if(StringUtil.empty(storeId)){
        	storeId = BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID);
        }
        builder.set(BusinessFields.STORE_ID, storeId); //storeId 必传
        builder.set(BusinessFields.GOODS_NAME, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        carryOverExtendedParams(extended, builder);
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), (String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY), BestpayConstants.METHOD_BSC_PAY, builder.build());
            setTransactionContextErrorInfo(result, context, OP_PAY);
            if (context.getApiVer() == 1) {
            	transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call bestpay pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in bestpay pay", ex);
            if(ex instanceof MpayApiSendError || ex instanceof MpayApiReadError){
            	return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }

        boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseFields.SUCCESS);
        if(!isSuccess){
            String errorCode = BeanUtil.getPropString(result, ResponseFields.ERROR_CODE);
            if(BestpayConstants.ERROR_CODE_REMOTE_SERVICE_INVOKE_FAIL.equals(errorCode)
                    || BestpayConstants.ERROR_CODE_BARCODE_VALID_ERROR.equals(errorCode)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_PROTOCOL_ERROR;
        }
        String transStatus = (String) BeanUtil.getNestedProperty(result, ResponseFields.RESPONSE_KEY_TRANS_STATUS);
        if(BestpayConstants.TRANS_STATUS_TRADE.equalsIgnoreCase(transStatus)){
        	Map<String,Object> resultInfo = (Map<String, Object>) result.get(ResponseFields.RESULT);
            //付款成功
            transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(resultInfo, ResponseFields.PAYER_ACCOUNT));
            transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(resultInfo, ResponseFields.TRANS_PHONE));
            transaction.put(Transaction.TRADE_NO,  BeanUtil.getPropString(resultInfo, ResponseFields.OUR_TRANS_NO));
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            resolveFunds(resultInfo , context);
            return Workflow.RC_PAY_SUCCESS;
        }else if(BestpayConstants.TRANS_STATUS_INPROG.equalsIgnoreCase(transStatus)){
            return Workflow.RC_IN_PROG;
        }else if(BestpayConstants.TRANS_STATUS_FAIL.equalsIgnoreCase(transStatus)){
            Map<String,Object> resultInfo = (Map<String, Object>) result.get(ResponseFields.RESULT);
            String respCode = BeanUtil.getPropString(resultInfo, ResponseFields.RESP_CODE);
            if(BestpayConstants.RESPONSE_CODE_BALANCE_NOT_ENOUGH.equals(respCode)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_ERROR;
        }else {
            return Workflow.RC_ERROR;
        }

    }



    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.MERCHANT_PWD, (String)config.get(TransactionParam.BESTPAY_MERCHANT_PWD));
        builder.set(BusinessFields.OLD_ORDER_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.OLD_ORDER_REQ_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.REFUND_REQ_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessFields.REFUND_REQ_DATE, DateUtil.formatDate(new Date(), DateUtil.FORMATTER_DATE_INT));
        builder.set(BusinessFields.TRANS_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CHANNEL, BestpayConstants.CHANNEL);
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), (String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY), BestpayConstants.METHOD_CANCEL, builder.build());
            setTransactionContextErrorInfo(result, context, OP_CANCEL);
        } catch (MpayException ex) {
            logger.error("failed to call bestpay cancel", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
        	logger.error("encountered ioex in bestpay cancel", ex);
        	if(ex instanceof MpayApiSendError || ex instanceof MpayApiReadError){
            	return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }

        Boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseFields.SUCCESS, false);
        if(isSuccess 
        		|| BestpayConstants.ERROR_CODE_DUPLICATE_CANCEL.equals(BeanUtil.getPropString(result, ResponseFields.ERROR_CODE))
        		|| BestpayConstants.ERROR_CODE_ORDER_NOT_EXISTED.equals(BeanUtil.getPropString(result, ResponseFields.ERROR_CODE))){
            return Workflow.RC_CANCEL_SUCCESS;
        }else{
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.ORDER_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.ORDER_REQ_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        if(Transaction.TYPE_REFUND == BeanUtil.getPropInt(transaction, Transaction.TYPE)){
        	builder.set(BusinessFields.ORDER_REQ_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        }
        builder.set(BusinessFields.ORDER_DATE, DateUtil.formatDate(new Date(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)), DateUtil.FORMATTER_DATE_INT));
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), (String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY), BestpayConstants.METHOD_QUERY, builder.build());
            setTransactionContextErrorInfo(result, context, OP_QUERY);
        } catch (MpayException ex) {
            logger.error("failed to call bestpay query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
        	if(ex instanceof MpayApiSendError || ex instanceof MpayApiReadError){
            	return Workflow.RC_IN_PROG;
            }
            logger.error("encountered ioex in bestpay query", ex);
            return Workflow.RC_IOEX;
        }
        
        if(!BeanUtil.getPropBoolean(result, ResponseFields.SUCCESS,false)){
            return Workflow.RC_ERROR;
        }
        
        String transStatus = (String) BeanUtil.getNestedProperty(result, ResponseFields.RESPONSE_KEY_TRANS_STATUS);
        if(BestpayConstants.TRANS_STATUS_TRADE.equalsIgnoreCase(transStatus)){
        	// 0代表没有退款  1 已退款 2部分退款 3已冲正
        	int refundFlag = BeanUtil.getPropInt(result, String.format("%s.%s", ResponseFields.RESULT, ResponseFields.REFUND_FLAG));
        	if(refundFlag == 0){
	        	Map<String,Object> resultInfo = (Map<String, Object>) result.get(ResponseFields.RESULT);
	            //付款成功
	            transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(resultInfo, ResponseFields.PAYER_ACCOUNT));
	            transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(resultInfo, ResponseFields.CUSTOMER_ID));
	            transaction.put(Transaction.TRADE_NO,  BeanUtil.getPropString(resultInfo, ResponseFields.CUSTOMER_ID));
	            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
	            resolveFunds(resultInfo , context);
	            return Workflow.RC_PAY_SUCCESS;
        	}
        }else if(BestpayConstants.TRANS_STATUS_INPROG.equalsIgnoreCase(transStatus)){
        	return Workflow.RC_IN_PROG;
        }
    
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(BusinessFields.MERCHANT_PWD, (String)config.get(TransactionParam.BESTPAY_MERCHANT_PWD));
        builder.set(BusinessFields.OLD_ORDER_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.OLD_ORDER_REQ_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.REFUND_REQ_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessFields.REFUND_REQ_DATE, DateUtil.formatDate(new Date(), DateUtil.FORMATTER_DATE_INT));
        builder.set(BusinessFields.TRANS_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CHANNEL, BestpayConstants.CHANNEL);
        SetSubMchIdAndLedgerDetail(builder, config, transaction);
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), (String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY), BestpayConstants.METHOD_REFUND, builder.build());
            setTransactionContextErrorInfo(result, context, OP_REFUND);
            if (context.getApiVer() == 1) {
            	transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call bestpay refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
        	if(ex instanceof MpayApiConnectError || ex instanceof MpayApiSendError){
            	return Workflow.RC_RETRY;
            }
            logger.error("encountered ioex in bestpay refund", ex);
            return Workflow.RC_IOEX;
        }

        Boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseFields.SUCCESS,false);
        if(!isSuccess && !BestpayConstants.ERROR_CODE_REFUNDED.equals(BeanUtil.getPropString(result, ResponseFields.ERROR_CODE))){
            return Workflow.RC_SYS_ERROR;
        }else {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            return Workflow.RC_REFUND_SUCCESS;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
    	Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI){
            return wapPrecreate(context, resume);
        }else{
            return Workflow.RC_SYS_ERROR;
        }
        
        
    }

    private String wapPrecreate(TransactionContext context, boolean resume) {
    	Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);

        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.MERCHANT_ID.toUpperCase(), BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID));
        builder.set(BusinessFields.ORDER_SEQ, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.ORDER_REQ_TRANSEQ, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessFields.ORDER_REQ_TIME, formatTimeString(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
        builder.set(BusinessFields.TRANS_CODE, BestpayConstants.TRANS_CODE_01);
        builder.set(BusinessFields.ORDER_AMT.toUpperCase(), transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        builder.set(BusinessFields.PRODUCT_ID, BestpayConstants.PRODUCT_ID_04);
        builder.set(BusinessFields.PRODUCT_DESC, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessFields.ENCODE_TYPE, BestpayConstants.ENCODE_TYPE_1);
        SetSubMchIdAndLedgerDetail(builder, config, transaction);
        String notifyUrl = getNotifyUrl(notifyHost, context);
        builder.set(BusinessFields.BG_URL, notifyUrl);
        
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP), (String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY), BestpayConstants.METHOD_WAP_PAY, builder.build());
            String success = BeanUtil.getPropString(result, ResponseFields.SUCCESS);
            if(!"00".equals(success)){
                result.put(ResponseFields.SUCCESS, false);
            	result.put(ResponseFields.ERROR_CODE, success);
            	result.put(ResponseFields.ERROR_MSG, BeanUtil.getPropString(result, ResponseFields.MESSAGE));
            	setTransactionContextErrorInfo(result, context, OP_PRECREATE);
            	return Workflow.RC_PROTOCOL_ERROR;
            }
            
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            Map wapPayRequest = CollectionUtil.hashMap(BusinessFields.SERVICE, "mobile.security.pay",
            		BusinessFields.MERCHANT_ID.toUpperCase(), BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID),
            		BusinessFields.MERCHANT_PWD.toUpperCase(), (String)config.get(TransactionParam.BESTPAY_MERCHANT_PWD),
            		BusinessFields.BACK_MERCHANT_URL, notifyUrl,
                    BusinessFields.SIGN_TYPE, "MD5",
                    BusinessFields.ORDER_SEQ.toUpperCase(), BeanUtil.getPropString(transaction, Transaction.ORDER_SN),
                    BusinessFields.ORDER_REQ_TRANSEQ.toUpperCase(), BeanUtil.getPropString(transaction, Transaction.TSN),
                    BusinessFields.ORDER_TIME, formatTimeString(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)),
                    BusinessFields.ORDER_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)),
                    BusinessFields.CURTYPE, "RMB",
                    BusinessFields.PRODUCT_ID.toUpperCase(), BestpayConstants.PRODUCT_ID_04,
                    BusinessFields.PRODUCT_DESC.toUpperCase(), BeanUtil.getPropString(transaction, Transaction.SUBJECT),
                    BusinessFields.PRODUCT_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)),
                    BusinessFields.ATTACH_AMOUNT, "0",
                    BusinessFields.BUSI_TYPE.toUpperCase(), "04",
                    BusinessFields.SWTICH_ACC, "true",
                    BusinessFields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT),
                    BusinessFields.OTHER_FLOW, "01",
                    BusinessFields.CUSTOMER_ID, BeanUtil.getPropString(transaction, KEY_PAYER_UID));
            
            wapPayRequest.put(ProtocolFields.SIGN, BestpaySignature.getSign(wapPayRequest, (String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY), BestpayClient.getSignatureColumns(BestpayConstants.METHOD_WAP_H5_PAY)));
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapPayRequest);

            return Workflow.RC_CREATE_SUCCESS;
        } catch (MpayException ex) {
            logger.error("failed to call bestpay pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in bestpay pay", ex);
            return Workflow.RC_IOEX;
        }
	}
	@Override
    public String explainNotification(Map<String, Object> providerNotification) {
		TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(Transaction.TYPE_PAYMENT == type){
            return Workflow.RC_PAY_SUCCESS.equals(query(context))?Workflow.RC_PAY_SUCCESS:null;
        }
		return null;
    }


    @Override
    public Map<String,Object> getTradeParams(Map<String, Object> transaction){
        return getTradeParams(transaction, TransactionParam.BESTPAY_TRADE_PARAMS);
    }



    private void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key){
        boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseFields.SUCCESS);
        Map<String, Object> resResult = (Map<String, Object>) result.get(ResponseFields.RESULT);
        String transStatus = (String) BeanUtil.getNestedProperty(resResult, ResponseFields.TRANS_STATUS);
        String detailErrorCode = BeanUtil.getPropString(result, ResponseFields.ERROR_CODE) == null ? BeanUtil.getPropString(resResult, ResponseFields.RESP_CODE) : BeanUtil.getPropString(result, ResponseFields.ERROR_CODE);
        String detailErrorDes = BeanUtil.getPropString(result, ResponseFields.ERROR_MSG) == null ? BeanUtil.getPropString(resResult, ResponseFields.RESP_DESC) : BeanUtil.getPropString(result, ResponseFields.ERROR_MSG);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.SUCCESS, isSuccess);
        map.put(ResponseFields.ERROR_CODE, detailErrorCode);
        map.put(ResponseFields.ERROR_MSG, detailErrorDes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess && (StringUtil.empty(transStatus) || BestpayConstants.TRANS_STATUS_TRADE.equalsIgnoreCase(transStatus)), detailErrorCode, detailErrorDes);
    }

    /**
     * 解析返回金额相关信息
     * @param context
     */
    private void resolveFunds(Map<String, Object> resultInfo, TransactionContext context) {
    	String coupon = BeanUtil.getPropString(resultInfo, ResponseFields.COUPON);			// 订单优惠金 额，用户使代订单优惠金 额
        if(!StringUtils.isEmpty(coupon)){
        	context.getTransaction().put(Transaction.PAID_AMOUNT, BeanUtil.getPropLong(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT) - new Long(coupon));
        	context.getTransaction().put(Transaction.RECEIVED_AMOUNT, BeanUtil.getPropLong(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT) - new Long(coupon));
        	
        	context.getOrder().put(Order.TOTAL_DISCOUNT, new Long(coupon));
        	context.getOrder().put(Order.NET_DISCOUNT, new Long(coupon));
        }
    }


    public  RequestBuilder getDefaultRequestBuilder (TransactionContext context) {
    	RequestBuilder builder = new RequestBuilder();
    	Map config = getTradeParams(context.getTransaction());
        builder.set(BusinessFields.MERCHANT_ID, BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID));
        return builder;
    }

    public void resolveNofityPayFunds(Map<String, Object> transaction, Map<String, Object> payToolsPayAmount){

    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }


    private void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            //商品详情列表，可做单品营销使用（非必传）
            if(UpayConstant.GOODS_DETAIL.equals(key) ){
                String value = JsonUtil.toJsonStr(extendedParam.getValue());
                if (value != null) {
                    builder.set(UpayConstant.BESTPAY_GOODS_DETAIL, value);
                }
            }else{
                Object value = extendedParam.getValue();
                if (value != null) {
                    builder.set(key, value.toString());
                }
            }
        }
    }

    public void SetSubMchIdAndLedgerDetail(RequestBuilder builder, Map<String, Object> config, Map<String, Object> transaction){
        String subMchId = BeanUtil.getPropString(config, TransactionParam.BESTPAY_SUB_MERCHANT_ID);
        if (!StringUtil.empty(subMchId)){
            builder.set(BusinessFields.SUB_MERCHANT_ID, subMchId); // subMerchantId 非必传
            if (!StringUtil.empty(BeanUtil.getPropString(config, TransactionParam.BESTPAY_LEDGERDETAIL))){
                //分账是否设置的优先级策略： 1.商户是否有自己传过来；如果是，直接透传；如果否，判断是否有subMchId,并且配置中设置了需要分账。如果true,则100%分给该subMchId
                builder.set(BusinessFields.LEDGER_DETAIL,StringUtils.join(subMchId, ":", transaction.get(Transaction.EFFECTIVE_AMOUNT)));
            }
        }
    }
}
