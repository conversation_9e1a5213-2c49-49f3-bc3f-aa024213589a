package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields;
import com.wosai.mpay.api.tl.syb.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.DateUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通联收银宝刷卡provider
 */
public class TLSybBankServiceProvider extends AbstractServiceProvider {

    public static final String NAME = "provider.tl.syb.bank";
    public static final Logger logger = LoggerFactory.getLogger(TLSybBankServiceProvider.class);
    private static final Set<Integer> CAN_HANDLER_PAYWAY = new HashSet<>(Arrays.asList(Payway.BANKCARD.getCode()));

    private static final Map<String, String> ACCT_TYPE_PAYMENTS_TYPE_MAPPING = MapUtil.hashMap(
            SybConstants.DEBIT_CARD, Payment.TYPE_BANKCARD_DEBIT, //借记卡
            SybConstants.PASSBOOK, Payment.TYPE_BANKCARD_DEBIT,//存折->借记卡
            SybConstants.CREDIT_CARD, Payment.TYPE_BANKCARD_CREDIT,//贷记卡
            SybConstants.SEMI_CREDIT_CARD, Payment.TYPE_BANKCARD_SEMI_CREDIT,//准贷记卡
            SybConstants.PREPAID_CARD, Payment.TYPE_BANKCARD_PREPAID,//预付卡
            SybConstants.OTHER, Payment.TYPE_BANKCARD_DEBIT //其他为借记卡
    );

    @Autowired
    TlSybClient tlSybClient;

    public String notifyHost;
    private int retryTimes = 3;



    private SafeSimpleDateFormat safeSimpleDateFormatMMDD = new SafeSimpleDateFormat("MMdd");


    public TLSybBankServiceProvider() {
        super.dateFormat = new SafeSimpleDateFormat(SybConstants.DATA_FORMAT);
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Provider.TL_SYB.getCode();
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return CAN_HANDLER_PAYWAY.contains(payway) && getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.TL_SYB_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    @Override
    public String cancel(TransactionContext context) {
        //刷卡类型不支持撤销
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    @Override
    public String query(TransactionContext context) {
        return doQuery(context, OP_QUERY);
    }


    @Override
    public String refund(TransactionContext context) {
        String op = OP_REFUND;
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, String> request = buildRefundRequest(context, tradeParams);
        Map<String, Object> result = null;
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        }
        try {
            result = retryIfNetworkException(op, tradeParams, request, 1);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, op, ex);
            logger.error("failed to call syb bank refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, op, e);
            logger.error("encountered ioex in syb bank refund", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, op);
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        if (SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            if (SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)) {
                setTransactionInfoIfSuccess(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            } else if (SybConstants.TRX_STATUS_PROCESSING_1.equalsIgnoreCase(trxStatus) || SybConstants.TRX_STATUS_PROCESSING_2.equalsIgnoreCase(trxStatus)) {
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return Workflow.RC_RETRY;
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refundQuery(TransactionContext context) {
        return doQuery(context, OP_REFUND_QUERY);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        mockTransactionPrecreateSuccess(context);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return query(context);
    }


    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        mockTransactionPrecreateSuccess(context);
        return Workflow.RC_CREATE_SUCCESS;
    }

    @Override
    public String depositQuery(TransactionContext context) {
        return doQuery(context, OP_QUERY);
    }

    @Override
    public String depositConsume(TransactionContext context) {
        return doQuery(context, OP_QUERY);
    }

    @Override
    public String depositCancel(TransactionContext context) {
        return doQuery(context, OP_QUERY);
    }

    protected String doQuery(TransactionContext context, String op) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, String> request = buildQueryRequest(context, tradeParams);
        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(op, tradeParams, request, retryTimes);
        } catch (MpayException ex) {
            setTransactionContextErrorInfo(context, op, ex);
            logger.error("failed to call syb bank query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError e) {
            setTransactionContextErrorInfo(context, op, e);
            logger.error("encountered ioex in syb bank query", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, op);

        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (!SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            return Workflow.RC_ERROR;
        }

        if (!SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)) {
            if (SybConstants.TRX_STATUS_PROCESSING_1.equalsIgnoreCase(trxStatus) ||
                    SybConstants.TRX_STATUS_PROCESSING_2.equalsIgnoreCase(trxStatus) ||
                    SybConstants.TRX_STATUS_NOT_FOUND.equals(trxStatus)) {
                return type == Transaction.TYPE_DEPOSIT_FREEZE ? Workflow.RC_IN_PROG : Workflow.RC_RETRY;
            }
            return Workflow.RC_ERROR;
        }

        // 交易成功的情况处理
        String trxCode = MapUtil.getString(result, ResponseFields.TRX_CODE);
        String workflowResultCode = Workflow.RC_ERROR;
        switch (type) {
            case Transaction.TYPE_REFUND:
                workflowResultCode = Workflow.RC_REFUND_SUCCESS;
                break;
            case Transaction.TYPE_PAYMENT:
            case Transaction.TYPE_DEPOSIT_FREEZE:
                workflowResultCode = Workflow.RC_PAY_SUCCESS;
                break;
            case Transaction.TYPE_DEPOSIT_CONSUME:
                workflowResultCode = Objects.equals(trxCode, SybConstants.PRE_AUTH_COMPLETE)
                        ? Workflow.RC_CONSUME_SUCCESS
                        : Workflow.RC_RETRY;
                break;
            case Transaction.TYPE_CANCEL:
                workflowResultCode = Workflow.RC_CANCEL_SUCCESS;
                break;
            case Transaction.TYPE_DEPOSIT_CANCEL:
                workflowResultCode = Objects.equals(trxCode, SybConstants.PRE_AUTH_CANCEL)
                        ? Workflow.RC_CANCEL_SUCCESS
                        : Workflow.RC_RETRY;
                break;
            default:
                workflowResultCode = Workflow.RC_ERROR;
                break;
        }
        if (Objects.equals(workflowResultCode, Workflow.RC_REFUND_SUCCESS) || Objects.equals(workflowResultCode, Workflow.RC_PAY_SUCCESS)
                || Objects.equals(workflowResultCode, Workflow.RC_CONSUME_SUCCESS) || Objects.equals(workflowResultCode, Workflow.RC_CANCEL_SUCCESS)) {
            setTransactionInfoIfSuccess(result, context);
        }
        return workflowResultCode;
    }

    /**
     * 构建查询参数 使用流水号进行查询
     *
     * @param context
     * @param config
     * @return
     */
    private Map<String, String> buildQueryRequest(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        setCommonRequest(builder, config);
        //对方的系统的订单号 = 收钱吧的流水号。即是发起退款在对方系统里也是一笔新的订单
        builder.set(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.TRX_DATE, safeSimpleDateFormatMMDD.format(new Date(MapUtil.getLongValue(transaction, DaoConstants.CTIME))));
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (type == Transaction.TYPE_DEPOSIT_CONSUME || type ==Transaction.TYPE_DEPOSIT_CANCEL) {
            //预授权完成需要使用预授权冻结的那笔发起查询
            builder.set(BusinessFields.ORDER_ID, MapUtil.getString(order, Order.SN));
            builder.set(BusinessFields.TRX_DATE, safeSimpleDateFormatMMDD.format(new Date(MapUtil.getLongValue(transaction, DaoConstants.CTIME))));
        }
        builder.set(BusinessFields.RESEND_NOTIFY, BusinessFields.RESEND_NOTIFY_NO);
        return builder.build();
    }

    /**
     * 构建退款请求
     *
     * @param context
     * @param config
     * @return
     */
    private Map<String, String> buildRefundRequest(TransactionContext context, Map<String, Object> config) {
        RequestBuilder builder = new RequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME));
        setCommonRequest(builder, config);
        //对方的系统的订单号 = 收钱吧的流水号。即是发起退款在对方系统里也是一笔新的订单
        builder.set(BusinessFields.REQSN, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.TRXID, MapUtil.getString(payOrConsumerTransaction, Transaction.TRADE_NO));
        builder.set(BusinessFields.TRXAMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        return builder.build();
    }

    private void setCommonRequest(RequestBuilder builder, Map<String, Object> config) {
        builder.set(BusinessFields.APPID, MapUtil.getString(config, TransactionParam.TL_SYB_APP_Id));
        builder.set(BusinessFields.CUSID, MapUtil.getString(config, TransactionParam.TL_SYB_CUS_ID));
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        builder.set(BusinessFields.ORGID, MapUtil.getString(config, TransactionParam.TL_SYB_ORG_ID));
    }


    protected Map<String, Object> retryIfNetworkException(String opFlag, Map<String, Object> config, Map<String, String> request, int times) throws MpayException, MpayApiNetworkError {
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag);
        String appId = MapUtil.getString(config, TransactionParam.APP_ID);
        String privateKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.TL_SYB_PRIVATE_KEY));
        MpayApiNetworkError tex = null;
        for (int i = 0; i < times; ++i) {
            try {
                return tlSybClient.call(url, appId, privateKey, request);
            } catch (MpayApiNetworkError ex) {
                tex = ex;
                logger.warn("encountered ioex in tl syb bank {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw tex;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String retCode = MapUtils.getString(result, ResponseFields.RET_CODE, "");//返回的响应码
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS, "");
        String retMsg = MapUtils.getString(result, ResponseFields.RET_MSG, "");//响应描述
        String errmsg = MapUtil.getString(result, ResponseFields.TRX_ERR_MSG, "");
        if (OP_REFUND.equals(key)) {
            //退款的错误信息是存放在errmsg中
            errmsg = MapUtil.getString(result, ResponseFields.ERRMSG, "");
        }
        //收银宝扫码返回的报错信息里是会附带一些商户号的信息的，不便于转ep错误码，因此这里会替换掉。目前不确定刷卡、预授权类型是否也存在该逻辑。先保留
        if (!com.wosai.pantheon.util.StringUtil.isEmpty(errmsg)) {
            errmsg = errmsg.replaceAll("[\\s\\d[a-zA-Z]]", "");
        }

        map.put(ResponseFields.RET_CODE, retCode);//返回状态码
        map.put(ResponseFields.RET_MSG, retMsg);//返回信息
        map.put(ResponseFields.TRX_STATUS, trxStatus);
        map.put(ResponseFields.TRX_ERR_MSG, errmsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, SybConstants.RET_CODE_SUCCESS.equals(retCode) && SybConstants.TRX_STATUS_SUCCESS.equals(trxStatus), retCode, errmsg);
    }

    /**
     * 设置交易成功后的一些信息
     *
     * @param result
     * @param context
     */
    protected void setTransactionInfoIfSuccess(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        //订单信息：支付机构交易流水号、通道源流水号
        //流水信息：支付机构交易流水号、通道源流水号、通道完成时间、payments、银行卡卡号、可选：通道手续费

        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        String trxStatus = MapUtil.getString(result, ResponseFields.TRX_STATUS);
        if (SybConstants.RET_CODE_SUCCESS.equalsIgnoreCase(retCode)) {
            if (SybConstants.TRX_STATUS_SUCCESS.equalsIgnoreCase(trxStatus)) {

                //设置付款人信息
                String acct = MapUtil.getString(result, ResponseFields.ACCT_NO);
                if (StringUtil.isNotEmpty(acct)) {
                    if (!transaction.containsKey(Transaction.BUYER_UID)) {
                        transaction.put(Transaction.BUYER_UID, acct);
                    }
                    if (!transaction.containsKey(Transaction.BUYER_LOGIN)) {
                        transaction.put(Transaction.BUYER_LOGIN, acct);
                    }
                    if (!order.containsKey(Order.BUYER_UID)) {
                        order.put(Transaction.BUYER_UID, acct);
                    }
                    if (!order.containsKey(Order.BUYER_LOGIN)) {
                        order.put(Transaction.BUYER_LOGIN, acct);
                    }
                }

                //设置payments & 外卡种类信息
                if (!extraOutFields.containsKey(Transaction.PAYMENTS)) {
                    String acctType = MapUtil.getString(result, ResponseFields.ACCT_TYPE);
                    if (Objects.equals(SybConstants.FOREIGN_CARD, acctType) && !extraOutFields.containsKey(Transaction.WILD_CARD_TYPE)) {
                        String guessWildCardType = guessWildCardTypeByFee(result, transaction);
                        if (StringUtil.isNotEmpty(guessWildCardType)) {
                            extraOutFields.put(Transaction.WILD_CARD_TYPE, guessWildCardType);
                        }
                    }
                    extraOutFields.put(Transaction.PAYMENTS, analysisPaymentsByNotify(result, transaction));
                }




                long channelFinishTime = System.currentTimeMillis();
                String finishTime = MapUtil.getString(result, ResponseFields.FIN_TIME);
                try {
                    Date parse = dateFormat.parse(finishTime);
                    channelFinishTime = parse.getTime();
                } catch (ParseException e) {
                    logger.error("syb bank response finishTime format fail", e);
                }
                if (MapUtil.getObject(transaction, Transaction.CHANNEL_FINISH_TIME) == null) {
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
                }

                String channelTradeNo = MapUtil.getString(result, ResponseFields.CHANNEL_TRX_ID);
                if (StringUtil.isNotEmpty(channelTradeNo)) {
                    extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
                }

                String tradeNo = MapUtil.getString(result, ResponseFields.TRX_ID);
                if (StringUtil.isNotEmpty(tradeNo)) {
                    if (StringUtil.isEmpty(MapUtil.getString(order, Order.TRADE_NO))) {
                        order.put(Order.TRADE_NO, tradeNo);
                    }
                    if (StringUtil.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
                        transaction.put(Transaction.TRADE_NO, tradeNo);
                    }
                }

                //使用通道手续费
                Long fee = MapUtil.getLong(result, ResponseFields.FEE);
                if (fee != null) {
                    extraOutFields.put(Transaction.REAL_TRADE_FEE, fee);
                }
                String termRefNum = MapUtil.getString(result, ResponseFields.TERM_REF_NUM);
                String termBatchNo = MapUtil.getString(result, ResponseFields.TERM_BATCH_ID);
                String termAuthNo = MapUtil.getString(result, ResponseFields.TERM_AUTH_NO);
                String termTraceNo = MapUtil.getString(result, ResponseFields.TERM_TRACE_NO);
                //参考号
                if (StringUtil.isNotEmpty(termRefNum)) {
                    extraOutFields.put(Transaction.REFER_NUMBER, termRefNum);
                }
                //授权码
                if (StringUtil.isNotEmpty(termAuthNo)) {
                    extraOutFields.put(Transaction.AUTH_NO, termAuthNo);
                }
                //终端批次号
                if (StringUtil.isNotEmpty(termBatchNo)) {
                    extraOutFields.put(Transaction.BATCH_BILL_NO, termBatchNo);
                }
                //终端流水
                if (StringUtil.isNotEmpty(termTraceNo)) {
                    extraOutFields.put(Transaction.SYS_TRACE_NO, termTraceNo);
                }
            }
        }

    }

    private void forceReturn(TransactionContext context) {
        if (!context.isForceReturn()) {
            context.setForceReturn(true);
        }
    }

    public static String guessWildCardTypeByFee(Map<String, Object> queryResult, Map<String, Object> transaction) {
        //如果是外卡类型的刷卡，目前通道不会明确告诉是什么卡，需要去根据手续费/交易金额去反推出一个费率，再去猜使用的是什么卡类型
        String wildCardType = Transaction.WILD_CARD_TYPE_EDC;
        if (queryResult == null) {
            return null;
        }
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        Long channelAmount = MapUtil.getLong(queryResult, ResponseFields.AMOUNT);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> tradeParams = (Map<String, Object>) configSnapshot.get(TransactionParam.TL_SYB_TRADE_PARAMS);
        if (channelAmount != null) {
            amount = channelAmount;
        }
        Long channelFee = MapUtil.getLong(queryResult, ResponseFields.FEE);
        if (channelFee == null) {
            //如果通知里没有返回交易手续费，则使用查单里的交易手续费(对方接口文档里通知里手续费字段可为null)
            Long queryProviderFee = (Long) BeanUtil.getNestedProperty(transaction, Transaction.KEY_PROVIDER_FEE);
            if (queryProviderFee != null) {
                channelFee = queryProviderFee;
            }
        }
        if (channelFee != null) {
            String feeRate = BigDecimal.valueOf(channelFee * 1.0D * 100 / amount).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString();
            long feeRateCents = StringUtils.yuan2cents(feeRate);
            if (MapUtil.isNotEmpty(tradeParams)) {
                //外卡类型不存在阶梯费率的情况
                Map<String, Map<String, Object>> bankcardFeeMap = (Map<String, Map<String, Object>>) tradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE);
                //计算出edc 和 dcc 外卡费率的跟反推的费率的差值
                if (MapUtil.isNotEmpty(bankcardFeeMap)) {
                    Map<String, Long> wildCardDiffFeeRate = new HashMap<>();
                    for (Map.Entry<String, Map<String, Object>> bankCardFeeInfo : bankcardFeeMap.entrySet()) {
                        String bankCardType = bankCardFeeInfo.getKey();
                        if (Objects.equals(Transaction.WILD_CARD_TYPE_EDC, bankCardType) || Objects.equals(Transaction.WILD_CARD_TYPE_EDC, bankCardType)) {
                            Map<String, Object> feeInfo = bankCardFeeInfo.getValue();
                            if (feeInfo != null) {
                                String bankFeeRate = MapUtil.getString(feeInfo, TransactionParam.FEE);
                                if (StringUtil.isNotEmpty(bankFeeRate)) {
                                    long configBankFeeRateCents = StringUtils.yuan2cents(bankFeeRate);
                                    long diffFee = Math.abs(configBankFeeRateCents - feeRateCents);
                                    wildCardDiffFeeRate.put(bankCardType, diffFee);
                                }
                            }
                        }
                    }
                    if (MapUtil.isNotEmpty(wildCardDiffFeeRate)) {
                        Long minDiff = Collections.min(wildCardDiffFeeRate.values());
                        List<String> minDiffCardType = wildCardDiffFeeRate.entrySet().stream().filter(o -> Objects.equals(minDiff, o.getValue())).map(o -> o.getKey()).collect(Collectors.toList());
                        if (minDiffCardType != null && minDiffCardType.size() == 1) {
                            wildCardType = minDiffCardType.get(0);
                        }
                    }
                }
            }
        }
        return wildCardType;
    }

    public static List<Map<String, Object>> analysisPaymentsByNotify(Map<String, Object> result, Map<String, Object> transaction) {
        if (result == null) {
            return null;
        }
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();
        String acctType = MapUtil.getString(result, ResponseFields.ACCT_TYPE);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        Long channelAmount = MapUtil.getLong(result, ResponseFields.AMOUNT);
        if (channelAmount != null) {
            amount = channelAmount;
        }
        String paymentType = ACCT_TYPE_PAYMENTS_TYPE_MAPPING.getOrDefault(acctType, Payment.TYPE_BANKCARD_CREDIT);

        payments.add(MapUtil.hashMap(
                Transaction.PAYMENT_TYPE, paymentType,
                Transaction.PAYMENT_ORIGIN_TYPE, acctType,
                Transaction.PAYMENT_AMOUNT, amount
        ));
        return payments;
    }

    public static String analysisPayerInfoByNotify(Map<String, Object> notifyBody) {
        if (notifyBody == null) {
            return null;
        }
        return MapUtil.getString(notifyBody, ResponseFields.ACCT);
    }

    public static void convertBankProductFlag(Map<String, Object> transaction) {
        String productFlags = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);
        boolean isWildCard = false;
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        if (extraOutFields.containsKey(Transaction.WILD_CARD_TYPE)) {
            isWildCard = true;
        }
        Set<String> productFlagSet = new HashSet<>();
        if (StringUtil.isNotEmpty(productFlags)) {
            productFlagSet = Arrays.stream(productFlags.split(",")).collect(Collectors.toSet());
        }
        productFlagSet.remove(ProductFlag.CROSS_CARD_PAY.getCode());
        productFlagSet.remove(ProductFlag.LOCAL_CARD_PAY.getCode());
        if (isWildCard) {
            productFlagSet.add(ProductFlag.CROSS_CARD_PAY_INDIRECT.getCode());
        } else {
            productFlagSet.add(ProductFlag.LOCAL_CARD_PAY.getCode());
        }
        String newProductFlags = String.join(",", productFlagSet);
        transaction.put(Transaction.PRODUCT_FLAG, newProductFlags);
    }


    protected void mockTransactionPrecreateSuccess(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        wapRequest.put(ResponseV3Fields.COUNTER_URL, "");
        wapRequest.put(ResponseV3Fields.PAY_ORDER_NO, MapUtil.getString(context.getTransaction(), Transaction.TSN));
        transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
    }
}
