package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.techtrans.*;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.wosai.upay.util.ProviderUtil.updateMapIfResponseNotNull;

/**
 * 北京首都机场支付接口
 * 只支持小程序支付
 */
@Slf4j
@Component
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class PkxAirportMiniServiceProvider extends AbstractServiceProvider {
    public static final String NAME = "provider.pkx.airport";

    @Resource
    private TechTransClient client;


    public PkxAirportMiniServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(TechTransRequestFields.REFUND_AMOUNT));
        dateFormat = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Order.PAYWAY_WEIXIN != MapUtil.getIntValue(transaction, Transaction.PAYWAY)) {
            return false;
        }
        if (Order.SUB_PAYWAY_MINI != MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY)) {
            return false;
        }
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.PKX_AIRPORT_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Provider.PKX_AIRPORT.getCode();
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        // not support
        return Workflow.RC_TRADE_CANCELED;
    }


    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, false);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        updateTransactionCommonInfo(transaction, result);
        String status = MapUtil.getString(result, TechTransResponseFields.STATUS);
        if(!Objects.equals(status, TechTransConstant.RESPONSE_STATUS_SUCCESS)){
            return Workflow.RC_IN_PROG;
        }
        String orderStatus = MapUtil.getString(result, TechTransResponseFields.ORDER_STATUS);
        if(PaymentStatusConstants.isPaymentCompleted(orderStatus)){
            if(PaymentStatusConstants.isPaymentSuccessful(orderStatus)){
                // success
                updateTransactionPaymentInfo(transaction, result);
                return Workflow.RC_PAY_SUCCESS;
            }else {
                // fail or close
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        if(PaymentStatusConstants.isPaymentInProgress(orderStatus)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_IN_PROG;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, true);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String status = MapUtil.getString(result, TechTransResponseFields.STATUS, "");
        if(!TechTransConstant.RESPONSE_STATUS_SUCCESS.equals(status)){
            return Workflow.RC_SYS_ERROR;
        }
        String refundStatus = MapUtil.getString(result, TechTransResponseFields.REFUND_STATUS);
        if(RefundStatusConstants.isRefundCompleted(refundStatus)){
            if(RefundStatusConstants.isRefundSuccessful(refundStatus)){
                // success
                updateTransactionRefundInfo(context, result);
                return Workflow.RC_REFUND_SUCCESS;
            }else {
                // fail or close
                return Workflow.RC_SYS_ERROR;
            }
        }
        if(RefundStatusConstants.isRefundInProgress(refundStatus)){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_RETRY;
    }

    // doQuery
    protected Map<String,Object> doQuery(TransactionContext context, boolean isRefundQuery) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> request = buildCommonRequestParams(config);

        String queryFlag = isRefundQuery ? OP_REFUND_QUERY : OP_QUERY;
        String idKey = TechTransRequestFields.OUT_ID;
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        String [] fields = TechTransConstant.WX_MINI_ORDER_QUERY_FIELDS;
        String method = TechTransConstant.METHOD_WX_MINI_ORDER_QUERY;
        if (isRefundQuery) {
            idKey = TechTransRequestFields.REFUND_OUT_ID;
            url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_QUERY);
            fields = TechTransConstant.WX_MINI_REFUND_QUERY_FIELDS;
            method = TechTransConstant.METHOD_WX_MINI_REFUND_QUERY;
        }
        request.put(idKey, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> result = null;
        try {
            result = client.call(method, url, request, MapUtil.getString(config, TransactionParam.PKX_AIRPORT_USERNAME), MapUtil.getString(config, TransactionParam.PKX_AIRPORT_SIGN_KEY), fields);
        } catch (Exception ex) {
            logger.error("failed to call pkx airport query", ex);
            setTransactionContextErrorInfo(context, queryFlag, ex);
        }
        setTransactionContextErrorInfo(result, context, queryFlag);
        return result;

    }



    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery){
            return refundQuery(context);
        }else{
            return doRefund(context);
        }
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> request = buildCommonRequestParams(config);
        request.put(TechTransRequestFields.REFUND_OUT_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(TechTransRequestFields.OUT_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        request.put(TechTransRequestFields.REFUND_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        request.put(TechTransRequestFields.TOTAL_FEE, MapUtil.getLongValue(order, Order.EFFECTIVE_TOTAL));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            result = client.call(TechTransConstant.METHOD_WX_MINI_ORDER_REFUND, url, request, MapUtil.getString(config, TransactionParam.PKX_AIRPORT_USERNAME), MapUtil.getString(config, TransactionParam.PKX_AIRPORT_SIGN_KEY), TechTransConstant.WX_MINI_ORDER_REFUND_FIELDS);

        } catch (Exception ex) {
            logger.error("failed to call pkx airport refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String status = MapUtil.getString(result, TechTransResponseFields.STATUS, "");
        if(TechTransConstant.RESPONSE_STATUS_SUCCESS.equals(status)){
            // success
            updateTransactionRefundInfo(context, result);
            return Workflow.RC_REFUND_SUCCESS;
        }else{
            return Workflow.RC_RETRY;
        }
    }


    @Override
    public String cancel(TransactionContext context) {
        // not support
        return Workflow.RC_TRADE_CANCELED;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        try{
            // 构建前端调用大兴机场插件的所需参数
            Map<String, Object> wapRequest = new HashMap<String, Object>();
            wapRequest.put(TechTransRequestFields.TOTAL_FEE, transaction.get(Transaction.EFFECTIVE_AMOUNT));
            wapRequest.put(TechTransRequestFields.OUT_ID, transaction.get(Transaction.ORDER_SN));
            wapRequest.put(TechTransRequestFields.MALL_ID, config.get(TransactionParam.PKX_AIRPORT_MALL_ID));
            wapRequest.put(TechTransRequestFields.USER, config.get(TransactionParam.PKX_AIRPORT_USERNAME));
            wapRequest.put(TechTransRequestFields.SELLER_ID, config.get(TransactionParam.PKX_AIRPORT_SELLER_ID));
            wapRequest.put(TechTransRequestFields.STORE_CODE, config.get(TransactionParam.PROVIDER_MCH_ID));
            wapRequest.put(TechTransRequestFields.BS_STORE_CODE, config.get(TransactionParam.PROVIDER_MCH_ID));
            wapRequest.put(TechTransRequestFields.CALLBACK_URL, getNotifyUrl(notifyHost, context));
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }catch (Exception e){
            logger.error("process wap pay request error: " + e.getMessage(), e);
            return Workflow.RC_TRADE_CANCELED;
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT){
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    private static Map<String, Object> buildCommonRequestParams(Map<String,Object> config) {
        Map<String, Object> request = new HashMap<>();
        request.put(TechTransRequestFields.SELLER_ID, MapUtil.getString(config, TransactionParam.PKX_AIRPORT_SELLER_ID));
        request.put(TechTransRequestFields.STORE_CODE, MapUtil.getString(config, TransactionParam.PKX_AIRPORT_PROVIDER_MCH_ID));
        request.put(TechTransRequestFields.MALL_ID, MapUtil.getString(config, TransactionParam.PKX_AIRPORT_MALL_ID));
        request.put(TechTransRequestFields.BS_STORE_CODE, MapUtil.getString(config, TransactionParam.PKX_AIRPORT_PROVIDER_MCH_ID));
        return request;
    }

    /**
     * 更新付款信息
     * @param transaction
     * @param result
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> result){
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, result, TechTransResponseFields.TIME_END, object -> parseTimeString((String) object));
        // 计算支付明细
        Long effectiveAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        List<Map<String,Object>> payments = new ArrayList<>();
        payments.add(
                CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_WEIXIN,
                        Transaction.PAYMENT_ORIGIN_TYPE, null,
                        Transaction.PAYMENT_AMOUNT, effectiveAmount
                )
        );
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }

    /**
     * 更新退款信息
     * @param context
     * @param result
     */
    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> result){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }


    /**
     * 更新通用字段
     * @param transaction
     * @param result
     */
    protected void updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> result) {
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        // 解析号
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, result, TechTransResponseFields.TRADE_NO);
        // 解析用户信息
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID, result, TechTransResponseFields.BUYER_ID);
    }


    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String status = MapUtil.getString(result, TechTransResponseFields.STATUS);
        String msg = MapUtil.getString(result, TechTransResponseFields.MSG);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(TechTransResponseFields.STATUS, status);
        map.put(TechTransResponseFields.MSG, msg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, TechTransConstant.RESPONSE_STATUS_SUCCESS.equals(status), status, msg);
    }
}
