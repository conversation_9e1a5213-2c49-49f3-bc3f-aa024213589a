package com.wosai.upay.workflow;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/***
 * @ClassName: DirectAlipayV2MiniServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/6/26 4:17 PM
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 2)
public class DirectAlipayV2PreDepositServiceProvider extends DirectAlipayV2WapServiceProvider {

    private static final Logger logger = LoggerFactory.getLogger(DirectAlipayV2PreDepositServiceProvider.class);

    public static final String NAME = "provider.alipay.pre.deposit";

    public DirectAlipayV2PreDepositServiceProvider(){
        extendedFilterFields = new HashSet<>(Lists.newArrayList(BusinessFields.AUTHORIZATION_CODE));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if ((Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway)
                && Order.SUB_PAYWAY_MINI == MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY)) {

            Map<String, Object> tradeParams = getTradeParams(transaction);
            if (Objects.isNull(tradeParams)) {
                return false;
            }
            String sqbProductCode = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.SQB_PRODUCT_CODE);

            if (StringUtils.isEmpty(sqbProductCode)) {
                sqbProductCode = MapUtil.getString(tradeParams, TransactionParam.ALIPAY_PRODUCT_CODE);
            }
            return !StringUtils.isEmpty(sqbProductCode) && TransactionParam.SQB_PRODUCT_CODE_ALIPAY_ZM.equals(sqbProductCode);
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);

        return getBasicRequestBuilder(config);
    }

    private RequestV2Builder getBasicRequestBuilder(Map<String, Object> config) {
        RequestV2Builder builder = new RequestV2Builder();

        builder.set(ProtocolV2Fields.APP_ID, BeanUtil.getPropString(config, TransactionParam.APP_ID));
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.TIMESTAMP, dateFormat.format(new Date()));
        builder.set(ProtocolV2Fields.VERSION, "1.0");

        return builder;
    }

    @Override
    public String depositPreFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String zmServiceId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SERVICE_ID);
        RequestV2Builder builder = getAlipayV2Builder(context);

        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        //商户外部订单号
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //订单金额
        builder.bizSet(BusinessV2Fields.ORDER_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.AMOUNT_TYPE, AlipayConstants.AMOUNT_TYPE_ORDER_AMOUNT);
        //芝麻外部类目
        builder.bizSet(BusinessV2Fields.ZM_CATEGORY_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_CATEGORY));
        //产品码
        builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_CREDIT_PAY_AFTER_USE);
        //订单标题
        builder.bizSet(BusinessV2Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        if (!StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BODY))) {
            //订单描述
            builder.bizSet(BusinessV2Fields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        }
        //授权状态
        boolean isAuth = BeanUtil.getPropBoolean(transaction, Transaction.KEY_SQB_PAYER_AUTH);
        if (!isAuth) {
            //商户外部协议号
            String outAgreementNo = BeanUtil.getPropString(extendedParams, BusinessFields.AUTHORIZATION_CODE);
            if (StringUtil.empty(outAgreementNo)) {
                String payerUid = BeanUtil.getPropString(extraParams, Transaction.PAYER_UID);
                try {
                    outAgreementNo = Digest.md5((payerUid + zmServiceId).getBytes(StandardCharsets.UTF_8));
                } catch (NoSuchAlgorithmException e) {
                    throw new RuntimeException("generate agreement no error", e);
                }
            }
            //商户外部协议号
            builder.bizSet(BusinessV2Fields.OUT_AGREEMENT_NO, outAgreementNo);
            //芝麻信用服务ID
            builder.bizSet(BusinessV2Fields.ZM_SERVICE_ID, zmServiceId);
        }
        // Carry over extended params to the pay service provider.
        extendedParams.remove(BusinessFields.AUTHORIZATION_CODE);
        carryOverExtendedParams(extendedParams, builder);

        Map<String, Object> result;
        Map<String, String> request = null;

        //若已授权，则直接信用下单
        if (isAuth) {
            builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_ORDER);
            try {
                request = builder.build();
            } catch (BuilderException e) {
                logger.error("alipayV2 zm request builder error ", e);
            }
            try {
                result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_FREEZE), request, 1, OP_DEPOSIT_FREEZE);
            } catch (Exception ex) {
                logger.error("failed to call alipayV2 zm deposit freeze", ex);
                setTransactionContextErrorInfo(context, OP_DEPOSIT_FREEZE, ex);
                //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
                return Workflow.RC_IOEX;
            }
            if (result == null) {
                return Workflow.RC_IOEX;
            }

            String returnCode = BeanUtil.getPropString(result, BusinessV2Fields.CODE);//返回状态码
            setTransactionContextErrorInfo(result, context, OP_DEPOSIT_PREFREEZE);
            if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
                //业务处理失败
                String subCode = BeanUtil.getPropString(result, BusinessV2Fields.SUB_CODE);
                if (AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.contains(subCode) || AlipayConstants.DEPOSIT_FAIL_ERR_CODE_LISTS.contains(subCode) || AlipayConstants.DEPOSIT_ZM_CHECK_NO_PASS_ERR_CODE_LISTS.contains(subCode)) {
                    return Workflow.RC_TRADE_CANCELED;
                }
                return Workflow.RC_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
                //业务出现未知错误或者系统异常
                return Workflow.RC_SYS_ERROR;
            } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
                //统一处理为需要继续查询，并在查询中记录credit_biz_order_id 和 credit_agreement_id
                //给wapRequest随便塞一个credit_biz_order_id, 以防止precreateResponse校验不通过
                String creditBizOrderId = BeanUtil.getPropString(result, BusinessV2Fields.CREDIT_BIZ_ORDER_ID);
                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                Map<String, Object> wapRequest = new HashMap<>();
                wapRequest.put(Transaction.CREDIT_BIZ_ORDER_ID, creditBizOrderId);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                return Workflow.RC_CREATE_SUCCESS;
            }
            return Workflow.RC_ERROR;
        } else {
            builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_CREATE);
            String responseBodyStr;
            try {
                request = builder.build();
                request.put(AlipayConstants.FORMAT, AlipayConstants.FORMAT_JSON);
                AlipayV2NewClient.preProcess(AlipayV2NewClient.SIGN_TYPE_ALIPAY, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
                for(String key : request.keySet()) {
                    request.put(key, URLEncoder.encode(request.get(key), AlipayConstants.CHARSET_UTF8));
                }
                responseBodyStr = RsaSignature.getSignCheckContent(request);
            }catch (Exception ex) {
                logger.error("failed to call alipayV2 zm deposit pre freeze", ex);
                setTransactionContextErrorInfo(context, OP_DEPOSIT_PREFREEZE, ex);
                return Workflow.RC_IOEX;
            }
            //预下单成功
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

            Map<String, Object> wapRequest = new HashMap<>();
            wapRequest.put(WapV2Fields.SIGN_STR, responseBodyStr);
            wapRequest.put(WapV2Fields.TYPE, AlipayConstants.BIZ_TYPE_PAY_AFTER);
            wapRequest.put(WapV2Fields.ZM_SERVICE_ID, zmServiceId);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }

    }


    @Override
    public String depositCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        RequestV2Builder builder = completeCreditOrderBuilder(context, false);
        Map<String, String> request = null;
        Map<String, Object> result;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 zm request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CANCEL), request, retryTimes, OP_DEPOSIT_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call alipayV2 zm deposit cancel", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CANCEL, ex);
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_RETRY;
        }
        String returnCode = BeanUtil.getPropString(result, BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_CANCEL);

        if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
            String subCode = (String) result.get(BusinessV2Fields.SUB_CODE);
            if (AlipayConstants.RESULT_CODE_DEPOSIT_CANCEL_ORDER_ALREADY_CLOSED.equals(subCode)) {
                return Workflow.RC_CANCEL_SUCCESS;
            }
            return Workflow.RC_ERROR;
        } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
            return Workflow.RC_RETRY;
        } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String depositQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        RequestV2Builder builder = getAlipayV2Builder(context);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));

        //商户外部单号
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        String method = AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_QUERY;
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if (type == Transaction.TYPE_DEPOSIT_CONSUME) {
            method = AlipayV2Methods.ALIPAY_TRADE_QUERY;
            setQueryOptions(builder, transaction, OP_DEPOSIT_QUERY);
        }
        //接口名称
        builder.set(ProtocolV2Fields.METHOD, method);
        Map<String, String> request = null;
        Map<String, Object> result;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 zm request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_QUERY), request, retryTimes, OP_DEPOSIT_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call alipayV2 zm deposit query", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_QUERY, ex);
            //连接错误，继续轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }

        //用于判断是否是首次开通
        boolean isAuth = BeanUtil.getPropBoolean(transaction, Transaction.KEY_SQB_PAYER_AUTH);

        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String returnCode = BeanUtil.getPropString(result, BusinessV2Fields.CODE);//返回状态码
        String errCode = BeanUtil.getPropString(result, BusinessV2Fields.SUB_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_QUERY);

        if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
            if (AlipayConstants.RESULT_CODE_SETTLE_INVALID_PARAMETER.equals(errCode) && !isAuth) {
                // 下单成功后，再次查单返回“INVALID_PARAMETER”时，需要进行查单动作
                Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction,
                        UpayUtil.getProviderErrorInfoKey(MpayServiceProvider.OP_DEPOSIT_PREFREEZE));
                if (null == payResultMap) {
                    return Workflow.RC_IN_PROG;
                } else {
                    return Workflow.RC_ERROR;
                }
            }
            return Workflow.RC_ERROR;
        } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode) || AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)) {
            return Workflow.RC_IN_PROG;
        } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
            if (Transaction.TYPE_DEPOSIT_FREEZE == type || Transaction.TYPE_DEPOSIT_CANCEL == type) {
                String orderStatus = BeanUtil.getPropString(result, BusinessV2Fields.ORDER_STATUS);
                String tradeNo = getRealTradeNo(BeanUtil.getPropInt(transaction, Transaction.PROVIDER), result);
                transaction.put(Transaction.TRADE_NO, tradeNo);
                if (StringUtil.empty(BeanUtil.getPropString(context.getOrder(), Order.TRADE_NO))) {
                    context.getOrder().put(Order.TRADE_NO, tradeNo);
                }
                //由于支付宝返回的create_time是创建时间，非芝麻先享成功下单时间，故此处取返回当前时间戳
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                setExtraOutField(context, result);
                if (AlipayConstants.STATUS_INIT.equals(orderStatus)) {
                    return Workflow.RC_PAY_SUCCESS;
                } else if (AlipayConstants.TRADE_STATUS_TRADE_CLOSED.equals(orderStatus)) {
                    return Workflow.RC_TRADE_CANCELED;
                }

            } else if (Transaction.TYPE_DEPOSIT_CONSUME == type) {
                String tradeStatus = BeanUtil.getPropString(result, BusinessV2Fields.TRADE_STATUS);
                if (AlipayConstants.TRADE_STATUS_WAIT_BUYER_PAY.equals(tradeStatus)) {
                    return Workflow.RC_IN_PROG;
                } else if (AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus)) {
                    String tradeNo = getRealTradeNo(BeanUtil.getPropInt(transaction, Transaction.PROVIDER), result);
                    transaction.put(Transaction.BUYER_UID, getBuyerUid(result));
                    transaction.put(Transaction.TRADE_NO, tradeNo);
                    if (StringUtil.empty(BeanUtil.getPropString(context.getOrder(), Order.TRADE_NO))) {
                        context.getOrder().put(Order.TRADE_NO, tradeNo);
                    }
                    transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(result, BusinessV2Fields.BUYER_LOGON_ID));
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.SEND_PAY_DATE)));

                    resolvePayFund(context.getOrder(), transaction, result);
                    return Workflow.RC_CONSUME_SUCCESS;
                }
                // 预授权完成失败查单设置下外部订单号
                String tradeNo = BeanUtil.getPropString(result, BusinessV2Fields.TRADE_NO);
                if (!StringUtil.empty(tradeNo)) {
                    context.getTransaction().put(Transaction.TRADE_NO, tradeNo);
                }
            }
        }

        return Workflow.RC_ERROR;
    }


    @Override
    public String depositConsume(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        String notifyUrl = getNotifyUrl(notifyHost, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), context, NOTIFY_DEPOSIT);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_TRADE);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        //商户订单号
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单总金额
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        //产品码
        builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_GENERAL_WITHHOLDING);
        //订单标题
        builder.bizSet(BusinessV2Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //信用下单阶段返回的订单号 credit_biz_order_id
        String creditBizOrderId = String.valueOf(BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.CREDIT_BIZ_ORDER_ID));
        builder.bizSet(BusinessV2Fields.AUTH_CODE, creditBizOrderId);
        //芝麻先用后付协议号，固定传值为：ZHIMA_CREDIT_CODE
        builder.bizSet(BusinessV2Fields.SCENE, AlipayConstants.SCENE_ZHIMA_CREDIT_CODE);
        //卖家支付宝用户ID
        String sellerId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SELLER_ID
                , BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_ID));
        if (!StringUtils.isEmpty(sellerId)) {
            builder.bizSet(BusinessV2Fields.SELLER_ID, sellerId);
        }
        //商户门店编号
        builder.bizSet(BusinessV2Fields.STORE_ID, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_STORE_ID,
                BeanUtil.getPropString(transaction, KEY_STORE_CLIENT_SN, (String) transaction.get(Transaction.STORE_ID))).replaceAll("-", ""));
        //商户操作员编号
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, BeanUtil.getPropString(transaction, Transaction.OPERATOR));
        //商户机具终端编号
        builder.bizSet(BusinessV2Fields.TERMINAL_ID, BeanUtil.getPropString(transaction, KEY_TERMINAL_SN));
        //业务扩展参数 - 信用交易场景，必须上送：CREDIT_PAY_UNCERTAIN_FEE
        builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                BusinessV2Fields.EXTEND_PARAMS_CREDIT_TRADE_SCENE, AlipayConstants.CREDIT_PAY_UNCERTAIN_FEE_SCENE
        ));
        //异步扣款
        builder.bizSet(BusinessV2Fields.IS_ASYNC_PAY, true);
        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);
        setQueryOptions(builder, transaction, OP_DEPOSIT_CONSUME);

        Map<String, String> request = null;
        Map<String, Object> result;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 zm request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CONSUME), request, 1, OP_DEPOSIT_CONSUME);
        } catch (Exception ex) {
            logger.error("failed to call alipayV2 zm deposit consume", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CONSUME, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if (result == null) {
            return Workflow.RC_IN_PROG;
        }

        String tradeNo = BeanUtil.getPropString(result, BusinessV2Fields.TRADE_NO);
        if (Objects.nonNull(tradeNo) && !tradeNo.trim().isEmpty()) {
            context.getTransaction().put(Transaction.TRADE_NO, tradeNo);
        }

        String returnCode = BeanUtil.getPropString(result, BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_CONSUME);
        if (AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)) {
            String subCode = (String) result.get(BusinessV2Fields.SUB_CODE);
            if (AlipayConstants.RESULT_CODE_V2_PAY_TRADE_HAS_SUCCESS.equals(subCode)) {
                return Workflow.RC_CONSUME_SUCCESS;
            } else if (AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.contains(subCode)) {
                // 预授权完成业务明确处理失败
                return Workflow.RC_CONSUME_FAIL;
            }
            //业务处理失败
            return Workflow.RC_ERROR;
        } else if (AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)) {
            //业务出现未知错误或者系统异常
            return Workflow.RC_IN_PROG;
        } else if (AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)) {
            //业务处理中
            return Workflow.RC_IN_PROG;
        } else if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
            transaction.put(Transaction.TRADE_NO, getRealTradeNo(MapUtil.getIntValue(transaction, Transaction.PROVIDER), result));
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.GMT_PAYMENT)));
            resolvePayFund(context.getOrder(), transaction, result);
            return Workflow.RC_CONSUME_SUCCESS;
        }
        return Workflow.RC_ERROR;

    }

    private void setExtraOutField(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }

        String creditBizOrderId = BeanUtil.getPropString(result, BusinessV2Fields.CREDIT_BIZ_ORDER_ID);
        String creditAgreementId = BeanUtil.getPropString(result, BusinessV2Fields.CREDIT_AGREEMENT_ID);
        String zmServiceId = BeanUtil.getPropString(result, BusinessV2Fields.ZM_SERVICE_ID);
        String totalAmount = BeanUtil.getPropString(result, BusinessV2Fields.TOTAL_AMOUNT);
        if (!StringUtils.isEmpty(creditBizOrderId)) {
            extraOutFields.put(Transaction.CREDIT_BIZ_ORDER_ID, creditBizOrderId);
        }

        if (!StringUtils.isEmpty(creditAgreementId)) {
            extraOutFields.put(Transaction.CREDIT_AGREEMENT_ID, creditAgreementId);
        }

        if (!StringUtils.isEmpty(zmServiceId)) {
            extraOutFields.put(Transaction.ZM_SERVICE_ID, zmServiceId);
        }

        if (!StringUtils.isEmpty(totalAmount)) {
            extraOutFields.put(Transaction.TOTAL_AMOUNT, StringUtils.yuan2cents(totalAmount));
        }

    }

    @Override
    public String depositSync(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);

        RequestV2Builder builder = completeCreditOrderBuilder(context, true);

        Map<String, String> request = null;
        Map<String, Object> result;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 zm request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_SYNC), request, 1, OP_DEPOSIT_SYNC);
        } catch (Exception ex) {
            logger.error("failed to call alipayV2 zm deposit sync", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_SYNC, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return Workflow.RC_CONSUME_FAIL;
        }
        if (result == null) {
            return Workflow.RC_CONSUME_FAIL;
        }

        String returnCode = BeanUtil.getPropString(result, BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_SYNC);
        if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            return Workflow.RC_CONSUME_SUCCESS;
        }
        return Workflow.RC_CONSUME_FAIL;

    }

    private RequestV2Builder completeCreditOrderBuilder(TransactionContext context, boolean isFulfilled) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_FINISH);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, MapUtil.getString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        //信用服务订单号
        String creditBizOrderId = String.valueOf(BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.CREDIT_BIZ_ORDER_ID));
        builder.bizSet(BusinessV2Fields.CREDIT_BIZ_ORDER_ID, creditBizOrderId);
        //商户外部请求号
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //用户此订单是否守约
        builder.bizSet(BusinessV2Fields.IS_FULFILLED, String.valueOf(isFulfilled));
        builder.bizSet(BusinessV2Fields.REMARK, StringUtils.join(DateUtil.formatDate(new Date(), DateUtil.FORMATTER_DATE_INT), "完结订单",
                StringUtils.cents2yuan(BeanUtil.getPropLong(context.getOrder(), Order.EFFECTIVE_TOTAL)), "元"));

        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);
        return builder;
    }

    public Map<String, Object> depositAuthApply(Map<String, Object> config, Map<String, Object> extended) {
        RequestV2Builder builder = getBasicRequestBuilder(config);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITAGREEMENT_SIGN);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, MapUtil.getString(config, TransactionParam.APP_AUTH_TOKEN, ""));
        String zmServiceId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SERVICE_ID);
        //商户外部协议号
        String outAgreementNo = BeanUtil.getPropString(extended, BusinessFields.AUTHORIZATION_CODE);
        if (StringUtil.empty(outAgreementNo)) {
            String payerUid = BeanUtil.getPropString(extended, Transaction.PAYER_UID);
            try {
                outAgreementNo = Digest.md5((payerUid + zmServiceId).getBytes(StandardCharsets.UTF_8));
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException("generate agreement no error", e);
            }
        }
        builder.bizSet(BusinessV2Fields.OUT_AGREEMENT_NO, outAgreementNo);
        //芝麻信用服务ID
        builder.bizSet(BusinessV2Fields.ZM_SERVICE_ID, zmServiceId);
        //芝麻外部类目
        builder.bizSet(BusinessV2Fields.ZM_CATEGORY_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_CATEGORY));
        extended.remove(BusinessFields.AUTHORIZATION_CODE);
        carryDepositOverExtendedParams(extended, builder, AlipayConstants.DEPOSIT_AUTH_APPLY_ALLOWED_FIELDS);

        Map<String, String> request;
        Map<String, Object> result = new HashMap<>();
        String responseBodyStr;
        try {
            request = builder.build();

            request.put(AlipayConstants.FORMAT, AlipayConstants.FORMAT_JSON);
            AlipayV2NewClient.preProcess(AlipayV2NewClient.SIGN_TYPE_ALIPAY, getPrivateKeyContent((String) config.get(TransactionParam.PRIVATE_KEY)), request);
            for(String key : request.keySet()) {
                request.put(key, URLEncoder.encode(request.get(key), AlipayConstants.CHARSET_UTF8));
            }
            responseBodyStr = RsaSignature.getSignCheckContent(request);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 auth apply");
            throw new RuntimeException("alipayV2 deposit auth apply error", ex);
        }

        if (!StringUtils.isEmpty(outAgreementNo)) {
            result.put(BusinessFields.AUTHORIZATION_CODE, outAgreementNo);
            result.put(ResponseFields.APPLY_PERMISSIONS_TOKEN, responseBodyStr);
        }

        return result;
    }

    public Map<String, Object> depositAuthQuery(Map<String, Object> config, Map<String, Object> extended) {
        RequestV2Builder builder = getBasicRequestBuilder(config);

        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITAGREEMENT_QUERY);

        String outAgreementNo = BeanUtil.getPropString(extended, BusinessFields.AUTHORIZATION_CODE);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, MapUtil.getString(config, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.OUT_AGREEMENT_NO, outAgreementNo);
        extended.remove(BusinessFields.AUTHORIZATION_CODE);
        carryDepositOverExtendedParams(extended, builder, AlipayConstants.DEPOSIT_AUTH_QUERY_ALLOWED_FIELDS);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_AUTH_APPLY), builder.build(), 1, OP_DEPOSIT_AUTH_APPLY);
        } catch (Exception ex) {
            logger.error("failed to call alipayV2 zm deposit auth query", ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return CollectionUtil.hashMap(ResponseFields.ERR_CODE_DES, ex.getMessage());
        }

        return result;
    }

    protected void carryDepositOverExtendedParams(Map<String, Object> extended, RequestV2Builder builder, Set<String> allowedFields) {
        if (extended == null || extended.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if ((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.bizSet(key, value);
            }
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(type == Transaction.TYPE_DEPOSIT_FREEZE) {
            return Workflow.RC_PAY_SUCCESS.equals(depositQuery(context)) ? Workflow.RC_PAY_SUCCESS : null;
        } else {
            providerNotification.put(TransactionContext.class.getName(), context);
            return super.explainNotification(providerNotification);
        }
    }


}