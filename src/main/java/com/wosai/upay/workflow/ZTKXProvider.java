package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.chinaums.ResponseFields;
import com.wosai.mpay.api.unionqrcode.BusinessFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.ztkx.*;
import com.wosai.mpay.api.ztkx.enums.*;
import com.wosai.mpay.api.ztkx.util.ZTKXHeadUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.wosai.upay.core.model.TransactionParam.*;
import static com.wosai.upay.util.LocalDateTimeUtil.YYYYMMDDHHMMSS;
import static com.wosai.upay.util.LocalDateTimeUtil.toEpochMs;


/**
 * 中投科信支付接口
 */
@Slf4j
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class ZTKXProvider extends AbstractServiceProvider {
    public static final String NAME = "provider.ztkx";
    private static final int ORDER_TIME_OUT_MINUTE = 1; // 订单超时时间设置为1分钟
    protected static final int NOTIFY_URL_LIMIT = 200;
    @Resource
    private ZTKXClient ztkxClient;

    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ZTKX_TRADE_PARAMS);
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_ZTKX;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        ZTKXRequestBuilder ztkxRequestBuilder = getPayRequestBuilder(tradeParams, transaction, extraParams);
        // 刷卡交易必输微信、支付宝或银联付款码
        ztkxRequestBuilder.set(ZTKXBusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        // 终端信息
        ztkxRequestBuilder.set(ZTKXBusinessFields.TERMINAL_INFO, getTerminalInfoValue(transaction));
        // 回调通知地址
        ztkxRequestBuilder.set(ZTKXBusinessFields.SERVER_URL, getNotifyUrl(notifyHost, context));
        // 添加交易类型
        ztkxRequestBuilder.set(ZTKXBusinessFields.PAY_TYPE, getPayTradeCode(transaction));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS016.getCode(), ztkxRequestBuilder.build(), tradeParams, buildTranFlow(transaction, tradeParams), OP_PAY, 1);
        } catch (Exception e) {
            log.error("call ztkx pay error: {}", e.getMessage(), e);
            transaction.put(Transaction.TRADE_NO, buildTranFlow(transaction, tradeParams));
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_ERROR;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context);
    }

    private static String buildTranFlow(Map<String, Object> transaction, Map<String, Object> tradeParams) {
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        String platformMchId = MapUtils.getString(tradeParams, PLATFORM_MCH_ID);
        return platformMchId + tsn;
    }

    private static String getPayTradeCode(Map<String, Object> transaction) {
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_WEIXIN == payway) {
            return ZTKXPayTypeEnum.WECHAT_SWIPE.getCode();
        }
        if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
            return ZTKXPayTypeEnum.ALIPAY_SWIPE.getCode();
        }
        throw new UnsupportedOperationException("中投科信只支持微信和支付宝B2C支付");
    }

    private  String getTerminalInfoValue(Map<String, Object> transaction) {
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        Map<Object, Object> deviceInfo = new HashMap<>();
        deviceInfo.put(ZTKXBusinessFields.DEVICE_ID, terminalInfo.getId());
        deviceInfo.put(ZTKXBusinessFields.DEVICE_IP, terminalInfo.getIp());
        String terminalInfoValue = null;
        try {
            terminalInfoValue = JsonUtil.objectToJsonString(deviceInfo);
        } catch (MpayException e) {
            logger.error("ztkx terminalInfo to json error: {}", e.getMessage(), e);
        }
        return terminalInfoValue;
    }

    private ZTKXRequestBuilder getPayRequestBuilder(Map<String, Object> tradeParams, Map<String, Object> transaction, Map extraParams) {
        ZTKXRequestBuilder ztkxRequestBuilder = new ZTKXRequestBuilder();
        ztkxRequestBuilder.set(ZTKXBusinessFields.MERID, MapUtils.getString(tradeParams, ZTKX_PROVIDER_MCH_ID));
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        ztkxRequestBuilder.set(ZTKXBusinessFields.TRAN_AMT, amount);

        // 商品信息
        String subject = CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT));
        ztkxRequestBuilder.set(ZTKXBusinessFields.PRODUCT_LIST, getProductList(subject, amount));
        // 终端IP
        ztkxRequestBuilder.set(ZTKXBusinessFields.SPBILL_CREATE_IP, getTerminalIp(extraParams));
        // 订单超时时间，单位：分钟，不传默认30分钟
        ztkxRequestBuilder.set(ZTKXBusinessFields.ORDER_EXPIRE_TIME, ORDER_TIME_OUT_MINUTE);
        // 指定支付方式，不允许使用信用卡
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))) {
            ztkxRequestBuilder.set(ZTKXBusinessFields.LIMIT_PAY, "1");
        }
        // 花呗分期笔数
        String hbFqNum = BeanUtil.getPropString(transaction, "extended_params.extend_params.hb_fq_num");
        if (!StringUtils.isEmpty(hbFqNum)) {
            ztkxRequestBuilder.set(ZTKXBusinessFields.INSTALLMENT_NUM, hbFqNum);
            ztkxRequestBuilder.set(ZTKXBusinessFields.FEE_PERCENT, "0");
        }
        return ztkxRequestBuilder;
    }

    private static String getProductList(String subject, String amount) {
        // 商品信息
        Map<String, Object> productListMap = new HashMap<>();
        Map<String, Object> product = new HashMap<>();
        product.put(ZTKXBusinessFields.PRODUCT_NAME, subject);
        product.put(ZTKXBusinessFields.PRODUCT_NUM, "1");
        product.put(ZTKXBusinessFields.PRODUCT_AMT, amount);
        productListMap.put(ZTKXBusinessFields.PRODUCT_LIST, Arrays.asList(product));
        String value = null;
        try {
            value = JsonUtil.objectToJsonString(productListMap);
        } catch (MpayException e) {
            logger.error("ztkx bussinessParams productList to json error: {}", e.getMessage(), e);
        }
        return value;
    }

    /**
     * 预下单
     *
     * @param context
     * @param resume
     * @return
     */
    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        // 微信预下单
        if (Order.PAYWAY_WEIXIN == payway) {
            return wechatPreCreate(context, resume);
        }
        // 支付宝预下单
        if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
            return alipayPreCreate(context, resume);
        }
        // 银联预下单
        if (Order.PAYWAY_UNIONPAY == payway ) {
            return unionPayPreCreate(context, resume);
        }

        throw new UnsupportedOperationException("中投科信不支持预下单");
    }

    private String unionPayPreCreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        ZTKXRequestBuilder ztkxRequestBuilder = getPayRequestBuilder(tradeParams, transaction, extraParams);
        // 回调通知地址
        ztkxRequestBuilder.set(ZTKXBusinessFields.SERVER_URL, getNotifyUrl(notifyHost, context));
        // 添加交易类型
        ztkxRequestBuilder.set(ZTKXBusinessFields.PAY_TYPE, ZTKXPayTypeEnum.UNIONPAY_JS_PAYMENT.getCode());
        // 用户ID
        ztkxRequestBuilder.set(ZTKXBusinessFields.OPENID, MapUtil.getString(extraParams, Transaction.PAYER_UID));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS016.getCode(), ztkxRequestBuilder.build(), tradeParams, buildTranFlow(transaction, tradeParams), OP_PRECREATE, 1);
        } catch (Exception e) {
            log.error("call ztkx unionpay preCreate error: {}", e.getMessage(), e);
            transaction.put(Transaction.TRADE_NO, buildTranFlow(transaction, tradeParams));
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildUnionPayPreCreateResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        ZTKXRequestBuilder ztkxRequestBuilder = new ZTKXRequestBuilder();
        ztkxRequestBuilder.set(ZTKXBusinessFields.ORI_TRAN_CODE, getQueryTradeCode(context).getCode());
        String tradeNo = MapUtil.getString(transaction, Transaction.TRADE_NO);
        // 兼容如果没有tradeNo，则使用原交易流水号
        if (StringUtils.isEmpty(tradeNo)) {
            tradeNo = buildTranFlow(transaction, tradeParams);
        }
        ztkxRequestBuilder.set(ZTKXBusinessFields.ORI_TRAN_FLOW, tradeNo);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS008.getCode(), ztkxRequestBuilder.build(), tradeParams, null, OP_QUERY, 1);
        } catch (Exception e) {
            log.error("call ztkx query error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IN_PROG;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);
        return buildQueryResult(result, context, OP_QUERY);
    }

    /**
     * 获取查询的交易代码
     *
     * @param context
     * @return
     */
    private static ZTKXTradeCodeEnum getQueryTradeCode(TransactionContext context) {
        int type = MapUtil.getIntValue(context.getTransaction(), Transaction.TYPE);
        if (type == Transaction.TYPE_PAYMENT) {
            return ZTKXTradeCodeEnum.CAS016;
        }
        if (type == Transaction.TYPE_REFUND) {
            return ZTKXTradeCodeEnum.CAS006;
        }
        if (type == Transaction.TYPE_CANCEL) {
            return ZTKXTradeCodeEnum.CAS027;
        }

        throw new UnsupportedOperationException("中投科信不支持查询该交易类型");
    }

    /**
     * 查询退款结果
     *
     * @param context
     * @return
     */
    private String queryRefundResult(TransactionContext context) {
        String workflowStatus = query(context);
        // 退款工作流不识别Workflow.RC_IN_PROG，因此需要转换为Workflow.RC_RETRY
        return Workflow.RC_IN_PROG.equals(workflowStatus) ? Workflow.RC_RETRY : workflowStatus;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return queryRefundResult(context);
        }

        ZTKXRequestBuilder ztkxRequestBuilder = new ZTKXRequestBuilder();
        // 原交易流水号
        Map<String, Object> order = context.getOrder();
        ztkxRequestBuilder.set(ZTKXBusinessFields.ORI_TRAN_FLOW, MapUtil.getString(order, Transaction.TRADE_NO));
        // 中投子商户号
        ztkxRequestBuilder.set(ZTKXBusinessFields.MERID, MapUtils.getString(tradeParams, ZTKX_PROVIDER_MCH_ID));
        // 退款金额
        ztkxRequestBuilder.set(ZTKXBusinessFields.REFUND_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        // 退款明细，打开分账时必填，关闭分账功能后非必输,此入无效
        ztkxRequestBuilder.set(ZTKXBusinessFields.REFUND_LIST, "");
        // 是否支持现金账户(账户余额)退款
        ztkxRequestBuilder.set(ZTKXBusinessFields.ISSUPPORTCASH, "N");
        // 备注
        ztkxRequestBuilder.set(ZTKXBusinessFields.REMARK, "");

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS006.getCode(), ztkxRequestBuilder.build(), tradeParams, buildTranFlow(transaction, tradeParams), OP_REFUND, 1);
        } catch (Exception e) {
            log.error("call ztkx refund error: {}", e.getMessage(), e);
            transaction.put(Transaction.TRADE_NO, buildTranFlow(transaction, tradeParams));
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            // 设置已经调过退款接口的标识
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context, OP_REFUND);
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("中投科信暂不支持撤单");
    }

    /**
     * 微信预下单
     *
     * @param context
     * @param resume
     * @return
     */
    public String wechatPreCreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map extended = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);

        ZTKXRequestBuilder ztkxRequestBuilder = getPayRequestBuilder(tradeParams, transaction, extraParams);
        // 回调通知地址
        ztkxRequestBuilder.set(ZTKXBusinessFields.SERVER_URL, getNotifyUrl(notifyHost, context));
        // 添加交易类型
        ztkxRequestBuilder.set(ZTKXBusinessFields.PAY_TYPE, ZTKXPayTypeEnum.WECHAT_PUBLIC_MINI.getCode());
        // 付款人id(openId)
        ztkxRequestBuilder.set(ZTKXBusinessFields.OPENID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        // 设置微信subAppId
        String wechatSubAppId = MapUtils.getString(extended, ProtocolFields.SUB_APP_ID);
        if (StringUtil.empty(wechatSubAppId)) {
            // 未上送subAppId, 则使用默认值
            int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
            if (Order.SUB_PAYWAY_MINI == subPayway) {
                wechatSubAppId = MapUtils.getString(tradeParams, ZTKX_WECHAT_MINI_SUB_APP_ID);
            } else {
                wechatSubAppId = MapUtils.getString(tradeParams, ZTKX_WECHAT_SUB_APP_ID);
            }
            log.info("中投科信微信预下单未上送subAppId, 使用默认值, defaultSubAppId={}, subPayway={}", wechatSubAppId, subPayway);
        }
        if (ApolloConfigurationCenterUtil.isSupportZtkxWechatSubAppid()) {
            // 测试环境关闭
            ztkxRequestBuilder.set(ZTKXBusinessFields.SUB_APPID, wechatSubAppId);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS016.getCode(), ztkxRequestBuilder.build(), tradeParams, buildTranFlow(transaction, tradeParams), OP_PRECREATE, 1);
        } catch (Exception e) {
            log.error("call ztkx wechat preCreate error: {}", e.getMessage(), e);
            transaction.put(Transaction.TRADE_NO, buildTranFlow(transaction, tradeParams));
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildWechatPreCreateResult(result, context);
    }

    /**
     * 支付宝预下单
     *
     * @param context
     * @param resume
     * @return
     */
    public String alipayPreCreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        ZTKXRequestBuilder ztkxRequestBuilder = getPayRequestBuilder(tradeParams, transaction, extraParams);
        // 支付宝用户id
        ztkxRequestBuilder.set(ZTKXBusinessFields.OPENID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        // 支付宝subappid
        ztkxRequestBuilder.set(ZTKXBusinessFields.SUB_APPID, MapUtils.getString(tradeParams, TransactionParam.ZTKX_ALIPAY_SUB_MCH_ID));
        // 回调通知地址
        ztkxRequestBuilder.set(ZTKXBusinessFields.SERVER_URL, getNotifyUrl(notifyHost, context));
        // 添加交易类型
        ztkxRequestBuilder.set(ZTKXBusinessFields.PAY_TYPE, ZTKXPayTypeEnum.ALIPAY_SERVICE_MINI.getCode());

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS016.getCode(), ztkxRequestBuilder.build(), tradeParams, buildTranFlow(transaction, tradeParams), OP_PRECREATE, 1);
        } catch (Exception e) {
            log.error("call ztkx alipay preCreate error: {}", e.getMessage(), e);
            transaction.put(Transaction.TRADE_NO, buildTranFlow(transaction, tradeParams));
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildAlipayPreCreateResult(result, context);
    }

    @Override
    public String explainNotification(Map<String, Object> notification) {
        log.info("处理中投科信回调通知");
        TransactionContext context = (TransactionContext) notification.get(TransactionContext.class.getName());
        notification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT != type) {
            return null;
        }

        //默认直接再查询一遍
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }


    private static String getTerminalIp(Map extraParams) {
        // 设置ip：优先取终端上报ip，再取终端上报ip, 兜底localhost ip
        String userIp = MapUtil.getString(extraParams, Transaction.SQB_IP);
        String clientIp = MapUtil.getString(extraParams, Transaction.CLIENT_IP);
        String ip = !StringUtil.empty(clientIp)
                ? clientIp
                : (StringUtil.empty(userIp) ? UpayUtil.getLocalHostIp() : userIp);
        return ip;
    }

    /**
     * 构建支付结果
     *
     * @param result
     * @param context
     * @return
     */
    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        // 设置通道、支付源订单号
        setTradeNoBuyerInfoIfExists(result, context, OP_PAY);

        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> headMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.HEAD);
        String responseCode = MapUtil.getString(headMap, ZTKXCommonFields.RESPCODE);
        if (ZTKXRespCodeEnum.isSuccess(responseCode)) {
            // 需要重新查询一次获取支付源订单号、通道完成时间
            return query(context);
        }
        if (ZTKXRespCodeEnum.isProcessing(responseCode)) {
            return Workflow.RC_IN_PROG;
        }
        if (ZTKXRespCodeEnum.isFailed(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    /**
     * 构建微信预下单结果
     *
     * @param result
     * @param context
     * @return
     */
    private String buildWechatPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        // 设置通道订单号
        setTradeNoBuyerInfoIfExists(result, context, OP_PRECREATE);

        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);

        // 构建微信参数
        Map<String, Object> wapPayRequest = new HashMap<>();
        String string = MapUtils.getString(bodyMap, ZTKXBusinessFields.PAY_INFO);
        try {
            wapPayRequest = JsonUtil.jsonStringToObject(string, Map.class);
        } catch (Exception e) {
            logger.error("微信预下单返回参数解析错误: {}", e.getMessage(), e);
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapPayRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }


    /**
     * 构建支付宝预下单结果
     *
     * @param result
     * @param context
     * @return
     */
    private String buildUnionPayPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        setTradeNoBuyerInfoIfExists(result, context, OP_PRECREATE);
        // 构建云闪付地址
        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
        String path = MapUtils.getString(bodyMap, ZTKXBusinessFields.PAY_INFO);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(ResponseFields.REDIRECTURL, path));

        return Workflow.RC_CREATE_SUCCESS;
    }


    /**
     * 构建支付宝预下单结果
     *
     * @param result
     * @param context
     * @return
     */
    private String buildAlipayPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        // 设置通道订单号
        setTradeNoBuyerInfoIfExists(result, context, OP_PRECREATE);

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);

        // 设置支付宝订单号
        String alipayOrderNo = MapUtils.getString(bodyMap, ZTKXBusinessFields.PAY_INFO);
        if (!StringUtils.isEmpty(alipayOrderNo)) {
            // 中投科信返回的支付宝交易号增加了两位前缀，保存时需要去掉
            alipayOrderNo = alipayOrderNo.substring(2);
        }
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (!StringUtils.isEmpty(alipayOrderNo)) {
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, alipayOrderNo));
        }
        if (StringUtils.isEmpty(MapUtils.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO)) && !StringUtils.isEmpty(alipayOrderNo)) {
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, alipayOrderNo);
        }
        return Workflow.RC_CREATE_SUCCESS;
    }

    /**
     * 构建查询结果
     *
     * @param result
     * @param context
     * @param op
     * @return
     */
    private String buildQueryResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        // 设置付款人id、渠道订单号等
        setTradeNoBuyerInfoIfExists(result, context, op);

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
        String orderStatusCode = MapUtil.getString(bodyMap, ZTKXBusinessFields.ORI_TRANSTATUS);
        ZTKXOrderQueryStatusEnum ztkxOrderStatusEnum = ZTKXOrderQueryStatusEnum.getByCode(orderStatusCode);
        switch (ztkxOrderStatusEnum) {
            case FAILED:
                return Workflow.RC_TRADE_CANCELED;
            case SUCCESS:
                int type = MapUtil.getInteger(context.getTransaction(), Transaction.TYPE);
                if (Transaction.TYPE_PAYMENT == type) {
                    // 解析交易金额、完成时间
                    resolveQueryPayFund(context, result);
                    return Workflow.RC_PAY_SUCCESS;
                }
                if (Transaction.TYPE_REFUND == type) {
                    resolveQueryPayFund(context, result);
                    // 解析退款交易金额
                    resolveRefundFund(context);
                    return Workflow.RC_REFUND_SUCCESS;
                }
                if (Transaction.TYPE_CANCEL == type) {
                    return Workflow.RC_CANCEL_SUCCESS;
                }
                return Workflow.RC_CANCEL_SUCCESS;
            case PROCESSING:
            default:
                return Workflow.RC_IN_PROG;
        }
    }

    private void resetProviderFee(Map<String, Object> transaction, Map<String, Object> bodyMap) {
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        // 补贴手续费
        String subsidyFeeTotal = MapUtils.getString(bodyMap, ZTKXBusinessFields.FEE_SUBSIDY_AMT_TOTAL);
        long subsidyFee = StringUtils.yuan2cents(StringUtils.isEmpty(subsidyFeeTotal) ? "0" : subsidyFeeTotal);
        // 商户手续费（包含补贴手续费）
        String merchantFee = MapUtils.getString(bodyMap, ZTKXBusinessFields.FEE_AMT);
        long fee = StringUtils.yuan2cents(StringUtils.isEmpty(merchantFee)? "0" : merchantFee);
        // 设置真实手续费 = 商户手续费 - 补贴手续费
        Map<String, Object> tradeParams = getTradeParams(transaction);
        tradeParams.put(TransactionParam.FEE, Math.max(fee - subsidyFee, 0));
        // 设置补贴手续费
        if (subsidyFee > 0 || extraOutFields.containsKey(Transaction.QUOTA_FEE)) {
            extraOutFields.put(Transaction.QUOTA_FEE, subsidyFee);
        }
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        // 设置付款人id、渠道订单号等
        setTradeNoBuyerInfoIfExists(result, context, op);

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
        Map extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);
        ZTKXRefundStatusEnum orderStatus = ZTKXRefundStatusEnum.getByCode(MapUtil.getString(bodyMap, ZTKXBusinessFields.REFUND_STATUS));
        switch (orderStatus) {
            case FAILED:
                return Workflow.RC_ERROR;
            case SUCCESS:
                Map<String, Object> transaction = context.getTransaction();
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                // 返还补贴手续费
                String subsidyFeeTotal = MapUtils.getString(bodyMap, ZTKXBusinessFields.FEE_SUBSIDY_AMT_TOTAL);
                long subsidyFee = StringUtils.yuan2cents(StringUtils.isEmpty(subsidyFeeTotal) ? "0" : subsidyFeeTotal);
                // 返还商户手续费（包含补贴手续费）
                String merchantFee = MapUtils.getString(bodyMap, "returnFee");
                long fee = StringUtils.yuan2cents(StringUtils.isEmpty(merchantFee)? "0" : merchantFee);
                // 设置真实手续费 = 商户手续费 - 补贴手续费
                Map<String, Object> tradeParams = getTradeParams(transaction);
                tradeParams.put(TransactionParam.FEE, Math.max(fee - subsidyFee, 0));
                // 设置补贴手续费
                if (subsidyFee > 0 || extraOutFields.containsKey(Transaction.QUOTA_FEE)) {
                    extraOutFields.put(Transaction.QUOTA_FEE, subsidyFee);
                }

                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            case PROCESSING:
            default:
                // 须进行退款查询，从而确定最终的退款情况
                // 设置已经退款过的标识
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return queryRefundResult(context);
        }
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    private void resolveQueryPayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
        String channelFinishDate = MapUtils.getString(bodyMap, ZTKXBusinessFields.CHL_FINISHTIME);

        // 记录通道交易完成时间
        if (Objects.isNull(transaction.get(Transaction.CHANNEL_FINISH_TIME))) {
            if (StringUtils.empty(channelFinishDate)) {
                // 通道没返回交易完成时间，则设置为当前时间
                log.warn("通道未返回交易完成时间, 则使用当前时间");
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            } else {
                LocalDateTime finishTime = LocalDateTimeUtil.parse(channelFinishDate, LocalDateTimeUtil.YYYY_MM_DD_HH_MM_SS_SSS);
                long epochMs = finishTime != null ? toEpochMs(finishTime) : System.currentTimeMillis();
                transaction.put(Transaction.CHANNEL_FINISH_TIME, epochMs);
            }
        }
        // 设置通道交易金额:交易金额-免充值优惠金额 单位：元
        long amount = StringUtils.yuan2cents(MapUtils.getString(bodyMap, ZTKXBusinessFields.SETTLEA_MT));
        transaction.put(Transaction.PAID_AMOUNT, amount);
        transaction.put(Transaction.RECEIVED_AMOUNT, amount);
        // 设置通道侧手续费
        resetProviderFee(transaction, bodyMap);
    }

    /**
     * 设置付款人id、渠道订单号等
     *
     * @param result
     * @param context
     * @param opFlag
     */
    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String opFlag) {
        if (MapUtils.isEmpty(result)) {
            return;
        }
        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
        Map<String, Object> headMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.HEAD);
        Map<String, Object> transaction = context.getTransaction();

        String buyerUid = null;
        String tradeNo = null; // 通道订单号
        String channelNo = null;// 渠道订单号（微信或支付宝订单号）
        if (OP_PAY.equals(opFlag)) {
            tradeNo = MapUtils.getString(headMap, ZTKXCommonFields.TRANFLOW);
        } else if (OP_QUERY.equals(opFlag)) {
            tradeNo = MapUtils.getString(bodyMap, ZTKXBusinessFields.ORI_TRAN_FLOW);
            buyerUid = MapUtils.getString(bodyMap, ZTKXBusinessFields.OPEN_ID);
            channelNo = MapUtils.getString(bodyMap, ZTKXBusinessFields.CHL_ORDER_ID);
        } else if (OP_REFUND.equals(opFlag)) {
            tradeNo = MapUtils.getString(headMap, ZTKXCommonFields.TRANFLOW);
        } else if (OP_PRECREATE.equals(opFlag)) {
            tradeNo = MapUtils.getString(headMap, ZTKXCommonFields.TRANFLOW);
        }

        if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_UID)) && !StringUtil.empty(buyerUid)) {
            transaction.put(Transaction.BUYER_UID, buyerUid);
        }
        if (StringUtils.isEmpty(MapUtils.getString(transaction, Transaction.TRADE_NO)) && !StringUtils.isEmpty(tradeNo)) {
            transaction.put(Transaction.TRADE_NO, tradeNo);
        }
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtils.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO)) && !StringUtils.isEmpty(channelNo)) {
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelNo);
        }
    }

    /**
     * 设置通道返回的错误信息
     *
     * @param result
     * @param context
     * @param opFlag
     */
    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String opFlag) {
        Map<String, Object> map = new LinkedHashMap<>();
        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        String respCode = errorInfo.getMiddle();
        String respMsg = errorInfo.getRight();
        // 查询时，返回的错误码和错误信息是来自原交易的错误信息
        if (isSuccess && Objects.equals(opFlag, OP_QUERY)) {
            Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
            Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
            Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
            respCode = MapUtil.getString(bodyMap, ZTKXBusinessFields.ORI_RESPCODE);
            respMsg = MapUtil.getString(bodyMap, ZTKXBusinessFields.ORI_RESPMSG);
        }

        map.put(ZTKXCommonFields.RESPCODE, respCode);
        map.put(ZTKXCommonFields.RESPMSG, respMsg);
        setTransactionContextErrorInfo(context.getTransaction(), opFlag, map, isSuccess, respCode, respMsg);
    }

    private Triple<Boolean, String, String> getResponseErrorInfo(Map<String, Object> result) {
        boolean isSuccess = true;
        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> headMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.HEAD);
        String respCode = MapUtil.getString(headMap, ZTKXCommonFields.RESPCODE);
        String respMsg = MapUtil.getString(headMap, ZTKXCommonFields.RESPMSG);
        if (ZTKXRespCodeEnum.isFailed(respCode)) {
            isSuccess = false;
        }
        return Triple.of(isSuccess, respCode, respMsg);
    }

    protected Map<String, Object> retryIfNetworkException(String method, Map<String, Object> request,
                                                          Map<String, Object> tradeParams, String tranFlow, String opFlag, int retryTimes) throws Exception {
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), opFlag);
        String sqbEncPrivateKey = serviceFacade.getRsaKeyDataById(MapUtil.getString(tradeParams, ZTKX_SQB_ENC_PRIVATE_KEY));
        String sqbSignPrivateKey = serviceFacade.getRsaKeyDataById(MapUtil.getString(tradeParams, ZTKX_SQB_SIGN_PRIVATE_KEY));
        String sqbEncPrivateKeyPwd = serviceFacade.getRsaKeyDataById(MapUtil.getString(tradeParams, TransactionParam.ZTKX_SQB_SIGN_PRIVATE_KEY_PWD));
        String sqbSignPrivateKeyPwd = serviceFacade.getRsaKeyDataById(MapUtil.getString(tradeParams, TransactionParam.ZTKX_SQB_SIGN_PRIVATE_KEY_PWD));
        String ztkxEncPublicKey = serviceFacade.getRsaKeyDataById(MapUtil.getString(tradeParams, ZTKX_PROVIDER_ENC_PUBLIC_KEY));
        String ztkxVerSignPublicKey = serviceFacade.getRsaKeyDataById(MapUtil.getString(tradeParams, ZTKX_PROVIDER_SIGN_PUBLIC_KEY));
        Map<String, Map<String, Object>> wrapperRequest = wrapperRequest(request, MapUtils.getString(tradeParams, PLATFORM_MCH_ID), method, tranFlow);

        return retryIfNetworkException(() -> ztkxClient.call(gatewayUrl, wrapperRequest,
                        sqbSignPrivateKey, sqbSignPrivateKeyPwd, sqbEncPrivateKey, sqbEncPrivateKeyPwd, ztkxEncPublicKey, ztkxVerSignPublicKey),
                log, retryTimes, opFlag, getName());
    }


    private static Map<String, Map<String, Object>> wrapperRequest(Map<String, Object> params, String ztkxMchId, String tradeCode, String tranFlow) {
        Map<String, Object> head = new HashMap<>();
        head.put(ZTKXCommonFields.MERTRANDATE, ZTKXHeadUtil.getDate());
        head.put(ZTKXCommonFields.MERTRANTIME, ZTKXHeadUtil.getTime());
        head.put(ZTKXCommonFields.PLATMERID, ztkxMchId); //商户号
        head.put(ZTKXCommonFields.RESPCODE, "");
        head.put(ZTKXCommonFields.RESPMSG, "");
        head.put(ZTKXCommonFields.TRANCODE, tradeCode);
        // 交易流水号，不为空时，以传入的为准
        String tranflow = tranFlow;
        if (StringUtils.isEmpty(tranFlow)) {
            tranflow = ZTKXHeadUtil.getTranFlow(ztkxMchId);
        }
        head.put(ZTKXCommonFields.TRANFLOW, tranflow);
        head.put(ZTKXCommonFields.ZTPAGE, Boolean.FALSE); //是否跳转页面

        Map<String, Map<String, Object>> data = new HashMap<>();
        data.put(ZTKXProtocolFields.HEAD, head);
        data.put(ZTKXProtocolFields.BODY, params);
        return data;
    }

    @Override
    public Map<String,Object> queryUserInfo(Map<String,Object> transaction) {
        Map<String,Object> tradeParams= getTradeParams(transaction);
        Map<String,Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            return null;
        }

        ZTKXRequestBuilder ztkxRequestBuilder = new ZTKXRequestBuilder();
        //授权码
        ztkxRequestBuilder.set(ZTKXBusinessFields.USER_AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        //银联支付标识
        ztkxRequestBuilder.set(ZTKXBusinessFields.APP_UP_IDENTIFIER, MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ZTKXTradeCodeEnum.CAS059.getCode(), ztkxRequestBuilder.build(), tradeParams, null, OP_QUERY, 1);
        } catch (Exception e) {
            log.error("call ztkx unionpay userid error: {}", e.getMessage(), e);
            return CollectionUtil.hashMap(ZTKXCommonFields.RESPMSG, e.getMessage());
        }
        if (org.apache.commons.collections.MapUtils.isEmpty(result)) {
            return null;
        }

        Map<String, Object> messageMap = MapUtil.getMap(result, ZTKXProtocolFields.MESSAGE);
        Map<String, Object> dataMap = MapUtil.getMap(messageMap, ZTKXProtocolFields.DATA);
        Map<String, Object> headMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.HEAD);
        Map<String, Object> bodyMap = MapUtil.getMap(dataMap, ZTKXProtocolFields.BODY);
        String respCode = org.apache.commons.collections.MapUtils.getString(headMap, ZTKXCommonFields.RESPCODE);
        if (ZTKXRespCodeEnum.isFailed(respCode)) {
            return null;
        }
        return CollectionUtil.hashMap(BusinessFields.USER_ID, MapUtil.getString(bodyMap, BusinessFields.USER_ID));
    }

    public String refundQuery(TransactionContext context) {
        return queryRefundResult(context);
    }
}
