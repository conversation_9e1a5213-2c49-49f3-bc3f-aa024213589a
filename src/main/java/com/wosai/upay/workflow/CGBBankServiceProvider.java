package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.cgbbank.*;
import com.wosai.mpay.api.cibbank.CIBBankConstants;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.util.SM2Util;
import com.wosai.mpay.util.SM4Util;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.RedisService;
import com.wosai.upay.util.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description CGBBankServiceProvider
 * @Date 2021/6/7 10:45 AM
 */
public class CGBBankServiceProvider extends AbstractServiceProvider{

    public static final Logger logger = LoggerFactory.getLogger(CGBBankServiceProvider.class);
    public static final String NAME = "provider.cgbbank";
    private static final long defaultTimeExpire = DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
    private static final long b2cTimeExpire = B2C_TIME_EXPIRE_MINUTE * 60 * 1000;

    protected String notifyHost;

    @Autowired
    private CGBBankClient cgbBankClient;
    @Autowired
    private RedisService redisService;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider(){
        return Order.PROVIDER_CGBBANK;
    }

    public CGBBankServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat(CGBBankConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(CGBBankBusinessFields.TOTAL_FEE, CGBBankBusinessFields.FEE_TYPE));
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CGBBANK_TRADE_PARAMS);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return !Objects.isNull(getTradeParams(transaction));
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);

        //签名密钥
        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRIVATE_KEY);
        //签名公钥
        String publicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PUBLIC_KEY);
        //广发加密公钥
        String cgbPublicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_CGB_PUBLIC_KEY);
        //广发应用ID
        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_APP_ID);

        CGBBankRequestBuilder builder = getDefaultRequestBuilder(context);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.MICRO_PAY);
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_MICROPAY);
        //二级渠道
        builder.bizBodySet(CGBBankBusinessFields.SUB_CHANNEL, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_SUB_CHANNEL));
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //设备号，需要进行电子围栏校验使用CGBBANK_TERMINAL_ID
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_INFO, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_TERMINAL_ID));
        //商品描述
        builder.bizBodySet(CGBBankBusinessFields.BODY, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //总金额
        builder.bizBodySet(CGBBankBusinessFields.TOTAL_FEE, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        //货币类型, 默认CNY
        builder.bizBodySet(CGBBankBusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        //终端IP
        String termIp = !StringUtils.isEmpty(BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP)) ? BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP) : UpayUtil.getLocalHostIp();
        builder.bizBodySet(CGBBankBusinessFields.MCH_CREATE_IP, termIp);
        //授权码
        builder.bizBodySet(CGBBankBusinessFields.AUTH_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        long start = System.currentTimeMillis();
        //订单生成时间
        builder.bizBodySet(CGBBankBusinessFields.TIME_START, formatTimeString(start));
        //最晚付款时间
        builder.bizBodySet(CGBBankBusinessFields.TIME_EXPIRE, formatTimeString(start + b2cTimeExpire));
        //经纬度
        String latitude = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_LATITUDE);
        String longitude = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_LONGITUDE);
        Map<String, Object> poi = GeoUDFUtil.randomLongAndLat(Double.parseDouble(longitude), Double.parseDouble(latitude));
        latitude = BeanUtil.getPropString(poi, TransactionParam.CGBBANK_LATITUDE);
        longitude = BeanUtil.getPropString(poi, TransactionParam.CGBBANK_LONGITUDE);
        String location = getLocation(latitude, longitude);
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_LOCATION, location);
        //商品标记
        builder.bizBodySet(CGBBankBusinessFields.GOODS_TAG, BeanUtil.getPropString(tradeParams, TransactionParam.GOODS_TAG));
        //终端类型 POS
        builder.bizBodySet(CGBBankBusinessFields.TERM_TYPE, CGBBankConstants.TERM_TYPE_POS);
        //交易发起方式 如果是线下（POS,收银机、扫码盒子等线下设备），则选择现场，否则自助
        builder.bizBodySet(CGBBankBusinessFields.LAUNCH_MODEL, CGBBankConstants.LAUNCH_MODEL_SCENE);

        if (payway == Order.PAYWAY_WEIXIN){
            /* 微信上送信息 */
            //微信公众账号id
            builder.bizBodySet(CGBBankBusinessFields.SUB_APP_ID, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_WEIXIN_SUB_APP_ID));
        }
        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder, BeanUtil.getPropInt(transaction, Transaction.PAYWAY));

        Map<String,Object> result;
        try {
            result = cgbBankClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), appId, getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), getPrivateKeyContent(cgbPublicKey));
        } catch (Exception ex) {
            logger.error("failed to call cgbbank pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return (ex instanceof MpayApiConnectError || ex instanceof JsonProcessingException) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        Map<String, Object> resultBody = MapUtils.getMap(result, CGBBankProtocolFields.BODY);
        setTransactionContextErrorInfo(resultBody, context, OP_PAY);

        return buildPayResult(resultBody, context);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        //签名密钥
        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRIVATE_KEY);
        //签名公钥
        String publicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PUBLIC_KEY);
        //广发加密公钥
        String cgbPublicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_CGB_PUBLIC_KEY);
        //广发应用ID
        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_APP_ID);

        CGBBankRequestBuilder builder = getDefaultRequestBuilder(context);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.CREATE_ORDER);
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_JSPAY);
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //二级渠道
        builder.bizBodySet(CGBBankBusinessFields.SUB_CHANNEL, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_SUB_CHANNEL));
        //支付类型  公众号、小程序、服务窗支付
        builder.bizBodySet(CGBBankBusinessFields.PAY_TYPE, CGBBankConstants.PAY_TYPE_OF);
        //支付渠道
        if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway){
            builder.bizBodySet(CGBBankBusinessFields.PAY_WAY, CGBBankConstants.PAY_WAY_ALIPAY);
        } else if (Order.PAYWAY_WEIXIN == payway){
            builder.bizBodySet(CGBBankBusinessFields.PAY_WAY, CGBBankConstants.PAY_WAY_WXPAY);
            if (Order.SUB_PAYWAY_WAP == subPayway){
                builder.bizBodySet(CGBBankBusinessFields.APPLETS_TYPE, CGBBankConstants.APPLETS_TYPE_SUBAPP);
            } else if (Order.SUB_PAYWAY_MINI == subPayway){
                builder.bizBodySet(CGBBankBusinessFields.APPLETS_TYPE, CGBBankConstants.APPLETS_TYPE_APPLETS);
            }
            //公众账号或小程序ID
            builder.bizBodySet(CGBBankBusinessFields.SUB_APP_ID, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_WEIXIN_SUB_APP_ID));
            //微信直连正式的交易，小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
            //门店码支付的sub_app_id为weixin_sub_appid, 小程序的sub_app_id为weixin_mini_sub_appid
            if(Order.SUB_PAYWAY_MINI == subPayway){
                String miniSubAppId = BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
                if(!StringUtil.empty(miniSubAppId)){
                    builder.bizBodySet(ProtocolFields.SUB_APP_ID, miniSubAppId);
                }
            }
        }
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        //用户标识
        builder.bizBodySet(CGBBankBusinessFields.OPEN_ID, payerUid);
        //设备号
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_INFO, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRE_TERMINAL_ID));
        //门店编号
        builder.bizBodySet(CGBBankBusinessFields.OP_SHOP_ID, BeanUtil.getPropString(transaction, Transaction.KEY_STORE_SN));
        //商品描述
        builder.bizBodySet(CGBBankBusinessFields.BODY, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //总金额
        builder.bizBodySet(CGBBankBusinessFields.TOTAL_FEE, BeanUtil.getPropInt(transaction, Transaction.EFFECTIVE_AMOUNT));
        //货币类型, 默认CNY
        builder.bizBodySet(CGBBankBusinessFields.FEE_TYPE, getTradeCurrency(transaction));
        //终端IP
        String termIp = !StringUtils.isEmpty(BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP)) ? BeanUtil.getPropString(extraParams, Transaction.CLIENT_IP) : UpayUtil.getLocalHostIp();
        builder.bizBodySet(CGBBankBusinessFields.MCH_CREATE_IP, termIp);
        long start = System.currentTimeMillis();
        //订单生成时间
        builder.bizBodySet(CGBBankBusinessFields.TIME_START, formatTimeString(start));
        //支付有效时间（秒为单位）
        builder.bizBodySet(CGBBankBusinessFields.PAYMENT_VALID_TIME, formatTimeString(start + defaultTimeExpire));
        //商品标记
        builder.bizBodySet(CGBBankBusinessFields.GOODS_TAG, BeanUtil.getPropString(tradeParams, TransactionParam.GOODS_TAG));
        //终端类型
        builder.bizBodySet(CGBBankBusinessFields.TERM_TYPE, CGBBankConstants.TERM_TYPE_QRCODE);
        //交易发起方式
        builder.bizBodySet(CGBBankBusinessFields.LAUNCH_MODEL, CGBBankConstants.LAUNCH_MODEL_SELF);
        //是否限制信用卡
        limitCredit(builder, transaction);
        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder, BeanUtil.getPropInt(transaction, Transaction.PAYWAY));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.bizBodySet(CGBBankBusinessFields.NOTIFY_URL, notifyUrl);
        }
        Map<String,Object> result;
        try {
            result = cgbBankClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE), builder.build(), appId, getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), getPrivateKeyContent(cgbPublicKey));
        } catch (Exception ex) {
            logger.error("failed to call cgbbank precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return (ex instanceof MpayApiConnectError || ex instanceof JsonProcessingException) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        Map<String, Object> resultBody = MapUtils.getMap(result, CGBBankProtocolFields.BODY);
        setTransactionContextErrorInfo(resultBody, context, OP_PRECREATE);

        return buildPreCreateResult(resultBody, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        //签名密钥
        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRIVATE_KEY);
        //签名公钥
        String publicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PUBLIC_KEY);
        //广发加密公钥
        String cgbPublicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_CGB_PUBLIC_KEY);
        //广发应用ID
        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_APP_ID);

        CGBBankRequestBuilder builder = getDefaultRequestBuilder(context);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.REVERSE);
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_REVERSE);
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //商户撤销订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_CANCEL_NO, BeanUtil.getPropString(transaction, Transaction.TSN));

        Map<String, Object> result;

        try {
            result = cgbBankClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), builder.build(), appId, getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), getPrivateKeyContent(cgbPublicKey));
        } catch (Exception ex) {
            logger.error("failed to call cgbbank cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return (ex instanceof MpayApiConnectError || ex instanceof JsonProcessingException) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        Map<String, Object> resultBody = MapUtils.getMap(result, CGBBankProtocolFields.BODY);
        setTransactionContextErrorInfo(resultBody, context, OP_CANCEL);

        return buildCancelResult(resultBody, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> result = doQuery(context);
        Map<String, Object> resultBody = MapUtils.getMap(result, CGBBankProtocolFields.BODY);
        setTransactionContextErrorInfo(resultBody, context, OP_QUERY);
        return buildQueryResult(resultBody, context);
    }

    private Map doQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        //签名密钥
        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRIVATE_KEY);
        //签名公钥
        String publicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PUBLIC_KEY);
        //广发加密公钥
        String cgbPublicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_CGB_PUBLIC_KEY);
        //广发应用ID
        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_APP_ID);

        CGBBankRequestBuilder builder = getDefaultRequestBuilder(context);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.QUERY_ORDER);
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_QUERY);
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));

        Map<String,Object> result = null;
        try {
            result = cgbBankClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), appId, getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), getPrivateKeyContent(cgbPublicKey));
        } catch (Exception ex) {
            logger.error("failed to call cgbbank query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
        }

        return result;
    }
    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long refundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        return doRefund(context, OP_REFUND, refundFee);
    }

    private String doRefund(TransactionContext context, String opFlag, long refundFee){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        //签名密钥
        String privateKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRIVATE_KEY);
        //签名公钥
        String publicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PUBLIC_KEY);
        //广发加密公钥
        String cgbPublicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_CGB_PUBLIC_KEY);
        //广发应用ID
        String appId = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_APP_ID);

        CGBBankRequestBuilder builder = getDefaultRequestBuilder(context);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.REFUND);
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_REFUND);
        //平台订单号(平台交易号)
        builder.bizBodySet(CGBBankBusinessFields.TRANSACTION_ID, BeanUtil.getPropString(order, Order.TRADE_NO));
        //商户退款订单号, 同个退款单号多次请求，平台当一个单处理，只会退一次款。如果出现退款不成功，请采用原退款单号重新发起，避免出现重复退款。
        builder.bizBodySet(CGBBankBusinessFields.OUT_REFUND_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //总金额
        builder.bizBodySet(CGBBankBusinessFields.TOTAL_FEE, BeanUtil.getPropInt(order, Order.EFFECTIVE_TOTAL));
        //退款金额
        builder.bizBodySet(CGBBankBusinessFields.REFUND_FEE,  refundFee);
        //操作员, 默认商户号
        builder.bizBodySet(CGBBankBusinessFields.OP_USER_ID, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PROVIDER_MCH_ID));

        Map<String, Object> result;
        try {
            result = cgbBankClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), appId, getPrivateKeyContent(privateKey), getPrivateKeyContent(publicKey), getPrivateKeyContent(cgbPublicKey));
        } catch (Exception ex) {
            logger.error("failed to call cibbank refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> resultBody = MapUtils.getMap(result, CGBBankProtocolFields.BODY);
        setTransactionContextErrorInfo(resultBody, context, opFlag);

        return buildRefundResult(resultBody, context);
    }

    @SneakyThrows
    @Override
    public String explainNotification(Map<String, Object> providerNotification) {

        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());

        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        //签名密钥
        String privateKey = MapUtils.getString(tradeParams, TransactionParam.CGBBANK_PRIVATE_KEY);
        //广发加密公钥
        String cgbPublicKey = BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_CGB_PUBLIC_KEY);
        String encryptKey = MapUtils.getString(providerNotification, CGBBankProtocolFields.ENCRYPT_KEY);
        String body = MapUtils.getString(providerNotification, CGBBankProtocolFields.BODY);

        String keyToEncrypt = new String(SM2Util.decrypt(Base64.getDecoder().decode(getPrivateKeyContent(privateKey)), Base64.getDecoder().decode(encryptKey)), CGBBankConstants.ENCODING);
        String reqDecryptStr = new String(SM4Util.decryptCBC(Base64.getDecoder().decode(body), keyToEncrypt.getBytes(), keyToEncrypt.getBytes()), CGBBankConstants.ENCODING);

        Map request = JSONObject.parseObject(reqDecryptStr, Map.class);

        //处理返回结果
        Map<String, Object> notifyResult = new HashMap<>();
        Map<String, Object> header = com.wosai.pantheon.util.MapUtil.getMap(request, CGBBankProtocolFields.HEADER);
        header.put(CGBBankProtocolFields.RESPONSE_TIME, dateFormat.format(new Date()));
        notifyResult.put(CGBBankProtocolFields.HEADER, com.wosai.pantheon.util.MapUtil.getMap(request, "Header"));
        notifyResult.put(CGBBankProtocolFields.BODY, ImmutableMap.of("errorCode", "SUCCESS", "errorMsg", ""));


        // 对响应报文加密
        String responseEncryptJson = SM4Util.gfEncrypt(JSONObject.toJSONString(notifyResult), keyToEncrypt);
        String responseSignature = SM2Util.signature(JSONObject.toJSONString(notifyResult), Base64.getDecoder().decode(getPrivateKeyContent(privateKey)),  CGBBankConstants.ENCODING);
        String keyToEncryptResult = SM2Util.sm2EncryptString(keyToEncrypt, Base64.getDecoder().decode(getPrivateKeyContent(cgbPublicKey)), CGBBankConstants.ENCODING);

        Map<String, Object> copsResponseEntity = new HashMap<>();
        copsResponseEntity.put("responseStr", responseEncryptJson);
        Map<String, Object> respHeader = ImmutableMap.of(CGBBankProtocolFields.ENCRYPT_KEY, keyToEncryptResult, CGBBankProtocolFields.SIGNATURE, responseSignature, CGBBankProtocolFields.SIGN_TYPE, "SM2", CGBBankProtocolFields.CONTENT_TYPE, "application/json;charset=UTF-8", CGBBankProtocolFields.ENCRYPT_TYPE, "SM4");
        copsResponseEntity.put("responseHeader", respHeader);
        providerNotification.put("copsResponseEntity", copsResponseEntity);
        logger.info("providerNotification is {}", providerNotification);

        long type = com.wosai.pantheon.util.MapUtil.getLongValue(context.getTransaction(), Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }
        //处理回调
        Map<String, Object> requestBody = MapUtils.getMap(request, CGBBankProtocolFields.BODY);
        String CGBBANK_TRADE_STATUS = String.format("%s.%s", CGBBankProtocolFields.BODY, CGBBankResponseFields.TRADE_STATE);
        String tradeState = BeanUtil.getPropString(request, CGBBANK_TRADE_STATUS);
        boolean paySuccess = CGBBankConstants.TRADE_STATE_SUCCESS.equals(tradeState);
        if (paySuccess) {
            setTradeNoBuyerInfoIfExists(requestBody, context);
            resolveFund(context, requestBody);
            return Workflow.RC_PAY_SUCCESS;
        }

        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    /**
     * 公共请求参数
     * @param context
     */
    private CGBBankRequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        CGBBankRequestBuilder requestBuilder = new CGBBankRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        /* 请求报文头 */
        //广发分配的商户号
        requestBuilder.bizHeaderSet(CGBBankProtocolFields.INST_ID, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_INST_ID));
        //广发分配的商户应用编号
        requestBuilder.bizHeaderSet(CGBBankProtocolFields.APP_ID, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_APP_ID));
        //接口的产品归类
        requestBuilder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PRODUCT_CODE));
        //请求流水号
        requestBuilder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户号
        requestBuilder.bizBodySet(CGBBankBusinessFields.MCH_ID, BeanUtil.getPropString(tradeParams, TransactionParam.CGBBANK_PROVIDER_MCH_ID));

        return requestBuilder;

    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String resultCode = BeanUtil.getPropString(result, CGBBankResponseFields.RESULT_CODE);//业务结果
        String errorCode = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_CODE);//错误代码
        String errorMsg = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_MSG);//错误代码描述

        map.put(CGBBankResponseFields.RESULT_CODE, resultCode);
        map.put(CGBBankResponseFields.ERROR_CODE, errorCode);
        map.put(CGBBankResponseFields.ERROR_MSG, errorMsg);

        setTransactionContextErrorInfo(context.getTransaction(), key, map, CGBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode), errorCode, errorMsg);
    }

    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String resultCode = BeanUtil.getPropString(result, CGBBankResponseFields.RESULT_CODE);//业务结果
        String errorCode = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_CODE);//错误代码
        String payResult = BeanUtil.getPropString(result, CGBBankResponseFields.PAY_RESULT);
        String needQuery = BeanUtil.getPropString(result, CGBBankResponseFields.NEED_QUERY);

        setTradeNoBuyerInfoIfExists(result, context);
        if (CGBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                && CGBBankConstants.ERROR_CODE_SUCCESS.equals(errorCode)
                && CGBBankConstants.PAY_RESULT_SUCCESS.equals(payResult)){
            if (CGBBankConstants.OH_YES.equals(needQuery)){
                return Workflow.RC_IN_PROG;
            }
            //付款成功
            resolveFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        }else if (CGBBankConstants.PROTOCOL_ERROR_CODE_LIST.contains(errorCode)) {
            return Workflow.RC_TRADE_CANCELED;
        } else if (CGBBankConstants.UNKNOWN_ERROR_CODE_LIST.contains(errorCode)) {
            //业务出现未知错误或者系统异常或者请求重试需要继续查询
            return Workflow.RC_IN_PROG;
        }else if (CGBBankConstants.FAIL_ERROR_CODE_LIST.contains(errorCode)){
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_ERROR;
    }

    public String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String resultCode = BeanUtil.getPropString(result, CGBBankResponseFields.RESULT_CODE);//业务结果
        String errorCode = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_CODE);//错误代码

        setTradeNoBuyerInfoIfExists(result, context);

        if (CGBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode) && CGBBankConstants.ERROR_CODE_SUCCESS.equals(errorCode)){
            String tradeState = BeanUtil.getPropString(result, CGBBankResponseFields.TRADE_STATE);
            int subPayWay = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
            //广发主扫支付，直接查询会存在时间差，短时间内查询会返回NOTPAY，此时需要重试查询
            if (CGBBankConstants.TRADE_STATE_USERPAYING.equals(tradeState)
                || (CGBBankConstants.TRADE_STATE_NOTPAY.equals(tradeState) && (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay))){
                return Workflow.RC_IN_PROG;
            } else if (CGBBankConstants.TRADE_STATE_NOTPAY.equals(tradeState) || CGBBankConstants.TRADE_STATE_PAYERROR.equals(tradeState)){
                return Workflow.RC_TRADE_CANCELED;
            } else if (CGBBankConstants.TRADE_STATE_REFUND.equals(tradeState)){
                return Workflow.RC_REFUND_SUCCESS;
            } else if (CGBBankConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
                resolveFund(context, result);
                return Workflow.RC_PAY_SUCCESS;
            }
        }else if (CGBBankConstants.PROTOCOL_ERROR_CODE_LIST.contains(errorCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (CGBBankConstants.UNKNOWN_ERROR_CODE_LIST.contains(errorCode)) {
            //业务出现未知错误或者系统异常或者请求重试需要继续查询
            return Workflow.RC_IN_PROG;
        }else if (CGBBankConstants.FAIL_ERROR_CODE_LIST.contains(errorCode)){
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_ERROR;
    }

    public String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String resultCode = BeanUtil.getPropString(result, CGBBankResponseFields.RESULT_CODE);//业务结果
        String errorCode = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_CODE);//错误代码

        if (CGBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode) && CGBBankConstants.ERROR_CODE_SUCCESS.equals(errorCode)){
            //退款成功
            context.getTransaction().put(Transaction.TRADE_NO, BeanUtil.getPropString(result, CGBBankResponseFields.REFUND_ID));
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }else if (CGBBankConstants.PROTOCOL_ERROR_CODE_LIST.contains(errorCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (CGBBankConstants.UNKNOWN_ERROR_CODE_LIST.contains(errorCode)) {
            //业务出现未知错误或者系统异常或者请求重试需要继续重试退款
            return Workflow.RC_RETRY;
        }else if (CGBBankConstants.FAIL_ERROR_CODE_LIST.contains(errorCode)){
            return Workflow.RC_ERROR;
        }

        return Workflow.RC_ERROR;
    }

    public String buildPreCreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        //响应码
        String resultCode = BeanUtil.getPropString(result, CGBBankResponseFields.RESULT_CODE);//业务结果
        String errorCode = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_CODE);//错误代码

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

        if (CGBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode) && CGBBankConstants.ERROR_CODE_SUCCESS.equals(errorCode)){
            //预下单成功
            Map payInfo =  MapUtils.getMap(result, CGBBankResponseFields.PAY_INFO);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
                String tradeNo = BeanUtil.getPropString(payInfo, CGBBankResponseFields.PAY_INFO_TRADE_NO);
                //银联支付宝，报文返回的tradeNo字段，前2位是银联的的“分套信息”，需要去掉，否则门店码交易会报  ALI38110,订单不存在。
                tradeNo = tradeNo.substring(2);
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(WapV2Fields.TRADE_NO, tradeNo);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            }else if (Order.PAYWAY_WEIXIN == payway) {
                String wcPayData = BeanUtil.getPropString(payInfo, CGBBankResponseFields.PAY_INFO_WC_PAY_DATA);
                Map wapRequest = JSONObject.parseObject(wcPayData, Map.class);
                if (wapRequest != null && !wapRequest.isEmpty()) {
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                }
            }
            return Workflow.RC_CREATE_SUCCESS;
        }else if (CGBBankConstants.PROTOCOL_ERROR_CODE_LIST.contains(errorCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (CGBBankConstants.UNKNOWN_ERROR_CODE_LIST.contains(errorCode)) {
            //业务出现未知错误或者系统异常
            return Workflow.RC_SYS_ERROR;
        }else if (CGBBankConstants.FAIL_ERROR_CODE_LIST.contains(errorCode)){
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_ERROR;
    }

    private String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_RETRY;
        }
        //响应码
        String resultCode = BeanUtil.getPropString(result, CGBBankResponseFields.RESULT_CODE);//业务结果
        String errorCode = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_CODE);//错误代码
        String errorMsg = BeanUtil.getPropString(result, CGBBankResponseFields.ERROR_MSG);//错误描述
        String retryFlag = BeanUtil.getPropString(result, CGBBankResponseFields.RETRY_FLAG);//撤销重试标识
        boolean needRefund = false;

        if ((CGBBankConstants.RESULT_CODE_SUCCESS.equals(resultCode) && CGBBankConstants.ERROR_CODE_SUCCESS.equals(errorCode))
                || CGBBankConstants.ERROR_CODE_ORDER_NO_FOUND_ERROR.equals(errorCode)){
            if (CGBBankConstants.OH_YES.equals(retryFlag)){
                return Workflow.RC_RETRY;
            }
            //撤销成功
            return Workflow.RC_CANCEL_SUCCESS;
        }else if (CGBBankConstants.ERROR_CODE_PAY_BARCODE_EXPIRED.equals(errorCode) && "交易超过了撤销的时间范围".equals(errorMsg)){
            //超出撤单时限， 查单，如果付款成功，则退款
            Map queryResult = doQuery(context);
            Map<String, Object> resultBody = MapUtils.getMap(queryResult, CGBBankProtocolFields.BODY);
            //响应码
            String queryResultCode = BeanUtil.getPropString(resultBody, CGBBankResponseFields.RESULT_CODE);//业务结果
            String queryErrorCode = BeanUtil.getPropString(resultBody, CGBBankResponseFields.ERROR_CODE);//错误代码
            if (CGBBankConstants.RESULT_CODE_SUCCESS.equals(queryResultCode) && CGBBankConstants.ERROR_CODE_SUCCESS.equals(queryErrorCode)){
                String tradeState = BeanUtil.getPropString(result, CGBBankResponseFields.TRADE_STATE);
                if (CGBBankConstants.TRADE_STATE_SUCCESS.equals(tradeState)) {
                    needRefund = true;
                }else{
                    return Workflow.RC_CANCEL_SUCCESS;
                }
            } else{
                return Workflow.RC_ERROR;
            }
        } else if (CGBBankConstants.PROTOCOL_ERROR_CODE_LIST.contains(errorCode)) {
            return Workflow.RC_PROTOCOL_ERROR;
        } else if (CGBBankConstants.UNKNOWN_ERROR_CODE_LIST.contains(errorCode)) {
            //业务出现未知错误或者系统异常或者请求重试需要继续重试退款
            return Workflow.RC_RETRY;
        }

        //超过撤销时间范围需要退款
        if (needRefund){
            long refundFee = BeanUtil.getPropLong(context.getOrder(), Order.EFFECTIVE_TOTAL);
            String rcFlag = doRefund(context, OP_CANCEL, refundFee);
            if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
                return Workflow.RC_CANCEL_SUCCESS;
            }else{
                return rcFlag;
            }
        }

        return Workflow.RC_ERROR;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if(MapUtils.isEmpty(result)){
            return;
        }

        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String,Object> config = getTradeParams(transaction);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);

        String thirdPartyBuyerId = null;
        String thirdPartyLogonId = null;
        if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway){
            thirdPartyBuyerId = BeanUtil.getPropString(result, CGBBankResponseFields.BUY_USER_ID);
            thirdPartyLogonId = BeanUtil.getPropString(result, CGBBankResponseFields.BUY_LOGON_ID);
        }else if (Order.PAYWAY_WEIXIN == payway){
            thirdPartyBuyerId = BeanUtil.getPropString(result, CGBBankResponseFields.SUB_OPEN_ID);
            thirdPartyLogonId = BeanUtil.getPropString(result, CGBBankResponseFields.OPEN_ID);
            if(StringUtil.empty(BeanUtil.getPropString(extraOutFields, Transaction.WEIXIN_APPID))){
                extraOutFields.put(Transaction.WEIXIN_APPID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
            }
        }
        if(!StringUtil.empty(thirdPartyBuyerId)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))){
                transaction.put(Transaction.BUYER_UID, thirdPartyBuyerId);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_UID))){
                order.put(Order.BUYER_UID, thirdPartyBuyerId);
            }

        }
        if(!StringUtil.empty(thirdPartyLogonId)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))){
                transaction.put(Transaction.BUYER_LOGIN, thirdPartyLogonId);
            }
            if(StringUtil.empty(BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                order.put(Order.BUYER_LOGIN, thirdPartyLogonId);
            }
        }
        String transactionId = BeanUtil.getPropString(result, CGBBankResponseFields.TRANSACTION_ID);        //平台订单号(平台交易号)
        String outTransactionId = BeanUtil.getPropString(result, CGBBankResponseFields.OUT_TRANSACTION_ID); //第三方订单号
        if (!StringUtils.isEmpty(transactionId)){
            if(StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, transactionId);
            }
            if(StringUtils.isEmpty(BeanUtil.getPropString(order, Order.TRADE_NO))) {
                order.put(Order.TRADE_NO, transactionId);
            }
        }
        if(!StringUtils.isEmpty(outTransactionId)){
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, outTransactionId);
        }
        String finishTime = BeanUtil.getPropString(result, ResponseFields.TIME_END);
        if(!StringUtil.empty(finishTime)){
            if(StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME))){
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(finishTime));
            }
        }
    }

    private void resolveFund(TransactionContext context, Map<String,Object> result){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        int payway = BeanUtil.getPropInt(order, Order.PAYWAY);
        if(Order.PAYWAY_ALIPAY2 == payway|| Order.PAYWAY_ALIPAY == payway){
            DirectAlipayV2ServiceProvider.resolvePayFund(order, transaction, result);
        }else if(Order.PAYWAY_WEIXIN == payway || Order.PAYWAY_UNIONPAY == payway){
            DirectWeixinServiceProvider.resolvePayFund(result, context);
        }
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    private void limitCredit(CGBBankRequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))){
            builder.bizBodySet(CGBBankBusinessFields.LIMIT_CREDIT_PAY, CIBBankConstants.NO_CREDIT);
        }
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, CGBBankRequestBuilder builder, int payway) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                try {
                    //如果设置了单品优惠，由于广发传值与直连微信和云闪付不一致，这里需要特殊处理
                    if(UpayConstant.DETAIL.equals(key) && value instanceof Map && ((Map) value).get(UpayConstant.GOODS_DETAIL) != null){
                        builder.bizBodySet(UpayConstant.GOODS_DETAIL, objectMapper.writeValueAsString(value));
                    } else if(UpayConstant.GOODS.equals(key) && value instanceof Map && ((Map) value).get(UpayConstant.GOODS_DETAIL) != null){
                        builder.bizBodySet(UpayConstant.GOODS_DETAIL, objectMapper.writeValueAsString(value));
                    } else if((Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) && BusinessV2Fields.EXTEND_PARAMS.equals(key) && value instanceof Map && ((Map) value).get(BusinessV2Fields.EXTEND_PARAMS) != null){
                        Map<String, Object> extendParams =  MapUtils.getMap((Map) value, BusinessV2Fields.EXTEND_PARAMS);
                        //花呗分期数 只支持传"3"|"6"|"12"，只适用于支付宝支付
                        if (extendParams.containsKey(CGBBankBusinessFields.HB_FQ_NUM)) {
                            String hb_fq_num = BeanUtil.getPropString(extendParams, CGBBankBusinessFields.HB_FQ_NUM);
                            if (CGBBankConstants.HB_FQ_NUM_LIST.contains(hb_fq_num)){
                                builder.bizBodySet(CGBBankBusinessFields.HB_FQ_NUM, hb_fq_num);
                            }
                        }
                        //花呗承担手续费 只支持传"0"|"100"，商家承担手续费传"100"，用户承担传"0"，在有hb_fq_num字段时默认为“0”
                        if (extendParams.containsKey(CGBBankBusinessFields.HB_FQ_SELLER_PERCENT)) {
                            String hb_fq_seller_percent = BeanUtil.getPropString(extendParams, CGBBankBusinessFields.HB_FQ_SELLER_PERCENT);
                            if (CGBBankConstants.HB_FQ_SELLER_PERCENT_LIST.contains(hb_fq_seller_percent)){
                                builder.bizBodySet(CGBBankBusinessFields.HB_FQ_SELLER_PERCENT, hb_fq_seller_percent);
                            }
                        }
                    } else{
                        builder.bizBodySet(key, value instanceof String ? value : objectMapper.writeValueAsString(value));
                    }
                } catch (JsonProcessingException e) {
                    logger.error("process extend fields fail: " + e.getMessage(), e);
                }
            }
        }
    }

    private String getLocation(String latitude, String longitude){
        String location = redisService.getCacheString(latitude , longitude);
        if (StringUtils.empty(location)){
            Map<String, Object> geoUDF = GeoUDFUtil.evaluate(longitude, latitude);
            String adCode = MapUtils.isNotEmpty(geoUDF) ? BeanUtil.getPropString(geoUDF, "adCode") : "000000";
            location = org.apache.commons.lang3.StringUtils.rightPad(adCode + longitude + "," + latitude, 30, ' ');
            redisService.setCache(latitude, longitude, location, TimeUnit.DAYS, 1);
        }

        return location;
    }
}
