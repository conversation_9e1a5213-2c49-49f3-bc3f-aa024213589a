package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.cmb.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.util.cmb.CmbSM2Util;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> Date: 2020/10/19 Time: 2:21 下午
 */
public class CmbServiceProvider extends AbstractServiceProvider {
    public static final Logger LOGGER = LoggerFactory.getLogger(CmbServiceProvider.class);

    public static final String NAME = "provider.cmb";
    private static final DateTimeFormatter TRADE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final long ALLOW_CANCEL_TIME_INTERVAL = 7 * 24 * 3600 * 1000;
    private static final String DEFAULT_TIMEOUT_EXPRESS = DEFAULT_TIME_EXPIRE_MINUTE + "m";//csb 二维码支付,超时时间
    private static final String ALIPAY_PROMOTION_MCH_TOP_UP = "ALIPAY_CASH_VOUCHER";
    private static final DateTimeFormatter B2C_DATE_TIMEOUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    public static final String ERR_CODE_ORDER_INVALID = "ORDERID_INVALID";


    private String notifyHost;

    @Autowired
    private CmbClient cmbClient;

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_ALIPAY
                || payway == Order.PAYWAY_ALIPAY2
                || payway == Order.PAYWAY_WEIXIN
                || payway == Order.PAYWAY_DCEP;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CMB_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);

        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);

        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        String terminalSn = (String) BeanUtil.getNestedProperty(transaction, Transaction.KEY_TERMINAL_SN);

        String url;
        RequestBuilder builder;
        if (payway == Order.PAYWAY_DCEP) {
            url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), "decp");
            builder = new RequestBuilder();
            builder.bizSet(BusinessFields.MER_ID, merId);
            builder.bizSet(BusinessFields.USER_ID, userId);
            builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
            builder.bizSet(BusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
            builder.bizSet(BusinessFields.TXN_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
            builder.bizSet(BusinessFields.TRANSACTION_TYPE, CmbConstants.TRANSACTION_TYPE_BARCODE);
            builder.bizSet(BusinessFields.CURRENCY_CODE, CmbConstants.CURRENCY_CODE_CNY);
            builder.bizSet(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, url, context));
            builder.bizSet(BusinessFields.GOODS_NAME, MapUtil.getString(transaction, Transaction.SUBJECT));
            builder.bizSet(BusinessFields.ORDER_TIME_EXPIRE, B2C_DATE_TIMEOUT_FORMATTER.format(LocalDateTime.now().plusMinutes(B2C_TIME_EXPIRE_MINUTE)));
            setTerminalInfo(context, builder);
        } else {
            String wxSubAppId = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID);
            url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

            builder = new RequestBuilder();
            builder.bizSet(BusinessFields.MER_ID, merId);
            builder.bizSet(BusinessFields.USER_ID, userId);
            if(payway == Order.PAYWAY_WEIXIN && wxSubAppId != null){
                builder.bizSet(BusinessFields.SUB_APP_ID, wxSubAppId);
            }
            builder.bizSet(BusinessFields.TERM_ID, buildTermId(terminalSn));
            builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
            builder.bizSet(BusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
            builder.bizSet(BusinessFields.TXN_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
            builder.bizSet(BusinessFields.TRADE_SCENE, CmbConstants.TRADE_SCENE_OFFLINE);
            builder.bizSet(BusinessFields.ITEM_DISCOUNT, null);
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 1, OP_PAY);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        if (needRefund(context.getOrder(), transaction)) {
            return cancelToRefundProcess(context);
        }

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);
        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.MER_ID, merId);
        builder.bizSet(BusinessFields.USER_ID, userId);
        builder.bizSet(BusinessFields.ORIG_ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 1, OP_CANCEL);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb cancel", e);
            setTransactionContextErrorInfo(context, OP_CANCEL, e);
            return (e instanceof MpayApiSendError || e instanceof MpayApiReadError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        return buildCancelResult(context, result);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);
        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.MER_ID, merId);
        builder.bizSet(BusinessFields.USER_ID, userId);
        builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 1, OP_QUERY);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(context, result);
    }



    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);
        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.MER_ID, merId);
        builder.bizSet(BusinessFields.USER_ID, userId);
        builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        builder.bizSet(BusinessFields.ORIG_ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.bizSet(BusinessFields.TXN_AMT, MapUtil.getString(order, Order.EFFECTIVE_TOTAL));
        builder.bizSet(BusinessFields.REFUND_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 1, OP_REFUND);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(context, result);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            return alipayWay(context);
        }
        if (payway == Order.PAYWAY_WEIXIN) {
            return wxWay(context);
        }
        throw new UpayBizException("该支付方式不支持预下单");
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_CMB;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {

        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String cmbPublicKeyId = MapUtil.getString(tradeParams, TransactionParam.CMB_PUBLIC_KEY);
        Map<String, Object> cmbNotify = MapUtil.getMap(providerNotification, "cmbNotify");
        String sign = MapUtil.getString(cmbNotify, ProtocolFields.SIGN);
        String cmbPublicKey = getPrivateKeyContent(cmbPublicKeyId);

        boolean isPass = false;
        try {
            isPass = CmbSM2Util.sm2Check(cmbNotify, ProtocolFields.SIGN
                    , sign, cmbPublicKey);
        } catch (Exception e) {
            LOGGER.warn("【招商银行回调验签】验签异常, 异常栈: ", e);
        }

        long type = MapUtil.getLongValue(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }

        if (context.isFakeRequest() || isPass) {
            Map<String, Object> bizContent = getBizContent(providerNotification);
            setTradeNoBuyerInfoIfExists(bizContent, context);
            resolvePayFund(context, bizContent);
            return Workflow.RC_PAY_SUCCESS;
        }

        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);
        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.MER_ID, merId);
        builder.bizSet(BusinessFields.USER_ID, userId);
        builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 3, ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb refund query", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundQueryResult(context, result);
    }

    private String alipayWay(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);
        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);
        String buyerId = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_ALIPAY_WAP);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);

        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.MER_ID, merId);
        builder.bizSet(BusinessFields.USER_ID, userId);
        builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        builder.bizSet(BusinessFields.TRADE_SCENE, CmbConstants.TRADE_SCENE_OFFLINE);
        builder.bizSet(BusinessFields.TIMEOUT_EXPRESS, DEFAULT_TIMEOUT_EXPRESS);
        builder.bizSet(BusinessFields.TXN_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.bizSet(BusinessFields.NOTIFY_URL, notifyUrl);
        builder.bizSet(BusinessFields.BUYER_ID, buyerId);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 1, OP_PRECREATE);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb alipay way", e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildAlipayWayResult(context, result);
    }

    private String wxWay(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);
        Object goodsDetail = extendedParams.get(UpayConstant.DETAIL);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CMB_APP_ID);
        String secret = MapUtil.getString(tradeParams, TransactionParam.CMB_SECRET);
        String privateKeyId = MapUtil.getString(tradeParams, TransactionParam.SQB_PRIVATE_KEY);
        String merId = MapUtil.getString(tradeParams, TransactionParam.MER_ID);
        String userId = getStoreUserId(transaction);
        String wxSubAppId = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID);
        String wxMiniSubAppId = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        String openId = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WX_WAP);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);

        String tradeType;
        String subAppId;
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (subPayway == Order.SUB_PAYWAY_WAP) {
            tradeType = CmbConstants.TRADE_TYPE_JSAPI;
            subAppId = wxSubAppId;
        } else if (subPayway == Order.SUB_PAYWAY_MINI) {
            tradeType = CmbConstants.TRADE_TYPE_MINI;
            subAppId = wxMiniSubAppId;
        } else {
            throw new UpayBizException("暂不支持该二级支付方式");
        }



        RequestBuilder builder = new RequestBuilder();
        builder.bizSet(BusinessFields.MER_ID, merId);
        builder.bizSet(BusinessFields.USER_ID, userId);
        builder.bizSet(BusinessFields.SUB_APP_ID, subAppId);
        builder.bizSet(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        builder.bizSet(BusinessFields.TRADE_TYPE, tradeType);
        builder.bizSet(BusinessFields.TRADE_SCENE, CmbConstants.TRADE_SCENE_OFFLINE);
        builder.bizSet(BusinessFields.BODY, MapUtil.getString(transaction, Transaction.SUBJECT));
        builder.bizSet(BusinessFields.NOTIFY_URL, notifyUrl);
        builder.bizSet(BusinessFields.TXN_AMT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.bizSet(BusinessFields.SPBILL_CREATE_IP, UpayUtil.getLocalHostIp());
        builder.bizSet(BusinessFields.SUB_OPEN_ID, openId);
        builder.bizSet(BusinessFields.GOODS_DETAIL, goodsDetail);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder, appId, secret, privateKeyId, 1, OP_PRECREATE);
        } catch (Exception e) {
            LOGGER.error("failed to call cmb wx wap", e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildWxWayResult(context, result);
    }

    private Map<String, Object> retryIfNetworkException(String url, RequestBuilder builder, String appId
            , String secret, String privateKeyId, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                return cmbClient.call(url, builder, appId, secret, getPrivateKeyContent(privateKeyId));
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                LOGGER.warn("encountered ioex in cmb {}", opFlag, ex);
            }
        }
        LOGGER.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String returnCode = MapUtil.getString(result, ResponseFields.RETURN_CODE);
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        String respMsg = MapUtil.getString(result, ResponseFields.RESP_MSG);
        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        String errMsg = MapUtil.getString(result, ResponseFields.ERR_DESCRIPTION);
        map.put(ResponseFields.RETURN_CODE, returnCode);
        map.put(ResponseFields.RESP_CODE, respCode);
        map.put(ResponseFields.RESP_MSG, respMsg);
        map.put(ResponseFields.ERR_CODE, errCode);
        map.put(ResponseFields.ERR_DESCRIPTION, errMsg);

        Map<String, Object> bizResults = getBizContent(result);
        String tradeState = MapUtil.getString(bizResults, ResponseFields.TRADE_STATE);

        if (OP_QUERY.equals(key)) {
            map.put(ResponseFields.TRADE_STATE, tradeState);
        }

        setTransactionContextErrorInfo(context.getTransaction(), key, map, StringUtils.equals(tradeState, CmbConstants.TRADE_STATE_SUCCESS), respCode, respMsg);
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);

        Map<String, Object> bizContent = getBizContent(result);

        setTradeNoBuyerInfoIfExists(bizContent, context);

        String tradeState = MapUtil.getString(bizContent, ResponseFields.TRADE_STATE);
        if (StringUtils.equalsIgnoreCase(tradeState, CmbConstants.TRADE_STATE_PROCESS)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_ERROR)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_MAINTAINING)) {
            return Workflow.RC_IN_PROG;
        }

        if (StringUtils.equals(tradeState, CmbConstants.TRADE_STATE_SUCCESS)) {
            resolvePayFund(context, bizContent);
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_TRADE_CANCELED;
    }

    private String buildCancelResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        Map<String, Object> bizContent = getBizContent(result);
        String cancelState = MapUtil.getString(bizContent, ResponseFields.CANCEL_STATE);

        if (StringUtils.equalsIgnoreCase(cancelState, CmbConstants.TRADE_STATE_CANCEL)
                || StringUtils.equalsIgnoreCase(cancelState, CmbConstants.TRADE_STATE_FAIL)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        if (StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_MAINTAINING)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_ERROR)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_OPERATING_FREQUENTLY)) {
            return Workflow.RC_RETRY;
        }

        if (StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_ORDER_PAID)) {
            return cancelToRefundProcess(context);
        }

        return Workflow.RC_ERROR;

    }

    private String buildRefundResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        Map<String, Object> bizContent = getBizContent(result);
        String refundState = MapUtil.getString(bizContent, ResponseFields.REFUND_STATE);
        if (StringUtils.equalsIgnoreCase(refundState, CmbConstants.TRADE_STATE_SUCCESS)) {
            resolveRefundFund(context, bizContent);
            return Workflow.RC_REFUND_SUCCESS;
        }
        if (StringUtils.equalsIgnoreCase(refundState, CmbConstants.TRADE_STATE_PROCESS)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_ERROR)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_MAINTAINING)) {
            return refundQuery(context);
        }

        return Workflow.RC_ERROR;
    }


    private String buildQueryResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> bizContent = getBizContent(result);
        setTradeNoBuyerInfoIfExists(bizContent, context);
        String errCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        String tradeState = MapUtil.getString(bizContent, ResponseFields.TRADE_STATE);
        if (StringUtils.equalsIgnoreCase(tradeState, CmbConstants.TRADE_STATE_SUCCESS)) {
            resolvePayFund(context, bizContent);
            return Workflow.RC_PAY_SUCCESS;
        }
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);

        //明确失败
        if(CmbConstants.RESP_CODE_SUCCESS.equals(respCode)){
            if(CmbConstants.TRADE_STATE_CLOSE.equals(tradeState) || CmbConstants.TRADE_STATE_CANCEL.equals(tradeState) ||CmbConstants.TRADE_STATE_FAIL.equals(tradeState)){
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        if(CmbConstants.RESP_CODE_FAIL.equals(respCode) && ERR_CODE_ORDER_INVALID.equals(errCode)){
            return Workflow.RC_TRADE_CANCELED;

        }
        
        if (StringUtils.equalsIgnoreCase(tradeState, CmbConstants.TRADE_STATE_PROCESS)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_ERROR)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_SYSTERM_MAINTAINING)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_CMBORDE_RID_NOT_EXIST)
                || StringUtils.equalsIgnoreCase(errCode, CmbConstants.ERR_CODE_UNPAIED_ORDER)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }

    private String buildRefundQueryResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> bizContent = getBizContent(result);
        String tradeState = MapUtil.getString(bizContent, ResponseFields.TRADE_STATE);
        if (StringUtils.equalsIgnoreCase(tradeState, CmbConstants.TRADE_STATE_PROCESS)
                || StringUtils.equalsIgnoreCase(tradeState, CmbConstants.TRADE_STATE_SUCCESS)) {
            resolveRefundFund(context, bizContent);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    private String buildAlipayWayResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> bizContent = getBizContent(result);
        String payInfo = MapUtil.getString(bizContent, ResponseFields.PAY_INFO);
        if (StringUtils.isBlank(payInfo)) {
            return Workflow.RC_ERROR;
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> wapRequest = new HashMap();
        wapRequest.put(WapV2Fields.TRADE_NO, payInfo);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    private String buildWxWayResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> bizContent = getBizContent(result);
        Map<String, Object> payDate = MapUtil.getMap(bizContent, ResponseFields.PAY_DATA);
        if (MapUtils.isEmpty(payDate)) {
            return Workflow.RC_ERROR;
        }

        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, payDate);
        return Workflow.RC_CREATE_SUCCESS;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> bizContent, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String thirdPartyBuyerId = MapUtil.getString(bizContent, ResponseFields.OPEN_ID);
            if (StringUtils.isNotEmpty(thirdPartyBuyerId)) {
                transaction.put(Transaction.BUYER_UID, thirdPartyBuyerId);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tranNo = MapUtil.getString(bizContent, ResponseFields.CMB_ORDER_ID);
            if (StringUtils.isNotEmpty(tranNo)) {
                transaction.put(Transaction.TRADE_NO, tranNo);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String channelTranNo = MapUtil.getString(bizContent, ResponseFields.THIRD_ORDER_ID);
            int payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
            if (payway == Order.PAYWAY_ALIPAY2) {
                channelTranNo = getAlipayRealTradeNo(Order.PROVIDER_CMB, channelTranNo);
            }
            if (StringUtils.isNotEmpty(channelTranNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTranNo);
            }
        }
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> bizResult) {
        Map<String, Object> transaction = context.getTransaction();
        //交易完成时间
        long channelFinishTime = getTradeTimestamp(bizResult);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            resolveAlipayV2PayFund(bizResult, context);
        }
        if (payway == Order.PAYWAY_WEIXIN) {
            resolveWeixinPayFund(bizResult, context);
        }
    }

    private long getTradeTimestamp(Map<String, Object> bizResult) {
        try {
            String tradeTime = MapUtil.getString(bizResult, ResponseFields.TXN_TIME);
            if (StringUtils.isEmpty(tradeTime)) {
                return System.currentTimeMillis();
            }
            return LocalDateTime.parse(tradeTime, TRADE_TIME_FORMATTER).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        } catch (Exception e) {
            return System.currentTimeMillis();
        }
    }

    private Map<String, Object> getBizContent(Map<String, Object> result) {
        String resultStr = MapUtil.getString(result, ProtocolFields.BIZ_CONTENT);
        if (StringUtils.isBlank(resultStr)) {
            resultStr = "{}";
        }
        return JSON.parseObject(resultStr, new TypeReference<Map<String, Object>>(){});
    }

    private String buildTermId(String terminalSn) {
        if (StringUtils.isBlank(terminalSn)) {
            throw new UpayBizException("终端号不能为空");
        }
        if (terminalSn.length() <= 8) {
            return terminalSn;
        }
        return terminalSn.substring(terminalSn.length() - 8);
    }

    private String cancelToRefundProcess(TransactionContext context) {
        String result = refund(context);
        if (Workflow.RC_REFUND_SUCCESS.equals(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        return result;
    }

    private boolean needRefund(Map<String, Object> order, Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(order, Order.PAYWAY);
        if (payway == Order.PAYWAY_DCEP) {
            return true;
        }

        Map<String, Object> payTrans = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(order, DaoConstants.CTIME));

        long channelFinishTime = BeanUtil.getPropLong(payTrans, Transaction.CHANNEL_FINISH_TIME);
        if (channelFinishTime == 0) {
            return false;
        }

        long todayStartTimestamp = DateTimeUtil.getOneDayStart(System.currentTimeMillis());
        long channelFinishDayStart = DateTimeUtil.getOneDayStart(channelFinishTime);

        return todayStartTimestamp - channelFinishDayStart > ALLOW_CANCEL_TIME_INTERVAL;
    }

    private void resolveRefundFund(TransactionContext context, Map<String, Object> bizContent) {
        Map<String,Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction
                , MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));

        //本次退款总额
        long refundAmountTotal = MapUtil.getLongValue(bizContent, ResponseFields.REFUND_AMT);
        //退款优惠金额
        long refundDscAmount = MapUtil.getLongValue(bizContent, ResponseFields.REFUND_DSC_AMT);
        //实际退款金额
        long refundPaidAmount = refundAmountTotal - refundDscAmount;
        transaction.put(Transaction.PAID_AMOUNT, refundPaidAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, refundPaidAmount);

        String tranNo = MapUtil.getString(bizContent, ResponseFields.CMB_ORDER_ID);
        String channelTranNo = MapUtil.getString(bizContent, ResponseFields.THIRD_ORDER_ID);
        if(StringUtils.isNotEmpty(tranNo)){
            if(StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))){
                transaction.put(Transaction.TRADE_NO, tranNo);
            }
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(Objects.isNull(extraOutFields)){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        if (StringUtils.isNotEmpty(channelTranNo)) {
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTranNo);
        }

        if (MapUtil.getLongValue(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        } else {
            List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil
                    .getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            if (Objects.nonNull(payments)) {
                List<Map<String,Object>> refundPayments = new ArrayList<>();
                long refundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                if(0 != refundFee && payments.size() > 0) {
                    long totalAmount = 0L;
                    for (Map<String, Object> payment : payments) {
                        totalAmount += MapUtil.getLongValue(payment, Transaction.PAYMENT_AMOUNT);
                    }
                    if(totalAmount != 0) {
                        for (int i = 0; i < payments.size(); i++) {
                            Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
                            refundPayment.put(Transaction.PAYMENT_AMOUNT
                                    , Math.round((refundFee * 1d/totalAmount) * BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT)));
                            refundPayments.add(refundPayment);
                        }
                    }
                }

                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
            }
        }
    }

    private Map<String, Object> buildItemDiscount(Map<String, Object> transaction) {
        Map<String, Object> extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);

        Object goodsDetail = null;
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            goodsDetail = extendedParams.get(UpayConstant.GOODS_DETAIL);
        } else if (payway == Order.PAYWAY_WEIXIN) {
            goodsDetail = extendedParams.get(UpayConstant.DETAIL);
        }
        if (Objects.nonNull(goodsDetail)) {
            return CollectionUtil.hashMap(UpayConstant.GOODS_DETAIL, goodsDetail);
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    private void resolveWeixinPayFund(Map<String,Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long couponSum = 0;
        long discountChanelMchTotal = 0; // 商户免充值不结算给商户
        List<Map<String,Object>> payments = new ArrayList<Map<String,Object>>();
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            List<Map<String,Object>> promotions = getPromotions(result);
            if (CollectionUtils.isNotEmpty(promotions)) {
                for (Map<String,Object> promotion : promotions) {
                    if (promotion.isEmpty()) {
                        continue;
                    }
                    String type = MapUtil.getString(promotion, ResponseFields.TYPE);
                    String promotionId = BeanUtil.getPropString(promotion, ResponseFields.PROMOTION_ID);
                    long amount = BeanUtil.getPropLong(promotion, ResponseFields.AMOUNT);
                    long wxpayContribute = BeanUtil.getPropLong(promotion, ResponseFields.WXPAY_CONTRIBUTE);
                    long otherContribute = BeanUtil.getPropLong(promotion, ResponseFields.OTHER_CONTRIBUTE);
                    long channelAmount = wxpayContribute + otherContribute;
                    long mchAmount = amount - channelAmount;
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)) {
                        if(mchAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                            discountChanelMchTotal += mchAmount;
                        }
                        if (channelAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }
                    } else if (WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)) {
                        if (mchAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }
                        if (channelAmount > 0) {
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }

                    }
                    couponSum += amount;
                }
            }
        } else {
            long discount = MapUtil.getLongValue(result, ResponseFields.DSC_AMT);
            if (discount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
                couponSum += discount;
            }
        }

        long totalFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long cashFee = totalFee - couponSum;
        if (cashFee > 0) {
            String banktype = BeanUtil.getPropString(result, ResponseFields.PAY_BANK);
            Map<String,Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
        }

        Map<String,Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if(couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum);
        }
        transaction.put(Transaction.PAID_AMOUNT, cashFee);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee - discountChanelMchTotal);
    }

    @SuppressWarnings("unchecked")
    private void resolveAlipayV2PayFund(Map<String,Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();

        long couponSum = 0;
        long discountChanelMchTotal = 0; // 商户免充值不结算给商户
        List<Map<String,Object>> payments = new ArrayList<Map<String,Object>>();
        if(result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            List<Map<String,Object>> promotions = getPromotions(result);
            if (CollectionUtils.isNotEmpty(promotions)) {
                for (Map<String,Object> promotion : promotions) {
                    if (promotion.isEmpty()) {
                        continue;
                    }
                    String type = MapUtil.getString(promotion, ResponseFields.TYPE);
                    String promotionId = BeanUtil.getPropString(promotion, ResponseFields.VOUCHER_ID);
                    if (Objects.isNull(promotionId)) {
                        promotionId = BeanUtil.getPropString(promotion, ResponseFields.ID);
                    }
                    long amount = com.wosai.mpay.util.StringUtils.yuan2cents(BeanUtil.getPropString(promotion, ResponseFields.AMOUNT)) ;
                    String otherContribute = BeanUtil.getPropString(promotion, ResponseFields.ZF_OTHER_CONTRIBUTE);
                    if (Objects.isNull(otherContribute)) {
                        otherContribute = BeanUtil.getPropString(promotion, ResponseFields.OTHER_CONTRIBUTE, "0");
                    }
                    long channelAmount = com.wosai.mpay.util.StringUtils.yuan2cents(otherContribute);
                    long mchAmount = amount - channelAmount;
                    if (mchAmount > 0) {
                        Map<String, Object> payment = CollectionUtil.hashMap(
                                Transaction.PAYMENT_ORIGIN_TYPE, type,
                                Transaction.PAYMENT_AMOUNT, mchAmount,
                                Transaction.PAYMENT_SOURCE, promotionId
                        );
                        if (StringUtils.equalsIgnoreCase(type, ALIPAY_PROMOTION_MCH_TOP_UP)) {
                            payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP);
                        } else {
                            payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH);
                            discountChanelMchTotal += mchAmount;
                        }
                        payments.add(payment);
                    }
                    if (channelAmount > 0) {
                        payments.add(
                                CollectionUtil.hashMap(
                                        Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                        Transaction.PAYMENT_ORIGIN_TYPE, type,
                                        Transaction.PAYMENT_AMOUNT, channelAmount,
                                        Transaction.PAYMENT_SOURCE, promotionId
                                )
                        );
                    }
                    couponSum += amount;
                }
            }
        } else {
            long discount = MapUtil.getLongValue(result, ResponseFields.DSC_AMT);
            if(discount > 0){
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
                couponSum += discount;
            }
        }

        long totalFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long cashFee = totalFee - couponSum;
        if (cashFee > 0) {
            String banktype = BeanUtil.getPropString(result, ResponseFields.PAY_BANK);
            Map<String, Object> payment = (Map<String, Object>) AlipayV2ServiceProvider.fundChannelPayment.get(banktype);
            if(payment == null){
                payment = CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                        Transaction.PAYMENT_ORIGIN_TYPE, banktype
                );
            } else {
                payment = (Map)((HashMap)payment).clone();
            }
            payment.put(Transaction.PAYMENT_AMOUNT, cashFee);
            payments.add(payment);
        }

        Map<String,Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if(couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum);
        }
        transaction.put(Transaction.PAID_AMOUNT, cashFee);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee - discountChanelMchTotal);
    }

    private List<Map<String, Object>> getPromotions(Map<String,Object> result) {
        String promotionsStr = MapUtil.getString(result, ResponseFields.PROMOTION_DETAIL);
        if (StringUtils.isEmpty(promotionsStr)) {
            promotionsStr = "[]";
        }
        return JSONArray.parseArray(promotionsStr).toJavaObject(new TypeReference<List<Map<String, Object>>>(){});
    }

    //获取门店收银员
    private String getStoreUserId(Map<String,Object> transaction){
        //1、先获取门店级别配置的term_id作为userId
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String userId = MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        if(StringUtils.isNotBlank(userId)){
            return userId;
        }

        //2、其次获取配置参数中的user_id
        Map<String, Object> tradeParams = getTradeParams(transaction);
        return MapUtil.getString(tradeParams, TransactionParam.USER_ID);
    }

    protected void setTerminalInfo(TransactionContext context, RequestBuilder builder) {
        Map<String, Object> transaction = context.getTransaction();
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        String terminalIp = terminalInfo.getIp();
        if (StringUtils.isBlank(terminalIp)) {
            terminalIp = UpayUtil.getLocalHostIp();
        }
        String terminalSn = MapUtil.getString(transaction, Transaction.KEY_TERMINAL_SN);
        if (StringUtils.isBlank(terminalSn)) {
            terminalSn = terminalIp;
        }
        String merchantName = MapUtil.getString(transaction, Transaction.KEY_MERCHANT_NAME);
        if (StringUtils.isBlank(merchantName)) {
            merchantName = terminalIp;
        }

        builder.bizSet(BusinessFields.TERMINAL_IP, terminalIp);
        builder.bizSet(BusinessFields.TERMINAL_NO, terminalSn);
        builder.bizSet(BusinessFields.TRADE_PLACE, merchantName);
    }

}
