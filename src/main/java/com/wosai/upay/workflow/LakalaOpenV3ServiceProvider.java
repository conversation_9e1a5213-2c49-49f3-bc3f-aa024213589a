package com.wosai.upay.workflow;

import java.util.*;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.upay.core.model.MetaPayway;
import com.wosai.upay.exception.UpayBizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.lakala.open.BusinessFields;
import com.wosai.mpay.api.lakala.open.BusinessV3Fields;
import com.wosai.mpay.api.lakala.open.LakalaOpenClient;
import com.wosai.mpay.api.lakala.open.LakalaOpenV3Constants;
import com.wosai.mpay.api.lakala.open.LakalaOpenV3Constants.TransType;
import com.wosai.mpay.api.lakala.open.RequestV3Builder;
import com.wosai.mpay.api.lakala.open.ResponseV3Fields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;
import org.springframework.beans.factory.annotation.Value;

public class LakalaOpenV3ServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(LakalaOpenV3ServiceProvider.class);
    public static final String NAME = "provider.lakala.openv3";
    public static final Map<String, String> EXTENDED_CONVERT_MAP = MapUtil.hashMap(com.wosai.mpay.api.lakala.open.BusinessFields.FRONT_URL, BusinessV3Fields.FRONT_URL, 
                com.wosai.mpay.api.lakala.open.BusinessFields.FRONT_FAILURL, BusinessV3Fields.FRONT_FAIL_URL
            );

    public String notifyHost;

    private int retryTimes = 3;

    @Autowired
    LakalaOpenClient client;

    @Value("${jdbt.mini.redirectUrlFormat}")
    public String jdRedirectUrlFormat;

    public LakalaOpenV3ServiceProvider() {
        super.dateFormat = new SafeSimpleDateFormat(LakalaOpenV3Constants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<>(Arrays.asList(BusinessV3Fields.MERCHANT_NO, BusinessV3Fields.TOTAL_AMOUNT, BusinessV3Fields.OUT_TRADE_NO, Transaction.EXTENDED_USER_AUTH_CODE, Transaction.QR_CODE));
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Order.PAYWAY_UNIONPAY || payway == Order.PAYWAY_JD) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestV3Builder builder = getDefaultRequestBuilder(config, context);
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.reqDataSet(BusinessV3Fields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        builder.reqDataSet(BusinessV3Fields.TOTAL_AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.reqDataSet(BusinessV3Fields.SUBJECT, MapUtil.getString(transaction, Transaction.SUBJECT));
        builder.reqDataSet(BusinessV3Fields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        setTermInfo(builder, context);
        carryOverExtendedParams(extended, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_PAY, config, builder.build(), 1);
        } catch (Exception ex) {
            logger.error("failed to call lakala open v3 pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            if (ex instanceof MpayApiReadError) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setTradeNoBuyerInfoIfExists(result, context);
        if(LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(responseCode)) {
            Map<String, Object> respData = MapUtil.getMap(result, ResponseV3Fields.RESP_DATA);
            String needQuery = MapUtil.getString(respData, ResponseV3Fields.NEED_QUERY);
            if (LakalaOpenV3Constants.NEED_QUERY_FALSE.equals(needQuery)) {
                //success
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            } else {
                return Workflow.RC_IN_PROG;
            }
        } else if (LakalaOpenV3Constants.PAY_IN_PROCESS_SET.contains(responseCode)) {
            return Workflow.RC_IN_PROG;
        } else if(LakalaOpenV3Constants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)){
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = new ArrayList<>();
        Map<String,Object> respData = (Map<String, Object>) result.get(ResponseV3Fields.RESP_DATA);
        Map<String, Object> accFields = MapUtil.getMap(respData, ResponseV3Fields.ACC_RESP_FIELDS);
        String upCouponInfo = MapUtil.getString(accFields, ResponseV3Fields.UP_COUPON_INFO);
        long couponSum = 0L;
        if (!StringUtils.isEmpty(upCouponInfo)) {
            List<Map<String, Object>> couponInfo = JsonUtil.jsonStrToObject(upCouponInfo, List.class);
            for(Map<String,Object> coupon: couponInfo){
                String spnsrId = BeanUtil.getPropString(coupon, ResponseV3Fields.COUPON_INFO_SPNSR_ID); //出资方
                long amount = BeanUtil.getPropLong(coupon, ResponseV3Fields.COUPON_INFO_OFFST_AMT);
                String couponId = BeanUtil.getPropString(coupon, ResponseV3Fields.COUPON_INFO_ID);
                String couponType = BeanUtil.getPropString(coupon, ResponseV3Fields.COUPON_INFO_TYPE);
                couponSum = couponSum + amount;
                //注意银联接口返回不能准确的区分商户优惠是否是免充值，默认当做是免充值的优惠。如果后续此通道对接了微信交易，需要特别注意。
                String paymentType;
                if(LakalaOpenV3Constants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)){
                    paymentType = LakalaOpenV3Constants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                }else{
                    paymentType = LakalaOpenV3Constants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                }
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, amount,
                        Transaction.PAYMENT_SOURCE, couponId,
                        Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                        Transaction.PAYMENT_TYPE, paymentType
                ));
            }
        }
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            String paymentType = null;
            String cardType = MapUtil.getString(respData, ResponseV3Fields.CARD_TYPE);
            if (MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_JD) {
                paymentType = Payment.TYPE_JD_BAITIAO;
            } else if (LakalaOpenV3Constants.CARD_TYPE_CREDIT_CARD.equals(cardType)) {
                paymentType = Payment.TYPE_BANKCARD_CREDIT;
            } else if (LakalaOpenV3Constants.CARD_TYPE_DEBIT_CARD.equals(cardType)) {
                paymentType = Payment.TYPE_BANKCARD_DEBIT;
            } else {
                paymentType = Payment.TYPE_OTHERS;
            }
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardType,
                    Transaction.PAYMENT_TYPE, paymentType
                    )
            );
        }
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if(oldPayments == null || oldPayments.isEmpty()){
            if (payments != null && !payments.isEmpty()) {
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
        }
        if(!StringUtil.empty(MapUtil.getString(respData, ResponseV3Fields.PAYER_AMOUNT))){
            long payAmt = MapUtil.getLongValue(respData, ResponseV3Fields.PAYER_AMOUNT);
            transaction.put(Transaction.PAID_AMOUNT, payAmt);
        }
    }

    private void setTermInfo(RequestV3Builder builder, TransactionContext context){
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> termInfo = new LinkedHashMap<>();
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
            termInfo.put(BusinessV3Fields.LOCATION, terminalInfo.getFormatLatitude() + "," + terminalInfo.getFormatLongitude());
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }
        if (!terminalInfo.isOffset() && terminalInfo.isSendIp()) {
            termInfo.put(BusinessV3Fields.REQUEST_IP, terminalInfo.getIp());
        } else {
            termInfo.put(BusinessV3Fields.REQUEST_IP, UpayUtil.getLocalHostIp());
        }
        Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        String baseStation = BeanUtil.getPropString(extraParams, Transaction.SQB_STATION);
        if (!StringUtils.isEmpty(baseStation)) {
            termInfo.put(BusinessV3Fields.BASE_STATION, baseStation.replaceAll(",", "-"));
        }
        builder.reqDataSet(BusinessV3Fields.LOCATION_INFO, termInfo);
    }


    public void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> respData = (Map<String, Object>) result.get(ResponseV3Fields.RESP_DATA);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        Map<String, Object> accFields = MapUtil.getMap(respData, ResponseV3Fields.ACC_RESP_FIELDS);
        // 云闪付支付
        if (payway == Order.PAYWAY_UNIONPAY) {
            String payInfo = MapUtil.getString(accFields, ResponseV3Fields.PAYER_INFO);
            String userId = MapUtil.getString(accFields, ResponseV3Fields.USER_ID);
            String accNo = "";
            String name = "";
            try {
                Map userInfo = JsonUtil.jsonStrToObject(payInfo, Map.class);
                accNo = MapUtil.getString(userInfo, ResponseV3Fields.ACC_NO);
                name = MapUtil.getString(userInfo, ResponseV3Fields.NAME);

            } catch (Exception e) {
                logger.error("failed to parse payer info", e);
            }
            if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_UID))) {
                if (!StringUtil.empty(accNo) && !accNo.contains("*")) {
                    transaction.put(Transaction.BUYER_UID, accNo);
                } else if (!StringUtil.empty(userId)) {
                    transaction.put(Transaction.BUYER_UID, userId);
                }
            }
            if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_LOGIN)) && !StringUtil.empty(name)) {
                transaction.put(Transaction.BUYER_LOGIN, name);
            }

        } else if (payway == Order.PAYWAY_JD) {
            // 京东支付
            String userId = MapUtil.getString(accFields, ResponseV3Fields.USER_ID);
            if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_LOGIN)) && !StringUtil.empty(userId)) {
                transaction.put(Transaction.BUYER_LOGIN, userId);
            }
        }


        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(respData, ResponseV3Fields.TRADE_NO);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        int payStatus = MapUtil.getIntValue(payTransaction, Transaction.STATUS);
        if (Transaction.STATUS_SUCCESS == payStatus) {
            // 支付成功交易撤单转退款
            String rc = refund(context);
            return Workflow.RC_REFUND_SUCCESS.equals(rc) ? Workflow.RC_CANCEL_SUCCESS : Workflow.RC_ERROR;
        } else {
            // 状态未知，进行撤单或关单
            int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
            if(subPayway == Order.SUB_PAYWAY_BARCODE){
                return revokeWhenCancel(context);
            }else{
                return closeWhenCancel(context);
            }
        }
    }

    private String closeWhenCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV3Builder builder = getDefaultRequestBuilder(config, context);
        builder.reqDataSet(BusinessV3Fields.ORIGIN_OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        setTermInfo(builder, context);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_CLOSE, config, builder.build(), 1);
        } catch (Exception ex) {
            logger.error("failed to call lakala open v3 close", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CLOSE);
        String responseCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        if (LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(responseCode)
                || LakalaOpenV3Constants.ResultCode.TRANSACTION_NOT_EXIST.getCode().equals(responseCode)){
            // 关单成功
            return Workflow.RC_CANCEL_SUCCESS; 
        }
        return Workflow.RC_ERROR;
    }

    public String revokeWhenCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV3Builder builder = getDefaultRequestBuilder(config, context);
        builder.reqDataSet(BusinessV3Fields.ORIGIN_OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        setTermInfo(builder, context);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_CANCEL, config, builder.build(), 1);
        } catch (Exception ex) {
            logger.error("failed to call lakala open v3 cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String responseCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        if (LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(responseCode)
                || LakalaOpenV3Constants.ResultCode.TRANSACTION_NOT_EXIST.getCode().equals(responseCode)){
            // 撤销成功
            return Workflow.RC_CANCEL_SUCCESS; 
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV3Builder builder = getDefaultRequestBuilder(config, context);
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_QUERY, config, builder.build(), retryTimes);
        } catch (Exception ex) {
            logger.error("failed to call lakala open v3 query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseV3Fields.CODE);
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        if(LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(responseCode)){
            Map<String, Object> respData = MapUtil.getMap(result, ResponseV3Fields.RESP_DATA);
            String tradeState = MapUtil.getString(respData, ResponseV3Fields.TRADE_STATE);
            if (LakalaOpenV3Constants.OrderStatus.SUCCESS.getCode().equals(tradeState)) {
                //success
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            } else if (LakalaOpenV3Constants.OrderStatus.CLOSE.getCode().equals(tradeState) 
                    || LakalaOpenV3Constants.OrderStatus.FAIL.getCode().equals(tradeState)) {
                return Workflow.RC_TRADE_CANCELED;
            } else if (LakalaOpenV3Constants.OrderStatus.INIT.getCode().equals(tradeState)
                    || LakalaOpenV3Constants.OrderStatus.CREATE.getCode().equals(tradeState)
                    || LakalaOpenV3Constants.OrderStatus.DEAL.getCode().equals(tradeState)
                    || LakalaOpenV3Constants.OrderStatus.DEAL.getCode().equals(tradeState)) {
                return Workflow.RC_IN_PROG;
            } else {
                return Workflow.RC_ERROR;
            }
        }else if(LakalaOpenV3Constants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)){
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        RequestV3Builder builder = getDefaultRequestBuilder(config, context);
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.reqDataSet(BusinessV3Fields.REFUND_AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.reqDataSet(BusinessV3Fields.ORIGIN_OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        setTermInfo(builder, context);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_REFUND, config, builder.build(), 1);
        } catch (Exception ex) {
            logger.error("failed to call lakala open v3 refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseV3Fields.CODE);
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        if(LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(responseCode)){
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    private void resolveRefundFund(Map<String, Object> result, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = new ArrayList<>();
        Map<String,Object> respData = (Map<String, Object>) result.get(ResponseV3Fields.RESP_DATA);
        String upCouponInfo = MapUtil.getString(respData, ResponseV3Fields.UP_COUPON_INFO);
        long couponSum = 0L;
        if (!StringUtils.isEmpty(upCouponInfo)) {
            List<Map<String, Object>> couponInfo = JsonUtil.jsonStrToObject(upCouponInfo, List.class);
            for(Map<String,Object> coupon: couponInfo){
                String spnsrId = BeanUtil.getPropString(coupon, ResponseV3Fields.COUPON_INFO_SPNSR_ID); //出资方
                long amount = BeanUtil.getPropLong(coupon, ResponseV3Fields.COUPON_INFO_OFFST_AMT);
                String couponId = BeanUtil.getPropString(coupon, ResponseV3Fields.COUPON_INFO_ID);
                String couponType = BeanUtil.getPropString(coupon, ResponseV3Fields.COUPON_INFO_TYPE);
                couponSum = couponSum + amount;
                //注意银联接口返回不能准确的区分商户优惠是否是免充值，默认当做是免充值的优惠。如果后续此通道对接了微信交易，需要特别注意。
                String paymentType;
                if(LakalaOpenV3Constants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)){
                    paymentType = LakalaOpenV3Constants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                }else{
                    paymentType = LakalaOpenV3Constants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                }
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, amount,
                        Transaction.PAYMENT_SOURCE, couponId,
                        Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                        Transaction.PAYMENT_TYPE, paymentType
                ));
            }
        }
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if(amount > 0){
            // 退款接口没有返回银行卡信息，取订单中的卡类型
            Map<String, Object> orderItem = MapUtil.getMap(context.getOrder(), Order.ITEMS);
            List<Map<String, Object>> channelPayments = (List<Map<String, Object>>) MapUtil.getObject(orderItem, Order.CHANNEL_PAYMENTS);
            String cardType = null;
            for (Map<String, Object> channelPayment : channelPayments) {
                String type = MapUtil.getString(channelPayment, Payment.TYPE);
                long netAmount = MapUtil.getLongValue(channelPayment, Payment.NET_AMOUNT);
                if (netAmount > amount && (Payment.TYPE_BANKCARD_DEBIT.equals(type) 
                        || Payment.TYPE_BANKCARD_CREDIT.equals(type) 
                        || Payment.TYPE_OTHERS.equals(type))) {
                    cardType = type;
                    break;
                }
            }
            String paymentType = null;
            if (!StringUtil.empty(cardType)) {
                paymentType = cardType;
            } else {
                paymentType = Payment.TYPE_OTHERS;
            }
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardType,
                    Transaction.PAYMENT_TYPE, paymentType
                    )
            );
        }
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if(oldPayments == null || oldPayments.isEmpty()){
            if (payments != null && !payments.isEmpty()) {
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
        }
        if(!StringUtil.empty(MapUtil.getString(respData, ResponseV3Fields.PAYER_AMOUNT))){
            long payAmt = MapUtil.getLongValue(respData, ResponseV3Fields.PAYER_AMOUNT);
            transaction.put(Transaction.PAID_AMOUNT, payAmt);
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        RequestV3Builder builder = getDefaultRequestBuilder(config, context);
        Map<String, Object> accBusiFields = MapUtil.hashMap(BusinessV3Fields.TIMEOUT_EXPRESS, DEFAULT_TIME_EXPIRE_MINUTE);
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.reqDataSet(BusinessV3Fields.ACCOUNT_TYPE, LakalaOpenV3Constants.AccountType.UQRCODEPAY.getCode());
        if (subPayway == Order.SUB_PAYWAY_QRCODE) {
            builder.reqDataSet(BusinessV3Fields.TRANS_TYPE, TransType.NATIVE.getCode());
        } else {
            builder.reqDataSet(BusinessV3Fields.TRANS_TYPE, TransType.JSAPI.getCode());
            accBusiFields.put(BusinessV3Fields.USER_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
            if (Order.PAYWAY_UNIONPAY == payway) {
                accBusiFields.put(BusinessV3Fields.UN_QRCODE, MapUtil.getString(extended, Transaction.QR_CODE));
                accBusiFields.put(BusinessV3Fields.USER_AUTH_CODE, MapUtil.getString(extended, Transaction.EXTENDED_USER_AUTH_CODE));
            }
        }
        if (Order.PAYWAY_JD == payway) {
            builder.reqDataSet(BusinessV3Fields.TRANS_TYPE, TransType.NATIVE.getCode());
            //如果是京东白条支付 拉卡拉下需要传一些格外的参数
            Map<String, Object> acqAddnDataOrderInfo = new HashMap<>();
            Map<String, Object> addnInfo = new HashMap<>();
            addnInfo.put(BusinessV3Fields.PRE_PRODUCT, MapUtil.getString(config, TransactionParam.PARAMS_BAITIAO_PRE_PRODUCT));
            Map extendParams = MapUtil.getMap(extended, BusinessV2Fields.EXTEND_PARAMS);
            if (MapUtil.isNotEmpty(extendParams)) {
                if (extendParams.containsKey(TransactionParam.BAITIAO_FQ_NUM)) {
                    String fqNum = MapUtil.getString(extendParams, TransactionParam.BAITIAO_FQ_NUM);
                    if (!StringUtils.isEmpty(fqNum)) {
                        addnInfo.put(BusinessV3Fields.LOCK_PLAN, fqNum);
                    }
                    //没用的参数移除掉 不透传给通道源
                    extendParams.remove(TransactionParam.BAITIAO_FQ_NUM);
                    if (extendParams.isEmpty()) {
                        extended.remove(BusinessV2Fields.EXTEND_PARAMS);
                    }
                }
            }
            Map<String, Object> riskInfo = new HashMap<>();
            //目前仅支持微信APP扫一扫
            riskInfo.put(BusinessV3Fields.ORDER_SOURCE, LakalaOpenV3Constants.OrderSource.WEIXIN_APP.getValue());
            riskInfo.put(BusinessV3Fields.PAY_USER_Id, MapUtil.getString(extraParams, Transaction.PAYER_UID));
            riskInfo.put(BusinessV3Fields.PAY_CODE_ID, BeanUtil.getPropString(transaction, Transaction.KEY_TERMINAL_SN));
            riskInfo.put(BusinessV3Fields.ITEM_NO, BeanUtil.getPropString(config, TransactionParam.PARAMS_BAITIAO_ITEM_NO));
            if (Objects.equals(0, MapUtil.getInteger(config, TransactionParam.PARAMS_MERC_TYPE))) {
                riskInfo.put(BusinessV3Fields.MERCHANT_TYPE, LakalaOpenV3Constants.MerchantType.INDIVIDUAL.getValue());
            }
            addnInfo.put(BusinessV3Fields.RICK_INFO, riskInfo);
            acqAddnDataOrderInfo.put(BusinessV3Fields.ADDN_INFO, addnInfo);
            accBusiFields.put(BusinessV3Fields.ACQ_ADDN_DATA_ORDER_INFO, acqAddnDataOrderInfo);
        }
        builder.reqDataSet(BusinessV3Fields.TOTAL_AMOUNT, MapUtil.getString(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.reqDataSet(BusinessV3Fields.SUBJECT, MapUtil.getString(transaction, Transaction.SUBJECT));
        builder.reqDataSet(BusinessV3Fields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        builder.reqDataSet(BusinessV3Fields.ACC_BUSI_FIELDS, accBusiFields);
        setTermInfo(builder, context);
        carryOverExtendedParams(extended, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(OP_PRECREATE, config, builder.build(), 1);
        } catch (MpayException | MpayApiNetworkError ex) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            logger.error("failed to call lakala open v3 precreate", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        String resultCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        if (LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(resultCode)) {
            Map<String, Object> respData = MapUtil.getMap(result, ResponseV3Fields.RESP_DATA);
            Map<String, Object> accRespFields = MapUtil.getMap(respData, ResponseV3Fields.ACC_RESP_FIELDS);
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (payway == Order.PAYWAY_JD) {
                String qrCode = MapUtil.getString(accRespFields, ResponseV3Fields.CODE);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(Transaction.REDIRECT_URL, String.format(jdRedirectUrlFormat, MapUtil.getString(respData, BusinessV3Fields.MERCHANT_NO), qrCode)));
            } else if (subPayway == Order.SUB_PAYWAY_QRCODE) {
                extraOutFields.put(Transaction.QRCODE, MapUtil.getString(accRespFields, ResponseV3Fields.CODE));
            } else {
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(Transaction.REDIRECT_URL, MapUtil.getString(accRespFields, BusinessV3Fields.REDIRECT_URL)));
            }

            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if (type != Transaction.TYPE_PAYMENT) {
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_LAKALA_UNION_PAY_V3;
    }

    private RequestV3Builder getDefaultRequestBuilder(Map<String, Object> config, TransactionContext context) {
        RequestV3Builder requestBuilder = new RequestV3Builder();
        requestBuilder.reqDataSet(BusinessV3Fields.MERCHANT_NO, BeanUtil.getPropString(config, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID));
        requestBuilder.reqDataSet(BusinessV3Fields.TERM_NO, BeanUtil.getPropString(config, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID));
        requestBuilder.reqSet(BusinessV3Fields.REQ_TIME, formatTimeString(System.currentTimeMillis()));
        requestBuilder.reqSet(BusinessV3Fields.VERSION, LakalaOpenV3Constants.VERSION);
        Map configSnapshot = com.wosai.pantheon.util.MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT);
        String extTermId = com.wosai.pantheon.util.MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        if(extTermId != null){
            requestBuilder.reqDataSet(BusinessV3Fields.TERM_NO, extTermId);
        }
        return requestBuilder;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String responseCode = BeanUtil.getPropString(result, ResponseV3Fields.CODE);
        String message = BeanUtil.getPropString(result, ResponseV3Fields.MSG);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseV3Fields.CODE, responseCode);//返回状态码
        map.put(ResponseV3Fields.MSG, message);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, LakalaOpenV3Constants.ResultCode.SUCCESS.getCode().equals(responseCode), responseCode, message);
    }

    protected Map<String, Object> retryIfNetworkException(String logFlag, Map<String, Object> config, Map<String, Object> request, int times) throws MpayException, MpayApiNetworkError {
        String url;
        if (ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY.equals(logFlag)) {
            url = ApolloConfigurationCenterUtil.getProviderGatewayConfig(getName(), logFlag);
        } else {
            url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), logFlag);
        }
        String appId = MapUtil.getString(config, TransactionParam.LAKALA_UNION_PAY_OPEN_APP_ID);
        String privateKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.LAKALA_PRIVATE_KEY));
        String serialNo = MapUtil.getString(config, BusinessV3Fields.SERIA_NO);
        MpayApiNetworkError tex = null;
        for (int i = 0; i < times; ++i) {
            try {
                return client.call(url, appId, serialNo, privateKey, request);
            } catch (MpayApiNetworkError ex) {
                tex = ex;
                logger.warn("encountered ioex in lakala open {}", logFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw tex;
    }

    public void carryOverExtendedParams(Map<String, Object> extended, RequestV3Builder builder) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = EXTENDED_CONVERT_MAP.getOrDefault(extendedParam.getKey(), extendedParam.getKey());
            Object value = extendedParam.getValue();
            if(overFilterField(key) || value == null) {
                continue;
            }
            if (UpayConstant.GOODS.equals(key)) {
                Map<String, Object> accBusiFields = MapUtil.getMap(builder.getReqData(), BusinessV3Fields.ACC_BUSI_FIELDS);
                if (accBusiFields == null) {
                    accBusiFields = new HashMap<>();
                    builder.reqDataSet(BusinessV3Fields.ACC_BUSI_FIELDS, accBusiFields);
                }
                accBusiFields.put(BusinessV3Fields.ACQ_ADDN_DATA_GOODS_INFO, JsonUtil.toJsonStr(value));
            } else if(BusinessV3Fields.FRONT_URL.equals(key) || BusinessV3Fields.FRONT_FAIL_URL.equals(key)) {
                Map<String, Object> accBusiFields = MapUtil.getMap(builder.getReqData(), BusinessV3Fields.ACC_BUSI_FIELDS);
                if (accBusiFields == null) {
                    accBusiFields = new HashMap<>();
                    builder.reqDataSet(BusinessV3Fields.ACC_BUSI_FIELDS, accBusiFields);
                }
                accBusiFields.put(key, value);
            } else {
                builder.reqDataSet(key, value);
            }
        }
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        Map<String,Object> extraParams = MapUtils.getMap(transaction, Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            return null;
        }
        Map<String, Object> config = getTradeParams(transaction);
        com.wosai.mpay.api.lakala.open.RequestBuilder builder = new com.wosai.mpay.api.lakala.open.RequestBuilder();
        builder.reqDataSet(BusinessFields.MERC_ID, BeanUtil.getPropString(config, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID));
        builder.reqDataSet(BusinessFields.TERM_NO, BeanUtil.getPropString(config, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID));
        // 使用报备的银行终端号
        Map<String, Object> configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String extTermId = MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        if(extTermId != null){
            builder.reqDataSet(BusinessFields.TERM_NO, extTermId);
        }
        builder.reqDataSet(BusinessFields.TRADE_CODE, com.wosai.mpay.api.lakala.open.LakalaConstants.TRADE_CODE_UNIONPAY);
        //授权码
        builder.reqDataSet(BusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        //银联支付标识
        builder.reqDataSet(BusinessFields.APP_UP_IDENTIFIER, MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER));

        Map<String,Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY, config, builder.build(), 1);
        } catch (Exception ex) {
            logger.error("failed to call lakala open v3 queryUserInfo", ex);
            return null;
        }
        Map<String, Object> respData = MapUtil.getMap(result, com.wosai.mpay.api.lakala.open.ResponseFields.RESP_DATA);
        String userId = MapUtil.getString(respData, com.wosai.mpay.api.lakala.open.ResponseFields.USER_ID);
        if(StringUtils.isEmpty(userId)){
            return null;
        }
        return CollectionUtil.hashMap(com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID, userId);
    }
}
