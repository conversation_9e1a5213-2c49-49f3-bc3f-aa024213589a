package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.unionpayonline.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2019/6/10.
 * 在线网关支付：https://open.unionpay.com/tjweb/acproduct/list?apiservId=448&version=V2.2
 * 企业网银支付（B2B支付） https://open.unionpay.com/tjweb/acproduct/list?apiservId=452&version=V2.2
 */
public class UnionPayOnlineServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(UnionPayOnlineServiceProvider.class);

    public static final String NAME = "provider.unionpay.online";
    public static final int DEFAULT_TIME_EXPIRE_MINUTE = 4; // 订单默认过期时间设定为4分钟

    @Autowired
    private UnionPayOnlineClient client;
    private int retryTimes = 3;
    private String notifyHost;


    public UnionPayOnlineServiceProvider(){
        dateFormat = new com.wosai.upay.util.SafeSimpleDateFormat(UnionPayOnlineConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.CURRENCY_CODE, BusinessFields.TXN_AMT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (payWay == Order.PAYWAY_BANKCARD && subPayway == Order.SUB_PAYWAY_WAP) ? true : false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_ONLINE_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_UNIONPAY_ONLINE;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("不支持此支付方式");
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("不支持此支付方式");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        builder.set(BusinessFields.TXN_TIME, formatTimeString(BeanUtil.getPropLong(transaction, DaoConstants.CTIME)));
        builder.set(BusinessFields.TXN_TYPE, UnionPayOnlineConstants.TXN_TYPE_QUERY);
        builder.set(BusinessFields.TXN_SUB_TYPE, UnionPayOnlineConstants.TXN_SUB_TYPE_00);
        builder.set(BusinessFields.ORDER_ID, transaction.get(Transaction.ORDER_SN) + "");
        Map<String, String> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open online", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE, "");
        String originResponseCode = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_CODE, "");
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        switch (responseCode){
            case UnionPayOnlineConstants.RESP_CODE_SUCCESS:
                //query success
                switch (originResponseCode){
                    case UnionPayOnlineConstants.RESP_CODE_SUCCESS:
                    case UnionPayOnlineConstants.RESP_CODE_PARTIAL_SUCCESS:
                        //pay success
                        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                        resolvePayFund(result, context);
                        clearBizErrorCode(transaction);
                        return Workflow.RC_PAY_SUCCESS;
                    case UnionPayOnlineConstants.RESP_CODE_TIMEOUT:
                    case UnionPayOnlineConstants.RESP_CODE_REQUEST_ACCEPTED:
                        return Workflow.RC_IN_PROG;
                    default:
                        return Workflow.RC_ERROR;
                }
            case UnionPayOnlineConstants.RESP_CODE_SYSTEM_BUSY:
            case UnionPayOnlineConstants.RESP_CODE_TIMEOUT:
                return Workflow.RC_IN_PROG;
            case UnionPayOnlineConstants.RESP_CODE_ORDER_NOT_EXISTS:
                long ctime = BeanUtil.getPropLong(transaction, DaoConstants.CTIME);
                return System.currentTimeMillis() - ctime > 4 * 60 * 1000 ? Workflow.RC_TRADE_CANCELED : Workflow.RC_IN_PROG;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> order = context.getOrder();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        builder.set(BusinessFields.TXN_TIME, formatTimeString(BeanUtil.getPropLong(transaction, DaoConstants.CTIME)));
        builder.set(BusinessFields.TXN_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.TXN_TYPE, UnionPayOnlineConstants.TXN_TYPE_REFUND);
        builder.set(BusinessFields.TXN_SUB_TYPE, UnionPayOnlineConstants.TXN_SUB_TYPE_00);
        builder.set(BusinessFields.BACK_URL, "http://www.specialUrl.com");//忽略通知
        builder.set(BusinessFields.ORIG_QRY_ID, order.get(Order.TRADE_NO) + ""); // ?
        builder.set(BusinessFields.ORDER_ID, transaction.get(Transaction.TSN) + ""); // ?
        String payChannelType = (String) BeanUtil.getNestedProperty(transaction, Transaction.KEY_UNION_PAY_CHANNEL_TYPE);
        if(payChannelType == null){
            payChannelType = UnionPayOnlineConstants.CHANNEL_TYPE_PC;
        }
        builder.set(BusinessFields.CHANNEL_TYPE, payChannelType);
        Map<String, String> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call unionpay online refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE, "");
        switch (responseCode){
            case UnionPayOnlineConstants.RESP_CODE_SUCCESS:
                //success
                resolveRefundFund(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(config);
        builder.set(BusinessFields.TXN_TIME, formatTimeString(BeanUtil.getPropLong(transaction, DaoConstants.CTIME)));
        builder.set(BusinessFields.TXN_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOnlineConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.TXN_TYPE, UnionPayOnlineConstants.TXN_TYPE_PRECREATE);
        builder.set(BusinessFields.TXN_SUB_TYPE, UnionPayOnlineConstants.TXN_SUB_TYPE_01);
        builder.set(BusinessFields.CHANNEL_TYPE, UnionPayOnlineConstants.CHANNEL_TYPE_PC); //默认pc
        builder.set(BusinessFields.ORDER_ID, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORDER_DESC, transaction.get(Transaction.SUBJECT) + "");
        builder.set(BusinessFields.ORDER_TIMEOUT, "1"); //网页1分钟超时
        builder.set(BusinessFields.PAY_TIMEOUT, formatTimeString(System.currentTimeMillis() + DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000));
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if(upayOrderNumber != null){
            builder.set(BusinessFields.BACK_URL, getNotifyUrl(notifyHost, context));
        }
        carryOverExtendedParams(extended, builder);
        Map<String,String> request = builder.build();
        try {
            //银联网银支付，此处没有调用支付通道的接口，准备数据供前端去调用。
            UnionPayOnlineClient.addSignInfo(getPrivateKeyContent((String)config.get(TransactionParam.UNION_PAY_ONLINE_PRIVATE_KEY)), request);
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, request);
            extraOutFields.put(Transaction.UNION_PAY_CHANNEL_TYPE, request.get(BusinessFields.CHANNEL_TYPE));//用于撤单等
            return Workflow.RC_CREATE_SUCCESS;
        } catch (Exception e) {
            return Workflow.RC_IOEX;
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT){
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    public Map<String, String> retryIfNetworkException(Map<String,Object> config, String url, Map<String,String> request, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                return client.call(url, getPrivateKeyContent((String)config.get(TransactionParam.PRIVATE_KEY)), request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in unionpay online {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    public static RequestBuilder getDefaultRequestBuilder(Map<String, Object> config){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.ACCESS_TYPE, UnionPayOnlineConstants.ACCESS_TYPE_MERCHANT);
        builder.set(ProtocolFields.MER_ID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_ONLINE_MCH_ID));
        builder.set(ProtocolFields.CERT_ID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_ONLINE_CERT_ID));
        builder.set(ProtocolFields.BIZ_TYPE, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_ONLINE_BIZ_TYPE));
        return builder;
    }

    public void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null && !TransactionParam.UNION_PAY_ONLINE_MCH_ID.equals(key)) {
                builder.set(key, value + "");
            }
        }
    }

    public void setTradeNoBuyerInfoIfExists(Map<String, String> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerId = MapUtil.getString(result, ResponseFields.ACC_NO);
            if (!StringUtil.empty(buyerId)) {
                transaction.put(Transaction.BUYER_UID, buyerId);
            }
        }
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(result, ResponseFields.QUERY_ID);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    public void resolvePayFund(Map<String, String> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = new ArrayList<>();
        long amount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        String payCardType = BeanUtil.getPropString(result, ResponseFields.PAY_CARD_TYPE);
        String type = UnionPayOnlineConstants.PAY_CARD_TYPE_CREDIT_CARD.equals(payCardType) ? UnionPayOnlineConstants.PAY_CARD_TYPE_CREDIT_CARD : UnionPayOnlineConstants.PAY_CARD_TYPE_DEBIT_CARD;
        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, amount,
                Transaction.PAYMENT_ORIGIN_TYPE, payCardType,
                Transaction.PAYMENT_TYPE, type
        ));
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        transaction.put(Transaction.PAID_AMOUNT, amount);
        transaction.put(Transaction.RECEIVED_AMOUNT, amount);
    }

    public boolean isFullRefund(TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        return BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
    }

    public void resolveRefundFund(Map<String, String> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if(isFullRefund(context)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else {
            //部分退款
            List<Map<String,Object>> payments = new ArrayList<>();
            long amount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            long payAmount = BeanUtil.getPropLong(payTransaction, Transaction.EFFECTIVE_AMOUNT);
            List<Map<String,Object>> payPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            if(payPayments != null){
                for(Map<String,Object> payPayment: payPayments){
                    Map<String,Object> clone = new HashMap<>(payPayment);
                    long cloneAmount = BeanUtil.getPropLong(clone, Transaction.PAYMENT_AMOUNT);
                    long newCloneAmount = cloneAmount * amount / payAmount;
                    clone.put(Transaction.PAYMENT_AMOUNT, newCloneAmount);
                    payments.add(clone);
                }
                Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                extraOutFields.put(Transaction.PAYMENTS, payments);
                transaction.put(Transaction.PAID_AMOUNT, amount);
                transaction.put(Transaction.RECEIVED_AMOUNT, amount);
            }
        }
    }

    protected void setTransactionContextErrorInfo(Map<String,String> result, TransactionContext context , String key) {
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE, "");
        String responseMsg = BeanUtil.getPropString(result, ResponseFields.RESP_MSG);
        String originalResponseCode = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_CODE, "");
        String originalResponseMsg = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_MSG);

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RESP_CODE, responseCode);
        map.put(ResponseFields.RESP_MSG, responseMsg);
        map.put(ResponseFields.ORIG_RESP_CODE, originalResponseCode);
        map.put(ResponseFields.ORIG_RESP_MSG, originalResponseMsg);
        String codes = responseCode + originalResponseCode; // 00 或者 0000 为成功
        boolean success = UnionPayOnlineConstants.RESP_CODE_SUCCESS.equals(codes) || (UnionPayOnlineConstants.RESP_CODE_SUCCESS + UnionPayOnlineConstants.RESP_CODE_SUCCESS).equals(codes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, success, StringUtil.empty(originalResponseCode) ? responseCode: originalResponseCode, StringUtil.empty(originalResponseMsg) ? responseMsg : originalResponseMsg);
    }


    public String getNotifyHost() {
        return notifyHost;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }
}
