package com.wosai.upay.workflow;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

import java.util.Map;

/***
 * @ClassName: HaikeRefundOrderWorkflow
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/19 4:30 PM
 */
public class HaikeRefundOrderWorkflow extends RefundOrderWorkflow{
    private static long[] delays = {500, 1000, 1500, 2000, 2000, 3000, 5000, 5000, 10000, 10000, 10000, 15000};
    private static final String NAME = "generic.haike.refund.workflow";

    public HaikeRefundOrderWorkflow() {
        super(delays);
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_REFUND) {
            if (Order.PROVIDER_HAIKE_UNION_PAY == MapUtil.getIntValue(transaction, Transaction.PROVIDER)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String getName() {
        return NAME;
    }
}