package com.wosai.upay.workflow;

import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> @description 微信周期代扣款 商户模式
 * @link <a href="https://pay.weixin.qq.com/doc/v2/merchant/**********">微信周期代扣费文档（商户模式）</a>
 * @date 2025-05-08
 */
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 2)
public class DirectWeixinV2CycleMerchantServiceProvider extends DirectWeixinV2CycleServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(DirectWeixinV2CycleMerchantServiceProvider.class);
    public static final String NAME = "provider.weixin.cycle.v2.merchant";

    public DirectWeixinV2CycleMerchantServiceProvider() {
        extendedFilterFields = new HashSet<>(Collections.singletonList(Transaction.SQB_PRODUCT_CODE));
    }

    @Override
    public String getName() {
        return NAME;
    }

    protected int getServiceMode() {
        return TransactionParam.SERVICE_MODE_MERCHANT;
    }

    /**
     * 构建预授权请求参数
     *
     * @param extended    扩展参数
     * @param tradeConfig 交易配置
     * @return 请求参数
     */
    protected Map<String, Object> buildPreFreezeRequest(Map<String, Object> extended, Map<String, Object> tradeConfig) {
        Map<String, Object> requestParams = new HashMap<>(8);
        requestParams.put(ProtocolFields.MCHID, MapUtils.getString(tradeConfig, TransactionParam.WEIXIN_SUB_MCH_ID));
        requestParams.put(ProtocolFields.APP_ID, MapUtils.getString(extended, ProtocolFields.SUB_APP_ID));
        requestParams.put(BusinessFields.DEDUCT_DURATION, MapUtils.getMap(extended, BusinessFields.DEDUCT_DURATION));
        requestParams.put(BusinessFields.ESTIMATED_AMOUNT, MapUtils.getMap(extended, BusinessFields.ESTIMATED_AMOUNT));
        return requestParams;
    }

    protected String getWeixinMchIdByServiceMode(Map<String, Object> config) {
        return MapUtil.getString(config, TransactionParam.WEIXIN_SUB_MCH_ID);
    }

    /**
     * 获取默认的requestBuilder，设置请求默认值。
     *
     * @param context
     * @return
     */
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extended = MapUtils.getMap(transaction, Transaction.EXTENDED_PARAMS);
        Map<String, Object> config = getTradeParams(transaction);

        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.MCH_ID, MapUtils.getString(config, TransactionParam.WEIXIN_SUB_MCH_ID));
        builder.set(ProtocolFields.APP_ID, MapUtils.getString(extended, ProtocolFields.SUB_APP_ID));
        return builder;
    }

    //extended透传给支付通道
    protected void carryDepositOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if (MapUtils.isEmpty(extended)) {
            return;
        }

        extended.forEach((key, value) -> {
            if (overFilterField(key) || Objects.isNull(value)) {
                return;
            }
            if (key.equals(ProtocolFields.SUB_APP_ID)) {
                builder.set(ProtocolFields.APP_ID, value);
            } else if (key.equals(Transaction.CLIENT_IP)) {
                builder.set(BusinessFields.SPBILL_CREATE_IP, value);
            } else {
                builder.set(key, value);
            }
        });
    }
}
