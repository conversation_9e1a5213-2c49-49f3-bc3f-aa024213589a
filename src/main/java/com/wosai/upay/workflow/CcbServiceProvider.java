package com.wosai.upay.workflow;

import com.ccb.mis.CcbMisSdk;
import com.ccb.mis.entity.ParamEntity;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.ccb.CcbConstants;
import com.wosai.mpay.api.ccb.ResponseFields;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> Date: 2021/8/25 Time: 2:39 下午
 */
public class CcbServiceProvider extends AbstractServiceProvider {
    public static final Logger LOGGER = LoggerFactory.getLogger(CcbServiceProvider.class);

    public static final String NAME = "provider.ccb";

    public static final String FIX_TERM_TYPE_VALUE  = "11"; //条码支付辅助受理终端


    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return subPayway == Order.SUB_PAYWAY_BARCODE && Objects.nonNull(getTradeParams(transaction));
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CCB_TRADE_PARAMS);
    }

    @SneakyThrows
    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_PARAMS);

        String instNo = MapUtil.getString(tradeParams, TransactionParam.CCB_INST_NO);
        String merchantId = MapUtil.getString(tradeParams, TransactionParam.CCB_MERCHANT_ID);
        String terminalId = MapUtil.getString(tradeParams, TransactionParam.CCB_TERMINAL_ID);
        String terminalNo = MapUtil.getString(tradeParams, TransactionParam.CCB_TERMINAL_NO);
        String key = MapUtil.getString(tradeParams, TransactionParam.CCB_SECRET_KEY);

        ParamEntity paramEntity = new ParamEntity();
        paramEntity.setInstNo(instNo);
        paramEntity.setMerchantId(merchantId);
        paramEntity.setTerminalId(terminalId);
        paramEntity.setTerminalNo(terminalNo);
        paramEntity.setKey(key);
        paramEntity.setTermSN(terminalId);
        paramEntity.setTermType(FIX_TERM_TYPE_VALUE);
        paramEntity.setUrl(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY));

        String orderSn = MapUtil.getString(transaction, Transaction.TSN);
        String barCode = MapUtil.getString(extraParams, Transaction.BARCODE);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        String result;
        try {
            result = CcbMisSdk.getInstance().getMisAggregateService(paramEntity)
                    .aggregatePay(com.wosai.mpay.util.StringUtils.cents2yuan(amount)
                            , orderSn, barCode, null, null, null, null, B2C_TIME_EXPIRE_MINUTE + "m");

            LOGGER.info("[建行B2C支付]>>>>>>result: {}", result);
        } catch (Exception e) {
            LOGGER.error("failed to call ccb pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IN_PROG;
        }
        Map<String, Object> resultMap = JsonUtil.jsonStringToObject(result, Map.class);
        setTransactionContextErrorInfo(resultMap, context, OP_PAY);
        return buildPayResult(resultMap, context);
    }

    @Override
    public String cancel(TransactionContext context) {

        return cancelToRefundProcess(context);
    }

    @SneakyThrows
    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String instNo = MapUtil.getString(tradeParams, TransactionParam.CCB_INST_NO);
        String merchantId = MapUtil.getString(tradeParams, TransactionParam.CCB_MERCHANT_ID);
        String terminalId = MapUtil.getString(tradeParams, TransactionParam.CCB_TERMINAL_ID);
        String terminalNo = MapUtil.getString(tradeParams, TransactionParam.CCB_TERMINAL_NO);
        String key = MapUtil.getString(tradeParams, TransactionParam.CCB_SECRET_KEY);

        ParamEntity paramEntity = new ParamEntity();
        paramEntity.setInstNo(instNo);
        paramEntity.setMerchantId(merchantId);
        paramEntity.setTerminalId(terminalId);
        paramEntity.setTerminalNo(terminalNo);
        paramEntity.setKey(key);
        paramEntity.setUrl(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY));

        String oriSn = MapUtil.getString(transaction, Transaction.TSN);

        String result;
        try {
            if (isTodayTransaction(transaction)) {
                result = CcbMisSdk.getInstance().getMisAggregateService(paramEntity)
                        .aggregatePayQuery(oriSn);
            } else {
                result = CcbMisSdk.getInstance().getMisAggregateService(paramEntity).queryScanPrint(oriSn);
            }
            LOGGER.info("[建行B2C支付查询]>>>>>>result: {}", result);
        } catch (Exception e) {
            LOGGER.error("failed to call ccb query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IN_PROG;
        }
        Map resultMap = JsonUtil.jsonStringToObject(result, Map.class);
        setTransactionContextErrorInfo(resultMap, context, OP_QUERY);
        return buildQueryResult(resultMap, context);
    }

    @SneakyThrows
    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String instNo = MapUtil.getString(tradeParams, TransactionParam.CCB_INST_NO);
        String merchantId = MapUtil.getString(tradeParams, TransactionParam.CCB_MERCHANT_ID);
        String terminalId = MapUtil.getString(tradeParams, TransactionParam.CCB_TERMINAL_ID);
        String terminalNo = MapUtil.getString(tradeParams, TransactionParam.CCB_TERMINAL_NO);
        String key = MapUtil.getString(tradeParams, TransactionParam.CCB_SECRET_KEY);

        ParamEntity paramEntity = new ParamEntity();
        paramEntity.setInstNo(instNo);
        paramEntity.setMerchantId(merchantId);
        paramEntity.setTerminalId(terminalId);
        paramEntity.setTerminalNo(terminalNo);
        paramEntity.setKey(key);
        paramEntity.setUrl(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND));

        String orderSn = MapUtil.getString(transaction, Transaction.TSN);
        String oriSn = MapUtil.getString(transaction, Transaction.ORDER_SN);
        long refundAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        String result;
        try {
            result = CcbMisSdk.getInstance().getMisAggregateService(paramEntity)
                    .aggregatePayRefund(com.wosai.mpay.util.StringUtils.cents2yuan(refundAmount)
                            , oriSn, orderSn);
            LOGGER.info("[建行B2C退款]>>>>>>result: {}", result);
        } catch (Exception e) {
            LOGGER.error("failed to call ccb refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        Map resultMap = JsonUtil.jsonStringToObject(result, Map.class);
        setTransactionContextErrorInfo(resultMap, context, OP_REFUND);
        return buildRefundResult(resultMap, context);
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }


    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        if (StringUtils.equals(CcbConstants.RET_CODE_SUCCESS, retCode)) {
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (CcbConstants.B2C_RET_CODE_UNKNOWN_SET.contains(retCode)) {
            return Workflow.RC_IN_PROG;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        String retCode = MapUtil.getString(result, ResponseFields.RET_CODE);
        Map<String, Object> transData = MapUtil.getMap(result, ResponseFields.TRANS_DATA);
        String statusCode = MapUtil.getString(transData, ResponseFields.STATUS_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        if (StringUtils.equals(CcbConstants.RET_CODE_SUCCESS, retCode)
                && StringUtils.equals(CcbConstants.STATUS_CODE_SUCCESS, statusCode)) {
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        if (CcbConstants.B2C_RET_CODE_UNKNOWN_SET.contains(retCode)
                || StringUtils.equals(CcbConstants.STATUS_CODE_PROCESSING, statusCode)) {
            return Workflow.RC_IN_PROG;
        } else if (StringUtils.equals(CcbConstants.STATUS_CODE_FAILURE, retCode)) {
            return Workflow.RC_ERROR;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private String cancelToRefundProcess(TransactionContext context) {
        String result = refund(context);
        if (Workflow.RC_REFUND_SUCCESS.equals(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        return result;
    }

    private String buildRefundResult(Map result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String responseCode = BeanUtil.getPropString(result, ResponseFields.RET_CODE);
        if (Objects.equals(responseCode, CcbConstants.RET_CODE_SUCCESS)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    protected void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transData = MapUtil.getMap(result, ResponseFields.TRANS_DATA);
        Map<String, Object> transaction = context.getTransaction();
        long amount = com.wosai.mpay.util.StringUtils.yuan2cents(MapUtil
                .getString(transData, ResponseFields.AMT));
        transaction.put(Transaction.PAID_AMOUNT, amount);
        transaction.put(Transaction.RECEIVED_AMOUNT, amount);
        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        List<Map<String,Object>> payments = null;
        if (Payway.CCB_APP.getCode().equals(payway)) {
            payments = buildCCBPayments(transData, transaction);
        }
        if (CollectionUtils.isNotEmpty(payments)) {
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        }
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transData = MapUtil.getMap(result, ResponseFields.TRANS_DATA);
        Map<String,Object> transaction = context.getTransaction();

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            //支付宝微信建行会返回pid, 云闪付建行会返回cardNo
            String userId = getOrDefault(transData, ResponseFields.PID, ResponseFields.CARD_NO);
            if (!StringUtil.empty(userId)) {
                transaction.put(Transaction.BUYER_UID, userId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            //支付源订单号
            String tradeNo = MapUtil.getString(transData, ResponseFields.THIRD_TRADE_NO);
            int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            if(payway == Order.PAYWAY_ALIPAY2){
                tradeNo = getAlipayRealTradeNo(Order.PROVIDER_CCB, tradeNo);
            }
            if(!StringUtil.empty(tradeNo)){
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }

    }

    protected void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context
                .getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder()
                , DaoConstants.CTIME)));
    }

    private void setTransactionContextErrorInfo(Map<String, Object> result
            , TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap();
        String respCode;
        String systemRetCode = (String) BeanUtil.getNestedProperty(result, ResponseFields.TRANS_DATA
                + "." + ResponseFields.SYSTEM_RET_CODE);
        if (StringUtils.isNotEmpty(systemRetCode)) {
            respCode = systemRetCode;
        } else {
            respCode = getOrDefault(result, ResponseFields.SYSTEM_RET_CODE, ResponseFields.RET_CODE);
        }
        String respMsg = BeanUtil.getPropString(result, ResponseFields.RET_MSG, StringUtils.EMPTY);
        map.put(ResponseFields.RET_CODE, respCode);
        if (Objects.nonNull(respCode) && (StringUtils.equals(CcbConstants.RET_CODE_FAILURE, respCode) || StringUtils.equals(CcbConstants.RET_CODE_UNKNOWN, respCode))){
            respMsg = respMsg.replaceAll("[0-9]", StringUtils.EMPTY); //当retCode = -1/-2时，过滤错误信息里面的随机数
        }
        map.put(ResponseFields.RET_MSG, respMsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, Objects.equals(respCode, CcbConstants.RET_CODE_SUCCESS), respCode, respMsg);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_CCB;
    }

    public String getOrDefault(Map<String,Object> result, String key, String defaultKey){
        String value = MapUtil.getString(result, key);
        return StringUtil.empty(value) ? MapUtil.getString(result, defaultKey) : value;
    }

    /**
     * 构建 payments
     *
     * @param transData  transData子节点
     * @return
     */
    private List<Map<String,Object>> buildCCBPayments(Map<String, Object> transData, Map<String, Object> transaction) {
        //交易金额
        long amt = com.wosai.mpay.util.StringUtils.yuan2cents((String) transData.get("amt"));
        //折后金额
        long disAmt = com.wosai.mpay.util.StringUtils.yuan2cents((String) transData.get("disAmt"));
        //通道优惠
        long channelAmt = amt - disAmt;
        if (channelAmt <= 0) {
            return null;
        }
        //　有优惠时则以折后金额为准
        transaction.put(Transaction.PAID_AMOUNT, disAmt);

        return Collections.singletonList(CollectionUtil.hashMap(
                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                Transaction.PAYMENT_ORIGIN_TYPE, null,
                Transaction.PAYMENT_AMOUNT, channelAmt
        ));
    }


}
