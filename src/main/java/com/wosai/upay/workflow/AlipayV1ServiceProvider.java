package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;

import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.util.UpayUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.upay.model.dao.Order;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

import org.springframework.beans.factory.annotation.Autowired;

public class AlipayV1ServiceProvider extends AbstractServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(AlipayV1ServiceProvider.class);

    private String notifyHost;
    public static final String NAME = "provider.alipay.v1";
    private String b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE + "m";         //B扫C订单自动关闭时间
    private String defaultTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE + "m";  //C扫B订单自动关闭时间

    protected static final int NOTIFY_URL_LIMIT = 256;
    
    public static final String FC_00_HONGBAO = "00";            // 支付宝红包
    public static final String FC_10_BALANCE = "10";            // 支付宝余额
    public static final String FC_60_DEPOSIT = "60";            // 支付宝预存卡
    public static final String FC_30_POINT = "30";              // 积分
    public static final String FC_70_CREDIT = "70";             // 信用支付
    public static final String FC_40_VOUCHER = "40";            // 折扣券
    public static final String FC_80_PREPAID = "80";            // 预付卡
    public static final String FC_90_FINANCE = "90";            // 信用支付(消费信贷)
    public static final String FC_100_BROKERAGE = "100";        // 支付宝理财专户
    public static final String FC_101_MERCHANT_CARD = "101";    // 商户店铺卡
    public static final String FC_102_MERCHANT_VOUCHER = "102"; // 商户优惠券

    private Set<String> consumerFunds = CollectionUtil.hashSet(FC_10_BALANCE,
                                                               FC_60_DEPOSIT,
                                                               FC_70_CREDIT,
                                                               FC_80_PREPAID,
                                                               FC_90_FINANCE,
                                                               FC_100_BROKERAGE);
    
    private Set<String> alipayFunds = CollectionUtil.hashSet(FC_00_HONGBAO,
                                                             FC_30_POINT,
                                                             FC_40_VOUCHER);

    private Set<String> merchantFunds = CollectionUtil.hashSet(FC_101_MERCHANT_CARD,
                                                               FC_102_MERCHANT_VOUCHER,
                                                               "104");


    @Autowired
    private AlipayV1Client client;

    public AlipayV1ServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessV1Fields.TOTAL_FEE));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Order.PAYWAY_ALIPAY == com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY)
                && Order.SUB_PAYWAY_WAP != com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY)) {

        	if(getTradeParams(transaction) == null){
                return false;
            }
            String currency = getTradeCurrency(transaction);
            return TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency);
        }
        return false;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestV1Builder builder = getDefaultRequestV1Builder();

        String notifyUrl = getNotifyUrl(notifyHost,context);
        if (notifyUrl != null) {
            builder.set(ProtocolV1Fields.NOTIFY_URL, notifyUrl);
        }

        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_CREATEANDPAY);
        builder.set(BusinessV1Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessV1Fields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        builder.set(BusinessV1Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessV1Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_BARCODE_PAY_OFFLINE);
        builder.set(BusinessV1Fields.TOTAL_FEE, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessV1Fields.IT_B_PAY, b2cTimeoutExpress);
        builder.set(BusinessV1Fields.DYNAMIC_ID_TYPE, AlipayConstants.SCENE_BAR_CODE);
        builder.set(BusinessV1Fields.DYNAMIC_ID, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        builder.setExtend(BusinessV1Fields.EX_AGENT_ID, BeanUtil.getPropString(config, TransactionParam.AGENT_ID));
        carryOverExtendedParams(extended, builder);
        Map<String, String> req;
        try {
            req = builder.build();
        } catch (BuilderException e) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY),
            					 BeanUtil.getPropString(config, TransactionParam.PARTNER),
                                 BeanUtil.getPropString(config, TransactionParam.APP_KEY),
                                 req);
            setTransactionContextErrorInfo(result, context, OP_PAY);
            if (context.getApiVer() == 1) {
            	transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call alipayV1 pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in alipayV1 pay", ex);
            return Workflow.RC_IOEX;
        }

        String isSuccess = BeanUtil.getPropString(result, ResponseV1Fields.IS_SUCCESS);
        String resultCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_RESULT_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        if(!AlipayConstants.TRUE.equals(isSuccess)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(AlipayConstants.RESULT_CODE_V1_ORDER_SUCCESS_PAY_SUCCESS.equals(resultCode)){
            //付款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_GMT_PAYMENT)));
            resolveFunds(transaction, BeanUtil.getNestedProperty(result, ResponseV1Fields.RESPONSE_KEY_TRADE_FUND_BILL));
            return Workflow.RC_PAY_SUCCESS;
        }else if(AlipayConstants.RESULT_CODE_V1_ORDER_FAIL.equals(resultCode) || AlipayConstants.RESULT_CODE_V1_ORDER_SUCCESS_PAY_FAIL.equals(resultCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.RESULT_CODE_V1_ORDER_SUCCESS_PAY_INPROCESS.equals(resultCode) || AlipayConstants.RESULT_CODE_V1_UNKNOWN.equals(resultCode)){
            return Workflow.RC_IN_PROG;
        }else {
            return Workflow.RC_ERROR;
        }

    }



    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestV1Builder builder = getDefaultRequestV1Builder();
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_CANCEL);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        Map<String, String> req;
        try {
            req = builder.build();
        } catch (BuilderException e) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL),
            					 BeanUtil.getPropString(config, TransactionParam.PARTNER),
                                 BeanUtil.getPropString(config, TransactionParam.APP_KEY),
                                 req);
            setTransactionContextErrorInfo(result, context, OP_CANCEL);
        } catch (MpayException ex) {
            logger.error("failed to call alipayV1 cancel", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in alipayV1 cancel", ex);
            return Workflow.RC_IOEX;
        }

        String isSuccess = BeanUtil.getPropString(result, ResponseV1Fields.IS_SUCCESS);
        String resultCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_RESULT_CODE);
        if(!AlipayConstants.TRUE.equals(isSuccess)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(AlipayConstants.RESULT_CODE_V1_SUCCESS.equals(resultCode)){
            return Workflow.RC_CANCEL_SUCCESS;
        }else if(AlipayConstants.RESULT_CODE_V1_FAIL.equals(resultCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.RESULT_CODE_V1_UNKNOWN.equals(resultCode)) {
            return Workflow.RC_RETRY;
        }else{
            return Workflow.RC_SYS_ERROR;
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestV1Builder builder = getDefaultRequestV1Builder();
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_QUERY);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        Map<String, String> req;
        try {
            req = builder.build();
        } catch (BuilderException e) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY),
            					 BeanUtil.getPropString(config, TransactionParam.PARTNER),
                                 BeanUtil.getPropString(config, TransactionParam.APP_KEY),
                                 req);
            setTransactionContextErrorInfo(result, context, OP_QUERY);
        } catch (MpayException ex) {
            logger.error("failed to call alipayV1 query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in alipayV1 query", ex);
            return Workflow.RC_IOEX;
        }
        String isSuccess = BeanUtil.getPropString(result, ResponseV1Fields.IS_SUCCESS);
        String resultCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_RESULT_CODE);
        String detailErrorCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_DETAIL_ERROR_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        if(!AlipayConstants.TRUE.equals(isSuccess)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(AlipayConstants.RESULT_CODE_V1_FAIL.equals(resultCode)){
            //支付宝预下单，如果没有支付，那么查询会返回交易不存在
            if(AlipayConstants.DETAIL_ERROR_CODE_V1_TRADE_NOT_EXIST.equals(detailErrorCode)){
                return Workflow.RC_IN_PROG;
            }else{
                return Workflow.RC_ERROR;
            }
        }else if(AlipayConstants.RESULT_CODE_V1_PROCESS_EXCEPTION.equals(resultCode)){
            return Workflow.RC_SYS_ERROR;
        }else if(AlipayConstants.RESULT_CODE_V1_SUCCESS.equals(resultCode)){
            String tradeStatus = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_TRADE_STATUS);
            if(AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus)){
                //支付成功
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_SEND_PAY_DATE)));
                resolveFunds(transaction, BeanUtil.getNestedProperty(result, ResponseV1Fields.RESPONSE_KEY_TRADE_FUND_BILL));
                return  Workflow.RC_PAY_SUCCESS;
            }else if(AlipayConstants.TRADE_STATUS_WAIT_BUYER_PAY.equals(tradeStatus)){
                return Workflow.RC_IN_PROG;
            }else {
                return Workflow.RC_ERROR;
            }
        }else {
            return Workflow.RC_SYS_ERROR;
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestV1Builder builder = getDefaultRequestV1Builder();
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_REFUND);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessV1Fields.OUT_REQUEST_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        builder.set(BusinessV1Fields.REFUND_AMOUNT, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        Map<String, String> req;
        try {
            req = builder.build();
        } catch (BuilderException e) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND),
            				 	 BeanUtil.getPropString(config, TransactionParam.PARTNER),
                                 BeanUtil.getPropString(config, TransactionParam.APP_KEY),
                                 req);
            setTransactionContextErrorInfo(result, context, OP_REFUND);
            if (context.getApiVer() == 1) {
            	transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call alipayV1 refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in alipayV1 refund", ex);
            return Workflow.RC_IOEX;
        }

        String isSuccess = BeanUtil.getPropString(result, ResponseV1Fields.IS_SUCCESS);
        String resultCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_RESULT_CODE);
        if(!AlipayConstants.TRUE.equals(isSuccess)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(AlipayConstants.RESULT_CODE_V1_SUCCESS.equals(resultCode)){
            transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_BUYER_USER_ID));
            transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_BUYER_LOGON_ID));
            transaction.put(Transaction.TRADE_NO,  BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_TRADE_NO));
            transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_GMT_PAYMENT)));
            // TODO: 退款也需要解析资金渠道。但现有的文档缺乏对退款资金渠道的描述。fund_channel=ALIPAYACCOUNT ?
            // resolveRefundedFunds(transaction, BeanUtil.getNestedProperty(result, prefix + ResponseV1Fields.FUND_BILL_LIST));
            return Workflow.RC_REFUND_SUCCESS;
        }else if(AlipayConstants.RESULT_CODE_V1_FAIL.equals(resultCode)){
            return Workflow.RC_ERROR;
        }else {
            return Workflow.RC_SYS_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestV1Builder builder = getDefaultRequestV1Builder();
        String notifyUrl = getNotifyUrl(notifyHost,context);
        if (notifyUrl != null) {
            builder.set(ProtocolV1Fields.NOTIFY_URL, notifyUrl);
        }

        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_PRECREATE);
        builder.set(BusinessV1Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        builder.set(BusinessV1Fields.BODY, BeanUtil.getPropString(transaction, Transaction.BODY));
        builder.set(BusinessV1Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessV1Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_QR_CODE_OFFLINE);
        builder.set(BusinessV1Fields.TOTAL_FEE, StringUtils.cents2yuan(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)));
        builder.set(BusinessV1Fields.IT_B_PAY, defaultTimeoutExpress);
        builder.setExtend(BusinessV1Fields.EX_AGENT_ID, BeanUtil.getPropString(config, TransactionParam.AGENT_ID));
        carryOverExtendedParams(extended, builder);
        Map<String, String> req;
        try {
            req = builder.build();
        } catch (BuilderException e) {
            return Workflow.RC_IOEX;
        }
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE),
            					 BeanUtil.getPropString(config, TransactionParam.PARTNER),
                                 BeanUtil.getPropString(config, TransactionParam.APP_KEY),
                                 req);
            setTransactionContextErrorInfo(result, context, OP_PRECREATE);
            if (context.getApiVer() == 1) {
            	transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call alipayV1 precreate", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in alipayV1 precreate", ex);
            return Workflow.RC_IOEX;
        }

        String isSuccess = BeanUtil.getPropString(result, ResponseV1Fields.IS_SUCCESS);
        String resultCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_RESULT_CODE);
        if(!AlipayConstants.TRUE.equals(isSuccess)){
            return Workflow.RC_PROTOCOL_ERROR;
        }
        if(AlipayConstants.RESULT_CODE_V1_SUCCESS.equals(resultCode)){
            //预下单成功
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.QRCODE, BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_QR_CODE));
            return Workflow.RC_CREATE_SUCCESS;
        }else if(AlipayConstants.RESULT_CODE_V1_FAIL.equals(resultCode)){
            return Workflow.RC_ERROR;
        }else {
            return Workflow.RC_SYS_ERROR;
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        int subPayWay = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(type == Transaction.TYPE_PAYMENT && Order.SUB_PAYWAY_BARCODE == subPayWay){
            String notifyActionType = BeanUtil.getPropString(providerNotification, ResponseV1Fields.NOTIFY_ACTION_TYPE);
            if(AlipayConstants.NOTIFY_ACTION_TYPE_PAY_BY_ACCOUNT_ACTION.equals(notifyActionType)){
                //付款成功
                return Workflow.RC_PAY_SUCCESS.equals(query(context))?Workflow.RC_PAY_SUCCESS:null;
            }
        }else if(type == Transaction.TYPE_PAYMENT && Order.SUB_PAYWAY_QRCODE == subPayWay){
            String tradeStatus = BeanUtil.getPropString(providerNotification, ResponseV1Fields.TRADE_STATUS);
            if(AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) && AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus)){
                //付款成功
                return Workflow.RC_PAY_SUCCESS.equals(query(context))?Workflow.RC_PAY_SUCCESS:null;
            }
        }
        return null;
    }


    @Override
    public Map<String,Object> getTradeParams(Map<String, Object> transaction){
        return getTradeParams(transaction, TransactionParam.ALIPAY_V1_TRADE_PARAMS);
    }



    private void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key){
        String isSuccess = BeanUtil.getPropString(result, ResponseV1Fields.IS_SUCCESS);
        String error = BeanUtil.getPropString(result, ResponseV1Fields.ERROR);
        String resultCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_RESULT_CODE);
        String detailErrorCode = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_DETAIL_ERROR_CODE);
        String detailErrorDes = BeanUtil.getPropString(result, ResponseV1Fields.RESPONSE_KEY_DETAIL_ERROR_DES);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseV1Fields.IS_SUCCESS, isSuccess);
        map.put(ResponseV1Fields.ERROR, error);
        map.put(ResponseV1Fields.RESULT_CODE, resultCode);
        map.put(ResponseV1Fields.DETAIL_ERROR_CODE, detailErrorCode);
        map.put(ResponseV1Fields.DETAIL_ERROR_DES, detailErrorDes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, AlipayConstants.RESULT_CODE_V1_SUCCESS.equals(resultCode), detailErrorCode, detailErrorDes);
    }

    protected void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerUid = MapUtil.getString(result, ResponseV1Fields.RESPONSE_KEY_BUYER_USER_ID);
            if(!StringUtils.isEmpty(buyerUid)){
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }

        if(StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))){
            String buyerLogonId = MapUtil.getString(result, ResponseV1Fields.RESPONSE_KEY_BUYER_LOGON_ID);
            if(!StringUtils.isEmpty(buyerLogonId)){
                transaction.put(Transaction.BUYER_LOGIN, buyerLogonId);
            }
        }

        if(StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))){
            String tradeNo = MapUtil.getString(result, ResponseV1Fields.RESPONSE_KEY_TRADE_NO);
            if(!StringUtils.isEmpty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    private void resolveFunds(Map<String, Object> transaction, Object tradeFundBill) {
        List<Map<String, Object>> tradeFundBills = new ArrayList<Map<String,Object>>();
        if (tradeFundBill instanceof List) {
            tradeFundBills.addAll((List<Map<String, Object>>)tradeFundBill);
        }else if (tradeFundBill instanceof Map){
            tradeFundBills.add((Map<String,Object>)tradeFundBill);
        }

        long paid = 0;
        long received = 0;
        for (Map<String, Object> bill: tradeFundBills ) {
            String fundChannel = BeanUtil.getPropString(bill, ResponseV1Fields.FUND_CHANNEL);
            if (consumerFunds.contains(fundChannel)) {
                paid += StringUtils.yuan2cents(BeanUtil.getPropString(bill, ResponseV1Fields.AMOUNT));
            }
            if (! merchantFunds.contains(fundChannel)) {
                received += StringUtils.yuan2cents(BeanUtil.getPropString(bill, ResponseV1Fields.AMOUNT));
            }
        }

        transaction.put(Transaction.PAID_AMOUNT, paid);
        transaction.put(Transaction.RECEIVED_AMOUNT, received);
    }


    public  RequestV1Builder getDefaultRequestV1Builder () {
        RequestV1Builder builder = new RequestV1Builder();
        builder.set(ProtocolV1Fields.PARTNER, AlipayV1Config.SQB_PARTNERID);
        builder.set(ProtocolV1Fields.INPUT_CHARSET, "gbk");
        return builder;
    }

    public void resolveNofityPayFunds(Map<String, Object> transaction, Map<String, Object> payToolsPayAmount){

    }

    public void setConsumerFunds(Set<String> consumerFunds) {
        this.consumerFunds = consumerFunds;
    }

    public void setAlipayFunds(Set<String> alipayFunds) {
        this.alipayFunds = alipayFunds;
    }

    public void setMerchantFunds(Set<String> merchantFunds) {
        this.merchantFunds = merchantFunds;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }


    private void carryOverExtendedParams(Map<String, Object> extended, RequestV1Builder builder) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if(overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.set(key, value.toString());
            }
        }
    }

    @Override
    protected int getNotifyUrlLimit(){
        return NOTIFY_URL_LIMIT;
    }
}
