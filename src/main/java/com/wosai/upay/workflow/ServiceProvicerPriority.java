package com.wosai.upay.workflow;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ServiceProvicerPriority {
    /**
     * 优先级，值越小越靠前，排序方法查看WorkflowManager.addServiceProvider()
     * @return
     */
    int priority() default Integer.MAX_VALUE;
}
