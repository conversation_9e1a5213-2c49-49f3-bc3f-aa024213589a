package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.wecard.*;
import com.wosai.mpay.api.wecard.enums.WecardPayRespCodeEnum;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.aspectj.weaver.ast.Or;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.wosai.upay.core.model.TransactionParam.*;
import static com.wosai.upay.util.ProviderUtil.*;

public class WecardServiceProvider extends AbstractServiceProvider {

    protected long b2cTimeExpire = B2C_TIME_EXPIRE_MINUTE * 60 * 1000;

    protected String notifyHost;

    public static final String NAME = "provider.wecard";

    @Autowired
    private WecardPayClient client;

    public WecardServiceProvider() {
        dateFormat = new SafeSimpleDateFormat(WecardPayConstants.DATE_FORMAT);
    }

    /**
     * 设置通知主机
     *
     * @param notifyHost 通知主机
     */
    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_WECARD;
    }


    private static final Map<String, String> PAY_WAY_CHANNEL_CONFIG = new HashMap<String, String>() {{
        put(Order.PAYWAY_ALIPAY + "", WecardPayConstants.CHANNEL_ALIPAY);
        put(Order.PAYWAY_ALIPAY2 + "", WecardPayConstants.CHANNEL_ALIPAY);
        put(Order.PAYWAY_WEIXIN + "", WecardPayConstants.CHANNEL_WECHAT);
        put(Order.PAYWAY_UNIONPAY + "", WecardPayConstants.CHANNEL_UNIONPAY);
    }};

    private static final Map<String, String> SUB_PAY_WAY_TYPE_CONFIG = new HashMap<String, String>() {{
        put(Order.SUB_PAYWAY_BARCODE + "", WecardPayConstants.PAY_TYPE_SWIPE);
        put(Order.SUB_PAYWAY_WAP + "", WecardPayConstants.PAY_TYPE_PUBLIC);
        put(Order.SUB_PAYWAY_MINI + "", WecardPayConstants.PAY_TYPE_MINI_PROGRAM);
    }};

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.WECARD_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        //构建下单参数
        Map<String, Object> request = new HashMap<>();
        request.put(WecardPayRequestFields.CHANNEL_SUB_MERCHANT_ID, MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID));
        request.put(WecardPayRequestFields.TOTAL_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        request.put(WecardPayRequestFields.PAY_TYPE, WecardPayConstants.PAY_TYPE_SWIPE);
        request.put(WecardPayRequestFields.PAY_CHANNEL, PAY_WAY_CHANNEL_CONFIG.get(payway + ""));
        request.put(WecardPayRequestFields.OUT_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(WecardPayRequestFields.ORDER_SUBJECT, MapUtil.getString(transaction, Transaction.SUBJECT));
        //1分钟过期时间
        request.put(WecardPayRequestFields.EXPIRE_TIME, formatTimeString(System.currentTimeMillis() + b2cTimeExpire));
        //ExternalPaymentData支付渠道扩展字段，第三方通道的扩展信息，根据具体对接的渠道情况传值;
        //该字段为 json 序列化之后的字符串，参考右侧请求示例；
        //如：微信、支付宝下单时需要将用户的openid或userid，及应用id传入
        Map<String, String> externalPaymentData = MapUtil.hashMap(WecardPayRequestFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        String externalPaymentDataStr = "";
        try {
            externalPaymentDataStr=JsonUtil.objectToJsonString(externalPaymentData);
        } catch (MpayException e) {
            logger.error("wecard pay externalPaymentData to json  error", e);
        }
        request.put(WecardPayRequestFields.EXTERNAL_PAYMENT_DATA,externalPaymentDataStr);
        Map<String, Object> result;
        try {
            result = client.call(
                    gatewayUrl,
                    MapUtil.getString(tradeParams, TransactionParam.WECARD_OCODE),
                    MapUtil.getString(tradeParams, TransactionParam.WECARD_SECRET_ID),
                    getPrivateKeyContent(MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY)),
                    request
            );
        } catch (Exception ex) {
            logger.error("failed to call wecard pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        Map resultInfo = MapUtils.getMap(result, WecardPayResponseFields.RESULT);
        updateTransactionCommonInfo(context, resultInfo);
        String errCode = MapUtil.getString(result, WecardPayResponseFields.ERR_CODE);
        if (!WecardPayRespCodeEnum.isResponseSuccess(errCode)) {
            return Workflow.RC_ERROR;
        }
        String orderStatus = MapUtil.getString(resultInfo, WecardPayResponseFields.ORDER_STATUS);

        if (WecardPayStatusUtils.isPaymentCompleted(orderStatus)) {
            if (WecardPayStatusUtils.isPaymentSuccess(orderStatus)) {
                // success
                updateTransactionPaymentInfo(transaction, resultInfo);
                String payChlOrderId = MapUtil.getString(resultInfo, WecardPayResponseFields.PAY_CHL_ORDER_ID);
                //同步没返回支付源订单号当作处理中依赖查询
                if (StringUtils.isEmpty(payChlOrderId)) {
                    return Workflow.RC_IN_PROG;
                }
                return Workflow.RC_PAY_SUCCESS;
            } else {
                // fail
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        //支付中
        if (WecardPayStatusUtils.isPaymentProcessing(orderStatus)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> result = doQuery(context, false);
        if (result == null) {
            return Workflow.RC_IOEX;
        }

        Map resultInfo = MapUtils.getMap(result, WecardPayResponseFields.RESULT);
        updateTransactionCommonInfo(context, resultInfo);
        String errCode = MapUtil.getString(result, WecardPayResponseFields.ERR_CODE);
        if (!WecardPayRespCodeEnum.isResponseSuccess(errCode)) {
            return Workflow.RC_IN_PROG;
        }
        String orderStatus = MapUtil.getString(resultInfo, WecardPayResponseFields.ORDER_STATUS);

        if (WecardPayStatusUtils.isPaymentCompleted(orderStatus)) {
            if (WecardPayStatusUtils.isPaymentSuccess(orderStatus)) {
                // success
                updateTransactionPaymentInfo(transaction, resultInfo);
                return Workflow.RC_PAY_SUCCESS;
            } else {
                // fail (包含部分退款和全额退款)
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        //支付中
        if (WecardPayStatusUtils.isPaymentProcessing(orderStatus)) {
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        } else {
            return doRefund(context);
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> request = new HashMap();
        request.put(WecardPayRequestFields.CHANNEL_SUB_MERCHANT_ID, MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID));
        request.put(WecardPayRequestFields.TOTAL_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        request.put(WecardPayRequestFields.PAY_TYPE, SUB_PAY_WAY_TYPE_CONFIG.get(subPayway + ""));
        request.put(WecardPayRequestFields.PAY_CHANNEL, PAY_WAY_CHANNEL_CONFIG.get(payway + ""));
        request.put(WecardPayRequestFields.OUT_ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(WecardPayRequestFields.ORDER_SUBJECT, MapUtil.getString(transaction, Transaction.SUBJECT));
        String notifyUrl = getNotifyUrl(notifyHost, gatewayUrl, context);
        if (!StringUtils.isEmpty(notifyUrl)) {
            request.put(WecardPayRequestFields.NOTIFY_URL, notifyUrl);
        }
        //4分钟过期
        request.put(WecardPayRequestFields.EXPIRE_TIME, formatTimeString(System.currentTimeMillis() + DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000));
        Map<String, Object> externalPaymentData = new HashMap<>();
        if (payway == Order.PAYWAY_WEIXIN) {
            String wechatSubAppId = MapUtils.getString(extendedParams, ProtocolFields.SUB_APP_ID);
            if (StringUtils.isEmpty(wechatSubAppId)) {
                //未上送subAppId, 则使用默认值
                if (Order.SUB_PAYWAY_MINI == subPayway) {
                    String wechatMinitSubAppId = MapUtils.getString(tradeParams, WECARD_WX_MINI_SUB_APP_ID);
                    wechatSubAppId = StringUtils.isEmpty(wechatMinitSubAppId) ? MapUtils.getString(tradeParams, WECARD_WX_SUB_APPID) : wechatMinitSubAppId;
                } else {
                    wechatSubAppId = MapUtils.getString(tradeParams, WECARD_WX_SUB_APPID);
                }
                logger.info("微信预下单未上送subAppId, 使用默认值, defaultSubAppId={}, subPayway={}", wechatSubAppId, subPayway);
            }
            externalPaymentData.put(WecardPayRequestFields.WX_APP_ID, wechatSubAppId);
            externalPaymentData.put(WecardPayRequestFields.WX_OPEN_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        }
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            externalPaymentData.put(WecardPayRequestFields.ALI_APP_ID, MapUtil.getString(tradeParams, TransactionParam.WECARD_ALI_APPID));
            externalPaymentData.put(WecardPayRequestFields.ALI_USER_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        }
        String externalPaymentDataStr = "";
        try {
            externalPaymentDataStr=JsonUtil.objectToJsonString(externalPaymentData);
        } catch (MpayException e) {
            logger.error("wecard pay externalPaymentData to json  error", e);
        }
        request.put(WecardPayRequestFields.EXTERNAL_PAYMENT_DATA, externalPaymentDataStr);

        Map<String, Object> result;
        try {
            result = client.call(
                    gatewayUrl,
                    MapUtil.getString(tradeParams, TransactionParam.WECARD_OCODE),
                    MapUtil.getString(tradeParams, TransactionParam.WECARD_SECRET_ID),
                    getPrivateKeyContent(MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY)),
                    request
            );
        } catch (Exception ex) {
            logger.error("failed to call wecard pay", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        Map resultInfo = MapUtils.getMap(result, WecardPayResponseFields.RESULT);
        updateTransactionCommonInfo(context, resultInfo);
        String errCode = MapUtil.getString(result, WecardPayResponseFields.ERR_CODE);
        if (!WecardPayRespCodeEnum.isResponseSuccess(errCode)) {
            return Workflow.RC_ERROR;
        }
        String channelPayInfo = MapUtils.getString(resultInfo, WecardPayResponseFields.PAY_INFO);
        if (StringUtils.isEmpty(channelPayInfo)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        try {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            Map<String, Object> wapRequest = new HashMap<>();
            if (payway == Order.PAYWAY_WEIXIN) {
                // json
                wapRequest.putAll(JsonUtil.jsonStringToObject(channelPayInfo, Map.class));
            } else if (payway == Order.PAYWAY_ALIPAY2) {
                // string
                wapRequest.put(WAP_PAY_REQUEST_ALIPAY_TRADE_NO, channelPayInfo);
            } else if (payway == Order.PAYWAY_UNIONPAY) {
                //redict url
                wapRequest.put(WAP_PAY_REQUEST_ALIPAY_UNION_PAY_QRCODE_REDIRECT_URL, channelPayInfo);
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        } catch (Exception e) {
            logger.error("process precreate request error: " + e.getMessage(), e);
            return Workflow.RC_TRADE_CANCELED;
        }
    }

    @Override
    public String explainNotification(Map<String, Object> notification) {
        logger.info("处理腾讯微卡回调通知");
        TransactionContext context = (TransactionContext) notification.get(TransactionContext.class.getName());
        notification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT != type) {
            return null;
        }
        //默认直接再查询一遍
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, true);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String errCode = MapUtil.getString(result, WecardPayResponseFields.ERR_CODE);
        if (!WecardPayRespCodeEnum.isResponseSuccess(errCode)) {
            return Workflow.RC_SYS_ERROR;
        }

        Map resultInfo = MapUtils.getMap(result, WecardPayResponseFields.RESULT);
        String refundStatus = MapUtil.getString(resultInfo, WecardPayResponseFields.REFUND_STATUS);
        if (WecardPayStatusUtils.isRefundSuccess(refundStatus)) {
            // success
            updateTransactionRefundInfo(context, resultInfo);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (WecardPayStatusUtils.isRefundFailed(refundStatus)) {
            // fail
            return Workflow.RC_SYS_ERROR;
        }
        //退款中
        if (WecardPayStatusUtils.isRefundProcessing(refundStatus)) {
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> request = new HashMap<>();
        request.put(WecardPayRequestFields.OUT_ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        request.put(WecardPayRequestFields.OUT_REFUND_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(WecardPayRequestFields.REFUND_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            result = client.call(gatewayUrl,
                    MapUtil.getString(config, TransactionParam.WECARD_OCODE),
                    MapUtil.getString(config, TransactionParam.WECARD_SECRET_ID),
                    getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)),
                    request);
        } catch (Exception ex) {
            logger.error("failed to call wecard refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        } finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String errCode = MapUtil.getString(result, WecardPayResponseFields.ERR_CODE);
        if (!WecardPayRespCodeEnum.isResponseSuccess(errCode)) {
            return Workflow.RC_SYS_ERROR;
        }
        Map resultInfo = MapUtils.getMap(result, WecardPayResponseFields.RESULT);
        String refundStatus = MapUtil.getString(resultInfo, WecardPayResponseFields.REFUND_STATUS);
        if (WecardPayStatusUtils.isRefundSuccess(refundStatus)) {
            // success
            updateTransactionRefundInfo(context, result);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (WecardPayStatusUtils.isRefundFailed(refundStatus)) {
            // fail or close
            return Workflow.RC_SYS_ERROR;
        }
        return Workflow.RC_RETRY;
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }


    // doQuery
    protected Map<String, Object> doQuery(TransactionContext context, boolean isRefundQuery) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> request = new HashMap<>();
        request.put(WecardPayRequestFields.OUT_ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        if (isRefundQuery) {
            request.put(WecardPayRequestFields.OUT_REFUND_ID, MapUtil.getString(transaction, Transaction.TSN));
        }
        String queryFlag = isRefundQuery ? OP_REFUND_QUERY : OP_QUERY;
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), queryFlag);
        Map<String, Object> result = null;
        try {
            result = client.call(gatewayUrl,
                    MapUtil.getString(config, TransactionParam.WECARD_OCODE),
                    MapUtil.getString(config, TransactionParam.WECARD_SECRET_ID),
                    getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)),
                    request);
        } catch (Exception ex) {
            logger.error("failed to call wecard query", ex);
            setTransactionContextErrorInfo(context, queryFlag, ex);
        }
        setTransactionContextErrorInfo(result, context, queryFlag);
        return result;

    }


    /**
     * 更新退款信息
     *
     * @param context
     * @param result
     */
    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> result) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
        updateMapIfResponseNotNull(context.getTransaction(), Transaction.CHANNEL_FINISH_TIME, result, WecardPayResponseFields.FINISH_TIME, object -> parseTimeString((String) object));
        updateMapIfResponseNotNull(context.getTransaction(), Transaction.TRADE_NO, result, WecardPayResponseFields.CHANNEL_REFUND_ID);
    }


    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String errCode = MapUtil.getString(result, WecardPayResponseFields.ERR_CODE);
        String errMsg = MapUtil.getString(result, WecardPayResponseFields.ERR_MESSAGE);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(WecardPayResponseFields.ERR_CODE, errCode);
        map.put(WecardPayResponseFields.ERR_MESSAGE, errMsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, WecardPayRespCodeEnum.isResponseSuccess(errCode), errCode, errMsg);
    }

    /**
     * 更新通用字段
     *
     * @param context
     * @param result
     */
    protected void updateTransactionCommonInfo(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        // 解析腾讯云商付平台订单号
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, result, WecardPayResponseFields.CHANNEL_ORDER_ID);
        updateMapIfResponseNotNull(order, Order.TRADE_NO, result, WecardPayResponseFields.CHANNEL_ORDER_ID);
        // 收单方订单号
        updateMapIfResponseNotNull(extraOutFields, Transaction.TRADE_NO, result, WecardPayResponseFields.PAY_CHL_ORDER_ID);
    }

    /**
     * 更新付款信息
     *
     * @param transaction
     * @param result
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> result) {
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, result, WecardPayResponseFields.FINISH_TIME, object -> parseTimeString((String) object));
        updateMapIfResponseNotNull(transaction, Transaction.PAID_AMOUNT, result, WecardPayResponseFields.PAY_AMOUNT);
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID, result, WecardPayResponseFields.PAY_CHL_PAYER_ID);
        List<Map<String,Object>> payments = new ArrayList<>();
        // 计算支付明细
        Long effectiveAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        //获取用户实付金额
        Long paidAmount = MapUtil.getLong(transaction, Transaction.PAID_AMOUNT, effectiveAmount);
        long couponSum = effectiveAmount - paidAmount;
        if(couponSum > 0){
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_AMOUNT, couponSum
                    )
            );
        }
       String paymentType = getDefaultPaymentType(MapUtil.getString(transaction, Transaction.PAYWAY));
        if(paidAmount > 0){
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, paymentType,
                            Transaction.PAYMENT_ORIGIN_TYPE, MapUtils.getString(result,WecardPayResponseFields.PAY_CHANNEL),
                            Transaction.PAYMENT_AMOUNT, paidAmount
                    )
            );
        }
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }
}
