package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.Action;
import com.wosai.fsm.MachineBuilder;
import com.wosai.fsm.MachineContext;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class DepositCancelOrderWorkflow extends Workflow {
    private static final Logger logger = LoggerFactory.getLogger(DepositCancelOrderWorkflow.class);
    private static final long[] delays = {1000, 2000, 3000, 3000, 5000, 5000, 10000};
    public static final String NAME = "generic.deposit.cancel.workflow";

    public DepositCancelOrderWorkflow() {
        this(delays);
    }

    public DepositCancelOrderWorkflow(long[] delays) {
        MachineBuilder builder = new MachineBuilder();
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return depositCancelOrder((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, SUCCESS)
          .transition(RC_ERROR, CANCEL_ERROR)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_IOEX,  FAIL_IO_1)

        .on(SUCCESS).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return finish((TransactionContext)context);
            }
        }).end()

        .on(IN_PROG).delay(delays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return depositCancelOrder((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, SUCCESS)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_IOEX, FAIL_IO_2)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_ERROR, CANCEL_ERROR)
          .transition(RC_EXPIRE, CANCEL_ERROR)

        .on(CANCEL_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()
        
        .on(FAIL_IO_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end();
        
        machine = builder.build();
    }

    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CANCEL
                && MapUtil.getIntValue(transaction, Transaction.PAYWAY) != Order.PAYWAY_BANKCARD) {
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        return context.getServiceProvider().explainNotification(notification);
    }

    public String depositCancelOrder(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_DEPOSIT_CANCEL), () -> {
            String result = depositProvider.depositCancel(context);
            logger.debug("TID {} deposit cancel method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @SuppressWarnings("unchecked")
    @Override
    public String finish(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        order.put(Order.NET_ORIGINAL, 0L);
        order.put(Order.NET_EFFECTIVE, 0L);
        order.put(Order.NET_DISCOUNT, 0L);
        order.put(Order.STATUS, Order.STATUS_DEPOSIT_CANCELED);
        transaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
        PaymentUtil.updateOrderPaymentsNetAmountForCancelSuccess((List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH));
        PaymentUtil.updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(order, transaction);
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.RECEIVED_AMOUNT, transaction.get(Transaction.RECEIVED_AMOUNT),
                                                                        Transaction.PAID_AMOUNT, transaction.get(Transaction.PAID_AMOUNT),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.NET_ORIGINAL, order.get(Order.NET_ORIGINAL),
                                                                  Order.NET_EFFECTIVE, order.get(Order.NET_EFFECTIVE),
                                                                  Order.NET_DISCOUNT, order.get(Order.NET_DISCOUNT),
                                                                  Order.ITEMS, order.get(Order.ITEMS),
                                                                  Order.STATUS, order.get(Order.STATUS));

        if(BeanUtil.getPropBoolean(context.getTransaction(), Transaction.KEY_IS_HISTORY_DEPOSIT_CANCEL, false)){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
            orderUpdates = null;
        }

        finishTransaction(context, orderUpdates, transactionUpdates);

        if(context.isFakeRequest()){
            logger.debug("TID {} deposit cancel finished", context.getTid());
            return null;
        }
        logger.debug("TID {} deposit cancel finished", context.getTid());
        return null;
    }
    @SuppressWarnings("unchecked")
    public String closeOrder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        order.put(Order.STATUS, Order.STATUS_DEPOSIT_CANCEL_ERROR);
        // RC_EXPIRE 不会变更流水状态，需要做下状态变更
        if (MapUtil.getIntValue(context.getTransaction(), Transaction.STATUS) == Transaction.STATUS_IN_PROG) {
            context.getTransaction().put(Transaction.STATUS, Transaction.STATUS_CANCEL_ERROR);
        }
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.STATUS, order.get(Order.STATUS));

        repository.getTransactionDao().updatePart(transactionUpdates);
        if(BeanUtil.getPropBoolean(context.getTransaction(), Transaction.KEY_IS_HISTORY_DEPOSIT_CANCEL, false)){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
        }else {
            repository.getOrderDao().updatePart(orderUpdates);
        }
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        logger.debug("TID {} closed", context.getTid());
        return null;
    }
}
