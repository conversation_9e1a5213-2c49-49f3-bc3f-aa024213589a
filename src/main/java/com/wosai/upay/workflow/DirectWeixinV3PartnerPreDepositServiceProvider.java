package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.util.Digest;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/***
 * @ClassName: DirectWeixinV3PartnerPreDepositServiceProvider
 * @Description: 微信支付分免确认模式
 * @Auther: dabuff
 * @Date: 2022/1/11 4:42 PM
 */
public class DirectWeixinV3PartnerPreDepositServiceProvider extends DirectWeixinV3PreDepositServiceProvider{
    public static final String NAME = "provider.weixin.wapOrMini.v3.partner";

    @Override
    public String getName() {
        return NAME;
    }

    public DirectWeixinV3PartnerPreDepositServiceProvider(){
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_AMOUNT, BusinessFields.AUTHORIZATION_CODE));
    }

    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());

        return getBasicRequestBuilder(config);
    }

    public RequestBuilder getBasicRequestBuilder(Map<String, Object> config){
        //部分接口不需要上送app_id,故重写 getDefaultRequestBuilder 方法
        RequestBuilder builder = new RequestBuilder();
        //服务ID
        builder.set(BusinessFields.SERVICE_ID, config.get(TransactionParam.WEIXIN_SERVICE_ID));
        //子商户号
        builder.set(ProtocolFields.SUB_MCHID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));

        return builder;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if(!Transaction.TYPE_DEPOSIT_SET.contains(type)){
            return false;
        }

        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (subPayway != Order.SUB_PAYWAY_MINI && subPayway != Order.SUB_PAYWAY_APP && subPayway != Order.SUB_PAYWAY_H5) {
            return false;
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);
        //区分商户支付分和服务商支付分，默认使用商户支付分
        boolean matchProductCode = matchProductCode(transaction, tradeParams,TransactionParam.SQB_PRODUCT_CODE_PAY_SCORE, true);
        return Objects.nonNull(tradeParams) && matchProductCode && TransactionParam.SERVICE_MODE_PARTNER == MapUtil.getIntValue(tradeParams, TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_MERCHANT);
    }

    @Override
    protected RequestBuilder buildPreFreezeRequest(RequestBuilder builder, Map<String, Object> transaction, Map<String, Object> config) {
        //用户id
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        //服务商应用ID
        builder.set(ProtocolFields.APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_APP_ID));
        //子商户应用ID
        builder.set(ProtocolFields.SUB_APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
        //子商户公众号下的用户标识
        builder.set(BusinessFields.SUB_OPEN_ID, payerUid);
        //是否需要用户确认	  默认免确认模式-false
        builder.set(BusinessFields.NEED_USER_CONFIRM, BeanUtil.getPropBoolean(config, TransactionParam.NEED_CONFIRM, false));

        return builder;
    }

    public String buildPreFreezeResult(Map<String, Object> result, TransactionContext context) {

        int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
        String state = BeanUtil.getPropString(result, ResponseFields.STATE);
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        boolean isNeedConfirm = BeanUtil.getPropBoolean(config, TransactionParam.NEED_CONFIRM, false);
        if (isNeedConfirm) {
            //需确认模式
            if (HttpConstant.HTTP_CODE_SUCCESS == httpCode && state.equals(WeixinConstants.DEPOSIT_STATE_CREATE)) {
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                Map<String, Object> wapRequest = new HashMap<>();
                wapRequest.put(WapV3Fields.PACKAGE, BeanUtil.getPropString(result, ResponseFields.PACKAGE));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                return Workflow.RC_CREATE_SUCCESS;
            }
        } else {
            //服务商免确认模式
            if (HttpConstant.HTTP_CODE_SUCCESS == httpCode && (state.equals(WeixinConstants.DEPOSIT_STATE_CREATE) || state.equals(WeixinConstants.DEPOSIT_STATE_DOING))) {
                //免确认模式 给wapRequest随便塞一个mch_id, 以防止precreateResponse校验不通过
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
                Map<String, Object> wapRequest = new HashMap<>();
                wapRequest.put(WapV3Fields.MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
                processWhenPreFreezeSuccess(context.getTransaction(), result);
                return Workflow.RC_CREATE_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }

    protected String buildConsumeResult(Map<String, Object> result, TransactionContext context){
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
        int httpCode = BeanUtil.getPropInt(result,HttpConstant.HTTP_CODE);
        if (httpCode == HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE) {
            return Workflow.RC_IN_PROG;
        }
        String code = BeanUtil.getPropString(result, ResponseFields.CODE);
        if (WeixinConstants.DEPOSIT_CODE_ORDER_DONE.equals(code) || WeixinConstants.DEPOSIT_CODE_PARAM_ERROR.equals(code)) {
            return Workflow.RC_IN_PROG;
        }
        if(WeixinConstants.DEPOSIT_CONSUMER_V3_FAIL_LIST.contains(code)) {
            return Workflow.RC_CONSUME_FAIL;
        }else if (WeixinConstants.DEPOSIT_CODE_SYSTEM_ERROR.equals(code)) {
            return Workflow.RC_SYS_ERROR;
        }else if(WeixinConstants.DEPOSIT_CODE_FREQUENCY_LIMITED.equals(code)){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }

    public Map<String, Object> depositAuthApply(Map<String, Object> config, Map<String, Object> extended) {
        RequestBuilder builder = getBasicRequestBuilder(config);
        //服务商应用ID
        builder.set(ProtocolFields.APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_APP_ID));
        //子商户应用ID
        String subAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, subAppId);
        //授权协议号
        String authorizationCode = BeanUtil.getPropString(extended, BusinessFields.AUTHORIZATION_CODE);
        if (StringUtil.isBlank(authorizationCode)) {
            String subOpenId = BeanUtil.getPropString(extended, BusinessFields.SUB_OPEN_ID);
            try {
                authorizationCode = Digest.md5((subOpenId + subAppId).getBytes(StandardCharsets.UTF_8));

            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException("generate auth code error", e);
            }
        }
        builder.set(BusinessFields.AUTHORIZATION_CODE, authorizationCode);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_AUTH_APPLY);
        url = url.substring(0, url.lastIndexOf(URL_INTERVAL)) + "/permissions";

        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_AUTH_APPLY_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), retryTimes, OP_DEPOSIT_AUTH_APPLY);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit auth apply", ex);
            return CollectionUtil.hashMap(ResponseFields.ERR_CODE_DES,ex.getMessage());
        }
        if (StringUtil.isNotBlank(authorizationCode)) {
            result.put(BusinessFields.AUTHORIZATION_CODE, authorizationCode);
        }

        return result;
    }

    public Map<String, Object> depositAuthQuery(Map<String, Object> config, Map<String, Object> extended) {
        RequestBuilder builder = getBasicRequestBuilder(config);
        String authorizationCode = BeanUtil.getPropString(extended, BusinessFields.AUTHORIZATION_CODE);
        String subOpenId = BeanUtil.getPropString(extended, BusinessFields.SUB_OPEN_ID);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_AUTH_QUERY);
        if (Objects.nonNull(authorizationCode)){
            builder.set(BusinessFields.AUTHORIZATION_CODE, authorizationCode);
            url = url + "/permissions/authorization-code/" + authorizationCode;
        } else if (Objects.nonNull(subOpenId)){
            //服务商应用ID
            builder.set(ProtocolFields.APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_APP_ID));
            //子商户应用ID
            builder.set(ProtocolFields.SUB_APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
            //子商户公众号下openid
            builder.set(BusinessFields.SUB_OPEN_ID, subOpenId);
            url = url.substring(0, url.lastIndexOf(URL_INTERVAL)) + "/permissions/search";
        }

        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_AUTH_QUERY_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), retryTimes, OP_DEPOSIT_AUTH_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit auth query", ex);
            return CollectionUtil.hashMap(ResponseFields.ERR_CODE_DES,ex.getMessage());
        }

        return result;
    }

    public Map<String, Object> depositAuthTerminate(Map<String, Object> config, Map<String, Object> extended) {
        RequestBuilder builder = getBasicRequestBuilder(config);
        String authorizationCode = BeanUtil.getPropString(extended, BusinessFields.AUTHORIZATION_CODE);
        String subOpenId = BeanUtil.getPropString(extended, BusinessFields.SUB_OPEN_ID);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_AUTH_TERMINATE);
        if (Objects.nonNull(authorizationCode)){
            //授权协议号
            builder.set(BusinessFields.AUTHORIZATION_CODE, authorizationCode);
            url = url.substring(0, url.lastIndexOf(URL_INTERVAL)) +  "/permissions/authorization-code/" + authorizationCode + "/terminate";
        } else if (Objects.nonNull(subOpenId)){
            //服务商应用ID
            builder.set(ProtocolFields.APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_APP_ID));
            //子商户应用ID
            builder.set(ProtocolFields.SUB_APP_ID, BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID));
            //子商户公众号下openid
            builder.set(BusinessFields.SUB_OPEN_ID, subOpenId);
            url = url.substring(0, url.lastIndexOf(URL_INTERVAL)) + "/permissions/terminate";
        }

        carryDepositOverExtendedParams(extended, builder, WeixinConstants.DEPOSIT_AUTH_TERMINATE_ALLOWED_FIELDS);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, url, builder.build(), retryTimes, OP_DEPOSIT_AUTH_TERMINATE);
        } catch (Exception ex) {
            logger.error("failed to call weixin deposit auth terminate", ex);
            return CollectionUtil.hashMap(ResponseFields.ERR_CODE_DES,ex.getMessage());
        }

        return result;
    }
}