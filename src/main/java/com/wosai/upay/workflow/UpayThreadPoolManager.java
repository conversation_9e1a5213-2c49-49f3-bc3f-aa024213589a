package com.wosai.upay.workflow;

import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayScheduledExecutorService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

import javax.annotation.Resource;

@Service
public class UpayThreadPoolManager implements InitializingBean {

    @Resource(name = "providerDefaultThreadPool")
    private ScheduledExecutorService executor;

    @Autowired
    private ApolloConfigurationCenterUtil apolloConfigurationCenterUtil;

    private static final String INDIRECT_PROVIDER = "indirect";
    private UpayScheduledExecutorService defaultExecutor;

    private int largeDelayTime;

    private static Map<String, UpayScheduledExecutorService> providerExecutors = null;

    @Override
    @SuppressWarnings("unchecked")
    public void afterPropertiesSet() {
        this.defaultExecutor = new UpayScheduledExecutorService("defaultExecutor", executor, largeDelayTime);
        UpayScheduledExecutorService inDirectService = new UpayScheduledExecutorService(INDIRECT_PROVIDER + "-default", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(INDIRECT_PROVIDER+":")), largeDelayTime);
        UpayScheduledExecutorService alipayService = new UpayScheduledExecutorService(Order.PAYWAY_ALIPAY+ "&" +Order.PAYWAY_ALIPAY2, Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(":"+Order.PAYWAY_ALIPAY2)), largeDelayTime);
        UpayScheduledExecutorService weixinService = new UpayScheduledExecutorService(Order.PAYWAY_WEIXIN+"", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(":"+Order.PAYWAY_WEIXIN)), largeDelayTime);
        UpayScheduledExecutorService giftCardService = new UpayScheduledExecutorService(Order.PAYWAY_GIFT_CARD+"", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(":"+Order.PAYWAY_GIFT_CARD)),largeDelayTime);
        UpayScheduledExecutorService unionpayOpenService = new UpayScheduledExecutorService(Order.PROVIDER_UNIONPAY_OPEN + "-default", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_UNIONPAY_OPEN + ":")), largeDelayTime);
        UpayScheduledExecutorService chinaumsService = new UpayScheduledExecutorService(Order.PROVIDER_CHINAUMS + "-default", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_CHINAUMS + ":")), largeDelayTime);
        UpayScheduledExecutorService unionpayOnlineService = new UpayScheduledExecutorService(Order.PROVIDER_UNIONPAY_ONLINE + "-default", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_UNIONPAY_ONLINE + ":")), largeDelayTime);

        providerExecutors = CollectionUtil.hashMap(
                // 直连
                ":"+Order.PAYWAY_ALIPAY, alipayService,
                ":"+Order.PAYWAY_ALIPAY2, alipayService,
                ":"+Order.PAYWAY_WEIXIN, weixinService,
                ":"+Order.PAYWAY_GIFT_CARD, giftCardService,

                // 拉卡拉
                Order.PROVIDER_LAKALA_UNION_PAY+":"+Order.PAYWAY_ALIPAY2,new UpayScheduledExecutorService(Order.PROVIDER_LAKALA_UNION_PAY+":"+Order.PAYWAY_ALIPAY2,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_LAKALA_UNION_PAY+":"+Order.PAYWAY_ALIPAY2)), largeDelayTime),
                Order.PROVIDER_LAKALA_UNION_PAY+":"+Order.PAYWAY_WEIXIN,new UpayScheduledExecutorService(Order.PROVIDER_LAKALA_UNION_PAY+":"+Order.PAYWAY_WEIXIN,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_LAKALA_UNION_PAY+":"+Order.PAYWAY_WEIXIN)), largeDelayTime),
                Order.PROVIDER_NUCC+":"+Order.PAYWAY_BESTPAY,new UpayScheduledExecutorService(Order.PROVIDER_NUCC + "-" + Order.PAYWAY_BESTPAY, Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_NUCC+":"+Order.PAYWAY_BESTPAY)), largeDelayTime),
                Order.PROVIDER_UNIONPAY_OPEN + ":", unionpayOpenService,

                // 银联在线
                Order.PROVIDER_UNIONPAY_ONLINE + ":", unionpayOnlineService,

                // 银商
                Order.PROVIDER_CHINAUMS+":"+Order.PAYWAY_ALIPAY2,new UpayScheduledExecutorService(Order.PROVIDER_CHINAUMS+":"+Order.PAYWAY_ALIPAY2,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_CHINAUMS+":"+Order.PAYWAY_ALIPAY2)), largeDelayTime),
                Order.PROVIDER_CHINAUMS+":"+Order.PAYWAY_WEIXIN,new UpayScheduledExecutorService(Order.PROVIDER_CHINAUMS+":"+Order.PAYWAY_WEIXIN,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_CHINAUMS+":"+Order.PAYWAY_WEIXIN)), largeDelayTime),
                Order.PROVIDER_CHINAUMS + ":", chinaumsService,

                // 通联
                Order.PROVIDER_TL_SYB +":"+Order.PAYWAY_ALIPAY2,new UpayScheduledExecutorService(Order.PROVIDER_TL_SYB +":"+Order.PAYWAY_ALIPAY2,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_TL_SYB +":"+Order.PAYWAY_ALIPAY2)), largeDelayTime),
                Order.PROVIDER_TL_SYB +":"+Order.PAYWAY_WEIXIN,new UpayScheduledExecutorService(Order.PROVIDER_TL_SYB +":"+Order.PAYWAY_WEIXIN,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_TL_SYB +":"+Order.PAYWAY_WEIXIN)), largeDelayTime),

                // 邮储
                Order.PROVIDER_PSBCBANK+":", new UpayScheduledExecutorService(Order.PROVIDER_PSBCBANK + "", Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_PSBCBANK+":")), largeDelayTime),

                // 海科
                Order.PROVIDER_HAIKE_UNION_PAY +":"+Order.PAYWAY_ALIPAY2,new UpayScheduledExecutorService(Order.PROVIDER_HAIKE_UNION_PAY +":"+Order.PAYWAY_ALIPAY2,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_HAIKE_UNION_PAY +":"+Order.PAYWAY_ALIPAY2)), largeDelayTime),
                Order.PROVIDER_HAIKE_UNION_PAY +":"+Order.PAYWAY_WEIXIN,new UpayScheduledExecutorService(Order.PROVIDER_HAIKE_UNION_PAY +":"+Order.PAYWAY_WEIXIN,Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_HAIKE_UNION_PAY +":"+Order.PAYWAY_WEIXIN)), largeDelayTime),

                // 华夏
                Order.PROVIDER_HXBANK +":",new UpayScheduledExecutorService(Order.PROVIDER_HXBANK + "",Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_HXBANK + ":")), largeDelayTime),

                // 民生银行
                Order.PROVIDER_DIRECT_CMBCBANK +":", new UpayScheduledExecutorService(Order.PROVIDER_DIRECT_CMBCBANK + "",Executors.newScheduledThreadPool(apolloConfigurationCenterUtil.getProviderThreadPoolConfig(Order.PROVIDER_DIRECT_CMBCBANK + ":")), largeDelayTime),

                // 威富通 + 间连通道（未单独配置线程池的间连通道）
                INDIRECT_PROVIDER + ":", inDirectService,

                // 默认（非单独配置线程池的直连通道）
                "default", defaultExecutor
        );
    }

    public static synchronized void changeThreadPoolSize(String key, Integer corePoolSize){
        UpayScheduledExecutorService upayScheduledExecutorService = providerExecutors.get(key);
        if (upayScheduledExecutorService != null) {
            if (corePoolSize != null && corePoolSize > 0) {
                ScheduledThreadPoolExecutor scheduledExecutorService = (ScheduledThreadPoolExecutor) upayScheduledExecutorService.getDelegate();
                scheduledExecutorService.setCorePoolSize(corePoolSize);
            }
        } else {
            if (corePoolSize != null && corePoolSize > 0) {
                // 通道不存在，生成新的通道线程池
                // 注意：该通道只是临时有效，网关服务重启后就不会存在了，如果需要一直存在，则要在afterPropertiesSet()中进行定义
                providerExecutors.put(key, new UpayScheduledExecutorService(key, Executors.newScheduledThreadPool(corePoolSize), 500));
            }
        }
    }

    public int getLargeDelayTime() {
        return largeDelayTime;
    }

    public void setLargeDelayTime(int largeDelayTime) {
        this.largeDelayTime = largeDelayTime;
    }

    public UpayScheduledExecutorService getExecutor(TransactionContext context){
        String provider = MapUtil.getString(context.getTransaction(), Transaction.PROVIDER, "");
        int payway   = MapUtil.getIntValue(context.getTransaction(),Transaction.PAYWAY);
        String fkey  = StringUtils.join(provider, ":", payway);
        if(providerExecutors.get(fkey) != null){
            return providerExecutors.get(fkey);
        }
        fkey = StringUtils.join(provider, ":");
        if(providerExecutors.get(fkey) != null){
            return providerExecutors.get(fkey);
        }
        if(StringUtil.empty(provider)){
            return this.defaultExecutor;
        }

        //TODO 新增provider后，需要新增相应代码来选择走什么线程池
        fkey = StringUtils.join(INDIRECT_PROVIDER, ":");
        if(providerExecutors.get(fkey) != null){
            return providerExecutors.get(fkey);
        }
        return this.defaultExecutor;
    }


    public Map getInfo(){
        ArrayList list = new ArrayList();
        if(providerExecutors != null){
            for(String name: providerExecutors.keySet()){
                ScheduledExecutorService executorService = providerExecutors.get(name).getDelegate();
                if(executorService instanceof ThreadPoolExecutor){
                    int activeCount = ((ThreadPoolExecutor) executorService).getActiveCount();
                    int queueSize = ((ThreadPoolExecutor) executorService).getQueue().size();
                    int corePoolSize = ((ThreadPoolExecutor) executorService).getCorePoolSize();
                    list.add(
                            CollectionUtil.hashMap(
                                    "name", name,
                                    "activeCount", activeCount,
                                    "queueSize", queueSize,
                                    "corePoolSize", corePoolSize
                            )
                    );
                }
            }
        }
        return CollectionUtil.hashMap(
                "executors", list
        );
    }

}
