package com.wosai.upay.workflow;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.alipay.AlipayApiClient;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayServices;
import com.wosai.mpay.api.alipay.ApiRequestBuilder;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

public class AlipayFitnessServiceProvider extends DirectAlipayV2WapServiceProvider{
    private static final Logger logger = LoggerFactory.getLogger(AlipayFitnessServiceProvider.class);
    public static final String NAME = "provider.alipay.fitness";

    @Autowired
    protected AlipayApiClient alipayApiClient;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        if ((Order.PAYWAY_ALIPAY == payway|| Order.PAYWAY_ALIPAY2 == payway )
                && Order.SUB_PAYWAY_MINI == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY)) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return super.getTradeParams(transaction, TransactionParam.FITNESS_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        throw new UnsupportedOperationException("该功能暂不支持");
    }

    @Override
    public String cancel(TransactionContext context) {
        String rc = refund(context);
        return Workflow.RC_REFUND_SUCCESS.equals(rc) ? Workflow.RC_CANCEL_SUCCESS : rc;
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        String tradeNo = MapUtil.getString(transaction, Transaction.TRADE_NO);
        ApiRequestBuilder requestBuilder = getDefaultBuilder(context);
        requestBuilder.set(ProtocolV2Fields.SERVICE, AlipayServices.API_FITNESS_ORDER_QUERY);
        requestBuilder.bizSet(BusinessV2Fields.ORDER_NO, tradeNo);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> response = null;
        try {
            response = alipayApiClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), AlipayConstants.SIGN_TYPE_RSA2, getPrivateKeyContent(MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY)), requestBuilder.build());
        } catch (Exception e) {
            logger.error("failed to call alipayV2 query", e);
            return Workflow.RC_IOEX;
        }
        Object data = MapUtil.getObject(response, BusinessV2Fields.DATA);
        if (data == null || !(data instanceof List)) {
            return Workflow.RC_ERROR;
        }else {
            List<Map<String,Object>> results = (List<Map<String, Object>>) data;
            //如果通过订单编号查询只返回一笔
            String rc = Workflow.RC_ERROR;
            if(results.isEmpty()){
                return rc;
            }
            Map<String, Object> result = results.get(0);
            String orderStatus = MapUtil.getString(result, BusinessV2Fields.FITNESS_ORDER_STATUS, "");
            if (AlipayConstants.ORDER_STATUS_PAID.equals(orderStatus)) {
                rc = Workflow.RC_PAY_SUCCESS;
                // 设置外部订购单号
                String subscriptionNo = MapUtil.getString(result, BusinessV2Fields.SUBSCRIPTION_NO);
                Map<String, Object> extraOutFields = (Map<String, Object>) transaction.getOrDefault(Transaction.EXTRA_OUT_FIELDS, new HashMap<>());
                extraOutFields.put(Transaction.SUBSCRIPTION_NO, subscriptionNo);
                transaction.put(Transaction.BUYER_UID, MapUtil.getString(result, BusinessV2Fields.USER_ID));
                context.getOrder().put(Order.BUYER_UID, MapUtil.getString(result, BusinessV2Fields.USER_ID));
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtil.getString(result, "actualDeductionTime")));
            }
            return rc;
        }
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String merchantPid = MapUtil.getString(tradeParams, TransactionParam.FITNESS_PARAMS_ALIPAY_MERCHANT_PID);
        String tradeNo = MapUtil.getString(order, Transaction.TRADE_NO);
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        String subscriptionNo = MapUtil.getString(extraOutFields, Transaction.SUBSCRIPTION_NO);

        ApiRequestBuilder requestBuilder = getDefaultBuilder(context);
        requestBuilder.bizSet(BusinessV2Fields.SUBSCRIPTION_NO, subscriptionNo);
        requestBuilder.bizSet(BusinessV2Fields.MERCHANT_PID, merchantPid);
        requestBuilder.bizSet(BusinessV2Fields.ORDER_NO, tradeNo);
        requestBuilder.set(ProtocolV2Fields.SERVICE, AlipayServices.API_FITNESS_ORDER_REFUND);
        Map<String, Object> response = null;
        try {
            response = alipayApiClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), AlipayConstants.SIGN_TYPE_RSA2, getPrivateKeyContent(MapUtil.getString(tradeParams, TransactionParam.FITNESS_PARAMS_PRIVATE_KEY)), requestBuilder.build());
        } catch (Exception e) {
            logger.error("failed to call alipayV2 refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(response, context, OP_REFUND);
        String code = MapUtil.getString(response, BusinessV2Fields.CODE);
        if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(code)) {
            transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    public ApiRequestBuilder getDefaultBuilder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        ApiRequestBuilder builder = new ApiRequestBuilder();
        builder.set(ProtocolV2Fields.CHARSET,AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.ISV_APP_ID, BeanUtil.getPropString(config,TransactionParam.FITNESS_PARAMS_ISV_APP_ID));
        builder.set(ProtocolV2Fields.UTC_TIMESTAMP, System.currentTimeMillis() + "");
        builder.set(ProtocolV2Fields.VERSION, AlipayConstants.VERSION_ONE);
        return builder;
    }


}
