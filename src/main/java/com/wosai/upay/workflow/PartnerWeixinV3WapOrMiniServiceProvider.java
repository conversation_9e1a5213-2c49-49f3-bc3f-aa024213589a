package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.api.weixin.WapV3Fields;
import com.wosai.mpay.api.weixin.WeixinV3Client;
import com.wosai.mpay.api.weixin.v3.BusinessFields;
import com.wosai.mpay.api.weixin.v3.ProtocolFields;
import com.wosai.mpay.api.weixin.v3.ResponseFields;
import com.wosai.mpay.api.weixin.v3.WeixinConstants;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 使用服务商模式对接
 */
//需要比DirectWeixinServiceProvider优先级低
@ServiceProvicerPriority(priority = 0)
public class PartnerWeixinV3WapOrMiniServiceProvider extends WeixinV3ServiceProvider {

    public static final String NAME = "provider.weixin.wapOrMini.v3.partner.transactions";

    protected String notifyHost;

    public PartnerWeixinV3WapOrMiniServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(WeixinConstants.DATE_TIME_FORMAT);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return super.canHandle(transaction) && (MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == SubPayway.MINI.getCode() || MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == SubPayway.WAP.getCode());
    }


    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("下单暂不支持");
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = new RequestBuilder();
        setAppIdAndMchId(builder, config, extended);

        builder.set(BusinessFields.DESCRIPTION, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.NOTIFY_URL, getNotifyUrl(notifyHost, context));
        long timeEnd = System.currentTimeMillis() + DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
        builder.set(BusinessFields.TIME_EXPIRE, formatTimeString(timeEnd));
        builder.set(BusinessFields.AMOUNT, MapUtil.hashMap(BusinessFields.AMOUNT_TOTAL, transaction.get(Transaction.EFFECTIVE_AMOUNT), BusinessFields.AMOUNT_CURRENCY, TransactionParam.UPAY_DEFAULT_CURRENCY_CNY));
        builder.set(BusinessFields.GOODS_TAG, config.get(TransactionParam.GOODS_TAG));
        //设置结算相关信息 ：分账
        setSettleInfo(transaction, builder);
        //设置支付方信息
        setPayer(builder, transaction);
        //设置支付限制（非信用卡支付 未成年人支付等信息 但是目前接口信息不全
//        handlerCustomizedSwitch(builder, config);
        //优惠信息传入
        carryOverExtendedParams((Map) transaction.get(Transaction.EXTENDED_PARAMS), builder, WeixinConstants.PRECREATE_ALLOWED_FIELDS);
        //订单优惠标记
        setGoodsTag(builder, context);
        //设置场景信息
        setSceneInfo(builder, transaction, extended);
        Map<String, Object> result;
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);

        try {
            result = retryIfNetworkException(url, WeixinV3Client.METHOD_POST, config, builder.build(), retryTimes, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call weixin precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String code = (String) result.get(ResponseFields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if (com.wosai.pantheon.util.StringUtil.isNotEmpty(code)) {
            return Workflow.RC_ERROR;
        }
        //预下单成功
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        Map<String, Object> wapRequest = new HashMap<String, Object>();
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        String subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_SUB_APP_ID);
        String miniSubAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        if (Order.SUB_PAYWAY_MINI == subPayway && !StringUtil.empty(miniSubAppid)){
            subAppid = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        }
        if(subPayway == Order.SUB_PAYWAY_WAP) {
            subAppid = com.wosai.pantheon.util.MapUtil.getString(config, TransactionParam.WEIXIN_APP_ID);
        }
        // 优先使用上送的sub_app_id
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, com.wosai.mpay.api.weixin.ProtocolFields.SUB_APP_ID);
        if(!StringUtil.empty(sendSubAppId)) {
            subAppid = sendSubAppId;
        }

        wapRequest.put(WapFields.APP_ID, subAppid);
        wapRequest.put(WapFields.TIME_STAMP, System.currentTimeMillis() / 1000 + "");
        wapRequest.put(WapFields.NONCE_STR, getNonceStr());
        wapRequest.put(WapFields.SIGN_TYPE, RsaSignature.KEY_RSA);
        wapRequest.put(WapFields.PACKAGE, buildPackage(result, context));
        String signParams = wapRequest.get(WapFields.APP_ID) + "\n" + wapRequest.get(WapFields.TIME_STAMP) + "\n" + wapRequest.get(WapFields.NONCE_STR) + "\n" + wapRequest.get(WapFields.PACKAGE) + "\n";
        try {
            String signKey = getPrivateKeyContent(MapUtil.getString(config, TransactionParam.WEIXIN_PRIVATE_KEY_V3));
            String paySign = RsaSignature.sign(signParams, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
            wapRequest.put(WapFields.PAY_SIGN, paySign);
        } catch (MpayException e) {
            return Workflow.RC_ERROR;
        }
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    /**
     * 设置 【服务商APPID】  【服务商商户号】 【子商户APPID】 【子商户号】
     *
     * @param builder
     * @param config
     */
    private void setAppIdAndMchId(RequestBuilder builder, Map<String, Object> config, Map<String, Object> extended) {
        builder.set(ProtocolFields.SP_APP_ID, config.get(TransactionParam.WEIXIN_APP_ID));
        builder.set(ProtocolFields.SP_MCH_ID, config.get(TransactionParam.WEIXIN_MCH_ID));

        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.WEIXIN_SUB_MCH_ID));
        String miniSubAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        if (!StringUtil.empty(miniSubAppId)) {
            builder.set(ProtocolFields.SUB_APP_ID, miniSubAppId);
        }

        // 优先使用上送的sub_app_id
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, com.wosai.mpay.api.weixin.ProtocolFields.SUB_APP_ID);
        if(!StringUtil.empty(sendSubAppId)) {
            builder.set(ProtocolFields.SUB_APP_ID, sendSubAppId);
        }
    }


    private String buildPackage(Map<String, Object> result, TransactionContext context) {
        StringBuilder sb = new StringBuilder(StringUtils.join(WapFields.PREPAY_ID, "=", result.get(ResponseFields.PREPAY_ID)));
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        if (MapUtil.isNotEmpty(extended) && extended.containsKey(Transaction.WEIXIN_DEFAULT_SUBSIDY_PERIOD_TYPE)) {
            String subsidyPeriodType = MapUtil.getString(extended, Transaction.WEIXIN_DEFAULT_SUBSIDY_PERIOD_TYPE);
            String selectedInstallmentNumber = MapUtil.getString(extended, Transaction.WEIXIN_DEFAULT_SELECTED_INSTALLMENT_NUMBER);
            if (Objects.equals(WeixinConstants.SUBSIDY_PERIOD_TYPE_DAILY, subsidyPeriodType)) {
                sb.append(StringUtils.join("&", WapV3Fields.SUBSIDY_PERIOD_TYPE, "=", WeixinConstants.SUBSIDY_PERIOD_TYPE_DAILY));
            } else if (Objects.equals(WeixinConstants.SUBSIDY_PERIOD_TYPE_PERIOD, subsidyPeriodType) && !StringUtils.isEmpty(selectedInstallmentNumber)) {
                sb.append(StringUtils.join("&", WapV3Fields.SUBSIDY_PERIOD_TYPE, "=", WeixinConstants.SUBSIDY_PERIOD_TYPE_PERIOD));
                sb.append(StringUtils.join("&", WapV3Fields.SELECTED_INSTALLMENT_NUMBER, "=", selectedInstallmentNumber));
            }
        }
        return sb.toString();
    }


    /**
     * todo check bean注入时需要填写
     *
     * @param notifyHost
     */
    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }
}
