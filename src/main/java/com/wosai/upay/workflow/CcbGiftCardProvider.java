package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.ccb.giftcard.*;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class CcbGiftCardProvider extends AbstractServiceProvider {
    public static final Logger LOGGER = LoggerFactory.getLogger(CcbGiftCardProvider.class);

    public static final String NAME = "provider.ccb.giftcard";

    @Autowired
    CcbGiftCardClient ccbGiftCardClient;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_CCB_GIFT_CARD;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return payway == Order.PAYWAY_CCB_GIFT_CARD && getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CCB_GIFT_CARD_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.putRequest(BusinessFields.TXCODE, CcbGiftCardConstants.PAY_CODE);
        setCommonRequest(requestBuilder, tradeParams);
        setPayRequest(context, requestBuilder, tradeParams);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        String privateKey = getPrivateKeyContent(MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY));
        Map<String, Object> result = null;
        try {
            result = ccbGiftCardClient.call(url, privateKey, requestBuilder);
        } catch (Exception e) {
            setTransactionContextErrorInfo(context, OP_PAY, e);
            LOGGER.error("failed to call ccb giftcard pay", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        String resultCode = MapUtil.getString(result, ResponseFields.RESULT);
        if (CcbGiftCardConstants.RESULT_SUCCESS.equalsIgnoreCase(resultCode)) {
            setTransactionContextIfPaySuccess(result, context, tradeParams);
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String resultCode = MapUtils.getString(result, ResponseFields.RESULT);
        String errCode = MapUtil.getString(result, ResponseFields.ERRCODE);
        String errMsg = MapUtil.getString(result, ResponseFields.ERRMSG);

        map.put(ResponseFields.RESULT, resultCode);//返回状态码
        map.put(ResponseFields.ERRCODE, errCode);//返回状态码
        map.put(ResponseFields.ERRMSG, errMsg);//返回状态码

        setTransactionContextErrorInfo(context.getTransaction(), key, map, CcbGiftCardConstants.RESULT_SUCCESS.equals(resultCode), errCode, errMsg);
    }

    protected void setTransactionContextIfPaySuccess(Map<String, Object> result, TransactionContext context, Map<String, Object> tradeParams) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        String resultCode = MapUtils.getString(result, ResponseFields.RESULT);
        if (CcbGiftCardConstants.RESULT_SUCCESS.equals(resultCode)) {
            List<Map<String, Object>> accList = new ArrayList<>();
            Object accListObject = BeanUtil.getProperty(result, ResponseFields.ACC_LIST);
            if (accListObject != null && accListObject instanceof List) {
                accList = (List<Map<String, Object>>) accListObject;
            }
            Map<String, Object> accInfo = null;
            String accNo = MapUtil.getString(result, ResponseFields.ACC_NO);
            if (accList.size() > 0) {
                accInfo = accList.get(0);
                if(StringUtils.isEmpty(accNo)) {
                    accNo = MapUtil.getString(result, ResponseFields.ACC_NO);
                }
            }
            if (!StringUtils.isEmpty(accNo)) {
                if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_UID))) {
                    order.put(Order.BUYER_UID, accNo);
                }
                if (StringUtils.isEmpty(BeanUtil.getPropString(order, Order.BUYER_LOGIN))) {
                    order.put(Order.BUYER_LOGIN, accNo);
                }
                if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                    transaction.put(Transaction.BUYER_UID, accNo);
                }
                if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
                    transaction.put(Transaction.BUYER_LOGIN, accNo);
                }
            }
            if (accInfo != null) {
                String tranId = MapUtil.getString(accInfo, ResponseFields.TRAN_ID);
                if (!StringUtils.isEmpty(tranId)) {
                    order.put(Order.TRADE_NO, tranId);
                    transaction.put(Transaction.TRADE_NO, tranId);
                }
            }
            long finishTime = System.currentTimeMillis();
            transaction.put(Transaction.CHANNEL_FINISH_TIME, finishTime);
        }
    }

    private void setCommonRequest(RequestBuilder request, Map<String, Object> tradeParams) {
        request.putRequest(BusinessFields.CAMPUS_ID, MapUtils.getString(tradeParams, TransactionParam.CCB_GIFT_CARD_PARAMS_CAMPUS_ID));
        request.putRequest(BusinessFields.CORP_ID, MapUtils.getString(tradeParams, TransactionParam.CCB_GIFT_CARD_PARAMS_CORP_ID));
    }

    private void setPayRequest(TransactionContext context, RequestBuilder request, Map<String, Object> tradeParams) {
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);

        request.putCcbSafeParam(BusinessFields.QR_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        request.putCcbSafeParam(BusinessFields.ORDER_ID, tsn);
        request.putCcbSafeParam(BusinessFields.BUSINESS_ID, MapUtils.getString(tradeParams, TransactionParam.CCB_GIFT_CARD_PARAMS_BUSINESS_ID));
        request.putCcbSafeParam(BusinessFields.VPOS_ID, MapUtils.getString(tradeParams, TransactionParam.CCB_GIFT_CARD_PARAMS_VPOS_ID));
        request.putCcbSafeParam(BusinessFields.OFFLINE, CcbGiftCardConstants.ONLINE);
        request.putCcbSafeParam(BusinessFields.PAYMENT, StringUtils.cents2yuan(effectiveAmount));
        request.putCcbSafeParam(BusinessFields.ACTUAL_PAYMENT, StringUtils.cents2yuan(effectiveAmount));
    }

    private void setPayQueryRequest(TransactionContext context, RequestBuilder request) {
        Map<String, Object> transaction = context.getTransaction();
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        request.putCcbSafeParam(BusinessFields.ORDER_ID, tsn);
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持撤销");
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.putRequest(BusinessFields.TXCODE, CcbGiftCardConstants.PAY_QUERY_CODE);
        setCommonRequest(requestBuilder, tradeParams);
        setPayQueryRequest(context, requestBuilder);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY);
        String privateKey = getPrivateKeyContent(MapUtil.getString(tradeParams, TransactionParam.PRIVATE_KEY));
        Map<String, Object> result = null;
        try {
            result = ccbGiftCardClient.call(url, privateKey, requestBuilder);
        } catch (Exception e) {
            LOGGER.error("failed to call ccb giftcard is query", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        String resultCode = MapUtil.getString(result, ResponseFields.RESULT);
        if (CcbGiftCardConstants.RESULT_SUCCESS.equalsIgnoreCase(resultCode)) {
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持退款");
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("暂不支持预下单");
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }
}
