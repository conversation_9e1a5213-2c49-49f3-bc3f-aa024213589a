package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.fsm.Action;
import com.wosai.fsm.MachineBuilder;
import com.wosai.fsm.MachineContext;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@WorkflowPriority(priority = 3)
public class DepositQRCodeWorkflow extends AbstractPayWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(DepositQRCodeWorkflow.class);

    public static final String NAME = "generic.deposit.qrcode.workflow";

    public  static  long[] delays = {3000, 3000, 3000, 5000, 5000, 5000, 10000, 10000, 20000, 30000, 60000, 90000 };
    public  static  long[] cancelDelays = {200, 1500, 5000};

    public DepositQRCodeWorkflow() {
        this(delays, cancelDelays);
    }

    public DepositQRCodeWorkflow(long [] delays, long [] cancelDelays){
        MachineBuilder builder = new MachineBuilder();
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return depositPreFreeze((TransactionContext)context, resume);
            }
        }).transition(RC_CREATE_SUCCESS, IN_PROG)
                .transition(RC_ERROR, FAIL_CANCELED)
                .transition(RC_SYS_ERROR, FAIL_CANCELED)
                .transition(RC_PROTOCOL_ERROR,FAIL_CANCELED)
                .transition(RC_IOEX,  FAIL_CANCELED)
                .transition(RC_TRADE_CANCELED, FAIL_CANCELED)
                .transition(RC_TRADE_DISCARD, ABORTED)
        .on(IN_PROG).delay(delays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return query((TransactionContext)context);
            }
        }).transition(RC_PAY_SUCCESS, PRE_SUCCESS)
                .transition(RC_IN_PROG, IN_PROG)
                .transition(RC_IOEX, FAIL_IO_2)
                .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_2)
                .transition(RC_SYS_ERROR, FAIL_PROTOCOL_2)
                .transition(RC_ABORT, ABORTING)
                .transition(RC_ERROR, FAIL_ERROR)
                .transition(RC_EXPIRE, QUERY_EXPIRE)
                .transition(RC_TRADE_NOT_EXISTS, IN_PROG)
                .transition(RC_TRADE_CANCELED, FAIL_CANCELED)

        .on(PRE_SUCCESS).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return finish((TransactionContext)context);
            }
        }).end()

        .on(ERROR_RECOVERY).delay(cancelDelays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return cancel((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, FAIL_CANCELED)
                .transition(RC_IOEX, FAIL_IO_3)
                .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_3)
                .transition(RC_SYS_ERROR, FAIL_PROTOCOL_3)
                .transition(RC_ERROR, FAIL_ERROR)
                .transition(RC_RETRY, ERROR_RECOVERY)
                .transition(RC_EXPIRE, FAIL_ERROR)

        .on(ABORTING).delay(cancelDelays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return cancel((TransactionContext)context);
            }
        }).transition(RC_CANCEL_SUCCESS, ABORTED)
                .transition(RC_IOEX, FAIL_IO_3)
                .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_3)
                .transition(RC_SYS_ERROR, FAIL_PROTOCOL_3)
                .transition(RC_ERROR, FAIL_ERROR)
                .transition(RC_RETRY, ABORTING)
                .transition(RC_EXPIRE, FAIL_ERROR)

        .on(FAIL_CANCELED).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return safeClose((TransactionContext)context);
            }
        }).end()
                
        .on(QUERY_EXPIRE).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return queryExpireClose((TransactionContext)context);
            }
        }).end()

        .on(ABORTED).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return safeAbort((TransactionContext)context);
            }
        }).end()

        .on(FAIL_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_PROTOCOL_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_IO_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_PROTOCOL_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_IO_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_PROTOCOL_3).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end()

        .on(FAIL_IO_3).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return unsafeClose((TransactionContext)context);
            }
        }).end();


        machine = builder.build();
    }


    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_FREEZE 
                && (subPayway == Order.SUB_PAYWAY_QRCODE || subPayway == Order.SUB_PAYWAY_MINI || subPayway == Order.SUB_PAYWAY_APP
                || subPayway == Order.SUB_PAYWAY_H5 || subPayway== Order.SUB_PAYWAY_WAP)){
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        return context.getServiceProvider().explainNotification(notification);
    }

    public String depositPreFreeze(TransactionContext context, boolean resume) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_DEPOSIT_PREFREEZE), () -> {
            //时间过长 丢弃
            if(System.currentTimeMillis()-BeanUtil.getPropLong(context.getTransaction(), DaoConstants.CTIME) > ApolloConfigurationCenterUtil.getWorkflowDiscardThreshold()){
                logger.warn("sn {} delay {} ms, trade discard", BeanUtil.getPropString(context.getOrder(), Order.SN), System.currentTimeMillis()-BeanUtil.getPropLong(context.getTransaction(), DaoConstants.CTIME));
                return RC_TRADE_DISCARD;
            }
            String result = depositProvider.depositPreFreeze(context, resume);
            if (RC_CREATE_SUCCESS.equals(result)) {
                context.setForceReturn(true);
            }
            logger.debug("TID {} precreate method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @Override
    protected String cancel(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_DEPOSIT_CANCEL), () -> {
            String result = depositProvider.depositCancel(context);
            logger.debug("TID {} deposit cancel method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @Override
    protected String query(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_DEPOSIT_QUERY), () -> {
            String result = depositProvider.depositQuery(context);
            logger.debug("TID {} deposit query method returns {}", context.getTid(), result);
            return result;
        }).call();
    }
}
