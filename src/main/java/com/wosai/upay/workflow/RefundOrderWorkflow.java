package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.Action;
import com.wosai.fsm.MachineBuilder;
import com.wosai.fsm.MachineContext;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trace.TimedSupplier;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.service.FeeRateProcessor;
import com.wosai.upay.service.SceneConfigFacade;
import com.wosai.upay.service.SimpleRedisLock;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@WorkflowPriority(priority = 6)
public class RefundOrderWorkflow extends Workflow {
    private static final Logger logger = LoggerFactory.getLogger(RefundOrderWorkflow.class);

    static long[] delays = {500, 1000, 1500, 2000, 5000};
    public static final String NAME = "generic.refund.workflow";
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private SimpleRedisLock simpleRedisLock;
    @Autowired
    private FeeRateProcessor feeRateProcessor;

    public RefundOrderWorkflow() {
        this(delays);
    }

    public RefundOrderWorkflow(long[] delays) {
        MachineBuilder builder = new MachineBuilder();
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return sharingRestituteAndThenRefundOrder((TransactionContext)context);
            }
        }).transition(RC_REFUND_SUCCESS, SUCCESS)
          .transition(RC_ERROR, REFUND_ERROR)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_1)
          .transition(RC_IOEX, FAIL_IO_1)
          .transition(RC_RETRY, IN_PROG)


        .on(SUCCESS).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return finish((TransactionContext) context);
            }
        }).end()
        .on(IN_PROG).delay(delays, RC_EXPIRE).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return refundOrder((TransactionContext) context);
            }
        }).transition(RC_REFUND_SUCCESS, SUCCESS)
          .transition(RC_ERROR, REFUND_ERROR)
          .transition(RC_SYS_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_PROTOCOL_ERROR, FAIL_PROTOCOL_2)
          .transition(RC_IOEX, FAIL_IO_2)
          .transition(RC_RETRY, IN_PROG)
          .transition(RC_EXPIRE, REFUND_ERROR)
        .on(REFUND_ERROR).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext) context);
            }
        }).end()
        
        .on(FAIL_PROTOCOL_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext) context);
            }
        }).end()
        
        .on(FAIL_IO_1).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()

        .on(FAIL_PROTOCOL_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end()

        .on(FAIL_IO_2).inproc().invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return closeOrder((TransactionContext)context);
            }
        }).end();

        machine = builder.build();
    }

    @Override
    public String getName() {
        return NAME;
    }
    
    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_REFUND 
                && MapUtil.getIntValue(transaction, Transaction.PAYWAY) != Order.PAYWAY_BANKCARD
                && MapUtil.getIntValue(transaction, Transaction.PROVIDER) != Order.PROVIDER_HAIKE_UNION_PAY) {
            return true;
        }
        return false;
    }
    
    @Override
    public String explainNotification(TransactionContext context, Map<String, Object> notification) {
        return context.getServiceProvider().explainNotification(notification);
    }

    public String sharingRestituteAndThenRefundOrder(TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        try{
            facade.sharingRestitute(context);
        }catch (Exception e){
            BeanUtil.setNestedProperty(context.getTransaction(), UpayUtil.getProviderErrorInfoKey(MpayServiceProvider.OP_REFUND), CollectionUtil.hashMap(
                    "message", StringUtils.join(e.getClass() , ":" , e.getMessage()
            )));
            UpayException exception = facade.translateProfitSharingException(e);
            String path = UpayUtil.getBizErrorCodeKey(MpayServiceProvider.OP_REFUND);
            if(exception.getStandardCode() != null){
                Map bizErrorMap = SceneConfigFacade.getWosaiErrorDefinition(exception.getStandardCode(), null);
                BeanUtil.setNestedProperty(context.getTransaction(), path, bizErrorMap);
            }else{
                UpayBizError bizError = new UpayBizError(UpayBizError.REFUND_FAILED.getCode(),
                        exception.getCode(),
                        null, exception.getMessage());
                BeanUtil.setNestedProperty(context.getTransaction(), path, bizError);
            }
            logger.debug("refund sharing restitute fail or error, tid: {}, msg: {}", transaction.get(DaoConstants.ID), e.getMessage(), e);
            return Workflow.RC_ERROR;
        }
        return refundOrder(context);

    }

    public String refundOrder(TransactionContext context) {
        return TimedSupplier.of(UpayUtil.getSpanName(context.getServiceProvider().getName(), MpayServiceProvider.OP_REFUND), () -> {
            // 订单存在喔噻优惠时，首次退款已将所有实收都退掉，再次退款时退款金额为0，此时不用调支付通道退款
            long refundAmount = MapUtil.getLongValue(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT);
            String result = null;
            if(refundAmount == 0) {
                result = RC_REFUND_SUCCESS;
                context.getTransaction().put(Transaction.PAID_AMOUNT, 0);
                context.getTransaction().put(Transaction.RECEIVED_AMOUNT, 0);
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            }else {
                result = depositProvider.matchServiceProvider(context).refund(context);
            }
            logger.debug("TID {} refund method returns {}", context.getTid(), result);
            return result;
        }).call();
    }

    @SuppressWarnings("unchecked")
    @Override
    public String finish(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();

        long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        long netOriginal = BeanUtil.getPropLong(order, Order.NET_ORIGINAL);
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long netEffective = BeanUtil.getPropLong(order, Order.NET_EFFECTIVE);

        if (netOriginal - originalAmount > 0) {
            order.put(Order.NET_ORIGINAL, netOriginal - originalAmount);
            order.put(Order.NET_EFFECTIVE, netEffective - effectiveAmount);
            order.put(Order.STATUS, Order.STATUS_PARTIAL_REFUNDED);
        }else{
            order.put(Order.NET_ORIGINAL, 0L);
            order.put(Order.NET_EFFECTIVE, netEffective - effectiveAmount);
            order.put(Order.STATUS, Order.STATUS_REFUNDED);
        }
        PaymentUtil.updateOrderPaymentsNetAmountForRefundSuccess(
                (List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH),
                (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH));
        PaymentUtil.updateOrderChannelPaymentsByTransactionChannelPaymentsAndTypeAfterSuccess(order, transaction);
        //如果此笔交易是正式的免充值交易，则手续费需要重新计算，因为手续费不包含免充值的部分
        //全额退款，手续费是从付款流水里面获取的，不需要再重新计算，
        //如果是部分退款，则需要重新根据部分退款对应的免充值金额重新计算手续费
        boolean isFullRefund = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        Map<String, Object> extraOutFields =  MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean isHistoryRefund = MapUtil.getBooleanValue(extraOutFields, Transaction.IS_HISTORY_TRADE_REFUND, false);
        Map<String,Object> tradeParams = context.getServiceProvider().getTradeParams(transaction);
        if(!isFullRefund && UpayUtil.isChannelNotTopUp(transaction)){
            long fee = FeeUtil.reCalculateRefundOrCancelFee(repository, transaction, isHistoryRefund ? gatewaySupportService: null, MapUtil.getLongValue(order, DaoConstants.CTIME), isHistoryRefund ? MapUtil.getBooleanValue(extraOutFields, Transaction.USE_APPROXIMATE_FEE, false) : false);
            tradeParams.put(TransactionParam.FEE, fee);
        }
        long refundFee = MapUtil.getLongValue(tradeParams, TransactionParam.FEE);
        //reset refund fee, 以通道侧返回的手续费为主（海科通道）
        if (extraOutFields.containsKey(Transaction.REAL_TRADE_FEE)) {
            long realRefundFee = MapUtil.getLongValue(extraOutFields, Transaction.REAL_TRADE_FEE);
            tradeParams.put(TransactionParam.FEE, realRefundFee);
            if (refundFee != realRefundFee) {
                logger.warn("tsn {} refund fee is not equal, calculate fee is {}, real refund fee is {}", MapUtil.getString(transaction, Transaction.TSN), refundFee, realRefundFee);
            }
        }
        //设置额度包回退手续费
        feeRateProcessor.setRefundQuotaFee(transaction);
        // 设置SPDB退款手续费, 退款时不退手续费
        FeeUtil.resetSPDBTradeFee(transaction, tradeParams);
        if (transaction.get(Transaction.PAID_AMOUNT) == null) {
            transaction.put(Transaction.PAID_AMOUNT, effectiveAmount);
        }
        if (transaction.get(Transaction.RECEIVED_AMOUNT) == null) {
            transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
        }
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.STATUS, Transaction.STATUS_SUCCESS,
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.PAID_AMOUNT, transaction.get(Transaction.PAID_AMOUNT),
                                                                        Transaction.RECEIVED_AMOUNT, transaction.get(Transaction.RECEIVED_AMOUNT),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.EXTRA_PARAMS, transaction.get(Transaction.EXTRA_PARAMS),
                                                                        Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                                                                        Transaction.CONFIG_SNAPSHOT, transaction.get(Transaction.CONFIG_SNAPSHOT),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.FINISH_TIME, transaction.get(Transaction.FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS));

        
        Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                 Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                 Order.NET_ORIGINAL, order.get(Order.NET_ORIGINAL),
                                                                 Order.NET_EFFECTIVE, order.get(Order.NET_EFFECTIVE),
                                                                 Order.ITEMS, order.get(Order.ITEMS),
                                                                 Order.STATUS, order.get(Order.STATUS));
        

        // 历史退款交易需要更新hbase
        if(isHistoryRefund){
            try {
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
            simpleRedisLock.unlock(SimpleRedisLock.LOCK_KEY_HISTORY_REFUND_PREFIX + order.get(Order.SN), (String)transaction.get(Transaction.TSN));
            orderUpdate = null;
        }
        transaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
        finishTransaction(context, orderUpdate, transactionUpdates);
        if(context.isFakeRequest()){
            logger.debug("TID {} refund finished", context.getTid());
            return null;
        }
        logger.debug("TID {} refund finished", context.getTid());
        return null;
    }
    @SuppressWarnings("unchecked")
    public String closeOrder(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        order.put(Order.STATUS, Order.STATUS_REFUND_ERROR);
        if (!UpayUtil.isFormal(transaction)){
            facade.unfreezeWalletBalanceDeduction(BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID), BeanUtil.getPropString(transaction, DaoConstants.ID));
        }
        // RC_EXPIRE 不会变更流水状态，需要做下状态变更
        if (MapUtil.getIntValue(context.getTransaction(), Transaction.STATUS) == Transaction.STATUS_IN_PROG) {
            context.getTransaction().put(Transaction.STATUS, Transaction.STATUS_REFUND_ERROR);
        }
        Map<String, Object> transactionUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getTid(),
                                                                        Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                                                                        Transaction.TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                                                        Transaction.BUYER_LOGIN, transaction.get(Transaction.BUYER_LOGIN),
                                                                        Transaction.BUYER_UID, transaction.get(Transaction.BUYER_UID),
                                                                        Transaction.PROVIDER_ERROR_INFO, transaction.get(Transaction.PROVIDER_ERROR_INFO),
                                                                        Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME),
                                                                        Transaction.BIZ_ERROR_CODE, transaction.get(Transaction.BIZ_ERROR_CODE),
                                                                        Transaction.STATUS, transaction.get(Transaction.STATUS));

        Map<String, Object> orderUpdates = CollectionUtil.hashMap(DaoConstants.ID, context.getOid(),
                                                                  Order.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                  Order.STATUS, order.get(Order.STATUS));

         // 历史退款交易需要更新hbase
        if(BeanUtil.getPropBoolean(context.getTransaction(), Transaction.KEY_IS_HISTORY_TRADE_REFUND, false)){
            try {
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
            simpleRedisLock.unlock(SimpleRedisLock.LOCK_KEY_HISTORY_REFUND_PREFIX + order.get(Order.SN), (String)transaction.get(Transaction.TSN));
        }else {
            repository.getOrderDao().updatePart(orderUpdates);
        }
        repository.getTransactionDao().updatePart(transactionUpdates);
        amqpFacade.errorTransactionNotify(context.getOrder(), context.getTransaction());
        logger.debug("TID {} refund closed", context.getTid());
        return null;
    }
}
