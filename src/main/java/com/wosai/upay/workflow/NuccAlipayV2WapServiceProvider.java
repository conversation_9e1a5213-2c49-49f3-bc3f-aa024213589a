package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV2NewClient;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.alipay.RequestV2Builder;
import com.wosai.mpay.api.nucc.ProtocolFields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created by jianfree on 25/4/16.
 */
public class NuccAlipayV2WapServiceProvider extends AlipayV2WapServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(NuccAlipayV2WapServiceProvider.class);

    public static final String NAME = "provider.alipay.nucc.wap.v2";


    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);

        if ((Order.PAYWAY_ALIPAY == payway|| Order.PAYWAY_ALIPAY2 == payway )
                && (Order.SUB_PAYWAY_WAP == subPayway ||Order.SUB_PAYWAY_MINI == subPayway)) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.NUCC_TRADE_PARAMS);
    }

    @Override
    public RequestV2Builder getAlipayV2Builder(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = new RequestV2Builder();

        builder.set(ProtocolFields.PID, BeanUtil.getPropString(config,TransactionParam.NUCC_ALIPAY_PID));
        builder.set(ProtocolV2Fields.CHARSET,AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        String sysProviderId = BeanUtil.getPropString(config, TransactionParam.NUCC_SYS_PROVIDER_ID);
        if(!StringUtil.empty(sysProviderId)) {
            builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, CollectionUtil.hashMap(
                    BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, sysProviderId
            ));
        }
        String alipaySubMchId = BeanUtil.getPropString(config, TransactionParam.NUCC_ALIPAY_SUB_MCH_ID);
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(
                BusinessV2Fields.SUB_MERCHANT_ID, alipaySubMchId
        ));
        builder.bizSet(ProtocolFields.IDC_FLAG, config.get(TransactionParam.NUCC_IDC_FLAG));
        return builder;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String gatewayUrl, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        return client.call(gatewayUrl, AlipayV2NewClient.SIGN_TYPE_NUCC, getPrivateKeyContent((String) config.get(TransactionParam.NUCC_PRIVATE_KEY)), request);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_NUCC;
    }


}
