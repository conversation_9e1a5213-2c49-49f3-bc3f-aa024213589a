package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.chinaums.*;
import com.wosai.mpay.api.chinaums.ChinaumsConstants.CardAttrEnum;
import com.wosai.mpay.api.unionpayopen.UnionPayOpenConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 
 * 银商支付通道v1版本
 *
 */
public class ChinaumsV1ServiceProvider extends AbstractServiceProvider {
    public static final Logger logger = LoggerFactory.getLogger(ChinaumsV1ServiceProvider.class);

    public static final String TRADE_PARAMS_SQB_VERSION_1 = "1";
    public static final String NAME = "provider.chinaums.v1";
    private static final Map<Integer, String> SUFFIX_CSB_URI = MapUtil.hashMap(Order.PAYWAY_ALIPAY, "/trade/create",
                            Order.PAYWAY_ALIPAY2, "/trade/create",
                            Order.PAYWAY_WEIXIN, "/wx/unified-order",
                            Order.PAYWAY_UNIONPAY, "/acp/js-pay"
            );
    private static final String ALIPAY_FOOD_ORDER_TYPE = "food_order_type";
    
    private static final Map<String, String> PAYMENT_TYPE = MapUtil.hashMap(CardAttrEnum.CREDIT_CARD.getCode(), Payment.TYPE_BANKCARD_CREDIT,
                            CardAttrEnum.DEBIT_CARD.getCode(), Payment.TYPE_BANKCARD_DEBIT
            );
    private static final long TIME_EXPIRE_MILLIS = DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000;
    
    private static final Map<String, String> feeMap = new ConcurrentHashMap<String, String>();
    private static final Map<String, Integer> feeRateIntegerMap = new ConcurrentHashMap<String, Integer>();
    private static int PROIVDER_FEE_RATE_FIXED_COST = 22; // 通道成本，为了方便做校验转换成了整型，实际为0.22%
    
    private String notifyHost;
    @Value("${chinaums.connection.readTimeout}")
    private int readTimeout;

    public ChinaumsV1ServiceProvider(){
        this.dateFormat = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TOTAL_AMOUNT, BusinessFields.TRANSACTION_AMOUNT));
    }

    @Autowired
    private ChinaumsClient client;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if (Objects.isNull(tradeParams)) {
            return false;
        }
        boolean sqbVersionMatch = Objects.equals(TRADE_PARAMS_SQB_VERSION_1, MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_SQB_VERSION));
        return sqbVersionMatch || !UpayUtil.isFormalByTradeParams(tradeParams);
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.CHINAUMS_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_CHINAUMS;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);

        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.EXT_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.BARCODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.TOTAL_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.FEE_RATIO, parseFeerate((String)tradeParams.get(TransactionParam.FEE_RATE)));
        builder.set(BusinessFields.BIZ_IDENTIFIER, ChinaumsConstants.BIZ_IDENTIFIER_YRY);
        setSubject(builder, MapUtil.getString(transaction, Transaction.SUBJECT));
        setCostSubsidy(builder, (String)tradeParams.get(TransactionParam.FEE_RATE));

        //如果微信，sub_appid需要传递
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(payway == Order.PAYWAY_WEIXIN){
            builder.set(BusinessFields.SUB_APP_ID, MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID));
        }
        limitCredit(builder, transaction);
        carryOverExtendedParams(extendedParams, builder);

        //259终端信息上送
        setTerminalInfo(context, builder, true);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_PAY);
        } catch (Exception e) {
            logger.error("failed to call chinaums v1 pay", e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            if(e instanceof MpayApiSendError || e instanceof MpayApiReadError) {
                // 银商下单接口只有在有明确结果后才会返回，接口超时时间为4s，需要做特殊处理
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    private void setCostSubsidy(RequestBuilder builder, String feeRate) {
        // 商户费率小于通道成本时，需要上送补贴标识
        if(!feeRateIntegerMap.containsKey(feeRate)) {
            feeRateIntegerMap.put(feeRate, new BigDecimal(feeRate).multiply(new BigDecimal(100)).intValue());
        }
        if(feeRateIntegerMap.get(feeRate) < PROIVDER_FEE_RATE_FIXED_COST) {
            builder.set(BusinessFields.COST_SUBSIDY, true);
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), StringUtil.join(".", MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? OP_PAY : ApolloConfigurationCenterUtil.GATEWAY_OP_WAP, OP_CANCEL));

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.EXT_ORDER_ID, transaction.get(Transaction.ORDER_SN));

        //259终端信息上送
        setTerminalInfo(context, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 3, OP_CANCEL);
        } catch (Exception e) {
            logger.error("failed to call chinaums v1 cancel", e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(result, context);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), StringUtil.join(".", MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? OP_PAY : ApolloConfigurationCenterUtil.GATEWAY_OP_WAP, OP_QUERY));

        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.EXT_ORDER_ID, transaction.get(Transaction.ORDER_SN));

        //259终端信息上送
        setTerminalInfo(context, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 3, OP_QUERY);
        } catch (Exception e) {
            logger.error("failed to call chinaums query", e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IOEX;
        }

        return buildQueryResult(result, context);
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), StringUtil.join(".", MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE ? OP_PAY : ApolloConfigurationCenterUtil.GATEWAY_OP_WAP, OP_REFUND));


        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        builder.set(BusinessFields.EXT_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.REFUND_AMOUNT, transaction.get(Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.REFUND_EXT_ORDERID, transaction.get(Transaction.TSN));

        //259终端信息上送
        setTerminalInfo(context, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url, builder.build(), appId, appKey, 1, OP_REFUND);
        } catch (Exception e) {
            logger.error("failed to call chinaums v1 refund", e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context);
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String respCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        String respMsg = MapUtil.getString(result, ResponseFields.ERR_MSG);
        map.put(ResponseFields.ERR_CODE, respCode);
        map.put(ResponseFields.ERR_MSG, respMsg);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, Objects.equals(respCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS), respCode, respMsg);
    }

    protected Map<String, Object> retryIfNetworkException(String url, Map<String,Object> request, String appId, String appKey
            , int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i < times; ++i) {
            try {
                // 银商bsc接口是同步的，只到有明确接口后才会返回，将此接口改为4秒超时
                if(OP_PAY.equals(opFlag)) {
                    return client.call(url, appId, appKey, request, 4000);
                }else if (OP_REFUND.equals(opFlag)){
                    return client.call(url, appId, appKey, request, readTimeout * 2);
                }else {
                    return client.call(url, appId, appKey, request);
                }
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in chinaums v1 {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        //明确成功
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String)result.get(ResponseFields.PAY_TIME)));
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }else if(ChinaumsConstants.RESP_CODE_V1_PAY_FAIL_SET.contains(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    private String buildPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        //明确成功
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if(payway == Order.PAYWAY_WEIXIN) {
                if(MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_MINI) {
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, result.get(ResponseFields.MINIPAY_REQUEST));
                }else {
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, result.get(ResponseFields.JSPAY_REQUEST));
                }
            }else if(payway == Order.PAYWAY_ALIPAY2) {
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, MapUtil.getString(result, ResponseFields.TARGET_ORDER_ID)));
            }else if(payway == Order.PAYWAY_UNIONPAY) {
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(ResponseFields.REDIRECTURL, MapUtil.getString(result, ResponseFields.REDIRECTURL)));
            }
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_TRADE_CANCELED;
    }

    private String buildCancelResult(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        String errMsg = MapUtil.getString(result, ResponseFields.ERR_MSG);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)
                || (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_NO_ORDER) && !ChinaumsConstants.ERR_MSG_TRADE_HAS_REFUND.equals(errMsg))) {
            return Workflow.RC_CANCEL_SUCCESS;
        }else if(Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_NO_CROSS_DAY_TRADING)) {
            String refundResult = refund(context);
            return Workflow.RC_REFUND_SUCCESS.equals(refundResult) ? Workflow.RC_CANCEL_SUCCESS : refundResult;
        }
        if (ChinaumsConstants.RESP_CODE_V1_CANCEL_RETRY_SET.contains(responseCode)) {
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_ERROR;
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }

        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);
        if (Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            context.getTransaction().put(Transaction.TRADE_NO, result.get(ResponseFields.REFUND_ORDER_ID));
            resolveRefundFund(context, result);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    private String buildQueryResult(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.ERR_CODE);

        int payway = MapUtil.getIntValue(context.getTransaction(), Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(context.getTransaction(), Transaction.SUB_PAYWAY);
        //特殊解决云闪付门店码查询报错的问题
        if("TARGET_FAIL".equals(responseCode) && payway == Order.PAYWAY_UNIONPAY && subPayway != Order.SUB_PAYWAY_BARCODE){
            return Workflow.RC_IN_PROG;
        }
        if (!Objects.equals(responseCode, ChinaumsConstants.RESP_CODE_V1_SUCCESS)) {
            return Workflow.RC_IOEX;
        }

        setTradeNoBuyerInfoIfExists(result, context);
        String queryResCode = MapUtil.getString(result, ResponseFields.STATUS, StringUtils.EMPTY);
        switch (queryResCode) {
            //支付成功
            case ChinaumsConstants.STATUS_TRADE_SUCCESS:
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String)result.get(ResponseFields.PAY_TIME)));
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            //处理中，状态未知
            case ChinaumsConstants.STATUS_NEW_ORDER:
            case ChinaumsConstants.STATUS_UNKNOWN:
            case ChinaumsConstants.STATUS_WAIT_BUYER_PAY:
                return Workflow.RC_IN_PROG;
            //交易已关闭
            case ChinaumsConstants.STATUS_TRADE_CLOSED:
                return Workflow.RC_TRADE_CANCELED;
            default:
                return Workflow.RC_IN_PROG;
        }

    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (result == null || result.isEmpty()) {
            return;
        }
        switch(MapUtil.getIntValue(context.getTransaction(), Transaction.PAYWAY)) {
            case Order.PAYWAY_ALIPAY2:
                resolveAlipayPayFund(result, context);
                break;
            case Order.PAYWAY_WEIXIN:
                resolveWeixinPayFund(result, context);
                break;
            case Order.PAYWAY_UNIONPAY:
                resolveUnionPayPayFund(result, context);
                break;
        }
    }

    private void resolveAlipayPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> oriPromotionList = MapUtil.getMap(result, ResponseFields.ORI_PROMOTION_LIST);
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long invoiceAmount = MapUtil.getLongValue(result, ResponseFields.INVOICE_AMOUNT);

        long couponSum = 0;
        long receiverAmount = effectiveAmount;
        List<Map<String,Object>> payments = new ArrayList<Map<String,Object>>();
        if(Objects.nonNull(oriPromotionList) && !oriPromotionList.isEmpty()) {
            Map<String, Object> chnlInfo = MapUtil.getMap(oriPromotionList, ResponseFields.CHNL_INFO);
            String alipayChnlList = MapUtil.getString(chnlInfo, ResponseFields.ALIPAY_CHNL_LIST);
            if(!StringUtil.empty(alipayChnlList)) {
                List<Map<String, Object>> couponInfo = JsonUtil.jsonStrToObject(new String(Base64.decode(alipayChnlList)), List.class);
                if(Objects.nonNull(couponInfo) && !couponInfo.isEmpty()) {
                    for (Map<String,Object> coupon: couponInfo) {
                        String fundChannel = BeanUtil.getPropString(coupon, BusinessV2Fields.FUNDCHANNEL);
                        long amount = StringUtils.yuan2cents(BeanUtil.getPropString(coupon, BusinessV2Fields.AMOUNT));

                        couponSum = couponSum + amount;
                        amount = Math.abs(amount);
                        Map<String, Object> payment = (Map) AlipayV2ServiceProvider.fundChannelPayment.get(fundChannel);
                        if(payment == null){
                            payment = CollectionUtil.hashMap(
                                    Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                                    Transaction.PAYMENT_ORIGIN_TYPE, fundChannel
                            );
                        }else{
                            payment = (Map)((HashMap)payment).clone();
                        }
                        payment.put(Transaction.PAYMENT_AMOUNT, amount);
                        payments.add(payment);
                        
                        // 商户免充值不结算给商户
                        String paymentType = MapUtil.getString(payment, Transaction.PAYMENT_TYPE);
                        if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(paymentType)) {
                            receiverAmount -= amount;
                        }
                    }
                }
            }
        }else if(invoiceAmount > 0) {
            String cardAttr = MapUtil.getString(result, ResponseFields.CARD_ATTR);
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, invoiceAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                    Transaction.PAYMENT_TYPE, getPaymentType(cardAttr, Payment.TYPE_WALLET_ALIPAY)));
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        long amount = effectiveAmount - couponSum;
        if (amount > 0 && payments.isEmpty()) {
            String cardAttr = MapUtil.getString(result, ResponseFields.CARD_ATTR);
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                    Transaction.PAYMENT_TYPE, getPaymentType(cardAttr, Payment.TYPE_BANKCARD_DEBIT)));
        
        }
        
        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        long discount = effectiveAmount - invoiceAmount;
        if(effectiveAmount - invoiceAmount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
        }
        transaction.put(Transaction.PAID_AMOUNT, invoiceAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiverAmount);
    }

    private void resolveWeixinPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> oriPromotionList = MapUtil.getMap(result, ResponseFields.ORI_PROMOTION_LIST);
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long invoiceAmount = MapUtil.getLongValue(result, ResponseFields.INVOICE_AMOUNT);

        long couponSum = 0;
        long receiverAmount = effectiveAmount;
        List<Map<String,Object>> payments = new ArrayList<Map<String,Object>>();
        long discountChanelMchTotal = 0l;
        if(Objects.nonNull(oriPromotionList) && !oriPromotionList.isEmpty()) {
            Map<String, Object> chnlInfo = MapUtil.getMap(oriPromotionList, ResponseFields.CHNL_INFO);
            String weixinChnlList = MapUtil.getString(chnlInfo, ResponseFields.WXPAY_PROMOTION_DETAIL);
            if(!StringUtil.empty(weixinChnlList)) {
                List<Map<String, Object>> couponInfo = JsonUtil.jsonStrToObject(new String(Base64.decode(weixinChnlList)), List.class);
                if(Objects.nonNull(couponInfo) && !couponInfo.isEmpty()) {
                    // 解析微信优惠
                    for (Map<String,Object> promotion: couponInfo) {
                        Map<String, Object> payment = null;
                        String promotionType = BeanUtil.getPropString(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_TYPE);
                        String promotionId = BeanUtil.getPropString(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_PROMOTION_ID);
                        long amount = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_AMOUNT);
                        long wxpayContribute = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                        long otherContribute = BeanUtil.getPropLong(promotion, com.wosai.mpay.api.weixin.ResponseFields.PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                        long channelAmount = wxpayContribute + otherContribute;
                        long mchAmount = amount - channelAmount;
                        //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                        if(WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(promotionType)){
                            if(mchAmount > 0){
                                payment = CollectionUtil.hashMap(
                                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                                Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                                Transaction.PAYMENT_AMOUNT, mchAmount,
                                                Transaction.PAYMENT_SOURCE, promotionId
                                        );
                                discountChanelMchTotal += mchAmount;
                            }else if(channelAmount > 0){
                                payment = CollectionUtil.hashMap(
                                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                                Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                                Transaction.PAYMENT_AMOUNT, channelAmount,
                                                Transaction.PAYMENT_SOURCE, promotionId
                                        );
                            }
                        }else if(WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(promotionType)){
                            if(mchAmount > 0){
                                payment = CollectionUtil.hashMap(
                                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                                Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                                Transaction.PAYMENT_AMOUNT, mchAmount,
                                                Transaction.PAYMENT_SOURCE, promotionId
                                        );
                            }else if(channelAmount > 0){
                                payment = CollectionUtil.hashMap(
                                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                                Transaction.PAYMENT_ORIGIN_TYPE, promotionType,
                                                Transaction.PAYMENT_AMOUNT, channelAmount,
                                                Transaction.PAYMENT_SOURCE, promotionId
                                        );
                            }
                        }
                        if(Objects.nonNull(payment)) {
                            couponSum += MapUtil.getLongValue(payment, Transaction.PAYMENT_AMOUNT);
                            payments.add(payment);
                        }
                    }
                }
            }
        }
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            String cardAttr = MapUtil.getString(result, ResponseFields.CARD_ATTR);
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                    Transaction.PAYMENT_TYPE, getPaymentType(cardAttr, Payment.TYPE_WALLET_WEIXIN)));
        
        }
        
        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if(couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum);
        }
        transaction.put(Transaction.PAID_AMOUNT, invoiceAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount - discountChanelMchTotal);
    }

    private void resolveUnionPayPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String,Object>> payments = new ArrayList<>();
        long couponSum = 0;
        long receiverAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        Map<String, Object> oriPromotionList = MapUtil.getMap(result, ResponseFields.ORI_PROMOTION_LIST);
        if(Objects.nonNull(oriPromotionList) && !oriPromotionList.isEmpty()) {
            Map<String, Object> chnlInfo = MapUtil.getMap(oriPromotionList, ResponseFields.CHNL_INFO);
            String cupPromotionDetail = MapUtil.getString(chnlInfo, ResponseFields.CUP_PROMOTION_DETAIL);
            if(!StringUtil.empty(cupPromotionDetail)) {
                List<Map<String, Object>> couponInfo = JsonUtil.jsonStrToObject(new String(Base64.decode(cupPromotionDetail)), List.class);
                if(Objects.nonNull(couponInfo) && !couponInfo.isEmpty()) {
                    for (Map<String,Object> coupon: couponInfo) {
                        String spnsrId = BeanUtil.getPropString(coupon, ResponseFields.CUP_PROMOTION_DETAIL_SPNSR_ID); //出资方
                        long amount = BeanUtil.getPropLong(coupon, ResponseFields.CUP_PROMOTION_DETAIL_OFFST_AMT);
                        String couponId = BeanUtil.getPropString(coupon, ResponseFields.CUP_PROMOTION_DETAIL_ID);
                        String couponType = BeanUtil.getPropString(coupon, ResponseFields.CUP_PROMOTION_DETAIL_TYPE);
                        couponSum = couponSum + amount;
                        //注意银联接口返回不能准确的区分商户优惠是否是免充值，默认当做是免充值的优惠。如果后续此通道对接了微信交易，需要特别注意。
                        String paymentType;
                        if(UnionPayOpenConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)){
                            paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                        }else{
                            paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                        }
                        payments.add(CollectionUtil.hashMap(
                                Transaction.PAYMENT_AMOUNT, amount,
                                Transaction.PAYMENT_SOURCE, couponId,
                                Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                                Transaction.PAYMENT_TYPE, paymentType
                                ));
                        
                        // 商户免充值不结算给商户
                        if(Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(paymentType)) {
                            receiverAmount -= amount;
                        }
                    }
                }
            }
        }
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long invoiceAmount = BeanUtil.getPropLong(result, ResponseFields.INVOICE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            String cardAttr = MapUtil.getString(result, ResponseFields.CARD_ATTR);
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                    Transaction.PAYMENT_TYPE, getPaymentType(cardAttr, Payment.TYPE_BANKCARD_DEBIT)));
        
        }
        
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if(oldPayments == null || oldPayments.isEmpty()){
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        long discount = effectiveAmount - invoiceAmount;
        if(effectiveAmount - invoiceAmount > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
            context.getOrder().put(Order.NET_DISCOUNT, discount);
        }
        transaction.put(Transaction.PAID_AMOUNT, invoiceAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receiverAmount);
    }
    
    private String getPaymentType(String cardAttr, String defaultPaymentType) {
        String paymentType = defaultPaymentType;
        if(!StringUtil.empty(cardAttr)) {
            CardAttrEnum card = CardAttrEnum.queryByCode(cardAttr);
            if(card != null) {
                paymentType = PAYMENT_TYPE.getOrDefault(card.getCode(), defaultPaymentType);
            }
        }
        return paymentType;
    }

    private void resolveRefundFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(payway == Order.PAYWAY_ALIPAY2) {
            resolveAlipayRefundFund(context, result);
        }else {
            resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
        }
    }

    private void resolveAlipayRefundFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> oriInfo = MapUtil.getMap(result, ResponseFields.ORI_INFO);
        Map<String, Object> chnlInfo = MapUtil.getMap(oriInfo, ResponseFields.CHNL_INFO);
        String refundDetailItemList = MapUtil.getString(chnlInfo, ResponseFields.REFUND_DETAIL_ITEM_LIST);
        long sendBackAmount = MapUtil.getLongValue(result, ResponseFields.REFUND_SEND_BACK_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        long paidAmount = 0L;
        long receivedAmount = sendBackAmount;
        List<Map<String, Object>> payments = new ArrayList<>();
        if(!StringUtils.empty(refundDetailItemList)) {
            List<Map<String, Object>> couponInfo = JsonUtil.jsonStrToObject(new String(Base64.decode(refundDetailItemList)), List.class);
            for (Map<String,Object> coupon: couponInfo) {
                String fundChannel = BeanUtil.getPropString(coupon, BusinessV2Fields.FUNDCHANNEL);
                long amount = StringUtils.yuan2cents(BeanUtil.getPropString(coupon, BusinessV2Fields.AMOUNT));
                amount = Math.abs(amount);
                Map<String, Object> payment = (Map) AlipayV2ServiceProvider.fundChannelPayment.get(fundChannel);
                if(payment == null){
                    payment = CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                            Transaction.PAYMENT_ORIGIN_TYPE, fundChannel
                    );
                }else{
                    payment = (Map)((HashMap)payment).clone();
                }
                if(!AlipayV2ServiceProvider.consumerDiscount.contains(fundChannel)){
                    paidAmount += amount;
                }
                payment.put(Transaction.PAYMENT_AMOUNT, amount);
                payments.add(payment);
            }
        }
        if(payments.isEmpty()) {
            String cardAttr = MapUtil.getString(result, ResponseFields.CARD_ATTR);
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, sendBackAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                    Transaction.PAYMENT_TYPE, getPaymentType(cardAttr, Payment.TYPE_WALLET_ALIPAY)));
        }
        if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0){
            transaction.put(Transaction.PAID_AMOUNT, paidAmount);
        }
        if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0){
            transaction.put(Transaction.RECEIVED_AMOUNT, receivedAmount);
        }
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerId = MapUtil.getString(result, ResponseFields.BUYER_ID);
            String subBuyerId = MapUtil.getString(result, ResponseFields.SUB_BUYER_ID);
            if (!StringUtil.empty(subBuyerId)) {
                buyerId = subBuyerId;
            }
            if (!StringUtil.empty(buyerId)) {
                transaction.put(Transaction.BUYER_UID, buyerId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerUserName = MapUtil.getString(result, ResponseFields.BUYER_USER_NAME);
            if (!StringUtil.empty(buyerUserName)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerUserName);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String orderId = MapUtil.getString(result, ResponseFields.MER_ORDER_ID);
            if (!StringUtil.empty(orderId)) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtil.empty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String channelTradeNo = MapUtil.getString(result, ResponseFields.TARGET_ORDER_ID);
            if (!StringUtil.empty(channelTradeNo)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTradeNo);
            }
        }
    }

    public RequestBuilder getDefaultRequestBuilder(Map<String,Object> config, Map<String, Object> transaction) {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ProtocolFields.MID, config.get(TransactionParam.CHINAUMS_MCH_CODE));
        requestBuilder.set(ProtocolFields.TID, config.get(TransactionParam.CHINAUMS_TERM_CODE));
        requestBuilder.set(BusinessFields.REQUEST_TIMESTAMP, formatTimeString(System.currentTimeMillis()));
        requestBuilder.set(BusinessFields.INST_MID, ChinaumsConstants.YUEDAN_DEFAULT);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(Order.PAYWAY_WEIXIN == payway && Order.SUB_PAYWAY_MINI == subPayway) {
            requestBuilder.set(BusinessFields.INST_MID, ChinaumsConstants.MINI_DEFAULT);
        }
        return requestBuilder;
    }

    public String buildMerchantRemark(String subject) {
        if (subject.length() > 30) {
            StringBuilder merchantRemarkBuilder = new StringBuilder(30);
            return merchantRemarkBuilder.append(subject, 0, 27).append("...").toString();
        }
        return subject;
    }

    private void limitCredit(RequestBuilder builder, Map<String, Object> transaction) {
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_CREDIT_CARD, true);
        }
    }

    private void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        if (Objects.isNull(extended) || extended.isEmpty()) {
            return;
        }
        // 间连渠道参数转换
        if(extended.containsKey(BusinessV2Fields.EXTEND_PARAMS) && extended.get(BusinessV2Fields.EXTEND_PARAMS) instanceof Map) {
            Map<String, Object> extendParams = (Map)extended.get(BusinessV2Fields.EXTEND_PARAMS);
            if(null != extendParams) {
                if(extendParams.containsKey(ALIPAY_FOOD_ORDER_TYPE)) {
                    // 支付宝扫码点餐转换
                    extended.put(BusinessFields.RET_COMM_PARAMS, CollectionUtil.hashMap(BusinessFields.FOOD_ORDER_TYPE, extendParams.get(ALIPAY_FOOD_ORDER_TYPE)));
                }else if(extendParams.containsKey(TransactionParam.HB_FQ_NUM)) {
                    // 支付宝花呗分期转换
                    extended.put(BusinessFields.INSTALLMENT_NUMBER, BeanUtil.getPropInt(extendParams, TransactionParam.HB_FQ_NUM));
                }
            }
            extended.remove(BusinessV2Fields.EXTEND_PARAMS);
        }

        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            builder.set(key, value);
        }
    }



    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String appId = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_ID);
        String appKey = MapUtil.getString(tradeParams, TransactionParam.CHINAUMS_APP_KEY);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP);

        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        RequestBuilder builder = getDefaultRequestBuilder(tradeParams, transaction);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(BusinessFields.NOTIFY_URL, notifyUrl);
        }
        builder.set(BusinessFields.EXT_ORDER_ID, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.TRANSACTION_AMOUNT, MapUtil.getLongValue(context.getOrder(), Order.ORIGINAL_TOTAL));
        builder.set(BusinessFields.TOTAL_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.set(BusinessFields.FEE_RATIO, parseFeerate((String)tradeParams.get(TransactionParam.FEE_RATE)));
        builder.set(BusinessFields.TRADE_TYPE, subPayway == Order.SUB_PAYWAY_MINI ? ChinaumsConstants.TRADE_TYPE_MINI : ChinaumsConstants.TRADE_TYPE_JSAPI);
        builder.set(BusinessFields.EXPIRE_TIME, formatTimeString(System.currentTimeMillis() + TIME_EXPIRE_MILLIS));
        int payway = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(payway == Order.PAYWAY_WEIXIN) {
            builder.set(BusinessFields.SUB_OPEN_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
            boolean isMini = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_MINI;
            String subAppId = MapUtil.getString(tradeParams, isMini ? TransactionParam.CHINAUMS_WEIXIN_MINI_SUB_APP_ID : TransactionParam.CHINAUMS_WEIXIN_SUB_APP_ID);
            if(!StringUtil.empty(subAppId)) {
                builder.set(BusinessFields.SUB_APP_ID, subAppId);
            }
        } else if (payway == Order.PAYWAY_UNIONPAY) {
            builder.set(BusinessFields.USER_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
            builder.set(BusinessFields.QRCODE, MapUtil.getString(extendedParams, Transaction.QR_CODE));
            builder.set(BusinessFields.CODE, MapUtil.getString(extendedParams, Transaction.EXTENDED_USER_AUTH_CODE));
            builder.set(BusinessFields.QRCODE_TYPE, ChinaumsConstants.QRCODE_TYPE_STATIC);
            if (extraParams != null) {
                extendedParams.remove(Transaction.QR_CODE);
                extendedParams.remove(Transaction.EXTENDED_USER_AUTH_CODE);
            }
        } else {
            builder.set(BusinessFields.USER_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        }
        setSubject(builder, MapUtil.getString(transaction, Transaction.SUBJECT));
        setCostSubsidy(builder, (String)tradeParams.get(TransactionParam.FEE_RATE));
        limitCredit(builder, transaction);
        carryOverExtendedParams(extendedParams, builder);
        //银联终端信息上送
        setTerminalInfo(context, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(url + SUFFIX_CSB_URI.get(payway), builder.build(), appId, appKey, 1, ApolloConfigurationCenterUtil.GATEWAY_OP_WAP);
        } catch (Exception e) {
            logger.error("failed to call chinaums v1 precreate", e);
            setTransactionContextErrorInfo(context, ApolloConfigurationCenterUtil.GATEWAY_OP_WAP, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, ApolloConfigurationCenterUtil.GATEWAY_OP_WAP);

        return buildPrecreateResult(result, context);
    }

    private void setSubject(RequestBuilder builder, String subject) {
        if(!StringUtil.empty(subject)) {
            builder.set(BusinessFields.THIRD_PARTY_GOODS_NAME, StringUtils.byteTruncate(subject, 100));
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        String status = MapUtil.getString(providerNotification, ResponseFields.STATUS);
        if(ChinaumsConstants.STATUS_TRADE_SUCCESS.equals(status)) {
            TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
//            Map<String, Object> transaction = context.getTransaction();
//            String extOrderId = MapUtil.getString(providerNotification, BusinessFields.EXT_ORDER_ID);
//            long totalAmount = MapUtil.getLongValue(providerNotification, BusinessFields.TOTAL_AMOUNT);
//            boolean isValidatePass = context.isFakeRequest();
//            if(!isValidatePass) {
//                if(MapUtil.getString(transaction, Transaction.ORDER_SN).equals(extOrderId) 
//                        && MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT) == totalAmount) {
//                    String appKey = MapUtil.getString(getTradeParams(transaction), TransactionParam.CHINAUMS_APP_KEY);
//                    isValidatePass = ChinaumsSignature.check(providerNotification, appKey);
//                }
//            }
//            if(isValidatePass) {
//                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString((String)providerNotification.get(ResponseFields.PAY_TIME)));
//                setTradeNoBuyerInfoIfExists(providerNotification, context);
//                resolvePayFund(providerNotification, context);
//                return Workflow.RC_PAY_SUCCESS;
//            }
            // 回调结果和查询接口返回的参数位置不一致，为了处理方便，还是做下查询
            return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
        }
        return null;
    }
    
    @Override
    protected int getNotifyUrlLimit() {
        return 200;
    }

    public String parseFeerate(String feeRate) {
        if(!feeMap.containsKey(feeRate)) {
            feeMap.put(feeRate, new BigDecimal(feeRate).divide(new BigDecimal(100)).setScale(4, RoundingMode.HALF_UP).toPlainString());
        }
        return feeMap.get(feeRate);
    }

    protected void setTerminalInfo(TransactionContext context, RequestBuilder builder) {
        setTerminalInfo(context, builder, false);
    }

    protected void setTerminalInfo(TransactionContext context, RequestBuilder builder, boolean isPay) {
        if (!ApolloConfigurationCenterUtil.isSendChinaums259Params()) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        if (isPay) {
            builder.set(BusinessFields.DEVICE_TYPE, terminalInfo.getOrDefaultType(ChinaumsConstants.DEVICE_TYPE_11));
            if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
                builder.set(BusinessFields.LONGITUDE, terminalInfo.getStandardFormatLongitude());
                builder.set(BusinessFields.LATITUDE, terminalInfo.getStandardFormatLatitude());
                BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
            }
            if (terminalInfo.isSendIp()) {
                builder.set(BusinessFields.IP, terminalInfo.getIp());
            }
            if (terminalInfo.getSerialNum() != null) {
                builder.set(BusinessFields.SERIAL_NUM, terminalInfo.getSerialNum());
            }
        }

        String termId = terminalInfo.getId();
        if (Objects.nonNull(termId) && termId.length() > 0) {
            builder.set(ProtocolFields.TID, termId);
        }

    }
}
