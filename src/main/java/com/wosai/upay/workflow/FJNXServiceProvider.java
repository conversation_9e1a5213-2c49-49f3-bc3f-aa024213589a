package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.fjnx.*;
import com.wosai.mpay.api.pab.PabConstant;
import com.wosai.mpay.api.pab.ResponseFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;

import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.CommonUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;


/**
 * <AUTHOR>
 * @Date 2024/6/25、23:29
 **/

public class FJNXServiceProvider extends AbstractServiceProvider {

    public static final Logger LOGGER = LoggerFactory.getLogger(FJNXServiceProvider.class);

    @Autowired
    private FJNXClient client;


    @Override
    public String getName() {

        return FJNXConstant.NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_FJNX;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {

        return getTradeParams(transaction) != null;

    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {

        return getTradeParams(transaction, TransactionParam.FJNX_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PUBLIC_KEY);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PRIVATE_KEY);
        String password = MapUtil.getString(tradeParams, TransactionParam.FJNX_PASSWORD);
        String fjnxTerminalId = MapUtil.getString(tradeParams, TransactionParam.FJNX_TERMINAL_ID);

        FJNXRequestBuilder builder = buildCommonRequest(context);
        //用户授权码
        builder.setBody(FJNXRequestFields.USER_AHR_CD, MapUtil.getString(extraParams, Transaction.BARCODE));
        //请求方流水号
        builder.setBody(FJNXRequestFields.TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        //终端编号
        builder.setBody(FJNXRequestFields.TERMINAL_ID, fjnxTerminalId);
        //支付场景
        builder.setBody(FJNXRequestFields.PAY_SCENE, FJNXConstant.OTHER);
        //交易金额
        String orderAmount = StringUtils.cents2yuan(MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT));
        builder.setBody(FJNXRequestFields.TXN_AMT, orderAmount);
        //业务类型
        builder.setBody(FJNXRequestFields.TRADE_TYPE, FJNXConstant.MICROPAY);
        //支付方式
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            builder.setBody(FJNXRequestFields.PAY_TYPE, FJNXConstant.AL);
        } else if (Order.PAYWAY_WEIXIN == payway) {
            builder.setBody(FJNXRequestFields.PAY_TYPE, FJNXConstant.WX);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            builder.setBody(FJNXRequestFields.PAY_TYPE, FJNXConstant.UN);
        }
        //商户订单信息描述
        builder.setBody(FJNXRequestFields.MERCHANT_ORDER_INFO, FJNXConstant.DEFAULT_MERCHANT_ORDER_INFO);
        //商户订单信息详情
        List<Map<String, Object>> merchantOrderInfoDtl = new ArrayList<>();
        merchantOrderInfoDtl.add(MapUtil.hashMap(FJNXRequestFields.GOODS_ID, FJNXConstant.DEFAULT_GOODS_ID, FJNXRequestFields.GOODS_NAME, FJNXConstant.DEFAULT_GOODS_NAME,
                FJNXRequestFields.GOODS_NUM, FJNXConstant.DEFAULT_GOODS_NUM, FJNXRequestFields.GOODS_PRICE, orderAmount));
        builder.setBody(FJNXRequestFields.MERCHANT_ORDER_INFO_DTL, merchantOrderInfoDtl);
        //附加信息
        builder.setBody(FJNXRequestFields.ATTACH, MapUtil.getString(transaction, Transaction.SUBJECT));
        //终端信息
        List<Map<String, Object>> termInfos = new ArrayList<>();
        termInfos.add(MapUtil.hashMap(FJNXRequestFields.DEVICE_TYPE, FJNXConstant.TERM_INFO_TERMINAL_TYPE,
                FJNXRequestFields.DEVICE_IP, builder.getBody().get(FJNXRequestFields.TERMINAL_IP)
        ));
        builder.setBody(FJNXRequestFields.TERM_INFO, termInfos);

        //勾兑查询使用
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(FJNXRequestFields.ORG_TRADE_DATE, CommonUtil.getTime(FJNXConstant.FORMAT_YYYYMMDD, MapUtil.getLong(transaction, DaoConstants.CTIME)));

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PAY);
        Map<String, Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = client.call(builder, gatewayUrl.getUrl(), privateKey, publicKey, password);
            } else {
                result = fakeCall(gatewayUrl, builder);
            }
        } catch (Exception ex) {
            logger.error("failed to call fjnx pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        return buildPayResult(result, context);
    }

    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String respCode = MapUtil.getString(result, FJNXResponseField.RET_CODE, "");
        String status = MapUtil.getString(result, FJNXResponseField.STATUS);
        setTradeNoBuyerInfoIfExists(result, context);
        if (Objects.equals(FJNXConstant.RET_CODE_SUCCESS, respCode)) {
            //明确成功
            if (FJNXConstant.SUCCESS.equals(status)) {
                resolvePayFund(context, result);
                return Workflow.RC_PAY_SUCCESS;
            } else if (FJNXConstant.INIT.equals(status) || FJNXConstant.PROCESSING.equals(status) || FJNXConstant.UNKNOWN.equals(status)) {
                return Workflow.RC_IN_PROG;
            }
        }

        //状态未知，需要查询
        if (FJNXConstant.RET_CODE_PROCESSING.equals(respCode)
                || FJNXConstant.RET_CODE_UNKNOWN.equals(respCode)
                || FJNXConstant.RET_CODE_FJNX_TIMEOUT.equals(respCode)
                || FJNXConstant.RET_CODE_SYSTEM_ERROR.equals(respCode)) {
            return Workflow.RC_IN_PROG;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private FJNXRequestBuilder buildWxC2BRequest(TransactionContext context, FJNXRequestBuilder fjnxRequestBuilder) {

        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String subAppid = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID);

        Integer subPayway = MapUtil.getInteger(transaction, Transaction.SUB_PAYWAY);

        if (subPayway == SubPayway.MINI.getCode()) {
            String minSubAppId = MapUtil.getString(tradeParams, TransactionParam.FJNX_PAY_WEIXIN_MINI_SUB_APP_ID);
            if (!StringUtils.isEmpty(minSubAppId)) {
                subAppid = minSubAppId;
            }
        }

        // 优先使用前端上送的sub_app_id
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, ProtocolFields.SUB_APP_ID);

        if (!StringUtil.empty(sendSubAppId)) {
            subAppid = sendSubAppId;

        }
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);

        fjnxRequestBuilder.setBody(FJNXRequestFields.SUB_OPENID, payerUid);

        fjnxRequestBuilder.setBody(FJNXRequestFields.SUB_APPID, subAppid);


        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_TYPE, FJNXConstant.OFFICIAL);

        fjnxRequestBuilder.setBody(FJNXRequestFields.PAY_CHNL, FJNXConstant.OFFLINE);

        return fjnxRequestBuilder;

    }

    void buildALiC2BRequest(TransactionContext context, FJNXRequestBuilder fjnxRequestBuilder) {
        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_TYPE, FJNXConstant.ALIPAY_TRADE_TYPE);
        String payerUid = BeanUtil.getPropString(context.getTransaction(), KEY_PAYER_UID);

        fjnxRequestBuilder.setBody(FJNXRequestFields.BUYER_ID, payerUid);

    }


    private FJNXRequestBuilder buildCommonRequest(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        FJNXRequestBuilder fjnxRequestBuilder = new FJNXRequestBuilder();

        String fjnxMerchantId = MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);
        fjnxRequestBuilder.setBody(FJNXRequestFields.MCHT_NO, fjnxMerchantId);
        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_DATE, CommonUtil.getTime(FJNXConstant.FORMAT_YYYYMMDD, MapUtil.getLong(transaction, DaoConstants.CTIME)));
        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_TIME, CommonUtil.getTime(FJNXConstant.FORMAT_HHMMSS, MapUtil.getLong(transaction, DaoConstants.CTIME)));

        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());

        String ip = terminalInfo.getIp();

        if (Objects.isNull(ip)) {
            ip = PabConstant.DEFAULT_IP;
        }

        String iPv4Address = CommonUtil.getIPv4Address(ip);
        fjnxRequestBuilder.setBody(FJNXRequestFields.TERMINAL_IP, iPv4Address);

        fjnxRequestBuilder.setBody(FJNXRequestFields.VERSION, FJNXConstant.VERSION);
        String application = MapUtil.getString(tradeParams, FJNXRequestFields.APPLICATION);

        fjnxRequestBuilder.setBody(FJNXRequestFields.APPLICATION, application);


        //请求头
        buildRequestHead(fjnxRequestBuilder, tradeParams);


        return fjnxRequestBuilder;

    }


    private FJNXRequestBuilder buildC2BRequest(TransactionContext context, FJNXRequestBuilder fjnxRequestBuilder) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));


        fjnxRequestBuilder.setBody(FJNXRequestFields.PAY_SCENE, FJNXConstant.H5);

        long amount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        String orderAmount = StringUtils.cents2yuan(amount);

        fjnxRequestBuilder.setBody(FJNXRequestFields.TXN_AMT, orderAmount);

        fjnxRequestBuilder.setBody(FJNXRequestFields.MERCHANT_ORDER_INFO, FJNXRequestFields.MERCHANT_ORDER_INFO);

        fjnxRequestBuilder.setBody(FJNXRequestFields.MERCHANT_ORDER_INFO_DTL, new ArrayList<>());

        fjnxRequestBuilder.setBody(FJNXRequestFields.TIME_START, MapUtil.getLong(transaction, DaoConstants.CTIME));
        fjnxRequestBuilder.setBody(FJNXRequestFields.IS_NOTIFY_FLG, "N");


        String fjnxTerminalId = MapUtil.getString(tradeParams, TransactionParam.FJNX_TERMINAL_ID);

        fjnxRequestBuilder.setBody(FJNXRequestFields.TERMINAL_ID, fjnxTerminalId);

        //退款使用
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        extraOutFields.put(FJNXRequestFields.ORG_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        extraOutFields.put(FJNXRequestFields.ORG_TRADE_DATE, CommonUtil.getTime(FJNXConstant.FORMAT_YYYYMMDD, MapUtil.getLong(transaction, DaoConstants.CTIME)));


        return fjnxRequestBuilder;
    }


    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("福建农信通道暂不支持撤单");

    }


    @Override
    public String query(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        FJNXRequestBuilder fjnxRequestBuilder = buildCommonRequest(context);
        buildQueryRequest(context, fjnxRequestBuilder);

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String publicKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PUBLIC_KEY);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PRIVATE_KEY);
        String password = MapUtil.getString(tradeParams, TransactionParam.FJNX_PASSWORD);

        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_QUERY);

        Map<String, Object> result = null;

        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = client.call(fjnxRequestBuilder, gatewayUrl.getUrl(), privateKey, publicKey, password);
            } else {
                result = fakeCall(gatewayUrl, fjnxRequestBuilder);
            }
        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            LOGGER.error("failed to call fjnx query", ex);
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);
        return queryResult(result, context);

    }

    private String queryResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //原始交易状态
        String status = MapUtil.getString(result, FJNXResponseField.ORG_STATUS, "");//响应描述

        setTradeNoBuyerInfoIfExists(result, context);
        //支付成功
        if (Objects.equals(status, FJNXConstant.SUCCESS)) {
            resolvePayFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        } else if (Objects.equals(status, FJNXConstant.PROCESSING)) {
            return Workflow.RC_IN_PROG;
        } else {
            return Workflow.RC_ERROR;
        }

    }


    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        //交易成功时间
        context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
        Map<String, Object> transaction = context.getTransaction();

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Order.PAYWAY_WEIXIN) {
            resolveWeixinPayFund(result, context);
        } else {
            resolveOtherPayFund(result, context);
        }
    }


    private void resolveWeixinPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long couponSum = 0;
        //sAmtTrans
        long discountChanelMchTotal = 0; // 商户免充值不结算给商户
        long tradeAmount = MapUtil.getLongValue(result, FJNXResponseField.TXN_AMT); //用户实际支付金额
        long channelDiscountAmount = MapUtil.getLongValue(result, FJNXResponseField.MCHT_DISCOUNT_AMT);

        long paidAmt = tradeAmount - channelDiscountAmount;
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();
        if (result.containsKey(ResponseFields.S_PROMOTION_DETAIL)) {
            List<Map<String, Object>> promotions = getPromotions(result);
            Pair<Long, Long> discount = buildWeixinPaymentsByPromotions(promotions, payments);
            couponSum = discount.getLeft();
            discountChanelMchTotal = discount.getRight();
        } else {
            if (channelDiscountAmount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                        )
                );
                couponSum += channelDiscountAmount;
            }
        }

        long totalFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (couponSum > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, couponSum + discountChanelMchTotal);
            context.getOrder().put(Order.NET_DISCOUNT, couponSum + discountChanelMchTotal);
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, totalFee - discountChanelMchTotal);
    }

    private List<Map<String, Object>> getPromotions(Map<String, Object> result) {
        Object promotionsStr = MapUtil.getObject(result, ResponseFields.S_PROMOTION_DETAIL);
        List<Map<String, Object>> promotionList = new ArrayList<>();
        if (promotionsStr instanceof List) {
            promotionList = (List<Map<String, Object>>) promotionsStr;
        } else if (promotionsStr instanceof String) {
            if (StringUtils.isEmpty(String.valueOf(promotionsStr))) {
                promotionsStr = "[]";
            }
            promotionList = JsonUtil.jsonStrToObject(String.valueOf(promotionsStr), List.class);
        }
        return promotionList;
    }


    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        if (MapUtil.isEmpty(result)) {

            return;
        }
        Map<String, Object> transaction = context.getTransaction();

        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
            String payerUid = "";
            String userId = MapUtil.getString(result, ResponseFields.S_SUB_OPENID); //用户id
            if (!StringUtil.empty(userId)) {
                payerUid = userId;
            } else {
                Map<String, Object> extraParams = (Map<String, Object>) transaction
                        .get(Transaction.EXTRA_PARAMS);
                 payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);

            }
            if(!StringUtils.isEmpty(payerUid)) {
                transaction.put(Transaction.BUYER_UID, userId);
            }

        }

        String tpOrderId = MapUtil.getString(result, FJNXResponseField.ORG_OUT_ORDER_NO); //支付宝、微信侧订单号订单单号
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.TRADE_NO, tpOrderId);

        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String orgOrderNo = MapUtil.getString(result, FJNXResponseField.ORG_ORDER_NO); //
            if (!StringUtils.isEmpty(tpOrderId)) {
                transaction.put(Transaction.TRADE_NO, orgOrderNo);
            }
        }
    }


    private void resolveOtherPayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long tradeAmt = MapUtil.getLongValue(result, FJNXResponseField.TXN_AMT); //用户实际支付金额

        long totalDiscountAmount = MapUtil.getLongValue(result, FJNXResponseField.MCHT_DISCOUNT_AMT);

        long paidAmt = tradeAmt - totalDiscountAmount;

        //通道优惠
        long channelDiscountAmount = totalDiscountAmount;
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        if (channelDiscountAmount > 0) {
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_ORIGIN_TYPE, null,
                            Transaction.PAYMENT_AMOUNT, channelDiscountAmount
                    )
            );
        }

        long amount = effectiveAmount - totalDiscountAmount;
        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (amount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, amount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));

        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }
        if (effectiveAmount - paidAmt > 0) {
            context.getOrder().put(Order.TOTAL_DISCOUNT, totalDiscountAmount);
            context.getOrder().put(Order.NET_DISCOUNT, totalDiscountAmount);
        }
        transaction.put(Transaction.PAID_AMOUNT, paidAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount - channelDiscountAmount);
    }


    @Override
    public String refund(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return query(context);
        }

        FJNXRequestBuilder fjnxRequestBuilder = buildCommonRequest(context);
        buildRefundRequest(context, fjnxRequestBuilder);

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String publicKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PUBLIC_KEY);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PRIVATE_KEY);
        String password = MapUtil.getString(tradeParams, TransactionParam.FJNX_PASSWORD);


        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_REFUND);

        Map<String, Object> result = null;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = client.call(fjnxRequestBuilder, gatewayUrl.getUrl(), privateKey, publicKey, password);
            } else {
                result = fakeCall(gatewayUrl, fjnxRequestBuilder);
            }
        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            LOGGER.error("failed to call fjnx refund", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);

        return buildRefundResult(result, context);

    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context) {

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String respCode = MapUtil.getString(result, FJNXResponseField.RET_CODE, "");
        String status = MapUtil.getString(result, FJNXResponseField.STATUS);

        if (Objects.equals(FJNXConstant.RET_CODE_SUCCESS, respCode)) {
            if (Objects.equals(status, FJNXConstant.SUCCESS)) {
                //退款成功
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;

            } else if (Objects.equals(status, FJNXConstant.PROCESSING)) {
                Map extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);
                //待查询的状态
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return Workflow.RC_RETRY;

            }
        }
        return Workflow.RC_ERROR;

    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        FJNXRequestBuilder fjnxRequestBuilder = buildCommonRequest(context);
        fjnxRequestBuilder = buildC2BRequest(context, fjnxRequestBuilder);

        Map<String, Object> tradeParams = getTradeParams(transaction);


        String publicKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PUBLIC_KEY);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FJNX_PRIVATE_KEY);
        String password = MapUtil.getString(tradeParams, TransactionParam.FJNX_PASSWORD);


        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        GatewayUrl gatewayUrl = new GatewayUrl(null, false);
        if (Objects.equals(payway, Payway.WEIXIN.getCode())) {
            buildWxC2BRequest(context, fjnxRequestBuilder);
            gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WX_WAP);
        } else if (Objects.equals(payway, Payway.ALIPAY.getCode()) || Objects.equals(payway, Payway.ALIPAY2.getCode())) {
            buildALiC2BRequest(context, fjnxRequestBuilder);
            gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_ALIPAY_WAP);
        }

        Map<String, Object> result;
        try {
            if (!gatewayUrl.isSkipEnAndDecrypt()) {
                result = client.call(fjnxRequestBuilder, gatewayUrl.getUrl(), privateKey, publicKey, password);
            } else {
                result = fakeCall(gatewayUrl, fjnxRequestBuilder);
            }
        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            LOGGER.error("failed to call fjnx precreate", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildPreCreateResult(result, context);

    }


    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String respCode = MapUtil.getString(result, FJNXResponseField.RET_CODE, "");
        String retMsg = MapUtil.getString(result, FJNXResponseField.RET_MSG, "");//响应描述

        map.put(ResponseFields.S_RESP_CODE, respCode);//返回信息
        map.put(ResponseFields.S_TRANS_STATE_MSG, retMsg);
        Boolean success = false;
        if (Objects.equals(respCode, FJNXConstant.RET_CODE_SUCCESS)) {
            success = true;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, success, respCode, retMsg);
    }


    private String buildPreCreateResult(Map<String, Object> result, TransactionContext context) {

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String retCode = MapUtil.getString(result, FJNXResponseField.RET_CODE);


        if (FJNXConstant.RET_CODE_SUCCESS.equals(retCode)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if (Order.PAYWAY_ALIPAY2 == payway) {
                String tradeNo = MapUtil.getString(result, FJNXResponseField.PREPAY_ID);
                //前面两个入参截断。
                tradeNo = tradeNo.substring(2);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            } else if (Order.PAYWAY_WEIXIN == payway) {
                String data = MapUtil.getString(result, FJNXResponseField.WC_PAY_DATA);
                Map<String, Object> wxDataPackage = wxDataFormatConvert(data);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wxDataPackage);
            }
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;


    }

    private Map<String, Object> wxDataFormatConvert(String data) {
        Map<String, Object> wxData = JsonUtil.jsonStrToObject(data, Map.class);
        Map<String, Object> result = new HashMap<>();
        result.put(WapFields.APP_ID, MapUtil.getString(wxData, FJNXRequestFields.FJNX_APP_ID));
        result.put(WapFields.PACKAGE, MapUtil.getString(wxData, FJNXRequestFields.FJNX_PACKAGE_NAME));
        result.put(WapFields.NONCE_STR, MapUtil.getString(wxData, FJNXRequestFields.FJNX_NONCE_STR));
        result.put(WapFields.SIGN_TYPE, MapUtil.getString(wxData, FJNXRequestFields.FJNX_SIGN_TYPE));
        result.put(WapFields.PAY_SIGN, MapUtil.getString(wxData, FJNXRequestFields.FJNX_PAY_SIGN));
        result.put(WapFields.TIME_STAMP, MapUtil.getString(wxData, FJNXRequestFields.FJNX_TIME_STAMP));
        return result;
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }


    private FJNXRequestBuilder buildQueryRequest(TransactionContext context, FJNXRequestBuilder fjnxRequestBuilder) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        if (Transaction.TYPE_PAYMENT == MapUtil.getIntValue(transaction, Transaction.TYPE)) {
            fjnxRequestBuilder.setBody(FJNXRequestFields.ACTION_TYPE, FJNXConstant.ACTION_TYPE_QUERY);

        } else {
            //退款查询
            fjnxRequestBuilder.setBody(FJNXRequestFields.ACTION_TYPE, FJNXConstant.ACTION_TYPE_REFUND);
        }


        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLong(context.getOrder(), DaoConstants.CTIME));


        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));

        fjnxRequestBuilder.setBody(FJNXRequestFields.ORG_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));

        fjnxRequestBuilder.setBody(FJNXRequestFields.ORG_TRADE_DATE, CommonUtil.getTime(FJNXConstant.FORMAT_YYYYMMDD, MapUtil.getLong(payOrConsumerTransaction, DaoConstants.CTIME)));

        String fjnxTerminalId = MapUtil.getString(tradeParams, TransactionParam.FJNX_TERMINAL_ID);

        fjnxRequestBuilder.setBody(FJNXRequestFields.TERMINAL_ID, fjnxTerminalId);


        return fjnxRequestBuilder;

    }

    private FJNXRequestBuilder buildRefundRequest(TransactionContext context, FJNXRequestBuilder fjnxRequestBuilder) {

        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLong(context.getOrder(), DaoConstants.CTIME));

        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        String orgTradeNo = MapUtil.getString(transaction, Transaction.ORDER_SN);
        extraOutFields.put(FJNXRequestFields.ORG_TRADE_NO, orgTradeNo);
        fjnxRequestBuilder.setBody(FJNXRequestFields.ORG_TRADE_NO, orgTradeNo);


        fjnxRequestBuilder.setBody(FJNXRequestFields.TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));

        fjnxRequestBuilder.setBody(FJNXRequestFields.OUT_REFUND_NO, MapUtil.getString(payOrConsumerTransaction, Transaction.TSN));
        String orgTradeDate = CommonUtil.getTime(FJNXConstant.FORMAT_YYYYMMDD, MapUtil.getLong(payOrConsumerTransaction, DaoConstants.CTIME));

        fjnxRequestBuilder.setBody(FJNXRequestFields.ORG_TRADE_DATE, orgTradeDate);
        extraOutFields.put(FJNXRequestFields.ORG_TRADE_DATE, orgTradeDate);

        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String orderAmount = StringUtils.cents2yuan(amount);


        fjnxRequestBuilder.setBody(FJNXRequestFields.TXN_AMT, orderAmount);

        fjnxRequestBuilder.setBody(FJNXRequestFields.ACTION_TYPE, FJNXConstant.RT001);


        return fjnxRequestBuilder;
    }


    void buildRequestHead(FJNXRequestBuilder fjnxRequestBuilder, Map<String, Object> tradeParams) {
        //请求头
        String merInstId = MapUtil.getString(tradeParams, FJNXRequestFields.MER_INSTID);
        String sysInstId = MapUtil.getString(tradeParams, FJNXRequestFields.SYS_INSTID);
        fjnxRequestBuilder.setHead(FJNXRequestFields.MER_INSTID, merInstId);
        fjnxRequestBuilder.setHead(FJNXRequestFields.SYS_INSTID, sysInstId);

        fjnxRequestBuilder.setHead(FJNXRequestFields.SIGN_TYPE, FJNXConstant.SIGN_TYPE);

    }

    public Map<String, Object> fakeCall(GatewayUrl providerGateway, FJNXRequestBuilder fjnxRequestBuilder) throws Exception {
        Map<String, Object> result = fakeClient.call(providerGateway.getUrl(), fjnxRequestBuilder.buildRequest(), "application/json", FakeConstant.JSON_FORMAT);
        //获取body信息
        String respBodyString = MapUtils.getString(result, FJNXRequestFields.BODY);

        Map<String, Object> respHeadMap = MapUtils.getMap(result, FJNXRequestFields.HEAD);

        if(StringUtils.isEmpty(respBodyString)) {
            return  respHeadMap;
        }
        return JsonUtil.jsonStringToObject(respBodyString, Map.class);
    }

}
