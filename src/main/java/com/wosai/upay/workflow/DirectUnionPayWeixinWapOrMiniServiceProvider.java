package com.wosai.upay.workflow;

import java.util.Map;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;

@ServiceProvicerPriority(priority = 1)
public class DirectUnionPayWeixinWapOrMiniServiceProvider extends UnionPayWeixinWapOrMiniServiceProvider {
    public static final String NAME = "provider.direct.unionpay.weixin.wapOrMini";
    private static final String DEFAULT_CERT_ID = "**********";
    
    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return (type == Transaction.TYPE_REFUND && payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_WAP || subPayway == Order.SUB_PAYWAY_MINI)) ? true : false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_DIRECT_UNIONPAY;
    }
    
    @Override
    public Map<String, Object> call(Map<String, Object> config, String serviceUrl, Map<String, Object> request,String opFlag) throws MpayException, MpayApiNetworkError {
        removeIllegalFields(request);
        return client.call(serviceUrl, MapUtil.getString(config, TransactionParam.SIGN_TYPE, WeixinClient.SIGN_TYPE_UNIONPAY), getPrivateKeyContent((String) config.get(TransactionParam.UNION_PAY_DIRECT_PRIVATE_KEY)), null, request);
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHANNEL_ID, config.get(TransactionParam.UNION_PAY_DIRECT_CHANNEL_ID));
        builder.set(ProtocolFields.SIGN_TYPE, MapUtil.getString(config, TransactionParam.SIGN_TYPE, WeixinConstants.SIGN_TYPE_RSA2));
        builder.set(ProtocolFields.CERT_ID, MapUtil.getString(config, TransactionParam.UNION_PAY_DIRECT_CERT_ID, DEFAULT_CERT_ID));
        builder.set(ProtocolFields.APP_ID, config.get(TransactionParam.UNION_PAY_DIRECT_WEIXIN_APP_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.MCH_ID, config.get(TransactionParam.UNION_PAY_DIRECT_WEIXIN_MCH_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID));

        //小程序支付与门店码支付，交易参数的key不一样，sub_app_id对应的值都为weixin_sub_appid
        int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_MINI == subPayway){
            String miniSubAppId = BeanUtil.getPropString(config, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
            if(!StringUtil.empty(miniSubAppId)){
                builder.set(ProtocolFields.SUB_APP_ID, miniSubAppId);
            }
        }

        setTerminalInfo(context, MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), config, builder);
        return builder;
    }
}
