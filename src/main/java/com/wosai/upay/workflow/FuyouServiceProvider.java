package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.fuyou.*;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.util.FuyouSignature;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.Semaphore;

/***
 * @ClassName: FuyouServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/18 11:46 AM
 */
@ServiceProvicerPriority(priority = 4)
public class FuyouServiceProvider extends AbstractServiceProvider {

    @Resource
    private FuyouClient fuyouClient;

    public static final Logger logger = LoggerFactory.getLogger(FuyouServiceProvider.class);
    public static final String NAME = "provider.fuyou";

    private static final String ALIPAY_FOOD_ORDER_TYPE = "food_order_type";
    private static final int NOTIFY_URL_LIMIT = 128;
    private static final Long THREE_DAYS_BEFORE = 1000 * 60 * 60 * 24 * 3L; //3天
    public static final String UNION_PAYMENT_BANKCARD_CREDIT_SUFFIX = "_CREDIT"; //信用卡

    // 终端密文
    public static final String TERMINAL_SECRET_TEXT = "secret_text";

    // 加密随机数
    public static final String TERMINAL_ENCRYPT_RAND_NUM = "encrypt_rand_num";

    private String notifyHost;

    public FuyouServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        this.concurrencySemaphore = new Semaphore(10); // b2c交易延迟走查询最大并发数为10
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_FUYOU;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        //富友银行卡走的FuyouBankServiceProvider的接口
        return Objects.nonNull(getTradeParams(transaction)) && MapUtil.getIntValue(transaction, Transaction.PAYWAY) != Payway.BANKCARD.getCode();
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.FUYOU_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        //支付授权码
        builder.set(FuyouBusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        //订单金额,单位为分
        builder.set(FuyouBusinessFields.ORDER_AMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        //商品名称
        builder.set(FuyouBusinessFields.GOODS_DES, MapUtil.getString(transaction, Transaction.SUBJECT));
        //交易起始时间 使用订单ctime, 否则在跨日临界点会出现 请求日期是20231220， 但是订单生成日期是20231219 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.set(FuyouBusinessFields.TXN_BEGIN_TS, dateFormat.format(new Date(orderCtime)));
        //交易关闭时间
        builder.set(FuyouBusinessFields.RESERVED_EXPIRE_MINUTE, B2C_TIME_EXPIRE_MINUTE);
        //终端信息
        setTerminalInfo(context.getTransaction(), builder);
        //借贷标识
        limitCredit(builder, transaction);
        if (Order.PAYWAY_WEIXIN == payway) {
            //子商户公众号id
            builder.set(FuyouBusinessFields.RESERVED_SUB_APPID, MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID));
        }
        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder, MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_PAY, 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call fuyou pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);

        if (Order.PAYWAY_UNIONPAY == payway) {
            //由于富友是对接的银联的异步应答模式， 故均处理为需要进一步查询订单的实际状态
            return unionPayB2cTradeProcess(context);
        }

        return buildPayResult(result, context);
    }

    @Override
    public String cancel(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //被扫走撤销， 主扫走关单
        if (Order.SUB_PAYWAY_BARCODE == subPayWay) {
            return b2cCancel(context);
        } else if (Order.SUB_PAYWAY_QRCODE == subPayWay || Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay || Order.SUB_PAYWAY_H5 == subPayWay || Order.SUB_PAYWAY_APP == subPayWay) {
            return c2bCancel(context);
        }

        return Workflow.RC_ERROR;
    }

    private String b2cCancel(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //商户撤销单号
        builder.set(FuyouBusinessFields.CANCEL_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        //富友终端号
        builder.set(FuyouBusinessFields.RESERVED_FY_TERM_ID, terminalInfo.getId());

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_CANCEL, 1, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call fuyou cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(context, result);
    }

    private String c2bCancel(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        if (Order.PAYWAY_WEIXIN == payway) {
            //子商户公众号id
            builder.set(FuyouBusinessFields.SUB_APPID, MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID));
        }

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CLOSE), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_CLOSE, 1, OP_CLOSE);
        } catch (Exception ex) {
            logger.error("failed to call fuyou close", ex);
            setTransactionContextErrorInfo(context, OP_CLOSE, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        return buildCancelResult(context, result);
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_QUERY, 1, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call fuyou query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_ERROR;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(context, result, OP_QUERY);
    }

    @Override
    public String refund(TransactionContext context) {
        return doRefund(context, OP_REFUND);
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        //订单金额,单位为分
        builder.set(FuyouBusinessFields.ORDER_AMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        //商品名称
        builder.set(FuyouBusinessFields.GOODS_DES, MapUtil.getString(transaction, Transaction.SUBJECT));
        //交易起始时间 使用订单ctime, 否则在跨日临界点会出现 请求日期是20231220， 但是订单生成日期是20231219 的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.set(FuyouBusinessFields.TXN_BEGIN_TS, dateFormat.format(new Date(orderCtime)));
        //通知地址
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        builder.set(FuyouBusinessFields.NOTIFY_URL, notifyUrl);
        int subPayWay = MapUtil.getIntValue(context.getOrder(), Order.SUB_PAYWAY);
        //订单类型
        if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            //支付宝服务窗、支付宝小程序
            builder.set(FuyouBusinessFields.TRADE_TYPE, FuyouConstants.TRADE_TYPE_FWC);
        } else if (Order.PAYWAY_WEIXIN == payway) {
            if (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_QRCODE == subPayWay) {
                //公众号支付
                builder.set(FuyouBusinessFields.TRADE_TYPE, FuyouConstants.TRADE_TYPE_JSAPI);
            } else if (Order.SUB_PAYWAY_MINI == subPayWay) {
                //微信小程序
                builder.set(FuyouBusinessFields.TRADE_TYPE, FuyouConstants.TRADE_TYPE_LETPAY);
            }
            String subAppId = MapUtil.getString(tradeParams, TransactionParam.FUYOU_WEIXIN_SUB_APP_ID);
            if (subPayWay == Order.SUB_PAYWAY_MINI) {
                String miniSubAppId = MapUtil.getString(tradeParams, TransactionParam.FUYOU_WEIXIN_MINI_SUB_APP_ID);
                if (!StringUtils.isEmpty(miniSubAppId)) {
                    subAppId = miniSubAppId;
                }
            }
            //子商户公众号id
            builder.set(FuyouBusinessFields.SUB_APPID, subAppId);
        } else if (Order.PAYWAY_UNIONPAY == payway) {
            builder.set(FuyouBusinessFields.TRADE_TYPE, FuyouConstants.TRADE_TYPE_UNIONPAY);
        } else if(Order.PAYWAY_JD == payway) {
            builder.set(FuyouBusinessFields.TRADE_TYPE, FuyouConstants.TRADE_TYPE_JDBT);
        }
        //子商户用户标识
        builder.set(FuyouBusinessFields.SUB_OPENID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        //交易关闭时间
        builder.set(FuyouBusinessFields.RESERVED_EXPIRE_MINUTE, DEFAULT_TIME_EXPIRE_MINUTE);
        //终端信息
        setTerminalInfo(context.getTransaction(), builder);
        //借贷标识
        limitCredit(builder, transaction);
        //解析extended透传给支付通道，包括单品信息、花呗参数、小程序支付上送的sub_appid等
        carryOverExtendedParams(extendedParams, builder, subPayWay);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_PRECREATE, 1, OP_PRECREATE);
        } catch (Exception ex) {
            logger.error("failed to call fuyou precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildPrecreateResult(result, context);
    }

    private void setTerminalInfo(Map<String, Object> transaction, FuyouRequestBuilder builder) {
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        //实时交易终端IP
        builder.set(FuyouBusinessFields.TERM_IP, terminalInfo.getIp());
        //富友终端号
        builder.set(FuyouBusinessFields.RESERVED_FY_TERM_ID, terminalInfo.getId());
        //终端信息
        Map<String, Object> termInfo = new HashMap<>();
        termInfo.put(FuyouBusinessFields.LOCATION, terminalInfo.getFormatLongitude() + "/" + terminalInfo.getFormatLatitude());
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        if(terminalInfo.getSerialNum() != null){
            termInfo.put(FuyouBusinessFields.SERIAL_NUM, terminalInfo.getSerialNum());
        }

        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String secretText = MapUtil.getString(extendedParams, TERMINAL_SECRET_TEXT);
        if (secretText != null) {
            termInfo.put(FuyouBusinessFields.SECRET_TEXT, secretText);
        }

        String encryptRandNum = MapUtil.getString(extendedParams, TERMINAL_ENCRYPT_RAND_NUM);
        if (encryptRandNum != null) {
            termInfo.put(FuyouBusinessFields.ENCRYPT_RAND_NUM, encryptRandNum);
        }

        builder.set(FuyouBusinessFields.RESERVED_TERMINAL_INFO, JSON.toJSONString(termInfo));
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(context.getTransaction());

        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if(Transaction.TYPE_PAYMENT != type){
            return null;
        }

        //密钥
        String publicKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PUBLIC_KEY);
        String reqStr = MapUtil.getString(providerNotification, FuyouProtocolFields.REQ);
        try {
            Map<String, Object> notifyResult = FuyouClient.postProcess(URLDecoder.decode(reqStr, FuyouConstants.CHARSET_GBK));
            String sign = MapUtil.getString(notifyResult, FuyouResponseFields.SIGN);
            boolean validateSign = RsaSignature.validateSign(FuyouSignature.getSignContent(notifyResult, FuyouConstants.METHOD_NOTIFY), sign, RsaSignature.SIG_ALG_NAME_MD5_With_RSA, getPrivateKeyContent(publicKey), "GBK");

            if(context.isFakeRequest() || validateSign){
                boolean asExpected = true; //回调是否符合预期
                String resultCode = BeanUtil.getPropString(notifyResult, FuyouResponseFields.RESULT_CODE);
                if(!FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode)){
                    asExpected = false;
                }
                String sqbOrderNo = BeanUtil.getPropString(notifyResult, FuyouBusinessFields.MCHNT_ORDER_NO);
                if(!BeanUtil.getPropString(transaction, Transaction.ORDER_SN).equals(sqbOrderNo)){
                    asExpected = false;
                }
                long orderAmt = BeanUtil.getPropLong(notifyResult, FuyouBusinessFields.ORDER_AMT);
                if(BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) != orderAmt){
                    asExpected = false;
                }

                if(context.isFakeRequest() || asExpected) {
                    //success
                    setTradeNoBuyerInfoIfExists(notifyResult, context);
                    resolvePayFund(notifyResult, context);
                    return Workflow.RC_PAY_SUCCESS;
                }
            }
        }catch (Exception e){
            logger.error("process notify error ", e);
        }
        //默认还是再查询一遍
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    /**
     * 公共请求参数
     *
     * @param context
     */
    protected FuyouRequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);

        String insCd = MapUtil.getString(tradeParams, TransactionParam.FUYOU_AGENT_NO);
        String mchntCd = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PROVIDER_MCH_ID);

        //接口版本号
        requestBuilder.set(FuyouProtocolFields.VERSION, FuyouConstants.DEFAULT_VERSION);
        //机构号
        requestBuilder.set(FuyouProtocolFields.INS_CD, insCd);
        //商户号,富友分配给二级商户的商户号
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchntCd);
        //终端号
        requestBuilder.set(FuyouBusinessFields.TERM_ID, BeanUtil.getPropString(transaction, Transaction.KEY_TERMINAL_SN));

        return requestBuilder;

    }

    /**
     * 设置借贷标识
     *
     * @param builder
     * @param transaction
     */
    private void limitCredit(FuyouRequestBuilder builder, Map transaction) {
        int subPayWay = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))) {
            if (Order.SUB_PAYWAY_BARCODE == subPayWay) {
                builder.set(FuyouBusinessFields.RESERVED_LIMIT_PAY, FuyouConstants.LIMIT_PAY_NO_CREDIT);
            } else if (Order.SUB_PAYWAY_WAP == subPayWay || Order.SUB_PAYWAY_MINI == subPayWay) {
                builder.set(FuyouBusinessFields.LIMIT_PAY, FuyouConstants.LIMIT_PAY_NO_CREDIT);
            }
        }
    }

    private Map<String, Object> retryIfNetworkException(String serviceUrl, Map<String, Object> request, String privateKey, int method, int times, String opFlag) throws Exception {
        return retryIfNetworkException(() -> fuyouClient.call(serviceUrl, privateKey, request, method), logger, times, opFlag, this.getName());
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<>();
        String resultCode = MapUtil.getString(result, FuyouResponseFields.RESULT_CODE);//业务返回码
        String resultMsg = MapUtil.getString(result, FuyouResponseFields.RESULT_MSG);//业务返回信息
        map.put(BusinessV2Fields.CODE, resultCode);//返回状态码
        map.put(BusinessV2Fields.MSG, resultMsg);//返回信息
        boolean isSuccess = FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode);
        if (OP_PAY.equals(key) || OP_PRECREATE.equals(key) || OP_QUERY.equals(key)) {
            isSuccess = isSuccess || FuyouConstants.RESULT_CODE_USER_PAYING.equals(resultCode);
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, resultCode, resultMsg);
    }

    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        String resultCode = MapUtils.getString(result, FuyouResponseFields.RESULT_CODE);
        setTradeNoBuyerInfoIfExists(result, context);
        //明确成功
        if (Objects.equals(resultCode, FuyouConstants.RESULT_CODE_SUCCESS)) {
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, FuyouResponseFields.RESERVED_TXN_FIN_TS)));
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        //状态未知，需要查询
        if (FuyouConstants.RESULT_CODE_UNKNOWN_SET.contains(resultCode) || FuyouConstants.RESULT_CODE_USER_PAYING.equals(resultCode)) {
            return Workflow.RC_IN_PROG;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context) {
        Map<String,Object> transaction = context.getTransaction();

        int payway = MapUtil.getIntValue(context.getOrder(), Transaction.PAYWAY);

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerId = MapUtil.getString(result, FuyouResponseFields.BUYER_ID);
            if (StringUtils.isEmpty(buyerId)) {
                // 通知结果中的消费者id是user_id
                buyerId = MapUtil.getString(result, FuyouResponseFields.USER_ID);
            }
            if (Order.PAYWAY_UNIONPAY == payway) {
                //云闪付取值reserved_payer_card_no
                buyerId = MapUtil.getString(result, FuyouResponseFields.RESERVED_PAYER_CARD_NO);
            }

            if (!StringUtil.empty(buyerId)) {
                transaction.put(Transaction.BUYER_UID, buyerId);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerLogonId = MapUtil.getString(result, FuyouResponseFields.RESERVED_BUYER_LOGON_ID);
            if (StringUtil.empty(buyerLogonId)) {
                buyerLogonId = MapUtil.getString(result, FuyouResponseFields.RESERVED_OPENID);
            }
            if (!StringUtil.empty(buyerLogonId)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerLogonId);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String fyTraceNo = MapUtil.getString(result, FuyouResponseFields.RESERVED_FY_TRACE_NO);
            if (!StringUtil.empty(fyTraceNo)) {
                transaction.put(Transaction.TRADE_NO, fyTraceNo);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtil.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String channelTransactionId = MapUtil.getString(result, FuyouResponseFields.TRANSACTION_ID);
            if (!StringUtils.isEmpty(channelTransactionId)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelTransactionId);
            }
        }
    }

    private void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
            resolveAlipayPayFund(result, context);
        } else if (Order.PAYWAY_WEIXIN == payway || Order.PAYWAY_UNIONPAY == payway) {
            resolveWxAndUnionpayPayFund(result, context);
        } else if (Order.PAYWAY_JD == payway) {
            resolveJdPayFund(result, context);
        }
    }

    private void resolveJdPayFund(Map<String, Object> result, TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        //交易金额
        long totalAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        //应结算订单金额，以分为单位的整数  如果使用了商户免充值优惠券，该值为订单金额-商户免充值
        long settlementAmt = MapUtil.getLongValue(result, FuyouResponseFields.RESERVED_SETTLEMENT_AMT);
        //接口中并未返回消费者相关信息 以支付金额为支付信息
        long buyerPaidAmount = totalAmount;
        if (MapUtil.getLongValue(transaction, Transaction.PAID_AMOUNT, 0) == 0) {
            transaction.put(Transaction.PAID_AMOUNT, buyerPaidAmount);
        }
        if (MapUtil.getLongValue(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0) {
            transaction.put(Transaction.RECEIVED_AMOUNT, settlementAmt);
        }
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (CollectionUtils.isEmpty(payments)) {
            payments = new ArrayList<>();
        }
        payments.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_AMOUNT, settlementAmt,
                Transaction.PAYMENT_ORIGIN_TYPE, null,
                Transaction.PAYMENT_TYPE, Payment.TYPE_JD_BAITIAO
        ));
        extraOutFields.put(Transaction.PAYMENTS, payments);
    }

    private void resolveAlipayPayFund(Map<String, Object> result, TransactionContext context) {

        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        //交易金额
        long totalAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        //应结算订单金额，以分为单位的整数  如果使用了商户免充值优惠券，该值为订单金额-商户免充值
        long settlementAmt = MapUtil.getLongValue(result, FuyouResponseFields.RESERVED_SETTLEMENT_AMT);
        long discountChannelMchAmount = totalAmount - settlementAmt; //商户在支付宝那边的免充值优惠金额
        long discount = getSumAmountOfVoucherDetail(result);
        //交易金额 - 优惠金额 = 用户实付金额
        long buyerPaidAmount = totalAmount - discount;
        if(MapUtil.getLongValue(transaction, Transaction.PAID_AMOUNT, 0) == 0){
            transaction.put(Transaction.PAID_AMOUNT, buyerPaidAmount);
        }
        if(MapUtil.getLongValue(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0){
            transaction.put(Transaction.RECEIVED_AMOUNT, settlementAmt);
        }
        if(MapUtil.getLongValue(order, Order.TOTAL_DISCOUNT, 0) == 0){
            order.put(Order.TOTAL_DISCOUNT, discount);
            order.put(Order.NET_DISCOUNT, discount);
        }

        if (UpayUtil.isReturnProviderResponse(context.getTransaction())) {
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            String discountGoodsDetail = MapUtil.getString(result, FuyouResponseFields.RESERVED_DISCOUNT_GOODS_DETAIL);
            if(!StringUtils.empty(discountGoodsDetail)) {
                extraOutFields.put(Transaction.GOODS_DETAILS, JSON.parseArray(discountGoodsDetail, List.class));
            }
            List<Map<String, Object>> voucherDetailList = getVoucherDetails(result);
            if (CollectionUtils.isNotEmpty(voucherDetailList)) {
                extraOutFields.put(Transaction.VOUCHER_DETAILS, voucherDetailList);
            }
            if (MapUtil.isNotEmpty(extraOutFields)) {
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
        }

        Object tradeFundBill = BeanUtil.getNestedProperty(result, FuyouResponseFields.RESERVED_FUND_BILL_LIST);
        if(tradeFundBill instanceof String && !StringUtil.empty((String)tradeFundBill)){
            try {
                tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
            } catch (IOException e) {
                logger.warn("parse fundBillList error", e);
            }
        }
        List<Map<String, Object>> tradeFundBills = new ArrayList();
        if (tradeFundBill instanceof List) {
            tradeFundBills.addAll((List<Map<String, Object>>)tradeFundBill);
        }else if (tradeFundBill instanceof Map){
            tradeFundBills.add((Map<String,Object>)tradeFundBill);
        }

        if(tradeFundBills.isEmpty()) {
            return;
        }

        List<Map<String, Object>> payments = AlipayV2ServiceProvider.getAlipayV2Payments(tradeFundBills, totalAmount, discountChannelMchAmount);
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        //设置花呗分期标志
        if (payments != null) {
            for (Map<String, Object> payment : payments) {
                if (Payment.TYPE_CUSTOM_ALIPAY_HUABEI.equals(MapUtil.getString(payment, Transaction.TYPE, ""))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
                    if (extendedParams != null) {
                        Object extendParamsObject = extendedParams.get(BusinessV2Fields.EXTEND_PARAMS);
                        if ((extendParamsObject instanceof Map)) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> extendParams = (Map<String, Object>) extendParamsObject;
                            if (extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM) && extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT)) {
                                BeanUtil.setNestedProperty(transaction, AlipayV2ServiceProvider.TRANSACTION_CHANNEL_HB_FQ_PATH, true);
                            }
                        }
                        break;
                    }
                }
            }
        }

    }

    private List<Map<String, Object>> getVoucherDetails(Map<String,Object> result) {
        String voucherDetailStr = MapUtil.getString(result, FuyouResponseFields.RESERVED_VOUCHER_DETAIL_LIST);
        if (StringUtils.isEmpty(voucherDetailStr)) {
            return null;
        }
        return JSONArray.parseArray(voucherDetailStr).toJavaObject(new TypeReference<List<Map<String, Object>>>(){});
    }

    //计算支付宝的优惠券资金总和
    private long getSumAmountOfVoucherDetail(Map<String, Object> result){
        long sum = 0;

        if (result.containsKey(FuyouResponseFields.RESERVED_VOUCHER_DETAIL_LIST)) {
            List<Map<String,Object>> voucherDetails = getVoucherDetails(result);
            if (CollectionUtils.isNotEmpty(voucherDetails)) {
                for(Map<String,Object> detail: voucherDetails){
                    if(detail.isEmpty()){
                        continue;
                    }

                    long amount = StringUtils.yuan2cents(MapUtil.getString(detail, FuyouResponseFields.RESERVED_VOUCHER_DETAIL_LIST_AMOUNT, "0"));
                    sum = sum + amount;
                }
            }
        }

        return sum;
    }
    private void resolveWxAndUnionpayPayFund(Map<String, Object> result, TransactionContext context) {

        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

        List<Map<String, Object>> promotions = null;
        if (result.get(FuyouResponseFields.RESERVED_PROMOTION_DETAIL) != null && result.get(FuyouResponseFields.RESERVED_PROMOTION_DETAIL) instanceof String) {
            String promotionDetailStr = (String) result.get(FuyouResponseFields.RESERVED_PROMOTION_DETAIL);
            promotionDetailStr = promotionDetailStr.replaceAll("\\\\", "");
            if (!StringUtils.isEmpty(promotionDetailStr)) {
                promotions = JacksonUtil.toBeanQuietly(promotionDetailStr, List.class);
            }
        }
        long totalAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        if (CollectionUtils.isNotEmpty(promotions)) {
            List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

            if(payments == null || payments.isEmpty()){
                extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(transaction, result, promotions));
            }

            long discountAmount = WeixinServiceProvider.getSumAmountOfPromotionDetail(promotions);
            if(MapUtil.getLongValue(order, Order.TOTAL_DISCOUNT, 0) == 0 && discountAmount != 0){
                order.put(Order.TOTAL_DISCOUNT, discountAmount);
                order.put(Order.NET_DISCOUNT, discountAmount);
            }
            long paidAmount = totalAmount - discountAmount;
            if(MapUtil.getLongValue(transaction, Transaction.PAID_AMOUNT) == 0){
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }
            long receiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            for(Map<String,Object> promotion: promotions){
                if(promotion.isEmpty()){
                    continue;
                }
                String type = MapUtil.getString(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_TYPE);
                long amount = MapUtil.getLongValue(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_AMOUNT);
                long wxpayContribute = MapUtil.getLongValue(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                long otherContribute = MapUtil.getLongValue(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                if(WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                    receiveAmount = receiveAmount - (amount - wxpayContribute -otherContribute);

                }
            }
            if(MapUtil.getLongValue(transaction, Transaction.RECEIVED_AMOUNT) == 0 && receiveAmount > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, receiveAmount);
            }
        } else{
            //应结算订单金额，以分为单位的整数  如果使用了商户免充值优惠券，该值为订单金额-商户免充值
            long settlementAmt = MapUtil.getLongValue(result, FuyouResponseFields.RESERVED_SETTLEMENT_AMT);
            long discount = MapUtil.getLongValue(result, FuyouResponseFields.RESERVED_COUPON_FEE, 0);
            long cashFee = totalAmount - discount;
            transaction.put(Transaction.PAID_AMOUNT, cashFee);
            if(settlementAmt > 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, settlementAmt);
            }
            List<Map<String,Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
            if(payments == null || payments.isEmpty()){
                extraOutFields.put(Transaction.PAYMENTS, getWeixinPayments(transaction, result, null));
            }
            if(MapUtil.getLongValue(order, Order.TOTAL_DISCOUNT, 0) == 0 && discount != 0){
                order.put(Order.TOTAL_DISCOUNT, discount);
                order.put(Order.NET_DISCOUNT, discount);
            }
        }
    }

    public static  List<Map<String,Object>> getWeixinPayments(Map<String,Object> transaction, Map<String,Object> result, List<Map<String, Object>> promotions){
        long totalFee = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        if(result.containsKey(FuyouResponseFields.RESERVED_PROMOTION_DETAIL) && CollectionUtils.isNotEmpty(promotions)){
            long cashFee = totalFee - WeixinServiceProvider.getSumAmountOfPromotionDetail(promotions);
            String bankType = MapUtil.getString(result, FuyouResponseFields.RESERVED_BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(bankType, cashFee);
            if(payment != null){
                payments.add(payment);
            }

            if(CollectionUtils.isNotEmpty(promotions)){
                for(Map<String,Object> promotion: promotions){
                    if(promotion.isEmpty()){
                        continue;
                    }
                    String type = MapUtil.getString(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_TYPE);
                    String promotionId = MapUtil.getString(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_PROMOTION_ID);
                    long amount = MapUtil.getLongValue(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_AMOUNT);
                    long wxpayContribute = MapUtil.getLongValue(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_WXPAY_CONTRIBUTE);
                    long otherContribute = MapUtil.getLongValue(promotion, FuyouResponseFields.RESERVED_PROMOTION_DETAIL_OTHER_CONTRIBUTE);
                    long channelAmount = wxpayContribute + otherContribute;
                    long mchAmount = amount - channelAmount;
                    //普通优惠免充值， merchantContribute字段有可能返回为0， 不准确。
                    if(WeixinServiceProvider.PROMOTION_DETAIL_TYPE_DISCOUNT.equals(type)){
                        if(mchAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }else if(channelAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }
                    }else if(WeixinServiceProvider.PROMOTION_DETAIL_TYPE_COUPON.equals(type)){
                        if(mchAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, mchAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }else if(channelAmount > 0){
                            payments.add(
                                    CollectionUtil.hashMap(
                                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                            Transaction.PAYMENT_ORIGIN_TYPE, type,
                                            Transaction.PAYMENT_AMOUNT, channelAmount,
                                            Transaction.PAYMENT_SOURCE, promotionId
                                    )
                            );
                        }

                    }
                }
            }
            return  payments;
        }else{
            long discount = MapUtil.getLongValue(result, FuyouResponseFields.RESERVED_COUPON_FEE, 0);
            long cashFee = totalFee - discount;
            String banktype = MapUtil.getString(result, FuyouResponseFields.RESERVED_BANK_TYPE);
            List<Map<String,Object>> payments = new ArrayList<>();
            Map<String,Object> payment = WeixinServiceProvider.getWeixinPaymentByBanktype(banktype, cashFee);
            if(payment != null){
                payments.add(payment);
            }
            if(discount > 0){
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }
            return payments;
        }

    }

    private String buildCancelResult(TransactionContext context, Map<String, Object> result) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String resultCode = MapUtil.getString(result, FuyouResponseFields.RESULT_CODE);

        if (FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                || FuyouConstants.RESULT_CODE_ORDER_CANCELED.equals(resultCode)
                || FuyouConstants.RESULT_CODE_ORDER_REFUNDED.equals(resultCode)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        String recall = MapUtil.getString(result, FuyouResponseFields.RECALL);
        if (FuyouConstants.RESULT_CODE_UNKNOWN_SET.contains(resultCode) || FuyouConstants.RECALL_YES.equals(recall)) {
            return Workflow.RC_RETRY;
        }

        //只支持当天撤单，历史交易转退款； 主扫交易支付成功之后，撤单须转退款
        if (FuyouConstants.RESULT_CODE_ORDER_PAID.equals(resultCode) || FuyouConstants.RESULT_CODE_C2B_CAN_NOT_CLOSE.equals(resultCode)) {

            String rcFlag = doRefund(context, OP_CANCEL);
            if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
                return Workflow.RC_CANCEL_SUCCESS;
            }else{
                return rcFlag;
            }
        }

        return Workflow.RC_ERROR;
    }

    public String buildQueryResult(TransactionContext context, Map<String, Object> result, String opFlag) {

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String resultCode = MapUtil.getString(result, FuyouResponseFields.RESULT_CODE);

        Map<String,Object> transaction = context.getTransaction();

        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        long currentTime = System.currentTimeMillis();

        //系统错误，需要继续查询
        if (FuyouConstants.RESULT_CODE_SYSTEM_WRONG.equals(resultCode)) {
            return Workflow.RC_IN_PROG;
        }

        //查询返回订单不存在，且原订单交易时间为三天前，走历史交易查询接口
        if (FuyouConstants.RESULT_CODE_TRANSACTION_NOT_EXIST.equals(resultCode) && currentTime - orgOrderTime > THREE_DAYS_BEFORE && OP_QUERY.equals(opFlag)) {
            return doHistoryQuery(context);
        }

        String transStat = MapUtil.getString(result, FuyouResponseFields.TRANS_STAT);
        setTradeNoBuyerInfoIfExists(result, context);
        if (FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
            if (FuyouConstants.TRANS_STAT_SUCCESS.equals(transStat)) {
                //支付成功
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(MapUtils.getString(result, FuyouResponseFields.RESERVED_TXN_FIN_TS)));
                resolvePayFund(result, context);
                return Workflow.RC_PAY_SUCCESS;
            } else if (FuyouConstants.TRANS_STAT_USERPAYING.equals(transStat) || FuyouConstants.TRANS_STAT_NOTPAY.equals(transStat)) {
                //继续查询
                return Workflow.RC_IN_PROG;
            } else if (FuyouConstants.TRANS_STAT_CLOSED.equals(transStat) || FuyouConstants.TRANS_STAT_REVOKED.equals(transStat) || FuyouConstants.TRANS_STAT_REFUND.equals(transStat)) {
                return Workflow.RC_TRADE_CANCELED;
            }
        }

        if (FuyouConstants.TRANS_STAT_PAYERROR.equals(transStat)) {
            return Workflow.RC_TRADE_CANCELED;
        }

        return Workflow.RC_ERROR;
    }

    private String doHistoryQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        //交易日期
        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.set(FuyouBusinessFields.TRADE_DT, DateUtil.formatDate(new Date(orgOrderTime), DateUtil.FORMATTER_DATE_INT));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_HISTORY_QUERY), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_HISTORY_QUERY, 1, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call fuyou query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_ERROR;
        }

        setTransactionContextErrorInfo(result, context, OP_QUERY);

        return buildQueryResult(context, result, ApolloConfigurationCenterUtil.GATEWAY_OP_HISTORY_QUERY);
    }

    private String doRefund(TransactionContext context, String operation) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户订单号
        builder.set(FuyouBusinessFields.MCHNT_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //商户退款单号
        builder.set(FuyouBusinessFields.REFUND_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        //订单类型
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        builder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        //总金额
        builder.set(FuyouBusinessFields.TOTAL_AMT, MapUtil.getLongValue(order, Order.EFFECTIVE_TOTAL));
        //退款金额
        builder.set(FuyouBusinessFields.REFUND_AMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        //富友终端号
        builder.set(FuyouBusinessFields.RESERVED_FY_TERM_ID, terminalInfo.getId());
        //原交易日期(yyyyMMdd)
        long orgOrderTime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        builder.set(FuyouBusinessFields.RESERVED_ORIGI_DT, DateUtil.formatDate(new Date(orgOrderTime), DateUtil.FORMATTER_DATE_INT));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_REFUND, 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call fuyou refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常进行重试
            return Workflow.RC_RETRY;
        }

        setTransactionContextErrorInfo(result, context, operation);

        return buildRefundResult(context, result);
    }

    private String doRefundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        //密钥
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        FuyouRequestBuilder builder = getDefaultRequestBuilder(context);
        //商户退款单号
        builder.set(FuyouBusinessFields.REFUND_ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_QUERY), builder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_REFUND_QUERY, 1, OP_REFUND_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call fuyou refund query", ex);
            setTransactionContextErrorInfo(context, OP_REFUND_QUERY, ex);
            return Workflow.RC_ERROR;
        }

        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);

        return buildRefundQueryResult(context, result);
    }

    private String buildRefundResult(TransactionContext context, Map<String, Object> result) {

        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String resultCode = MapUtil.getString(result, FuyouResponseFields.RESULT_CODE);
        if (FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode)
                || FuyouConstants.RESULT_CODE_ORDER_REFUNDED.equals(resultCode)) {
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            context.getTransaction().put(Transaction.TRADE_NO, MapUtils.getString(result, FuyouResponseFields.REFUND_ID));//渠道退款流水号
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        }
        if (FuyouConstants.RESULT_CODE_UNKNOWN_SET.contains(resultCode)) {
            //状态未知 走退款查询
            return doRefundQuery(context);
        }

        return Workflow.RC_ERROR;
    }

    private String buildRefundQueryResult(TransactionContext context, Map<String, Object> result) {

        if (MapUtil.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        String resultCode = MapUtil.getString(result, FuyouResponseFields.RESULT_CODE);
        String transStat = MapUtil.getString(result, FuyouResponseFields.TRANS_STAT);
        if (FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode) && FuyouConstants.TRANS_STAT_SUCCESS.equals(transStat)) {
            //退款成功
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            context.getTransaction().put(Transaction.TRADE_NO, MapUtil.getString(result, FuyouResponseFields.REFUND_ID));//渠道退款流水号
            resolveRefundFund(result, context);
            return Workflow.RC_REFUND_SUCCESS;
        }

        return Workflow.RC_ERROR;
    }

    private void resolveRefundFund(Map<String, Object> result, TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    private String buildPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //响应码
        String resultCode = MapUtils.getString(result, FuyouResponseFields.RESULT_CODE);
        //明确成功
        if (FuyouConstants.RESULT_CODE_SUCCESS.equals(resultCode)) {
            Map<String, Object> transaction = context.getTransaction();
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if(Order.PAYWAY_WEIXIN == payway) {
                Map<String, Object> payInfo = new HashMap<>();
                payInfo.put(WapFields.APP_ID, MapUtil.getString(result, FuyouResponseFields.SDK_APPID));
                payInfo.put(WapFields.TIME_STAMP, MapUtil.getString(result, FuyouResponseFields.SDK_TIMESTAMP));
                payInfo.put(WapFields.NONCE_STR, MapUtil.getString(result, FuyouResponseFields.SDK_NONCESTR));
                payInfo.put(WapFields.PACKAGE, MapUtil.getString(result, FuyouResponseFields.SDK_PACKAGE));
                payInfo.put(WapFields.SIGN_TYPE, MapUtil.getString(result, FuyouResponseFields.SDK_SIGNTYPE));
                payInfo.put(WapFields.PAY_SIGN, MapUtil.getString(result, FuyouResponseFields.SDK_PAYSIGN));
                payInfo.put(WapFields.PARTNER_ID, MapUtil.getString(result, FuyouResponseFields.SDK_PARTNERID));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, payInfo);
            }else if(Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
                String tradeNo = MapUtil.getString(result, FuyouResponseFields.RESERVED_TRANSACTION_ID);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            }else if(Order.PAYWAY_UNIONPAY == payway){
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(Transaction.REDIRECT_URL, MapUtil.getString(result, FuyouResponseFields.QR_CODE));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            }else if(Order.PAYWAY_JD == payway){
                Map<String, Object> wapRequest = new HashMap<String, Object>();
                wapRequest.put(Transaction.REDIRECT_URL, MapUtil.getString(result, FuyouResponseFields.QR_CODE));
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            }
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;
    }

    private void carryOverExtendedParams(Map<String, Object> extended, FuyouRequestBuilder builder, int subPayway) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            Object value = extendedParam.getValue();
            if (value != null) {

                // 存终端信息时已经添加透传的secret_text 和 encrypt_rand_num,这里跳过处理
                if (TERMINAL_SECRET_TEXT.equals(key) || TERMINAL_ENCRYPT_RAND_NUM.equals(key)) {
                    continue;
                }

                //场景信息
                if (BusinessFields.SCENE_INFO.equals(key)) {
                    if (value instanceof Map || value instanceof List) {
                        builder.set(FuyouBusinessFields.RESERVED_SCENE_INFO, JSON.toJSONString(value));
                    } else {
                        builder.set(FuyouBusinessFields.RESERVED_SCENE_INFO, String.valueOf(value));
                    }
                }
                //微信 子商户公众号id
                if (BusinessFields.SUB_APPID.equals(key)) {
                    if (subPayway == Order.SUB_PAYWAY_BARCODE) {
                        builder.set(FuyouBusinessFields.RESERVED_SUB_APPID, String.valueOf(value));
                    } else {
                        builder.set(FuyouBusinessFields.SUB_APPID, String.valueOf(value));
                    }
                }
                //附加数据  如果需要用到微信点餐数据回传，该字段需要填写OrderSource=FoodOrder
                if (BusinessFields.ATTACH.equals(key)) {
                    builder.set(FuyouBusinessFields.ADDN_INF, String.valueOf(value));
                }
                //设备信息，托传给微信。用于单品券核销
                if (ProtocolFields.DEVICE_INFO.equals(key)) {
                    builder.set(FuyouBusinessFields.RESERVED_DEVICE_INFO, String.valueOf(value));
                }
                //业务信息
                if (BusinessV2Fields.BUSINESS_PARAMS.equals(key)) {
                    if (value instanceof Map || value instanceof List) {
                        builder.set(FuyouBusinessFields.RESERVED_BUSINESS_PARAMS, JSON.toJSONString(value));
                    } else {
                        builder.set(FuyouBusinessFields.RESERVED_BUSINESS_PARAMS, String.valueOf(value));
                    }
                }
                // 间连渠道参数转换
                if(BusinessV2Fields.EXTEND_PARAMS.equals(key) && value instanceof Map) {
                    Map<String, Object> extendParams = (Map)value;
                    Map<String, Object> aliExtendParams = new HashMap<>();
                    if(extendParams.containsKey(ALIPAY_FOOD_ORDER_TYPE)) {
                        // 支付宝扫码点餐转换
                        aliExtendParams.put(FuyouBusinessFields.FOOD_ORDER_TYPE, extendParams.get(ALIPAY_FOOD_ORDER_TYPE));
                    } else if (extendParams.containsKey(TransactionParam.HB_FQ_NUM)) {
                        // 支付宝花呗分期转换
                        aliExtendParams.put(FuyouBusinessFields.HB_FQ_NUM, MapUtil.getString(extendParams, TransactionParam.HB_FQ_NUM));
                        aliExtendParams.put(FuyouBusinessFields.HB_FQ_SELLER_PERCENT, MapUtil.getString(extendParams, TransactionParam.HB_FQ_SELLER_PERCENT, "0"));
                    } else if(extendParams.containsKey(TransactionParam.FQ_NUM)) {
                        // 信用卡分期转换
                        aliExtendParams.put(FuyouBusinessFields.FQ_NUM, MapUtil.getString(extendParams, TransactionParam.FQ_NUM));
                        aliExtendParams.put(FuyouBusinessFields.FQ_SELLER_PERCENT, MapUtil.getString(extendParams, TransactionParam.FQ_SELLER_PERCENT, "0"));
                        aliExtendParams.put(FuyouBusinessFields.FQ_CHANNELS, MapUtil.getString(extendParams, TransactionParam.FQ_CHANNELS, UpayConstant.CREDIT_FQ_CHANNELS));
                    } else if(extendParams.containsKey(TransactionParam.BAITIAO_FQ_NUM)){
                        aliExtendParams.put(FuyouBusinessFields.HB_FQ_NUM, MapUtil.getString(extendParams, TransactionParam.BAITIAO_FQ_NUM));
                    }
                    builder.set(FuyouBusinessFields.RESERVED_ALI_EXTEND_PARAMS, JSON.toJSONString(aliExtendParams));
                }
                //单品优惠功能
                if (UpayConstant.DETAIL.equals(key) && value instanceof Map && ((Map) value).get(UpayConstant.GOODS_DETAIL) != null) {
                    builder.set(FuyouBusinessFields.GOODS_DETAIL, JSON.toJSONString(((Map) value).get(UpayConstant.GOODS_DETAIL)));
                } else if (UpayConstant.GOODS_DETAIL.equals(key) && value instanceof Map) {
                    builder.set(FuyouBusinessFields.GOODS_DETAIL, JSON.toJSONString(value));
                }
            }
        }
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        Map<String, Object> extraParams = com.wosai.mpay.util.MapUtils.getMap(transaction, Transaction.EXTRA_PARAMS);
        if (extraParams == null) {
            return null;
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);

        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        String insCd = MapUtil.getString(tradeParams, TransactionParam.FUYOU_AGENT_NO);
        String mchntCd = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PROVIDER_MCH_ID);
        //接口版本号
        requestBuilder.set(FuyouProtocolFields.VERSION, FuyouConstants.DEFAULT_VERSION);
        //机构号
        requestBuilder.set(FuyouProtocolFields.INS_CD, insCd);
        //商户号,富友分配给二级商户的商户号
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchntCd);
        //终端号
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        //实时交易终端IP
        requestBuilder.set(FuyouBusinessFields.TERM_IP, terminalInfo.getIp());

        String appUpIdentifier = MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER);
        if (!StringUtils.isEmpty(appUpIdentifier)) {
            //如果为空则不传 接口支持
            requestBuilder.set(FuyouBusinessFields.SUB_APPID, appUpIdentifier);
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        String orderType = MapUtil.getString(FuyouConstants.PAYWAY_ORDER_TYPE_MAPPING, payway);
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, orderType);
        requestBuilder.set(FuyouBusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.FUYOU_PRIVATE_KEY);
        Map result;
        try {
            result = retryIfNetworkException(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY), requestBuilder.build(), getPrivateKeyContent(privateKey), FuyouConstants.METHOD_AUTH_2_OPENID, 1, ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call fuyou union.userId.query", ex);
            return null;
        }
        if (result != null) {
            String userId = MapUtil.getString(result, FuyouResponseFields.SUB_OPENID);
            if (!StringUtils.isEmpty(userId)) {
                return CollectionUtil.hashMap(com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID, userId);
            }
        }
        return null;
    }
}
