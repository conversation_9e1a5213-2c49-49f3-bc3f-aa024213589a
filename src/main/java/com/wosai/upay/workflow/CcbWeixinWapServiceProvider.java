package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.ccb.BusinessFields;
import com.wosai.mpay.api.ccb.CcbConstants;
import com.wosai.mpay.api.ccb.RequestBuilder;
import com.wosai.mpay.api.ccb.ResponseFields;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.PaymentUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> Date: 2021/8/25 Time: 3:07 下午
 */
public class CcbWeixinWapServiceProvider extends CcbWapServiceProvider {

    public static final String NAME = "provider.ccb.wap.weixin";
    private static final String SUB_APPID = "sub_appid";
    public static final DateTimeFormatter CCB_WEIXIN_TIMEOUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Objects.isNull(getTradeParams(transaction))) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_WEIXIN
                && (subPayway == Order.SUB_PAYWAY_WAP
                    || subPayway == Order.SUB_PAYWAY_MINI);
    }

    @Override
    protected String buildPrepaidResult(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_OUT_FIELDS);
        String wxPackage = MapUtil.getString(result, ResponseFields.PACKAGE);
        if (StringUtils.isEmpty(wxPackage)) {
            return Workflow.RC_ERROR;
        }
        Map<String, Object> wapRequest = new HashMap<>();
        wapRequest.put(ResponseFields.APP_ID
                , MapUtil.getString(result, ResponseFields.APP_ID));
        wapRequest.put(ResponseFields.TIME_STAMP
                , MapUtil.getString(result, ResponseFields.TIME_STAMP));
        wapRequest.put(ResponseFields.NONCE_STR
                , MapUtil.getString(result, ResponseFields.NONCE_STR));
        wapRequest.put(ResponseFields.PACKAGE, wxPackage);
        wapRequest.put(ResponseFields.SIGN_TYPE
                , MapUtil.getString(result, ResponseFields.SIGN_TYPE));
        wapRequest.put(ResponseFields.PAY_SIGN
                , MapUtil.getString(result, ResponseFields.PAY_SIGN));

        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    protected void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        String payBank = MapUtil.getString(result, ResponseFields.DSTCRD_ISSUBNK_INSNO);
        List<Map<String,Object>> payments = new ArrayList<>();
        long amount = com.wosai.mpay.util.StringUtils.yuan2cents(MapUtil.getString(result, ResponseFields.AHN_TXNAMT));
        Map<String,Object> payment = buildPayments(payBank, amount);
        if (payment != null) {
            payments.add(payment);
        }
        if (CollectionUtils.isNotEmpty(payments)) {
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        }
    }

    private Map<String, Object> buildPayments(String payBank, long amount) {
        if(amount  <= 0 || StringUtil.empty(payBank)){
            return null;
        }else{
            String type = Payment.TYPE_WALLET_WEIXIN;
            if(!payBank.equalsIgnoreCase(WeixinServiceProvider.WEIXIN_PAYMENT_OTHERS)){
                type = Payment.TYPE_BANKCARD;
            }
            return CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, type,
                    Transaction.PAYMENT_ORIGIN_TYPE, payBank,
                    Transaction.PAYMENT_AMOUNT, amount
            );
        }
    }

    @Override
    protected void buildWapParams(RequestBuilder builder, Map<String, Object> transaction) {

        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        if (subPayway == Order.SUB_PAYWAY_WAP) {
            builder.set(BusinessFields.TRADE_TYPE, CcbConstants.TRADE_TYPE_JS);

            String ccbWxSubAppid = MapUtil.getString(tradeParams
                    , TransactionParam.CCB_WX_SUB_APPID);
            builder.set(BusinessFields.SUB_APPID, ccbWxSubAppid);
        }
        if (subPayway == Order.SUB_PAYWAY_MINI) {
            builder.set(BusinessFields.TRADE_TYPE, CcbConstants.TRADE_TYPE_MINI);

            String ccbWxMiniSubAppid = MapUtil.getString(tradeParams
                    , TransactionParam.CCB_WX_MINI_SUB_APPID);
            builder.set(BusinessFields.SUB_APPID, ccbWxMiniSubAppid);
        }
        Map<String, Object> extendedParams = MapUtil.getMap(transaction
                , Transaction.EXTENDED_PARAMS);
        String subAppid = MapUtil.getString(extendedParams, SUB_APPID);
        if (StringUtils.isNotEmpty(subAppid)) {
            builder.set(BusinessFields.SUB_APPID, subAppid);
        }

        builder.set(BusinessFields.TX_CODE, CcbConstants.WX_TX_CODE);

        long payAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String buyerId = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        builder.set(BusinessFields.SUB_OPENID, buyerId);
        builder.set(BusinessFields.PAYMENT, com.wosai.mpay.util.StringUtils.cents2yuan(payAmount));
        builder.set(BusinessFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.CUR_CODE, CcbConstants.CUR_CODE_RMB);
        builder.set(BusinessFields.TIMEOUT, LocalDateTime.now().plusMinutes(DEFAULT_TIME_EXPIRE_MINUTE).format(CCB_WEIXIN_TIMEOUT_FORMATTER));

    }
}
