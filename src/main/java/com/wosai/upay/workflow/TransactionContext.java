package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.fsm.MachineContext;
import com.wosai.fsm.StateLabel;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.FakeRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.function.Consumer;

public class TransactionContext extends MachineContext {
    private static Logger logger = LoggerFactory.getLogger(TransactionContext.class);
    private int apiVer = 2;
    private Workflow workflow;
    private MpayServiceProvider serviceProvider;
    private String oid;
    private String tid;
    private Map<String, Object> order;
    private Map<String, Object> transaction;
    private boolean shouldReturn;
    private boolean forceReturn;

    private Throwable exception;

    private String terminalOrStoreSn;

    private boolean isRCExpire;
    private boolean fakeRequest;


    private Consumer<TransactionContext> returnConsumer;
    private Future timeoutSchedule;
    private boolean isCache;
    

    public TransactionContext(String terminalOrStoreSn,
                              Workflow workflow,
                              MpayServiceProvider serviceProvider,
                              StateLabel currentStateLabel,
                              String oid,
                              Map<String, Object> order,
                              String tid,
                              Map<String, Object> transaction) {

        super(workflow.getMachine(), currentStateLabel);
        this.terminalOrStoreSn = terminalOrStoreSn;
        this.workflow = workflow;
        this.serviceProvider = serviceProvider;
        this.oid = oid;
        this.order = Collections.synchronizedMap(order);
        this.tid = tid;
        this.transaction = Collections.synchronizedMap(transaction);
        this.shouldReturn = false;
        this.forceReturn = false;
        this.fakeRequest = FakeRequestUtil.isFakeRequest();
    }
    
    public TransactionContext(Map<String, Object> order, Map<String, Object> transaction) {
        super(null, null);
        this.order = order;
        this.transaction = transaction;
    }

    public int getApiVer() {
        return apiVer;
    }

    public void setApiVer(int apiVer) {
        this.apiVer = apiVer;
    }

    public Workflow getWorkflow() {
        return workflow;
    }
    
    public String getOid() {
        return oid;
    }
    public String getTid() {
        return tid;
    }
    
    public MpayServiceProvider getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(MpayServiceProvider serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public Map<String, Object> getOrder() {
        return order;
    }
    public Map<String, Object> getTransaction() {
        return transaction;
    }
    
    public void disappear() {
        getWorkflow().getManager().removeTransactionContext(this);
    }
    public boolean isShouldReturn() {
        return shouldReturn;
    }
    public synchronized void setShouldReturn(boolean shouldReturn) {
        this.shouldReturn = shouldReturn;
        if(null != returnConsumer) {
            processReturnConsumer();
        }
        this.notifyAll();
    }
    public boolean isForceReturn() {
        return forceReturn;
    }
    public synchronized void setForceReturn(boolean forceReturn) {
        this.forceReturn = forceReturn;
        if(null != returnConsumer) {
            processReturnConsumer();
        } else {
            this.notifyAll();
        }
    }
    
    public synchronized void waitUntilReturn(long timeoutMillis) {
        long beforeWait = System.currentTimeMillis();
        long remaining = timeoutMillis;
        
        while(!shouldReturn && !forceReturn) {
            try {
                this.wait(remaining);
            }
            catch (InterruptedException e) {
            }

            long elapsed = System.currentTimeMillis() - beforeWait;
            remaining -= elapsed;
            if (remaining <= 0) {
                return;
            }
        }
    }
    
    public synchronized void waitUntilShouldReturn(long timeoutMillis) {
        long beforeWait = System.currentTimeMillis();
        long remaining = timeoutMillis;
        
        while(!shouldReturn) {
            try {
                this.wait(remaining);
            }
            catch (InterruptedException e) {
            }

            long elapsed = System.currentTimeMillis() - beforeWait;
            remaining -= elapsed;
            if (remaining <= 0) {
                return;
            }
        }
    }

    private synchronized void processReturnConsumer(){
        try{
            if(!timeoutSchedule.isDone()) {
                returnConsumer.accept(this);
                timeoutSchedule.cancel(true);
            }
        }catch (Exception e){
           logger.warn("error in processReturnConsumer",e);
        }
    }

    public Throwable getException() {
        return exception;
    }

    public void setException(Throwable exception) {
        this.exception = exception;
    }

    public String getTerminalOrStoreSn() {
        return terminalOrStoreSn;
    }

    public void setTerminalOrStoreSn(String terminalOrStoreSn) {
        this.terminalOrStoreSn = terminalOrStoreSn;
    }

    @Override
    public String toString() {
        String transactionString = toString(transaction, (getCurrentStateLabel().getId() == 0 || getCurrentState() == null || getCurrentState().isEnd()) ? null : Transaction.CONFIG_SNAPSHOT);
        StringBuilder sb = new StringBuilder();
        sb.append("workflow: ").append(workflow.getName());
        sb.append(", provider: ").append(serviceProvider.getName());
        sb.append(", current state: ").append(getCurrentStateLabel());
        sb.append(", tid: ").append(tid);
        sb.append(", transaction: ").append(transactionString);
        return sb.toString();
    }

    private String toString(Map<String,Object> map, String ignoreField){
        if(map == null || map.isEmpty()){
            return "{}";
        }
        StringBuilder sb = new StringBuilder("{");
        Iterator<String> iterator = map.keySet().iterator();
        for (;;) {
            String key = iterator.next();
            Object value = map.get(key);
            if (value instanceof String && (key.contains("key") || (key.contains("Key")) || key.contains("secret") || key.contains("password"))) {
                value = "*";//敏感信息置为*
            }
            if(ignoreField == null || !ignoreField.equals(key)){
                sb.append(key);
                sb.append('=');
                sb.append((value instanceof Map) ?  toString((Map)value, ignoreField) : value);
            }
            if(!iterator.hasNext()){
                return sb.append("}").toString();
            }
            sb.append(",").append(' ');
        }
    }

    public boolean isRCExpire() {
        return isRCExpire;
    }

    public void setRCExpire(boolean isRCExpire) {
        this.isRCExpire = isRCExpire;
    }

    public boolean isFakeRequest() {
        return fakeRequest;
    }

    public void setReturnConsumer(Consumer<TransactionContext> returnConsumer) {
        this.returnConsumer = returnConsumer;
    }

    public void setTimeoutSchedule(Future schedule) {
       this.timeoutSchedule = schedule;
    }

    public boolean isCache() {
        return isCache;
    }

    public void setCache(boolean isCache) {
        this.isCache = isCache;
    }

    public boolean isAlipay() {
        Integer payway = MapUtil.getInteger(order, Order.PAYWAY);
        return Objects.nonNull(payway) && (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2);
    }

    /**
     * 异步同步余额
     *
     * @return
     */
    public boolean asyncWalletLog() {
        return BeanUtil.getPropBoolean(transaction, Transaction.KEY_ASYNC_WALLET_LOG);
    }
}
