package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.unionpayopen.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.mpay.util.Digest;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;

import com.wosai.upay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * Created by wujianwei on 2019/3/6.
 * all sub_payway in this provider
 * 银联开放平台支持微信，支付宝，银联二维码，暂时只支持银联二维码
 */
public class UnionPayOpenServiceProvider extends AbstractServiceProvider{
    public static final Logger logger = LoggerFactory.getLogger(UnionPayOpenServiceProvider.class);

    public static final String NAME = "provider.unionpay.open";

    public static final SafeSimpleDateFormat ORDER_TIME_SDF = new SafeSimpleDateFormat(UnionPayOpenConstants.ORDER_TIME_DATE_TIME_FORMAT);

    @Autowired
    private UnionPayOpenClient client;

    private int retryTimes = 3;
    private String notifyHost;


    public UnionPayOpenServiceProvider(){
        dateFormat = new SafeSimpleDateFormat(UnionPayOpenConstants.PAY_TIME_DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.TXN_AMT, BusinessFields.CURRENCY_CODE));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        return (payWay == Order.PAYWAY_UNIONPAY) ? true : false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_OPEN_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_UNIONPAY_OPEN;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction);
        setTermInfo(builder,context);
        builder.set(BusinessFields.AUTH_CODE, extraParams.get(Transaction.BARCODE));
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.ORDER_TIME, getOrderTime());
        builder.set(BusinessFields.PAYMENT_VALID_TIME, B2C_TIME_EXPIRE_MINUTE + "");
        builder.set(BusinessFields.TXN_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOpenConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.ORDER_INFO, transaction.get(Transaction.SUBJECT));
        handlerCustomizedSwitch(builder, transaction);
        carryOverExtendedParams(extended, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), builder.build(), 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open pay", ex);
            setTransactionContextErrorInfo(context, "pay", ex);
            if (ex instanceof MpayApiReadError) {
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        String responseMsg = BeanUtil.getPropString(result, ResponseFields.RESP_MSG);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        setTradeNoBuyerInfoIfExists(result, context);
        if(UnionPayOpenConstants.RESP_CODE_SUCCESS.equals(responseCode)){
            //success
            resolvePayFund(result, context);
            return Workflow.RC_PAY_SUCCESS;
        }
        if(UnionPayOpenConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)
                || UnionPayOpenConstants.TRADE_ERROR_FAIL_MESSAGE.contains(responseMsg)){
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_BARCODE){
            return revokeWhenCancel(context);
        }else{
            return closeWhenCancel(context);
        }
    }


    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction);
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open query", ex);
            setTransactionContextErrorInfo(context, "query", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        String originResponseCode = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_CODE, "");
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        if(UnionPayOpenConstants.RESP_CODE_SUCCESS.equals(responseCode)){
            switch (originResponseCode){
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_SUCCESS:
                    //success
                    resolvePayFund(result, context);
                    return Workflow.RC_PAY_SUCCESS;
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_REFUND:
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_CLOSED:
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_REVOKED:
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_PAYERROR:
                    //fail
                    return Workflow.RC_TRADE_CANCELED;
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_NOTPAY:
                case UnionPayOpenConstants.QUERY_ORIG_RESP_CODE_USERPAYING:
                    return Workflow.RC_IN_PROG;
                default:
                    return Workflow.RC_ERROR;
            }
        }else if(UnionPayOpenConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)
                && !UnionPayOpenConstants.RESP_CODE_RISK_FAILED.equals(responseCode)
                && !UnionPayOpenConstants.RESP_CODE_FREQUENCY_LIMITED.equals(responseCode)){
                //如果是因为非风控或者限流导致的查单失败，则返回错误
                return Workflow.RC_ERROR;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> order = context.getOrder();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction, true, com.wosai.pantheon.util.MapUtil.getLong(order, DaoConstants.CTIME));
        builder.set(BusinessFields.ORIG_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.set(BusinessFields.ORIG_TXN_AMT, order.get(Order.EFFECTIVE_TOTAL));
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.TSN));
        builder.set(BusinessFields.TXN_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            return Workflow.RC_RETRY;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        switch (responseCode){
            case UnionPayOpenConstants.RESP_CODE_SUCCESS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REFUNDED:
                //success
                resolveRefundFund(result, context);
                return Workflow.RC_REFUND_SUCCESS;
            case UnionPayOpenConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayOpenConstants.RESP_CODE_BUSY_RETRY_LATER:
                return Workflow.RC_RETRY;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_QRCODE == subPayway){
            throw new UnsupportedOperationException("不支持此支付方式");
        }else if(Order.SUB_PAYWAY_MINI == subPayway){
            return miniPrecreate(context, resume);
        }else {
            return unifiedOrder(context, resume);
        }
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        //validate sign
        MapUtil.removeNullValues(providerNotification);
        Map<String,Object> config = getTradeParams(transaction);
        String sign = BeanUtil.getPropString(providerNotification, ResponseFields.SIGNATURE);
        String signContent = RsaSignature.getSignCheckContent(providerNotification, ResponseFields.SIGNATURE);
        String publicKeyRsaKeyId = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_OPEN_PUBLIC_KEY);
        try {
            //全链路压测fake 请求不验签
            boolean fake = context.isFakeRequest();
            if(!fake){
                boolean valid = RsaSignature.validateSign(Digest.sha256(signContent.getBytes(UnionPayOpenConstants.CHARSET_UTF8)), sign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, getPrivateKeyContent(publicKeyRsaKeyId));
                if(!valid){
                    logger.warn("unionpay open validate sign fail");
                    return null;
                }
            }
        } catch (Exception e) {
            logger.error("unionpay open validate sign error", e);
            return null;
        }
        try {
            client.processSomeJsonString(providerNotification);
        } catch (MpayException e) {
            logger.error("unionpay open parse json string error", e);
            return null;
        }
        String tradeNo = BeanUtil.getPropString(providerNotification, ResponseFields.TRANS_INDEX);
        String payAmt = BeanUtil.getPropString(providerNotification, ResponseFields.PAY_AMT);
        String orderNo = BeanUtil.getPropString(providerNotification, ResponseFields.ORDER_NO);
        if(!StringUtil.empty(payAmt) && !StringUtil.empty(tradeNo) && !StringUtil.empty(orderNo)){
            int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
            if(type != Transaction.TYPE_PAYMENT){
                return null;
            }
            //fixme 银联开放平台有时候返回的字段不全, 此处做兼容，待对方接口调整后，去掉此特殊处理
            specialProcess(providerNotification);
            //success
            setTradeNoBuyerInfoIfExists(providerNotification, context);
            resolvePayFund(providerNotification, context);
            clearBizErrorCode(transaction);
            return Workflow.RC_PAY_SUCCESS;
        }
        return null;
    }

    public void specialProcess(Map<String, Object> providerNotification){
        long payAmt = BeanUtil.getPropLong(providerNotification, ResponseFields.PAY_AMT);
        long txnAmt = BeanUtil.getPropLong(providerNotification, ResponseFields.TXN_AMT);
        List<Map<String,Object>> couponInfo = (List<Map<String, Object>>) BeanUtil.getProperty(providerNotification, ResponseFields.COUPON_INFO);
        // 缺少 couponInfo 信息
        if(payAmt != txnAmt && (couponInfo == null || couponInfo.size() == 0)){
            logger.warn("unionpay open notify lack some params");
            //统一当做没有优惠
            providerNotification.put(ResponseFields.PAY_AMT, txnAmt + "");
            providerNotification.put(ResponseFields.SETTLEMENT_AMT, txnAmt + "");
        }
    }


    public String unifiedOrder(TransactionContext context, boolean resume){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction);
        setTermInfo(builder,context);
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UP_JS);
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORDER_TIME, getOrderTime());
        builder.set(BusinessFields.PAYMENT_VALID_TIME, DEFAULT_TIME_EXPIRE_MINUTE);
        builder.set(BusinessFields.TXN_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOpenConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.ORDER_INFO, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.CUSTOMER_IP, UpayUtil.getLocalHostIp());
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if(upayOrderNumber != null){
            builder.set(BusinessFields.BACK_URL, getNotifyUrl(notifyHost, context));
        }
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        handlerCustomizedSwitch(builder,transaction);
        if (payerUid != null) {
            builder.set(BusinessFields.USER_ID, payerUid);
        }
        carryOverExtendedParams(extended, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP), builder.build(), 1, "wap create");
        } catch (Exception ex) {
            logger.error("failed to call unionpay open wap create", ex);
            setTransactionContextErrorInfo(context, "wap create", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if(UnionPayOpenConstants.RESP_CODE_SUCCESS.equals(responseCode)){
            //success
            Map<String,Object> wapRequest = (Map<String, Object>) result.get(ResponseFields.PAY_DATA);
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    /**
     * 云闪付小程序
     * @param context
     * @return
     */
    public String miniPrecreate(TransactionContext context, boolean resume){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction);
        setTermInfo(builder,context);
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORDER_TIME, getOrderTime());
        builder.set(BusinessFields.ORDER_TIME_OUT, DEFAULT_TIME_EXPIRE_MINUTE);
        builder.set(BusinessFields.TXN_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOpenConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.ORDER_INFO, transaction.get(Transaction.SUBJECT));
        builder.set(BusinessFields.CUSTOMER_IP, UpayUtil.getLocalHostIp());
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if(upayOrderNumber != null){
            builder.set(BusinessFields.BACK_URL, getNotifyUrl(notifyHost, context));
        }
        handlerCustomizedSwitch(builder,transaction);
        carryOverExtendedParams(extended, builder);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE), builder.build(), 1, "mini create");
        } catch (Exception ex) {
            logger.error("failed to call unionpay open mini create", ex);
            setTransactionContextErrorInfo(context, "mini create", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if(UnionPayOpenConstants.RESP_CODE_SUCCESS.equals(responseCode)){
            //success
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(
                    //云闪付二维码支付控件 唤起小程序性支付 qrcode 的字段为 scanQrCodeContent
                    "scanQrCodeContent", BeanUtil.getPropString(result, ResponseFields.QR_CODE)
            ));
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;

    }

    public String revokeWhenCancel(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction);
        builder.set(BusinessFields.ORIG_ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.TSN) + "");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), builder.build(), 1, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open cancel", ex);
            setTransactionContextErrorInfo(context, "cancel", ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        switch (responseCode){
            case UnionPayOpenConstants.RESP_CODE_SUCCESS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_NOT_EXISTS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REFUNDED:
            case UnionPayOpenConstants.RESP_CODE_ORDER_CLOSED:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REVOKED:
                //success
                return Workflow.RC_CANCEL_SUCCESS;
            case UnionPayOpenConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayOpenConstants.RESP_CODE_IN_PROG:
            case UnionPayOpenConstants.RESP_CODE_ORDER_UNKNOWN:
            case UnionPayOpenConstants.RESP_CODE_BUSY_RETRY_LATER:
            case UnionPayOpenConstants.RESP_CODE_FREQUENCY_LIMITED:
                //retry
                return Workflow.RC_RETRY;
            case UnionPayOpenConstants.RESP_CODE_ORDER_PAID:
                //paid need refund
                return refundWhenCancel(context);
            default:
                return Workflow.RC_ERROR;

        }
    }

    public String closeWhenCancel(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction);
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE), builder.build(), 1, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open close", ex);
            setTransactionContextErrorInfo(context, "close", ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, ApolloConfigurationCenterUtil.GATEWAY_OP_CLOSE);
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        switch (responseCode){
            case UnionPayOpenConstants.RESP_CODE_SUCCESS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_NOT_EXISTS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REFUNDED:
            case UnionPayOpenConstants.RESP_CODE_ORDER_CLOSED:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REVOKED:
                //success
                return Workflow.RC_CANCEL_SUCCESS;
            case UnionPayOpenConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayOpenConstants.RESP_CODE_IN_PROG:
            case UnionPayOpenConstants.RESP_CODE_ORDER_UNKNOWN:
            case UnionPayOpenConstants.RESP_CODE_BUSY_RETRY_LATER:
            case UnionPayOpenConstants.RESP_CODE_FREQUENCY_LIMITED:
                //retry
                return Workflow.RC_RETRY;
            case UnionPayOpenConstants.RESP_CODE_ORDER_PAID:
                //paid need refund
                return refundWhenCancel(context);
            default:
                return Workflow.RC_ERROR;
        }
    }

    public String refundWhenCancel(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(config, transaction, true, com.wosai.pantheon.util.MapUtil.getLong(order, DaoConstants.CTIME));
        builder.set(BusinessFields.ORIG_ORDER_NO, transaction.get(Transaction.ORDER_SN) + "");
        builder.set(BusinessFields.ORIG_TXN_AMT, order.get(Order.EFFECTIVE_TOTAL) + "");
        builder.set(BusinessFields.ORDER_NO, transaction.get(Transaction.TSN) + "");
        builder.set(BusinessFields.TXN_AMT, order.get(Order.EFFECTIVE_TOTAL) + "");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), builder.build(), 1, OP_REFUND);
        } catch (Exception ex) {
            logger.error("failed to call unionpay open refund", ex);
            setTransactionContextErrorInfo(context, "refund", ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE);
        switch (responseCode){
            case UnionPayOpenConstants.RESP_CODE_SUCCESS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_NOT_EXISTS:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REFUNDED:
            case UnionPayOpenConstants.RESP_CODE_ORDER_CLOSED:
            case UnionPayOpenConstants.RESP_CODE_ORDER_REVOKED:
                //success
                return Workflow.RC_CANCEL_SUCCESS;
            case UnionPayOpenConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayOpenConstants.RESP_CODE_IN_PROG:
            case UnionPayOpenConstants.RESP_CODE_ORDER_UNKNOWN:
            case UnionPayOpenConstants.RESP_CODE_BUSY_RETRY_LATER:
            case UnionPayOpenConstants.RESP_CODE_FREQUENCY_LIMITED:
                //retry
                return Workflow.RC_RETRY;
            default:
                return Workflow.RC_ERROR;
        }
    }



    public Map<String, Object> retryIfNetworkException(Map<String,Object> config, String url, Map<String,Object> request, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                return client.call(url, getPrivateKeyContent((String)config.get(TransactionParam.PRIVATE_KEY)), request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in unionpay open {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }



    public RequestBuilder getDefaultRequestBuilder(Map<String,Object> config, Map<String,Object> transaction){
        return getDefaultRequestBuilder(config, transaction, false, null);
    }

    public RequestBuilder getDefaultRequestBuilder(Map<String,Object> config, Map<String,Object> transaction, boolean isRefund, Long orderCtime){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SER_PROV_ID, config.get(TransactionParam.UNION_PAY_OPEN_SERVICE_PROVIDER_ID));
        builder.set(ProtocolFields.MER_ID, config.get(TransactionParam.UNION_PAY_OPEN_MCH_ID));
        builder.set(ProtocolFields.TERM_ID, config.get(TransactionParam.UNION_PAY_OPEN_TERM_ID));
        String prnInsIdCd = BeanUtil.getPropString(config, TransactionParam.UNION_PAY_OPEN_PNR_INS_ID_CD);
        if (!StringUtils.empty(prnInsIdCd)){
            //银联开放平台云闪付交易上送服务商机构标识 "C1000001"
            builder.set(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
        }
        Map configSnapshot = com.wosai.pantheon.util.MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String extTermId = com.wosai.pantheon.util.MapUtil.getString(configSnapshot, TransactionParam.TRADE_EXT_TERM_ID);
        if(extTermId != null){
            builder.set(ProtocolFields.TERM_ID, extTermId);
        }
        if(isRefund && orderCtime != null){
            // 2023-10-27 13:41:32 上线了云闪付上送真实终端信息， 这之前的交易退款时，终端需要按照 lakala_term_id 上送
            long changeTime = 1698385292000L;
            if(orderCtime < changeTime){
                builder.set(ProtocolFields.TERM_ID, config.get(TransactionParam.UNION_PAY_OPEN_TERM_ID));
            }
        }
        return builder;
    }



    public String getOrderTime(){
        return ORDER_TIME_SDF.format(new Date());
    }

    public void handlerCustomizedSwitch(RequestBuilder builder, Map transaction){
        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALLOW_CREDIT_PAY,TransactionParam.CREDIT_PAY_ENABLE))){
            builder.set(BusinessFields.LIMIT_PAY, UnionPayOpenConstants.LIMIT_PAY_NO_CREDIT);
        }
    }

    public void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if(overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null && !TransactionParam.UNION_PAY_OPEN_MCH_ID.equals(key)) {
                builder.set(key, value);
            }
        }
    }

    public void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        //设置付款人信息
        Map<String,Object> payerInfo = (Map<String, Object>) result.get(ResponseFields.PAYER_INFO);
        String payTime = MapUtils.getString(result, ResponseFields.PAY_TIME);
        if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_UID))) {
            String accNo = MapUtils.getString(payerInfo, ResponseFields.PAYER_INFO_ACC_NO);
            String buyerId = MapUtils.getString(payerInfo, ResponseFields.PAYER_INFO_BUYER_ID);
            if (!StringUtil.empty(accNo) && StringUtil.empty(buyerId)) {
                buyerId = accNo;
            }
            if (!StringUtil.empty(buyerId)) {
                transaction.put(Transaction.BUYER_UID, buyerId);
            }
        }
        if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerAccount = MapUtils.getString(payerInfo, ResponseFields.PAYER_INFO_BUYER_ACCOUNT);
            String name = MapUtils.getString(payerInfo, ResponseFields.PAYER_INFO_NAME);
            //接口返回的名字没有脱敏，此处进行脱敏处理
            if (!StringUtils.empty(name)) {
                name = "*" + name.substring(name.length() - 1);
            }
            if (!StringUtil.empty(name) && StringUtil.empty(buyerAccount)) {
                buyerAccount = name;
            }
            if (!StringUtil.empty(buyerAccount)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerAccount);
            }
        }
        if (!StringUtil.empty(payTime)) {
            if (StringUtil.empty(MapUtils.getString(transaction, Transaction.CHANNEL_FINISH_TIME))) {
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(payTime));
            }
        }
        if (StringUtil.empty(MapUtils.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtils.getString(result, ResponseFields.TRANS_INDEX);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }

    }

    public void resolvePayFund(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        long settlementAmount = BeanUtil.getPropLong(result, ResponseFields.SETTLEMENT_AMT);
        extraOutFields.put(Transaction.SETTLEMENT_AMOUNT, settlementAmount);
        List<Map<String,Object>> payments = new ArrayList<>();
        List<Map<String,Object>>  couponInfo = (List<Map<String, Object>>) result.get(ResponseFields.COUPON_INFO);
        long couponSum = 0;
        if(couponInfo != null){
            for(Map<String,Object> coupon: couponInfo){
                String spnsrId = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_SPNSR_ID); //出资方
                long amount = BeanUtil.getPropLong(coupon, ResponseFields.COUPON_INFO_OFFST_AMT);
                String couponId = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_ID);
                String couponType = BeanUtil.getPropString(coupon, ResponseFields.COUPON_INFO_TYPE);
                couponSum = couponSum + amount;
                //注意银联接口返回不能准确的区分商户优惠是否是免充值，默认当做是免充值的优惠。如果后续此通道对接了微信交易，需要特别注意。
                String paymentType;
                if(UnionPayOpenConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)){
                    paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                }else{
                    paymentType = UnionPayOpenConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                }
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, amount,
                        Transaction.PAYMENT_SOURCE, couponId,
                        Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                        Transaction.PAYMENT_TYPE, paymentType
                ));
            }
        }
        long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if(amount > 0){
            String paymentType = null;
            Map<String,Object> payerInfo = (Map<String, Object>) BeanUtil.getProperty(result, ResponseFields.PAYER_INFO);
            String cardAttr = null;
            if (payerInfo != null) {
                cardAttr = BeanUtil.getPropString(payerInfo, ResponseFields.PAYER_INFO_CARD_ATTR);
                if (!StringUtils.isEmpty(cardAttr)) {
                    if (UnionPayOpenConstants.CARD_ATTR_CREDIT_CARD.equals(cardAttr)) {
                        paymentType = Payment.TYPE_BANKCARD_CREDIT;
                    } else if (UnionPayOpenConstants.CARD_ATTR_DEBIT_CARD.equals(cardAttr)) {
                        paymentType = Payment.TYPE_BANKCARD_DEBIT;
                    } else {
                        paymentType = Payment.TYPE_OTHERS;
                    }
                    payments.add(CollectionUtil.hashMap(
                            Transaction.PAYMENT_AMOUNT, amount,
                            Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                            Transaction.PAYMENT_TYPE, paymentType
                            )
                    );
                }
            }
        }
        List<Map<String,Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if(oldPayments == null || oldPayments.isEmpty()){
            if (payments != null && !payments.isEmpty()) {
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
        }
        if(!StringUtil.empty(BeanUtil.getPropString(result, ResponseFields.PAY_AMT))){
            long payAmt = BeanUtil.getPropLong(result, ResponseFields.PAY_AMT);
            transaction.put(Transaction.PAID_AMOUNT, payAmt);
        }
    }

    public void resolveRefundFund(Map<String, Object> result, TransactionContext context){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String responseCode = BeanUtil.getPropString(result, ResponseFields.RESP_CODE, "");
        String responseMsg = BeanUtil.getPropString(result, ResponseFields.RESP_MSG);
        String originalResponseCode = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_CODE, "");
        String originalResponseMsg = BeanUtil.getPropString(result, ResponseFields.ORIG_RESP_MSG);

        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseFields.RESP_CODE, responseCode);
        map.put(ResponseFields.RESP_MSG, responseMsg);
        map.put(ResponseFields.ORIG_RESP_CODE, originalResponseCode);
        map.put(ResponseFields.ORIG_RESP_MSG, originalResponseMsg);
        String codes = responseCode + originalResponseCode; // 00 或者 0000 为成功
        boolean success = UnionPayOpenConstants.RESP_CODE_SUCCESS.equals(codes) || (UnionPayOpenConstants.RESP_CODE_SUCCESS + UnionPayOpenConstants.RESP_CODE_SUCCESS).equals(codes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, success, StringUtil.empty(originalResponseCode) ? responseCode: originalResponseCode, StringUtil.empty(originalResponseMsg) ? responseMsg : originalResponseMsg);
    }

    @SuppressWarnings("unchecked")
    private void setTermInfo(RequestBuilder builder, TransactionContext context){
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> termInfo = new LinkedHashMap<>();
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
            termInfo.put(BusinessFields.TERM_LATITUDE, dropPlusSign(terminalInfo.getFormatLatitude()));
            termInfo.put(BusinessFields.TERM_LONGITUDE, dropPlusSign(terminalInfo.getFormatLongitude()));
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }
        if (!terminalInfo.isOffset() && terminalInfo.isSendIp()) {
            termInfo.put(BusinessFields.TERM_INFO_IP, terminalInfo.getIp());
        }
        termInfo.put(BusinessFields.TERM_DEVICE_TYPE, terminalInfo.getOrDefaultType(UnionPayOpenConstants.TERM_INFO_TERMINAL_TYPE));
        if(terminalInfo.getSerialNum() != null){
            termInfo.put(BusinessFields.TERM_SERIAL_NUM, terminalInfo.getSerialNum());
        }
        builder.set(BusinessFields.TERM_INFO, JsonUtil.toJsonStr(termInfo));
    }

    private String dropPlusSign(String poi){
        if(poi == null){
            return null;
        }
        return poi.startsWith("+") ? poi.substring(1) : poi;
    }

    public String getNotifyHost() {
        return notifyHost;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }
}
