package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.RequestBuilder;
import com.wosai.mpay.api.weixin.WeixinClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@ServiceProvicerPriority(priority = 1)
public class TLUnionPayWeixinServiceProvider extends UnionPayWeixinServiceProvider{

    protected static final int NOTIFY_URL_LIMIT = 128;

    public static final Logger logger = LoggerFactory.getLogger(TLUnionPayWeixinServiceProvider.class);
    public static final String NAME = "provider.tl.unionpay.weixin";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_TL;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if(getTradeParams(transaction) == null){
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        return payway == Order.PAYWAY_WEIXIN && (subPayway == Order.SUB_PAYWAY_BARCODE || subPayway == Order.SUB_PAYWAY_QRCODE);
    }

    @Override
    public RequestBuilder getDefaultRequestBuilder(TransactionContext context) {
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.ORGID, BeanUtil.getPropString(config, TransactionParam.UNION_PAY_TL_ORGID));
        builder.set(ProtocolFields.CHANNEL_ID, config.get(TransactionParam.UNION_PAY_TL_CHANNEL_ID));
        builder.set(ProtocolFields.SUB_APP_ID, config.get(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_ID));
        builder.set(ProtocolFields.SUB_MCH_ID, config.get(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID));

        setTerminalInfo(context, MapUtil.getMap(context.getTransaction(), Transaction.CONFIG_SNAPSHOT), config, builder);
        return builder;
    }

    @Override
    public Map<String, Object> call(Map<String, Object>  config, String serviceUrl, Map<String, Object> request, String logFlag) throws MpayException, MpayApiNetworkError {
        removeIllegalFields(request);
        return client.call(serviceUrl, WeixinClient.SIGN_TYPE_UNIONPAY, getPrivateKeyContent((String)config.get(TransactionParam.UNION_PAY_TL_PRIVATE_KEY)), null, request);
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

}
