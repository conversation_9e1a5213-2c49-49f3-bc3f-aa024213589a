package com.wosai.upay.workflow;


import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.psbc.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.*;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.Base64;

import static com.wosai.constant.UpayConstant.BARCODE_PAYMENT_SUPPLEMENT_ACCEPTANCE_TERMINAL;

public class PSBCServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(PSBCServiceProvider.class);

    public static final String NAME = "provider.psbc";
    @Autowired
    private PSBCClient psbcClient;

    protected String notifyHost;

    private static SM2Util sm2Util;

    private static SM4Util sm4Util;

    private static String iv;

    static {
        sm2Util = SM2Util.getInstance();
        sm4Util = new SM4Util();
        iv = "UISwD9fW6cFh9SNS";
    }


    private static final SafeSimpleDateFormat dateTimeFormatSimple = new SafeSimpleDateFormat(PSBCConstant.YY_MM_DD_HH_MM_SS);

    public static List<String> QUERY_CODE = Arrays.asList(PSBCConstant.THIRD_PARTY_EXCEPTION, PSBCConstant.TRANSACTION_PROCESSING, PSBCConstant.TRANSACTION_PROCESSING_V2, PSBCConstant.USER_PAYING);

    public PSBCServiceProvider() {
        dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
    }


    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_PSBCBANK_SX;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.PSBC_SX_TRADE_PARAMS);
    }

    @Override
    protected int getNotifyUrlLimit() {
        return 200;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        PSBCRequestBuilder psbcRequestBuilder = buildCommonRequestBuilder(config, PSBCConstant.B2C_METHOD, PSBCConstant.SCANNED_PAY_CODE, tsn);

        Map<String, Object> data = (Map) psbcRequestBuilder.getBody().get(PSBCRequestFields.DATA);

        // 业务字段
        data.put(PSBCRequestFields.MCHT_NO, MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_MCH_ID));
        data.put(PSBCRequestFields.TXN_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        data.put(PSBCRequestFields.CURRENCY_CODE, PSBCConstant.CNY);
        data.put(PSBCRequestFields.QR_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NOTIFY_FLAG);

        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        data.put(PSBCRequestFields.BACK_URL, notifyUrl);

        // 添加订单信息
        Map<String, String> orderData = new HashMap<>();
        orderData.put(PSBCRequestFields.ORDER_FLAG, PSBCConstant.ORDER_FLAG_0);
        orderData.put(PSBCRequestFields.ORDER_TITLE, MapUtil.getString(transaction, Transaction.SUBJECT));
        orderData.put(PSBCRequestFields.ORDER_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        data.put(PSBCRequestFields.ORDER_DATA, orderData);

        // 添加终端信息
        addTerminalInfo(transaction, data);

        carryOverExtendedParams(extendedParams, psbcRequestBuilder.getBody(), null);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getGatewayUrl(config, OP_PAY, (String) psbcRequestBuilder.getBody().get(PSBCRequestFields.BUSI_MAIN_ID)), config, psbcRequestBuilder.build(), FakeConstant.JSON_FORMAT, PSBCConstant.CONTENT_TYPE, logger, 1, OP_PAY, getName());
        } catch (Exception e) {
            logger.error("call pscb pay error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context);
    }


    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, false);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context, OP_QUERY);
        String respCode = MapUtil.getString(result, PSBCResponseFields.RESP_CODE);
        String txnSta = MapUtil.getString(result, PSBCResponseFields.TXN_STA);
        if (Objects.equals(respCode, PSBCConstant.SUCCESS_CODE) && Objects.equals(txnSta, PSBCConstant.SUCCESS)) {
            resolvePayFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        } else if (QUERY_CODE.contains(respCode) || Objects.equals(txnSta, PSBCConstant.PENDING_PAYMENT) || Objects.equals(txnSta, PSBCConstant.PROCESSING)) {
            return Workflow.RC_IN_PROG;
        } else if (Objects.equals(txnSta, PSBCConstant.FAILURE) || Objects.equals(respCode, PSBCConstant.VALIDATION_FAILURE)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, true);
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND_QUERY);
        setTradeNoBuyerInfoIfExists(result, context, OP_REFUND_QUERY);
        String respCode = MapUtil.getString(result, PSBCResponseFields.RESP_CODE);
        String txnSta = MapUtil.getString(result, PSBCResponseFields.TXN_STA);
        if (Objects.equals(respCode, PSBCConstant.SUCCESS_CODE) && Objects.equals(txnSta, PSBCConstant.SUCCESS)) {
            updateTransactionRefundInfo(context, result);
            return Workflow.RC_REFUND_SUCCESS;
        } else if (Objects.equals(respCode, PSBCConstant.SUCCESS_CODE) && (Objects.equals(txnSta, PSBCConstant.FAILURE) || Objects.equals(txnSta, PSBCConstant.VALIDATION_FAILURE))) {
            return Workflow.RC_SYS_ERROR;
        }
        return Workflow.RC_RETRY;
    }

    protected Map<String, Object> doQuery(TransactionContext context, boolean isRefundQuery) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        PSBCRequestBuilder psbcRequestBuilder = new PSBCRequestBuilder();
        String tsn = MapUtil.getString(transaction, Transaction.TSN);
        Map data = new HashMap<>();
        if (isRefundQuery) {
            psbcRequestBuilder = buildCommonRequestBuilder(config, PSBCConstant.REFUND_QUERY_METHOD, PSBCConstant.REFUND_QUERY_CODE, tsn);
            data = (Map) psbcRequestBuilder.getBody().get(PSBCRequestFields.DATA);
            data.put(PSBCRequestFields.TXN_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
            data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NON_NOTIFY_FLAG);
            // 添加终端信息
            addTerminalInfo(transaction, data);
        } else {
            psbcRequestBuilder = buildCommonRequestBuilder(config, PSBCConstant.QUERY_METHOD, PSBCConstant.PAYMENT_QUERY_CODE, tsn);
            data = (Map) psbcRequestBuilder.getBody().get(PSBCRequestFields.DATA);
            data.put(PSBCRequestFields.QUERY_FLAG, PSBCConstant.MERCHANT_QUERY);
        }

        // 业务字段
        data.put(PSBCRequestFields.MCHT_NO, MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_MCH_ID));
        data.put(PSBCRequestFields.ORG_REQ_TRACE_ID, tsn);


        Map<String, Object> result = null;
        try {
            result = retryIfNetworkException(getGatewayUrl(config, OP_QUERY, (String) psbcRequestBuilder.getBody().get(PSBCRequestFields.BUSI_MAIN_ID)), config, psbcRequestBuilder.build(), FakeConstant.JSON_FORMAT, PSBCConstant.CONTENT_TYPE, logger, 1, OP_QUERY, getName());
        } catch (Exception e) {
            logger.error("call pscb query error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
        }
        return result;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return refundQuery(context);
        } else {
            return doRefund(context);
        }
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        PSBCRequestBuilder psbcRequestBuilder = new PSBCRequestBuilder();
        String tsn = MapUtils.getString(transaction, Transaction.TSN);
        psbcRequestBuilder = buildCommonRequestBuilder(config, PSBCConstant.REFUND_METHOD, PSBCConstant.REFUND_CODE, tsn);
        Map data = new HashMap<>();
        data = (Map) psbcRequestBuilder.getBody().get(PSBCRequestFields.DATA);

        // 业务字段
        data.put(PSBCRequestFields.MCHT_NO, MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_MCH_ID));
        data.put(PSBCRequestFields.TXN_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        data.put(PSBCRequestFields.ORG_REQ_TRACE_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NON_NOTIFY_FLAG);

        // 添加终端信息
        addTerminalInfo(transaction, data);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getGatewayUrl(config, OP_REFUND, (String) psbcRequestBuilder.getBody().get(PSBCRequestFields.BUSI_MAIN_ID)), config, psbcRequestBuilder.build(), FakeConstant.JSON_FORMAT, PSBCConstant.CONTENT_TYPE, logger, 1, OP_REFUND, getName());
        } catch (Exception e) {
            logger.error("call pscb refund error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            return Workflow.RC_IOEX;
        } finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String respCode = MapUtil.getString(result, PSBCResponseFields.RESP_CODE);
        if (respCode.equals(PSBCConstant.SUCCESS_CODE)) {
            updateTransactionRefundInfo(context, result);
            return Workflow.RC_REFUND_SUCCESS;
        }
        return Workflow.RC_RETRY;
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        String orderSn = MapUtils.getString(transaction, Transaction.ORDER_SN);
        PSBCRequestBuilder psbcRequestBuilder = new PSBCRequestBuilder();
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            psbcRequestBuilder = buildCommonRequestBuilder(config, PSBCConstant.ALIPAY_PRECREATE_METHOD, PSBCConstant.ALIPAY_PAY_CODE, orderSn);
        } else if (payway == Order.PAYWAY_WEIXIN) {
            psbcRequestBuilder = buildCommonRequestBuilder(config, PSBCConstant.WX_PRECREATE_METHOD, PSBCConstant.WX_PAY_CODE, orderSn);
        }

        Map data = (Map) psbcRequestBuilder.getBody().get(PSBCRequestFields.DATA);

        // 业务字段
        data.put(PSBCRequestFields.MCHT_NO, MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_MCH_ID));
        data.put(PSBCRequestFields.TXN_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        data.put(PSBCRequestFields.CURRENCY_CODE, PSBCConstant.CNY);
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            data.put(PSBCRequestFields.BUYE_ID, payerUid);
            data.put(PSBCRequestFields.SUBJECT, MapUtil.getString(transaction, Transaction.SUBJECT));
            data.put(PSBCRequestFields.BODY, MapUtil.getString(transaction, Transaction.SUBJECT));
        }
        if (payway == Order.PAYWAY_WEIXIN) {
            String subAppId = "";
            if (subPayway == Order.SUB_PAYWAY_MINI) {
                subAppId = MapUtils.getString(config, TransactionParam.PSBC_SX_WECHAT_MINI_SUB_APP_ID);
                if (StringUtils.isEmpty(subAppId)) {
                    subAppId = MapUtils.getString(config, TransactionParam.PSBC_SX_WECHAT_SUB_APP_ID);
                }
            } else {
                subAppId = MapUtils.getString(config, TransactionParam.PSBC_SX_WECHAT_SUB_APP_ID);
            }
            data.put(PSBCRequestFields.SUB_APP_ID, subAppId);
            data.put(PSBCRequestFields.SUB_OPEN_ID_WX, payerUid);
            data.put(PSBCRequestFields.TRADE_TYPE_WX, PSBCConstant.WX_TYPE);
            if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(configSnapshot, TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE))) {
                data.put(PSBCRequestFields.IS_CREDIT, PSBCConstant.IS_CREDIT_1);
            } else {
                data.put(PSBCRequestFields.IS_CREDIT, PSBCConstant.IS_CREDIT_0);
            }
            data.put(PSBCRequestFields.BODY_WX, MapUtil.getString(transaction, Transaction.SUBJECT));
        }
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NOTIFY_FLAG);
        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        data.put(PSBCRequestFields.BACK_URL, notifyUrl);

        // 添加订单信息
        addOrderInfo(transaction, data);

        // 添加终端信息
        addTerminalInfo(transaction, data);

        carryOverExtendedParams(extendedParams, psbcRequestBuilder.getBody(), null);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(getGatewayUrl(config, OP_PRECREATE, (String) psbcRequestBuilder.getBody().get(PSBCRequestFields.BUSI_MAIN_ID)), config, psbcRequestBuilder.build(), FakeConstant.JSON_FORMAT, PSBCConstant.CONTENT_TYPE, logger, 1, OP_PRECREATE, getName());
        } catch (Exception e) {
            logger.error("call pscb precreate error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildPrecreateResult(result, context);
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        logger.info("邮储银行回调通知");
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, Map<String, Object> request, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam : extended.entrySet()) {
            String key = extendedParam.getKey();
            if ((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                if (UpayConstant.EXTENDED_SUB_APPID.equals(key)) {
                    request.put(PSBCRequestFields.APP_ID, value.toString());
                    continue;
                }
                request.put(key, value.toString());

            }
        }
    }

    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> result) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }


    public String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        setTradeNoBuyerInfoIfExists(result, context, OP_PAY);
        String respCode = MapUtil.getString(result, PSBCResponseFields.RESP_CODE);
        if (Objects.equals(respCode, PSBCConstant.SUCCESS_CODE)) {
            resolvePayFund(context, result);
            return Workflow.RC_PAY_SUCCESS;
        } else if (QUERY_CODE.contains(respCode)) {
            return Workflow.RC_IN_PROG;
        } else {
            return Workflow.RC_TRADE_CANCELED;
        }
    }

    public String buildPrecreateResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }
        setTradeNoBuyerInfoIfExists(result, context, OP_PRECREATE);
        String respCode = MapUtil.getString(result, PSBCResponseFields.RESP_CODE);
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (Objects.equals(respCode, PSBCConstant.SUCCESS_CODE)) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
                String alipayData = MapUtil.getString(result, PSBCResponseFields.ALI_PAY_DATA);
                Map alipayDataMap = null;
                if (!StringUtils.isEmpty(alipayData)) {
                    try {
                        alipayDataMap = JsonUtil.jsonStringToObject(alipayData, Map.class);
                    } catch (Exception e) {
                        logger.error("支付宝支付信息转换json失败", e);
                    }
                    String tradeNo = MapUtil.getString(alipayDataMap, PSBCResponseFields.TRADE_NO);
                    if (!StringUtil.empty(tradeNo)) {
                        tradeNo = tradeNo.substring(2);
                    }
                    extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
                }
            } else if (payway == Order.PAYWAY_WEIXIN) {
                String weixinData = MapUtil.getString(result, PSBCResponseFields.WC_PAY_DATA);
                Map weixinDataMap = null;
                if (!StringUtils.isEmpty(weixinData)) {
                    try {
                        weixinDataMap = JsonUtil.jsonStringToObject(weixinData, Map.class);
                    } catch (Exception e) {
                        logger.error("微信支付信息转换json失败", e);
                    }
                }
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, weixinDataMap);
            }
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_TRADE_CANCELED;
    }


    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return;
        }
        Map<String, Object> transaction = context.getTransaction();
        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String orderNo = MapUtil.getString(result, PSBCResponseFields.ORDER_NO);
            transaction.put(Transaction.TRADE_NO, orderNo);
        }
        // 支付宝支付信息
        String payerInfoAlipay = MapUtil.getString(result, PSBCResponseFields.PAYER_INFO_ALIPAY, "");
        Map payerInfoAlipayMap = null;
        if (!StringUtils.isEmpty(payerInfoAlipay)) {
            try {
                payerInfoAlipayMap = JsonUtil.jsonStringToObject(payerInfoAlipay, Map.class);
            } catch (Exception e) {
                logger.error("支付宝支付信息转换json失败", e);
            }
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                String buyerUid = MapUtil.getString(payerInfoAlipayMap, PSBCResponseFields.BUYER_ID);
                if (!StringUtil.empty(buyerUid)) {
                    transaction.put(Transaction.BUYER_UID, buyerUid);
                }
            }
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
                String buyerLogin = MapUtil.getString(payerInfoAlipayMap, PSBCResponseFields.BUYER_LOGON_ID);
                if (!StringUtil.empty(buyerLogin)) {
                    transaction.put(Transaction.BUYER_LOGIN, buyerLogin);
                }
            }
        }

        // 微信支付信息
        String payerInfoWechat = MapUtil.getString(result, PSBCResponseFields.PAYER_INFO_WECHAT, "");
        Map payerInfoWechatMap = null;
        if (!StringUtils.isEmpty(payerInfoWechat)) {
            try {
                payerInfoWechatMap = JsonUtil.jsonStringToObject(payerInfoWechat, Map.class);
            } catch (Exception e) {
                logger.error("微信支付信息转换json失败", e);
            }
            if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
                String orderNo = MapUtil.getString(result, PSBCResponseFields.ORDER_NO);
                transaction.put(Transaction.TRADE_NO, orderNo);
            }
            if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
                String buyerUid = MapUtil.getString(payerInfoWechatMap, PSBCResponseFields.OPEN_ID);
                if (!StringUtil.empty(buyerUid)) {
                    transaction.put(Transaction.BUYER_UID, buyerUid);
                }
            }
        }
    }


    public Map<String, Object> call(String url, Map<String, Object> config, Map<String, Object> request) throws Exception {
        String partnerId = MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_PARTNER_ID);
        String psbcSopPublicKey = MapUtil.getString(config, TransactionParam.PSBC_SX_SOP_PUBLIC_KEY);
        String psbcSqbPrivateKey = MapUtil.getString(config, TransactionParam.PSBC_SX_SQB_PRIVATE_KEY);
        String psbcSqbPublicKey = MapUtil.getString(config, TransactionParam.PSBC_SX_SQB_PUBLIC_KEY);
        String method = MapUtil.getString(config, PSBCRequestFields.METHOD, PSBCConstant.POST);
        return psbcClient.call(request, url, partnerId, psbcSopPublicKey, psbcSqbPublicKey, psbcSqbPrivateKey, method);
    }


    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        String respCode = MapUtil.getString(result, PSBCResponseFields.RESP_CODE);
        String respMsg = MapUtil.getString(result, PSBCResponseFields.RESP_MSG);
        String respCd = MapUtil.getString(result, PSBCResponseFields.RESP_CD);
        String respDesc = MapUtil.getString(result, PSBCResponseFields.RESP_DESC);
        Map<String, Object> map = new LinkedHashMap<>();
        map.put(PSBCResponseFields.RESP_CODE, respCode);
        map.put(PSBCResponseFields.RESP_MSG, respMsg);
        map.put(PSBCResponseFields.RESP_CD, respCd);
        map.put(PSBCResponseFields.RESP_DESC, respDesc);
        boolean isSuccess = false;
        if (Objects.equals(respCode, PSBCConstant.SUCCESS_CODE)) {
            isSuccess = true;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, StringUtils.empty(respCode) ? respCd : respCode, StringUtils.empty(respMsg) ? respDesc : respMsg);
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        long txnAmt = StringUtils.yuan2cents(MapUtils.getString(result, PSBCResponseFields.TXN_AMT));
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();
        if (Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway) {
            // 支付宝支付信息
            String payerInfoAlipay = MapUtil.getString(result, PSBCResponseFields.PAYER_INFO_ALIPAY, "");
            Map payerInfoAlipayMap = null;
            try {
                payerInfoAlipayMap = JsonUtil.jsonStringToObject(payerInfoAlipay, Map.class);
            } catch (Exception e) {
                logger.error("支付宝支付信息转换json失败", e);
            }
            // 用户在交易中支付的金额
            long buyerPayAmount = MapUtil.getLongValue(payerInfoAlipayMap, PSBCResponseFields.BUYER_PAY_AMOUNT);
            // 商户实际收到金额
            long receiptAmount = MapUtil.getLongValue(payerInfoAlipayMap, PSBCResponseFields.RECEIPT_AMOUNT);

            long discount = buyerPayAmount - receiptAmount;
            if (buyerPayAmount > 0 && payments.isEmpty()) {
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, buyerPayAmount,
                        Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_WALLET_ALIPAY,
                        Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY));
            }
            if (discount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

            if (oldPayments == null || oldPayments.isEmpty()) {
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
            if (discount > 0) {
                context.getOrder().put(Order.TOTAL_DISCOUNT, discount);
                context.getOrder().put(Order.NET_DISCOUNT, discount);
            }
            long channelFinishTime = System.currentTimeMillis();
            String gmtPayment = MapUtil.getString(payerInfoAlipayMap, PSBCResponseFields.GMT_PAYMENT);
            try {
                channelFinishTime = dateTimeFormatSimple.parse(gmtPayment).getTime();
            } catch (Exception e) {
                logger.error("支付宝支付时间格式化失败", e);
            }
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);

        } else if (Order.PAYWAY_WEIXIN == payway) {
            // 支付宝支付信息
            String payerInfoWechat = MapUtil.getString(result, PSBCResponseFields.PAYER_INFO_WECHAT, "");
            Map payerInfoWechatMap = null;
            try {
                payerInfoWechatMap = JsonUtil.jsonStringToObject(payerInfoWechat, Map.class);
            } catch (Exception e) {
                logger.error("微信支付信息转换json失败", e);
            }
            long totalFee = MapUtil.getLongValue(payerInfoWechatMap, PSBCResponseFields.TOTAL_FEE);
            long cashFee = MapUtil.getLongValue(payerInfoWechatMap, PSBCResponseFields.CASH_FEE);
            long discount = totalFee - cashFee;

            if (cashFee > 0 && payments.isEmpty()) {
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, cashFee,
                        Transaction.PAYMENT_ORIGIN_TYPE, Payment.TYPE_WALLET_WEIXIN,
                        Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_WEIXIN));
            }
            if (discount > 0) {
                payments.add(
                        CollectionUtil.hashMap(
                                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                                Transaction.PAYMENT_ORIGIN_TYPE, null,
                                Transaction.PAYMENT_AMOUNT, discount
                        )
                );
            }

            long channelFinishTime = System.currentTimeMillis();
            String timeEnd = MapUtil.getString(payerInfoWechatMap, PSBCResponseFields.TIME_END);
            try {
                channelFinishTime = dateFormat.parse(timeEnd).getTime();
            } catch (Exception e) {
                logger.error("微信支付时间格式化失败", e);
            }
            context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, channelFinishTime);
        }
        transaction.put(Transaction.PAID_AMOUNT, txnAmt);
        transaction.put(Transaction.RECEIVED_AMOUNT, txnAmt);
    }

    private void addTerminalInfo(Map<String, Object> transaction, Map<String, Object> data) {
        Map<String, String> terminalData = new HashMap<>();
        TerminalInfo terminal = genTerminalInfo(transaction);
        terminalData.put(PSBCRequestFields.DEVICE_TYPE, BARCODE_PAYMENT_SUPPLEMENT_ACCEPTANCE_TERMINAL);
        terminalData.put(PSBCRequestFields.DEVICE_ID, terminal.getId());
        String terminalInfo = "";
        try {
            terminalInfo = JsonUtil.objectToJsonString(terminalData);
        } catch (Exception e) {
            logger.error("terminalInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.TERM_ID, terminal.getId());
        data.put(PSBCRequestFields.TERM_INFO, terminalInfo);
    }

    private static void addOrderInfo(Map<String, Object> transaction, Map<String, Object> data) {
        Map<String, String> orderData = new HashMap<>();
        orderData.put(PSBCRequestFields.ORDER_FLAG, PSBCConstant.ORDER_FLAG_0);
        orderData.put(PSBCRequestFields.ORDER_TITLE, MapUtil.getString(transaction, Transaction.SUBJECT));
        orderData.put(PSBCRequestFields.ORDER_AMT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        String orderInfo = "";
        try {
            orderInfo = JsonUtil.objectToJsonString(orderData);
        } catch (Exception e) {
            logger.error("orderInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.ORDER_DATA, orderInfo);
    }

    private GatewayUrl getGatewayUrl(Map<String, Object> config, String opFlag, String partnerTxSriNo) {
        String partnerId = MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_PARTNER_ID);
        GatewayUrl gatewayUrl = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), opFlag);
        gatewayUrl.setUrl(gatewayUrl.getUrl() + "/" + partnerId + ".htm?" + PSBCRequestFields.PARTNER_TX_SRI_NO + "=" + partnerTxSriNo);
        return gatewayUrl;
    }

    private PSBCRequestBuilder buildCommonRequestBuilder(Map<String, Object> config, String method, String txnCode, String tsn) {
        Date now = new Date();
        String formatDateTime = dateFormat.format(now);
        //应用appid
        String appId = MapUtil.getString(config, TransactionParam.PSBC_SX_SQB_APP_ID);
        //合作方编号
        String partnerId = MapUtil.getString(config, TransactionParam.PSBC_SX_PROVIDER_PARTNER_ID);
        String date = dateFormat.format(now);
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, partnerId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appId);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, method);

        String platFormId = MapUtil.getString(config, TransactionParam.PSBC_SX_PLATFORM_ID);
        Map<String, Object> data = new HashMap<>();
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platFormId);
        data.put(PSBCRequestFields.REQ_TRACE_ID, tsn);
        data.put(PSBCRequestFields.REQ_DATE, formatDateTime);
        data.put(PSBCRequestFields.REQ_RESERVED, "");
        data.put(PSBCRequestFields.TXN_CODE, txnCode);

        String partnerTxSriNo = SerialNoUtil.getSerialNo();
        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, partnerTxSriNo);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, formatDateTime);
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, partnerTxSriNo);

        return requestBuilder;
    }


    @SneakyThrows
    public String makeNotifyResponseSuccessContent(String request) {
        Map<String, String> psbcDecryptParams = ApolloConfigurationCenterUtil.getPSBCDecryptParams();
        String partnerId = psbcDecryptParams.get(TransactionParam.PSBC_SX_PROVIDER_PARTNER_ID);
        String psbcSopPublicKey = psbcDecryptParams.get(TransactionParam.PSBC_SX_SOP_PUBLIC_KEY);
        String psbcSqbPrivateKey = psbcDecryptParams.get(TransactionParam.PSBC_SX_SQB_PRIVATE_KEY);
        String psbcSqbPublicKey = psbcDecryptParams.get(TransactionParam.PSBC_SX_SQB_PUBLIC_KEY);
        Map<String, Object> notification = JsonUtil.jsonStrToObject(request, Map.class);
        StringBuilder sb = new StringBuilder();
        sb.append(com.wosai.mpay.util.MapUtils.getString(notification, PSBCRequestFields.REQUEST, ""));
        sb.append(com.wosai.mpay.util.MapUtils.getString(notification, PSBCRequestFields.ENCRYPT_KEY, ""));
        sb.append(com.wosai.mpay.util.MapUtils.getString(notification, PSBCRequestFields.ACCESS_TOKEN, ""));
        boolean checked = sm2Util.verifySign(partnerId, psbcSopPublicKey, sb.toString(), SMUtil.fromString(MapUtils.getString(notification, PSBCResponseFields.SIGNATURE, "")));
        logger.info("回调通知验签结果:{}", checked);
        if (!checked) {
            logger.error("回调通知验签结果失败");
            throw new MpayApiNetworkError("响应报文sm4解密失败");
        }

        // 解析密钥
        String respSm4Key = "";
        try {
            respSm4Key = sm2Util.decryptWithSM3Hash(psbcSqbPrivateKey, MapUtils.getString(notification, PSBCRequestFields.ENCRYPT_KEY, ""));
        } catch (Exception e) {
            logger.error("响应报文sm4解密失败", e);
            throw new MpayApiNetworkError("响应报文sm4解密失败");
        }
        // 解析报文
        String respMessage = "";
        try {
            respMessage = new String((SM4Util.decryptCBC(Base64.getMimeDecoder().decode(MapUtils.getString(notification, PSBCRequestFields.REQUEST, "")), respSm4Key.getBytes(), iv.getBytes())), "UTF-8");
        } catch (Exception e) {
            logger.error("回调响应报文解密失败", e);
            throw new MpayApiNetworkError("响应报文解密失败");
        }
        logger.info("回调response:{}", respMessage);

        Map<String, Object> response = null;
        try {
            response = JsonUtil.jsonStringToObject(respMessage, Map.class);
        } catch (Exception e) {
            logger.error("响应报文解析失败", e);
            throw new MpayApiNetworkError("响应报文解析失败");
        }
        logger.info("回调response:{}", response);
        String body = MapUtils.getString(response, PSBCProtocolFields.BODY);
        logger.info("回调body:{}", body);
        Map<String, Object> bodyMap = null;
        try {
            bodyMap = JsonUtil.jsonStringToObject(body, Map.class);
        } catch (Exception e) {
            logger.error("响应报文body转换json失败", e);
            throw new MpayApiNetworkError("响应报文body转换json失败");
        }

        String head = MapUtils.getString(response, PSBCProtocolFields.HEAD);
        logger.info("回调head:{}", head);

        Map<String, Object> headMap = null;
        try {
            headMap = JsonUtil.jsonStringToObject(head, Map.class);
        } catch (Exception e) {
            logger.error("响应报文head转换json失败", e);
            throw new MpayApiNetworkError("响应报文head转换json失败");
        }
        String txnSta = MapUtils.getString(bodyMap, PSBCResponseFields.TXN_STA);
        logger.info("回调txnSta:{}", txnSta);
        if (!Objects.equals(PSBCConstant.SUCCESS, txnSta)) {
            return "回调通知失败";
        }

        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        requestBuilder.setHead(PSBCRequestFields.VERSION, MapUtils.getString(headMap, PSBCRequestFields.VERSION));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, MapUtils.getString(headMap, PSBCRequestFields.PARTNER_TX_SRI_NO));
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, MapUtils.getString(headMap, PSBCRequestFields.MERCHANT_ID));
        requestBuilder.setHead(PSBCRequestFields.APP_ID, MapUtils.getString(headMap, PSBCRequestFields.APP_ID));
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, MapUtils.getString(headMap, PSBCRequestFields.REQ_TIME));
        requestBuilder.setHead(PSBCRequestFields.METHOD, MapUtils.getString(headMap, PSBCRequestFields.METHOD));

        Map<String, Object> data = new HashMap<>();
        data.put(PSBCRequestFields.VERSION, MapUtils.getString(bodyMap, PSBCRequestFields.VERSION));
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.CONSUMPTION_RESULT_NOTIFICATION_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, MapUtils.getString(bodyMap, PSBCRequestFields.PLATFORM_ID));
        data.put(PSBCRequestFields.REQ_TRACE_ID, MapUtils.getString(bodyMap, PSBCRequestFields.REQ_TRACE_ID));
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCResponseFields.RESP_CD, PSBCConstant.SUCCESS_CODE);
        data.put(PSBCResponseFields.RESP_DESC, "通知成功");
        String dataMsg = "";
        try {
            dataMsg = JsonUtil.objectToJsonString(data);
        } catch (Exception e) {
            logger.error("data转换json失败", e);
        }
        requestBuilder.setBody(PSBCRequestFields.DATA, dataMsg);
        requestBuilder.setBody(PSBCResponseFields.RESP_CODE, PSBCConstant.SUCCESS_CODE);
        requestBuilder.setBody(PSBCResponseFields.RESP_DESC, "通知成功");

        String requestData = "";
        try {
            requestData = JsonUtil.objectToJsonString(requestBuilder.build());
        } catch (Exception e) {
            throw new MpayApiNetworkError("请求报文序列化错误");
        }
        logger.info("回调request{}", requestData);
        //1.生成 sm4Key
        String sm4Key = SMUtil.getSM4Key();

        //2.使用 sm4Key 加密请求报文
        String encryptRequest = "";
        try {
            encryptRequest = Base64.getMimeEncoder().encodeToString(SM4Util.encryptCBC(requestData.getBytes("UTF-8"), sm4Key.getBytes(), iv.getBytes()));
        } catch (Exception e) {
            throw new MpayApiNetworkError("SM4加密失败");
        }
        Map<String, String> pscbRequest = new HashMap<>();
        pscbRequest.put(PSBCResponseFields.RESPONSE, encryptRequest);
        String encryptKey = "";
        try {
            encryptKey = sm2Util.encryptWithSM3Hash(psbcSopPublicKey, sm4Key);
        } catch (Exception e) {
            throw new MpayApiNetworkError("SM2加密失败");
        }
        pscbRequest.put(PSBCRequestFields.ENCRYPT_KEY, encryptKey);
        pscbRequest.put(PSBCRequestFields.ACCESS_TOKEN, "");
        //4.签名
        sb.setLength(0);
        sb.append(MapUtils.getString(pscbRequest, PSBCResponseFields.RESPONSE, ""));
        sb.append(MapUtils.getString(pscbRequest, PSBCRequestFields.ENCRYPT_KEY, ""));
        sb.append(MapUtils.getString(pscbRequest, PSBCRequestFields.ACCESS_TOKEN, ""));
        SignatureInfo sign = sm2Util.sign(partnerId, psbcSqbPrivateKey, sb.toString(), psbcSqbPublicKey);
        String signature = SMUtil.toSignStr(sign);
        pscbRequest.put(PSBCRequestFields.SIGNATURE, signature);
        String requestBody = "";
        try {
            requestBody = JsonUtil.objectToJsonString(pscbRequest);
        } catch (Exception e) {
            throw new MpayApiNetworkError("请求报文序列化错误");
        }
        logger.info("回调加密response{}", requestBody);
        return requestBody;
    }


}
