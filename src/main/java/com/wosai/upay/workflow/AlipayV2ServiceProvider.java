package com.wosai.upay.workflow;

import java.io.IOException;
import java.util.*;

import com.wosai.mpay.api.weixin.hkv3.ResponseFields;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.util.*;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.constant.ProductFlagEnum;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.AlipayV2NewClient;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.alipay.RequestV2Builder;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SM2Util;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.QrcodeImaging;

/**
 * Created by wujianwei on 2018/5/29.
 */
public abstract class AlipayV2ServiceProvider extends AbstractServiceProvider{
    private static final Logger logger = LoggerFactory.getLogger(DirectAlipayV2ServiceProvider.class);

    @Autowired
    protected AlipayV2NewClient client;
    @Autowired
    private QrcodeImaging qrcodeImaging;

    protected static final int NOTIFY_URL_LIMIT = 256;

    protected String notifyHost;
    protected int retryTimes = 3;

    protected String defaultTimeoutExpress = DEFAULT_TIME_EXPIRE_MINUTE + "m";//csb 二维码支付,超时时间
    protected String b2cTimeoutExpress = B2C_TIME_EXPIRE_MINUTE + "m";
    protected static final String KEY_STORE_CLIENT_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_CLIENT_SN);
    protected static final String TRANSACTION_CHANNEL_HB_FQ_PATH = Transaction.EXTRA_OUT_FIELDS + "." + TransactionParam.HB_FQ;
    protected static final String KEY_TERMINAL_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_SN);
    protected static final String DEPOSIT_DEFAULT_ENABLE_PAY_CHANNELS = "[{\"payChannelType\":\"PCREDIT_PAY\"},{\"payChannelType\":\"MONEY_FUND\"},{\"payChannelType\":\"CREDITZHIMA\"}]";
    public static final String ADVANCE_AMOUNT_PATH = Transaction.EXTRA_OUT_FIELDS + "." + Transaction.ADVANCE_AMOUNT;

    //fundChannel
    public static final String FC_COUPON = "COUPON";
    public static final String FC_ALIPAYACCOUNT = "ALIPAYACCOUNT";
    public static final String FC_POINT = "POINT";
    public static final String FC_DISCOUNT = "DISCOUNT";
    public static final String FC_PCARD = "PCARD";
    public static final String FC_FINANCEACCOUNT = "FINANCEACCOUNT";
    public static final String FC_MCARD = "MCARD";
    public static final String FC_MDISCOUNT = "MDISCOUNT";
    public static final String FC_MCOUPON = "MCOUPON";
    public static final String FC_PCREDIT = "PCREDIT";
    public static final String FC_BANKCARD = "BANKCARD";
    public static final String FC_TMARKETING = "TMARKETING";

    //fundType
    public static final String FT_DEBIT_CARD = "DEBIT_CARD";
    public static final String FT_CREDIT_CARD = "CREDIT_CARD";
    //免充值优惠列表
    public static List<String> ALIPAY_CH_LISTS = Arrays.asList(Payment.TYPE_HONGBAO_CHANNEL_MCH, Payment.TYPE_DISCOUNT_CHANNEL_MCH);

    public static Set<String> consumerDiscount = CollectionUtil.hashSet(
            FC_DISCOUNT, FC_MDISCOUNT, FC_COUPON, FC_TMARKETING
    );
    @SuppressWarnings("unchecked")
    public static final Map<String,Object> fundChannelPayment = CollectionUtil.hashMap(
            FC_COUPON, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_HONGBAO_CHANNEL,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_COUPON
            ),
            FC_ALIPAYACCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_ALIPAYACCOUNT
            ),
            FC_POINT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CUSTOM_ALIPAY_POINT,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_POINT
            ),
            FC_DISCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_DISCOUNT
            ),
            FC_PCARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CARD_CHANNEL_MCH_PRE,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_PCARD
            ),
            FC_FINANCEACCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY_FINANCE,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_FINANCEACCOUNT
            ),
            FC_MCARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CARD_CHANNEL_MCH_BALANCE,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_MCARD
            ),
            FC_MDISCOUNT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_MDISCOUNT
            ),
            FC_MCOUPON, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_HONGBAO_CHANNEL_MCH,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_MCOUPON
            ),
            FC_PCREDIT, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_CUSTOM_ALIPAY_HUABEI,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_PCREDIT
            ),
            FC_BANKCARD + "." + FT_CREDIT_CARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_BANKCARD_CREDIT,
                    Transaction.PAYMENT_ORIGIN_TYPE, FT_CREDIT_CARD
            ),
            FC_BANKCARD + "." + FT_DEBIT_CARD, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_BANKCARD_DEBIT,
                    Transaction.PAYMENT_ORIGIN_TYPE, FT_DEBIT_CARD
            ),
            FC_TMARKETING, CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                    Transaction.PAYMENT_ORIGIN_TYPE, FC_TMARKETING
            )
    );



    public AlipayV2ServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessV2Fields.TOTAL_AMOUNT, BusinessV2Fields.AMOUNT, BusinessV2Fields.REFUND_AMOUNT));
    }



    protected void carryOverExtendedParams(Map<String, Object> extended, RequestV2Builder builder) {
        boolean formal = builder.getRequest().containsKey(ProtocolV2Fields.APP_AUTH_TOKEN);
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if(!formal){
                //seller_id 只允许直连交易可以透传
                if(TransactionParam.ALIPAY_SELLER_ID.equals(key)){
                    continue;
                }
            }
            if (BusinessV2Fields.EXTEND_PARAMS.equals(key)) {
                Map extendParams = (Map) builder.getBizContent().get(BusinessV2Fields.EXTEND_PARAMS);
                if (extendParams == null) {
                    extendParams = new HashMap<>();
                    builder.bizSet(BusinessV2Fields.EXTEND_PARAMS,extendParams);
                }
                if(value == null) continue;
                if (value instanceof Map) {
                    Map valueMap = (Map) value;
                    valueMap.remove(BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID);
                    extendParams.putAll(valueMap);
                }
            } else if(BusinessV2Fields.BUSINESS_PARAMS.equals(key)){
                Map businessParams = (Map) builder.getBizContent().get(BusinessV2Fields.BUSINESS_PARAMS);
                if (businessParams == null) {
                    businessParams = new HashMap<>();
                    builder.bizSet(BusinessV2Fields.BUSINESS_PARAMS, businessParams);
                }
                if(value instanceof Map){
                    businessParams.putAll((Map) value);
                }
            } else if(BusinessV2Fields.AGREEMENT_PARAMS.equals(key)){
                Map agreementParams = (Map) builder.getBizContent().get(BusinessV2Fields.AGREEMENT_PARAMS);
                if (agreementParams == null) {
                    agreementParams = new HashMap<>();
                    builder.bizSet(BusinessV2Fields.AGREEMENT_PARAMS, agreementParams);
                }
                if(value instanceof Map){
                    agreementParams.putAll((Map) value);
                }
            } else if (value != null) {
                builder.bizSet(key, value);
            }
        }
    }

    public abstract RequestV2Builder getAlipayV2Builder(TransactionContext context);

    public abstract Map<String, Object> call(Map<String, Object>  config, String gatewayUrl, Map<String, String> request) throws MpayException, MpayApiNetworkError;




    @Override
    public String pay(TransactionContext context, boolean resume)  {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_TRADE);


        String sellerId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SELLER_ID);
        if(!StringUtil.empty(sellerId)){
            builder.bizSet(BusinessV2Fields.SELLER_ID, sellerId);
        }
        Map authInfo = new HashMap();
        // 支付宝代扣只支持商家模式，不支持服务商模式，故无须传入auth_token；且代扣是异步扣款，无须传最晚付款时间
        if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(extraParams.get(Transaction.BARCODE_TYPE))) {
            //out_trade_no 使用 代扣交易号 由 业务方上送， 取BARCODE值
            builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, MapUtil.getString(extraParams, Transaction.BARCODE));
            builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_GENERAL_WITHHOLDING);
            //异步扣款
            builder.bizSet(BusinessV2Fields.IS_ASYNC_PAY, true);
            //增加回调
            String notifyUrl = getNotifyUrl(notifyHost, url, context);
            if (notifyUrl != null && notifyUrl.contains(NOTIFY_ALIPAY)) {
                //支付宝代扣回调地址
                notifyUrl = notifyUrl.replace(NOTIFY_ALIPAY, NOTIFY_ALIPAY_DEBIT);
                builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
            }
        } else{
            authInfo = getAlipayV2AppAuthInfo(context, config);
            builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
            builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, (String) transaction.get(Transaction.ORDER_SN));
            builder.bizSet(BusinessV2Fields.TIMEOUT_EXPRESS, b2cTimeoutExpress);
            if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_AUTH.equals(extraParams.get(Transaction.BARCODE_TYPE))) {
                //支付宝预授权
                builder.bizSet(BusinessV2Fields.AUTH_NO, (String) extraParams.get(Transaction.BARCODE));
                builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_PRE_AUTH);
            } else {
                builder.bizSet(BusinessV2Fields.AUTH_CODE, (String) extraParams.get(Transaction.BARCODE));
            }
            if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_FT_OFFLINE.equals(extraParams.get(Transaction.BARCODE_TYPE))) {
                builder.bizSet(BusinessV2Fields.SCENE, AlipayConstants.SCENE_OFFLINE_CODE);
                builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_GENERAL_WITHHOLDING);
            } else {
                builder.bizSet(BusinessV2Fields.SCENE, AlipayConstants.SCENE_BAR_CODE);
            }
        }
        builder.bizSet(BusinessV2Fields.SUBJECT, (String) transaction.get(Transaction.SUBJECT));

        //一些定制化参数，如是否禁用信用卡，是否传商户自己门店号等。
        handlerCustomizedSwitch(builder,transaction, authInfo);

        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.BODY, (String) transaction.get(Transaction.BODY));
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, transaction.get(Transaction.OPERATOR));

        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);
        // 设置拉卡拉万码银行间连参数
        if(TradeConfigService.PROVIDER_LKLWANMA == BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
            builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(BusinessV2Fields.MERCHANT_ID,BeanUtil.getPropString(config, TransactionParam.ALIPAY_PID)));
        }
        setQueryOptions(builder, transaction, OP_PAY);
        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, url, request, 1, OP_PAY);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        //设置顾客、订单号等信息
        setTradeNoBuyerInfoIfExists(result, context);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            //业务处理失败
            String subCode = BeanUtil.getPropString(result, BusinessV2Fields.SUB_CODE);
            if(AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.contains(subCode)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            //业务出现未知错误或者系统异常
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)){
            //业务处理中
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            //付款成功
            transaction.put(Transaction.CHANNEL_FINISH_TIME,  parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.GMT_PAYMENT)));
            resolvePayFund(context.getOrder(), transaction, result);
            setExtraOutField(context, result);
            return Workflow.RC_PAY_SUCCESS;
        }
        return Workflow.RC_ERROR;

    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_CANCEL);

        String barcodeType = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE_TYPE);
        if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
            //代扣交易撤单使用代扣交易号
            builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE));
        } else {
            Map authInfo;
            try{
                authInfo = getAlipayV2AppAuthInfo(context, config);
            }catch (Exception e){
                logger.error("cancel getAlipayV2AppAuthInfo error " + e.getMessage(), e);
                return Workflow.RC_ERROR;
            }
            builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
            builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        }
        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), request, retryTimes, OP_CANCEL);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            return Workflow.RC_RETRY;
        }
        if(result == null){
            return Workflow.RC_RETRY;
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_RETRY;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            if(TradeConfigService.PROVIDER_LKLWANMA == BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
                transaction.put(Transaction.TRADE_NO, result.get(BusinessV2Fields.TRADE_NO));
            }
            int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
            if(subPayway == Order.SUB_PAYWAY_H5 || subPayway == Order.SUB_PAYWAY_APP){
                if(!result.containsKey(BusinessV2Fields.ACTION)){
                    return Workflow.RC_ERROR;
                }
            }
            return Workflow.RC_CANCEL_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }


    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_QUERY);
        String orderSn = com.wosai.pantheon.util.MapUtil.getString(transaction, Transaction.ORDER_SN);
        if(Transaction.TYPE_DEPOSIT_CONSUME == com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.TYPE)) {
            Map<String, Object> extraOutFields = (Map<String, Object>)transaction.get(Transaction.EXTRA_OUT_FIELDS);
            String consumeOrderSn = BeanUtil.getPropString(extraOutFields, Transaction.CONSUME_ORDER_SN);
            if(!StringUtil.empty(consumeOrderSn)) {
                orderSn = consumeOrderSn;
            }
        }
        String barcodeType = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE_TYPE);
        if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
            //代扣交易查询使用代扣交易号
            builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE));
        } else {
            builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, orderSn);
            Map authInfo = getAlipayV2AppAuthInfo(context, config);
            builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        }
        setQueryOptions(builder, transaction, OP_QUERY);
        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), request, retryTimes, OP_QUERY);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            //连接错误，继续轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }

        if(result == null){
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        String errCode = (String)result.get(BusinessV2Fields.SUB_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        //设置顾客、订单号等信息
        setTradeNoBuyerInfoIfExists(result, context);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            if (AlipayConstants.RESULT_CODE_V2_QUERY_TRADE_NOT_EXIST.equals(errCode)){
                // 下单成功后，再次查单返回“ACQ.TRADE_NOT_EXIST”时，需要进行查单动作
                Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction, 
                        UpayUtil.getProviderErrorInfoKey(
                                Order.SUB_PAYWAY_BARCODE == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) ? MpayServiceProvider.OP_PAY : MpayServiceProvider.OP_PRECREATE));
                if(null == payResultMap 
                        || AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(BeanUtil.getPropString(payResultMap, BusinessV2Fields.CODE))
                        || AlipayConstants.V2_RETURN_CODE_INPROG.equals(BeanUtil.getPropString(payResultMap, BusinessV2Fields.CODE))){
                    return Workflow.RC_IN_PROG;
                }else{
                    return Workflow.RC_ERROR;
                }
            }
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            String tradeStatus = (String) result.get(BusinessV2Fields.TRADE_STATUS);
            if(AlipayConstants.TRADE_STATUS_WAIT_BUYER_PAY.equals(tradeStatus)){
                return Workflow.RC_IN_PROG;
            }else if(AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus)){
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.SEND_PAY_DATE)));
                int type = com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.TYPE);
                if(type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_DEPOSIT_CONSUME){
                    resolvePayFund(context.getOrder(), transaction, result);
                    setExtraOutField(context, result);
                }
                return Workflow.RC_PAY_SUCCESS;
            }
            // 预授权完成失败查单设置下外部订单号
            if(Transaction.TYPE_DEPOSIT_CONSUME == com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.TYPE)) {
                String tradeNo = BeanUtil.getPropString(result, BusinessV2Fields.TRADE_NO);
                if(!StringUtil.empty(tradeNo)){
                    context.getTransaction().put(Transaction.TRADE_NO, tradeNo);
                }
            }
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_REFUND);

        String outTradeNo = (String) transaction.get(Transaction.ORDER_SN);
        String barcodeType = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE_TYPE);
        if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
            //代扣交易退款使用代扣交易号
            outTradeNo = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE);
        } else {
            Map authInfo;
            try{
                authInfo = getAlipayV2AppAuthInfo(context, config);
            }catch (Exception e){
                logger.error("refund getAlipayV2AppAuthInfo error " + e.getMessage(), e);
                //当获取token失败，记录下错误信息，明确是退款失败，以便下次能继续进行做退款
                BeanUtil.setNestedProperty(context.getTransaction(), UpayUtil.getBizErrorCodeKey(MpayServiceProvider.OP_REFUND), UpayBizError.REFUND_FAILED);
                return Workflow.RC_ERROR;
            }
            builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
            boolean isRefundNonSqbOrder = BeanUtil.getPropBoolean(transaction.get(Transaction.EXTRA_OUT_FIELDS), Transaction.REFUND_NON_SQB_ORDER);
            if(!isRefundNonSqbOrder) {
                builder.bizSet(BusinessV2Fields.TRADE_NO, transaction.get(Transaction.TRADE_NO));
                Map extraOutFields = (Map)transaction.get(Transaction.EXTRA_OUT_FIELDS);
                if(null != extraOutFields && extraOutFields.containsKey(Transaction.CONSUME_ORDER_SN)) {
                    outTradeNo = BeanUtil.getPropString(extraOutFields, Transaction.CONSUME_ORDER_SN);
                }
            } else if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
                //代扣交易退款使用代扣交易号
                outTradeNo = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE);
            } else {
                outTradeNo = (String)context.getOrder().get(Transaction.TRADE_NO);
            }
        }
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, outTradeNo);
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, transaction.get(Transaction.TSN));
        builder.bizSet(BusinessV2Fields.REFUND_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        carryOverExtendedParams(extended, builder);
        setQueryOptions(builder, transaction, OP_REFUND);
        Map<String,String> request = null;
        Map<String,Object> result = null;

        try {
            request = builder.build();
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), request, retryTimes, OP_REFUND);
            if (context.getApiVer() == 1) {
                transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (Exception ex) {
            logger.error("failed to call alipayV2 refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常重试
            return Workflow.RC_RETRY;
        }

        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            //服务不可用  重试
            return Workflow.RC_RETRY;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            transaction.put(Transaction.BUYER_UID, getBuyerUid(result));
            transaction.put(Transaction.TRADE_NO, result.get(BusinessV2Fields.TRADE_NO));
            transaction.put(Transaction.BUYER_LOGIN,result.get(BusinessV2Fields.BUYER_LOGON_ID));
            transaction.put(Transaction.CHANNEL_FINISH_TIME,  parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.GMT_REFUND_PAY)));
            resolveRefundFund(context.getOrder(), context.getTransaction(), result);
            return Workflow.RC_REFUND_SUCCESS;

        }else{
            return Workflow.RC_ERROR;
        }
    }


    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }

        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_PRECREATE);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.SUBJECT, transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.BODY, transaction.get(Transaction.BODY));
        builder.bizSet(BusinessV2Fields.QR_CODE_TIMEOUT_EXPRESS, defaultTimeoutExpress);
        builder.bizSet(BusinessV2Fields.TIMEOUT_EXPRESS, defaultTimeoutExpress);
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,transaction.get(Transaction.OPERATOR));
        String sellerId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SELLER_ID);
        if(!StringUtil.empty(sellerId)){
            builder.bizSet(BusinessV2Fields.SELLER_ID, sellerId);
        }
        handlerCustomizedSwitch(builder,transaction, authInfo);//一些定制化参数，如是否禁用信用卡，是否传商户自己门店号等。
        carryOverExtendedParams(extended, builder);
        // 设置拉卡拉万码渠道银行间连参数
        if(TradeConfigService.PROVIDER_LKLWANMA == BeanUtil.getPropInt(transaction, Transaction.PROVIDER)){
            builder.bizSet(BusinessV2Fields.SUB_MERCHANT, CollectionUtil.hashMap(BusinessV2Fields.MERCHANT_ID,BeanUtil.getPropString(config, TransactionParam.ALIPAY_PID)));
        }
        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, url, request, 1, OP_PRECREATE);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if(result == null){
            return Workflow.RC_IOEX;
        }

        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if (context.getApiVer() == 1) {
            String qrcode = BeanUtil.getPropString(result, BusinessV2Fields.QR_CODE, "");
            transaction.put(Transaction.PROVIDER_RESPONSE, CollectionUtil.hashMap(PayResponse.RESPONSE, CollectionUtil.hashMap(
                    PayResponse.ALIPAY,CollectionUtil.hashMap(
                            PayResponse.QR_CODE, qrcode,
                            PayResponse.PIC_URL, qrcodeImaging.getQrcodeImageUrl(qrcode)
                    )
            )));
        }
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_SYS_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            //预下单成功
            Map<String,Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.QRCODE, result.get(BusinessV2Fields.QR_CODE));
            return Workflow.RC_CREATE_SUCCESS;

        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_DEPOSIT_CONSUME){
            String tradeStatus = BeanUtil.getPropString(providerNotification, BusinessV2Fields.TRADE_STATUS);
            boolean paySuccess = (AlipayConstants.TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || AlipayConstants.TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus));
            if(paySuccess){
                boolean isValidatePass = context.isFakeRequest();
                if(!isValidatePass) {
                    //是否跳过out_trade_no校验, 由于代扣支付时上送的out_trade_no是代扣单号，并不是收钱吧的订单号，故跳过
                    boolean skipOutTradeNoCheck = false;
                    String outTradeNo = BeanUtil.getPropString(providerNotification, BusinessV2Fields.OUT_TRADE_NO);
                    String barcodeType = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.BARCODE_TYPE);
                    if (UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
                        //支付宝代扣上送的是支付宝返回的代扣单号
                        skipOutTradeNoCheck = true;
                    }
                    String totalAmount = BeanUtil.getPropString(providerNotification, BusinessV2Fields.TOTAL_AMOUNT, "0");
                    long totalAmountLong = StringUtils.yuan2cents(totalAmount);
                    if((skipOutTradeNoCheck || BeanUtil.getPropString(transaction, Transaction.TSN).equals(outTradeNo))
                            && totalAmountLong == BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)) {
                        Map<String, String> request = new HashMap<String, String>();
                        for (String key : providerNotification.keySet()) {
                            request.put(key, String.valueOf(providerNotification.get(key)));
                        }
                        Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
                        String certId = null;
                        if(Objects.equals(provider, Order.PROVIDER_DIRECT_UNIONPAY)) {
                            certId = MapUtil.getString(request, TransactionParam.UNION_PAY_DIRECT_CERT_ID);
                            request.remove(TransactionParam.UNION_PAY_DIRECT_CERT_ID);
                        }
                        if(Objects.equals(provider, Order.PROVIDER_LAKALA_UNION_PAY)) {
                            certId = MapUtil.getString(request, TransactionParam.LAKALA_UNION_PAY_CERT_ID);
                            request.remove(TransactionParam.LAKALA_UNION_PAY_CERT_ID);
                        }
                        try {
                            String publicKey = BeanUtil.getPropString(getTradeParams(context.getTransaction()), TransactionParam.PUBLIC_KEY);
                            if(!StringUtils.isEmpty(publicKey)) {
                                if(context.getServiceProvider() instanceof TLUnionPayAlipayV2ServiceProvider 
                                        || context.getServiceProvider() instanceof TLUnionPayAlipayV2WapServiceProvider) {
                                    isValidatePass = AlipaySignature.rsaCheckV2(request, serviceFacade.getRsaKeyDataById(publicKey), AlipayConstants.CHARSET_UTF8);
                                }else {
                                    String signType = MapUtil.getString(request, ProtocolV2Fields.SIGN_TYPE);
                                    if(AlipayConstants.SIGN_TYPE_SM2.equals(signType)) {
                                        String sign = MapUtils.getString(request, ProtocolV2Fields.SIGN);
                                        String content = AlipaySignature.getSignCheckContentV1(request);
                                        isValidatePass = SM2Util.getInstance().unionpayVerifySign(content, sign, serviceFacade.getRsaKeyDataById(publicKey), certId, AlipayConstants.CHARSET_UTF8);
                                    }else {
                                        isValidatePass = AlipaySignature.rsaCheckV1(request, serviceFacade.getRsaKeyDataById(publicKey), AlipayConstants.CHARSET_UTF8);
                                    }
                                }
                            }
                        }catch (Exception e) {
                            logger.error("validate sign error", e);
                        }
                    }
                }

                if(isValidatePass) {
                    String tradeNo = getRealTradeNo(BeanUtil.getPropInt(transaction, Transaction.PROVIDER), providerNotification);
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(providerNotification, BusinessV2Fields.GMT_PAYMENT)));
                    resolvePayFund(context.getOrder(), transaction, providerNotification);
                    if(type == Transaction.TYPE_PAYMENT) {
                        transaction.put(Transaction.BUYER_UID, getBuyerUid(providerNotification));
                        transaction.put(Transaction.TRADE_NO, tradeNo);
                        if(StringUtil.empty(BeanUtil.getPropString(context.getOrder(), Order.TRADE_NO))){
                            context.getOrder().put(Order.TRADE_NO, tradeNo);
                        }
                        transaction.put(Transaction.BUYER_LOGIN,providerNotification.get(BusinessV2Fields.BUYER_LOGON_ID));
                        setExtraOutField(context, providerNotification);
                        return Workflow.RC_PAY_SUCCESS;
                    }else {
                        transaction.put(Transaction.TRADE_NO, tradeNo);
                        return Workflow.RC_CONSUME_SUCCESS;
                    }
                }

                if (type == Transaction.TYPE_PAYMENT) {
                    return Workflow.RC_PAY_SUCCESS.equals(query(context))? Workflow.RC_PAY_SUCCESS :null;
                } else {
                    return Workflow.RC_CONSUME_SUCCESS.equals(depositQuery(context))? Workflow.RC_CONSUME_SUCCESS :null;
                }
            }
        }else if(type == Transaction.TYPE_DEPOSIT_FREEZE) {
            boolean freezeSuccess = AlipayConstants.STATUS_SUCCESS.equals(BeanUtil.getPropString(providerNotification, BusinessV2Fields.STATUS));
            if(freezeSuccess){
                boolean isValidatePass = context.isFakeRequest();
                if(!isValidatePass) {
                    String outTradeNo = BeanUtil.getPropString(providerNotification, BusinessV2Fields.OUT_ORDER_NO);
                    String totalAmount = BeanUtil.getPropString(providerNotification, BusinessV2Fields.AMOUNT, "0");
                    long totalAmountLong = StringUtils.yuan2cents(totalAmount);
                    if(BeanUtil.getPropString(transaction, Transaction.TSN).equals(outTradeNo)
                            && totalAmountLong == BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT)) {
                        Map<String, String> request = new HashMap<String, String>();
                        for (String key : providerNotification.keySet()) {
                            request.put(key, String.valueOf(providerNotification.get(key)));
                        }
                        try {
                            String publicKey = BeanUtil.getPropString(getTradeParams(context.getTransaction()), TransactionParam.PUBLIC_KEY);
                            if(!StringUtils.isEmpty(publicKey)) {
                                String signType = MapUtil.getString(request, ProtocolV2Fields.SIGN_TYPE);
                                if(AlipayConstants.SIGN_TYPE_SM2.equals(signType)) {
                                    String sign = MapUtils.getString(request, ProtocolV2Fields.SIGN);
                                    Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
                                    String certId = null;
                                    if(Objects.equals(provider, Order.PROVIDER_DIRECT_UNIONPAY)) {
                                        certId = MapUtil.getString(request, TransactionParam.UNION_PAY_DIRECT_CERT_ID);
                                        request.remove(TransactionParam.UNION_PAY_DIRECT_CERT_ID);
                                    }
                                    if(Objects.equals(provider, Order.PROVIDER_LAKALA_UNION_PAY)) {
                                        certId = MapUtil.getString(request, TransactionParam.LAKALA_UNION_PAY_CERT_ID);
                                        request.remove(TransactionParam.LAKALA_UNION_PAY_CERT_ID);
                                    }
                                    String content = AlipaySignature.getSignCheckContentV1(request);
                                    isValidatePass = SM2Util.getInstance().unionpayVerifySign(content, sign, serviceFacade.getRsaKeyDataById(publicKey), certId, AlipayConstants.CHARSET_UTF8);
                                }else {
                                    isValidatePass = AlipaySignature.rsaCheckV1(request, serviceFacade.getRsaKeyDataById(publicKey), AlipayConstants.CHARSET_UTF8);
                                }
                            }
                        }catch (Exception e) {
                            logger.error("validate sign error", e);
                        }
                    }
                }

                if(isValidatePass) {
                    transaction.put(Transaction.BUYER_UID, providerNotification.get(BusinessV2Fields.PAYER_USER_ID));
                    transaction.put(Transaction.TRADE_NO, providerNotification.get(BusinessV2Fields.AUTH_NO));
                    if(StringUtil.empty(BeanUtil.getPropString(context.getOrder(), Order.TRADE_NO))){
                        context.getOrder().put(Order.TRADE_NO, providerNotification.get(BusinessV2Fields.AUTH_NO));
                    }
                    transaction.put(Transaction.BUYER_LOGIN, providerNotification.get(BusinessV2Fields.PAYER_LOGON_ID));
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(providerNotification, BusinessV2Fields.GMT_TRANS)));
                    return Workflow.RC_PAY_SUCCESS;
                }

                return Workflow.RC_PAY_SUCCESS.equals(depositQuery(context))?Workflow.RC_PAY_SUCCESS:null;
            }
        }
        return null;
    }



    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        String returnMsg = (String)result.get(BusinessV2Fields.MSG);//返回信息
        String errCode = (String)result.get(BusinessV2Fields.SUB_CODE); //错误代码
        String errCodeDes = (String)result.get(BusinessV2Fields.SUB_MSG);//错误代码描述
        map.put(BusinessV2Fields.CODE, returnCode);//返回状态码
        map.put(BusinessV2Fields.MSG, returnMsg);//返回信息
        map.put(BusinessV2Fields.SUB_CODE, errCode);//错误代码
        map.put(BusinessV2Fields.SUB_MSG, errCodeDes);//错误代码描述
        setTransactionContextErrorInfo(context.getTransaction(), key, map, AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode), StringUtil.empty(errCode) ? returnCode : errCode, StringUtil.empty(errCodeDes) ? returnMsg : errCodeDes);
    }

    public void resolveRefundFund(Map<String, Object> order, Map<String, Object> transaction, Map<String, Object> result){
        Map<String,Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(order, DaoConstants.CTIME));
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)){
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款，根据返回的refund_detail_item_list来计算
            Object tradeFundBill = BeanUtil.getNestedProperty(result,BusinessV2Fields.REFUND_DETAIL_ITEM_LIST);
            if(tradeFundBill instanceof String && !StringUtil.empty((String)tradeFundBill)){
                try {
                    tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                    tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
                } catch (IOException e) {
                    logger.warn("parse refund_detail_item_list error", e);
                }
            }
            List<Map<String, Object>> tradeFundBills = new ArrayList();
            if (tradeFundBill instanceof List) {
                tradeFundBills.addAll((List<Map<String, Object>>)tradeFundBill);
            }else if (tradeFundBill instanceof Map){
                tradeFundBills.add((Map<String,Object>)tradeFundBill);
            }
            long refundAmount = com.wosai.pantheon.util.MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            long receivedAmount = 0L;
            long discountChannelMchAmount = 0L;
            long paidAmount = 0L;
            if(result.containsKey(BusinessV2Fields.SEND_BACK_FEE)) {
                receivedAmount = StringUtils.yuan2cents(com.wosai.pantheon.util.MapUtil.getString(result, BusinessV2Fields.SEND_BACK_FEE));

                discountChannelMchAmount = refundAmount - receivedAmount;
            	
                for (Map<String, Object> bill: tradeFundBills ) {
                    String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL)?BusinessV2Fields.FUND_CHANNEL:BusinessV2Fields.FUNDCHANNEL);
                    long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                    if(!consumerDiscount.contains(fundChannel)){
                        paidAmount += amount;
                    }
                }
                // 花呗分期超过15天之后的特殊处理
                int provider = MapUtil.getIntValue(payTransaction, Transaction.PROVIDER);
                if(provider == Order.PROVIDER_DIRECT_UNIONPAY || provider == Order.PROVIDER_LAKALA_UNION_PAY) {
                    String productFlag = Optional.ofNullable(com.wosai.pantheon.util.MapUtil.getString(payTransaction, Transaction.PRODUCT_FLAG)).orElse("");
                    if(productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode())) {
                        int day = DateTimeUtil.getDaysBetween(com.wosai.pantheon.util.MapUtil.getLongValue(payTransaction, DaoConstants.CTIME), System.currentTimeMillis());
                        long hbfqFee = 0l;
                        if(day > 15) {
                            Map<String, Object> profitSharing = (Map<String, Object>) BeanUtil.getNestedProperty(transaction, Transaction.KEY_PROFIT_SHARING);
                            List<Map<String, Object>> receivers = (List<Map<String, Object>>) profitSharing.get(ProfitSharing.RECEIVERS);
                            Optional<Map<String, Object>> hbfqReceiver = receivers.stream().filter(receive -> ProductFlagEnum.HBFQ_DISCOUNT.getCode().equals(com.wosai.pantheon.util.MapUtil.getString(receive, Transaction.PRODUCT_FLAG))).findFirst();
                            if(hbfqReceiver.isPresent()) {
                                // 撤单由于不调用分账服务接口，所以需要将分账接收方回退状态置为不回退
                                if(com.wosai.pantheon.util.MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_CANCEL) {
                                    hbfqReceiver.get().put(ProfitSharing.SHARING_FLAG, ProfitSharing.SHARING_FLAG_DISABLE);
                                }
                                hbfqFee = com.wosai.pantheon.util.MapUtil.getLongValue(hbfqReceiver.get(), ProfitSharing.RECEIVER_SHARING_AMOUNT);
                            }
                            // 支付宝超过15天后，花呗手续费不退，而且需要从商户余额中扣除这部分
                            if(receivedAmount + hbfqFee <= refundAmount) {
                                logger.warn("hbfq order {} refund exceed 15 day, add hbfq fee {}", com.wosai.pantheon.util.MapUtil.getString(payTransaction, Transaction.ORDER_SN), hbfqFee);
                                receivedAmount += hbfqFee;
                                discountChannelMchAmount -= hbfqFee;
                            }
                        }
                    }
                }
            }else {
                logger.debug("alipay response send_back_fee not exits");
                for (Map<String, Object> bill: tradeFundBills ) {
                    String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL)?BusinessV2Fields.FUND_CHANNEL:BusinessV2Fields.FUNDCHANNEL);
                    long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                    if(!consumerDiscount.contains(fundChannel)){
                        paidAmount += amount;
                    }
                    if(!FC_MDISCOUNT.equals(fundChannel) && !FC_MCOUPON.equals(fundChannel) && !FC_TMARKETING.equals(fundChannel)){
                        receivedAmount += amount;
                    }else {
                        discountChannelMchAmount += amount;
                    }
                }
            }
            if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0){
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }
            if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0){
                transaction.put(Transaction.RECEIVED_AMOUNT, receivedAmount);
            }
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, getAlipayV2Payments(tradeFundBills, refundAmount, discountChannelMchAmount));
        }
    }

    /**
     * 解析处理用户支付金额信息
     * @param order
     * @param transaction
     * @param result
     */
    public static  void resolvePayFund(Map<String, Object> order, Map<String, Object> transaction, Map<String, Object> result) {
        Object tradeFundBill = BeanUtil.getNestedProperty(result,BusinessV2Fields.FUND_BILL_LIST);
        if(tradeFundBill instanceof String && !StringUtil.empty((String)tradeFundBill)){
            try {
                tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
            } catch (IOException e) {
                logger.warn("parse fundBillList error", e);
            }
        }
        List<Map<String, Object>> tradeFundBills = new ArrayList();
        if (tradeFundBill instanceof List) {
            tradeFundBills.addAll((List<Map<String, Object>>)tradeFundBill);
        }else if (tradeFundBill instanceof Map){
            tradeFundBills.add((Map<String,Object>)tradeFundBill);
        }
        
        // 预授权冻结时，不会返回fund_bill_list和buyer_pay_amount 信息，不需要计算金额
        if(tradeFundBills.isEmpty() && !result.containsKey(BusinessV2Fields.BUYER_PAY_AMOUNT)) {
            return;
        }
        long totalAmount = com.wosai.pantheon.util.MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long buyerPaidAmount = result.containsKey(BusinessV2Fields.BUYER_PAY_AMOUNT) ? StringUtils.yuan2cents(com.wosai.pantheon.util.MapUtil.getString(result, BusinessV2Fields.BUYER_PAY_AMOUNT)) : totalAmount;
        long receiptAmount = result.containsKey(BusinessV2Fields.RECEIPT_AMOUNT) ? StringUtils.yuan2cents(com.wosai.pantheon.util.MapUtil.getString(result, BusinessV2Fields.RECEIPT_AMOUNT)) : totalAmount;
        if(receiptAmount > totalAmount){
            //容错
            receiptAmount = totalAmount;
        }
        long discount = totalAmount - buyerPaidAmount;
        long discountChannelMchAmount = totalAmount - receiptAmount; //商户在支付宝那边的免充值优惠金额
        if(BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0){
            transaction.put(Transaction.PAID_AMOUNT, buyerPaidAmount);
        }
        if(BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0){
            transaction.put(Transaction.RECEIVED_AMOUNT, receiptAmount);
        }
        if(BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT, 0) == 0l){
            order.put(Order.TOTAL_DISCOUNT, discount);
            order.put(Order.NET_DISCOUNT, discount);
        }
        List<Map<String,Object>> payments = getAlipayV2Payments(tradeFundBills, totalAmount, discountChannelMchAmount);
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
        //设置花呗分期标志
        if (payments != null) {
            for (Map<String,Object> payment:payments){
                if(Payment.TYPE_CUSTOM_ALIPAY_HUABEI.equals(BeanUtil.getPropString(payment,Transaction.TYPE,""))){
                    @SuppressWarnings("unchecked")
                    Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
                    if (extendedParams != null) {
                        Object extendParamsObject = extendedParams.get(BusinessV2Fields.EXTEND_PARAMS);
                        if((extendParamsObject instanceof Map)){
                            @SuppressWarnings("unchecked")
                            Map<String,Object> extendParams = (Map<String, Object>) extendParamsObject;
                            if(extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM)&& extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT)){
                                BeanUtil.setNestedProperty(transaction, TRANSACTION_CHANNEL_HB_FQ_PATH, true);
                            }
                        }
                        break;
                    }
                }
            }
        }
        if(UpayUtil.isReturnProviderResponse(transaction)) {
            Map<String, Object> extraOutFields = com.wosai.pantheon.util.MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap<String, Object>());
            String discountGoodsDetail = com.wosai.pantheon.util.MapUtil.getString(result, BusinessV2Fields.DISCOUNT_GOODS_DETAIL);
            if(!StringUtils.empty(discountGoodsDetail)) {
                extraOutFields.put(Transaction.GOODS_DETAILS, JsonUtil.jsonStrToObject(discountGoodsDetail, List.class));
            }
            extraOutFields.put(Transaction.VOUCHER_DETAILS, result.get(BusinessV2Fields.VOUCHER_DETAIL_LIST));
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
    }

    protected Map<String,Object> retryIfNetworkException(Map<String, Object>  config, String gatewayUrl, Map<String, String> request, int retryTimes, String logFlag) throws Exception{
        return retryIfNetworkException(()->call(config, gatewayUrl, request), logger, retryTimes, logFlag, "alipayV2");
    }


    /**
     * 取得授权token
     * 如果是试用商户，那么直接返回null
     * 否则如果config里面有app_auth_token并且订单是在10分钟之内创建的，那么直接取config里面的相应信息
     * @param context
     * @param config
     */
    public Map getAlipayV2AppAuthInfo(TransactionContext context, Map config){
        String authAppId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_AUTH_APP_ID);
        String appAuthToken = BeanUtil.getPropString(config, TransactionParam.APP_AUTH_TOKEN);
        String authStoreId = BeanUtil.getPropString(config, TransactionParam.APP_AUTH_STORE_ID);
        String authShopId = BeanUtil.getPropString(config, TransactionParam.APP_AUTH_SHOP_ID);
        String storeId = (String) context.getOrder().get(Order.STORE_ID);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(config, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        if(liquidationNextDay){
            return null;
        }else{
            int subPayway = BeanUtil.getPropInt(context.getTransaction(), Transaction.SUB_PAYWAY);
            if(Order.SUB_PAYWAY_H5 == subPayway || Order.SUB_PAYWAY_APP == subPayway){
                return new HashMap();
            }
            if(StringUtil.empty(appAuthToken) && StringUtil.empty(authAppId)){
                throw new UpayBizException("支付宝2.0交易参数错误, 授权token与授权appId不能同时为空");
            }
            if(!StringUtil.empty(appAuthToken)){
                return CollectionUtil.hashMap(
                        TransactionParam.APP_AUTH_TOKEN, appAuthToken,
                        TransactionParam.APP_AUTH_STORE_ID, authStoreId,
                        TransactionParam.APP_AUTH_SHOP_ID, authShopId);
            } else {
                logger.warn("交易参数中不含授权参数，重新获取");
                return serviceFacade.getAlipayV2AppAuthInfo(authAppId, storeId);
            }
        }
    }

    protected void handlerCustomizedSwitch(RequestV2Builder builder, Map transaction, Map authInfo){
        Map config = getTradeParams(transaction);
        Object configSnapshot = BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extraParams = (Map<String, Object>) BeanUtil.getProperty(transaction, Transaction.EXTRA_PARAMS);

        boolean limitPayer = BeanUtil.getPropBoolean(configSnapshot, TransactionParam.LIMIT_PAYER);
        if (extraParams.containsKey(Transaction.SQB_LIMIT_PAYER_ADULT)) {
            limitPayer = BeanUtil.getPropBoolean(extraParams, Transaction.SQB_LIMIT_PAYER_ADULT);
        }
        if (limitPayer) {
            //限制未成年人交易
            builder.bizSet(BusinessV2Fields.EXT_USER_INFO, CollectionUtil.hashMap(BusinessV2Fields.NEED_CHECK_INFO, AlipayConstants.TRUE, BusinessV2Fields.MIN_AGE, 18));
        }

        int subPayWay = MapUtils.getIntValue(transaction, Transaction.SUB_PAYWAY);
        //h5 或 app支付时判断是否支持定制化参数
        if ((Order.SUB_PAYWAY_H5 == subPayWay || Order.SUB_PAYWAY_APP == subPayWay) && !ApolloConfigurationCenterUtil.getH5OrAppHandleCustomizedParamsEnable()) {
            return;
        }

        if (TransactionParam.CREDIT_PAY_DISABLE.equals(BeanUtil.getPropString(BeanUtil.getProperty(transaction,Transaction.CONFIG_SNAPSHOT),TransactionParam.ALLOW_CREDIT_PAY,TransactionParam.CREDIT_PAY_ENABLE))){
            builder.bizSet(BusinessV2Fields.DISABLE_PAY_CHANNELS, AlipayConstants.NO_CREDIT);
        }

        boolean formal = UpayUtil.isFormalByTradeParams(config);
        String configAlipayStoreId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_STORE_ID);
        String appAuthShopId = BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_SHOP_ID);
        // 设置支付宝门店号
        String alipayStoreId = StringUtils.isEmpty(configAlipayStoreId) ? appAuthShopId : configAlipayStoreId;
        // 设置收钱吧商户门店号 优先级 直连支付宝门店绑定的store_id -> 配置在门店的直连支付宝门店号client_sn -> 收钱吧门店id
        String storeId = BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_STORE_ID, BeanUtil.getPropString(transaction, KEY_STORE_CLIENT_SN,  BeanUtil.getPropString(transaction, Transaction.STORE_ID)));
        // 改写 storeId, 去掉"-" 符号
        storeId = storeId.replaceAll("-", "");

        //下面的代码是为了解决store_id与支付宝门店id不匹配的问题, 在某些情况下不需要上送store_id的值
        if(formal){
            // 直连如果配置不需要上送的开关，则不上送
            if(!BeanUtil.getPropBoolean(configSnapshot, TransactionParam.IS_SENT_STORE_ID, true)){
                storeId = null;
            }
        }else{
            //间连如果有alipay_store_id值了，那么则不上送store_id值
            if(!StringUtils.isEmpty(alipayStoreId)){
                storeId = null;
            }
        }
        if(!StringUtils.isEmpty(alipayStoreId)){
            builder.bizSet(BusinessV2Fields.ALIPAY_STORE_ID, alipayStoreId);
        }
        if(!StringUtils.isEmpty(storeId)){
            builder.bizSet(BusinessV2Fields.STORE_ID, storeId);
        }
        //设置高校id参数 新的值格式为 eduSchoolId:eduSchoolName
        String eduSchoolIdAndName = MapUtil.getString(config, TransactionParam.ALIPAY_EDU_SCHOOL_ID);
        if(!StringUtils.isEmpty(eduSchoolIdAndName)){
            String[] arr = eduSchoolIdAndName.split(":");
            String eduSchoolId = arr[0];
            String eduSchoolName = arr.length == 2 ? arr[1] : null;
            builder.bizSet(BusinessV2Fields.BUSINESS_PARAMS, MapUtil.hashMap(
                    BusinessV2Fields.EDU_SCHOOL_ID, eduSchoolId,
                    BusinessV2Fields.EDU_SCENE, AlipayConstants.EDU_SCENE_SCHOOL_CANTEEN
            ));

            String appid = MapUtil.getString((Map) configSnapshot, TransactionParam.TERMINAL_VENDOR_APP_APPID);
            String subject = MapUtil.getString(transaction, BusinessV2Fields.SUBJECT);

            if (ApolloConfigurationCenterUtil.getHaiMaVendorAppId().contains(appid) && !Objects.isNull(subject) && subject.startsWith(eduSchoolName + "_")) {
                builder.bizSet(BusinessV2Fields.SUBJECT, subject);

            } else {
                if (eduSchoolName != null) {
                    builder.bizSet(BusinessV2Fields.SUBJECT, eduSchoolName + "_食堂");
                }
            }

        }
    }


    /**
     *  获取资金明细
     * @param fundBillList 支付宝资金明细
     * @param totalAmount 上送支付源的金额
     * @param channelMchNoTopUpAmount 商户支付源免充值优惠
     * @return
     */
    public static List<Map<String,Object>> getAlipayV2Payments(List<Map<String,Object>> fundBillList, long totalAmount, long channelMchNoTopUpAmount){
        List<Map<String,Object>> payments = new ArrayList<>();
        for(Map<String,Object> bill: fundBillList){
            String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL) ? BusinessV2Fields.FUND_CHANNEL : BusinessV2Fields.FUNDCHANNEL);
            String fundType = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_TYPE) ? BusinessV2Fields.FUND_TYPE : BusinessV2Fields.FUNDTYPE);
            long  amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
            amount = Math.abs(amount);
            Map payment = (Map) fundChannelPayment.get(fundChannel);
            if(payment == null){
                payment = (Map) fundChannelPayment.get(fundChannel + "." + fundType);
            }
            if(payment == null){
                payment = CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                        Transaction.PAYMENT_ORIGIN_TYPE, fundChannel
                );
            }else{
                payment = (Map)((HashMap)payment).clone();
            }
            payment.put(Transaction.PAYMENT_AMOUNT, amount);
            payments.add(payment);
        }
        payments = fixPayments(payments, totalAmount, channelMchNoTopUpAmount);
        return payments;
    }

    /**
     * 修正payments资金明细里面的payment_type，保证商户免充值金额与明细里面的保持一致
     * 支付宝 DISCOUNT 可能为商户免充值优惠，MDISCOUNT，MCOUPON 可能为商户预充值优惠, 以及存在其他一些状况。
     * @param payments 支付明细
     * @param totalAmount 上送给支付宝的金额
     * @param channelMchNoTopUpAmount 支付源商户免充值金额
     * @return
     */
    private static List<Map<String,Object>> fixPayments(List<Map<String,Object>> payments, long totalAmount, long channelMchNoTopUpAmount){
        if(getChannelMchNoTopUpAmountSum(payments) == channelMchNoTopUpAmount){
            return payments;
        }
        //有差异，修正

        //无疑问的商户免充值优惠金额
        long noDoubtAmount = 0l;
        //有疑问的payment的index
        List<Integer> doubtedPaymentIndexList = new ArrayList<>();
        List<String> originalTypes = Arrays.asList(FC_DISCOUNT, FC_MDISCOUNT, FC_COUPON);
        for (int i = 0; i < payments.size(); i++) {
            Map<String,Object> payment = payments.get(i);
            String originalType = BeanUtil.getPropString(payment, Transaction.PAYMENT_ORIGIN_TYPE);
            long amount = BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
            if(originalTypes.contains(originalType)){
                doubtedPaymentIndexList.add(i);
            }else{
                String type = BeanUtil.getPropString(payment, Transaction.PAYMENT_TYPE);
                if(ALIPAY_CH_LISTS.contains(type)){
                    noDoubtAmount = noDoubtAmount + amount;
                }
            }
        }
        //待拼凑的商户免充值金额, 需要用 doubtedPaymentIndexList 里面的金额来拼凑
        long targetAmount = channelMchNoTopUpAmount - noDoubtAmount;
        //需要用有疑问的payment来拼凑出免充值目标金额
        //此处我们要解决的问题与 leetcode 40 题类似，但是此处为了实现简单，直接用排列组合的方式实现
        //可简化为，每一个数字都可以乘以 0 或者 1， 然后加起来判断是否是目标金额， 如果有5个元素，我们可以把每一种可能的符号进行排列得到如下的符号结果
        // 00000
        // 00001
        // 00010
        // *****
        // 11111
        // 可以看到，这个排列可以抽象为从0 到 pow(2,n)的二进制表示。 为0表示不是商户免充值金额， 为1是免充值金额

        long count = Math.round(Math.pow(2, doubtedPaymentIndexList.size()));
        for (int i = 0; i <= count; i++) {
            long amount = 0;
            String binary = org.apache.commons.lang3.StringUtils.leftPad(Integer.toBinaryString(i), doubtedPaymentIndexList.size(), "0");
            for (int j = 0; j < doubtedPaymentIndexList.size(); j++) {
                String bitValue = binary.substring(j, j + 1);
                amount = amount + Integer.parseInt(bitValue) * BeanUtil.getPropLong(payments.get(doubtedPaymentIndexList.get(j)), Transaction.PAYMENT_AMOUNT);
                if(amount > targetAmount){
                    break;
                }
            }
            //拼凑出了商户免充值目标金额, 修改掉对应payment的相关信息
            if(amount == targetAmount){
                for (int j = 0; j < doubtedPaymentIndexList.size(); j++) {
                    String bitValue = binary.substring(j, j + 1);
                    Map<String,Object> payment = payments.get(doubtedPaymentIndexList.get(j));
                    if("1".equals(bitValue)){
                        payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH);
                    }else{
                        String originalType = BeanUtil.getPropString(payment, Transaction.PAYMENT_ORIGIN_TYPE);
                        if(FC_MCOUPON.equals(originalType) || FC_MDISCOUNT.equals(originalType)){
                            payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP);
                        }else{
                            payment.put(Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL);
                        }
                    }
                }
                break;
            }
        }
        if(getChannelMchNoTopUpAmountSum(payments) == channelMchNoTopUpAmount){
            return payments;
        }

        //修正后还有差异, 忽略支付宝返回的明细, 生成新的明细
        logger.warn("alipay payments did't match the channel mch no top up amount, {}, {}", channelMchNoTopUpAmount, payments);
        List<Map<String,Object>> generated = new ArrayList<>();
        generated.add(CollectionUtil.hashMap(
                Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL_MCH,
                Transaction.PAYMENT_ORIGIN_TYPE, null,
                Transaction.PAYMENT_AMOUNT, channelMchNoTopUpAmount));
        if(totalAmount - channelMchNoTopUpAmount > 0){
            generated.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_TYPE, Payment.TYPE_WALLET_ALIPAY,
                    Transaction.PAYMENT_ORIGIN_TYPE, null,
                    Transaction.PAYMENT_AMOUNT, totalAmount - channelMchNoTopUpAmount));
        }
        return generated;
    }


    /**
     * 获取channel商户免充值优惠金额
     * @param payments
     * @return
     */
    private static long getChannelMchNoTopUpAmountSum(List<Map<String,Object>> payments){
        if(payments == null || payments.isEmpty()){
            return 0;
        }
        long sum = 0;
        for(Map<String,Object> payment: payments){
            if(ALIPAY_CH_LISTS.contains(BeanUtil.getPropString(payment, Transaction.PAYMENT_TYPE))){
                sum = sum + BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
            }
        }
        return sum;
    }


    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    protected int getNotifyUrlLimit(){
        return NOTIFY_URL_LIMIT;
    }

    
    @Override
    public String depositFreeze(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        String notifyUrl = getNotifyUrl(notifyHost, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_FREEZE), context, NOTIFY_DEPOSIT);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_FREEZE);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.PAY_TIMEOUT, b2cTimeoutExpress);
        builder.bizSet(BusinessV2Fields.AUTH_CODE, (String)extraParams.get(Transaction.BARCODE));
        builder.bizSet(BusinessV2Fields.AUTH_CODE_TYPE, AlipayConstants.SCENE_BAR_CODE);
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, (String)transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, (String)transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.ORDER_TITLE, (String) transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_PRE_AUTH);
//        builder.bizSet(BusinessV2Fields.ENABLE_PAY_CHANNELS, DEPOSIT_DEFAULT_ENABLE_PAY_CHANNELS);
        Map extraParam = (Map) BeanUtil.getProperty(builder.getBizContent(), BusinessV2Fields.EXTRA_PARAM);
        if (MapUtils.isEmpty(extraParam)) {
            extraParam = CollectionUtil.hashMap();
        }
        extraParam.put(BusinessV2Fields.CATEGORY
                , BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_CATEGORY));

        if(config.containsKey(TransactionParam.ALIPAY_SERVICE_ID)) {
            extraParam.put(BusinessV2Fields.SERVICE_ID, BeanUtil.getPropString(config, TransactionParam.ALIPAY_SERVICE_ID));
        }
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, extraParam);

        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);

        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_FREEZE), request, 1, OP_DEPOSIT_FREEZE);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit freeze", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_FREEZE, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            //业务处理失败
            String subCode = BeanUtil.getPropString(result, BusinessV2Fields.SUB_CODE);
            if(AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.contains(subCode) || AlipayConstants.DEPOSIT_FAIL_ERR_CODE_LISTS.contains(subCode)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            //业务出现未知错误或者系统异常
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)){
            //业务处理中
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            String status = BeanUtil.getPropString(result, BusinessV2Fields.STATUS);
            if(AlipayConstants.STATUS_SUCCESS.equals(status)) {
                //付款成功
                transaction.put(Transaction.BUYER_UID, result.get(BusinessV2Fields.PAYER_USER_ID));
                transaction.put(Transaction.TRADE_NO, result.get(BusinessV2Fields.AUTH_NO));
                context.getOrder().put(Transaction.TRADE_NO, result.get(BusinessV2Fields.AUTH_NO));
                transaction.put(Transaction.BUYER_LOGIN,result.get(BusinessV2Fields.PAYER_LOGON_ID));
                transaction.put(Transaction.CHANNEL_FINISH_TIME,  parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.GMT_TRANS)));
                resolvePayFund(context.getOrder(), transaction, result);
                return Workflow.RC_PAY_SUCCESS;
            }else if(AlipayConstants.STATUS_INIT.equals(status)) {
                return Workflow.RC_IN_PROG; 
            }else if(AlipayConstants.STATUS_CLOSED.equals(status)) {
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        return Workflow.RC_ERROR;

    }

    @Override
    public String depositConsume(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        String notifyUrl = getNotifyUrl(notifyHost, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), context, NOTIFY_DEPOSIT);
        if (notifyUrl != null) {
            builder.set(ProtocolV2Fields.NOTIFY_URL, notifyUrl);
        }
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_TRADE);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.STORE_ID, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_STORE_ID, 
                BeanUtil.getPropString(transaction, KEY_STORE_CLIENT_SN, (String)transaction.get(Transaction.STORE_ID))).replaceAll("-", ""));
        builder.bizSet(BusinessV2Fields.TERMINAL_ID, BeanUtil.getPropString(transaction, KEY_TERMINAL_SN));
        builder.bizSet(BusinessV2Fields.ALIPAY_STORE_ID, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_SHOP_ID));
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.TIMEOUT_EXPRESS, b2cTimeoutExpress);
        //支付宝预授权
        builder.bizSet(BusinessV2Fields.AUTH_NO, BeanUtil.getProperty(context.getOrder(), Order.TRADE_NO));
        int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
        if(subPayway == Order.SUB_PAYWAY_BARCODE) {
            builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_PRE_AUTH);
            builder.bizSet(BusinessV2Fields.SCENE, AlipayConstants.SCENE_BAR_CODE);
        }else{
            builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_PRE_AUTH_ONLINE);
        }
        builder.bizSet(BusinessV2Fields.AUTH_CONFIRM_MODE, AlipayConstants.AUTH_CONFIRM_MODE_COMPLETE);

        String sellerId = BeanUtil.getPropString(config, TransactionParam.ALIPAY_SELLER_ID
                , BeanUtil.getPropString(config, TransactionParam.ALIPAY_MCH_ID));
        if (!StringUtils.isEmpty(sellerId)) {
            builder.bizSet(BusinessV2Fields.SELLER_ID, sellerId);
        }
        builder.bizSet(BusinessV2Fields.BUYER_ID, BeanUtil.getPropString(context.getOrder(), Order.BUYER_UID));
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT, StringUtils.cents2yuan((Long) transaction.get(Transaction.EFFECTIVE_AMOUNT)));
        builder.bizSet(BusinessV2Fields.SUBJECT, (String) transaction.get(Transaction.SUBJECT));
        builder.bizSet(BusinessV2Fields.BODY, (String) transaction.get(Transaction.BODY));
        builder.bizSet(BusinessV2Fields.OPERATOR_ID, transaction.get(Transaction.OPERATOR));

        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);

        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CONSUME), request, 1, OP_DEPOSIT_CONSUME);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit consume", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CONSUME, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if(result == null){
            return Workflow.RC_IN_PROG;
        }

        String tradeNo = BeanUtil.getPropString(result, BusinessV2Fields.TRADE_NO);
        if (Objects.nonNull(tradeNo) && !tradeNo.trim().isEmpty()) {
            context.getTransaction().put(Transaction.TRADE_NO, tradeNo);
        }

        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            String subCode = (String)result.get(BusinessV2Fields.SUB_CODE);
            if(AlipayConstants.RESULT_CODE_V2_PAY_TRADE_HAS_SUCCESS.equals(subCode)) {
                //预授权完成需要存储外部订单号，当前返回无外部订单号，需要做查单
                String rc = query(context);
                if(!rc.equals(Workflow.RC_PAY_SUCCESS)) {
                    return Workflow.RC_ERROR;
                }
                return Workflow.RC_CONSUME_SUCCESS;
            }else if(AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.contains(subCode)) {
                // 预授权完成业务明确处理失败
                return Workflow.RC_CONSUME_FAIL;
            }
            //业务处理失败
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            //业务出现未知错误或者系统异常
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)){
            //业务处理中
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            transaction.put(Transaction.TRADE_NO, getRealTradeNo(MapUtil.getIntValue(transaction, Transaction.PROVIDER), result));
            transaction.put(Transaction.CHANNEL_FINISH_TIME,  parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.GMT_PAYMENT)));
            resolvePayFund(context.getOrder(), transaction, result);
            return Workflow.RC_CONSUME_SUCCESS;
        }
        return Workflow.RC_ERROR;

    }
    
    @Override
    public String depositQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        initTransactionSomeValue(transaction);
        RequestV2Builder builder = getAlipayV2Builder(context);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY);
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, transaction.get(Transaction.TSN));
        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_QUERY), request, retryTimes, OP_DEPOSIT_QUERY);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit query", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_QUERY, ex);
            //连接错误，继续轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IN_PROG : Workflow.RC_IOEX;
        }

        if(result == null){
            return Workflow.RC_IOEX;
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        String errCode = (String)result.get(BusinessV2Fields.SUB_CODE); //错误代码
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            if (AlipayConstants.RESULT_CODE_DEPOSIT_AUTH_ORDER_NOT_EXIST.equals(errCode)){
                Map payResultMap = (Map) BeanUtil.getNestedProperty(transaction, 
                        UpayUtil.getProviderErrorInfoKey(
                                Order.SUB_PAYWAY_BARCODE == BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY) ? MpayServiceProvider.OP_PAY : MpayServiceProvider.OP_PRECREATE));
                if(null == payResultMap 
                        || AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(BeanUtil.getPropString(payResultMap, BusinessV2Fields.CODE))
                        || AlipayConstants.V2_RETURN_CODE_INPROG.equals(BeanUtil.getPropString(payResultMap, BusinessV2Fields.CODE))){
                    return Workflow.RC_IN_PROG;
                }else{
                    return Workflow.RC_ERROR;
                }
            }
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)){
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            String tradeStatus = (String) result.get(BusinessV2Fields.STATUS);
            if(AlipayConstants.STATUS_INIT.equals(tradeStatus)){
                return Workflow.RC_IN_PROG;
            }else if(AlipayConstants.STATUS_SUCCESS.equals(tradeStatus)){
                transaction.put(Transaction.BUYER_UID, result.get(BusinessV2Fields.PAYER_USER_ID));
                transaction.put(Transaction.TRADE_NO, result.get(BusinessV2Fields.AUTH_NO));
                if(StringUtil.empty(BeanUtil.getPropString(context.getOrder(), Order.TRADE_NO))){
                    context.getOrder().put(Order.TRADE_NO, result.get(BusinessV2Fields.AUTH_NO));
                }
                transaction.put(Transaction.BUYER_LOGIN,result.get(BusinessV2Fields.PAYER_LOGON_ID));
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, BusinessV2Fields.GMT_TRANS)));
                return Workflow.RC_PAY_SUCCESS;
            }
        }
        return Workflow.RC_ERROR;
    }
    
    @Override
    public String depositCancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        initTransactionSomeValue(transaction);
        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_FUND_ORDER_CANCEL);
        Map authInfo;
        try{
            authInfo = getAlipayV2AppAuthInfo(context, config);
        }catch (Exception e){
            logger.error("cancel getAlipayV2AppAuthInfo error " + e.getMessage(), e);
            return Workflow.RC_ERROR;
        }
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.REMARK, StringUtils.join(DateUtil.formatDate(new Date(), DateUtil.FORMATTER_DATE_INT), " 解冻", 
                StringUtils.cents2yuan(BeanUtil.getPropLong(context.getOrder(), Order.EFFECTIVE_TOTAL)), "元"));
        
        carryOverExtendedParams(extendedParams, builder);
        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_CANCEL), request, retryTimes, OP_DEPOSIT_CANCEL);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit cancel", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_CANCEL, ex);
            return Workflow.RC_RETRY;
        }
        if(result == null){
            return Workflow.RC_RETRY;
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_CANCEL);

        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            String subCode = (String)result.get(BusinessV2Fields.SUB_CODE);
            if(AlipayConstants.RESULT_CODE_DEPOSIT_CANCEL_ORDER_ALREADY_CLOSED.equals(subCode)) {
                return Workflow.RC_CANCEL_SUCCESS; 
            }else if(AlipayConstants.RESULT_CODE_DEPOSIT_CANCEL_OPERATION_TIME_OUT.equals(subCode)) {
                if(Workflow.RC_REFUND_SUCCESS.equals(depositUnFreezed(context, BeanUtil.getPropLong(context.getOrder(), Order.EFFECTIVE_TOTAL)))) {
                    return Workflow.RC_CANCEL_SUCCESS;
                }
            }
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            return Workflow.RC_RETRY;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            return Workflow.RC_CANCEL_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }
    
    public String depositUnFreezed(TransactionContext context, long amount) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_FUND_ORDER_UNFREEZE);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, BeanUtil.getPropString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, transaction.get(Transaction.ORDER_SN));
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, (String)transaction.get(Transaction.TSN));
        //支付宝预授权
        builder.bizSet(BusinessV2Fields.AUTH_NO, BeanUtil.getProperty(context.getOrder(), Order.TRADE_NO));
        String yuanAmount = StringUtils.cents2yuan(amount);
        builder.bizSet(BusinessV2Fields.AMOUNT, yuanAmount);
        builder.bizSet(BusinessV2Fields.REMARK, StringUtils.join(DateUtil.formatDate(new Date(), DateUtil.FORMATTER_DATE_INT), " 解冻", yuanAmount, "元"));

        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_UNFREEZE), request, 1, OP_DEPOSIT_UNFREEZE);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit unfreeze", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_UNFREEZE, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return (ex instanceof MpayApiConnectError) ? Workflow.RC_IOEX : Workflow.RC_IN_PROG;
        }
        if(result == null){
            return Workflow.RC_IN_PROG;
        }
        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_UNFREEZE);
        if(AlipayConstants.V2_RETURN_CODE_FAIL.equals(returnCode)){
            //业务处理失败
            return Workflow.RC_ERROR;
        }else if(AlipayConstants.V2_RETURN_CODE_UNKNOW.equals(returnCode)){
            //业务出现未知错误或者系统异常
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_INPROG.equals(returnCode)){
            //业务处理中
            return Workflow.RC_IN_PROG;
        }else if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            if(AlipayConstants.STATUS_SUCCESS.equals(BeanUtil.getPropString(result, BusinessV2Fields.STATUS))) {
                return Workflow.RC_REFUND_SUCCESS;
            }else if(AlipayConstants.STATUS_INIT.equals(BeanUtil.getPropString(result, BusinessV2Fields.STATUS))) {
                return Workflow.RC_IN_PROG;
            }
        }
        return Workflow.RC_ERROR;

    }

    protected void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String buyerUid = getBuyerUid(result);
            if(!StringUtils.isEmpty(buyerUid)){
                transaction.put(Transaction.BUYER_UID, buyerUid);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String buyerLogonId = MapUtil.getString(result, BusinessV2Fields.BUYER_LOGON_ID);
            if (!StringUtils.isEmpty(buyerLogonId)) {
                transaction.put(Transaction.BUYER_LOGIN, buyerLogonId);
            }
        }

        if (StringUtils.isEmpty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = getRealTradeNo(MapUtil.getIntValue(transaction, Transaction.PROVIDER), result);
            if (!StringUtils.isEmpty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    private void setExtraOutField(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String,Object> extraOutFields = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }

        String advanceAmount = BeanUtil.getPropString(result, Transaction.ADVANCE_AMOUNT);
        Map riskInfo = MapUtil.getMap(result, BusinessV2Fields.RISK_INFO);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(advanceAmount)) {
            extraOutFields.put(Transaction.ADVANCE_AMOUNT, StringUtils.yuan2cents(advanceAmount));
        }
        if (MapUtil.isNotEmpty(riskInfo)) {
            extraOutFields.put(Transaction.RISK_INFO, riskInfo);
        }

    }

    @Override
    public String depositSync(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);

        RequestV2Builder builder = getAlipayV2Builder(context);
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_ORDERINFO_SYNC);
        Map authInfo = getAlipayV2AppAuthInfo(context, config);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, MapUtil.getString(authInfo, TransactionParam.APP_AUTH_TOKEN, ""));
        builder.bizSet(BusinessV2Fields.TRADE_NO, MapUtil.getString(transaction, Transaction.TRADE_NO));
        String outRequstNo = MapUtil.getString(transaction, Transaction.ORDER_SN);
        Map<String, Object> extraOutFields = (Map<String, Object>)transaction.get(Transaction.EXTRA_OUT_FIELDS);
        String consumeOrderSn = BeanUtil.getPropString(extraOutFields, Transaction.CONSUME_ORDER_SN);
        if(!StringUtil.empty(consumeOrderSn)) {
            outRequstNo = consumeOrderSn;
        }
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO, outRequstNo);
        builder.bizSet(BusinessV2Fields.BIZ_TYPE, AlipayConstants.BIZ_TYPE_CREDIT_AUTH);
        builder.bizSet(BusinessV2Fields.ORDER_BIZ_INFO, JsonUtil.toJsonStr(MapUtil.hashMap(BusinessV2Fields.STATUS, AlipayConstants.SYNC_STATUS_COMPLETE)));

        // Carry over extended params to the pay service provider.
        carryOverExtendedParams(extendedParams, builder);

        Map<String,String> request = null;
        Map<String,Object> result = null;
        try {
            request = builder.build();
        } catch (BuilderException e) {
            logger.error("alipayV2 request builder error ", e);
        }
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_DEPOSIT_SYNC), request, 1, OP_DEPOSIT_SYNC);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 deposit sync", ex);
            setTransactionContextErrorInfo(context, OP_DEPOSIT_SYNC, ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return Workflow.RC_CONSUME_FAIL;
        }
        if(result == null){
            return Workflow.RC_CONSUME_FAIL;
        }

        if (context.getApiVer() == 1) {
            transaction.put(Transaction.PROVIDER_RESPONSE, result);
        }
        String returnCode = (String)result.get(BusinessV2Fields.CODE);//返回状态码
        setTransactionContextErrorInfo(result, context, OP_DEPOSIT_SYNC);
        if(AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)){
            transaction.put(Transaction.TRADE_NO, result.get(BusinessV2Fields.TRADE_NO));
            transaction.put(Transaction.BUYER_UID, getBuyerUid(result));
            transaction.put(Transaction.CHANNEL_FINISH_TIME,  System.currentTimeMillis());
            return Workflow.RC_CONSUME_SUCCESS;
        }
        return Workflow.RC_CONSUME_FAIL;

    }


    protected void setTerminalInfo(TransactionContext context
            , Map<String, Object> config, RequestV2Builder builder) {
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());

        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> termInfo = CollectionUtil.hashMap(
                BusinessV2Fields.TERM_INFO_TERMINAL_TYPE, terminalInfo.getOrDefaultType(AlipayConstants.TERM_INFO_TERMINAL_TYPE)
                , BusinessV2Fields.TERM_INFO_TERMINAL_ID, terminalInfo.getId()
        );
        if(terminalInfo.getSerialNum() != null){
            termInfo.put(BusinessV2Fields.TERM_INFO_SERIAL_NUM, terminalInfo.getSerialNum());
        }

        if (Order.SUB_PAYWAY_BARCODE == subPayWay && terminalInfo.isSendPoi()) {
            termInfo.put(BusinessV2Fields.TERM_INFO_LOCATION, terminalInfo.getFormatLatitude() + "/" + terminalInfo.getFormatLongitude());
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }
        if (!terminalInfo.isOffset() && terminalInfo.isSendIp()) {
            termInfo.put(BusinessV2Fields.TERM_INFO_TERMINAL_IP, terminalInfo.getIp());
        }
        builder.bizSet(BusinessV2Fields.AREA_INFO, MapUtil.getString(config
                , TransactionParam.DISTRICT_CODE));
        builder.bizSet(BusinessV2Fields.TERMINAL_INFO, termInfo);
    }

    public String getBuyerUid(Map<String,Object> result){
        String buyerUid = MapUtil.getString(result, BusinessV2Fields.BUYER_USER_ID);
        if(!StringUtil.empty(buyerUid)){
            return buyerUid;
        }
        buyerUid = MapUtil.getString(result, BusinessV2Fields.BUYER_OPEN_ID);
        if(!StringUtil.empty(buyerUid)){
            return buyerUid;
        }
        // 支付宝回调的时候，返回的是 buyer_id
       return MapUtil.getString(result, BusinessV2Fields.BUYER_ID);
    }

    protected void setQueryOptions(RequestV2Builder builder, Map<String, Object> transaction, String opQuery) {
        String commonSwitch = MapUtil.getString((Map)transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.COMMON_SWITCH);

        List<String> queryOptions = (List<String>) builder.getBizContent().get(BusinessV2Fields.QUERY_OPTIONS);
        if(queryOptions == null) {
            queryOptions = new ArrayList<>();
        }
        // 返回资金渠道
        if ((UpayUtil.isSendFundBillList(commonSwitch)) || (getProvider() == null && GrayReleaseUtil.shouldEnable(ApolloConfigurationCenterUtil.getGraySendFundBillListPercent()))) {
            if (opQuery.equals(OP_REFUND)) {
                queryOptions.add(AlipayConstants.QUERY_OPTIONS_REFUND_DETAIL_ITEM_LIST);
            } else {
                queryOptions.add(AlipayConstants.QUERY_OPTIONS_FUND_BILL_LIST);
            }
        }
        // 返回优惠券使用信息
        if(UpayUtil.isReturnProviderResponse(commonSwitch)) {
            queryOptions.add(AlipayConstants.QUERY_OPTIONS_VOUCHER_DETAIL_LIST);
            queryOptions.add(AlipayConstants.QUERY_OPTIONS_DISCOUNT_GOODS_DETAIL);
        }
        if(!queryOptions.isEmpty()) {
            builder.bizSet(BusinessV2Fields.QUERY_OPTIONS, queryOptions);
        }
    }
}
