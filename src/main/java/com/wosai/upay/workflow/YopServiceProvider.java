package com.wosai.upay.workflow;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.yop.*;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.wosai.upay.util.ProviderUtil.*;


/**
 * 易宝支付
 * Created by wujianwei on 2025/3/30.
 */
public class YopServiceProvider extends AbstractServiceProvider{
    protected static final Logger logger = LoggerFactory.getLogger(YopServiceProvider.class);


    public static final String YOP_BANK_WEIXIN = "CFT";
    public static final String YOP_BANK_ALIPAY = "ALIPAY";


    private static final Map<String, String> PAY_WAY_SCENE_CONFIG = new HashMap<String, String>(){{
        put(Order.PAYWAY_ALIPAY + "", SceneConstants.AlipayScene.OFFLINE);
        put(Order.PAYWAY_ALIPAY2 + "", SceneConstants.AlipayScene.OFFLINE);
        put(Order.PAYWAY_WEIXIN + "", SceneConstants.WechatScene.OFFLINE);
    }};

    /**
     * payway + ":" + subPayway -> yop payway
     */
    private static final Map<String, String> PAY_WAY_SUB_PAYWAY_YOP_PAY_WAY_CONFIG = new HashMap<String, String>(){{
        put(Order.PAYWAY_WEIXIN + ":" + Order.SUB_PAYWAY_WAP, PaywayConstant.WECHAT_OFFIACCOUNT);
        put(Order.PAYWAY_WEIXIN + ":" + Order.SUB_PAYWAY_MINI, PaywayConstant.MINI_PROGRAM);

        put(Order.PAYWAY_ALIPAY2 + ":" + Order.SUB_PAYWAY_WAP, PaywayConstant.ALIPAY_LIFE);
        put(Order.PAYWAY_ALIPAY2 + ":" + Order.SUB_PAYWAY_MINI, PaywayConstant.MINI_PROGRAM);

        put(Order.PAYWAY_UNIONPAY + ":" + Order.SUB_PAYWAY_WAP, PaywayConstant.JS_PAY);
    }};

    private static final Map<String, String> PAY_WAY_CHANNEL_CONFIG = new HashMap<String, String>(){{
        put(Order.PAYWAY_ALIPAY + "", YopConstant.CHANNEL_ALIPAY);
        put(Order.PAYWAY_ALIPAY2 + "", YopConstant.CHANNEL_ALIPAY);
        put(Order.PAYWAY_WEIXIN + "", YopConstant.CHANNEL_WECHAT);
        put(Order.PAYWAY_UNIONPAY + "", YopConstant.CHANNEL_UNIONPAY);
        put(Order.PAYWAY_DCEP + "", YopConstant.CHANNEL_DCEP);
    }};

    private static final List<String> BUYER_LOGIN_RESPONSE_FIELDS = Arrays.asList(
            YopResponseFields.PayerInfoConstants.BUYER_LOGON_ID,
            YopResponseFields.PayerInfoConstants.MOBILE_PHONE_NO,
            YopResponseFields.PayerInfoConstants.BANK_CARD_NO,
            YopResponseFields.PayerInfoConstants.ACCOUNT_NAME
    );

    private static final List<String> CHANNEL_FINISH_TIME_RESPONSE_FIELDS = Arrays.asList(
            YopResponseFields.PAY_SUCCESS_TIME,
            YopResponseFields.PAY_SUCCESS_DATE
    );

    private static final Map<String, String> RESPONSE_CARD_TYPE_PAYMENT_TYPE_CONFIG = CollectionUtil.hashMap(
            YopResponseFields.PayerInfoConstants.CARD_TYPE_DEBIT, Payment.TYPE_BANKCARD_DEBIT,
            YopResponseFields.PayerInfoConstants.CARD_TYPE_CREDIT, Payment.TYPE_BANKCARD_CREDIT,
            YopResponseFields.PayerInfoConstants.CARD_TYPE_CFT, Payment.TYPE_WALLET_WEIXIN,
            YopResponseFields.PayerInfoConstants.CARD_TYPE_QUASI_CREDIT, Payment.TYPE_BANKCARD_CREDIT,
            YopResponseFields.PayerInfoConstants.CARD_TYPE_PUBLIC_ACCOUNT, Payment.TYPE_BANKACCOUNT
    );



    protected String notifyHost;



    @Autowired
    private YopClient client;

    public static final String NAME = "provider.yop";


    public YopServiceProvider() {
        extendedFilterFields = new HashSet<String>(Arrays.asList(YopRequestFields.ORDER_AMOUNT, YopRequestFields.REFUND_AMOUNT));
        dateFormat = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_YOP;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.YOP_TRADE_PARAMS);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        String scene = PAY_WAY_SCENE_CONFIG.get(payway + "");
        Map<String, String> request = buildCommonRequestParams(config);
        request.put(YopRequestFields.PAY_WAY, PaywayConstant.MERCHANT_SCAN);
        request.put(YopRequestFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        if(scene != null){
            request.put(YopRequestFields.SCENE, scene);
        }
        request.put(YopRequestFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(YopRequestFields.ORDER_AMOUNT, StringUtils.cents2yuan((MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT))));
        request.put(YopRequestFields.GOODS_NAME, MapUtil.getString(transaction, Transaction.SUBJECT));
        request.put(YopRequestFields.EXPIRE_TIME, YopUtil.formatDate(System.currentTimeMillis() + DEFAULT_TIME_EXPIRE_MINUTE * 60 * 1000));
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        if(terminalInfo.getIp() != null){
            request.put(YopRequestFields.USER_IP, terminalInfo.getIp());
        }
        request.put(YopRequestFields.TERMINAL_ID, terminalInfo.getId() != null ? terminalInfo.getId() : MapUtil.getString(configSnapshot, TransactionParam.TERMINAL_SN));
        if(payway == Order.PAYWAY_WEIXIN){
            request.put(YopRequestFields.APP_ID, MapUtil.getString(config, TransactionParam.YOP_SUB_APPID));
        }

        carryOverExtendedParams(extendedParams, request, null);
        Map<String, Object> result;
        try {
            
            result = client.call(YopConstant.YopRequestMethod.POST,YopConstant.YopRequestContentType.FORM_URL_ENCODE, url, request, MapUtil.getString(config, TransactionParam.YOP_APP_KEY), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.YOP_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call yop pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        updateTransactionCommonInfo(transaction, result);
        String resultCode = MapUtil.getString(result, YopResponseFields.CODE);
        if(!YopConstant.CODE_SUCCESS_LIST.contains(resultCode)){
            return Workflow.RC_ERROR;
        }
        String status = MapUtil.getString(result, YopResponseFields.STATUS);
        if(PaymentStatusConstants.isPaymentCompleted(status)){
            if(PaymentStatusConstants.isPaymentSuccessful(status)){
                // success
                updateTransactionPaymentInfo(transaction, result);
                return Workflow.RC_PAY_SUCCESS;
            }else {
                // fail
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        if(PaymentStatusConstants.isPaymentInProgress(status)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_ERROR;
    }





    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, false);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        updateTransactionCommonInfo(transaction, result);
        String resultCode = MapUtil.getString(result, YopResponseFields.CODE);
        if(!YopConstant.CODE_SUCCESS_LIST.contains(resultCode)){
            return Workflow.RC_IN_PROG;
        }
        String status = MapUtil.getString(result, YopResponseFields.STATUS);
        if(PaymentStatusConstants.isPaymentCompleted(status)){
            if(PaymentStatusConstants.isPaymentSuccessful(status)){
                // success
                updateTransactionPaymentInfo(transaction, result);
                return Workflow.RC_PAY_SUCCESS;
            }else {
                // fail or close
                return Workflow.RC_TRADE_CANCELED;
            }
        }
        if(PaymentStatusConstants.isPaymentInProgress(status)){
            return Workflow.RC_IN_PROG;
        }
        return Workflow.RC_IN_PROG;
    }

    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> result = doQuery(context, true);
        if(result == null){
            return Workflow.RC_IOEX;
        }
        String resultCode = MapUtil.getString(result, YopResponseFields.CODE, "");
        if(!YopConstant.CODE_SUCCESS_LIST.contains(resultCode)){
            return Workflow.RC_SYS_ERROR;
        }
        String status = MapUtil.getString(result, YopResponseFields.STATUS);
        if(RefundStatusConstants.isRefundCompleted(status)){
            if(RefundStatusConstants.isRefundSuccessful(status)){
                // success
                updateTransactionRefundInfo(context, result);
                return Workflow.RC_REFUND_SUCCESS;
            }else {
                // fail or close
                return Workflow.RC_SYS_ERROR;
            }
        }
        if(RefundStatusConstants.isRefundInProgress(status)){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_RETRY;
    }


    // doQuery
    protected Map<String,Object> doQuery(TransactionContext context, boolean isRefundQuery) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, String> request = buildCommonRequestParams(config);
        // 查询参数
        request.put(YopRequestFields.ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        if(isRefundQuery){
            request.put(YopRequestFields.REFUND_REQUEST_ID, MapUtil.getString(transaction, Transaction.TSN));
        }
        String queryFlag = isRefundQuery ? OP_REFUND_QUERY : OP_QUERY;
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), queryFlag);
        Map<String, Object> result = null;
        try {
            result = client.call(YopConstant.YopRequestMethod.GET, YopConstant.YopRequestContentType.FORM_URL_ENCODE, url, request, MapUtil.getString(config, TransactionParam.YOP_APP_KEY), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.YOP_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call yop query", ex);
            setTransactionContextErrorInfo(context, queryFlag, ex);
        }
        setTransactionContextErrorInfo(result, context, queryFlag);
        return result;

    }



    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery){
            return refundQuery(context);
        }else{
            return doRefund(context);
        }
    }

    protected String doRefund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map<String, String> request = buildCommonRequestParams(config);
        request.put(YopRequestFields.ORDER_ID, MapUtil.getString(transaction, Transaction.ORDER_SN));
        request.put(YopRequestFields.REFUND_REQUEST_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(YopRequestFields.REFUND_AMOUNT, StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT)));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND);
        Map<String, Object> result;
        try {
            result = client.call(YopConstant.YopRequestMethod.POST,YopConstant.YopRequestContentType.FORM_URL_ENCODE, url, request, MapUtil.getString(config, TransactionParam.YOP_APP_KEY), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.YOP_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call yop refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            return Workflow.RC_IOEX;
        }finally {
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        String resultCode = MapUtil.getString(result, YopResponseFields.CODE, "");
        if(!YopConstant.CODE_SUCCESS_LIST.contains(resultCode)){
            return Workflow.RC_SYS_ERROR;
        }
        String status = MapUtil.getString(result, YopResponseFields.STATUS);
        if(RefundStatusConstants.isRefundCompleted(status)){
            if(RefundStatusConstants.isRefundSuccessful(status)){
                // success
                updateTransactionRefundInfo(context, result);
                return Workflow.RC_REFUND_SUCCESS;
            }else {
                // fail or close
                return Workflow.RC_SYS_ERROR;
            }
        }
        if(RefundStatusConstants.isRefundInProgress(status)){
            return Workflow.RC_RETRY;
        }
        return Workflow.RC_RETRY;
    }



    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);

        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        String scene = PAY_WAY_SCENE_CONFIG.get(payway + "");

        Map<String, String> request = buildCommonRequestParams(config);
        request.put(YopRequestFields.PAY_WAY, PAY_WAY_SUB_PAYWAY_YOP_PAY_WAY_CONFIG.get(payway + ":" + subPayway));
        request.put(YopRequestFields.CHANNEL, PAY_WAY_CHANNEL_CONFIG.get(payway + ""));
        request.put(YopRequestFields.ORDER_ID, MapUtil.getString(transaction, Transaction.TSN));
        request.put(YopRequestFields.ORDER_AMOUNT, StringUtils.cents2yuan((MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT))));
        if(scene != null){
            request.put(YopRequestFields.SCENE, scene);
        }
        if(payway == Order.PAYWAY_WEIXIN){
            request.put(YopRequestFields.APP_ID, MapUtil.getString(config, TransactionParam.YOP_MINI_SUB_APPID));
        }
        request.put(YopRequestFields.USER_ID, MapUtil.getString(extraParams, Transaction.PAYER_UID));
        request.put(YopRequestFields.GOODS_NAME, MapUtil.getString(transaction, Transaction.SUBJECT));
        request.put(YopRequestFields.EXPIRE_TIME, YopUtil.formatDate(System.currentTimeMillis() + B2C_TIME_EXPIRE_MINUTE * 60 * 1000));
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        if(terminalInfo.getIp() != null){
            request.put(YopRequestFields.USER_IP, terminalInfo.getIp());
        }
        request.put(YopRequestFields.TERMINAL_ID, terminalInfo.getId() != null ? terminalInfo.getId() : MapUtil.getString(configSnapshot, TransactionParam.TERMINAL_SN));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PRECREATE);
        String notifyUrl = getNotifyUrl(notifyHost, url, context);
        if (notifyUrl != null) {
            request.put(YopRequestFields.NOTIFY_URL, notifyUrl);
        }
        carryOverExtendedParams(extendedParams, request, null);
        Map<String, Object> result;
        try {
            result = client.call(YopConstant.YopRequestMethod.POST, YopConstant.YopRequestContentType.FORM_URL_ENCODE, url, request, MapUtil.getString(config, TransactionParam.YOP_APP_KEY), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.YOP_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call yop precreate", ex);
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        updateTransactionCommonInfo(transaction, result);
        String resultCode = MapUtil.getString(result, YopResponseFields.CODE);
        if(!YopConstant.CODE_SUCCESS_LIST.contains(resultCode)){
            return Workflow.RC_ERROR;
        }
        String prePayTn = MapUtil.getString(result, YopResponseFields.PRE_PAY_TN);
        if(StringUtils.isEmpty(prePayTn)){
            return Workflow.RC_TRADE_CANCELED;
        }
        try{
            Map<String, Object> wapRequest = new HashMap<String, Object>();
            if(payway == Order.PAYWAY_WEIXIN){
                // json
                wapRequest.putAll(JsonUtil.jsonStringToObject(prePayTn, Map.class));
            }else if(payway == Order.PAYWAY_ALIPAY2){
                // string
                wapRequest.put(WAP_PAY_REQUEST_ALIPAY_TRADE_NO, prePayTn);
            }else if(payway == Order.PAYWAY_UNIONPAY){
                wapRequest.put(WAP_PAY_REQUEST_ALIPAY_UNION_PAY_QRCODE_REDIRECT_URL, prePayTn);
            }
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }catch (Exception e){
            logger.error("process wap pay request error: " + e.getMessage(), e);
            return Workflow.RC_TRADE_CANCELED;
        }
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, String> request = buildCommonRequestParams(config);
        request.put(YopRequestFields.USER_AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        String url = ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY);
        Map<String, Object> result;
        try {
            result = client.call(YopConstant.YopRequestMethod.POST, YopConstant.YopRequestContentType.JSON, url, request, MapUtil.getString(config, TransactionParam.YOP_APP_KEY), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.PRIVATE_KEY)), getPrivateKeyContent(MapUtil.getString(config, TransactionParam.YOP_PUBLIC_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call yop get user id", ex);
            return null;
        }
        if (result == null) {
            return null;
        }
        return CollectionUtil.hashMap(com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID, MapUtil.getString(result, YopResponseFields.USER_ID));
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    @Override
    protected int getNotifyUrlLimit() {
        // 易宝有200字符限制。此处设置小一点
        return 150;
    }

    private static Map<String, String> buildCommonRequestParams(Map<String,Object> config) {
        Map<String, String> request = new HashMap();
        request.put(YopRequestFields.MERCHANT_NO, MapUtil.getString(config, TransactionParam.PROVIDER_MCH_ID));
        request.put(YopRequestFields.PARENT_MERCHANT_NO, MapUtil.getString(config, TransactionParam.YOP_ISV_MCH_ID));
        return request;
    }

    protected void carryOverExtendedParams(Map<String, Object> extended, Map<String, String> request, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if((allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)) || overFilterField(key)){
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                if(UpayConstant.EXTENDED_SUB_APPID.equals(key)){
                    request.put(YopRequestFields.APP_ID, value.toString());
                    continue;
                }
                request.put(key, value.toString());

            }
        }
    }

    /**
     * 更新付款信息
     * @param transaction
     * @param result
     */
    protected void updateTransactionPaymentInfo(Map<String, Object> transaction, Map<String, Object> result){
        updateMapIfResponseNotNull(transaction, Transaction.CHANNEL_FINISH_TIME, result, CHANNEL_FINISH_TIME_RESPONSE_FIELDS, object -> parseTimeString((String) object));
        updateMapIfResponseNotNull(transaction, Transaction.PAID_AMOUNT, result, YopResponseFields.REAL_PAY_AMOUNT, object -> StringUtils.yuan2cents((String) object));

        // 计算支付明细
        Long effectiveAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT);
        //获取用户实付金额
        Long paidAmount = MapUtil.getLong(transaction, Transaction.PAID_AMOUNT, effectiveAmount);
        List<Map<String,Object>> payments = new ArrayList<>();
        long couponSum = effectiveAmount - paidAmount;
        if(couponSum > 0){
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, Payment.TYPE_DISCOUNT_CHANNEL,
                            Transaction.PAYMENT_AMOUNT, couponSum
                    )
            );
        }
        Map payInfo = MapUtil.getMap(result, YopResponseFields.PAYER_INFO);
        String cardType = MapUtil.getString(payInfo, YopResponseFields.PayerInfoConstants.CARD_TYPE);
        if(cardType == null){
            cardType = MapUtil.getString(result, YopResponseFields.CARD_TYPE);
        }
        String payBank = MapUtil.getString(result, YopResponseFields.PAY_BANK);
        String bankId = MapUtil.getString(payInfo, YopResponseFields.BANK_ID);
        String bank = getOrDefault(payBank, bankId);
        if(Objects.equals(cardType, YopResponseFields.PayerInfoConstants.CARD_TYPE_DEBIT) && (Objects.equals(bank, YOP_BANK_ALIPAY) || Objects.equals(bank, YOP_BANK_WEIXIN))){
            // 对于AT非卡支付业务，cardType 易宝会转成借记卡类型, 此处做修正，设置为空
            cardType = null;
        }
        String paymentType = RESPONSE_CARD_TYPE_PAYMENT_TYPE_CONFIG.get(cardType);
        if(paymentType == null){
            paymentType = getDefaultPaymentType(MapUtil.getString(transaction, Transaction.PAYWAY));
        }
        if(paidAmount > 0){
            payments.add(
                    CollectionUtil.hashMap(
                            Transaction.PAYMENT_TYPE, paymentType,
                            Transaction.PAYMENT_ORIGIN_TYPE, getOrDefault(payBank, cardType),
                            Transaction.PAYMENT_AMOUNT, paidAmount
                    )
            );
        }
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, payments);
    }

    /**
     * 更新退款信息
     * @param context
     * @param result
     */
    protected void updateTransactionRefundInfo(TransactionContext context, Map<String, Object> result){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }


    /**
     * 更新通用字段
     * @param transaction
     * @param result
     */
    protected void updateTransactionCommonInfo(Map<String, Object> transaction, Map<String, Object> result) {
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        // 解析易宝订单号
        updateMapIfResponseNotNull(transaction, Transaction.TRADE_NO, result, YopResponseFields.UNIQUE_ORDER_NO);
        // 解析微信支付宝支付源订单号
        updateMapIfResponseNotNull(extraOutFields, Transaction.TRADE_NO, result, YopResponseFields.BANK_ORDER_ID);
        // 解析用户信息
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID, result, YopResponseFields.USER_ID);
        // 解析用户信息
        Map<String,Object> payInfo = MapUtil.getMap(result, YopResponseFields.PAYER_INFO);
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_UID, payInfo, YopResponseFields.PayerInfoConstants.USER_ID);
        updateMapIfResponseNotNull(transaction, Transaction.BUYER_LOGIN, payInfo, BUYER_LOGIN_RESPONSE_FIELDS);
    }


    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String resultCode = MapUtil.getString(result, YopResponseFields.CODE);
        String resultDesc = MapUtil.getString(result, YopResponseFields.MESSAGE);
        String subResultCode = MapUtil.getString(result, YopResponseFields.SUB_CODE);
        String subResultDesc = MapUtil.getString(result, YopResponseFields.SUB_MESSAGE);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(YopResponseFields.CODE, resultCode);
        map.put(YopResponseFields.MESSAGE, resultDesc);
        map.put(YopResponseFields.SUB_CODE, subResultCode);
        map.put(YopResponseFields.SUB_MESSAGE, subResultDesc);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, YopConstant.CODE_SUCCESS_LIST.contains(resultCode), getOrDefault(subResultCode, resultCode), getOrDefault(subResultDesc, resultDesc));
    }





    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }
}
