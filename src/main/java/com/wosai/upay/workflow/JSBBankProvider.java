package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.WapV2Fields;
import com.wosai.mpay.api.jsbank.*;
import com.wosai.mpay.api.jsbank.enums.JSBOrderStatusEnum;
import com.wosai.mpay.api.jsbank.enums.JSBRefundOrderStatusEnum;
import com.wosai.mpay.api.jsbank.enums.JSBTradeTypeEnum;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.CharacterUtil;
import com.wosai.upay.util.LocalDateTimeUtil;
import com.wosai.upay.util.UpayUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.wosai.mpay.api.jsbank.JSBConstants.*;
import static com.wosai.upay.core.model.TransactionParam.*;
import static com.wosai.upay.util.LocalDateTimeUtil.getFormatDateTime;
import static com.wosai.upay.util.LocalDateTimeUtil.toEpochMs;

/**
 * <AUTHOR>
 * @description 江苏银行
 * @date 2024/11/5
 */
@Slf4j
@ServiceProvicerPriority(priority = Integer.MAX_VALUE - 1)
public class JSBBankProvider extends AbstractServiceProvider {
    public static final String NAME = "provider.jsb";
    private static final int B2C_ORDER_TIME_OUT_MINUTE = 1; //b2c订单超时时间设置为1分钟
    protected static final int NOTIFY_URL_LIMIT = 200;
    //设备指纹长度限制
    private static final int TERMINAL_FINGERPRINT_LENGTH_MAX = 28;

    @Resource
    private JSBClient jsbClient;
    /**
     * 通知地址
     */
    @Setter
    private String notifyHost;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.JSB_TRADE_PARAMS);
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_JSB;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        JSBRequestBuilder requestBuilder = getDefaultRequestBuilder(tradeParams);
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        requestBuilder.set(JSBBusinessFields.AUTH_CODE, MapUtil.getString(extraParams, Transaction.BARCODE));
        //交易金额
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        requestBuilder.set(JSBBusinessFields.TOTAL_FEE, amount);
        //商品名称
        String subject = CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT));
        requestBuilder.set(JSBBusinessFields.PRODUCT_INFO, subject);
        //添加门店编号
        requestBuilder.set(JSBBusinessFields.SHOP_ID, getStoreSnFromTransaction(transaction));
        //添加交易类型
        addTradeType(requestBuilder, context);
        //添加设备指纹
        addTerminalFingerprint(requestBuilder, transaction, tradeParams);
        //添加终端ip
        addTerminalIp(requestBuilder, extraParams);

        //交易起始时间 使用订单ctime, 否则在跨日临界点会出现 请求日期是20231220， 但是订单生成日期是20231219的情况，这种情况下订单查询会有问题
        long orderCtime = MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME);
        LocalDateTime orderStartTime = LocalDateTimeUtil.valueOfEpochMs(orderCtime);
        LocalDateTime orderExpireTime = orderStartTime.plusMinutes(B2C_ORDER_TIME_OUT_MINUTE);
        requestBuilder.set(JSBBusinessFields.TIME_START, getFormatDateTime(orderStartTime, ORDER_TIME_FORMAT));
        requestBuilder.set(JSBBusinessFields.TIME_EXPIRE, getFormatDateTime(orderExpireTime, ORDER_TIME_FORMAT));

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(METHOD_PAY, requestBuilder.build(), tradeParams, OP_PAY, 1);
        } catch (Exception e) {
            log.error("call jsbBank pay error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PAY, e);
            return Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildPayResult(result, context);
    }

    /**
     * 预下单
     *
     * @param context
     * @param resume
     * @return
     */
    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int payway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        if (Order.PAYWAY_WEIXIN == payway) {
            //微信预下单
            return wechatPreCreate(context, resume);
        } else if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
            //支付宝预下单
            return alipayPreCreate(context, resume);
        } else {
            throw new UnsupportedOperationException("江苏银行只支持微信和支付宝预下单");
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        JSBRequestBuilder requestBuilder = getDefaultRequestBuilder(tradeParams);
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.TSN));
        requestBuilder.set(JSBBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TRADE_NO));
        //添加交易类型
        addTradeType(requestBuilder, context);
        //添加设备指纹
        addTerminalFingerprint(requestBuilder, transaction, tradeParams);
        //添加终端ip
        addTerminalIp(requestBuilder, extraParams);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(METHOD_QUERY, requestBuilder.build(), tradeParams, OP_QUERY, 1);
        } catch (Exception e) {
            log.error("call jsbBank query error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_QUERY, e);
            return Workflow.RC_IN_PROG;
        }
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        return buildQueryResult(result, context, OP_QUERY);
    }

    /**
     * 查询退款结果
     *
     * @param context
     * @return
     */
    private String queryRefundResult(TransactionContext context) {
        String workflowStatus = query(context);
        //退款工作流不识别Workflow.RC_IN_PROG，因此需要转换为Workflow.RC_RETRY
        return Workflow.RC_IN_PROG.equals(workflowStatus) ? Workflow.RC_RETRY : workflowStatus;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if (onlyRefundQuery) {
            return queryRefundResult(context);
        }

        JSBRequestBuilder requestBuilder = getDefaultRequestBuilder(tradeParams);
        //原支付订单号
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //江苏银行订单号
        requestBuilder.set(JSBBusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TRADE_NO));
        //退款流水号
        requestBuilder.set(JSBBusinessFields.OUT_REFUND_NO, MapUtils.getString(transaction, Transaction.TSN));
        //退款金额
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        requestBuilder.set(JSBBusinessFields.REFUND_AMT, amount);
        //添加交易类型
        addTradeType(requestBuilder, context);

        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayway) {
            //退款时只有B2C才需要上送设备指纹
            addTerminalFingerprint(requestBuilder, transaction, tradeParams);
        }
        //添加终端ip
        addTerminalIp(requestBuilder, extraParams);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(METHOD_REFUND, requestBuilder.build(), tradeParams, OP_REFUND, 1);
        } catch (Exception e) {
            log.error("call jsbBank refund error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_REFUND, e);
            //设置已经调过退款接口的标识
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;
        }
        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundResult(result, context, OP_REFUND);
    }

    @Override
    public String cancel(TransactionContext context) {
        throw new UnsupportedOperationException("江苏银行暂不支持撤单");
    }

    /**
     * 微信预下单
     *
     * @param context
     * @param resume
     * @return
     */
    public String wechatPreCreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map extended = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS);

        JSBRequestBuilder requestBuilder = getDefaultRequestBuilder(tradeParams);
        //付款人id(openId)
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        requestBuilder.set(JSBBusinessFields.OPEN_ID, payerUid);
        //订单号
        requestBuilder.set(JSBBusinessFields.EXT_FIELD2, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //交易金额
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        requestBuilder.set(JSBBusinessFields.TOTAL_FEE, amount);
        //商品名称
        String subject = CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT));
        requestBuilder.set(JSBBusinessFields.PRODUCT_INFO, subject);
        //添加交易类型
        requestBuilder.set(JSBBusinessFields.TRADE_TYPE, WECHAT_TRADE_TYPE_JSAPI);
        //添加ip
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        requestBuilder.set(JSBBusinessFields.MCH_IP, terminalInfo.getIp());
        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        requestBuilder.set(JSBBusinessFields.BACK_URL, notifyUrl);
        //添加门店编号
        requestBuilder.set(JSBBusinessFields.SHOP_ID, getStoreSnFromTransaction(transaction));

        //设置微信subAppId
        String wechatSubAppId = MapUtils.getString(extended, ProtocolFields.SUB_APP_ID);
        if (StringUtil.empty(wechatSubAppId)) {
            //未上送subAppId, 则使用默认值
            int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
            if (Order.SUB_PAYWAY_MINI == subPayway) {
                wechatSubAppId = MapUtils.getString(tradeParams, JSB_WECHAT_MINI_SUB_APP_ID);
            } else {
                wechatSubAppId = MapUtils.getString(tradeParams, JSB_WECHAT_SUB_APP_ID);
            }
            log.info("微信预下单未上送subAppId, 使用默认值, defaultSubAppId={}, subPayway={}", wechatSubAppId, subPayway);
        }
        requestBuilder.set(JSBBusinessFields.EXT_FIELD1, wechatSubAppId);

        //由于江苏银行未返回，故我们自己取
        transaction.put(Transaction.BUYER_UID, payerUid);
        context.getOrder().put(Order.BUYER_UID, payerUid);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(METHOD_WECHAT_PREPAY, requestBuilder.build(), tradeParams, OP_PRECREATE, 1);
        } catch (Exception e) {
            log.error("call jsbBank wechat preCreate error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildWechatPreCreateResult(result, transaction);
    }

    /**
     * 支付宝预下单
     *
     * @param context
     * @param resume
     * @return
     */
    public String alipayPreCreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);

        JSBRequestBuilder requestBuilder = getDefaultRequestBuilder(tradeParams);
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        //交易金额
        String amount = StringUtils.cents2yuan(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        requestBuilder.set(JSBBusinessFields.AMOUNT, amount);
        //商品名称
        String subject = CharacterUtil.filterSpecialCharacter(MapUtil.getString(transaction, Transaction.SUBJECT));
        requestBuilder.set(JSBBusinessFields.ORDER_DESC, subject);
        //回调通知地址
        String notifyUrl = getNotifyUrl(notifyHost, context);
        requestBuilder.set(JSBBusinessFields.BACK_URL, notifyUrl);
        //添加门店编号
        requestBuilder.set(JSBBusinessFields.SHOP_ID, getStoreSnFromTransaction(transaction));

        //支付宝用户id
        String payerUid = MapUtil.getString(extraParams, Transaction.PAYER_UID);
        requestBuilder.set(JSBBusinessFields.USER_ID, payerUid);
        //支付宝id
        requestBuilder.set(JSBBusinessFields.APPID, MapUtils.getString(tradeParams, TransactionParam.JSB_ALIPAY_APP_ID));

        //由于江苏银行未返回，故我们自己取
        transaction.put(Transaction.BUYER_UID, payerUid);
        context.getOrder().put(Order.BUYER_UID, payerUid);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(METHOD_ALIPAY_PREPAY, requestBuilder.build(), tradeParams, OP_PRECREATE, 1);
        } catch (Exception e) {
            log.error("call jsbBank alipay preCreate error: {}", e.getMessage(), e);
            setTransactionContextErrorInfo(context, OP_PRECREATE, e);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        return buildAlipayPreCreateResult(result, transaction);
    }

    @Override
    public String explainNotification(Map<String, Object> notification) {
        log.info("处理江苏银行回调通知");
        TransactionContext context = (TransactionContext) notification.get(TransactionContext.class.getName());
        notification.remove(TransactionContext.class.getName());

        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT != type) {
            return null;
        }

        //默认直接再查询一遍
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    //添加交易类型
    private void addTradeType(JSBRequestBuilder requestBuilder, TransactionContext context) {
        //交易类型（获取不到则不要上送）
        JSBTradeTypeEnum tradeTypeEnum = getTradeType(context);
        if (null != tradeTypeEnum) {
            requestBuilder.set(JSBBusinessFields.TRADE_TYPE, tradeTypeEnum.getType());
        }
    }

    /**
     * 添加设备指纹
     *
     * @param requestBuilder
     * @param transaction
     */
    private void addTerminalFingerprint(JSBRequestBuilder requestBuilder, Map<String, Object> transaction, Map<String, Object> tradeParams) {
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE != subPayway) {
            //C2B场景，设备指纹固定使用收钱吧渠道号来替代
            requestBuilder.set(JSBBusinessFields.DEVICE_NO, MapUtils.getString(tradeParams, JSB_CHANNEL_PARTNER_ID));
            return;
        }

        //B2C场景, 添加设备指纹
        Map configSnapshot = MapUtils.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String terminalFingerprint = MapUtils.getString(configSnapshot, TERMINAL_DEVICE_FINGERPRINT);
        if (isTerminalFingerprintValid(terminalFingerprint)) {
            //设备指纹有效，直接上送
            requestBuilder.set(JSBBusinessFields.DEVICE_NO, terminalFingerprint);
            return;
        }

        //设备指纹无效, 使用商户号替代
        String merchantSn = MapUtils.getString(configSnapshot, TransactionParam.MERCHANT_SN);
        requestBuilder.set(JSBBusinessFields.DEVICE_NO, merchantSn);
        log.info("terminalFingerprint invalid[{}], use merchantSn[{}] instead", terminalFingerprint, merchantSn);
    }

    //设备指纹是否有效
    private boolean isTerminalFingerprintValid(String terminalFingerprint) {
        if (StringUtils.isEmpty(terminalFingerprint)) {
            return false;
        }

        return terminalFingerprint.length() <= TERMINAL_FINGERPRINT_LENGTH_MAX;
    }

    /**
     * 添加终端ip
     *
     * @param requestBuilder
     * @param extraParams
     */
    private void addTerminalIp(JSBRequestBuilder requestBuilder, Map extraParams) {
        //设置ip：优先取终端上报ip，再取终端上报ip, 兜底localhost ip
        String userIp = MapUtil.getString(extraParams, Transaction.SQB_IP);
        String clientIp = MapUtil.getString(extraParams, Transaction.CLIENT_IP);
        String ip = !StringUtil.empty(clientIp)
                ? clientIp
                : (StringUtil.empty(userIp) ? UpayUtil.getLocalHostIp() : userIp) ;
        requestBuilder.set(JSBBusinessFields.MCH_IP, ip);
    }

    /**
     * 公共请求参数
     *
     * @param tradeParams
     */
    protected JSBRequestBuilder getDefaultRequestBuilder(Map<String, Object> tradeParams) {
        JSBRequestBuilder requestBuilder = new JSBRequestBuilder();

        LocalDateTime currentDate = LocalDateTime.now();
        requestBuilder.set(JSBProtocolFields.CREATE_DATE, getFormatDateTime(currentDate, JSBConstants.DATE_SIMPLE_FORMAT));
        requestBuilder.set(JSBProtocolFields.CREATE_TIME, getFormatDateTime(currentDate, JSBConstants.TIME_SIMPLE_FORMAT));
        requestBuilder.set(JSBProtocolFields.BIZ_DATE, getFormatDateTime(currentDate, JSBConstants.DATE_SIMPLE_FORMAT));
        requestBuilder.set(JSBProtocolFields.MSG_ID, generateMsgId());
        requestBuilder.set(JSBProtocolFields.CHANNEL_NO, JSBProtocolFields.DEFAULT_CHANNEL_NO);
        requestBuilder.set(JSBProtocolFields.PUBLIC_KEY_CODE, JSBProtocolFields.DEFAULT_PUBLIC_KEY_CODE);
        requestBuilder.set(JSBProtocolFields.VERSION, JSBProtocolFields.DEFAULT_VERSION);
        requestBuilder.set(JSBProtocolFields.CHARSET, JSBProtocolFields.DEFAULT_CHARSET);

        //商户id
        requestBuilder.set(JSBProtocolFields.PARTNER_ID, MapUtils.getString(tradeParams, JSB_PROVIDER_MCH_UUID));
        //渠道商编号
        requestBuilder.set(JSBBusinessFields.FIELD1, MapUtils.getString(tradeParams, JSB_CHANNEL_PARTNER_ID));
        return requestBuilder;
    }

    /**
     * 构建支付结果
     *
     * @param result
     * @param context
     * @return
     */
    private String buildPayResult(Map<String, Object> result, TransactionContext context) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();

        //设置付款人id、渠道订单号等
        setTradeNoBuyerInfoIfExists(result, context, OP_PAY);

        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        JSBOrderStatusEnum orderStatus = JSBOrderStatusEnum.of(MapUtil.getString(result, JSBBusinessFields.ORDER_STATUS));
        switch (orderStatus) {
            case FAILED:
                return Workflow.RC_ERROR;
            case SUCCESS:
                resolvePayFund(context, result);
                return Workflow.RC_PAY_SUCCESS;
            case PROCESSING:
            default:
                return Workflow.RC_IN_PROG;
        }
    }

    /**
     * 构建微信预下单结果
     *
     * @param result
     * @param transaction
     * @return
     */
    private String buildWechatPreCreateResult(Map<String, Object> result, Map<String, Object> transaction) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();

        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        //设置通道订单号
        String tradeNo = MapUtils.getString(result, JSBBusinessFields.ORDER_NO);
        if (StringUtils.isEmpty(MapUtils.getString(transaction, Transaction.TRADE_NO)) && !StringUtils.isEmpty(tradeNo)) {
            transaction.put(Transaction.TRADE_NO, tradeNo);
        }

        //构建微信参数
        Map<String, Object> wapPayRequest = new HashMap<>(8);
        wapPayRequest.put(JSBBusinessFields.APPID, MapUtils.getString(result, JSBBusinessFields.APPID));
        wapPayRequest.put(JSBBusinessFields.NONCE_STR, MapUtils.getString(result, JSBBusinessFields.NONCE_STR));
        wapPayRequest.put(JSBBusinessFields.TIMESTAMP, MapUtils.getString(result, JSBBusinessFields.TIMESTAMP));
        wapPayRequest.put(JSBBusinessFields.SIGN_TYPE, JSBProtocolFields.DEFAULT_SIGN_METHOD);
        wapPayRequest.put(JSBBusinessFields.PAY_SIGN, MapUtils.getString(result, JSBBusinessFields.PAY_SIGN));
        String prepayId = JSBBusinessFields.PREPAY_ID + JSBConstants.ASSIGNMENT + MapUtils.getString(result, JSBBusinessFields.PACKAGE);
        wapPayRequest.put(WapFields.PACKAGE, prepayId);
        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapPayRequest);
        return Workflow.RC_CREATE_SUCCESS;
    }

    /**
     * 构建支付宝预下单结果
     *
     * @param result
     * @param transaction
     * @return
     */
    private String buildAlipayPreCreateResult(Map<String, Object> result, Map<String, Object> transaction) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();

        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        //设置通道订单号
        String tradeNo = MapUtils.getString(result, JSBBusinessFields.ORDER_NO);
        if (StringUtils.isEmpty(MapUtils.getString(transaction, Transaction.TRADE_NO)) && !StringUtils.isEmpty(tradeNo)) {
            transaction.put(Transaction.TRADE_NO, tradeNo);
        }

        //设置支付宝订单号
        String alipayOrderNo = MapUtils.getString(result, JSBBusinessFields.ALIPAY_TRADE_NO);
        if (!StringUtils.isEmpty(alipayOrderNo)) {
            //江苏银行返回的支付宝交易号增加了两位前缀，保存时需要去掉
            alipayOrderNo = alipayOrderNo.substring(2);
        }

        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, alipayOrderNo));

        if (StringUtils.isEmpty(MapUtils.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO)) && !StringUtils.isEmpty(alipayOrderNo)) {
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, alipayOrderNo);
        }
        return Workflow.RC_CREATE_SUCCESS;
    }

    /**
     * 构建查询结果
     *
     * @param result
     * @param context
     * @param op
     * @return
     */
    private String buildQueryResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //设置付款人id、渠道订单号等
        setTradeNoBuyerInfoIfExists(result, context, op);

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();

        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        JSBOrderStatusEnum orderStatus = JSBOrderStatusEnum.of(MapUtil.getString(result, JSBBusinessFields.ORDER_STATUS));
        switch (orderStatus) {
            case FAILED:
                return Workflow.RC_ERROR;
            case SUCCESS:
                int type = MapUtil.getInteger(context.getTransaction(), Transaction.TYPE);
                if (Transaction.TYPE_PAYMENT == type) {
                    //解析交易金额、完成时间
                    resolvePayFund(context, result);
                    return Workflow.RC_PAY_SUCCESS;
                } else if (Transaction.TYPE_REFUND == type) {
                    Map<String, Object> transaction = context.getTransaction();
                    transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                    //解析交易金额
                    resolveRefundFund(context);
                    return Workflow.RC_REFUND_SUCCESS;
                } else if (Transaction.TYPE_CANCEL == type) {
                    return Workflow.RC_CANCEL_SUCCESS;
                }
                return Workflow.RC_CANCEL_SUCCESS;
            case PROCESSING:
            default:
                return Workflow.RC_IN_PROG;
        }
    }

    private String buildRefundResult(Map<String, Object> result, TransactionContext context, String op) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        //设置付款人id、渠道订单号等
        setTradeNoBuyerInfoIfExists(result, context, op);

        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();

        if (!isSuccess) {
            return Workflow.RC_ERROR;
        }

        JSBRefundOrderStatusEnum orderStatus = JSBRefundOrderStatusEnum.of(MapUtil.getString(result, JSBBusinessFields.ORDER_STATUS));
        switch (orderStatus) {
            case FAILED:
            case CLOSED:
                return Workflow.RC_ERROR;
            case SUCCESS:
                Map<String, Object> transaction = context.getTransaction();
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;
            case PROCESSING:
            default:
                //须进行退款查询，从而确定最终的退款情况
                //设置已经退款过的标识
                Map extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return queryRefundResult(context);
        }
    }

    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();

        String channelFinishDate = MapUtils.getString(result, JSBBusinessFields.FIELD3);;
        //记录通道交易完成时间
        if (Objects.isNull(transaction.get(Transaction.CHANNEL_FINISH_TIME))) {
            if (StringUtils.empty(channelFinishDate)) {
                //通道没返回交易完成时间，则设置为当前时间
                log.warn("通道未返回交易完成时间, 则使用当前时间");
                transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
            } else {
                LocalDateTime finishTime = LocalDateTimeUtil.parse(channelFinishDate, ORDER_TIME_FORMAT);
                long epochMs = finishTime != null ? toEpochMs(finishTime) : System.currentTimeMillis();
                transaction.put(Transaction.CHANNEL_FINISH_TIME, epochMs);
            }
        }

        //设置通道交易金额
        long amount = StringUtils.yuan2cents(MapUtils.getString(result, JSBBusinessFields.TOTAL_FEE));
        transaction.put(Transaction.PAID_AMOUNT, amount);
        transaction.put(Transaction.RECEIVED_AMOUNT, amount);
    }

    /**
     * 设置付款人id、渠道订单号等
     *
     * @param result
     * @param context
     * @param opFlag
     */
    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String opFlag) {
        if (MapUtils.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();
        String buyerUid = null;
        String tradeNo = null; //通道订单号（江苏银行）
        String channelNo = null;//渠道订单号（微信或支付宝订单号）
        boolean needRemoveChannelNoPrefix = false;//是否移除渠道订单号的前缀（江苏银行返回的渠道订单号前在支付和预下单时，会拼接两位数字，保存时去掉）
        if (OP_PAY.equals(opFlag)) {
            tradeNo = MapUtils.getString(result, JSBBusinessFields.ORDER_NO);
            buyerUid = MapUtils.getString(result, JSBBusinessFields.FIELD1);
            channelNo = MapUtils.getString(result, JSBBusinessFields.FIELD2);
            needRemoveChannelNoPrefix = true;
        } else if (OP_QUERY.equals(opFlag)) {
            tradeNo = MapUtils.getString(result, JSBBusinessFields.ORDER_NO);
            Map extendMap = MapUtils.getMap(result, JSBBusinessFields.EXTEND_MAP);
            buyerUid = MapUtils.getString(extendMap, JSBBusinessFields.OPEN_ID);
            channelNo = MapUtils.getString(extendMap, JSBBusinessFields.PAY_ID);
        } else if (OP_REFUND.equals(opFlag)) {
            tradeNo = MapUtils.getString(result, JSBBusinessFields.REFUND_NO);
        }

        if (StringUtil.empty(MapUtils.getString(transaction, Transaction.BUYER_UID)) && !StringUtil.empty(buyerUid)) {
            //江苏银行返回的付款人编号前拼接了数字0，保存时去掉
            buyerUid = buyerUid.substring(1);
            transaction.put(Transaction.BUYER_UID, buyerUid);
        }

        if (StringUtils.isEmpty(MapUtils.getString(transaction, Transaction.TRADE_NO)) && !StringUtils.isEmpty(tradeNo)) {
            transaction.put(Transaction.TRADE_NO, tradeNo);
        }

        Map<String, Object> extraOutFields = MapUtils.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(MapUtils.getString(extraOutFields, Transaction.CHANNEL_TRADE_NO)) && !StringUtils.isEmpty(channelNo)) {
            if (needRemoveChannelNoPrefix) {
                channelNo = channelNo.substring(2);
            }
            extraOutFields.put(Transaction.CHANNEL_TRADE_NO, channelNo);
        }
    }

    /**
     * 设置通道返回的错误信息
     *
     * @param result
     * @param context
     * @param opFlag
     */
    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String opFlag) {
        Map<String, Object> map = new LinkedHashMap<>();
        Triple<Boolean, String, String> errorInfo = getResponseErrorInfo(result);
        boolean isSuccess = errorInfo.getLeft();
        String errorCode = errorInfo.getMiddle();
        String errorMsg = errorInfo.getRight();

        map.put(JSBResponseFields.BIZ_ERROR_CODE, errorCode);
        map.put(JSBResponseFields.BIZ_ERROR_MSG, errorMsg);

        String orderStatus;
        int type = MapUtil.getIntValue(context.getTransaction(), Transaction.TYPE);
        if (Transaction.TYPE_REFUND == type) {
            orderStatus = JSBRefundOrderStatusEnum.of(MapUtil.getString(result, JSBBusinessFields.ORDER_STATUS)).getDesc();
        } else {
            orderStatus = JSBOrderStatusEnum.of(MapUtil.getString(result, JSBBusinessFields.ORDER_STATUS)).getDesc();
        }
        map.put(JSBBusinessFields.ORDER_STATUS, orderStatus);

        setTransactionContextErrorInfo(context.getTransaction(), opFlag, map, isSuccess, errorCode, errorMsg);
    }

    private Triple<Boolean, String, String> getResponseErrorInfo(Map<String, Object> result) {
        boolean isSuccess = true;
        String errorCode = MapUtil.getString(result, JSBResponseFields.RESP_CODE);//系统响应码
        String errorMsg = MapUtil.getString(result, JSBResponseFields.RESP_MSG);//系统错误信息
        String bizErrorCode = MapUtil.getString(result, JSBResponseFields.BIZ_ERROR_CODE);//业务错误码
        if (!JSBResponseUtil.isResponseSuccess(errorCode)) {
            isSuccess = false;
        } else if (!JSBResponseUtil.isBizSuccess(bizErrorCode)) {
            isSuccess = false;
            errorCode = bizErrorCode;
            errorMsg = MapUtil.getString(result, JSBResponseFields.RESP_MSG);//业务错误信息
        }
        return Triple.of(isSuccess, errorCode, errorMsg);
    }

    private String getStoreSnFromTransaction(Map<String, Object> transaction) {
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        return MapUtils.getString(configSnapshot, TransactionParam.STORE_SN);
    }

    /**
     * 获取交易类型
     *
     * @param context
     * @return
     */
    private JSBTradeTypeEnum getTradeType(TransactionContext context) {
        int payway = MapUtil.getIntValue(context.getOrder(), Order.PAYWAY);
        if (Order.PAYWAY_WEIXIN == payway) {
            return JSBTradeTypeEnum.WECHAT;
        }

        if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
            return JSBTradeTypeEnum.ALIPAY;
        }

        if (Order.PAYWAY_UNIONPAY == payway) {
            return JSBTradeTypeEnum.UNION_PAY;
        }

        return null;
    }

    private String generateMsgId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private String getPrivateKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.JSB_SQB_PRIVATE_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    private String getProviderPublicKey(Map<String, Object> tradeParams) {
        String rsaKeyId = MapUtils.getString(tradeParams, TransactionParam.JSB_PROVIDER_PUBLIC_KEY);
        return getPrivateKeyContent(rsaKeyId);
    }

    protected Map<String, Object> retryIfNetworkException(String method, Map<String, Object> request,
                                                          Map<String, Object> tradeParams, String opFlag, int retryTimes) throws Exception {
        String gatewayUrl = ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY);
        String privateKey = getPrivateKey(tradeParams);
        String providerPublicKey = getProviderPublicKey(tradeParams);
        return retryIfNetworkException(() -> jsbClient.call(gatewayUrl, method, request, privateKey, providerPublicKey), log, retryTimes, opFlag, getName());
    }
}
