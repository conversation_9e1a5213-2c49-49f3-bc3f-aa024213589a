package com.wosai.upay.workflow;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.haike.*;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.UUIDGenerator;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/***
 * @ClassName: HaikeUnionPayServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/11 6:19 PM
 */
@ServiceProvicerPriority(priority = 3)
public class HaikeUnionPayServiceProvider extends AbstractServiceProvider {

    @Resource
    protected HaikeClient haikeClient;

    public static final Logger logger = LoggerFactory.getLogger(HaikeUnionPayServiceProvider.class);
    public static final String NAME = "provider.haike.unionpay";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.HAIKE_UNION_PAY_TRADE_PARAMS);
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_HAIKE_UNION_PAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        long effectiveTotal = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        if (Transaction.TYPE_REFUND == type || (Transaction.TYPE_CANCEL == type && effectiveTotal > 0)) {
            //海科支付宝、微信统一退款接口
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("暂不支持下单");
    }

    @Override
    public String cancel(TransactionContext context) {
        //流水类型为撤单，且金额大于0 时需要走海科的退款接口
        String rcFlag = refund(context);
        if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
            return Workflow.RC_CANCEL_SUCCESS;
        } else if (Workflow.RC_IN_PROG.equals(rcFlag)){
            return Workflow.RC_RETRY;
        } else {
            return rcFlag;
        }
    }

    @Override
    public String query(TransactionContext context) {
        throw new UnsupportedOperationException("暂不支持查询");
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        throw new UnsupportedOperationException("暂不支持预下单");
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        return null;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery){
            return refundQuery(context);
        }
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, BeanUtil.getPropString(tradeParams, TransactionParam.HAIKE_UNION_PAY_AGENT_NO));
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //海科商户编号
        String providerMchId = getRealProviderMchId(tradeParams);
        builder.set(HaikeBusinessFields.MERCH_NO, providerMchId);
        //收钱吧原订单编号
        builder.set(HaikeBusinessFields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //收钱吧系统内部唯一订单号
        builder.set(HaikeBusinessFields.OUT_REFUND_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //退款金额
        builder.set(HaikeBusinessFields.REFUND_AMOUNT, BeanUtil.getPropString(transaction, Transaction.EFFECTIVE_AMOUNT));
        //实退款手续费金额
        builder.set(HaikeBusinessFields.REAL_REFUND_FEE_AMOUNT, MapUtil.getString(tradeParams, TransactionParam.FEE));

        Map<String, Object> request;
        Map<String, Object> result;

        try {
            request = builder.build();
            result = haikeClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), request, getPrivateKeyContent(BeanUtil.getPropString(tradeParams, TransactionParam.HAIKE_UNION_PAY_ACCESS_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call haike refund", ex);
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            //异常重试
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;
        }

        setTransactionContextErrorInfo(result, context, OP_REFUND);
        return buildRefundAndQueryResult(result, context, true);
    }


    public String refundQuery(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> tradeParams = getTradeParams(transaction);

        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, BeanUtil.getPropString(tradeParams, TransactionParam.HAIKE_UNION_PAY_AGENT_NO));
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //海科商户编号
        String providerMchId = getRealProviderMchId(tradeParams);
        builder.set(HaikeBusinessFields.MERCH_NO, providerMchId);

        //收钱吧退款订单号,收钱吧系统内部唯一订单号
        builder.set(HaikeBusinessFields.OUT_REFUND_NO, BeanUtil.getPropString(transaction, Transaction.TSN));

        Map<String, Object> request;
        Map<String, Object> result;
        try {
            request = builder.build();
            result = haikeClient.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND_QUERY), request, getPrivateKeyContent(BeanUtil.getPropString(tradeParams, TransactionParam.HAIKE_UNION_PAY_ACCESS_KEY)));
        } catch (Exception ex) {
            logger.error("failed to call haike refundQuery", ex);
            return Workflow.RC_IOEX;
        }

        return buildRefundAndQueryResult(result, context, false);
    }

    private String buildRefundAndQueryResult(Map<String, Object> result, TransactionContext context, boolean isRefund) {
        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (Objects.isNull(result) || result.isEmpty()) {
            return Workflow.RC_IOEX;
        }
        String resultCode = BeanUtil.getPropString(result, HaikeResponseFields.RESULT_CODE);//业务返回码
        String resultMsg = BeanUtil.getPropString(result, HaikeResponseFields.RESULT_MSG);//业务返回信息
        if (HaikeConstants.RESULT_CODE_REPEAT_NO.equals(resultCode) && HaikeConstants.RESULT_MSG_REPEAT_NO.equals(resultMsg)) {
            //若退款返回订单号重复，那就走退款查询
            if (isRefund) {
                return refundQuery(context);
            }
        } else {
            String refundStatus = BeanUtil.getPropString(result, HaikeResponseFields.REFUND_STATUS);
            if (HaikeConstants.REFUND_STATUS_SUCCESS.equals(refundStatus)) {
                //海科退款订单号
                transaction.put(Transaction.TRADE_NO, BeanUtil.getPropString(result, HaikeResponseFields.TRADE_NO));
                transaction.put(Transaction.CHANNEL_FINISH_TIME, parseTimeString(BeanUtil.getPropString(result, HaikeResponseFields.TRADE_END_TIME)));
                //实退款手续费金额
                String realRefundFee = BeanUtil.getPropString(result, HaikeBusinessFields.REAL_REFUND_FEE_AMOUNT);
                if (!StringUtils.isEmpty(realRefundFee)) {
                    extraOutFields.put(Transaction.REAL_TRADE_FEE, realRefundFee);
                }
                //支付宝通道原生参数
                String attach = BeanUtil.getPropString(result, HaikeResponseFields.ATTACH);
                Map<String, Object> providerResult = JSONObject.parseObject(attach, Map.class);

                int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
                if (Order.PAYWAY_ALIPAY == payway || Order.PAYWAY_ALIPAY2 == payway) {
                    resolveAlipayRefundFund(context, providerResult);
                } else if (Order.PAYWAY_WEIXIN == payway) {
                    resolveWeixinRefundFund(context, providerResult);
                } else if (Order.PAYWAY_UNIONPAY == payway) {
                    resolveRefundFund(context);
                }
                return Workflow.RC_REFUND_SUCCESS;
            } else if (HaikeConstants.REFUND_STATUS_UNKNOWN.equals(refundStatus)) {
                extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
                return Workflow.RC_RETRY;
            }
        }
        return Workflow.RC_ERROR;
    }

    protected void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        String resultCode = BeanUtil.getPropString(result, HaikeResponseFields.RESULT_CODE);//业务返回码
        String resultMsg = BeanUtil.getPropString(result, HaikeResponseFields.RESULT_MSG);//业务返回信息
        map.put(BusinessV2Fields.CODE, resultCode);//返回状态码
        map.put(BusinessV2Fields.MSG, resultMsg);//返回信息
        setTransactionContextErrorInfo(context.getTransaction(), key, map, HaikeConstants.RESULT_CODE_SUCCESS.equals(resultCode), resultCode, resultMsg);
    }

    public void resolveAlipayRefundFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(order, DaoConstants.CTIME));
        if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        } else {
            //部分退款，根据返回的refund_detail_item_list来计算
            Object tradeFundBill = BeanUtil.getNestedProperty(result, BusinessV2Fields.REFUND_DETAIL_ITEM_LIST);
            if (tradeFundBill instanceof String && !StringUtil.empty((String) tradeFundBill)) {
                try {
                    tradeFundBill = ((String) tradeFundBill).replaceAll("\\\\", "");
                    tradeFundBill = objectMapper.readValue(((String) tradeFundBill).getBytes(), Object.class);
                } catch (IOException e) {
                    logger.warn("parse refund_detail_item_list error", e);
                }
            }
            List<Map<String, Object>> tradeFundBills = new ArrayList();
            if (tradeFundBill instanceof List) {
                tradeFundBills.addAll((List<Map<String, Object>>) tradeFundBill);
            } else if (tradeFundBill instanceof Map) {
                tradeFundBills.add((Map<String, Object>) tradeFundBill);
            }
            long refundAmount = com.wosai.pantheon.util.MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            long receivedAmount = 0L;
            long discountChannelMchAmount = 0L;
            long paidAmount = 0L;
            if (MapUtils.isNotEmpty(result) && result.containsKey(BusinessV2Fields.SEND_BACK_FEE)) {
                receivedAmount = StringUtils.yuan2cents(com.wosai.pantheon.util.MapUtil.getString(result, BusinessV2Fields.SEND_BACK_FEE));

                discountChannelMchAmount = refundAmount - receivedAmount;

                for (Map<String, Object> bill : tradeFundBills) {
                    String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL) ? BusinessV2Fields.FUND_CHANNEL : BusinessV2Fields.FUNDCHANNEL);
                    long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                    if (!AlipayV2ServiceProvider.consumerDiscount.contains(fundChannel)) {
                        paidAmount += amount;
                    }
                }

            } else {
                logger.debug("alipay response send_back_fee not exits");
                for (Map<String, Object> bill : tradeFundBills) {
                    String fundChannel = BeanUtil.getPropString(bill, bill.containsKey(BusinessV2Fields.FUND_CHANNEL) ? BusinessV2Fields.FUND_CHANNEL : BusinessV2Fields.FUNDCHANNEL);
                    long amount = StringUtils.yuan2cents(BeanUtil.getPropString(bill, BusinessV2Fields.AMOUNT));
                    if (!AlipayV2ServiceProvider.consumerDiscount.contains(fundChannel)) {
                        paidAmount += amount;
                    }
                    if (!AlipayV2ServiceProvider.FC_MDISCOUNT.equals(fundChannel) && !AlipayV2ServiceProvider.FC_MCOUPON.equals(fundChannel) && !AlipayV2ServiceProvider.FC_TMARKETING.equals(fundChannel)) {
                        receivedAmount += amount;
                    } else {
                        discountChannelMchAmount += amount;
                    }
                }
            }
            if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT, 0) == 0) {
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }
            if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT, 0) == 0) {
                transaction.put(Transaction.RECEIVED_AMOUNT, receivedAmount);
            }
            BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, AlipayV2ServiceProvider.getAlipayV2Payments(tradeFundBills, refundAmount, discountChannelMchAmount));
        }
    }

    public void resolveWeixinRefundFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (extraOutFields == null) {
            extraOutFields = new HashMap<>();
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        Map<String, Object> payTransaction = getPayOrConsumerTransaction(transaction, com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME));
        if (BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
            //全额退款
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        } else {
            //部分退款 通过付款流水里面记录的优惠券id与退款返回的优惠券id进行关联，判断对应的支付组成退了多少钱
            List<Map<String, Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
            //目前只处理海外付款错误的情况
            long paidAmount;
            String currency = getTradeCurrency(transaction);
            if (currency != null && !TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)) {
                long refundFee = BeanUtil.getPropLong(result, ResponseFields.REFUND_FEE);
                long couponRefundFee = BeanUtil.getPropLong(result, ResponseFields.COUPON_REFUND_FEE);
                paidAmount = refundFee - couponRefundFee;
            } else {
                paidAmount = BeanUtil.getPropLong(result, ResponseFields.CASH_REFUND_FEE);
            }
            if (BeanUtil.getPropLong(transaction, Transaction.PAID_AMOUNT) == 0 && paidAmount != 0) {
                transaction.put(Transaction.PAID_AMOUNT, paidAmount);
            }

            if (payments != null) {
                List<Map<String, Object>> refundPayments = new ArrayList<>();
                for (int i = 0; i < payments.size(); i++) {
                    Map<String, Object> refundPayment = (Map<String, Object>) ((HashMap) payments.get(i)).clone();
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
                    refundPayments.add(refundPayment);
                }

                //直连微信 会返回 coupon_refund_count coupon_xxx_$n 等字段, 但是网联银联不会返回，而是返回refund_details字段
                if (MapUtils.isNotEmpty(result) && result.containsKey(ResponseFields.REFUND_DETAILS)) {
                    Object detailObject = JSONObject.parse(MapUtil.getString(result, ResponseFields.REFUND_DETAILS));

                    List<Map<String, Object>> refundDetail = new ArrayList();
                    if (detailObject instanceof List) {
                        refundDetail.addAll((List<Map<String, Object>>) detailObject);
                    }
                    if (CollectionUtils.isNotEmpty(refundDetail)) {
                        for (int i = 0; i < refundDetail.size(); i++) {
                            Map<String, Object> detail = refundDetail.get(i);
                            String couponRefundId = BeanUtil.getPropString(detail, ResponseFields.REFUND_DETAILS_PROMOTION_ID);
                            long couponRefundFee = BeanUtil.getPropLong(detail, ResponseFields.REFUND_DETAILS_REFUND_AMOUNT);
                            for (int j = 0; j < refundPayments.size(); j++) {
                                Map<String, Object> refundPayment = refundPayments.get(j);
                                String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                                if (sourceId.equals(couponRefundId)) {
                                    refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                                }
                            }
                        }
                    }
                } else {
                    int couponCount = BeanUtil.getPropInt(result, ResponseFields.COUPON_REFUND_COUNT);
                    for (int i = 0; i < couponCount; i++) {
                        String couponRefundId = BeanUtil.getPropString(result, ResponseFields.COUPON_REFUND_ID_PREFIX + "_" + i);
                        long couponRefundFee = BeanUtil.getPropLong(result, ResponseFields.COUPON_REFUND_FEE_PREFIX + "_" + i);
                        for (int j = 0; j < refundPayments.size(); j++) {
                            Map<String, Object> refundPayment = refundPayments.get(j);
                            String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                            if (sourceId.equals(couponRefundId)) {
                                refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                            }
                        }
                    }
                    // 微信预授权退款返回参数中未明确定义优惠的退款明细，只有退款金额，需要进行处理
                    if (BeanUtil.getPropInt(payTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_FREEZE) {
                        long refundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                        if (0 != refundFee && null != payments && payments.size() > 0) {
                            long totalAmount = 0l;
                            for (Map<String, Object> payment : payments) {
                                totalAmount += BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
                            }
                            if (totalAmount != 0) {
                                refundPayments.clear();
                                for (int i = 0; i < payments.size(); i++) {
                                    Map<String, Object> refundPayment = (Map<String, Object>) ((HashMap) payments.get(i)).clone();
                                    refundPayment.put(Transaction.PAYMENT_AMOUNT, Math.round((refundFee * 1d / totalAmount) * BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT)));
                                    refundPayments.add(refundPayment);
                                }
                            }
                        }
                    }
                }

                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String, Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if (!Payment.TYPE_DISCOUNT_SET.contains(type)) {
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, paidAmount);
                        break;
                    }
                }
                BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
                //免充值下才会有coupon_type_0, settlement_refund_fee,settlement_total_fee字段， 商户实收金额通过 effective_amount - 免充值金额来计算
                long settlementRefundFee = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
                for (int j = 0; j < refundPayments.size(); j++) {
                    Map<String, Object> refundPayment = refundPayments.get(j);
                    String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                    if (Payment.TYPE_DISCOUNT_CHANNEL_MCH.equals(type)) {
                        settlementRefundFee = settlementRefundFee - BeanUtil.getPropLong(refundPayment, Transaction.PAYMENT_AMOUNT);
                    }
                }
                if (BeanUtil.getPropLong(transaction, Transaction.RECEIVED_AMOUNT) == 0 && settlementRefundFee != 0) {
                    transaction.put(Transaction.RECEIVED_AMOUNT, settlementRefundFee);
                }
            }

        }

    }

    public void resolveRefundFund(TransactionContext context){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }

    /**
     * 获取通道商户号
     * @param tradeParams
     * @return
     */
    private String getRealProviderMchId(Map tradeParams) {
        String merchNo;
        //由于存在品牌挂靠，商户的交易参数被品牌的参数覆盖，但是在同步交易时，仍然需要使用商户的海科商户号进行同步
        boolean isAffiliated = MapUtils.getBoolean(tradeParams, TransactionParam.IS_AFFILIATED, false);
        if (!isAffiliated) {
            merchNo = MapUtils.getString(tradeParams, TransactionParam.HAIKE_UNION_PAY_PROVIDER_MCH_ID);
        } else {
            merchNo = MapUtils.getString(tradeParams, TransactionParam.ORIGINAL_PROVIDER_MCH_ID);
        }

        return merchNo;
    }
}
