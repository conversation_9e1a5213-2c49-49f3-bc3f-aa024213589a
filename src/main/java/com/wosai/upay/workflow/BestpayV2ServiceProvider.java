package com.wosai.upay.workflow;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.bestpay.*;
import com.wosai.mpay.exception.*;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;

/**
 * 翼支付3.0版本的支付通道
 *
 * <AUTHOR>
 * @date 2020/04/29
 */
public class BestpayV2ServiceProvider extends AbstractServiceProvider {
    private static final Logger logger = LoggerFactory.getLogger(BestpayServiceProvider.class);

    private String notifyHost;
    public static final String NAME = "provider.bestpay.v2";
    @Autowired
    private BestpayV2Client client;

    public BestpayV2ServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(BestpayConstants.DATE_TIME_FORMAT2);
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessV2Fields.TRADE_AMT, BusinessV2Fields.CCY));
    }
    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        if (Order.PAYWAY_BESTPAY == MapUtil.getIntValue(transaction, Transaction.PAYWAY)
                && Order.SUB_PAYWAY_BARCODE == MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY)) {
            Map<String, Object> tradeParams = getTradeParams(transaction);
            return tradeParams != null && TransactionParam.SIGN_TYPE_RSA.equals(MapUtil.getString(tradeParams, TransactionParam.SIGN_TYPE));
        }
        return false;
    }

    @Override
    public Integer getProvider() {
        return null;
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        //订单号
        builder.set(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //交易金额
        builder.set(BusinessV2Fields.TRADE_AMT, transaction.get(Transaction.EFFECTIVE_AMOUNT)+"");
        //订单标题
        builder.set(BusinessV2Fields.SUBJECT, BeanUtil.getPropString(transaction, Transaction.SUBJECT));
        //支付授权码,即付款码
        builder.set(BusinessV2Fields.AUTH_CODE, BeanUtil.getPropString(extraParams, Transaction.BARCODE));
        //币种
        builder.set(BusinessV2Fields.CCY, BestpayConstants.TRANSACTION_CURRENCY_CODE);
        //请求日期
        builder.set(BusinessV2Fields.REQUEST_DATE, formatTimeString(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
        //交易渠道
        builder.set(BusinessV2Fields.TRADE_CHANNEL, BestpayConstants.TRADE_CHANNEL);
        //接入方标识码
        builder.set(BusinessV2Fields.ACCESS_CODE, BestpayConstants.ACCESS_CODE);
        //操作人，如：商户号或者手机号码
        builder.set(BusinessV2Fields.OPERATOR, BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID));
        //回调地址(非必传)
        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(BusinessV2Fields.NOTIFY_URL, notifyUrl);
        }
        //商户门店编号
        String storeCode = BeanUtil.getPropString(config, TransactionParam.BESTPAY_STORE_ID);
        if(StringUtil.empty(storeCode)){
            storeCode = BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID);
        }
        builder.set(BusinessV2Fields.STORE_CODE, storeCode);
        carryOverExtendedParams(extended, builder);
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), getPrivateKeyContent((String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY)), builder.build());
            setTransactionContextErrorInfo(result, context, OP_PAY);
            if (context.getApiVer() == 1) {
                transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call bestpay pay", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in bestpay pay", ex);
            if(ex instanceof MpayApiSendError || ex instanceof MpayApiReadError){
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_IOEX;
        } catch (JsonProcessingException ex) {
            logger.error("encountered JsonProcessingex in bestpay pay", ex);
            return Workflow.RC_IOEX;
        }

        boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseV2Fields.SUCCESS);
        if(!isSuccess){
            String errorCode = BeanUtil.getPropString(result, ResponseV2Fields.ERROR_CODE);
            if(BestpayConstants.BEST_PAY_RESULT_ERROR_CODE_FAIL_LIST.contains(errorCode)) {
                return Workflow.RC_TRADE_CANCELED;
            }
            if(BestpayConstants.BEST_PAY_RESULT_ERROR_CODE_UNKNOWN_LIST.contains(errorCode)){
                return Workflow.RC_IN_PROG;
            }
            return Workflow.RC_PROTOCOL_ERROR;
        }
        //交易状态
        String transStatus = (String) BeanUtil.getNestedProperty(result, ResponseV2Fields.RESPONSE_KEY_TRADE_STATUS);
        if(BestpayConstants.TRANS_STATUS_TRADE_V2.equalsIgnoreCase(transStatus)){
            Map<String,Object> resultInfo = (Map<String, Object>) result.get(ResponseV2Fields.RESULT);
            //付款成功
            transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(resultInfo, ResponseV2Fields.BUYER_LOGIN_NO));
            transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(resultInfo, ResponseV2Fields.BUYER_LOGIN_NO));
            transaction.put(Transaction.TRADE_NO,  BeanUtil.getPropString(resultInfo, ResponseV2Fields.TRADE_NO));
            transaction.put(Transaction.CHANNEL_FINISH_TIME, formatDate(BeanUtil.getPropString(resultInfo, ResponseV2Fields.TRADE_FINISHED_DATE)));
            resolveFunds(resultInfo , context);
            return Workflow.RC_PAY_SUCCESS;
        }else if(BestpayConstants.TRANS_STATUS_INPROG_V2.equalsIgnoreCase(transStatus)){
            return Workflow.RC_IN_PROG;
        }else if(BestpayConstants.TRANS_STATUS_FAIL_V2.equalsIgnoreCase(transStatus)){
            return Workflow.RC_ERROR;
        }else {
            return Workflow.RC_ERROR;
        }
    }

    @Override
    public String cancel(TransactionContext context) {
        String rcFlag = refund(context);
        if(Workflow.RC_REFUND_SUCCESS.equals(rcFlag)){
            return Workflow.RC_CANCEL_SUCCESS;
        }else{
            return rcFlag;
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        //订单号
        builder.set(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //交易日期
        builder.set(BusinessV2Fields.TRADE_DATE, formatTimeString(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), getPrivateKeyContent((String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY)), builder.build());
            setTransactionContextErrorInfo(result, context, OP_QUERY);
        } catch (MpayException ex) {
            logger.error("failed to call bestpay query", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            if(ex instanceof MpayApiSendError || ex instanceof MpayApiReadError){
                return Workflow.RC_IN_PROG;
            }
            logger.error("encountered ioex in bestpay query", ex);
            return Workflow.RC_IOEX;
        } catch (JsonProcessingException ex) {
            logger.error("encountered jsonprocessingex in bestpay query", ex);
            return Workflow.RC_IOEX;
        }

        if(!BeanUtil.getPropBoolean(result, ResponseV2Fields.SUCCESS,false)){
            return Workflow.RC_ERROR;
        }

        String errorCode = BeanUtil.getPropString(result, ResponseV2Fields.ERROR_CODE);
        //订单不存在
        if(BestpayConstants.RESULT_ERROR_CODE_ORDER_NO_EXIST.equals(errorCode)){
            return Workflow.RC_ERROR;
        }

        String transStatus = (String) BeanUtil.getNestedProperty(result, ResponseV2Fields.RESPONSE_KEY_TRADE_STATUS);
        if(BestpayConstants.TRANS_STATUS_TRADE_V2.equalsIgnoreCase(transStatus)){
            // True代表已退款 False表示未退款 SECTION表示部分退款
            String refundFlag = BeanUtil.getPropString(result, String.format("%s.%s", ResponseV2Fields.RESULT, ResponseV2Fields.REFUND_FLAG));
            if(refundFlag.equals(BestpayConstants.REFUND_FALSE)){
                Map<String,Object> resultInfo = (Map<String, Object>) result.get(ResponseV2Fields.RESULT);
                //付款成功
                transaction.put(Transaction.BUYER_UID, BeanUtil.getPropString(resultInfo, ResponseV2Fields.BUYER_LOGIN_NO));
                transaction.put(Transaction.BUYER_LOGIN, BeanUtil.getPropString(resultInfo, ResponseV2Fields.BUYER_LOGIN_NO));
                transaction.put(Transaction.TRADE_NO,  BeanUtil.getPropString(resultInfo, ResponseV2Fields.TRADE_NO));
                transaction.put(Transaction.CHANNEL_FINISH_TIME, formatDate(BeanUtil.getPropString(resultInfo, ResponseV2Fields.TRADE_FINISHED_DATE)));
                resolveFunds(resultInfo , context);
                return Workflow.RC_PAY_SUCCESS;
            }
        }else if(BestpayConstants.TRANS_STATUS_INPROG_V2.equalsIgnoreCase(transStatus)){
            return Workflow.RC_IN_PROG;
        }

        return Workflow.RC_ERROR;
    }

    @Override
    public String refund(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        //退款流水号
        builder.set(BusinessV2Fields.OUT_REQUEST_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //订单号
        builder.set(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //退款金额
        builder.set(BusinessV2Fields.REFUND_AMT, BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        //发送请求的时间
        builder.set(BusinessV2Fields.REQUEST_DATE, formatTimeString(System.currentTimeMillis()));
        //原交易日期
        builder.set(BusinessV2Fields.ORIGINAL_TRADE_DATE, formatTimeString(BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
        //操作人,如：商户号或者手机号码
        builder.set(BusinessV2Fields.OPERATOR, BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID));
        //交易渠道
        builder.set(BusinessV2Fields.TRADE_CHANNEL, BestpayConstants.TRADE_CHANNEL);
        //接入方标识码
        builder.set(BusinessV2Fields.ACCESS_CODE, BestpayConstants.ACCESS_CODE);
        //币种
        builder.set(BusinessV2Fields.CCY, BestpayConstants.TRANSACTION_CURRENCY_CODE);
        //回调地址(非必传)
        String notifyUrl = getNotifyUrl(notifyHost, context);
        if (notifyUrl != null) {
            builder.set(BusinessV2Fields.NOTIFY_URL, notifyUrl);
        }
        Map<String, Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_REFUND), getPrivateKeyContent((String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY)), builder.build());
            setTransactionContextErrorInfo(result, context, OP_REFUND);
            if (context.getApiVer() == 1) {
                transaction.put(Transaction.PROVIDER_RESPONSE, result);
            }
        } catch (MpayException ex) {
            logger.error("failed to call bestpay refund", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            if(ex instanceof MpayApiConnectError || ex instanceof MpayApiSendError){
                return Workflow.RC_RETRY;
            }
            logger.error("encountered ioex in bestpay refund", ex);
            return Workflow.RC_IOEX;
        } catch (JsonProcessingException ex) {
            logger.error("encountered jsonprocessingex in bestpay query", ex);
            return Workflow.RC_IOEX;
        }

        boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseV2Fields.SUCCESS,false);
        //退款失败
        if(!isSuccess){
            return Workflow.RC_SYS_ERROR;
        }

        //交易状态
        String transStatus = (String) BeanUtil.getNestedProperty(result, ResponseV2Fields.RESPONSE_KEY_TRADE_STATUS);
        if(BestpayConstants.TRANS_STATUS_TRADE_V2.equalsIgnoreCase(transStatus)){
            return Workflow.RC_REFUND_SUCCESS;
        }else if(BestpayConstants.TRANS_STATUS_FAIL_V2.equalsIgnoreCase(transStatus)){
            return Workflow.RC_ERROR;
        }else {
            return Workflow.RC_ERROR;
        }
    }

    /**退款查询接口*/
    public String refundQuery(TransactionContext context){
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(context.getTransaction());
        RequestBuilder builder = getDefaultRequestBuilder(context);
        //订单号
        builder.set(BusinessV2Fields.OUT_TRADE_NO, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        //退款请求流水号
        builder.set(BusinessV2Fields.OUT_REQUEST_NO, BeanUtil.getPropString(transaction, Transaction.TSN));
        //退款请求日期
        builder.set(BusinessV2Fields.REFUND_DATE, formatTimeString(System.currentTimeMillis()));

        Map<String,String> request = builder.build();
        Map<String,Object> result;
        try {
            result = client.call(ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_REFUND_QUERY),getPrivateKeyContent((String) config.get(TransactionParam.BESTPAY_MERCHANT_KEY)) ,request);
        } catch (MpayException ex) {
            logger.error("failed to call bestpay refundQuery", ex);
            return Workflow.RC_IOEX;
        } catch (MpayApiNetworkError ex) {
            logger.error("encountered ioex in bestpay refundQuery", ex);
            return Workflow.RC_IOEX;
        } catch (JsonProcessingException ex) {
            logger.error("encountered jsonprocessingex in bestpay query", ex);
            return Workflow.RC_IOEX;
        }

        boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseV2Fields.SUCCESS,false);
        if(!isSuccess){
            return Workflow.RC_SYS_ERROR;
        }
        List<Map<String, Object>> refundLists = (List<Map<String, Object>>) BeanUtil.getProperty(result, ResponseV2Fields.RESULT);
        String tsn = BeanUtil.getPropString(transaction, Transaction.TSN);
        for (Map map: refundLists){
            //交易状态
            String requestNo = BeanUtil.getPropString(map, BusinessV2Fields.OUT_REQUEST_NO);
            String transStatus = BeanUtil.getPropString(map, ResponseV2Fields.TRADE_STATUS);
            if (tsn.equals(requestNo)) {
                if (BestpayConstants.TRANS_STATUS_TRADE_V2.equals(transStatus)){
                    return Workflow.RC_REFUND_SUCCESS;
                } else if (BestpayConstants.TRANS_STATUS_FAIL_V2.equalsIgnoreCase(transStatus)) {
                    return Workflow.RC_ERROR;
                } else if (BestpayConstants.TRANS_STATUS_INPROG_V2.equalsIgnoreCase(transStatus)) {
                    return Workflow.RC_IN_PROG;
                }
            }
        }
        return null;
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        return null;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        Long type = BeanUtil.getPropLong(transaction, Transaction.TYPE);
        if(Transaction.TYPE_PAYMENT == type){
            return Workflow.RC_PAY_SUCCESS.equals(query(context))?Workflow.RC_PAY_SUCCESS:null;
        }else if (Transaction.TYPE_REFUND == type){
            return Workflow.RC_REFUND_SUCCESS.equals(refundQuery(context))?Workflow.RC_REFUND_SUCCESS:null;
        }
        return null;
    }


    @Override
    public Map<String,Object> getTradeParams(Map<String, Object> transaction){
        return getTradeParams(transaction, TransactionParam.BESTPAY_TRADE_PARAMS);
    }



    private void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context, String key){
        boolean isSuccess = BeanUtil.getPropBoolean(result, ResponseV2Fields.SUCCESS);
        String detailErrorCode = BeanUtil.getPropString(result, ResponseV2Fields.ERROR_CODE);
        String detailErrorDes = BeanUtil.getPropString(result, ResponseV2Fields.ERROR_MSG);
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        map.put(ResponseV2Fields.SUCCESS, isSuccess);
        map.put(ResponseV2Fields.ERROR_CODE, detailErrorCode);
        map.put(ResponseV2Fields.ERROR_MSG, detailErrorDes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, detailErrorCode, detailErrorDes);
    }

    /**
     * 解析返回金额相关信息
     * @param context
     */
    private void resolveFunds(Map<String, Object> resultInfo, TransactionContext context) {
        long payAmt = BeanUtil.getPropLong(resultInfo, ResponseV2Fields.PAY_AMT);                   // 订单支付金额
        long discountAmt = BeanUtil.getPropLong(resultInfo, ResponseV2Fields.DISCOUNT_AMT);			// 订单优惠金额，用户使代订单优惠金额
        context.getTransaction().put(Transaction.PAID_AMOUNT, payAmt);
        context.getTransaction().put(Transaction.RECEIVED_AMOUNT, BeanUtil.getPropLong(context.getTransaction(), Transaction.EFFECTIVE_AMOUNT) - discountAmt);

        context.getOrder().put(Order.TOTAL_DISCOUNT, discountAmt);
        context.getOrder().put(Order.NET_DISCOUNT, discountAmt);
    }


    public  RequestBuilder getDefaultRequestBuilder (TransactionContext context) {
        RequestBuilder builder = new RequestBuilder();
        Map config = getTradeParams(context.getTransaction());
        builder.set(BusinessV2Fields.MERCHANT_NO, BeanUtil.getPropString(config, TransactionParam.BESTPAY_MERCHANT_ID));
        return builder;
    }


    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }


    private void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key)) {
                continue;
            }
            //商品详情列表，可做单品营销使用（非必传）
            if(UpayConstant.GOODS_DETAIL.equals(key) ){
                String value = JSON.toJSON(extendedParam.getValue()).toString();
                if (value != null) {
                    builder.set(UpayConstant.BESTPAY_V2_GOODS_DETAIL, value);
                }
            }else{
                Object value = extendedParam.getValue();
                if (value != null) {
                    builder.set(key, value.toString());
                }
            }
        }
    }

    public void setSubMchIdAndLedgerDetail(RequestBuilder builder, Map<String, Object> config, Map<String, Object> transaction){
        String subMchId = BeanUtil.getPropString(config, TransactionParam.BESTPAY_SUB_MERCHANT_ID);
        if (!StringUtil.empty(subMchId)){
            builder.set(BusinessFields.SUB_MERCHANT_ID, subMchId); // subMerchantId 非必传
            if (!StringUtil.empty(BeanUtil.getPropString(config, TransactionParam.BESTPAY_LEDGERDETAIL))){
                //分账是否设置的优先级策略： 1.商户是否有自己传过来；如果是，直接透传；如果否，判断是否有subMchId,并且配置中设置了需要分账。如果true,则100%分给该subMchId
                builder.set(BusinessFields.LEDGER_DETAIL,StringUtils.join(subMchId, ":", transaction.get(Transaction.EFFECTIVE_AMOUNT)));
            }
        }
    }

    //将格式为"yyyy-MM-dd'T'HH:mm:ss.SSS"的时间转化为时间戳
    public long formatDate(String date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(BestpayConstants.DATE_TIME_FORMAT_LONG);
        Date da = null;
        try {
            da = simpleDateFormat.parse(date);
            return da.getTime();
        } catch (ParseException e) {
            logger.error("encountered ParseException in format date", e);
            return System.currentTimeMillis();
        }
    }
}
