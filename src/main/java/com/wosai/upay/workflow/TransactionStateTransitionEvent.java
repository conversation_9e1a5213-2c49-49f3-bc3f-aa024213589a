package com.wosai.upay.workflow;

public class TransactionStateTransitionEvent extends TransactionStateEvent {
    public static final String NAME = "transaction.state.transition";

    private String outcome; 

    public TransactionStateTransitionEvent(int key, TransactionContext context, String outcome) {
        super(key, context);
        this.outcome = outcome;
    }

    @Override
    public String getName() {
        return NAME;
    }

    public String getOutcome() {
        return outcome;
    }
}
