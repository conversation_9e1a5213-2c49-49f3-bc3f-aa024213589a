package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.hxbank.HXBankBusinessFields;
import com.wosai.mpay.api.hxbank.HXBankConstants;
import com.wosai.mpay.api.psbcbank.PSBCResponseFields;
import com.wosai.mpay.api.unionqrcode.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Semaphore;

/***
 * @ClassName: HaikeUnionPayUnionQRCodeServiceProvider
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/12/21 10:31 AM
 */
public class HaikeUnionPayUnionQRCodeServiceProvider extends AbstractServiceProvider {

    public static final Logger logger = LoggerFactory.getLogger(HaikeUnionPayUnionQRCodeServiceProvider.class);

    private static final int DEFAULT_TIME_EXPIRE_SECOND = 4 * 60 ; // 订单默认过期时间设定为4分钟

    public static final String NAME = "provider.haike.unionpay.union.qrcode";

    protected static final int NOTIFY_URL_LIMIT = 200;

    @Resource
    private UnionPayQrCodeClient client;
    private static final int retryTimes = 3;
    private String notifyHost;

    public HaikeUnionPayUnionQRCodeServiceProvider() {
        this.dateFormat = new SafeSimpleDateFormat(UnionPayQRCodeConstants.ORDER_TIME_DATE_TIME_FORMAT);
        this.concurrencySemaphore = new Semaphore(10); // b2c交易延迟走查询最大并发数为10
        extendedFilterFields = new HashSet<String>(Arrays.asList(BusinessFields.CURRENCY_CODE, BusinessFields.TXN_AMT));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getProvider() {
        return Order.PROVIDER_HAIKE_UNION_PAY;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {
        long effectiveTotal = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT, 0);
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        //退款和 交易完成后的撤单直接走海科通道
        if (Transaction.TYPE_REFUND == type || (Transaction.TYPE_CANCEL == type && effectiveTotal > 0)) {
            return false;
        }
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(Order.PAYWAY_UNIONPAY == payway ) {
            return getTradeParams(transaction) != null;
        }
        return false;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.HAIKE_UNION_PAY_TRADE_PARAMS);
    }

    @Override
    protected int getNotifyUrlLimit() {
        return NOTIFY_URL_LIMIT;
    }

    public String getNotifyHost() {
        return notifyHost;
    }

    public void setNotifyHost(String notifyHost) {
        this.notifyHost = notifyHost;
    }

    @Override
    public Map<String, Object> queryUserInfo(Map<String, Object> transaction) {

        Map<String,Object> extraParams = MapUtils.getMap(transaction, Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            return null;
        }
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.VERSION, UnionPayQRCodeConstants.DEFAULT_VERSION);
        builder.set(ProtocolFields.CERT_ID, MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_CERT_ID));
        builder.set(ProtocolFields.SIGN_TYPE, UnionPayQRCodeConstants.DEFAULT_SIGN_TYPE);
        builder.set(ProtocolFields.ACQ_CODE, MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_ACQ_CODE));
        builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_QUERY_USER_ID);
        //授权码
        builder.set(HXBankBusinessFields.USER_AUTH_CODE, MapUtil.getString(extraParams, Transaction.USER_AUTH_CODE));
        //银联支付标识
        builder.set(HXBankBusinessFields.APP_UP_IDENTIFIER, MapUtil.getString(extraParams, Transaction.APP_UP_IDENTIFIER));

        Map<String,Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY), builder.build(), 1, ApolloConfigurationCenterUtil.GATEWAY_OP_UNION_USERID_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call haike queryUserInfo", ex);
            //当连接错误时，返回错误，退出流程， 其他错误，比如send read error的时候, 进入轮询
            return CollectionUtil.hashMap(PSBCResponseFields.RESP_DESC, ex.getMessage());
        }
        String userId = MapUtil.getString(result, BusinessFields.USER_ID);
        if(StringUtils.isEmpty(userId)){
            return null;
        }
        return CollectionUtil.hashMap(BusinessFields.USER_ID, userId);
    }

    @Override
    public String pay(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder requestBuilder = getDefaultRequestBuilder(context);
        Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String merId = MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MER_ID);
        String merCatCode = MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MCH_CAT_CODE);
        requestBuilder.set(ProtocolFields.MER_ID, merId);
        //若merCatCode为空时，取银联商户号固定取8~11位，例：83339405411HHHH  送 5411
        merCatCode = StringUtil.empty(merCatCode) && merId.length() >= 11 ? merId.substring(7, 11) : merCatCode;
        requestBuilder.set(ProtocolFields.MER_CAT_CODE, merCatCode);
        // 汉字*2+其它字符*1的长度不能大于40，长度超过20后直接截取
        String merName = MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MER_NAME);
        if(!StringUtil.empty(merName) && merName.length() > 20) {
            merName = merName.substring(0, 20);
        }
        requestBuilder.set(ProtocolFields.MER_NAME, merName);
        requestBuilder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE);
        requestBuilder.set(BusinessFields.QR_NO, MapUtil.getString(extraParams, Transaction.BARCODE));
        requestBuilder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        requestBuilder.set(BusinessFields.TXN_AMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        requestBuilder.set(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        requestBuilder.set(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        Map<String, Object> configSnapshot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
        requestBuilder.set(BusinessFields.AREA_INFO, MapUtil.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if (upayOrderNumber != null) {
            requestBuilder.set(BusinessFields.BACK_URL, getCommonNotifyUrl(notifyHost));
        }
        if (extended != null) {
            carryOverExtendedParams(extended, requestBuilder, UnionPayQRCodeConstants.PAY_ALLOWED_FIELDS);
        }

        //259终端信息上送
        setTerminalInfo(context, requestBuilder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_PAY), requestBuilder.build(), 1, OP_PAY);
        } catch (Exception ex) {
            logger.error("failed to call haike unionpay open pay", ex);
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_PAY);
        if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(responseCode)) {
            //由于海科是对接的银联的异步应答模式， 故均处理为需要进一步查询订单的实际状态
            return unionPayB2cTradeProcess(context);
        }
        if (UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)) {
            return Workflow.RC_TRADE_CANCELED;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String cancel(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        if(subPayway == Order.SUB_PAYWAY_BARCODE){
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE_CANCEL);
        }else{
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_CANCEL);
        }
        builder.set(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtil.getLongValue(transaction, DaoConstants.CTIME))));
        builder.set(BusinessFields.ORIG_ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.ORIG_ORDER_TIME, dateFormat.format(new Date(MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_CANCEL), builder.build(), 1, OP_CANCEL);
        } catch (Exception ex) {
            logger.error("failed to call haike unionpay open cancel", ex);
            setTransactionContextErrorInfo(context, OP_CANCEL, ex);
            //异常进行重试
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_CANCEL);
        String responseCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        switch (responseCode){
            case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                //success
                return Workflow.RC_CANCEL_SUCCESS;
            case UnionPayQRCodeConstants.RESP_CODE_SYSTEM_ERROR:
            case UnionPayQRCodeConstants.RESP_CODE_ORDER_UNKNOWN:
            case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                //retry
                return Workflow.RC_RETRY;
            default:
                return Workflow.RC_ERROR;
        }
    }

    @Override
    public String query(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if (Order.SUB_PAYWAY_BARCODE == subPayway) {
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_BARCODE_QUERY);
        } else {
            builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_QRCODE_WAP_QUERY);
        }
        int transactionType = MapUtil.getIntValue(transaction, Transaction.TYPE);
        builder.set(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.TSN));
        builder.set(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), OP_QUERY), builder.build(), retryTimes, OP_QUERY);
        } catch (Exception ex) {
            logger.error("failed to call haike unionpay open query", ex);
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        String originResponseCode = MapUtil.getString(result, ResponseFields.ORIG_RESP_CODE, "");
        setTransactionContextErrorInfo(result, context, OP_QUERY);
        setTradeNoBuyerInfoIfExists(result, context);
        if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(responseCode)) {
            switch (originResponseCode) {
                case UnionPayQRCodeConstants.RESP_CODE_SUCCESS:
                    if (Transaction.TYPE_REFUND == transactionType) {
                        resolveRefundFund(context);
                        return Workflow.RC_REFUND_SUCCESS;
                    } else if (Transaction.TYPE_PAYMENT == transactionType) {
                        resolvePayFund(result, context);
                        transaction.put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                        return Workflow.RC_PAY_SUCCESS;
                    } else if (Transaction.TYPE_CANCEL == transactionType){
                        return Workflow.RC_CANCEL_SUCCESS;
                    }
                case UnionPayQRCodeConstants.RESP_CODE_ORDER_UNKNOWN:
                case UnionPayQRCodeConstants.RESP_CODE_BUSY_RETRY_LATER:
                case UnionPayQRCodeConstants.RESP_CODE_TRADE_NOT_EXIST:
                case UnionPayQRCodeConstants.RESP_CODE_TRADE_NOT_EXIST_OR_ERROR:
                case UnionPayQRCodeConstants.RESP_CODE_TRADE_OPERATE_LIMIT:
                    return Workflow.RC_IN_PROG;
                default:
                    if (Transaction.TYPE_PAYMENT == transactionType && UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)) {
                        return Workflow.RC_TRADE_CANCELED;
                    }
                    return Workflow.RC_ERROR;
            }
        } else if (UnionPayQRCodeConstants.PAY_RESP_CODE_FAIL_SET.contains(responseCode)
                && !UnionPayQRCodeConstants.RESP_CODE_TRADE_RISK_FAIL.equals(responseCode)
                && !UnionPayQRCodeConstants.RESP_CODE_NOT_IN_BUSINESS_TIME.equals(responseCode)) {
            //如果是因为非风控或者限流导致的查单失败，则返回错误
            return Workflow.RC_ERROR;
        }
        return Workflow.RC_IN_PROG;
    }

    @Override
    public String refund(TransactionContext context) {
        //退款走海科直连
        throw new UnsupportedOperationException("暂不支持退款");
    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {
        Map<String, Object> transaction = context.getTransaction();
        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        if(Order.SUB_PAYWAY_QRCODE == subPayway){
            throw new UnsupportedOperationException("不支持此支付方式");
        }else{
            return wapPreCreate(context);
        }
    }

    private String wapPreCreate(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> config = getTradeParams(transaction);
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        RequestBuilder builder = getDefaultRequestBuilder(context);
        builder.set(ProtocolFields.ENCRYPT_CERT_ID, MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_CERT_ID));
        builder.set(BusinessFields.REQ_TYPE, UnionPayQRCodeConstants.REQ_TYPE_WAP);
        builder.set(BusinessFields.ORDER_NO, MapUtil.getString(transaction, Transaction.ORDER_SN));
        builder.set(BusinessFields.ORDER_TIME, dateFormat.format(new Date(MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME))));
        builder.set(BusinessFields.ORDER_TYPE, UnionPayQRCodeConstants.ORDER_TYPE_COMMON_CONSUME);
        builder.set(BusinessFields.PAYEE_INFO, getPayeeInfo(context));
        builder.set(BusinessFields.ORDER_DESC, MapUtil.getString(transaction, Transaction.SUBJECT));
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        builder.set(BusinessFields.CUSTOMER_IP, terminalInfo.getIp());
        builder.set(BusinessFields.PAYMENT_VALID_TIME, DEFAULT_TIME_EXPIRE_SECOND + "");
        builder.set(BusinessFields.ORDERTIMEOUT, dateFormat.format(new Date(MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME) + DEFAULT_TIME_EXPIRE_SECOND * 1000)));
        String upayOrderNumber = context.getWorkflow().getManager().upayOrderNumber(context);
        if (upayOrderNumber != null) {
            builder.set(BusinessFields.BACK_URL, getNotifyUrl(notifyHost, context));
        }
        Map<String, Object> configSnapshot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
        builder.set(BusinessFields.AREA_INFO, MapUtil.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        builder.set(BusinessFields.TXN_AMT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT) + "");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        String payerUid = (String) BeanUtil.getNestedProperty(transaction, KEY_PAYER_UID);
        if (payerUid != null) {
            builder.set(BusinessFields.USER_ID, payerUid);
        }
        carryOverExtendedParams(extended, builder, UnionPayQRCodeConstants.WAP_ALLOWED_FIELDS);
        builder.set(BusinessFields.QRCODE_TYPE, UnionPayQRCodeConstants.QRCODE_TYPE_STATIC);
        builder.set(BusinessFields.USER_AUTH_CODE, MapUtil.getString(extended, Transaction.EXTENDED_USER_AUTH_CODE));
        builder.set(BusinessFields.QRCODE, MapUtil.getString(extended, Transaction.QR_CODE));

        //259终端信息上送
        setTerminalInfo(context, builder);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(config, ApolloConfigurationCenterUtil.getProviderGateway(getName(), ApolloConfigurationCenterUtil.GATEWAY_OP_WAP), builder.build(), 1, "wap create");
        } catch (Exception ex) {
            logger.error("failed to call haike unionpay open wap create", ex);
            setTransactionContextErrorInfo(context, "wap create", ex);
            return Workflow.RC_IOEX;
        }
        if (result == null) {
            return Workflow.RC_IOEX;
        }
        String responseCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        setTransactionContextErrorInfo(result, context, OP_PRECREATE);
        if (UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(responseCode)) {
            //success
            Map<String, Object> wapRequest = new HashMap<String, Object>() {{
                put(ResponseFields.REDIRECTURL, MapUtil.getString(result, ResponseFields.REDIRECTURL));
            }};
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            extraOutFields.put(Transaction.WAP_PAY_REQUEST, wapRequest);
            return Workflow.RC_CREATE_SUCCESS;
        }
        return Workflow.RC_ERROR;
    }

    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        providerNotification.remove(TransactionContext.class.getName());
        Map<String, Object> transaction = context.getTransaction();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT){
            return null;
        }
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }


    public Map<String, Object> retryIfNetworkException(Map<String,Object> config, String url, Map<String,Object> request, int times, String opFlag) throws Exception {
        Exception exception = null;
        for (int i = 0; i< times; ++i) {
            try {
                return client.call(url, UnionPayQrCodeClient.SIGN_TYPE_HAIKE, getPrivateKeyContent(MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_ACCESS_KEY)), request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in unionpay online {}", opFlag, ex);
            }
        }
        logger.error(String.format("still network i/o error after retrying %d times.", times));
        throw exception;
    }

    public RequestBuilder getDefaultRequestBuilder(TransactionContext context){

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CERT_ID, MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_CERT_ID));
        builder.set(ProtocolFields.VERSION, UnionPayQRCodeConstants.DEFAULT_VERSION);
        builder.set(ProtocolFields.SIGN_TYPE, UnionPayQRCodeConstants.DEFAULT_SIGN_TYPE);
        builder.set(ProtocolFields.ACQ_CODE, MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_ACQ_CODE));

        String prnInsIdCd = MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_PNR_INS_ID_CD);
        if (!StringUtils.empty(prnInsIdCd)){
            //通联云闪付交易上送服务商机构标识 "C1000001"
            builder.set(ProtocolFields.PRN_INS_ID_CD, prnInsIdCd);
        }

        return builder;
    }

    public void carryOverExtendedParams(Map<String, Object> extended, RequestBuilder builder, Set<String> allowedFields) {
        for (Map.Entry<String, Object> extendedParam: extended.entrySet()) {
            String key = extendedParam.getKey();
            if (overFilterField(key) || !allowedFields.contains(key)) {
                continue;
            }
            Object value = extendedParam.getValue();
            if (value != null) {
                builder.set(key, value);
            }
        }
    }


    protected void setTerminalInfo(TransactionContext context, RequestBuilder builder) {

        Map<String, Object> transaction = context.getTransaction();
        int subPayWay = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
        TerminalInfo terminalInfo = genTerminalInfo(context.getTransaction());
        String deviceType = terminalInfo.getOrDefaultType(UnionPayQRCodeConstants.DEVICE_TYPE_11);
        Map<String, Object> termInfo = CollectionUtil.hashMap(
                BusinessFields.DEVICE_TYPE, deviceType
        );
        if (terminalInfo.isSendUnionpayPoi()) {
            termInfo.put(BusinessFields.LONGITUDE, terminalInfo.getUnionpayFormatLongitude());
            termInfo.put(BusinessFields.LATITUDE, terminalInfo.getUnionpayFormatLatitude());
            BeanUtil.setNestedProperty(context.getTransaction(), Transaction.KEY_IS_DEFAULT_POI, terminalInfo.isDefaultPoi());
        }
        if( terminalInfo.getSerialNum() != null){
            termInfo.put(HXBankBusinessFields.SERIAL_NUM, terminalInfo.getSerialNum());
        }

        String termId = terminalInfo.getId();
        if (Order.SUB_PAYWAY_BARCODE == subPayWay && Objects.nonNull(termId) && termId.length() > 0) {
            builder.set(ProtocolFields.TERM_ID, termId);
        }
        builder.set(BusinessFields.TERM_INFO, buildUnionSubDomainParams(termInfo));
    }


    private String buildUnionSubDomainParams(Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        StringBuilder temp = new StringBuilder("{");
        params.forEach((k, v) -> {
            if (temp.length() > 1) {
                temp.append("&");
            }
            temp.append(k).append("=").append(v);
        });
        temp.append("}");
        return Base64.encode(temp.toString().getBytes(StandardCharsets.UTF_8));
    }

    private String getPayeeInfo(TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = getTradeParams(transaction);
        String merId = MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MER_ID);
        String merCatCode = MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MCH_CAT_CODE);
        merCatCode = StringUtil.empty(merCatCode) && merId.length() >= 11 ? merId.substring(7, 11) : merCatCode;
        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        return  "{" +
                ProtocolFields.ID + "=" + MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MER_ID) + "&" +
                ProtocolFields.MER_CAT_CODE + "=" + merCatCode + "&" +
                ProtocolFields.NAME + "=" + MapUtil.getString(config, TransactionParam.HAIKE_UNION_PAY_UNION_MER_NAME) + "&" +
                ProtocolFields.TERM_ID + "=" + terminalInfo.getId() +
                "}";
    }

    protected void setTransactionContextErrorInfo(Map<String,Object> result, TransactionContext context , String key) {
        String responseCode = MapUtil.getString(result, ResponseFields.RESP_CODE, "");
        String responseMsg = MapUtil.getString(result, ResponseFields.RESP_MSG);
        String originalResponseCode = MapUtil.getString(result, ResponseFields.ORIG_RESP_CODE, "");
        String originalResponseMsg = MapUtil.getString(result, ResponseFields.ORIG_RESP_MSG);

        Map<String, Object> map = new LinkedHashMap<>();
        map.put(ResponseFields.RESP_CODE, responseCode);
        map.put(ResponseFields.RESP_MSG, responseMsg);
        map.put(ResponseFields.ORIG_RESP_CODE, originalResponseCode);
        map.put(ResponseFields.ORIG_RESP_MSG, originalResponseMsg);
        String codes = responseCode + originalResponseCode;
        boolean isSuccess = UnionPayQRCodeConstants.RESP_CODE_SUCCESS.equals(codes) || (UnionPayQRCodeConstants.RESP_CODE_SUCCESS + UnionPayQRCodeConstants.RESP_CODE_SUCCESS).equals(codes);
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, StringUtil.empty(originalResponseCode) ? responseCode : originalResponseCode, StringUtil.empty(originalResponseMsg) ? responseMsg : originalResponseMsg);
    }


    public void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        Map<String,Object> order = context.getOrder();
        //设置付款人信息
        Map<String,Object> payerInfo = (Map<String, Object>) result.get(ResponseFields.PAYER_INFO);
        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_UID))) {
            String accNo = MapUtil.getString(payerInfo, ResponseFields.PAYER_INFO_ACC_NO);
            if (!StringUtil.empty(accNo)) {
                transaction.put(Transaction.BUYER_UID, accNo);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.BUYER_LOGIN))) {
            String name = MapUtil.getString(payerInfo, ResponseFields.PAYER_INFO_NAME);
            //接口返回的名字没有脱敏，此处进行脱敏处理
            if (!StringUtils.empty(name)) {
                name = "*" + name.substring(name.length() - 1);
            }
            if (!StringUtil.empty(name)) {
                transaction.put(Transaction.BUYER_LOGIN, name);
            }
        }

        if (StringUtil.empty(MapUtil.getString(transaction, Transaction.TRADE_NO))) {
            String tradeNo = MapUtil.getString(result, ResponseFields.VOUCHER_NUM);
            if (!StringUtil.empty(tradeNo)) {
                transaction.put(Transaction.TRADE_NO, tradeNo);
            }
        }
    }

    public void resolvePayFund(Map<String, Object> result, TransactionContext context) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null){
            extraOutFields = new HashMap<>();
        }
        List<Map<String, Object>> payments = new ArrayList<>();
        List<Map<String, Object>> couponInfo = (List<Map<String, Object>>) result.get(ResponseFields.COUPON_INFO);
        long couponSum = 0;
        if (couponInfo != null) {
            for (Map<String, Object> coupon : couponInfo) {
                String spnsrId = MapUtil.getString(coupon, ResponseFields.COUPON_INFO_SPNSR_ID); //出资方
                long amount = MapUtil.getLongValue(coupon, ResponseFields.COUPON_INFO_OFFST_AMT);
                String couponId = MapUtil.getString(coupon, ResponseFields.COUPON_INFO_ID);
                String couponType = MapUtil.getString(coupon, ResponseFields.COUPON_INFO_TYPE);
                couponSum = couponSum + amount;
                String paymentType;
                if (UnionPayQRCodeConstants.COUPON_INFO_SPNSR_ID_UNIONPAY.equals(spnsrId)) {
                    paymentType = UnionPayQRCodeConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL : Payment.TYPE_DISCOUNT_CHANNEL;
                } else {
                    paymentType = UnionPayQRCodeConstants.COUPON_INFO_TYPE_CP02.equals(couponType) ? Payment.TYPE_HONGBAO_CHANNEL_MCH : Payment.TYPE_DISCOUNT_CHANNEL_MCH;
                }
                payments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_AMOUNT, amount,
                        Transaction.PAYMENT_SOURCE, couponId,
                        Transaction.PAYMENT_ORIGIN_TYPE, couponType + ":" + spnsrId,
                        Transaction.PAYMENT_TYPE, paymentType
                ));
            }
        }
        long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        long amount = effectiveAmount - couponSum;
        if (amount > 0) {
            String paymentType = null;
            Map<String, Object> payerInfo = MapUtil.getMap(result, ResponseFields.PAYER_INFO);
            String cardAttr = null;
            if (payerInfo != null) {
                cardAttr = MapUtil.getString(payerInfo, ResponseFields.PAYER_INFO_CARD_ATTR);
                if (!StringUtils.isEmpty(cardAttr)) {
                    if (UnionPayQRCodeConstants.CARD_ATTR_CREDIT_CARD.equals(cardAttr)) {
                        paymentType = Payment.TYPE_BANKCARD_CREDIT;
                    } else if (UnionPayQRCodeConstants.CARD_ATTR_DEBIT_CARD.equals(cardAttr)) {
                        paymentType = Payment.TYPE_BANKCARD_DEBIT;
                    } else {
                        paymentType = Payment.TYPE_OTHERS;
                    }
                    payments.add(CollectionUtil.hashMap(
                                    Transaction.PAYMENT_AMOUNT, amount,
                                    Transaction.PAYMENT_ORIGIN_TYPE, cardAttr,
                                    Transaction.PAYMENT_TYPE, paymentType
                            )
                    );
                }
            }
        }
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);
        if (oldPayments == null || oldPayments.isEmpty()) {
            if (payments != null && !payments.isEmpty()) {
                extraOutFields.put(Transaction.PAYMENTS, payments);
            }
        }
        if (!StringUtil.empty(MapUtil.getString(result, ResponseFields.PAY_AMT))) {
            long payAmt = MapUtil.getLongValue(result, ResponseFields.PAY_AMT);
            transaction.put(Transaction.PAID_AMOUNT, payAmt);
        }
    }

    public void resolveRefundFund(TransactionContext context){
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), com.wosai.pantheon.util.MapUtil.getLongValue(context.getOrder(), DaoConstants.CTIME)));
    }
}
