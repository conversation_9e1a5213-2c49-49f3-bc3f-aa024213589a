package com.wosai.upay.workflow;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.WapV2Fields;

import com.wosai.mpay.api.fake.FakeConstant;
import com.wosai.mpay.api.pab.ResponseFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WapFields;
import com.wosai.mpay.api.zjtlcb.*;

import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.net.GatewayUrl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/4/23、11:42
 * 浙江泰隆通道
 **/

public class ZJTLCBServiceProvider extends AbstractServiceProvider {

    public static final Logger LOGGER = LoggerFactory.getLogger(ZJTLCBServiceProvider.class);
    private long b2cTimeoutExpress = 1000 * 60;

    public static final String PAY_TYPE_SQB_CASH_REGISTER = "25"; // 收钱吧收银机

    // 泰隆返回的空时为null的字符串,判空时做特殊处理
    public static final String NULL = "null";


    @Autowired
    private TLCBTokenCache tlcbTokenCache;

    @Autowired
    private ZJTLCBClient tlbClient;


    @Autowired
    protected ExternalServiceFacade serviceFacade;

    @Override
    public String getName() {
        return TLCBConstant.NAME;
    }

    @Override
    public Integer getProvider() {

        return Order.PROVIDER_ZJTLCB;
    }

    @Override
    public boolean canHandle(Map<String, Object> transaction) {

        return getTradeParams(transaction) != null;
    }

    @Override
    public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
        return getTradeParams(transaction, TransactionParam.ZJTLCB_UP_TRADE_PARAMS);
    }


    @Override
    public String pay(TransactionContext context, boolean resume) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);
        Map<String, Object> b2cPayRequest = buildB2CPayRequest(context);
        Map<String, Object> result = null;
        try {
            result = commonRequest(context, TLCBConstant.B2C_PAY, b2cPayRequest);

        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_PAY, ex);
            LOGGER.error("failed to call zjtlcb pay", ex);
            return Workflow.RC_IOEX;
        }
        setTransactionContextErrorInfo(result, context, OP_PAY);
        return buildResult(result, context, OP_PAY);

    }

    //公共调用方法
    public Map<String, Object> commonRequest(TransactionContext context, String serviceId, Map<String, Object> request) {

        GatewayUrl providerGateway = ApolloConfigurationCenterUtil.getProviderGatewayUrl(getName(), OP_PAY);
        if (!providerGateway.isSkipEnAndDecrypt()) {
            Map<String, Object> tradeParams = getTradeParams(context.getTransaction());
            String appId = MapUtil.getString(tradeParams, TransactionParam.APP_ID);
            String tlPublicKey = MapUtil.getString(tradeParams, TransactionParam.ZJTLCB_PUBLIC_KEY);
            String sm2PrivateKey = MapUtil.getString(tradeParams, TransactionParam.ZJTLCB_SM2_PRIVATE_KEY);
            String appSecretKey = MapUtil.getString(tradeParams, TransactionParam.ZJTLCB_APP_SECRET_KEY);

            sm2PrivateKey = serviceFacade.getRsaKeyDataById(sm2PrivateKey);
            tlPublicKey = serviceFacade.getRsaKeyDataById(tlPublicKey);

            String accessToken = tlcbTokenCache.getAccessToken(appId, tlPublicKey, sm2PrivateKey, appSecretKey, providerGateway.getUrl());

            return tlbClient.call(request, appId, accessToken, serviceId, tlPublicKey, sm2PrivateKey, providerGateway.getUrl(), appSecretKey);
        } else {
            return fakeCall(providerGateway, serviceId, request);
        }
    }


    public void setTransactionContextErrorInfo(Map<String, Object> result, TransactionContext context, String key) {

        Map<String, Object> map = new LinkedHashMap<>();
        String returnCode = MapUtils.getString(result, TLBResponseFields.ERROR_CODE);//返回的响应码
        String returnMsg = MapUtils.getString(result, TLBResponseFields.ERROR_MSG);//响应描述

        map.put(TLBResponseFields.ERROR_CODE, returnCode);//返回状态码
        map.put(TLBResponseFields.ERROR_MSG, returnMsg);//返回信息

        boolean isSuccess = false;
        if (Objects.equals(returnCode, TLCBConstant.RESPONSE_SUCCESS)) {
            isSuccess = true;
        }
        setTransactionContextErrorInfo(context.getTransaction(), key, map, isSuccess, returnCode, returnMsg);
    }


    private String buildResult(Map<String, Object> result, TransactionContext context, String op) {

        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String tranStatus = MapUtil.getString(result, TLBResponseFields.TRAN_STATUS);
        setTradeNoBuyerInfoIfExists(result, context, op);
        int type = MapUtil.getIntValue(context.getTransaction(), Transaction.TYPE);
        if (Objects.equals(tranStatus, TLCBConstant.SUCCESS)) {
            if (type == Transaction.TYPE_PAYMENT) {
                //付款成功
                context.getTransaction().put(Transaction.CHANNEL_FINISH_TIME, System.currentTimeMillis());
                resolvePayFund(context, result);
                return Workflow.RC_PAY_SUCCESS;
            } else if (type == Transaction.TYPE_REFUND) {
                resolveRefundFund(context);
                return Workflow.RC_REFUND_SUCCESS;

            }

        } else if (Objects.equals(tranStatus, TLCBConstant.PROCESSING)) {
            if (type == Transaction.TYPE_PAYMENT) {
                return Workflow.RC_IN_PROG;
            } else if (type == Transaction.TYPE_REFUND)
                return Workflow.RC_RETRY;

        }
        return Workflow.RC_ERROR;

    }




    private void resolveRefundFund(TransactionContext context) {
        resolveRefundFundByPercent(context.getTransaction(), getPayOrConsumerTransaction(context.getTransaction(), BeanUtil.getPropLong(context.getOrder(), DaoConstants.CTIME)));
    }


    private void setTradeNoBuyerInfoIfExists(Map<String, Object> result, TransactionContext context, String op) {

        if (MapUtils.isEmpty(result)) {
            return;
        }

        Map<String, Object> transaction = context.getTransaction();

        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_UID))) {
            String userId = MapUtils.getString(result, TLBResponseFields.USER_NO);
            if (!StringUtil.empty(userId) && !Objects.equals(NULL, userId)) {
                transaction.put(Transaction.BUYER_UID, userId);
            }
        }
        if (StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN))) {
            String userId = MapUtils.getString(result, TLBResponseFields.USER_NO);
            if (!StringUtil.empty(userId) && !Objects.equals(NULL, userId)) {
                transaction.put(Transaction.BUYER_LOGIN, userId);
            }
        }
        if (StringUtils.isEmpty(BeanUtil.getPropString(transaction, Transaction.TRADE_NO))) {
            String orderId = MapUtils.getString(result, TLBResponseFields.INET_SEQ_NO); // 收款通道订单号
            if (!StringUtils.isEmpty(orderId) && !Objects.equals(NULL, orderId)) {
                transaction.put(Transaction.TRADE_NO, orderId);
            }
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (StringUtils.isEmpty(BeanUtil.getPropString(extraOutFields, Transaction.CHANNEL_TRADE_NO))) {
            String tpOrderId = null;
            if (Objects.equals(op, OP_PAY)) {   // 支付源订单号
                //付款返回
                tpOrderId = MapUtils.getString(result, TLBResponseFields.TR_PR_INET_NO); //第三方订单号
            } else {
                //查单
                tpOrderId = MapUtils.getString(result, TLBResponseFields.TR_PR_INET_NO); //第三方订单号
            }
            if (!StringUtils.isEmpty(tpOrderId) && !Objects.equals(NULL, tpOrderId)) {
                extraOutFields.put(Transaction.CHANNEL_TRADE_NO, tpOrderId);
            }
        }
    }

    private void resolvePayFund(TransactionContext context, Map<String, Object> result) {
        Map<String, Object> transaction = context.getTransaction();


        Long receivedAmount = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0L);

        List<Map<String, Object>> payments = new ArrayList<Map<String, Object>>();

        int payway = MapUtil.getIntValue(transaction, Order.PAYWAY);
        String paymentType = Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_ALIPAY == payway ? Payment.TYPE_WALLET_ALIPAY : Payment.TYPE_BANKCARD;
        if (receivedAmount > 0 && payments.isEmpty()) {
            payments.add(CollectionUtil.hashMap(
                    Transaction.PAYMENT_AMOUNT, receivedAmount,
                    Transaction.PAYMENT_ORIGIN_TYPE, paymentType,
                    Transaction.PAYMENT_TYPE, paymentType));
        }

        Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> oldPayments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        if (oldPayments == null || oldPayments.isEmpty()) {
            extraOutFields.put(Transaction.PAYMENTS, payments);
        }

        transaction.put(Transaction.PAID_AMOUNT, receivedAmount);
        transaction.put(Transaction.RECEIVED_AMOUNT, receivedAmount);
    }


    @Override
    public String cancel(TransactionContext context) {

        Map<String, Object> order = context.getOrder();
        String status = MapUtil.getString(order, Order.STATUS);

        if (Objects.equals(status, Transaction.TYPE_PAYMENT)) {
            return cancelToRefundProcess(context);
        }
        throw new UnsupportedOperationException("不支持订单撤销");

    }


    private String cancelToRefundProcess(TransactionContext context) {
        String result = refund(context);
        if (Workflow.RC_REFUND_SUCCESS.equals(result)) {
            return Workflow.RC_CANCEL_SUCCESS;
        }

        return result;
    }


    @Override
    public String query(TransactionContext context) {

        Map<String, Object> request = buildQueryRequest(context);

        Map<String, Object> result = null;
        try {
            result = commonRequest(context, TLCBConstant.TRANSACTION_STATUS, request);
        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_QUERY, ex);
            LOGGER.error("failed to call zjtlcb query", ex);
            return Workflow.RC_IOEX;
        }

        Map<String, Object> transaction = context.getTransaction();
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery) {
            setTransactionContextErrorInfo(result, context, OP_REFUND);

        } else {
            setTransactionContextErrorInfo(result, context, OP_QUERY);

        }

        return buildResult(result, context, OP_QUERY);

    }

    @Override
    public String refund(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        initTransactionSomeValue(transaction);

        Map<String, Object> refundRequest = buildRefundRequest(context);


        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        boolean onlyRefundQuery = MapUtil.getBooleanValue(extraOutFields, Transaction.NEED_ONLY_REFUND_QUERY);
        if(onlyRefundQuery){
            return query(context);
        }

        Map<String, Object> result = null;
        try {
            result = commonRequest(context, TLCBConstant.REFUND_TRANSACTIONS, refundRequest);

        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_REFUND, ex);
            LOGGER.error("failed to call zjtlcb refund", ex);
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_REFUND);

        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        String tranStatus = MapUtils.getString(result, TLBResponseFields.TRAN_STATUS);//返回的响应码

        if (Objects.equals(TLCBConstant.SUCCESS, tranStatus)) {
            resolveRefundFund(context);
            return Workflow.RC_REFUND_SUCCESS;

        } else if (!Objects.equals(TLCBConstant.SUCCESS, tranStatus)) {
            //待查询的状态
            extraOutFields.put(Transaction.NEED_ONLY_REFUND_QUERY, true);
            return Workflow.RC_RETRY;

        }

        return Workflow.RC_ERROR;

    }

    @Override
    public String precreate(TransactionContext context, boolean resume) {

        initTransactionSomeValue(context.getTransaction());

        Map<String, Object> transaction = context.getTransaction();
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        String serviceId = null;
        Map request = null;
        ZJTLCBRequestBuilder tlbRequestBuilder = buildCommonPreCreateRequest(context);
        if (payway == Payway.WEIXIN.getCode()) {
            request = buildWxC2BRequest(context, tlbRequestBuilder);
            serviceId = TLCBConstant.C2B_WX;
        } else if (payway == Payway.ALIPAY.getCode() || payway == Payway.ALIPAY2.getCode()) {
            request = buildAliPayC2bRequest(context, tlbRequestBuilder);
            serviceId = TLCBConstant.C2B_ALIPAY;
        }

        Map<String, Object> result = null;
        try {
            result = commonRequest(context, serviceId, request);

        } catch (Exception ex) {
            setTransactionContextErrorInfo(context, OP_PRECREATE, ex);
            LOGGER.error("failed to call zjtlcb precreate", ex);
            return Workflow.RC_IOEX;
        }

        setTransactionContextErrorInfo(result, context, OP_PRECREATE);

        return buildPreCreateResult(result, context);

    }

    private String buildPreCreateResult(Map<String, Object> result, TransactionContext transactionContext) {
        if (MapUtils.isEmpty(result)) {
            return Workflow.RC_IOEX;
        }

        Map<String, Object> transaction = transactionContext.getTransaction();
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        //返回则视为成功  泰隆侧返回的是字符串的null
        String quickRspString = MapUtil.getString(result, TLBResponseFields.QUICK_RSP_STRING);
        if (!StringUtils.isEmpty(quickRspString) && !Objects.equals(quickRspString, "null")) {

            if (payway == Payway.WEIXIN.getCode()) {
                Map<String, Object> wxDataPackage = wxDataFormatConvert(result);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, wxDataPackage);

            }
            if (payway == Payway.ALIPAY.getCode() || payway == Payway.ALIPAY2.getCode()) {

                String tradeNo = MapUtils.getString(result, TLBResponseFields.QUICK_RSP_STRING);
                extraOutFields.put(Transaction.WAP_PAY_REQUEST, CollectionUtil.hashMap(WapV2Fields.TRADE_NO, tradeNo));
            }
            return Workflow.RC_CREATE_SUCCESS;
        }

        return Workflow.RC_TRADE_CANCELED;

    }

    private Map<String, Object> wxDataFormatConvert(Map<String, Object> responseResult) {
        Map<String, Object> result = new HashMap<>();
        result.put(WapFields.APP_ID, MapUtil.getString(responseResult, ResponseFields.PAB_APP_ID));
        result.put(WapFields.PACKAGE, MapUtil.getString(responseResult, TLBResponseFields.QUICK_RSP_STRING));
        result.put(WapFields.NONCE_STR, MapUtil.getString(responseResult, TLBResponseFields.REMARK));
        result.put(WapFields.SIGN_TYPE, MapUtil.getString(responseResult, TLBResponseFields.SGN_TP));
        result.put(WapFields.PAY_SIGN, MapUtil.getString(responseResult, TLBResponseFields.SIGN_CERT));
        result.put(WapFields.TIME_STAMP, MapUtil.getString(responseResult, TLBResponseFields.TIMESTAMP));
        return result;
    }


    @Override
    public String explainNotification(Map<String, Object> providerNotification) {
        TransactionContext context = (TransactionContext) providerNotification.get(TransactionContext.class.getName());
        return Workflow.RC_PAY_SUCCESS.equals(query(context)) ? Workflow.RC_PAY_SUCCESS : null;
    }

    public Map<String, Object> buildB2CPayRequest(TransactionContext context) {

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> extraParams = (Map<String, Object>) transaction
                .get(Transaction.EXTRA_PARAMS);
        initTransactionSomeValue(transaction);

        Map<String, Object> order = context.getOrder();
        String orderSn = MapUtil.getString(order, Order.SN);
        Map<String, Object> tradeParams = getTradeParams(transaction);
        String barCode = MapUtil.getString(extraParams, Transaction.BARCODE);
        String channelCode = MapUtil.getString(tradeParams, TransactionParam.ZJTLCB_CHANNEL_CODE);
        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
        String providerMerchantId = MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);
        Long ctime = MapUtil.getLong(transaction, DaoConstants.CTIME);
        ZJTLCBRequestBuilder tlbRequestBuilder = new ZJTLCBRequestBuilder();
        tlbRequestBuilder.set(TLBRequestFields.MECH_NO, providerMerchantId);
        tlbRequestBuilder.set(TLBRequestFields.ACCT_NO, barCode);
        tlbRequestBuilder.set(TLBRequestFields.TRAN_AMT, amount);
        tlbRequestBuilder.set(TLBRequestFields.INET_NO, orderSn);
        tlbRequestBuilder.set(TLBRequestFields.USER_NO, channelCode);
        tlbRequestBuilder.set(TLBRequestFields.CHANNEL_CODE, channelCode);
        tlbRequestBuilder.set(TLBRequestFields.PAY_TYPE, PAY_TYPE_SQB_CASH_REGISTER);
        tlbRequestBuilder.set(TLBRequestFields.START_TIME, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, ctime));
        tlbRequestBuilder.set(TLBRequestFields.EXPIRE_TIME, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, ctime + b2cTimeoutExpress));


        //退款用的
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        String oriTranDate = ZJTLUtil.getTime(TLCBConstant.YYYYMMDD, ctime);

        extraOutFields.put(TLBRequestFields.ORI_TRAN_DATE, oriTranDate);


        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        String ip = terminalInfo.getIp();

        extraOutFields.put(TLBRequestFields.IP, ip);

        return tlbRequestBuilder.buildRequest();

    }

    Map<String, Object> buildQueryRequest(TransactionContext transactionContext) {

        String orderSn = MapUtil.getString(transactionContext.getOrder(), Order.SN);
        Map<String, Object> transaction = transactionContext.getTransaction();

        //退款查询使用 tsn
        String tsn = MapUtil.getString(transaction, Transaction.TSN);

        String inetNo = null;

        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);

        if (Transaction.TYPE_REFUND == transactionType) {
            inetNo = tsn;
        } else if (Transaction.TYPE_PAYMENT == transactionType) {
            inetNo = orderSn;
        }

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String providerMerchantId = MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);

        ZJTLCBRequestBuilder tlbRequestBuilder = new ZJTLCBRequestBuilder();
        tlbRequestBuilder.set(TLBRequestFields.MECH_NO, providerMerchantId);
        tlbRequestBuilder.set(TLBRequestFields.INET_NO, inetNo);
        tlbRequestBuilder.set(TLBRequestFields.SND_TM, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, MapUtil.getLong(transaction, DaoConstants.CTIME)));

        return tlbRequestBuilder.buildRequest();

    }


    public Map<String, Object> buildRefundRequest(TransactionContext context) {
        String orderSn = MapUtil.getString(context.getOrder(), Order.SN);
        String tsn = MapUtil.getString(context.getTransaction(), Transaction.TSN);

        Map<String, Object> transaction = context.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);

        String channelCode = MapUtil.getString(tradeParams, TransactionParam.ZJTLCB_CHANNEL_CODE);

        String providerMerchantId = MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);

        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        ZJTLCBRequestBuilder tlbRequestBuilder = new ZJTLCBRequestBuilder();
        tlbRequestBuilder.set(TLBRequestFields.MECH_NO, providerMerchantId);
        tlbRequestBuilder.set(TLBRequestFields.INET_NO, tsn);
        tlbRequestBuilder.set(TLBRequestFields.ORI_INET_NO, orderSn);

        Map<String, Object> payOrConsumerTransaction = getPayOrConsumerTransaction(transaction, MapUtil.getLong(context.getOrder(), DaoConstants.CTIME));


        Map extraOutFields = MapUtil.getMap(payOrConsumerTransaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
        String oriTranDate = MapUtil.getString(extraOutFields, TLBRequestFields.ORI_TRAN_DATE);

        //时间戳
        tlbRequestBuilder.set(TLBRequestFields.SND_TM, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS));
        //yyyyMMdd
        tlbRequestBuilder.set(TLBRequestFields.ORI_TRAN_DATE, oriTranDate);

        tlbRequestBuilder.set(TLBRequestFields.CHANNEL_CODE, channelCode);

        tlbRequestBuilder.set(TLBRequestFields.REFUND_AMT, amount + "");
        tlbRequestBuilder.set(TLBRequestFields.CCY, TLCBConstant.RMB);

        tlbRequestBuilder.set(TLBRequestFields.IP, MapUtil.getString(extraOutFields, TLBRequestFields.IP));


        return tlbRequestBuilder.buildRequest();
    }


    public Map<String, Object> buildAliPayC2bRequest(TransactionContext context, ZJTLCBRequestBuilder tlbRequestBuilder) {

        tlbRequestBuilder.set(TLBRequestFields.ACCT_TYPE, TLCBConstant.ACCT_TYPE_ALIPAY);

        String payerUid = BeanUtil.getPropString(context.getTransaction(), KEY_PAYER_UID);

        tlbRequestBuilder.set(TLBRequestFields.CLNT_NO, payerUid);
        tlbRequestBuilder.set(TLBRequestFields.SEND_DATE, ZJTLUtil.getTime(TLCBConstant.YYYYMMDD, MapUtil.getLong(context.getTransaction(), DaoConstants.CTIME)));

        return tlbRequestBuilder.buildRequest();
    }


    ZJTLCBRequestBuilder buildCommonPreCreateRequest(TransactionContext context) {

        Map<String, Object> order = context.getOrder();
        String orderSn = MapUtil.getString(order, Order.SN);

        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> tradeParams = getTradeParams(transaction);


        String providerMerchantId = MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);

        String channelCode = MapUtil.getString(tradeParams, TransactionParam.ZJTLCB_CHANNEL_CODE);


        long amount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);

        ZJTLCBRequestBuilder tlbRequestBuilder = new ZJTLCBRequestBuilder();
        tlbRequestBuilder.set(TLBRequestFields.MECH_NO, providerMerchantId);
        tlbRequestBuilder.set(TLBRequestFields.INET_NO, orderSn);

        tlbRequestBuilder.set(TLBRequestFields.SND_TM, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, MapUtil.getLong(transaction, DaoConstants.CTIME)));

        tlbRequestBuilder.set(TLBRequestFields.START_TIME, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, MapUtil.getLong(transaction, DaoConstants.CTIME)));
        tlbRequestBuilder.set(TLBRequestFields.EXPIRE_TIME, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSS, MapUtil.getLong(transaction, DaoConstants.CTIME) + TLCBConstant.FOUR_MINUTES));

        tlbRequestBuilder.set(TLBRequestFields.CHANNEL_CODE, channelCode);

        tlbRequestBuilder.set(TLBRequestFields.PAY_AMOUNT, amount + "");

        String subject = MapUtil.getString(transaction, BusinessV2Fields.SUBJECT);

        tlbRequestBuilder.set(TLBRequestFields.PRDCT_MSG, subject);
        tlbRequestBuilder.set(TLBRequestFields.CCY, TLCBConstant.RMB);
        tlbRequestBuilder.set(TLBRequestFields.ADD_MSG, TLCBConstant.ADD_MSG);

        TerminalInfo terminalInfo = genTerminalInfo(transaction);
        String ip = terminalInfo.getIp();
        tlbRequestBuilder.set(TLBRequestFields.IP, ip);

        //退款用的
        Map extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS, new HashMap());
        String oriTranDate = ZJTLUtil.getTime(TLCBConstant.YYYYMMDD, MapUtil.getLong(transaction, DaoConstants.CTIME));

        extraOutFields.put(TLBRequestFields.ORI_TRAN_DATE, oriTranDate);

        extraOutFields.put(TLBRequestFields.IP, ip);

        return tlbRequestBuilder;

    }

    public Map<String, Object> buildWxC2BRequest(TransactionContext transactionContext, ZJTLCBRequestBuilder tlbRequestBuilder) {

        Map<String, Object> transaction = transactionContext.getTransaction();

        Map<String, Object> tradeParams = getTradeParams(transaction);


        tlbRequestBuilder.set(TLBRequestFields.ACCT_TYPE, TLCBConstant.ACCT_TYPE_WX);

        String subAppid = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID);

        Integer subPayway = MapUtil.getInteger(transaction, Transaction.SUB_PAYWAY);

        if (subPayway == SubPayway.MINI.getCode()) {
            String minSubAppId = MapUtil.getString(tradeParams, TransactionParam.PAB_PAY_WEIXIN_MINI_SUB_APP_ID);
            if (!StringUtils.isEmpty(minSubAppId)) {
                subAppid = minSubAppId;
            }
        }

        // 优先使用前端上送的sub_app_id
        Map<String, Object> extended = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        String sendSubAppId = com.wosai.pantheon.util.MapUtil.getString(extended, ProtocolFields.SUB_APP_ID);

        if (!StringUtil.empty(sendSubAppId)) {
            subAppid = sendSubAppId;

        }
        String payerUid = BeanUtil.getPropString(transaction, KEY_PAYER_UID);
        tlbRequestBuilder.set(TLBRequestFields.CLNT_SBTP_ID, payerUid);

        tlbRequestBuilder.set(TLBRequestFields.WECHAT_PUBLIC_NO, subAppid);

        return tlbRequestBuilder.buildRequest();

    }

    public String getOrderSn(String encryptRequest) {
        Map<String, Object> stringObjectMap = decryptNotify(encryptRequest);
        return MapUtil.getString(stringObjectMap, TLBRequestFields.INET_NO, null);

    }

    @SneakyThrows
    public String makeNotifyResponseSuccessContent(String request){
        Map<String, String> zjtlDecryptParams = ApolloConfigurationCenterUtil.getZJTLDecryptParams();
        String tlPublicKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_PUBLIC_KEY);
        String sm2PrivateKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_SM2_PRIVATE_KEY);
        String appSecretKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_APP_SECRET_KEY);
        sm2PrivateKey = serviceFacade.getRsaKeyDataById(sm2PrivateKey);
        tlPublicKey = serviceFacade.getRsaKeyDataById(tlPublicKey);
        Map<String, Object> notification = JsonUtil.jsonStrToObject(request, Map.class);
        String appAccessToken = MapUtil.getString(notification, TLBRequestFields.APP_ACCESS_TOKEN);
        String appID = MapUtil.getString(notification, TLBRequestFields.APP_ID);
        String seqNo = MapUtil.getString(notification, TLBRequestFields.SEQ_NO);
        String randomKey = SM2Util.decryptByPrivateKey(MapUtil.getString(notification, TLBRequestFields.SM2_ENCRYPT_DATA), sm2PrivateKey);
        String respData = "{\"head\": {\"errorCode\": \"000000\",\"errorMsg\": \"交易成功\"}}";
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put(TLBRequestFields.APP_ID, appID);
        responseMap.put(TLBRequestFields.SEQ_NO, seqNo);
        responseMap.put(TLBRequestFields.SIGN_METHOD, "SM3");
        responseMap.put(TLBRequestFields.ENCRYPT_METHOD, "SM4");
        responseMap.put(TLBRequestFields.APP_ACCESS_TOKEN, appAccessToken);
        responseMap.put(TLBRequestFields.SM2_ENCRYPT_DATA, SM2Util.encryptByPublicKey(randomKey, tlPublicKey));
        responseMap.put(TLBRequestFields.SM2_SIGN, SM2Util.signByPrivateKey(randomKey, sm2PrivateKey, appID));
        responseMap.put(TLBRequestFields.SIGN, SM3Util.sign(respData + seqNo + appSecretKey + randomKey));
        responseMap.put(TLBRequestFields.RSP_DATA, SM4Util.encrypt(respData, seqNo + appAccessToken + appSecretKey + randomKey));
        return JsonUtil.objectToJsonString(responseMap);
    }

    public Map<String ,Object> decryptNotify(String encryptRequest) {
        Map<String, String> zjtlDecryptParams = ApolloConfigurationCenterUtil.getZJTLDecryptParams();
        if(MapUtil.isEmpty(zjtlDecryptParams)) {
            new HashMap<>();
        }
        String appId = MapUtil.getString(zjtlDecryptParams, TransactionParam.APP_ID);
        String tlPublicKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_PUBLIC_KEY);
        String sm2PrivateKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_SM2_PRIVATE_KEY);
        String appSecretKey = MapUtil.getString(zjtlDecryptParams, TransactionParam.ZJTLCB_APP_SECRET_KEY);

        sm2PrivateKey = serviceFacade.getRsaKeyDataById(sm2PrivateKey);
        tlPublicKey = serviceFacade.getRsaKeyDataById(tlPublicKey);
        try {
            Map encryptMap = JsonUtil.jsonStringToObject(encryptRequest, Map.class);

            Map<String, Object> decrypt = tlbClient.decrypt(encryptMap, sm2PrivateKey, tlPublicKey, appId, appSecretKey, TLBRequestFields.REQ_DATA);
            return  decrypt;
        } catch (Exception e) {
            logger.error("get order sn from encryptRequest error, ", e);
            return null;
        }

    }

    public Map<String, Object> fakeCall(GatewayUrl gatewayUrl, String serviceId, Map<String,Object> request) {

        Map<String, Object> result = fakeClient.call(gatewayUrl.getUrl() + serviceId, request, "application/x-www-form-urlencoded", FakeConstant.JSON_FORMAT);
        if (result.containsKey(TLBRequestFields.HEAD)) {
            result.putAll(MapUtil.getMap(result, TLBRequestFields.HEAD, new HashMap<>()));
        }
        if (result.containsKey(TLBRequestFields.BODY)) {
            result.putAll(MapUtil.getMap(result, TLBRequestFields.BODY, new HashMap<>()));
        }

        return result;
    }

}
