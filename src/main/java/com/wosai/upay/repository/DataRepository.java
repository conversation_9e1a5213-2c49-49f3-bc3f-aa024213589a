package com.wosai.upay.repository;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.model.dao.Transaction;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.upay.exception.ClientSnConflictError;
import com.wosai.upay.model.dao.NonSqbOrder;
import com.wosai.upay.model.dao.Order;

public class DataRepository {
    private static final Logger logger = LoggerFactory.getLogger(DataRepository.class);

    private Dao<Map<String, Object>> orderDao;
    private Dao<Map<String, Object>> transactionDao;
    private Dao<Map<String, Object>> walletDao;
    private Dao<Map<String, Object>> logDao;
    private Dao<Map<String, Object>> systemConfigDao;
    private Dao<Map<String, Object>> nonSqbOrderDao;
    private Dao<Map<String, Object>> preCancelOrderDao;




    @Transactional(isolation=Isolation.REPEATABLE_READ)
    public void save(Map<String, Object> order, Map<String, Object> transaction) {
        if (orderDao != null) {
            try {
                orderDao.save(order);
            }catch(DataIntegrityViolationException ex) {
                logger.warn("failed to save order", ex);

                throw new ClientSnConflictError(UpayErrorScenesConstant.CLIENT_SN_CONFLICT_MESSAGE);
            }
        }
        if (transactionDao != null) {
            transactionDao.save(transaction);
        }
    }
    
    @Transactional(isolation=Isolation.REPEATABLE_READ)
    public void doInTransaction(Runnable runnable) {
        runnable.run();
    }


    @Transactional(value = "ticketTransactionManager", isolation=Isolation.REPEATABLE_READ)
    public Long doInTicketTransaction(Callable<Long> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public void finishTransaction(Map<String, Object> orderUpdates,
                                  Map<String, Object> transactionUpdates,
                                  Map<String, Object> log,
                                  int retryTime) {
        if(transactionUpdates != null) {
            transactionDao.updatePart(transactionUpdates);
        }
        if (log != null) {
        	boolean existed = false;
        	if(retryTime >= 1 
        			&& null != logDao.get(BeanUtil.getPropString(log, DaoConstants.ID))){
        		existed = true;
        		logger.warn("log already in db, tid: {}, logid: {}", BeanUtil.getPropString(transactionUpdates, DaoConstants.ID), BeanUtil.getPropString(log, DaoConstants.ID));
        	}
        	if(!existed){
        		logDao.save(log);
        	}
        }
        if(null != orderUpdates){
            orderDao.updatePart(orderUpdates);
        }
    }

    public  Map<String,Object> getOrder(String merchantId, String storeId, String orderSn, String  clientSn){
        Map<String, Object> order = (!StringUtil.empty(orderSn))? this.getOrderDao().filter(Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.STORE_ID).is(storeId).with(Order.SN).is(orderSn)).fetchOne():
                this.getOrderDao().filter(Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.STORE_ID).is(storeId).with(Order.CLIENT_SN).is(clientSn)).fetchOne();

        if (order == null) {
            order = (!StringUtil.empty(orderSn))? this.getOrderDao().filter(Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.SN).is(orderSn)).fetchOne():
                    this.getOrderDao().filter(Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.CLIENT_SN).is(clientSn)).fetchOne();
        }
        //对接了拉卡拉或者兴业银行第三方支付通道，sn传的值有可能是拉卡拉或者兴业的订单号
        if (order == null && !StringUtils.isEmpty(orderSn)) {
            Criteria criteria = Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.TRADE_NO).is(orderSn);
            Filter filter = orderDao.filter(criteria);
            filter.limit(2);
            List<Map<String,Object>> orders = CollectionUtil.iterator2list(filter.fetchAll());
            if(orders != null && orders.size() == 1){
                order = orders.get(0);
            }
        }
        return order;
    }

    public Map<String,Object> getOrderByOrderSn(String merchantId, String orderSn){
        Criteria criteria = Criteria.where(Order.SN).is(orderSn);
        if(!StringUtil.empty(merchantId)){
            criteria.with(Order.MERCHANT_ID).is(merchantId);
        }
        return this.getOrderDao().filter(criteria).fetchOne();
    }

    public Map<String,Object> getOrderByClientSn(String merchantId, String clientSn){
        Criteria criteria = Criteria.where(Order.CLIENT_SN).is(clientSn);
        if(!StringUtil.empty(merchantId)){
            criteria.with(Order.MERCHANT_ID).is(merchantId);
        }
        return this.getOrderDao().filter(criteria).fetchOne();
    }
    
    public Map<String,Object> getLatestTransactionByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn));
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(1);
        return filter.fetchOne();
    }
    
    public Map<String, Object> getTransactionByOrderSnAndClientTsn(String merchantId, String orderSn,
			String clientTsn) {
    	 Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn)
    			 .with(Transaction.CLIENT_TSN).is(clientTsn));
         filter.orderBy(DaoConstants.CTIME, Filter.DESC);
         filter.limit(1);
         return filter.fetchOne();
	}

    public Map<String,Object> getPayTransactionByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_PAYMENT));
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        return filter.fetchOne();
    }

    public Map<String,Object> getLatestRefundTransactionByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_REFUND));
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        return filter.fetchOne();
    }

    public Map<String, Object> getLatestPartialRefundTransactionByOrderSn(String merchantId
            , String orderSn, long originalTotal) {
        Filter<Map<String, Object>> filter = this.getTransactionDao()
                .filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId)
                        .with(Transaction.ORDER_SN).is(orderSn)
                        .with(Transaction.TYPE).is(Transaction.TYPE_REFUND)
                        .with(Transaction.ORIGINAL_AMOUNT).lt(originalTotal));
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        return filter.fetchOne();
    }

    public long getCancelTransactionCountByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_CANCEL));
        return filter.count();
    }

    public Map<String,Object> getTransactionByTsn(String merchantId, String tsn){
        Criteria criteria = Criteria.where(Transaction.TSN).is(tsn);
        if(!StringUtil.empty(merchantId)){
            criteria.with(Transaction.MERCHANT_ID).is(merchantId);
        }
        return transactionDao.filter(criteria).fetchOne();
    }

    public Map<String,Object> getFreezeTransactionByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_DEPOSIT_FREEZE));
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        return filter.fetchOne();
    }
    
    public Map<String,Object> getConsumeTransactionByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_DEPOSIT_CONSUME));
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        return filter.fetchOne();
    }

    public long getDepositCancelTransactionCountByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId).with(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_DEPOSIT_CANCEL));
        return filter.count();
    }

    public List<Map<String,Object>> getRefundTransactionsByOrderSn(String merchantId, String orderSn){
        Filter<Map<String, Object>> filter = this.getTransactionDao().filter(
                Criteria.where(Transaction.MERCHANT_ID).is(merchantId)
                        .with(Transaction.ORDER_SN).is(orderSn)
                        .with(Transaction.TYPE).is(Transaction.TYPE_REFUND)
        );
        return CollectionUtil.iterator2list(filter.fetchAll());

    }
    
    public long getOrderRefundCount(String merchantId, String orderSn){
        Criteria criteria = Criteria.where(Transaction.ORDER_SN).is(orderSn)
                .with(Transaction.TYPE).is(Transaction.TYPE_REFUND)
                .with(Transaction.MERCHANT_ID).is(merchantId);
        return getTransactionDao().filter(criteria).count();

    }
    
    public List<Map<String,Object>> getNonSqbOrderByClientSn(String clientSn){
        Filter<Map<String, Object>> filter = this.getNonSqbOrderDao().filter(
                Criteria.where(NonSqbOrder.CLIENT_SN).is(clientSn)
        );
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    public  Map<String,Object> getPreCancelOrder(String merchantId, String storeId, String  clientSn){
        Filter<Map<String, Object>> filter = this.getPreCancelOrderDao().filter(Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.STORE_ID).is(storeId).with(Order.CLIENT_SN).is(clientSn));
        return filter.fetchOne();
    }

    public Dao<Map<String, Object>> getOrderDao() {
        return orderDao;
    }
    public void setOrderDao(Dao<Map<String,Object>> orderDao) {
        this.orderDao = orderDao;
    }
    public Dao<Map<String, Object>> getTransactionDao() {
        return transactionDao;
    }
    public void setTransactionDao(Dao<Map<String,Object>> transactionDao) {
        this.transactionDao = transactionDao;
    }

    public Dao<Map<String, Object>> getWalletDao() {
        return walletDao;
    }

    public void setWalletDao(Dao<Map<String, Object>> walletDao) {
        this.walletDao = walletDao;
    }

    public Dao<Map<String, Object>> getLogDao() {
        return logDao;
    }

    public void setLogDao(Dao<Map<String, Object>> logDao) {
        this.logDao = logDao;
    }

    public Dao<Map<String, Object>> getSystemConfigDao() {
        return systemConfigDao;
    }

    public void setSystemConfigDao(Dao<Map<String, Object>> systemConfigDao) {
        this.systemConfigDao = systemConfigDao;
    }

    public Dao<Map<String, Object>> getNonSqbOrderDao() {
        return nonSqbOrderDao;
    }

    public void setNonSqbOrderDao(Dao<Map<String, Object>> nonSqbOrderDao) {
        this.nonSqbOrderDao = nonSqbOrderDao;
    }

    public Dao<Map<String, Object>> getPreCancelOrderDao() {
        return preCancelOrderDao;
    }

    public void setPreCancelOrderDao(Dao<Map<String, Object>> preCancelOrderDao) {
        this.preCancelOrderDao = preCancelOrderDao;
    }
}
