package com.wosai.upay.validation;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PropPatternValidator implements ConstraintValidator<PropPattern, Map> {

    private String propName;
    private String regex;
    private Pattern pattern;
    private boolean nullable;
    
    @Override
    public void initialize(PropPattern constraintAnnotation) {
        this.propName = constraintAnnotation.value();
        this.regex = constraintAnnotation.regex();
        this.pattern = Pattern.compile(this.regex);
        this.nullable = constraintAnnotation.nullable();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        Object propVal = value.get(propName);
        if (propVal == null) {
            return nullable;
        }
        if (propVal instanceof CharSequence) {
            Matcher m = this.pattern.matcher((CharSequence)propVal);
            return m.matches();
        }
        return false;
    }

}
