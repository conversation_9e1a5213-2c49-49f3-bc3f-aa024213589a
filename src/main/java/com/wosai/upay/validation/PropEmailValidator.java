package com.wosai.upay.validation;

import java.net.IDN;
import java.util.Map;
import java.util.regex.Matcher;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PropEmailValidator implements ConstraintValidator<PropEmail, Map> {
    private static String ATOM = "[a-z0-9!#$%&'*+/=?^_`{|}~-]";
    private static String DOMAIN = "(" + ATOM + "+(\\." + ATOM + "+)*";
    private static String IP_DOMAIN = "\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\]";

    private java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
            "^" + ATOM + "+(\\." + ATOM + "+)*@"
                    + DOMAIN
                    + "|"
                    + IP_DOMAIN
                    + ")$",
            java.util.regex.Pattern.CASE_INSENSITIVE
    );

    private String propName;
    private boolean nullable;
    @Override
    public void initialize(PropEmail constraintAnnotation) {
        this.propName = constraintAnnotation.value();
        this.nullable = constraintAnnotation.nullable();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        Object propVal = value.get(propName);
        if (propVal == null) {
            return nullable;
        }
        if (propVal instanceof CharSequence) {
            String asciiString = IDN.toASCII( propVal.toString() );
            Matcher m = pattern.matcher( asciiString );
            return m.matches();
        }
        return false;
    }

}
