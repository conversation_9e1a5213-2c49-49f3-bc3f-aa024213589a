package com.wosai.upay.validation;

import java.util.Map;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PropMinValidator implements ConstraintValidator<PropMin, Map> {

    private String propName;
    private long min;
    private boolean nullable;
    @Override
    public void initialize(PropMin constraintAnnotation) {
        this.propName = constraintAnnotation.value();
        this.min = constraintAnnotation.min();
        this.nullable = constraintAnnotation.nullable();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        Object propVal = value.get(propName);
        if (propVal == null) {
            return nullable;
        }
        if (propVal instanceof Number) {
            return ((Number)propVal).longValue() >= min;
        }else if( propVal instanceof String){
            if (!((String)propVal).isEmpty()) {
                try {
                    Double d = Double.parseDouble((String)propVal);
                    return d.longValue() >= min;
                }catch (NumberFormatException e) {
                }
            }
        }
        return false;
    }

}
