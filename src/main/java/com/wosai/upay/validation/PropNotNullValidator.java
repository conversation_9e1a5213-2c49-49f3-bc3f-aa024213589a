package com.wosai.upay.validation;

import java.util.Map;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PropNotNullValidator implements ConstraintValidator<PropNotNull, Map> {

    private String propName;
    @Override
    public void initialize(PropNotNull constraintAnnotation) {
        this.propName = constraintAnnotation.value();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        return value.get(propName) != null;
    }

}
