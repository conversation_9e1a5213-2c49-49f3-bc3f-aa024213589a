package com.wosai.upay.validation;

import java.util.Map;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PropNotNullOnPropValidator implements ConstraintValidator<PropNotNullOnProp, Map> {

    private String propName;
    private String propName2;
    
    @Override
    public void initialize(PropNotNullOnProp constraintAnnotation) {
        this.propName = constraintAnnotation.value();
        this.propName2 = constraintAnnotation.value2();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        if (value.get(propName2) != null) {
            return value.get(propName) != null;
        }
        return true;
    }

}
