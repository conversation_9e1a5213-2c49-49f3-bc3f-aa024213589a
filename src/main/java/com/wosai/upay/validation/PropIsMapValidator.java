package com.wosai.upay.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.util.StringUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Map;

public class PropIsMapValidator implements ConstraintValidator<PropIsMap, Map> {
    private String propName;
    private boolean nullable;
    private boolean emptyable;
    public static ObjectMapper om = new ObjectMapper();

    @Override
    public void initialize(PropIsMap constraintAnnotation) {
        this.propName = constraintAnnotation.value();
        this.nullable = constraintAnnotation.nullable();
        this.emptyable = constraintAnnotation.emptyable();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        Object propVal = value.get(propName);
        if (propVal == null) {
            return nullable;
        }
        if (propVal instanceof Map) {
            return emptyable || ! ((Map)propVal).isEmpty();
        }
        return false;
    }

}
