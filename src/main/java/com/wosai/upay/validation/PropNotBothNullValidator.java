package com.wosai.upay.validation;

import java.util.Map;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PropNotBothNullValidator implements ConstraintValidator<PropNotBothNull, Map> {

    private String propName;
    private String propName2;
    
    @Override
    public void initialize(PropNotBothNull constraintAnnotation) {
        this.propName = constraintAnnotation.value();
        this.propName2 = constraintAnnotation.value2();
    }

    @Override
    public boolean isValid(Map value, ConstraintValidatorContext context) {
        return !(value.get(propName) == null && value.get(propName2) == null);
    }

}
