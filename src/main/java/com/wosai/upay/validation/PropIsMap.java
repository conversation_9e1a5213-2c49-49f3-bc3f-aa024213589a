package com.wosai.upay.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, PARAMETER, FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy=PropIsMapValidator.class)
public @interface PropIsMap {
    String message() default "{PropIsMap.message}";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default {};
    
    String value();
    boolean nullable() default true;

    boolean emptyable() default true;

    @Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER })
    @Retention(RUNTIME)
    @interface List {
        PropIsMap[] value();
    }

}
