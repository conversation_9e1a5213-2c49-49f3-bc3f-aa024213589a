package com.wosai.upay.dao;

import java.io.IOException;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.Filter;
import com.wosai.data.dao.common.ConverterIterator;

public class JsonBlobAwareDao implements Dao<Map<String, Object>>{

    private Dao<Map<String, Object>> delegate;
    private Set<String> jsonBlobColumns;

    @Autowired
    private ObjectMapper objectMapper;

    public JsonBlobAwareDao(Dao<Map<String, Object>> delegate, Set<String> jsonBlobColumns) {
        this.delegate = delegate;
        this.jsonBlobColumns = jsonBlobColumns;
    }

    private Map<String, Object> toBytes(Map<String, Object> model) {
        Map<String, Object> copy = new LinkedHashMap<String, Object>(model);
        for (String column: copy.keySet()) {
            if (jsonBlobColumns.contains(column)) {
                Object value = copy.get(column);
                if(null != value) {
                    try {
                        byte[] data = objectMapper.writeValueAsBytes(value);
                        copy.put(column, data);
                    }
                    catch (JsonProcessingException e) {
                        try {
                            byte[] data = objectMapper.writeValueAsBytes(value);
                            copy.put(column, data);
                        } catch (JsonProcessingException e1) {
                            throw new RuntimeException(e1);
                        }
                    }
                }
            }
        }
        return copy;
    }

    private void fromBytes(Map<String, Object> model) {
        if (model == null) {
            return;
        }
        for (String column: model.keySet()) {
            if (jsonBlobColumns.contains(column)) {
                Object value = model.get(column);
                if (value instanceof byte[]) {
                    try {
                        Object data = objectMapper.readValue((byte[])value, Object.class);
                        model.put(column, data);
                    } catch (IOException e) {
                    }
                }
            }
        }
    }

    @Override
    public void save(Map<String, Object> model) {
        delegate.save(toBytes(model));
    }

    @Override
    public void delete(String key) {
        delegate.delete(key);
    }

    @Override
    public void softDelete(String key) {
        delegate.softDelete(key);
    }

    @Override
    public Map<String, Object> get(String key) {
        Map<String, Object> result = delegate.get(key);
        fromBytes(result);
        return result;
    }

    @Override
    public Filter<Map<String, Object>> filter(Criteria criteria) {
        return new JsonBlobAwareFilter(delegate.filter(criteria));
    }

    @Override
    public void updatePart(Map<String, Object> update) {
        delegate.updatePart(toBytes(update));
    }

    @Override
    public Map<String, Object> getPart(String key, Collection<String> projection) {
        Map<String, Object> part = delegate.getPart(key, projection);
        fromBytes(part);
        return part;
    }

    @Override
    public Filter<Map<String, Object>> filter(Criteria criteria,
                                              Collection<String> projection) {
        return new JsonBlobAwareFilter(delegate.filter(criteria, projection));
    }

    @Override
    public Map<String, Object> findAndModify(Criteria criteria,
                                             Map<String, Object> update) {
        return delegate.findAndModify(criteria, update);
    }

    @Override
    public Map<String, Object> findAndModify(Criteria criteria,
                                             Map<String, Object> update,
                                             Map<String, Number> inc) {
        return delegate.findAndModify(criteria, update, inc);
    }

    class JsonBlobAwareFilter implements Filter<Map<String, Object>>{

        private Filter<Map<String, Object>> delegate;

        public JsonBlobAwareFilter(Filter<Map<String, Object>> delegate) {
            this.delegate = delegate;
        }

        @Override
        public Iterator<Map<String, Object>> fetchAll() {
            return new JsonBlobAwareIterator(delegate.fetchAll());
        }

        @Override
        public Map<String, Object> fetchOne() {
            Map<String, Object> result = delegate.fetchOne();
            fromBytes(result);
            return result;
        }

        @Override
        public long count() {
            return delegate.count();
        }

        @Override
        public void offset(int offset) {
            delegate.offset(offset);
        }

        @Override
        public int offset() {
            return delegate.offset();
        }

        @Override
        public void limit(int limit) {
            delegate.limit(limit);
        }

        @Override
        public int limit() {
            return delegate.limit();
        }

        @Override
        public void orderBy(String fieldName, int dir) {
            delegate.orderBy(fieldName, dir);
        }

        @Override
        public void orderBy(List<OrderByField> fields) {
            delegate.orderBy(fields);
        }

    }
    class JsonBlobAwareIterator extends ConverterIterator<Map<String, Object>, Map<String,Object>>{

        public JsonBlobAwareIterator(Iterator<Map<String, Object>> source) {
            super(source);
        }

        @Override
        protected Map<String, Object> convert(Map<String, Object> input) {
            
            fromBytes(input);
            return input;
        }

    }
}
