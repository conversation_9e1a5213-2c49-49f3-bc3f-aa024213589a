package com.wosai.upay.model.api;

public class QueryResponse {
    public static final String SN = "sn";
    public static final String TSN = "tsn";
    public static final String CLIENT_SN = "client_sn";
    public static final String CLIENT_TSN = "client_tsn";
    public static final String CTIME = "ctime";
    public static final String STATUS = "status";
    public static final String PAYWAY = "payway";
    public static final String PAYWAY_NAME = "payway_name";
    public static final String SUB_PAYWAY = "sub_payway";
    public static final String ORDER_STATUS = "order_status";
    public static final String PAYER_LOGIN = "payer_login";
    public static final String PAYER_UID = "payer_uid";
    public static final String TRADE_NO = "trade_no";
    public static final String CHANNEL_TRADE_NO = "channel_trade_no";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String NET_AMOUNT = "net_amount";
    public static final String FINISH_TIME = "finish_time";
    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";
    public static final String SUBJECT = "subject";
    public static final String DESCRIPTION = "description";
    public static final String STORE_ID = "store_id";
    public static final String TERMINAL_ID = "terminal_id";
    public static final String TCP_MODIFIED = "tcp_modified";
    public static final String OPERATOR = "operator";
    public static final String REFLECT = "reflect";
    public static final String PROVIDER_RESPONSE = "provider_response";
    public static final String SETTLEMENT_AMOUNT = "settlement_amount";
    public static final String ADVANCE_AMOUNT = "advance_amount";
    public static final String GOODS_DETAILS = "goods_details";
    public static final String VOUCHER_DETAILS = "voucher_details";
    public static final String SUB_IS_SUBSCRIBE = "sub_is_subscribe";
    public static final String BATCH_BILL_NO = "batch_bill_no";
    public static final String SYS_TRACE_NO = "sys_trace_no";
    public static final String PAYER_CURRENCY = "payer_currency";
    public static final String PAYER_AMOUNT = "payer_amount";
    public static final String EXCHANGE_RATE = "exchange_rate";
    public static final String AUTH_NO = "auth_no";
    public static final String REFER_NUMBER = "refer_number";
    public static final String FREEZE_TIME = "freeze_time";
    public static final String CONSUME_TIME = "consume_time";
    public static final String PAYER_INFO = "payer_info";

    public static final String INSTALLMENT_INFO = "installment_info";
    public static final String INSTALLMENT_INFO_NUM = "num";


    public static final String RESULT_CODE_SUCCESS = "SUCCESS";
    public static final String RESULT_CODE_QUERY_SUCCESS = "QUERY_SUCCESS";
    public static final String RESULT_CODE_QUERY_FAIL = "QUERY_FAIL";
}
