package com.wosai.upay.model.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 授权响应对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {
    /**
     * 是否成功
     */
    private boolean isSuccess;
    
    /**
     * 错误码
     */
    private String code;
    
    /**
     * 错误信息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private Map<String, Object> data;

    public static AuthResponse ofSuccess(Map<String, Object> data) {
        return new AuthResponse(true, null, null, data);
    }

    public static AuthResponse ofFailed(String code, String message) {
        return new AuthResponse(false, code, message, null);
    }
}