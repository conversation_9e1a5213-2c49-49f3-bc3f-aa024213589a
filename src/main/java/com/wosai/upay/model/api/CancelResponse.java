package com.wosai.upay.model.api;

public class CancelResponse {
    public static final String RESULT_CODE_CANCEL_ABORT_SUCCESS = "CANCEL_ABORT_SUCCESS";
    public static final String RESULT_CODE_CANCEL_ABORT_IN_PROGRESS = "CANCEL_ABORT_IN_PROGRESS";
    public static final String RESULT_CODE_CANCEL_ABORT_ERROR = "CANCEL_ABORT_ERROR";
    public static final String RESULT_CODE_CANCEL_SUCCESS = "CANCEL_SUCCESS";
    public static final String RESULT_CODE_CANCEL_IN_PROGRESS = "CANCEL_IN_PROGRESS";
    public static final String RESULT_CODE_CANCEL_ERROR = "CANCEL_ERROR";

    public static final String OUT_TRADE_NO = "out_trade_no";   //商户订单号
    public static final String CANCEL_REASON = "cancel_reason"; //取消原因
    public static final String CANCEL_TIME = "cancel_time";     //取消时间戳
}
