package com.wosai.upay.model.api;

public class PayResponse {
    public static final String RESPONSE = "response";
    public static final String CODE_URL = "code_url";
    public static final String ALIPAY = "alipay";
    public static final String QR_CODE = "qr_code";
    public static final String PIC_URL = "pic_url";
    public static final String PAY_URL = "pay_url";
    public static final String APP_ID =  "appid";
    public static final String PARTNER_ID = "partnerid";
    public static final String PREPAY_ID = "prepayid";
    public static final String NONCESTR = "noncestr";
    public static final String TIMESTAMP = "timestamp";
    public static final String PACKAGE = "package";
    public static final String STATUS = "status";
    public static final String QR_CODE_IMAGE_URL = "qr_code_image_url";
    public static final String WAP_PAY_REQUEST = "wap_pay_request";
    
    public static final String RESULT_CODE_PAY_SUCCESS = "PAY_SUCCESS";
    public static final String RESULT_CODE_REFUND_SUCCESS = "REFUND_SUCCESS";
    public static final String RESULT_CODE_PAY_FAIL = "PAY_FAIL";
    public static final String RESULT_CODE_PAY_IN_PROGRESS = "PAY_IN_PROGRESS";
    public static final String RESULT_CODE_PAY_FAIL_IN_PROGRESS = "PAY_FAIL_IN_PROGRESS";
    public static final String RESULT_CODE_PAY_FAIL_ERROR = "PAY_FAIL_ERROR";

    public static final String RESULT_CODE_PRECREATE_SUCCESS = "PRECREATE_SUCCESS";
    public static final String RESULT_CODE_PRECREATE_FAIL = "PRECREATE_FAIL";
    public static final String RESULT_CODE_PRECREATE_FAIL_ERROR = "PRECREATE_FAIL_ERROR";
    public static final String RESULT_CODE_PRECREATE_FAIL_IN_PROGRESS = "PRECREATE_FAIL_IN_PROGRESS";

    //确认支付状态
    public static final String RESULT_CODE_CONFIRM_PAY_SUCCESS = "CONFIRM_PAY_SUCCESS";
    public static final String RESULT_CODE_CONFIRM_PAY_FAIL = "CONFIRM_PAY_FAIL";
    public static final String RESULT_CODE_CONFIRM_PAY_FAIL_ERROR = "CONFIRM_PAY_FAIL_ERROR";
}
