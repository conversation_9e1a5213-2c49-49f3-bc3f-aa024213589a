package com.wosai.upay.model.dao;


import com.wosai.upay.core.meta.Provider;

public class Order {
    public static final String TN = "order";

    public static final int STATUS_CREATED = 0;
    public static final int STATUS_DEPOSIT_CREATED = 1;

    public static final int STATUS_PAID = 1200;
    public static final int STATUS_PAY_CANCELED = 1300;
    public static final int STATUS_PAY_ERROR = 1501;

    public static final int STATUS_REFUND_INPROGRESS = 2100;

    public static final int STATUS_REFUNDED = 2201;
    public static final int STATUS_PARTIAL_REFUNDED = 2210;
    public static final int STATUS_REFUND_ERROR = 2501;

    public static final int STATUS_CANCEL_INPROGRESS = 3100;
    public static final int STATUS_CANCELED = 3201;
    public static final int STATUS_CANCEL_ERROR = 3501;

    public static final int STATUS_DEPOSIT_FREEZED = 4200;
    public static final int STATUS_DEPOSIT_FREEZE_CANCELED = 4300;
    public static final int STATUS_DEPOSIT_FREEZE_ERROR = 4501;

    public static final int STATUS_DEPOSIT_CANCEL_INPROGRESS = 5100;
    public static final int STATUS_DEPOSIT_CANCELED = 5201;
    public static final int STATUS_DEPOSIT_CANCEL_ERROR = 5501;

    public static final int STATUS_DEPOSIT_CONSUME_INPROGRESS = 6100;
    public static final int STATUS_DEPOSIT_CONSUMED = 6201;
    public static final int STATUS_DEPOSIT_CONSUME_ERROR = 6501;

    public static final int CONTEXT_TYPE_STORE = 1;
    public static final int CONTEXT_TYPE_TERMINAL = 2;

    public static final int PROVIDER_CIBBANK = 1001;
    public static final int PROVIDER_LAKALA = 1002;
    public static final int PROVIDER_CITICBANK = 1003;
    public static final int PROVIDER_CEBBANK = 1004;
    public static final int PROVIDER_CZBBANK = 1005;
    public static final int PROVIDER_SSRBANK = 1006;
    public static final int PROVIDER_CMBCBANK = 1007; // 威富通 -> 民生
    public static final int PROVIDER_CIBGZBANK = 1008;
    public static final int PROVIDER_CITICZJBANK = 1009;
    public static final int PROVIDER_DIRECT_CMBCBANK = 1010; // 直连民生
    public static final int PROVIDER_LKLWANMA = 1011;
    public static final int PROVIDER_NUCC = 1013;
    public static final int PROVIDER_UNIONPAY = 1014;
    public static final int PROVIDER_CIBSHBANK = 1015;
    public static final int PROVIDER_DIRECT_UNIONPAY = 1016;
    public static final int PROVIDER_UNIONPAY_OPEN = 1017;
    public static final int PROVIDER_CHINAUMS = 1018;
    public static final int PROVIDER_UNIONPAY_ONLINE = 1019; //银联网银支付
    public static final int PROVIDER_TL = 1020; //通联支付
    public static final int PROVIDER_UEPAY = 1021; //澳门极易付通道
    public static final int PROVIDER_CMB = 1022; //招商银行通道
    public static final int PROVIDER_PSBCBANK = 1023; //邮储银行通道
    public static final int PROVIDER_FOXCONN = 1025; //富士康富圈圈
    public static final int PROVIDER_CGBBANK = 1024; //广发银行通道
    public static final int PROVIDER_CCB = 1026; //建设银行通道
    public static final int PROVIDER_HXBANK = 1028; //华夏银行通道
    public static final int PROVIDER_ICBCBANK = 1030; //工商银行通道

    public static final int PROVIDER_LAKALA_UNION_PAY = Provider.LAKALA_UNION_PAY.getCode(); // 拉卡拉银联
    public static final int PROVIDER_LAKALA_UNION_PAY_V3 = Provider.LAKALA_UNION_PAY_V3.getCode(); // 拉卡拉银联 智能pos
    public static final int PROVIDER_TL_SYB = Provider.TL_SYB.getCode(); //通联收银宝
    public static final int PROVIDER_CCB_GIFT_CARD = Provider.CCB_GIFT_CARD.getCode(); //建行福利卡
    public static final int PROVIDER_HAIKE_UNION_PAY = Provider.HAIKE_UNION_PAY.getCode(); //海科银联
    public static final int PROVIDER_PAD_PAY = Provider.PAB.getCode(); //平安
    public static final int PROVIDER_FUYOU = Provider.FUYOU.getCode(); //富友
    public static final int PROVIDER_BOCOM = Provider.BOCOM.getCode(); //交通银行通道

    public static final int PROVIDER_ABCBANK =   Provider.ABC.getCode(); //农业银行通道
    public static final int PROVIDER_ENTPAY = Provider.ENTPAY.getCode(); //微企付

    public static final int PROVIDER_ZJTLCB = Provider.ZJTLCB.getCode(); //浙江泰隆

    public static final int PROVIDER_FJNX = Provider.FJNX.getCode(); //福建农信
    public static final int PROVIDER_JY_CARD = Provider.JY_CARD.getCode(); //锦医一卡通
    public static final int PROVIDER_SPDB = Provider.SPDB.getCode(); //浦发银行
    public static final int PROVIDER_JSB = Provider.JSB_BANK.getCode(); //江苏银行

    public static final int PROVIDER_LZCCB = Provider.LZCCB_BANK.getCode(); //泸州银行
    public static final int PROVIDER_ZTKX = Provider.ZTKX.getCode(); //中投科信
    public static final int PROVIDER_YOP = Provider.YOP.getCode(); //易宝
    public static final int PROVIDER_AIRWALLEX = Provider.AIRWALLEX.getCode(); //空中云汇
    public static final int PROVIDER_PKX_AIRPORT = Provider.PKX_AIRPORT.getCode(); //首都机场
    public static final int PROVIDER_XZX = Provider.XZX.getCode(); //新中新
    public static final int PROVIDER_HOPE_EDU = Provider.HOPE_EDU.getCode(); // 院校通
    public static final int PROVIDER_GUOTONG = Provider.GUOTONG.getCode(); //国通
    public static final int PROVIDER_WECARD = Provider.WECARD.getCode(); //腾讯微卡支付

    public static final int PROVIDER_MACAU_PASS = Provider.MACAU_PASS.getCode(); // 澳门通


    public static final int PROVIDER_PSBCBANK_SX = Provider.PSBC_BANK_SX.getCode(); //邮储银行山西分行



    public static final int PAYWAY_ALIPAY = 1;
    public static final int PAYWAY_ALIPAY2 = 2;
    public static final int PAYWAY_WEIXIN = 3;
    public static final int PAYWAY_BAIFUBAO = 4;
    public static final int PAYWAY_JD = 5;
    public static final int PAYWAY_QQWALLET = 6;
    public static final int PAYWAY_APPLEPAY = 7;
    public static final int PAYWAY_LKLWALLET = 8;//拉卡拉钱包
    public static final int PAYWAY_CMCC = 9;// 和支付
    @Deprecated
    public static final int PAYWAY_LKL_UNIONPAY = 17;// 拉卡拉银联二维码支付
    public static final int PAYWAY_UNIONPAY = 17;//银联云闪付
    public static final int PAYWAY_BESTPAY = 18;// 翼支付
    public static final int PAYWAY_WEIXIN_HK = 19;// 微信香港本地支付
    public static final int PAYWAY_ALIPAY_INTL = 20;// 支付宝国际版支付
    public static final int PAYWAY_BANKCARD = 21;// 银行卡
    public static final int PAYWAY_SODEXO = 22;// 索迪斯
    public static final int PAYWAY_DCEP = 23;// 中国数字货币
    public static final int PAYWAY_FOXCONN = 25;// 富士康钱包
    public static final int PAYWAY_GRABPAY = 26;// grabpay钱包
    public static final int PAYWAY_BANKACCOUNT = 27;// 银行转账
    public static final int PAYWAY_CAMPUS_CARD = 28;// 校园卡
    public static final int PAYWAY_MACAU_PASS = 29;// 澳门通
    public static final int PAYWAY_HOPE_EDU = 32;// 院校通-一码通

    public static final int PAYWAY_GIFT_CARD = 101;//礼品卡支付方式
    public static final int PAYWAY_PREPAID_CARD = 105;//储值卡支付方式

    public static final int PAYWAY_CCB_APP = 109; //建行生活APP支付

    public static final int PAYWAY_CMB_APP = 110; //招行生活APP支付
    public static final int PAYWAY_WELFARE_CARD = 111; //福利卡
    public static final int PAYWAY_DOUYINQUAN = 112; //抖音券
    public static final int PAYWAY_MEITUANQUAN = 113; //美团券
    public static final int PAYWAY_CCB_GIFT_CARD = 114; //建行福利卡

    public static final int SUB_PAYWAY_BARCODE = 1;
    public static final int SUB_PAYWAY_QRCODE = 2;
    public static final int SUB_PAYWAY_WAP = 3;
    public static final int SUB_PAYWAY_MINI = 4;
    public static final int SUB_PAYWAY_APP = 5;//app
    public static final int SUB_PAYWAY_H5 = 6;//h5

    public static final String SN = "sn";                           // 订单号 VARCHAR(20)
    public static final String CLIENT_SN = "client_sn";             // 商户订单号/同一个商户下唯一，否则视为重复订单 VARCHAR(32)
    public static final String SUBJECT = "subject";                 // 标题 VARCHAR(45)
    public static final String BODY = "body";                       // 详情 VARCHAR(255)
    public static final String ITEMS = "items";                     // 明细 BLOB
    public static final String PAYMENTS = "payments";               // ITEMS 消费者在喔噻平台的付款方式明细 List {type, amount_total, net_amount}
    public static final String CHANNEL_PAYMENTS = "channel_payments";// ITEMS 消费者在支付通道的付款方式明细 List {type, amount_total, net_amount}

    public static final String NET_ITEMS = "net_items";             // 扣除退款商品后的明细 BLOB
    public static final String STATUS = "status";                   // 状态 INT
    public static final String TCP_MODIFIED = "tcp_modified";       // 订单是否触发trade-coprocessor处理逻辑 TINYINT
    public static final String ORIGINAL_TOTAL = "original_total";   // 原始总金额 BIGINT
    public static final String NET_ORIGINAL = "net_original";       // 扣除退款后的原始金额的净值 BIGINT
    public static final String EFFECTIVE_TOTAL = "effective_total"; // 向支付通道请求的支付总金额 BIGINT (trade-coprocessor可能会返回不等于original_total的值）
    public static final String NET_EFFECTIVE = "net_effective";     // 扣除退款后的向支付通道请求的支付金额的净值 BIGINT
    public static final String TOTAL_DISCOUNT = "total_discount";   // 向支付通道请求的支付总金额中的折扣部分，包括商户补贴和服务商补贴
    public static final String NET_DISCOUNT = "net_discount";       // 如果没有退款操作，net_discount 等于total_discount。否则为退款之后剩余的折扣。有些服务商规定折扣部分优先退。
    public static final String BUYER_UID = "buyer_uid";             // 付款人在支付通道的用户ID  VARCHAR(45)
    public static final String BUYER_LOGIN = "buyer_login";         // 付款人在支付通道的登录帐号 VARCHAR(45)
    public static final String MERCHANT_ID = "merchant_id";         // 商户ID VARCHAR(37)  商户记录的UUID
    public static final String STORE_ID = "store_id";               // 门店ID VARCHAR(37)  门店记录的UUID
    public static final String TERMINAL_ID = "terminal_id";         // 终端ID VARCHAR(37)  终端记录的UUID
    public static final String OPERATOR = "operator";               // 原始支付交易的操作员 VARCHAR(45) 姓名或其它ID
    public static final String PROVIDER = "provider";               // 支付通道 INT 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉'
    public static final String PAYWAY = "payway";                   // 支付通道 INT
    public static final String SUB_PAYWAY = "sub_payway";           // 支付方式 INT
    public static final String TRADE_NO = "trade_no";               // 支付通道返回的交易凭证号 VARCHAR(128)
    public static final String REFLECT = "reflect";                 // 商户上传的附加字段，保存在订单中。终端查询的时候原样返回。
    public static final String NFC_CARD = "nfc_card";               // nfc交易的时候的银行卡号

    public static enum Status {
        CREATED(STATUS_CREATED),
        PAID(STATUS_PAID),
        PAY_CANCELED(STATUS_PAY_CANCELED),
        PAY_ERROR(STATUS_PAY_ERROR),
        REFUND_INPROGRESS(STATUS_REFUND_INPROGRESS),
        REFUNDED(STATUS_REFUNDED),
        PARTIAL_REFUNDED(STATUS_PARTIAL_REFUNDED),
        REFUND_ERROR(STATUS_REFUND_ERROR),
        CANCEL_INPROGRESS(STATUS_CANCEL_INPROGRESS),
        CANCELED(STATUS_CANCELED),
        CANCEL_ERROR(STATUS_CANCEL_ERROR),
        DEPOSIT_CREATED(STATUS_DEPOSIT_CREATED),
        DEPOSIT_FREEZED(STATUS_DEPOSIT_FREEZED),
        DEPOSIT_FREEZE_CANCELED(STATUS_DEPOSIT_FREEZE_CANCELED),
        DEPOSIT_FREEZE_ERROR(STATUS_DEPOSIT_FREEZE_ERROR),
        DEPOSIT_CANCEL_INPROGRESS(STATUS_DEPOSIT_CANCEL_INPROGRESS),
        DEPOSIT_CANCELED(STATUS_DEPOSIT_CANCELED),
        DEPOSIT_CANCEL_ERROR(STATUS_DEPOSIT_CANCEL_ERROR),
        DEPOSIT_CONSUME_INPROGRESS(STATUS_DEPOSIT_CONSUME_INPROGRESS),
        DEPOSIT_CONSUMED(STATUS_DEPOSIT_CONSUMED),
        DEPOSIT_CONSUME_ERROR(STATUS_DEPOSIT_CONSUME_ERROR),
        INVALID_STATUS_CODE(99999);

        private int code;
        Status(int code) {
            this.code = code;
        }

        public static Status fromCode(int code) {
            for(Status status: values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return INVALID_STATUS_CODE;
        }


    }
}
