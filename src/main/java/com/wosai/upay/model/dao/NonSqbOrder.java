package com.wosai.upay.model.dao;

public class NonSqbOrder {
    public static final String TERMINAL_SN = "terminal_sn";             // 映射的终端号
    public static final String MERCHANT_ID = "merchant_id";             // 映射的商户id
    public static final String CLIENT_SN = "client_sn";                 // 商户订单号
    public static final String TRADE_NO = "trade_no";                   // 收款通道订单号
    public static final String AMOUNT = "amount";                       // 订单金额
    public static final String IS_SYNC = "is_sync";                     // 订单是否已同步到喔噻
    public static final String CHANNEL_TRADE_NO = "channel_trade_no";   // 支付源订单号
    public static final String FINISH_TIME = "finish_time";             // 支付时间
    
    public static final int IS_SYNC_TRUE = 1;                           // 已同步
    public static final int IS_SYNC_FALSE = 0;                          // 未同步
}
