package com.wosai.upay.model.dao;

import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class Transaction {
    public static final String TN = "transaction";
    
    public static final int TYPE_PAYMENT = 30;
    public static final int TYPE_REFUND_REVOKE = 31;
    public static final int TYPE_REFUND = 11;
    public static final int TYPE_CANCEL = 10;
    public static final int TYPE_DEPOSIT_FREEZE = 32;
    public static final int TYPE_DEPOSIT_CANCEL = 12;
    public static final int TYPE_DEPOSIT_CONSUME = 13;

    public static final int STATUS_CREATED = 0;
    
    public static final int STATUS_SUCCESS = 2000;
    public static final int STATUS_FAIL_CANCELED = 2001;
    public static final int STATUS_ABORTED = 2002;
    
    public static final int STATUS_FAIL_PROTOCOL_1 = 2101;
    public static final int STATUS_FAIL_IO_1 = 2102;
    public static final int STATUS_FAIL_PROTOCOL_2 = 2103;
    public static final int STATUS_FAIL_IO_2 = 2104;
    public static final int STATUS_FAIL_PROTOCOL_3 = 2105;
    public static final int STATUS_FAIL_IO_3 = 2106;
    
    public static final int STATUS_FAIL_ERROR = 2107;
    public static final int STATUS_CANCEL_ERROR = 2108;
    public static final int STATUS_REFUND_ERROR = 2109;
    public static final int STATUS_CONSUME_ERROR = 2110;
    
    public static final int STATUS_IN_PROG = 1001;
    public static final int STATUS_ERROR_RECOVERY = 1002;
    public static final int STATUS_ABORTING = 1003;
    public static final int STATUS_QUERY_EXPIRE = 1004; // 状态机中的查询超时状态，此状态不会变更到DB中
    public static final int STATUS_PRE_SUCCESS = 1100;

    public static final int PRODUCT_APP = 1;
    public static final int PRODUCT_SDK = 2;
    public static final int PRODUCT_POS = 3;




    /*EXTRA_PARAMS 中cancel_type常量定义*/
    public static final String CANCEL_TYPE_DEVICE = "device"; //设备自动撤单
    public static final String CANCEL_TYPE_CASHIER = "cashier"; //收银员撤单
    public static final String CANCEL_TYPE_RECONCILE = "reconcile";  //勾兑撤单





    public static final String USER_AUTH_CODE = "user_auth_code";       // 银联授权码
    public static final String APP_UP_IDENTIFIER = "app_up_identifier"; // 银联支付标识


    public static final String TSN = "tsn";                     // 交易流水号（按规则自动生成） VARCHAR(20)
    public static final String CLIENT_TSN = "client_tsn";       // 商户流水号（商户下唯一）VARCHAR(32)
    
    public static final String TYPE = "type";                   // 类型 INT
    public static final String SUBJECT = "subject";             // 标题 VARCHAR(45)
    public static final String BODY = "body";                   // 详情 VARCHAR(255)
    public static final String STATUS = "status";               // 状态 INT UNSIGNED
    public static final String ORIGINAL_AMOUNT = "original_amount";     // 原始金额 BIGINT
    public static final String EFFECTIVE_AMOUNT = "effective_amount";   // 向支付通道请求的金额 BIGINT
    public static final String PAID_AMOUNT = "paid_amount";     // 消费者实际支付金额
    public static final String RECEIVED_AMOUNT = "received_amount"; // 收钱吧或正式商户在支付通道的实际收款金额
    public static final String ITEMS = "items";                 // 明细 BLOB  payments：支付方式 {type, amount}

    public static final String EFFECTIVE_REFUND_AMOUNT = "effective_refund_amount";   // 实际退款金额
    
    
    public static final String BUYER_UID = "buyer_uid";         // 付款人在支付通道的用户ID VARCHAR(45)
    public static final String BUYER_LOGIN = "buyer_login";     // 付款人在支付通道的登录账户 VARCAR(45)
    public static final String MERCHANT_ID = "merchant_id";     // 商户ID VARCHAR(37)  商户记录的UUID
    public static final String STORE_ID = "store_id";           // 门店ID VARCHAR(37) 门店记录的UUID
    public static final String TERMINAL_ID = "terminal_id";     // VARCHAR(37) 终端记录的UUID
    public static final String OPERATOR = "operator";           // VARCHAR(45) 操作员姓名或其它ID
    
    public static final String ORDER_SN = "order_sn";           // 订单号 VARCHAR(20)
    public static final String ORDER_ID = "order_id";           // 订单ID VARCHAR(37)

    public static final String PROVIDER = "provider";           // 支付通道 INT 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉
    public static final String PAYWAY = "payway";               // 支付通道 INT UNSIGNED
    public static final String SUB_PAYWAY = "sub_payway";       // 支付方式 INT UNSIGNED
    public static final String TRADE_NO = "trade_no";           // 支付通道返回的交易凭证号 VARCHAR(128)
    public static final String CHANNEL_TRANS_NO = "channel_trans_no";           // 支付通道返回的交易流水no VARCHAR(128)
    
    public static final String PRODUCT_FLAG = "product_flag";   // 产品标志 INT

    public static final String EXTRA_PARAMS = "extra_params";   // 可选参数（poi, notify_url, remark, barcode, client_terminal_id, client_store_id,cross_mch_refund) BLOB
    public static final String POI = "poi";                     // EXTRA_PARAMS
    public static final String LONGITUDE = "longitude";         // poi
    public static final String LATITUDE = "latitude";           // poi
    public static final String NOTIFY_URL = "notify_url";       // EXTRA_PARAMS
    public static final String REMARK = "remark";               // EXTRA_PARAMS
    public static final String BARCODE = "barcode";             // EXTRA_PARAMS
    public static final String BARCODE_TYPE = "barcode_type";   // EXTRA_PARAMS
    public static final String DEVICE_ID = "device_id";         // EXTRA_PARAMS
    public static final String PAYER_UID = "payer_uid";         // EXTRA_PARAMS
    public static final String CANCEL_TYPE = "cancel_type";     // EXTRA_PARAMS
    public static final String CLIENT_IP = "client_ip";         // EXTRA_PARAMS
    public static final String SQB_CATERING = "sqb_catering";   // EXTRA_PARAMS 扫码点餐信息
    public static final String SQB_HB_FQ_SELLER_SERVICE_CHARGE = "sqb_hb_fq_seller_service_charge";     // EXTRA_PARAMS 花呗商家分期手续费
    public static final String SQB_HB_FQ_BUYER_SERVICE_CHARGE = "sqb_hb_fq_buyer_service_charge";       // EXTRA_PARAMS 花呗商家分期手续费
    public static final String SQB_FQ_SELLER_SERVICE_CHARGE = "sqb_fq_seller_service_charge";           // EXTRA_PARAMS 商家分期手续费
    public static final String SQB_FQ_BUYER_SERVICE_CHARGE = "sqb_fq_buyer_service_charge";             //EXTRA_PARAMS 买家分期手续费
    public static final String SQB_FQ_SERVICE_TYPE = "sqb_fq_service_type";                             //EXTRA_PARAMS 分期类型 1.花呗分期 2.信用卡分期 3.微信分付
    public static final String SQB_FQ_AMOUNT = "fq_amount";                                             //EXTRA_PARAMS 实际分期的金额,只有发生组合支付的时候才会有
    public static final String SQB_FQ_COMBINATION_PAY = "combination_pay";                              //EXTRA_PARAMS 是否为组合支付
    public static final String HB_FQ_SPONSOR = "hb_fq_sponsor";       // EXTRA_PARAMS 花呗分期发起人 1：商户侧发起分期 2：消费者支付时选择分期 兼容老数据暂时保留
    public static final String FQ_SPONSOR = "fq_sponsor";       // EXTRA_PARAMS 分期发起人 1：商户侧发起分期 2：消费者支付时选择分期
    public static final String SQB_CSB_TO_WAP_SN = "sqb_csb_to_wap_sn";     // EXTRA_PARAMS CSB转WAP收钱吧单号
    public static final String SQB_SCENE = "sqb_scene";                     // EXTRA_PARAMS 支付场景
    public static final String SQB_WALLET_NAME = "sqb_wallet_name";         // EXTRA_PARAMS c端钱包名称
    public static final String SQB_PRODUCT_FLAG = "sqb_product_flag";       // EXTRA_PARAMS 客户端上送的，活动服务返回的product_flag值
    public static final String SQB_GEN_SN = "sqb_gen_sn";                   // EXTRA_PARAMS 网关生成的客户端订单号
    public static final String SQB_REFUND_FLAG = "sqb_refund_flag";         // EXTRA_PARAMS 退款标识
    public static final String CROSS_MERCHANT_REFUND = "cross_mch_refund";  // EXTRA_PARAMS
    public static final String IS_MULTIPLE_PAY = "is_multiple_pay";         // EXTRA_PARAMS 是否多次重复支付
    public static final String RETURN_URL = "return_url";                   // EXTRA_PARAMS
    public static final String PROFIT_SHARING = "profit_sharing";           // EXTRA_PARAMS 分账参数
    public static final String PROFIT_SHARING_RETRY_AND_HAS_FINISHED = "retry_and_has_finished";           // EXTRA_PARAMS 分账重试参数
    public static final String PROFIT_SHARING_SHARING_APP = "sharing_app";  // 分账业务方
    public static final String SHARING_APP_PAY = "1";                       // 移动支付分账
    public static final String SHARING_APP_SFT = "2";                       // 收付通分账
    public static final String SQB_IP = "sqb_ip";                           // EXTRA_PARAMS 用户上送的ip信息
    public static final String SQB_MAC_ID = "sqb_mac_id";                   // EXTRA_PARAMS 设备MAC地址
    public static final String SQB_STATION = "sqb_station";                 // EXTRA_PARAMS 设备基站
    public static final String SQB_PAY_SOURCE = "sqb_pay_source";           // EXTRA_PARAMS 支付打开方式 比如 小程序扫码, 长按识别二维码
    public static final String SQB_ALIPAY_TIMEOUT = "sqb_alipay_timeout";   // EXTRA_PARAMS 支付宝垫资保付自定义超时时间
    public static final String SQB_AD_CODE = "sqb_ad_code";                 // EXTRA_PARAMS 市县6位地区码
    public static final String SQB_VOICE = "sqb_voice";                     // EXTRA_PARAMS 收钱吧语音播报标识 0：默认语音播报 2：堂食订单语音播报 ...
    public static final String SQB_INNER_BIZ = "sqb_inner_biz";             // EXTRA_PARAMS 内部系统业务自留用
    public static final String SQB_ACTIVITY_BIZ_EXT = "sqb_activity_biz_ext";// EXTRA_PARAMS 活动服务业务透传字段
    public static final String SQB_ACTIVITY_DISCOUNT = "sqb_activity_discount";// EXTRA_PARAMS 活动服务优惠标记
    public static final String SQB_BIZ_ORDER_SN = "sqb_biz_order_sn";        // EXTRA_PARAMS 收钱吧业务订单号

    public static final String SQB_CLIENT_SEQ_NO = "sqb_client_seq_no";     // EXTRA_PARAMS 拉卡拉刷卡交易退款 终端流水号
    public static final String SQB_CLIENT_BATCH_NO = "sqb_client_batch_no"; // EXTRA_PARAMS 拉卡拉刷卡交易退款 终端批次号
    public static final String SQB_LIMIT_PAYER_ADULT = "sqb_limit_payer_adult";     // EXTRA_PARAMS 限制未成年人支付标记
    public static final String SQB_USER_ID = "sqb_user_id";                 // EXTRA_PARAMS uc_user_id
    public static final String SQB_PAYER_AUTH = "sqb_payer_auth";                 // EXTRA_PARAMS 芝麻先享用户授权标识， true - 已授权; false - 未授权
    public static final String SQB_PRODUCT_CODE = "sqb_product_code";             // EXTRA_PARAMS 产品标识 ： 芝麻先享 ALIPAY_ZM
    public static final String SQB_ZFB_SHOWCASE_FLAG = "sqb_zfb_showcase";        //EXTRA_PARAMS 支付宝橱窗标识;
    public static final String SQB_COMBINED_PAYMENT_SN = "sqb_combined_payment_sn";   // EXTRA_PARAMS 组合支付单号
    public static final String SQB_PAY_PATH = "sqb_pay_path";                      //EXTRA_PARAMS 用户行为-支付路径;
    public static final String SQB_BIZ_MODEL = "sqb_biz_model";                    //EXTRA_PARAMS 业务模式
    public static final String SQB_PROMOTION_DETAIL = "sqb_promotion_detail";      // EXTRA_PARAMS 收钱吧业务方优惠明细
    public static final String SQB_TRACE_ID = "sqb_trace_id";              // EXTRA_PARAMS 收钱吧trace_id


    public static final String SQB_CANCEL_LIMIT = "sqb_cancel_limit";                       //EXTRA_PARAMS 撤单限制， exists - 已创建过的订单不允许撤单; paid - 已支付的订单不允许撤单
    public static final String SQB_CANCEL_REASON = "sqb_cancel_reason";                      //EXTRA_PARAMS 订单取消原因
    public static final String SQB_CANCEL_NOTIFY_URL = "sqb_cancel_notify_url";              //EXTRA_PARAMS 订单取消支付通知地址

    public static final String SQB_WX_VERSION = "sqb_weixin_version"; //EXTENDED_PARAMS 优先使用微信哪套接口实现

    public static final String WEIXIN_DEFAULT_SUBSIDY_PERIOD_TYPE = "weixin_default_subsidy_period_type";//EXTENDED_PARAMS 跳转微信收银台时默认选择的付款类型
    public static final String WEIXIN_DEFAULT_SELECTED_INSTALLMENT_NUMBER = "weixin_default_selected_installment_number";//EXTENDED_PARAMS 默认选择期数,如果weixin_default_subsidy_period_type为PERIOD必填


    public static final String QR_CODE_TYPE = "qrCodeType";                       // EXTENDED_PARAMS 二维码类型
    public static final String QR_CODE = "qrCode";                                // EXTENDED_PARAMS 二维码类型
    public static final String EXTENDED_USER_AUTH_CODE = "userAuthCode";          // EXTENDED_PARAMS 银联授权码
    public static final String TERMINAL_ENCRYPT_RAND_NUM = "encrypt_rand_num";    // EXTENDED_PARAMS 加密随机数
    public static final String TERMINAL_SECRET_TEXT = "secret_text";              // EXTENDED_PARAMS 终端密文
    public static final String DELAY_TRADE = "delay_trade";                       // EXTENDED_PARAMS 是否延迟交易。以微信周期代扣款为例，为false时表示实时扣款；为true时表示周期扣款





    public static final String EXTRA_OUT_FIELDS = "extra_out_fields";   // 可选的交易流水的返回字段（qrcode)BLOB
    public static final String RISK_INFO = "risk_info";                 // EXTRA_OUT_FIELDS 支付源返回的风控信息
    public static final String QRCODE = "qrcode";                       // EXTRA_OUT_FIELDS
    public static final String CHANNEL_TRADE_NO = TRADE_NO;             // EXTRA_OUT_FIELDS 支付通道的订单号
    public static final String WAP_PAY_REQUEST = "wap_pay_request";     // EXTRA_OUT_FIELDS 针对支付通道生成的wap支付请求。里面的内容由wap支付前端和支付通道之间约定。
    public static final String PAYMENTS = "payments";                   // EXTRA_OUT_FIELDS 消费者在支付通道的付款方式明细 List
    public static final String REDIRECT_URL = "redirectUrl";             // EXTRA_OUT_FIELDS云闪付跳转地址
    public static final String DEPOSIT_REFUND_PAYMENTS = "deposit_refund_payments";                   // 预授权完成退款
    public static final String PREPAY_ID = "prepay_id";                  // 预下单响应的 prepay_id
    public static final String TRANSACTION_ID = "transaction_id";                 // 调起收银台响应的 transactionId

    public static final String MERCHANT_SECOND_NO = "merchantSecondNo"; //WAP_PAY_REQUEST 下银联商编

    public static final String PAYMENT_TYPE = Payment.TYPE;                   // PAYMENT 付款方式
    public static final String PAYMENT_ORIGIN_TYPE = Payment.ORIGIN_TYPE;     // PAYMENT 原始付款方式
    public static final String PAYMENT_ORIGIN_NAME = Payment.ORIGIN_NAME;     // PAYMENT 原始付款或者优惠名称
    public static final String PAYMENT_AMOUNT = Payment.AMOUNT;               // PAYMENT 付款金额
    public static final String PAYMENT_SOURCE = Payment.SOURCE;               // PAYMENT 付款来源 券id

    
    public static final String NET_AMOUNT = "net_amount";               // PAYMENT 剩余的金额
    public static final String WEIXIN_APPID = "weixin_appid";           // EXTRA_OUT_FIELDS 微信交易buyer_uid对应的公众号id
    public static final String WEIXIN_SUB_APPID = "weixin_sub_appid";   // EXTRA_OUT_FIELDS 微信交易预下单时sub_appid的值
    public static final String CONSUME_ORDER_SN = "consume_order_sn";           // 预授权完成订单号 VARCHAR(20)
    public static final String DEPOSIT_CONSUME_EFFECTIVE_AMOUNT = "deposit_consume_effective_amount";                           // 预授权交易消费金额
    public static final String DEPOSIT_REFUND_EFFECTIVE_AMOUNT = "deposit_refund_effective_amount";                             // 预授权交易退款金额
    public static final String DEPOSIT_FREEZE_FEE = "deposit_freeze_fee";                             // 预授权冻结手续费

    public static final String DEPOSIT_CONSUME_REFUND_INFO = "deposit_consume_refund_info";                             // 预授权完成-退款信息

    public static final String DEPOSIT_PAY_TYPE = "deposit_pay_type";                             // 预授权交易流水类型
    public static final String ADVANCE_AMOUNT = "advance_amount";       //支付宝垫资保付金额
    public static final String VOUCHER_DETAILS = "voucher_details";     // EXTRA_OUT_FIELDS 所有优惠券信息
    public static final String GOODS_DETAILS = "goods_details";         // EXTRA_OUT_FIELDS 单品优惠信息
    public static final String SUB_IS_SUBSCRIBE = "sub_is_subscribe";   // EXTRA_OUT_FIELDS 用户是否关注子公众账号
    public static final String IS_DEPOSIT_SYNC = "is_deposit_sync";     // EXTRA_OUT_FIELDS 订单是否为交易同步订单
    public static final String ENTRUST_PAY = "entrust_pay";             // EXTRA_OUT_FIELDS 订单是否为免密支付
    public static final String CANCEL_TO_REFUND = "cancel_to_refund";   // EXTRA_OUT_FIELDS 订单是否为撤单转退款
    public static final String IS_FIX = "is_fix";                       // EXTRA_OUT_FIELDS 是否为勾兑成功的
    public static final String CREDIT_BIZ_ORDER_ID = "credit_biz_order_id";     // EXTRA_OUT_FIELDS 芝麻先享 信用服务订单号
    public static final String CREDIT_AGREEMENT_ID = "credit_agreement_id";     // EXTRA_OUT_FIELDS 芝麻先享 芝麻开通协议号
    public static final String ZM_SERVICE_ID = "zm_service_id";                 // EXTRA_OUT_FIELDS 芝麻先享 芝麻信用服务ID
    public static final String TOTAL_AMOUNT = "total_amount";                   // EXTRA_OUT_FIELDS 芝麻先享 信用服务订单金额
    public static final String BATCH_BILL_NO = "batchbillno"; //批次号
    public static final String SYS_TRACE_NO = "systraceno";  //凭证号
    public static final String LOG_NO = "log_no"; //手机pos外卡子单log_no
    public static final String AUTH_NO = "authno"; //授权号
    public static final String REFER_NUMBER = "refernumber";  //系统参考号
    public static final String CONSUME_TIME = "consume_time";  //预授权完成时间
    public static final String WILD_CARD_TYPE = "wild_card_type";       // EXTRA_OUT_FIELDS 币种转换方式，EDC或DCC
    public static final String WILD_CARD_TYPE_EDC ="edc"; // EXTRA_OUT_FIELDS.wild_card_type 外卡类型：edc
    public static final String WILD_CARD_TYPE_DCC ="dcc";// EXTRA_OUT_FIELDS.wild_card_type 外卡类型：dcc

    public static final String PAY_SETTLE_DATE = "pay_settle_date"; //EXTRA_OUT_FIELDS 富友刷卡 结算日期

    public static final String WX_INSTALLMENT_INFO = "wx_installment_info"; //EXTRA_OUT_FIELDS 微信分付 微信分付详情
    public static final String WX_INSTALLMENT_INFO_SUBSIDY = "use_subsidy"; //EXTRA_OUT_FIELDS 微信分付 微信分付详情
    public static final String WX_INSTALLMENT_INFO_NUM = "selected_installment_number"; //EXTRA_OUT_FIELDS 微信分付 微信分付详情


    public static final String PAYER_AMOUNT = "payer_amount";  //付款人支付币种金额
    public static final String EXCHANGE_RATE = "exchange_rate";                             // 境外支付，支付通道支付接口返回的交易时人民币对交易货币的汇率
    public static final String IS_OFFSET = "is_offset";
    public static final String IS_DEFAULT_POI = "is_default_poi";                       // EXTRA_OUT_FIELDS 是否上送的是兜底的商户或者门店的poi
    public static final String IS_FEE_UPDATE_FLAG = "is_fee_update_flag";                   //手续费是否更新
    public static final String REAL_TRADE_FEE = "real_trade_fee";             // EXTRA_OUT_FIELDS 支付通道实退手续费
    public static final String SUBSCRIPTION_NO = "subscription_no"; // 支付宝先享后付订单订购号
    public static final String PAYER_INFO = "payer_info";  //微企付通道 付款人信息
    @Deprecated
    public static final String LAKALA_WANMA_CALLBACK = "lakala_wanma_callback";        // EXTRA_OUT_FIELDS 万码回调交易参数
    public static final String ORDER_INFO = "order_info";               // EXTRA_OUT_FIELDS 用于保持部分订单信息
    public static final String OVERSEAS = "overseas";        			// EXTRA_OUT_FIELDS 境外支付参数
    public static final String USER_SITE = "user_site";                 // EXTRA_OUT_FIELDS 境外支付参数            
    public static final String NUCC_BESTPAY = "nucc_best_pay"; //EXTRA_OUT_FIELDS 网联翼支付参数
    public static final String IS_MCH_CHANNEL_COUPON_SUBSIDY = "is_mch_channel_coupon_subsidy"; //EXTRA_OUT_FIELDS 商户支付通道免充值优惠 是否补贴  true|false
    public static final String SETTLEMENT_AMOUNT = "settlement_amount"; //EXTRA_OUT_FIELDS 结算金额
    public static final String UNION_PAY_CHANNEL_TYPE = "unionpay_channel_type";  //EXTRA_OUT_FIELDS 银联网银支付 渠道类型
    public static final String IS_FIRST_MORE_THAN_CREDIT_DAY_LIMIT = "is_first_more_than_credit_day_limit"; //EXTRA_OUT_FIELDS 是否是首次超过日信用交易 true|false
    public static final String IS_FIRST_MORE_THAN_CREDIT_MONTH_LIMIT = "is_first_more_than_credit_month_limit"; //EXTRA_OUT_FIELDS 是否是首次超过月信用交易 true|false

    public static final String IS_FIRST_MORE_THAN_ALIPAY_CREDIT_DAY_LIMIT = "is_first_more_than_alipay_credit_day_limit"; //EXTRA_OUT_FIELDS 支付宝是否是首次超过日信用交易 true|false
    public static final String IS_FIRST_MORE_THAN_WECHAT_CREDIT_DAY_LIMIT = "is_first_more_than_wechat_credit_day_limit"; //EXTRA_OUT_FIELDS 微信是否是首次超过日信用交易 true|false
    public static final String IS_FIRST_MORE_THAN_ALIPAY_CREDIT_MONTH_LIMIT = "is_first_more_than_alipay_credit_month_limit"; //EXTRA_OUT_FIELDS 支付宝是否是首次超过月信用交易 true|false
    public static final String IS_FIRST_MORE_THAN_WECHAT_CREDIT_MONTH_LIMIT = "is_first_more_than_wechat_credit_month_limit"; //EXTRA_OUT_FIELDS 微信是否是首次超过月信用交易 true|false


    public static final String EXTENDED_PARAMS = "extended_params";     // 透传到支付通道的参数(goods_details)，由商户和支付通道约定，我们不做解析 BLOB
    
    public static final String REFLECT = "reflect";             // 商户上传的附加字段，保存在订单中。终端查询的时候原样返回。

    public static final String CONFIG_SNAPSHOT = "config_snapshot";     // 配置参数快照 BLOB

    public static final String STORE_SCENE_SWITCH = "store_scene_switch"; // 门店场景开关


    public static final String NFC_CARD = "nfc_card";// nfc支付的时候的银行卡
    public static final String NFC_CARDSN = "nfc_cardsn";// nfc支付的时候的银行卡
    public static final String NFC_NFC = "nfc_nfc";// nfc支付的时候的银行卡
    public static final String NFC_TRACK2 = "nfc_track2";// nfc支付的时候的银行卡
    public static final String NFC_PIN = "nfc_pin";// nfc支付的时候的银行卡
    
    public static final String PROVIDER_RESPONSE = "provider_response"; // 第三方支付通道的原始返回内容
    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";        // 交易完成时间（从支付通道得到）BIGINT
    public static final String FINISH_TIME = "finish_time";                         // 交易完成时间（收钱吧系统时间）BIGINT
    
    public static final String BIZ_ERROR_CODE = "biz_error_code";                   // 业务错误码，参考com.wosai.upay.workflow.UpayBizError
    public static final String PROVIDER_ERROR_INFO = "provider_error_info";         // 支付通道返回的错误信息 BLOB
    public static final String CURRENCY = "currency";                               // 货币类型, 退款币种与支付币种必须一致    

    public static final String IS_HISTORY_TRADE_REFUND = "is_history_trade_refund";   // 历史退款交易
    public static final String USE_APPROXIMATE_FEE = "use_approximate_fee";         // 使用按比例计算的费率
    public static final String REFUND_NON_SQB_ORDER = "refund_non_sqb_order";       // 非收钱吧交易退款
    public static final String IS_HISTORY_DEPOSIT_CANCEL = "is_history_deposit_cancel";   // 历史预授权撤销
    public static final String IS_HISTORY_DEPOSIT_CONSUME = "is_history_deposit_consume";   // 历史预授权完成
    public static final String IN_HBASE = "in_hbase";                                   // 数据存储在hbase中
    public static final String COMBO_ID = "combo_id";                                   // 费率套餐id
    public static final String QUOTA_FEE_RATE = "quota_fee_rate";                       // 优惠额度费率（减免费率）
    public static final String QUOTA_FEE_RATE_TAG = "quota_fee_rate_tag";               // 优惠额度标示
    public static final String QUOTA_FEE = "quota_fee";                                 // 额度包优惠手续费
    public static final String PROVIDER_FEE = "provider_fee";                           // 通道侧收取的手续费
    public static final String ASYNC_WALLET_LOG = "async_wallet_log";                           //　是否异步写余额log


    public static final String ENCRYPT_DATA = "encrypt_data";                              //加密数据
    public static final String PAY_TOKEN = "pay_token";                                     //支付token
    public static final String WELFARE_ACCOUNT_ID = "account_id"; //福利卡核销的账号

    public static final String DISCOUNT_TRANSFER_DETAIL = "discount_transfer_detail"; //优惠转账明细

    public static final String KEY_IS_HISTORY_TRADE_REFUND = EXTRA_OUT_FIELDS + "." + IS_HISTORY_TRADE_REFUND;   // 历史退款交易
    public static final String KEY_IS_IS_MCH_CHANNEL_COUPON_SUBSIDY = EXTRA_OUT_FIELDS + "." + IS_MCH_CHANNEL_COUPON_SUBSIDY;   // 商户支付通道免充值优惠 是否补贴
    public static final String KEY_SETTLEMENT_AMOUNT = EXTRA_OUT_FIELDS + "." + SETTLEMENT_AMOUNT;              // 支付通道返回的结算金额信息
    public static final String KEY_IS_HISTORY_DEPOSIT_CANCEL = EXTRA_OUT_FIELDS + "." + IS_HISTORY_DEPOSIT_CANCEL;   // 历史预授权撤销
    public static final String KEY_IS_HISTORY_DEPOSIT_CONSUME = EXTRA_OUT_FIELDS + "." + IS_HISTORY_DEPOSIT_CONSUME;   // 历史预授权完成
    public static final String KEY_IN_HBASE = EXTRA_OUT_FIELDS + "." + IN_HBASE;                                // 备份库数据
    public static final String KEY_COMBO_ID = EXTRA_OUT_FIELDS + "." + COMBO_ID;                                // 费率套餐id
    public static final String KEY_SQB_SCENE = EXTRA_PARAMS + "." + SQB_SCENE;                                // 场景
    public static final String KEY_PROVIDER_FEE = EXTRA_OUT_FIELDS + "." + PROVIDER_FEE;                        // 通道收取的手续费
    public static final String KEY_QUOTA_FEE_RATE = EXTRA_OUT_FIELDS + "." + QUOTA_FEE_RATE;                    // 优惠额度费率（减免费率）
    public static final String KEY_QUOTA_FEE_RATE_TAG = EXTRA_OUT_FIELDS + "." + QUOTA_FEE_RATE_TAG;            // 优惠额度标示
    public static final String KEY_QUOTA_FEE = EXTRA_OUT_FIELDS + "." + QUOTA_FEE;                              // 额度包优惠手续费
    public static final String KEY_ASYNC_WALLET_LOG = EXTRA_OUT_FIELDS + "." + ASYNC_WALLET_LOG;            // 是否异步写余额log
    public static final String KEY_BATCH_BILL_NO = EXTRA_OUT_FIELDS + "." + BATCH_BILL_NO;
    public static final String KEY_SYS_TRACE_NO = EXTRA_OUT_FIELDS + "." + SYS_TRACE_NO;
    public static final String KEY_EXTRA_OUT_FIELDS_WEIXIN_SUB_APPID = EXTRA_OUT_FIELDS + "." + WEIXIN_SUB_APPID;
    public static final String KEY_IS_FIX = EXTRA_OUT_FIELDS + "." + IS_FIX;
    public static final String KEY_IS_DEFAULT_POI = EXTRA_OUT_FIELDS + "." + IS_DEFAULT_POI;
    public static final String KEY_SQB_ACTIVITY_BIZ_EXT = EXTRA_PARAMS + "." + SQB_ACTIVITY_BIZ_EXT;
    public static final String KEY_IS_MULTIPLE_PAY = EXTRA_PARAMS + "." + IS_MULTIPLE_PAY;
    public static final String KEY_REAL_TRADE_FEE = EXTRA_OUT_FIELDS + "." + REAL_TRADE_FEE;

    @Deprecated
    public static final String NEED_RECONCILIATION = "need_reconciliation";         // 需要进行勾兑
    public static final String QUERY_EXPIRE = "query_expire";         // 由查单轮询结束导致的流水异常
    public static final String IS_DEPOSIT = "is_deposit";                           // 是否预授权交易
    public static final String DEPOSIT_TYPE = "deposit_type";                           // 预授权类型
    public static final String WALLET_ACCOUNT_TYPE = "wallet_account_type";                           // 余额账户类型
    public static final String DEPOSIT_CALLER = "deposit_caller";                           // 预授权调用者
    public static final String DEPOSIT_CALLER_CANCEL = "cancel";                           // 预授权调用者 - 预授权撤销
    public static final String DEPOSIT_CALLER_CONSUME_REFUND = "consume_refund";                           // 预授权调用者 - 预授权完成后退款

    //预授权备注详情 存于reflect字段
    public static final String REFLECT_TYPE = "type";                           // type
    public static final String REFLECT_DEPOSIT_REMARK = "remark";
    public static final String REFLECT_DEPOSIT_CELLPHONE = "cellphone";

    public static final String NEED_ONLY_REFUND_QUERY = "need_only_refund_query";   //只需要退款查询

    public static final String KEY_IS_DEPOSIT = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.IS_DEPOSIT);
    public static final String KEY_DEPOSIT_TYPE = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.DEPOSIT_TYPE);   // 预授权类型
    public static final String KEY_WALLET_ACCOUNT_TYPE = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.WALLET_ACCOUNT_TYPE);   // 银行卡刷卡类型
    public static final String KEY_DEPOSIT_CONSUME_REFUND_INFO = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.DEPOSIT_CONSUME_REFUND_INFO);   // 预授权完成(部分完成) - 退款信息
    public static final String KEY_DEPOSIT_FREEZE_FEE = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.DEPOSIT_FREEZE_FEE);   // 预授权冻结手续费
    public static final String KEY_DEPOSIT_CONSUME_EFFECTIVE_AMOUNT = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.DEPOSIT_CONSUME_EFFECTIVE_AMOUNT);
    public static final String KEY_DEPOSIT_REFUND_EFFECTIVE_AMOUNT = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.DEPOSIT_REFUND_EFFECTIVE_AMOUNT);

    public static final String KEY_DEPOSIT_PAY_TYPE = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.DEPOSIT_PAY_TYPE);
    public static final String KEY_UNION_PAY_CHANNEL_TYPE= String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.UNION_PAY_CHANNEL_TYPE);
    public static final String KEY_STORE_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.STORE_SN);
    public static final String KEY_TERMINAL_VENDOR_APP_APPID = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_VENDOR_APP_APPID);
    public static final String KEY_TERMINAL_SN = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TERMINAL_SN);
    public static final String KEY_CLEARANCE_PROVIDER = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.CLEARANCE_PROVIDER);
    public static final String KEY_PROFIT_SHARING= String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.PROFIT_SHARING);
    public static final String KEY_BESTPAY_TRADE_SIGNTYPE= String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.BESTPAY_TRADE_PARAMS, TransactionParam.SIGN_TYPE);  //翼支付的签名方式
    public static final String KEY_SQB_PRODUCT_FLAG= String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_PRODUCT_FLAG);
    public static final String KEY_SQB_VOICE = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_VOICE);
    public static final String KEY_TRADE_APP = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.TRADE_APP);
    public static final String KEY_MERCHANT_NAME = String.format("%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.MERCHANT_NAME);
    public static final String KEY_SQB_USER_ID = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_USER_ID);
    public static final String KEY_SQB_PAYER_AUTH = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_PAYER_AUTH);
    public static final String KEY_SQB_PRODUCT_CODE = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_PRODUCT_CODE);
    public static final String KEY_SQB_BIZ_ORDER_SN = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_BIZ_ORDER_SN);
    public static final String KEY_IS_FEE_UPDATE_FLAG = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.IS_FEE_UPDATE_FLAG);
    public static final String KEY_SQB_PROMOTION_DETAIL = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_PROMOTION_DETAIL);
    public static final String KEY_SQB_WX_VERSION = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_WX_VERSION);

    //收钱吧透传参数列表
    public static final List<String> SQB_EXTENDED_KEYS = Arrays.asList(SQB_HB_FQ_BUYER_SERVICE_CHARGE, SQB_HB_FQ_SELLER_SERVICE_CHARGE, SQB_CATERING, SQB_CSB_TO_WAP_SN, SQB_SCENE, SQB_WALLET_NAME, SQB_GEN_SN
            //暂时不开放 SQB_PRODUCT_FLAG 透传，后续如果有透传场景，再放开
            //,SQB_PRODUCT_FLAG
            ,SQB_REFUND_FLAG, SQB_IP, SQB_MAC_ID, SQB_STATION, SQB_PAY_SOURCE, SQB_AD_CODE
            , SQB_ALIPAY_TIMEOUT, SQB_VOICE,SQB_CLIENT_SEQ_NO,SQB_CLIENT_BATCH_NO, SQB_INNER_BIZ
            , SQB_FQ_SELLER_SERVICE_CHARGE, SQB_FQ_BUYER_SERVICE_CHARGE, SQB_FQ_SERVICE_TYPE, SQB_LIMIT_PAYER_ADULT
            , SQB_USER_ID, SQB_ACTIVITY_BIZ_EXT, SQB_PAYER_AUTH, SQB_PRODUCT_CODE, SQB_ZFB_SHOWCASE_FLAG
            , SQB_COMBINED_PAYMENT_SN
            , SQB_PAY_PATH, SQB_BIZ_MODEL, SQB_ACTIVITY_DISCOUNT, SQB_CANCEL_LIMIT, SQB_CANCEL_REASON, SQB_CANCEL_NOTIFY_URL
            , SQB_BIZ_ORDER_SN, SQB_PROMOTION_DETAIL, SQB_TRACE_ID, SQB_WX_VERSION
    );

    //收钱吧透传参数列表
    public static final List<String> SQB_REFUND_EXTENDED_KEYS = Arrays.asList(SQB_INNER_BIZ, SQB_PROMOTION_DETAIL);

    /**
     * 所有流水需要拷贝的参数定义, 比如退款、预授权，撤单等流水需要拷贝原始流水的参数
     */
    public static final List<String> COPY_EXTRA_PARAMS_KEYS = Arrays.asList(SQB_BIZ_ORDER_SN, SQB_WX_VERSION);

    public static final String KEY_REFUND_FLAG = String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_REFUND_FLAG);

    public static final Set<Integer> TYPE_DEPOSIT_SET = new HashSet<Integer>() {{
        add(TYPE_DEPOSIT_FREEZE);
        add(TYPE_DEPOSIT_CANCEL);
        add(TYPE_DEPOSIT_CONSUME);
    }};

    @Deprecated
    public static enum ProductFlag {
        APP(PRODUCT_APP),
        SDK(PRODUCT_SDK),
        POS(PRODUCT_POS);
        int code;

        ProductFlag(int code) {
            this.code = code;
        }
        
        public static ProductFlag fromCode(int code) {
            for (ProductFlag productFlag: values()) {
                if (productFlag.code == code) {
                    return productFlag;
                }
            }
            return APP;
        }
    }
    
    public static boolean notFailed(int status) {
        return status == STATUS_CREATED || status == STATUS_SUCCESS || status == STATUS_IN_PROG || status == STATUS_PRE_SUCCESS;
    }
}
