package com.wosai.upay.model.dao;

public class EventLog {
    @Deprecated
    //使用具体的10 11 30 31， 这样不用解析payload就能明确知道是正向的还是负向的处理
    public static final int TYPE_TRANSACTION = 1; //交易

    public static final int TYPE_TRANSACTION_PAYMENT = 30;
    public static final int TYPE_TRANSACTION_REFUND_REVOKE = 31;
    public static final int TYPE_TRANSACTION_REFUND = 11;
    public static final int TYPE_TRANSACTION_CANCEL = 10;

    public static final int TYPE_DRAW = 3; //提现
    public static final int TYPE_DRAW_BACK = 4; //提现驳回
    public static final int TYPE_TRANSFER_IN  = 5; //转入
    public static final int TYPE_TRANSFER_OUT = 6; //转出
    public static final String PAYLOAD_TOTAL_AMOUNT = "total_amount";
    public static final String PAYLOAD_AMOUNT = "amount";
    public static final String PAYLOAD_LEGACY_AMOUNT = "legacy_amount";
    public static final String PAYLOAD_STORE_ID = "store_id"; //门店id
    public static final String PAYLOAD_MERCHANT_ID = "merchant_id";//商户id
    public static final String PAYLOAD_IS_MCH_CHANNEL_COUPON_SUBSIDY = "is_mch_channel_coupon_subsidy"; //商户支付通道免充值优惠 是否补贴  true|false


    public static final String MERCHANT_ID = "merchant_id";
    public static final String TYPE = "type";
    public static final String PAYLOAD = "payload";
    public static final String PROCESSED = "processed";
    public static final String PARTITION = "partition";
    public static final String ACTION_ID = "action_id";


}
