package com.wosai.upay.model;


import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;


/**
 * <AUTHOR> Date: 2022/3/25 Time: 10:12 上午
 */
public class TerminalInfo {
    private static final int HX_POI_DECIMAL_PART_LEN = 6;
    private boolean isOffset = false;
    private boolean isDefaultPoi = false;

    private static final String DECIMAL_POINT = ".";
    private static final String ZERO = "0";

    private static final String STANDARD_LONGITUDE_INTEGER_PART_FORMATTER = "%03d";
    private static final int STANDARD_LONGITUDE_DECIMAL_PART_LENGTH = 5;
    private static final int STANDARD_LONGITUDE_STANDARD_LENGTH = 10;

    private static final String STANDARD_LATITUDE_INTEGER_PART_FORMATTER = "%02d";
    private static final int STANDARD_LATITUDE_DECIMAL_PART_LENGTH = 6;
    private static final int STANDARD_LATITUDE_STANDARD_LENGTH = 10;

    private static final String UNIONPAY_LONGITUDE_INTEGER_PART_FORMATTER = "%03d";
    private static final int UNIONPAY_LONGITUDE_DECIMAL_PART_LENGTH = 5;
    private static final int UNIONPAY_LONGITUDE_STANDARD_LENGTH = 10;

    private static final String UNIONPAY_LATITUDE_INTEGER_PART_FORMATTER = "%02d";
    private static final int UNIONPAY_LATITUDE_DECIMAL_PART_LENGTH = 6;
    private static final int UNIONPAY_LATITUDE_STANDARD_LENGTH = 10;

    private static final String SYB_LONGITUDE_INTEGER_PART_FORMATTER = "%03d";
    private static final int SYB_LONGITUDE_DECIMAL_PART_LENGTH = 5;
    private static final int SYB_LONGITUDE_STANDARD_LENGTH = 10;

    private static final String SYB_LATITUDE_INTEGER_PART_FORMATTER = "%02d";
    private static final int SYB_LATITUDE_DECIMAL_PART_LENGTH = 6;
    private static final int SYB_LATITUDE_STANDARD_LENGTH = 10;

    private static final String CHINAUMS_LONGITUDE_INTEGER_PART_FORMATTER = "%03d";
    private static final int CHINAUMS_LONGITUDE_DECIMAL_PART_LENGTH = 5;
    private static final int CHINAUMS_LONGITUDE_STANDARD_LENGTH = 10;

    private static final String CHINAUMS_LATITUDE_INTEGER_PART_FORMATTER = "%02d";
    private static final int CHINAUMS_LATITUDE_DECIMAL_PART_LENGTH = 6;
    private static final int CHINAUMS_LATITUDE_STANDARD_LENGTH = 10;



    private String id;
    private String type;
    private String serialNum;

    private String longitude;
    private String latitude;
    private String ip;

    private String formatLongitude;
    private String formatLatitude;

    private String standardFormatLongitude;
    private String standardFormatLatitude;

    private String unionpayFormatLongitude;
    private String unionpayFormatLatitude;

    private String chinaumsFormatLongitude;
    private String chinaumsFormatLatitude;


    public TerminalInfo(String id) {
        this.id = id;
    }

    public TerminalInfo(String id, Map<String,Object> termInfo) {
        if(termInfo != null){
            this.id = MapUtil.getString(termInfo, TransactionParam.TRADE_EXT_TERM_INFO_TERM_ID);
            this.type = MapUtil.getString(termInfo, TransactionParam.TRADE_EXT_TERM_INFO_TERM_TYPE);
            this.serialNum = MapUtil.getString(termInfo, TransactionParam.TRADE_EXT_TERM_INFO_SERIAL_NUM);
        }else{
            this.id = id;
        }
    }

    public boolean isSendIp() {
        return StringUtils.isNotEmpty(ip);
    }

    public boolean isSendPoi() {
        return StringUtils.isNotEmpty(getFormatLongitude())
                && StringUtils.isNotEmpty(getFormatLatitude());
    }

    public boolean isSendStandardPoi() {
        return StringUtils.isNotEmpty(getStandardFormatLatitude())
                && StringUtils.isNotEmpty(getStandardFormatLongitude());
    }

    public String getFormatLongitude() {
        if (StringUtils.isNotEmpty(formatLongitude)) {
            return formatLongitude;
        }
        formatLongitude = processPoi(longitude);
        return formatLongitude;
    }

    public String getFormatLatitude() {
        if (StringUtils.isNotEmpty(formatLatitude)) {
            return formatLatitude;
        }
        formatLatitude = processPoi(latitude);
        return formatLatitude;
    }

    public boolean isSendUnionpayPoi() {
        return StringUtils.isNotEmpty(getUnionpayFormatLongitude())
                && StringUtils.isNotEmpty(getUnionpayFormatLatitude());
    }

    public String getStandardFormatLongitude() {
        if (StringUtils.isNotEmpty(standardFormatLongitude)) {
            return standardFormatLongitude;
        }
        return standardFormatLongitude = processPoi(longitude, STANDARD_LONGITUDE_INTEGER_PART_FORMATTER, STANDARD_LONGITUDE_DECIMAL_PART_LENGTH, STANDARD_LONGITUDE_STANDARD_LENGTH);
    }

    public String getStandardFormatLatitude() {
        if (StringUtils.isNotEmpty(standardFormatLatitude)) {
            return standardFormatLatitude;
        }
        return standardFormatLatitude = processPoi(latitude, STANDARD_LATITUDE_INTEGER_PART_FORMATTER, STANDARD_LATITUDE_DECIMAL_PART_LENGTH, STANDARD_LATITUDE_STANDARD_LENGTH);
    }

    public String getUnionpayFormatLongitude() {
        if (StringUtils.isNotEmpty(unionpayFormatLongitude)) {
            return unionpayFormatLongitude;
        }
        return unionpayFormatLongitude = processPoi(longitude, UNIONPAY_LONGITUDE_INTEGER_PART_FORMATTER, UNIONPAY_LONGITUDE_DECIMAL_PART_LENGTH, UNIONPAY_LONGITUDE_STANDARD_LENGTH);
    }

    public String getUnionpayFormatLatitude() {
        if (StringUtils.isNotEmpty(unionpayFormatLatitude)) {
            return unionpayFormatLatitude;
        }
        return unionpayFormatLatitude = processPoi(latitude, UNIONPAY_LATITUDE_INTEGER_PART_FORMATTER, UNIONPAY_LATITUDE_DECIMAL_PART_LENGTH, UNIONPAY_LATITUDE_STANDARD_LENGTH);
    }

    public String getSybFormatLongitude() {
        if (StringUtils.isNotEmpty(unionpayFormatLongitude)) {
            return unionpayFormatLongitude;
        }
        return unionpayFormatLongitude = processPoi(longitude, SYB_LONGITUDE_INTEGER_PART_FORMATTER, SYB_LONGITUDE_DECIMAL_PART_LENGTH, SYB_LONGITUDE_STANDARD_LENGTH);
    }

    public String getSybFormatLatitude() {
        if (StringUtils.isNotEmpty(unionpayFormatLatitude)) {
            return unionpayFormatLatitude;
        }
        return unionpayFormatLatitude = processPoi(latitude, SYB_LATITUDE_INTEGER_PART_FORMATTER, SYB_LATITUDE_DECIMAL_PART_LENGTH, SYB_LATITUDE_STANDARD_LENGTH);
    }

    public String getChinaumsFormatLongitude() {
        if (StringUtils.isNotEmpty(chinaumsFormatLongitude)) {
            return chinaumsFormatLongitude;
        }
        return chinaumsFormatLongitude = processPoi(longitude, CHINAUMS_LONGITUDE_INTEGER_PART_FORMATTER, CHINAUMS_LONGITUDE_DECIMAL_PART_LENGTH, CHINAUMS_LONGITUDE_STANDARD_LENGTH);
    }

    public String getChinaumsFormatLatitude() {
        if (StringUtils.isNotEmpty(chinaumsFormatLatitude)) {
            return chinaumsFormatLatitude;
        }
        return chinaumsFormatLatitude = processPoi(latitude, CHINAUMS_LATITUDE_INTEGER_PART_FORMATTER, CHINAUMS_LATITUDE_DECIMAL_PART_LENGTH, CHINAUMS_LATITUDE_STANDARD_LENGTH);
    }

    private String processPoi(String poi) {
        String processedPoi = null;
        if (StringUtils.isNotEmpty(poi)) {
            String[] partArr = poi.split("\\.");
            if (ArrayUtils.isNotEmpty(partArr) && partArr.length == 2) {
                String partStr = partArr[1];
                int len = partStr.length();
                if (len == HX_POI_DECIMAL_PART_LEN) {
                    processedPoi = partArr[0] + DECIMAL_POINT + partStr;
                }
                if (len > HX_POI_DECIMAL_PART_LEN) {
                    processedPoi = partArr[0] + DECIMAL_POINT + partStr.substring(0, HX_POI_DECIMAL_PART_LEN);
                }
                if (len < HX_POI_DECIMAL_PART_LEN) {
                    processedPoi = partArr[0] + DECIMAL_POINT + partStr + genBunchZero(HX_POI_DECIMAL_PART_LEN - len);
                }
            }
        }
        return processedPoi;
    }

    private String processPoi(String poi, String integerPartFormatter
            , int decimalPartStandardLength, int fullStandardLength) {
        StringBuilder temp = new StringBuilder();
        if (StringUtils.isNotEmpty(poi)) {
            String[] partArr = poi.split("\\.");
            if (ArrayUtils.isNotEmpty(partArr) && partArr.length == 2) {
                String firstPart = partArr[0];
                if (firstPart.length() >= 2) {
                    //符号部分
                    String symbolPart = firstPart.substring(0, 1);
                    //整数部分
                    String integerPart = firstPart.substring(1);
                    if (StringUtils.isNotEmpty(symbolPart) && StringUtils.isNotEmpty(integerPart)) {
                        temp.append(symbolPart)
                                .append(String.format(integerPartFormatter
                                        , Integer.parseInt(integerPart)));
                    }
                    temp.append(DECIMAL_POINT);
                    //小数部分
                    String decimalPart = partArr[1];
                    int decimalPartLength = decimalPart.length();
                    if (decimalPartLength >= decimalPartStandardLength) {
                        temp.append(decimalPart, 0, decimalPartStandardLength);
                    } else {
                        temp.append(decimalPart)
                                .append(genBunchZero(decimalPartStandardLength - decimalPartLength));
                    }
                }
            }
        }
        if (temp.length() == fullStandardLength) {
            return temp.toString();
        }
        return null;
    }

    private String genBunchZero(int num) {
        StringBuilder temp = new StringBuilder(num);
        for (int i = 0; i < num; i++) {
            temp.append(ZERO);
        }
        return temp.toString();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public String getOrDefaultType(String defaultType) {
        return type == null ? defaultType : type;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public boolean isOffset() {
        return isOffset;
    }

    public void setOffset(boolean offset) {
        isOffset = offset;
    }

    public boolean isDefaultPoi() {
        return isDefaultPoi;
    }

    public void setDefaultPoi(boolean isDefaultPoi) {
        this.isDefaultPoi = isDefaultPoi;
    }

    @Override
    public String toString() {
        return "TerminalInfo{" +
                "id='" + id + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", ip='" + ip + '\'' +
                ", formatLongitude='" + formatLongitude + '\'' +
                ", formatLatitude='" + formatLatitude + '\'' +
                '}';
    }
}
