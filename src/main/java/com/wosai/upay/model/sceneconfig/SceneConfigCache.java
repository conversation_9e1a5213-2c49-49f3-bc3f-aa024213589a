package com.wosai.upay.model.sceneconfig;

import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SceneConfigCache {

    @Getter
    private Map<Long, UpayGatewayRecord> sourceIdAndUpayGatewayRecord = new HashMap<>();

    @Getter
    private Map<String, List<SourceInfo>> errorCodeAndSourceInfos = new HashMap<String, List<SourceInfo>>();

    @Getter
    private Map<Long, SourceInfo> sourceIdAndSourceInfo = new HashMap<>();

    @Getter
    private Map<String, UpayGatewayRecord> sceneErrorCodeAndUpayGatewayRecord = new HashMap<>();

    @Getter
    private static Map<String, Map<String, String>> processedResult = new ConcurrentHashMap<String, Map<String, String>>(); // matched joinKey -> info with all fields

    public void buildSourceIdAndUpayGatewayRecord(Long sourceId, UpayGatewayRecord upayGatewayRecord) {
        sourceIdAndUpayGatewayRecord.put(sourceId, upayGatewayRecord);
    }

    public void buildSourceIdAndSourceInfo(Long sourceId, SourceInfo singleSourceInfo) {
        sourceIdAndSourceInfo.put(sourceId, singleSourceInfo);
    }

    public void buildErrorCodeAndSourceInfos(String errorCode, List<SourceInfo> sourceInfoList) {
        errorCodeAndSourceInfos.put(errorCode, sourceInfoList);
    }

    public void resetProcessedResult(){
        processedResult = new ConcurrentHashMap<String, Map<String, String>>();
    }

}
