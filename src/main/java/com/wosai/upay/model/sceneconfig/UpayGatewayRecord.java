package com.wosai.upay.model.sceneconfig;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpayGatewayRecord {
    private Long id;

    @JsonProperty("scene_error_code")
    private String sceneErrorCode;

    @JsonProperty("to_b_tips_zh")
    private String toBTipsZh;

    @JsonProperty("to_b_tips_en")
    private String toBTipsEn;
    private String code;

    @JsonProperty("source_info")
    private List<SourceInfo> sourceInfo;
}
