package com.wosai.upay.model.sceneconfig;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceInfo {
    @JsonProperty("scene_error_code")
    private String sceneErrorCode;

    private Integer payway;
    private String model;

    @JsonProperty("source_info_id")
    private Long sourceInfoId;

    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("error_msg_keyword_list")
    private List<String> errorMsgKeywordList;

    private int keywordCount;
}
