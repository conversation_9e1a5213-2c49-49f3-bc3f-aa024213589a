package com.wosai.upay.model;

import com.wosai.data.util.CollectionUtil;

import java.util.Set;

/**
 * Created by jianfree on 1/3/17.
 * 支付方式
 */
public class Payment {

    public static final String TYPE_HONGBAO_WOSAI = "HONGBAO_WOSAI"; //喔噻红包
    public static final String TYPE_HONGBAO_WOSAI_MCH = "HONGBAO_WOSAI_MCH";//喔噻商户红包 免充值
    public static final String TYPE_DISCOUNT_WOSAI = "DISCOUNT_WOSAI";//喔噻立减
    public static final String TYPE_DISCOUNT_WOSAI_MCH = "DISCOUNT_WOSAI_MCH";//喔噻商户立减 免充值


    public static final String TYPE_DISCOUNT_CHANNEL = "DISCOUNT_CHANNEL";//支付通道 折扣(立减优惠)
    public static final String TYPE_DISCOUNT_CHANNEL_MCH = "DISCOUNT_CHANNEL_MCH";//折扣(立减优惠) 支付通道商户 免充值
    public static final String TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP = "DISCOUNT_CHANNEL_MCH_TOP_UP";//折扣(立减优惠) 支付通道商户 充值
    public static final String TYPE_HONGBAO_CHANNEL = "HONGBAO_CHANNEL"; //支付通道红包
    public static final String TYPE_HONGBAO_CHANNEL_MCH = "HONGBAO_CHANNEL_MCH"; //支付通道商户红包 免充值
    public static final String TYPE_HONGBAO_CHANNEL_MCH_TOP_UP = "HONGBAO_CHANNEL_MCH_TOP_UP"; //支付通道商户红包 充值

    public static final Set<String> TYPE_DISCOUNT_SET = CollectionUtil.hashSet(TYPE_DISCOUNT_CHANNEL
            , TYPE_DISCOUNT_CHANNEL_MCH, TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP, TYPE_HONGBAO_CHANNEL
            , TYPE_HONGBAO_CHANNEL_MCH, TYPE_HONGBAO_CHANNEL_MCH_TOP_UP);

    public static final Set<String> TYPE_WOSAI_DISCOUNT_SET = CollectionUtil.hashSet(TYPE_HONGBAO_WOSAI
            , TYPE_HONGBAO_WOSAI_MCH, TYPE_DISCOUNT_WOSAI, TYPE_DISCOUNT_WOSAI_MCH);


    public static final String TYPE_CARD_CHANNEL_MCH_PRE = "CARD_PRE"; //支付通道商户预付卡
    public static final String TYPE_CARD_CHANNEL_MCH_BALANCE = "CARD_BALANCE"; //支付通道商户储值卡

    public static final String TYPE_BANKCARD = "BANKCARD"; // 银行卡
    public static final String TYPE_BANKACCOUNT = "BANKACCOUNT"; // 银行转账
    public static final String TYPE_BANKCARD_CREDIT = "BANKCARD_CREDIT"; //信用卡 银行卡
    public static final String TYPE_BANKCARD_DEBIT = "BANKCARD_DEBIT"; //储蓄卡 银行卡
    public static final String TYPE_BANKCARD_SEMI_CREDIT = "BANKCARD_SEMI_CREDIT"; //准贷记卡
    public static final String TYPE_BANKCARD_PREPAID =  "BANKCARD_PREPAID";//预付卡

    public static final String TYPE_WALLET_ALIPAY = "WALLET_ALIPAY"; //余额 支付宝钱包
    public static final String TYPE_WALLET_ALIPAY_FINANCE = "WALLET_ALIPAY_FINANCE"; //余额 余额宝
    public static final String TYPE_WALLET_WEIXIN = "WALLET_WEIXIN"; //余额 微信钱包
    public static final String TYPE_WALLET_FOXCONN = "WALLET_FOXCONN"; //余额 富士康钱包
    public static final String TYPE_WALLET_GRABPAY = "WALLET_GRABPAY"; //余额 grabpay钱包

    public static final String TYPE_WALLET_MPAY = "WALLET_MPAY"; // 澳门通 mpay

    public static final String TYPE_WALLET_SODEXO = "WALLET_SODEXO"; //索迪斯


    public static final String TYPE_CUSTOM_ALIPAY_HUABEI = "ALIPAY_HUABEI"; //支付宝 花呗
    public static final String TYPE_CUSTOM_ALIPAY_POINT = "ALIPAY_POINT"; //支付宝 集分宝

    public static final String TYPE_WALLET_CMCC_WALLET = "CMCC_WALLET"; //和包现金钱包

    public static final String TYPE_JD_BAITIAO = "JD_BAITIAO";//京东白条

    public static final String TYPE_SQB_PREPAID_CARD = "SQB_PREPAID_CARD"; //收钱吧储值卡
    public static final String TYPE_SQB_PREPAID_CARD_RECHARGE = "SQB_PREPAID_CARD_RECHARGE"; //收钱吧储值卡 实充
    public static final String TYPE_SQB_PREPAID_CARD_GIFT = "SQB_PREPAID_CARD_GIFT"; //收钱吧储值卡  赠送

    public static final String TYPE_WEIXIN_INSTALL = "WEIXIN_INSTALL_PAY";//微信分付

    public static final String TYPE_HOPE_EDU = "HOPE_EDU_PAY";//院校通 一码通支付

    public static final String TYPE_OTHERS = "OTHERS"; //其他未知类型
    public static final String TYPE = "type"; //付款类型
    public static final String ORIGIN_TYPE = "origin_type";     // 原始付款方式   记录支付通道原始的付款方式
    public static final String ORIGIN_NAME = "origin_name";     // 原始付款或者优惠名称
    public static final String AMOUNT_TOTAL = "amount_total"; //原始总金额
    public static final String NET_AMOUNT = "net_amount";// 剩余的金额
    public static final String AMOUNT = "amount"; //变动金额
    public static final String SOURCE = "source"; //付款来源，记录相关联的优惠立减活动编号，以及核销红包的id等
    public static final String IS_DELIVERY = "is_delivery"; //标明优惠是否是上送的优惠

}
