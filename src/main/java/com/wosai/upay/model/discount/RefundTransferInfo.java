package com.wosai.upay.model.discount;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 撤销转账信息
 * @date 2024-06-24
 */
@Data
public class RefundTransferInfo {
    /**
     * 流水号
     */
    private String traceNo;
    /**
     * 源流水号
     */
    private String srcTraceNo;
    /**
     * 是否撤销转账失败
     */
    private Boolean refundTransFailed;
    /**
     *  历史重试流水号列表
     */
    private List<String> historyTraceNoList;
    /**
     * 撤销转账的金额
     */
    private long refundTransAmount;
    /**
     * 撤销转账成功日期，格式: YYYYMMDD
     */
    private String transDate;
}
