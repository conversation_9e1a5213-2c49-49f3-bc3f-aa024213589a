package com.wosai.upay.model.discount;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 费用详情
 * @date 2024-06-24
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NotifyFeeDetail {
    /**
     * 富友费率模板
     */
    private String reservedSetCd;
    /**
     * 费用
     */
    private Long fee;
    /**
     * 通道侧收取的手续费
     */
    private Long providerFee;
    /**
     * 基础费率
     */
    private String baseFeeRate;
    /**
     * 实际费率
     */
    private String actualFeeRate;
    /**
     * 额度包的优惠费率
     */
    private String quotaFeeRate;
    /**
     * 额度包的优惠额度id
     */
    private Long quotaRecordId;

     /**
     * 业务标识。目前在刷卡中使用，通过该字段判断余额写入内/外卡钱包
     */
    private String reservedBusiCd;
}
