package com.wosai.upay.model;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/8/29.
 */
public class CsbToWapConfig {

    private Map<Integer/*payway*/, Set<String>/*provider_trade_params_key*/> paywayProviderWhiteList; //支付源通道白名单


    public Map<Integer, Set<String>> getPaywayProviderWhiteList() {
        return paywayProviderWhiteList;
    }

    public void setPaywayProviderWhiteList(Map<Integer, Set<String>> paywayProviderWhiteList) {
        this.paywayProviderWhiteList = paywayProviderWhiteList;
    }

    public boolean isNotHit(int payway, Map<String, Object> tradeConfig) {
        if (MapUtils.isEmpty(tradeConfig)) {
            return true;
        }
        if (MapUtils.isEmpty(paywayProviderWhiteList)) {
            return true;
        }
        Set<String> providers = paywayProviderWhiteList.get(payway);
        if (CollectionUtils.isEmpty(providers)) {
            return true;
        }
        return providers.stream().noneMatch(tradeConfig::containsKey);
    }

    public boolean isHit(int payway, Map<String, Object> tradeConfig) {
        return !isNotHit(payway, tradeConfig);
    }

}
