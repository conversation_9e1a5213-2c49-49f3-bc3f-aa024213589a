package com.wosai.upay.helper;

import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.service.SceneConfigFacade;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.FakeRequestUtil;
import com.wosai.upay.util.LanguageCaseHolder;
import com.wosai.upay.util.MapUtil;
import com.wosai.upay.util.RateLimiterUtil;
import com.wosai.upay.util.WalletDeductionContextHolder;
import com.wosai.upay.wallet.request.UnFreezeWalletBalanceReq;
import com.wosai.upay.wallet.service.WalletServiceV3;
import net.logstash.logback.marker.Markers;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.hibernate.validator.method.MethodConstraintViolation;
import org.hibernate.validator.method.MethodConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.exception.InvalidParamException;
import com.wosai.upay.exception.ProviderStatusException;
import com.wosai.upay.exception.RefundFlagNotMatchException;
import com.wosai.upay.exception.TerminalConfigAbnormalException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.exception.UpayClientException;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.exception.UpaySystemError;
import com.wosai.upay.exception.ValidationException;


public class UpayServiceMethodInterceptor implements MethodInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(UpayServiceMethodInterceptor.class);
    private static final String RESULT_DEFERREDRESULT = "DeferredResult";
    
    @Autowired
    @Qualifier("walletService")
    private WalletServiceV3 walletService;
    @Autowired
    @Qualifier("fakeWalletService")
    private WalletServiceV3 fakeWalletService;

    private WalletServiceV3 getWalletService() {
        return FakeRequestUtil.isFakeRequest() ? this.fakeWalletService : this.walletService;
    }
    
    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Object result = doInvoke(invocation);
        if(result instanceof DeferredResult){
            return result;
        }else{
            if(RESULT_DEFERREDRESULT.equals(invocation.getMethod().getReturnType().getSimpleName())){
                DeferredResult response = new DeferredResult();
                response.setResult(result);
                return response;
            }else{
                return result;
            }
        }
    }


    public Object doInvoke(MethodInvocation invocation){
        final long before = System.currentTimeMillis();
        Object[] logArguments = logger.isTraceEnabled() ? getLogArguments(invocation.getArguments()) : null;
        if (logger.isTraceEnabled()) {
            logger.trace(Markers.appendEntries(getMethodStartLogAppendFields(invocation.getMethod().getDeclaringClass().getName(), invocation.getMethod().getName(), logArguments)), "invoking method start");
        }
        Object result = null;
        Throwable throwable = null;
        try {
            // 请求限流，只限制交易相关的操作
            rateLimiter(invocation);

            result = invocation.proceed();
            MapUtil.removeNullValues(result);
            return result;
        } catch (Throwable ex) {
            throwable = ex;
            result = handleException(ex);
            return result;
        } finally {
            String merchantId = WalletDeductionContextHolder.getMerchantId();
            String transactionId = WalletDeductionContextHolder.getTransactionId();
            WalletDeductionContextHolder.remove();
            LanguageCaseHolder.remove();
            if(throwable != null && merchantId != null){
                try{
                    getWalletService().unFreezeWalletBalance(new UnFreezeWalletBalanceReq(merchantId, transactionId));
                }catch (Exception e){
                    logger.error("upay wallet unfreezeWalletBalanceDeduction invoking fail: " + e.getMessage(), e);
                }
            }
            if(logger.isTraceEnabled()){
                if(result != null && result instanceof DeferredResult){
                    DeferredResult<Map<String,Object>> finalResult = (DeferredResult<Map<String, Object>>) result;
                    finalResult.onCompletion(RunnableWrapper.of(() -> {
                        long duration = System.currentTimeMillis() - before;
                        logger.trace(Markers.appendEntries(
                            getMethodEndLogAppendFields(invocation.getMethod().getDeclaringClass().getName(),
                                    invocation.getMethod().getName(), logArguments, duration, finalResult.getResult() , null)), "invoking method end");}));
                }else{
                    long duration = System.currentTimeMillis() - before;
                    if(throwable == null){
                        logger.trace(Markers.appendEntries(
                                getMethodEndLogAppendFields(invocation.getMethod().getDeclaringClass().getName(),
                                        invocation.getMethod().getName(), logArguments, duration, result , null)), "invoking method end");
                    }else{
                        logger.trace(Markers.appendEntries(
                                getMethodEndLogAppendFields(invocation.getMethod().getDeclaringClass().getName(),
                                        invocation.getMethod().getName(), logArguments, duration, result , throwable.getMessage())), "invoking method end", throwable);
                    }
                }
            }
        }
    }



    /**
     * 
     *  通过终端或门店进行进行限流
     * 
     * @param invocation
     */
    private void rateLimiter(MethodInvocation invocation) {
        if (invocation.getThis() instanceof UpayService) {
            if(invocation.getArguments().length > 0 && invocation.getArguments()[0] instanceof Map){
                String terminalSn = BeanUtil.getPropString(invocation.getArguments()[0], TransactionParam.TERMINAL_SN);
                String sn = !StringUtil.empty(terminalSn) ? terminalSn : BeanUtil.getPropString(invocation.getArguments()[0], UpayService.WOSAI_STORE_ID);
                RateLimiterUtil.verification(sn, invocation.getMethod().getName());
            }
        }
    }

    /**
     * @param ex
     * @return
     */
    public static Map<String, Object> handleException(Throwable ex){
        if(ex instanceof MethodConstraintViolationException){
            return validationError((MethodConstraintViolationException)ex);
        }else if(ex instanceof ValidationException){
            return validationError((ValidationException)ex);
        }else if(ex instanceof UpayClientException){
            return upayClientError((UpayClientException) ex);
        }else if(ex instanceof RefundFlagNotMatchException){
            return refundFlagNotMatchError((RefundFlagNotMatchException) ex);
        }else if(ex instanceof UpayBizException){
            return upayBizError((UpayBizException) ex);
        }else if(ex instanceof UpaySystemError){
            return systemError((UpayException) ex);
        }else if(ex instanceof InvalidParamException){
            return validationParamError((InvalidParamException)ex);
        }else {
            return unknownSystemError(ex);
        }
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> unknownSystemError(Throwable ex) {
        logger.error("unknown system error", ex);
        String message = ex.getMessage();
        if(ex instanceof DataAccessException){
            message = "数据访问异常";
        }
        return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SYSTEM_ERROR,
                                      CommonResponse.ERROR_CODE, CommonResponse.UNKNOWN_SYSTEM_ERROR,
                                      CommonResponse.ERROR_MESSAGE, "unknown system error");
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> systemError(UpayException ex) {
        Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(ex.getStandardCode(), ex.getData());
        if(null == configInfo){
    		return CollectionUtil.hashMap(CommonResponse.RESULT_CODE , CommonResponse.RESULT_CODE_SYSTEM_ERROR,
                    CommonResponse.ERROR_CODE, ex.getCode(),
                    CommonResponse.ERROR_MESSAGE, "系统错误");
    	}else{
	    	String errorMessage = BeanUtil.getPropString(configInfo, CommonResponse.ERROR_MESSAGE, ex.getMessage());
	    	if(!StringUtil.empty(BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD))){
	    		errorMessage = StringUtils.join(errorMessage, "[", BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD), "]");
	    	}
	    	return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SYSTEM_ERROR,
	    			CommonResponse.ERROR_CODE, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE),
	    			CommonResponse.ERROR_CODE_STANDARD, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD),
	    			CommonResponse.ERROR_MESSAGE, errorMessage);
    	}
    }

    @SuppressWarnings({ "unchecked", "deprecation" })
    private static Map<String, Object> validationError(MethodConstraintViolationException ex) {
        StringBuilder sb = new StringBuilder();
        for(MethodConstraintViolation<?> violation: ex.getConstraintViolations()) {
            if (sb.length() > 0)
                sb.append("\n");
            sb.append(violation.getMessage());
        }
        return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
                                      CommonResponse.ERROR_CODE, CommonResponse.INVALID_PARAMS,
                                      CommonResponse.ERROR_MESSAGE, sb.toString());
    }
    @SuppressWarnings({ "unchecked", "deprecation" })
    private static Map<String, Object> validationError(ValidationException ex) {
        return CollectionUtil.hashMap(
                CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
                CommonResponse.ERROR_CODE, ex.getCode(),
                CommonResponse.ERROR_MESSAGE, ex.getMessage());
    }

    @SuppressWarnings({ "unchecked", "deprecation" })
    private static Map<String, Object> validationParamError(InvalidParamException ex) {
        return CollectionUtil.hashMap(
                CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
                CommonResponse.ERROR_CODE, ex.getCode(),
                CommonResponse.ERROR_MESSAGE, ex.getMessage());
    }

    @SuppressWarnings("unchecked")
    private static Map<String, Object> upayClientError(UpayClientException ex) {
        Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(ex.getStandardCode(), ex.getData());
        if(null == configInfo){
    		return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
                    CommonResponse.ERROR_CODE, ex.getCode(),
                    CommonResponse.ERROR_MESSAGE, ex.getMessage());
    	}else{
	    	String errorMessage = BeanUtil.getPropString(configInfo, CommonResponse.ERROR_MESSAGE, ex.getMessage());
	    	if(!StringUtil.empty(BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD))){
	    		errorMessage = StringUtils.join(errorMessage, "[", BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD), "]");
	    	}
	    	return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
	                CommonResponse.ERROR_CODE, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE),
	                CommonResponse.ERROR_CODE_STANDARD, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD),
	                CommonResponse.ERROR_MESSAGE, errorMessage);
    	}
    }

    @SuppressWarnings("unchecked")
    private static Map<String, Object> upayBizError(UpayBizException ex) {
        Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(ex.getStandardCode(), ex.getData());
        if(null == configInfo){
    		return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
    		        CommonResponse.BIZ_RESPONSE, CollectionUtil.hashMap(CommonResponse.RESULT_CODE, ex.getResultCode(),
                            CommonResponse.ERROR_CODE, ex.getCode(),
                            CommonResponse.ERROR_MESSAGE, ex.getMessage()));
    	}else{
	    	String errorMessage = BeanUtil.getPropString(configInfo, CommonResponse.ERROR_MESSAGE, ex.getMessage());
	    	if((ex instanceof ProviderStatusException && !StringUtil.empty(ex.getMessage()))
	    	        || ex instanceof TerminalConfigAbnormalException){
	    	    errorMessage = ex.getMessage();
	    	}
	    	if(!StringUtil.empty(BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD))){
	    		errorMessage = StringUtils.join(errorMessage, "[", BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD), "]");
	    	}
	        return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
	                CommonResponse.BIZ_RESPONSE, CollectionUtil.hashMap(CommonResponse.RESULT_CODE, ex.getResultCode(),
	                        CommonResponse.ERROR_CODE, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE),
	                        CommonResponse.ERROR_CODE_STANDARD, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD),
	                        CommonResponse.ERROR_MESSAGE, errorMessage));
    	}
    }

    @SuppressWarnings("unchecked")
    private static Map<String, Object> refundFlagNotMatchError(RefundFlagNotMatchException ex) {
        Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(ex.getStandardCode(), ex.getData());
        if(null == configInfo){
            return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                    CommonResponse.BIZ_RESPONSE, CollectionUtil.hashMap(CommonResponse.RESULT_CODE, ex.getResultCode(),
                            CommonResponse.ERROR_CODE, ex.getCode(),
                            CommonResponse.ERROR_MESSAGE, ex.getMessage()));
        }else{
            String refundFlag = ex.getRefundFlag();
            String errorMessage = ex.getMessage();
            Object configErrorMessage = configInfo.get(CommonResponse.ERROR_MESSAGE);
            if(configErrorMessage instanceof String) {
                errorMessage = (String)configErrorMessage;
            }else if(configErrorMessage instanceof Map) {
                Map configErrorMessageMap = (Map)configErrorMessage;
                if(configErrorMessageMap.containsKey(refundFlag)) {
                    errorMessage = com.wosai.pantheon.util.MapUtil.getString(configErrorMessageMap, refundFlag);
                }else if(configErrorMessageMap.containsKey(UpayConstant.DEFAULT_REFUND_FLAG_KEY)){
                    errorMessage = com.wosai.pantheon.util.MapUtil.getString(configErrorMessageMap, UpayConstant.DEFAULT_REFUND_FLAG_KEY);
                }
            }

            if(!StringUtil.empty(BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD))){
                errorMessage = StringUtils.join(errorMessage, "[", BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD), "]");
            }
            return CollectionUtil.hashMap(CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                    CommonResponse.BIZ_RESPONSE, CollectionUtil.hashMap(CommonResponse.RESULT_CODE, ex.getResultCode(),
                            CommonResponse.ERROR_CODE, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE),
                            CommonResponse.ERROR_CODE_STANDARD, BeanUtil.getPropString(configInfo, CommonResponse.ERROR_CODE_STANDARD),
                            CommonResponse.ERROR_MESSAGE, errorMessage));
        }
    }

    class MethodInvocationFormatter {
        private final MethodInvocation invocation;
        private final boolean printArgs;
        public MethodInvocationFormatter(MethodInvocation invocation, boolean printArgs) {
            this.invocation = invocation;
            this.printArgs = printArgs;
        }
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("method ").append(invocation.getMethod());
            if (printArgs) {
                sb.append(" with arguments ");
                for(Object arg: invocation.getArguments()) {
                    sb.append(" =====> ").append(arg);
                }
            }
            return sb.toString();
        }
    }


    public Map<String, Object> getMethodStartLogAppendFields(String className, String methodName, Object[] logArguments){
        return CollectionUtil.hashMap(
                "path", className + "." + methodName,
                "arguments", logArguments

        );
    }

    public Map<String, Object> getMethodEndLogAppendFields(String className, String methodName, Object[] logArguments, Long durations , Object result , String  exceptionMessage){
        return CollectionUtil.hashMap(
                "path", className + "." + methodName,
                "arguments", logArguments,
                "durations", durations,
                "result", result,
                "exception", exceptionMessage
        );
    }


    private Object[] getLogArguments(Object[] arguments){
        // 下单接口会带上HttpServletResponse，如果新加接口也有不需要打印的参数，也需要特殊处理下
        if (arguments != null && arguments.length == 2 && arguments[1] instanceof HttpServletResponse) {
            return new Object[] {arguments[0]};
        }
        return arguments;
    }





}
