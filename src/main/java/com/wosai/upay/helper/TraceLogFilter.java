package com.wosai.upay.helper;

import org.slf4j.MDC;
import org.slf4j.Marker;

import com.wosai.middleware.hera.toolkit.trace.TraceContext;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;

public class TraceLogFilter extends TurboFilter {
    public static final String TRACE_ID_KEY = "trace_id";

    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String s, Object[] objects, Throwable throwable) {
        MDC.put(TRACE_ID_KEY, TraceContext.traceId());
        return FilterReply.NEUTRAL;
    }
}
