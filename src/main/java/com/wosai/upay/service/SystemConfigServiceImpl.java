package com.wosai.upay.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.upay.core.exception.CoreIOException;
import com.wosai.upay.core.model.SystemConfig;
import com.wosai.upay.repository.DataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;

import java.io.IOException;
import java.util.Map;

/**
 * Created by jianfree on 18/9/16.
 */
public class SystemConfigServiceImpl implements SystemConfigService {
    @Autowired
    private DataRepository dataRepository;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Cacheable("systemConfigContent")
    public  <T> T getSystemConfigContentByName(String name){
        Map map = this.dataRepository.getSystemConfigDao().filter(Criteria.where(SystemConfig.NAME).is(name)).fetchOne();
        Object content = BeanUtil.getProperty(map, SystemConfig.CONTENT);
        if(content instanceof byte[]){
            String contentStr = new String((byte[]) content);
            if(contentStr != null){
                contentStr = contentStr.trim();
            }
            if(contentStr != null && (contentStr.startsWith("{") || contentStr.startsWith("["))){
                try {
                    return (T)objectMapper.readValue((byte[]) content, Object.class);
                } catch (IOException e) {
                    throw new CoreIOException("SystemConfig: "+ name + " not a legal json string ");
                }
            }else{
                return (T)new String((byte [])content);
            }
        }
        return null;
    }
}
