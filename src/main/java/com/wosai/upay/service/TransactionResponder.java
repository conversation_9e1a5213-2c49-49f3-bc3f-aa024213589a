package com.wosai.upay.service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.api.weixin.v3.WeixinConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.AlipayV2ServiceProvider;
import com.wosai.upay.workflow.TransactionContext;

public class TransactionResponder {

    public static Map<String, Object> makeResponseData(TransactionContext context, boolean returnPayerInfo) {
        return makeResponseData(context.getOrder(), context.getTransaction(), returnPayerInfo);
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> makeResponseData(Map<String,Object> order, Map<String,Object> transaction, boolean returnPayerInfo) {
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        int orderStatus = MapUtil.getIntValue(order, Order.STATUS);
        if (Transaction.TYPE_PAYMENT == MapUtil.getIntValue(transaction, Transaction.TYPE) && Order.STATUS_PAY_ERROR == orderStatus) {
            //只有订单状态为PAY_ERROR的情况下，才会在指定时间之后将状态修改为PAY_CANCELED
            String provider = MapUtil.getString(transaction, Transaction.PROVIDER);
            long queryExpireTime = ApolloConfigurationCenterUtil.getProviderQueryExpireTime(provider);
            long currentTime = System.currentTimeMillis();
            long orderCtime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
            if (currentTime - orderCtime >= queryExpireTime) {
                orderStatus = Order.STATUS_PAY_CANCELED;
            } else if (Order.PAYWAY_BANKACCOUNT == MapUtil.getIntValue(transaction, Transaction.PAYWAY)) {
                //如果是微企付转账超过轮训时间，那么在24小时支付超时时间之内，返回的订单状态由PAY_ERROR修改为CREATED
                orderStatus = Order.STATUS_CREATED;
            }
        }
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, Collections.emptyMap());
        boolean isDesposit = MapUtil.getBooleanValue(extraOutFields, Transaction.IS_DEPOSIT, false);


        Map<String, Object> data = CollectionUtil.hashMap(QueryResponse.SN, order.get(Order.SN),
                                                          QueryResponse.TSN, transaction.get(Transaction.TSN),
                                                          QueryResponse.CLIENT_SN, order.get(Order.CLIENT_SN),
                                                          QueryResponse.CLIENT_TSN, transaction.get(Transaction.CLIENT_TSN),
                                                          QueryResponse.CTIME, BeanUtil.getPropString(transaction, DaoConstants.CTIME),
                                                          QueryResponse.STATUS, StateLabel.fromId(transactionStatus).getName(),
                                                          QueryResponse.PAYWAY,BeanUtil.getPropString(order, Order.PAYWAY),
                                                          QueryResponse.PAYWAY_NAME, UpayUtil.getPaywayName(BeanUtil.getPropInt(order, Order.PAYWAY)),
                                                          QueryResponse.SUB_PAYWAY,BeanUtil.getPropString(order, Order.SUB_PAYWAY),
                                                          QueryResponse.ORDER_STATUS, Order.Status.fromCode(orderStatus));

        if (transaction.get(Transaction.BUYER_LOGIN)!=null) {
            data.put(QueryResponse.PAYER_LOGIN, returnPayerInfo ? BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN) : null);
        }
        if (transaction.get(Transaction.BUYER_UID)!=null) {
            data.put(QueryResponse.PAYER_UID, returnPayerInfo ? BeanUtil.getPropString(transaction, Transaction.BUYER_UID) : null);
        }
        if (isReturnOrderTradeNo(order, isDesposit)) {
            data.put(QueryResponse.TRADE_NO, BeanUtil.getPropString(order, Order.TRADE_NO));
        } else if (transaction.get(Transaction.TRADE_NO) != null) {
            data.put(QueryResponse.TRADE_NO, BeanUtil.getPropString(transaction, Transaction.TRADE_NO));
        }
        String channelTradeNo = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS), Transaction.CHANNEL_TRADE_NO);
        if (channelTradeNo != null){
            data.put(QueryResponse.CHANNEL_TRADE_NO, channelTradeNo);
        }

        if (order.get(Order.ORIGINAL_TOTAL)!=null) {
            data.put(QueryResponse.TOTAL_AMOUNT, BeanUtil.getPropString(order, Order.ORIGINAL_TOTAL));
        }
        if (order.get(Order.NET_ORIGINAL)!=null) {
            data.put(QueryResponse.NET_AMOUNT, BeanUtil.getPropString(order, Order.NET_ORIGINAL));
        }
        if(BeanUtil.getPropInt(transaction, Transaction.STATUS) == Transaction.STATUS_SUCCESS){
            data.put(QueryResponse.SETTLEMENT_AMOUNT, PaymentUtil.calculateSettlementAmountByTransaction(transaction) + "");
        }
        if(transaction.get(Transaction.FINISH_TIME) != null){
            data.put(QueryResponse.FINISH_TIME, BeanUtil.getPropString(transaction, Transaction.FINISH_TIME));
        }
        if(transaction.get(Transaction.CHANNEL_FINISH_TIME) != null){
            data.put(QueryResponse.CHANNEL_FINISH_TIME,BeanUtil.getPropString(transaction,Transaction.CHANNEL_FINISH_TIME));
        }
        if(order.get(Order.SUBJECT) != null){
            data.put(QueryResponse.SUBJECT,BeanUtil.getPropString(order,Order.SUBJECT));
        }
        if(order.get(Order.BODY) != null){
            data.put(QueryResponse.DESCRIPTION, BeanUtil.getPropString(order,Order.BODY));
        }
        if(order.get(Order.STORE_ID) != null){
            data.put(QueryResponse.STORE_ID,BeanUtil.getPropString(order,Order.STORE_ID));
        }
        if(order.get(Order.TERMINAL_ID) != null){
            data.put(QueryResponse.TERMINAL_ID,BeanUtil.getPropString(order,Order.TERMINAL_ID));
        }
        if(BeanUtil.getPropBoolean(order, Order.TCP_MODIFIED)){
            data.put(QueryResponse.TCP_MODIFIED, true);
        }

        String operator =  BeanUtil.getPropString(transaction, Transaction.OPERATOR);
        if(operator != null){
            data.put(QueryResponse.OPERATOR,operator);
        }

        if(transaction.get(Transaction.REFLECT) != null) {
            data.put(QueryResponse.REFLECT, transaction.get(Transaction.REFLECT));
        }

        if(transaction.get(Transaction.PROVIDER_RESPONSE) != null) {
        	data.put(QueryResponse.PROVIDER_RESPONSE, transaction.get(Transaction.PROVIDER_RESPONSE));
        }

        // 返回支付通道信息
        if(Transaction.TYPE_PAYMENT == MapUtil.getIntValue(transaction, Transaction.TYPE) && UpayUtil.isReturnProviderResponse(transaction)) {
            int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            if(Order.PAYWAY_ALIPAY2 == payway || Order.PAYWAY_WEIXIN == payway) {
                Map<String, Object> providerResponse = (Map<String, Object>) data.get(QueryResponse.PROVIDER_RESPONSE);
                if(Objects.isNull(providerResponse)) {
                    providerResponse = new HashMap<String, Object>();
                }
                // 支付成功后返回代金券信息
                if(Transaction.STATUS_SUCCESS ==  MapUtil.getIntValue(transaction, Transaction.STATUS)) {
                    MapUtil.addKeysIfNotExist(extraOutFields, providerResponse, QueryResponse.GOODS_DETAILS, QueryResponse.VOUCHER_DETAILS);
                    if(Order.PAYWAY_WEIXIN == payway){
                        MapUtil.addKeysIfNotExist(extraOutFields, providerResponse, Transaction.SUB_IS_SUBSCRIBE);
                    }
                }
                data.put(QueryResponse.PROVIDER_RESPONSE, providerResponse);
            }
        }

        Object advanceAmountCent = BeanUtil.getNestedProperty(transaction, AlipayV2ServiceProvider.ADVANCE_AMOUNT_PATH);
        if (advanceAmountCent != null) {
            data.put(QueryResponse.ADVANCE_AMOUNT, String.valueOf(advanceAmountCent));
        }
        if (MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_BANKCARD) {
            String batchBillNo = MapUtil.getString(extraOutFields, Transaction.BATCH_BILL_NO);
            if (!StringUtil.empty(batchBillNo)) {
                data.put(QueryResponse.BATCH_BILL_NO, batchBillNo);
            }
            String sysTraceNo = MapUtil.getString(extraOutFields, Transaction.SYS_TRACE_NO);
            if (!StringUtil.empty(sysTraceNo)) {
                data.put(QueryResponse.SYS_TRACE_NO, sysTraceNo);
            }
            if (isDesposit) {
                // 返回预授权判断
                String authNo = BeanUtil.getPropString(extraOutFields, Transaction.AUTH_NO);
                if (!StringUtil.empty(authNo)) {
                    data.put(QueryResponse.AUTH_NO, authNo);
                }
                String refernumber = BeanUtil.getPropString(extraOutFields, Transaction.REFER_NUMBER);
                if (!StringUtil.empty(refernumber)) {
                    data.put(QueryResponse.REFER_NUMBER, refernumber);
                }
                data.put(QueryResponse.FREEZE_TIME, MapUtil.getString(order, DaoConstants.CTIME));
                String consumeTime =  MapUtil.getString(extraOutFields, Transaction.CONSUME_TIME);
                if (!StringUtil.empty(consumeTime)) {
                    data.put(QueryResponse.CONSUME_TIME, consumeTime);
                }
            }
        }

        //银行转账 交易成功之后返回payer信息
        if (Transaction.TYPE_PAYMENT == MapUtil.getIntValue(transaction, Transaction.TYPE)
                && MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_BANKACCOUNT) {

            Map<String, Object> providerResponse = (Map<String, Object>) data.get(QueryResponse.PROVIDER_RESPONSE);
            if(Objects.isNull(providerResponse)) {
                providerResponse = new HashMap<>();
            }
            Map<String, Object> payerInfo = MapUtil.getMap(extraOutFields, Transaction.PAYER_INFO);
            if (MapUtil.isNotEmpty(payerInfo)) {
                providerResponse.put(QueryResponse.PAYER_INFO, payerInfo);
            }
            data.put(QueryResponse.PROVIDER_RESPONSE, providerResponse);
        }

        // 返回境外支付信息
        if(transactionStatus == Transaction.STATUS_SUCCESS) {
            Map extraOutFileds = (Map)transaction.get(Transaction.EXTRA_OUT_FIELDS);
            Map overseas = MapUtil.getMap(extraOutFileds, Transaction.OVERSEAS);
            if (overseas != null && !overseas.isEmpty()) {
                String currency = MapUtil.getString(overseas, Transaction.CURRENCY);
                if (!StringUtil.empty(currency)) { 
                    data.put(QueryResponse.PAYER_CURRENCY, currency);
                }
                String payerAmount = MapUtil.getString(overseas, Transaction.PAYER_AMOUNT);
                if (!StringUtil.empty(payerAmount)) { 
                    data.put(QueryResponse.PAYER_AMOUNT, payerAmount);
                }
                String exchangeRate = MapUtil.getString(overseas, Transaction.EXCHANGE_RATE);
                if (!StringUtil.empty(exchangeRate)) { 
                    data.put(QueryResponse.EXCHANGE_RATE, exchangeRate);
                }
            }
        }

        Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS, Collections.emptyMap());
        String sqbWxVersion = MapUtil.getString(extraParams, Transaction.SQB_WX_VERSION);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);

        if (payway == Payway.WEIXIN.getCode() && Objects.equals(TransactionParam.WEIXIN_VERSION_V3, sqbWxVersion) && extraOutFields.containsKey(Transaction.WX_INSTALLMENT_INFO)) {
            Map installmentInfo = MapUtil.getMap(extraOutFields, Transaction.WX_INSTALLMENT_INFO, new HashMap());
            int fqNum = MapUtil.getIntValue(installmentInfo, Transaction.WX_INSTALLMENT_INFO_NUM);
            data.put(QueryResponse.INSTALLMENT_INFO, MapUtil.hashMap(
                    QueryResponse.INSTALLMENT_INFO_NUM, String.valueOf(fqNum)
            ));
        }
        return data;
    }

    private static boolean isReturnOrderTradeNo(Map<String, Object> order, boolean isDesposit) {
        int payway = MapUtil.getIntValue(order, Order.PAYWAY);
        int provider = MapUtil.getIntValue(order, Order.PROVIDER);
        if (payway == Order.PAYWAY_BANKCARD && provider == Order.PROVIDER_LAKALA_UNION_PAY_V3) {
            return true;
        }
        if(isDesposit && payway == Order.PAYWAY_BANKCARD && provider == Order.PROVIDER_FUYOU){
            return true;
        }
        return false;
    }
}
