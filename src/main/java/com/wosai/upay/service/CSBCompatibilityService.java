package com.wosai.upay.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.Sets;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.shorturl.api.ShortUrlService;
import com.wosai.shorturl.api.request.ShortUrlGenRequest;
import com.wosai.shorturl.api.result.ShortUrlGenResult;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.exception.ExternalServiceException;
import com.wosai.upay.exception.TerminalNotExistsException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.CsbToWapConfig;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> Date: 2019-08-28 Time: 15:00
 */
@Service
public class CSBCompatibilityService {
    private static final Logger logger = LoggerFactory.getLogger(CSBCompatibilityService.class);
    public static final String REQUEST_CTIME = "request_time";

    public static final String BIZ_TYPE_PROCREATE= "procreate";
    public static final String BIZ_TYPE_QUERY= "query";
    public static final String BIZ_TYPE_CANCEL= "cancel";

    private static final String HEADER_X_ENV_FLAG = "x-env-flag";

    private static final Charset DEFAULT_CHARSET = Charsets.UTF_8;
    private static final long CSB_REQUEST_EXPIRE_SECONDS = 3 * 60;
    private static final long CSB_SHORT_URL_EXPIRE_MILLIS = 1000 * 60 * 4;

    private static final Set<Integer> CSB_TO_WAP_PAYWAY_SET = Sets.newHashSet(
            Order.PAYWAY_WEIXIN, Order.PAYWAY_WEIXIN_HK,
            Order.PAYWAY_UNIONPAY, Order.PAYWAY_BESTPAY
    );


    @Autowired
    private QrcodeImaging qrcodeImaging;

    @Autowired
    private ShortUrlService shortUrlService;

    @Autowired
    private TerminalService terminalService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ObjectMapper om;

    @Value("${qr.host}")
    private String qrHost;
    @Value("${qr.return.host}")
    private String qrReturnHost;
    @Value("${shouqianba.region}")
    private String region;

    // 解决异地多活流量转发到其它区域问题
    @PostConstruct
    private void changeQrHost(){
        if(!StringUtil.empty(region)) {
            qrHost += "_" + region;
        }
    }

    public String genShortUrl(String oriUrl,long validityPeriod){
        ShortUrlGenRequest request = new ShortUrlGenRequest();
        request.setOriUrl(oriUrl);
        request.setValidityPeriod(validityPeriod);

        ShortUrlGenResult result;
        try {
            result = shortUrlService.genShortUrl(request);
            if (Objects.nonNull(result)) {
                return result.getShortUrl();
            }
        } catch (Exception e) {
            logger.error("invoke short-url exception, e", e);
            throw new ExternalServiceException(UpayErrorScenesConstant.SHORT_URL_SERVER_ERR
                    , UpayErrorScenesConstant.SHORT_URL_SERVER_ERR_MESSAGE);
        }

        logger.error("invoke short-url return null");
        throw new ExternalServiceException(UpayErrorScenesConstant.SHORT_URL_SERVER_ERR
                , UpayErrorScenesConstant.SHORT_URL_SERVER_ERR_MESSAGE);
    }

    public String getTerminalCurrentSecret(String terminalSn) {
        Map<String, Object> result = terminalService.getSecret(terminalSn);
        return BeanUtil.getPropString(result, Terminal.CURRENT_SECRET);
    }

    public String getOneTerminalSn(String wosaiStoreId){
        Map<String,Object> store  = storeService.getStoreByStoreSn(wosaiStoreId);
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        ListResult terminals = terminalService.findTerminals(new PageInfo(1, 1), CollectionUtil.hashMap(
                "store_id", storeId,
                "status", Terminal.STATUS_ACTIVATED
        ));
        if(terminals != null && terminals.getRecords() != null && terminals.getRecords().size() > 0){
            Map<String,Object> terminal = terminals.getRecords().get(0);
            return BeanUtil.getPropString(terminal, Terminal.SN);
        }
        return null;
    }

    public Map<String, Object> queryCSBRequest(String clientSn, String sn, String terminalOrStoreSn) {
        String key = null;
        if (StringUtils.isNotEmpty(sn)) {
            key = sn + ":" + terminalOrStoreSn;
        } else if (StringUtils.isNotEmpty(clientSn)) {
            key = clientSn + ":" + terminalOrStoreSn;
        }
        return JsonUtil.jsonStrToObject((String) redisTemplate.boundValueOps(key).get(), Map.class);
    }


    /**
     * 是否需要针对csb做兼容性处理
     *
     * @param request
     * @return
     */
    public boolean csbRequestIsNeedCsbCompatibleProcess(Map<String,Object> request, Map<String, Object> tradeConfig) {
        String payway = (String)request.get(UpayService.PAYWAY);
        String subPayway = (String)request.get(UpayService.SUB_PAYWAY);
        int paywayCode = Integer.parseInt(payway);
        int subPaywayCode = (subPayway==null? Order.SUB_PAYWAY_QRCODE: Integer.parseInt(subPayway));
        CsbToWapConfig config = ApolloConfigurationCenterUtil.getCsbToWapConfig();
        return (CSB_TO_WAP_PAYWAY_SET.contains(paywayCode)
                    || (Objects.nonNull(config) && config.isHit(paywayCode, tradeConfig)))
                && subPaywayCode == Order.SUB_PAYWAY_QRCODE;
    }


    public DeferredResult<Map<String,Object>> processCSBRequestAndMockResponse(String sn, Map<String,Object> request
            , Map<String,Object> config){
        String terminalSn = (String)request.get(UpayService.TERMINAL_SN);
        String wosaiStoreId = (String)request.get(UpayService.WOSAI_STORE_ID);
        String clientSn = (String)request.get(UpayService.CLIENT_SN);
        String otherTerminalSn = null;

        if(StringUtil.empty(terminalSn)){
            otherTerminalSn = getOneTerminalSn(wosaiStoreId);
            if(StringUtil.empty(otherTerminalSn)){
                throw new TerminalNotExistsException(UpayErrorScenesConstant.UPAY_TERMINAL_NOT_EXISTS_MESSAGE);
            }
        }

        //订单号存下来
        Map<String,Object> extended = (Map<String, Object>) UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);
        if(extended == null){
            extended = new HashMap<>();
            request.put(UpayService.EXTENDED, extended);
        }
        extended.put(Transaction.SQB_CSB_TO_WAP_SN, sn);
        request.put(REQUEST_CTIME, System.currentTimeMillis());


        if(StringUtil.empty(terminalSn)){
            saveCSBRequest(clientSn, sn, otherTerminalSn, request);
            saveCSBRequest(clientSn, sn, wosaiStoreId, request);
        }else{
            saveCSBRequest(clientSn, sn, terminalSn, request);
        }
        request.put(UpayService.TERMINAL_SN, StringUtil.empty(terminalSn) ? otherTerminalSn : terminalSn);
        return mockPrecreateResponse(sn, request, config);
    }


    public void saveCSBRequest(String clientSn, String sn, String terminalOrStoreSn, Map<String, Object> request) {
        byte[] ctKey = (clientSn + ":" + terminalOrStoreSn).getBytes(DEFAULT_CHARSET);
        byte[] stKey = (sn + ":" + terminalOrStoreSn).getBytes(DEFAULT_CHARSET);
        byte[] value = JsonUtil.toJsonStr(request).getBytes();

        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            connection.setEx(ctKey, CSB_REQUEST_EXPIRE_SECONDS, value);
            connection.setEx(stKey, CSB_REQUEST_EXPIRE_SECONDS, value);
            return null;
        });

    }

    /**
     * 撤单时清除缓存
     */
    public void removeCSBRequestCacheWhenCancel(Map<String,Object> csbRequest){
        if(csbRequest == null){
            return;
        }
        String clientSn = BeanUtil.getPropString(csbRequest, UpayService.CLIENT_SN);
        String terminalSn = BeanUtil.getPropString(csbRequest, UpayService.TERMINAL_SN);
        if(StringUtil.empty(terminalSn)){
            //为了实现简单，如果是基于门店来做交易，此处暂时不删除缓存, 可能会导致基于门店的交易，撤单后，又继续支付了。
           return;
        }
        String sn = (String) BeanUtil.getNestedProperty(csbRequest, UpayService.EXTENDED + "." + Transaction.SQB_CSB_TO_WAP_SN);
        byte[] ctKey = (clientSn + ":" + terminalSn).getBytes(DEFAULT_CHARSET);
        byte[] stKey = (sn + ":" + terminalSn).getBytes(DEFAULT_CHARSET);
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            connection.del(ctKey, stKey);
            return null;
        });
    }


    /**
     * 预下单mock响应数据
     *
     * @param request
     * @param config
     * @return
     */
    public DeferredResult<Map<String,Object>> mockPrecreateResponse(String sn, Map<String, Object> request, Map<String, Object> config) {
        return mockPrecreateResponse(sn,request,config,CSB_SHORT_URL_EXPIRE_MILLIS,false);
    }

    public DeferredResult<Map<String,Object>> mockPrecreateResponse(String sn,Map<String,Object> request,Map<String, Object> config,long validityPeriod,boolean passThrough){
        Map<String,Object>data;
        if(config != null && !config.isEmpty()){
            String merchantId = BeanUtil.getPropString(config, TransactionParam.MERCHANT_ID);
            String storeId = BeanUtil.getPropString(config, TransactionParam.STORE_ID);
            String terminalId = BeanUtil.getPropString(config, TransactionParam.TERMINAL_ID);
            //构建返回data
            data = buildResultData(request, merchantId, storeId, terminalId, BIZ_TYPE_PROCREATE);
        }else {
            data = new HashMap<>();
        }
        String qrShortUrl = genShortUrl(buildQrUrl(sn, request, passThrough),validityPeriod);
        data.put(PayResponse.QR_CODE, qrShortUrl);
        data.put(PayResponse.QR_CODE_IMAGE_URL, qrcodeImaging.getQrcodeImageUrl(qrShortUrl));
        DeferredResult<Map<String,Object>> result = new DeferredResult<>();
        result.setResult(UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PRECREATE_SUCCESS
                , null, null, null, data)));
        return result;
    }


    /**
     * 查单mock相应数据
     *
     * @param request
     * @param basicParams
     * @return
     */
    public Map<String, Object> mockQueryResponse(Map<String, Object> request, Map<String, Object> basicParams) {
        String merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(basicParams, TransactionParam.STORE_ID);
        String terminalId = BeanUtil.getPropString(basicParams, TransactionParam.TERMINAL_ID);
        //构建返回data
        Map<String, Object> data = buildResultData(request, merchantId, storeId, terminalId, BIZ_TYPE_QUERY);

        return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_SUCCESS
                , null, "EP104", null, data));
    }


    /**
     * 撤单mock响应数据
     *
     * @param request
     * @param basicParams
     * @return
     */
    public Map<String, Object> mockCancelResponse(Map<String, Object> request, Map<String, Object> basicParams) {
        String merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(basicParams, TransactionParam.STORE_ID);
        String terminalId = BeanUtil.getPropString(basicParams, TransactionParam.TERMINAL_ID);
        //构建返回data
        Map<String, Object> data = buildResultData(request, merchantId, storeId, terminalId, BIZ_TYPE_CANCEL);

        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_ABORT_SUCCESS
                , null, null, null, data));
    }




    /**
     * @param request
     * @return
     */
    public boolean wapRequestIsOriginalC2bRequest(Map<String,Object> request){
        Map<String,Object> extend = UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);
        if(BeanUtil.getPropString(extend, Transaction.SQB_CSB_TO_WAP_SN) != null){
            return true;
        }
        return false;
    }

    /**
     *
     * @param request
     * @return
     */
    public Map<String,Object> mergeOriginalC2bRequest(Map<String,Object> request){
        Map<String,Object> extend = UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);
        String sqbSn = BeanUtil.getPropString(extend, Transaction.SQB_CSB_TO_WAP_SN);
        String terminalSn= (String)request.get(UpayService.TERMINAL_SN);
        String clientSn = (String)request.get(UpayService.CLIENT_SN);
        Map<String, Object> oriReq = queryCSBRequest(clientSn, sqbSn, terminalSn);
        if (MapUtils.isEmpty(oriReq)) {
            //订单超时
            throw new UpayBizException(UpayErrorScenesConstant.TRADE_TIMEOUT, UpayErrorScenesConstant.TRADE_TIMEOUT_MESSAGE);
        }
        String payerUid = BeanUtil.getPropString(request, UpayService.PAYER_UID);
        oriReq.put(UpayService.PAYER_UID, payerUid);
        oriReq.put(UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_WAP + "");
        String clientIp = BeanUtil.getPropString(request, UpayService.CLIENT_IP);
        if(!StringUtil.empty(clientIp) && clientIp.indexOf(",") > 0) {
            clientIp = clientIp.split(",")[0];
        }
        oriReq.put(UpayService.CLIENT_IP, clientIp);
        return oriReq;
    }


    /**
     * 构建返回data
     *
     * @param request
     * @param merchantId
     * @param storeId
     * @param terminalId
     * @param bizType
     * @return
     */
    private Map<String, Object> buildResultData(Map<String, Object> request, String merchantId, String storeId, String terminalId, String bizType) {
        Map<String, Object> order = buildOrder(request, merchantId, storeId, terminalId, bizType);
        Map<String, Object> transaction = buildTransaction(request, merchantId, storeId, terminalId, bizType);
        Map<String, Object> data = TransactionResponder.makeResponseData(order, transaction, false);
        data.put(UpayService.PAYMENT_LIST, new ArrayList<>());
        return data;
    }

    /**
     * 构建订单
     *
     * @param request
     * @param merchantId
     * @param storeId
     * @param terminalId
     * @param bizType
     * @return
     */
    private Map<String, Object> buildOrder(Map<String, Object> request, String merchantId, String storeId, String terminalId, String bizType) {
        Map<String, Object> extended = (Map<String, Object>) request.get(UpayService.EXTENDED);
        Map<String, Object> order = CollectionUtil.hashMap(
                DaoConstants.CTIME, new Date().getTime(),
                Order.SN, BeanUtil.getPropString(extended, Transaction.SQB_CSB_TO_WAP_SN),
                Order.CLIENT_SN, BeanUtil.getPropString(request, UpayService.CLIENT_SN),
                Order.SUBJECT, BeanUtil.getPropString(request, UpayService.SUBJECT),
                Order.BODY , BeanUtil.getPropString(request, UpayService.DESCRIPTION),
                Order.ORIGINAL_TOTAL, BeanUtil.getPropString(request, UpayService.TOTAL_AMOUNT),
                Order.PAYWAY, BeanUtil.getPropString(request, UpayService.PAYWAY),
                Order.SUB_PAYWAY, BeanUtil.getPropString(request, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_QRCODE + ""),
                Order.MERCHANT_ID, merchantId,
                Order.STORE_ID, storeId,
                Order.TERMINAL_ID, terminalId,
                Order.OPERATOR, BeanUtil.getPropString(request, UpayService.OPERATOR),
                Order.REFLECT, BeanUtil.getProperty(request, UpayService.REFLECT));

        switch (bizType) {
            case BIZ_TYPE_PROCREATE:
            case BIZ_TYPE_QUERY:
                order.put(Order.STATUS, Order.STATUS_CREATED);
                order.put(Order.NET_ORIGINAL, BeanUtil.getPropString(request, UpayService.TOTAL_AMOUNT));
                break;
            case BIZ_TYPE_CANCEL:
                order.put(Order.STATUS, Order.STATUS_CANCELED);
                order.put(Order.NET_ORIGINAL, "0");
                break;
            default:

        }

        return order;
    }

    /**
     * 构建流水
     *
     * @param request
     * @param merchantId
     * @param storeId
     * @param terminalId
     * @param bizType
     * @return
     */
    private Map<String, Object> buildTransaction(Map<String, Object> request, String merchantId, String storeId, String terminalId, String bizType) {
        Map<String, Object> extended = (Map<String, Object>) request.get(UpayService.EXTENDED);
        Map<String, Object> transaction = CollectionUtil.hashMap(
                DaoConstants.CTIME, BeanUtil.getPropLong(request, REQUEST_CTIME),
                Transaction.TSN, BeanUtil.getPropString(extended, Transaction.SQB_CSB_TO_WAP_SN),
                Transaction.CLIENT_TSN, BeanUtil.getPropString(request, UpayService.CLIENT_SN),
                Transaction.SUBJECT, BeanUtil.getPropString(request, UpayService.SUBJECT),
                Transaction.BODY, BeanUtil.getPropString(request, UpayService.BODY),
                Transaction.ORIGINAL_AMOUNT, BeanUtil.getPropString(request, UpayService.TOTAL_AMOUNT),
                Transaction.EFFECTIVE_AMOUNT, BeanUtil.getPropString(request, UpayService.TOTAL_AMOUNT),
                Transaction.MERCHANT_ID, merchantId,
                Transaction.STORE_ID, storeId,
                Transaction.TERMINAL_ID, terminalId,
                Transaction.OPERATOR, BeanUtil.getPropString(request, UpayService.OPERATOR),
                Transaction.PAYWAY, BeanUtil.getPropString(request, UpayService.PAYWAY),
                Transaction.SUB_PAYWAY, BeanUtil.getPropString(request, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_QRCODE + ""),
                Transaction.REFLECT, BeanUtil.getProperty(request, UpayService.REFLECT));

        switch (bizType) {
            case BIZ_TYPE_PROCREATE:
            case BIZ_TYPE_QUERY:
                transaction.put(Transaction.STATUS, Transaction.STATUS_CREATED);
                break;
            case BIZ_TYPE_CANCEL:
                transaction.put(Transaction.STATUS, Transaction.STATUS_ABORTED);
                break;
            default:

        }

        return transaction;
    }

    /**
     * 构建二维码url
     *
     * @param sn
     * @param request
     * @return
     */
    public String buildQrUrl(String sn, Map<String, Object> request,boolean passThrough){
        String terminalSn = BeanUtil.getPropString(request, UpayService.TERMINAL_SN);
        String secret = getTerminalCurrentSecret(terminalSn);
        secret = Objects.isNull(secret) ? StringUtils.EMPTY : secret;
        Map<String, String> queryParam = null;
        if(!passThrough) {
            queryParam = CollectionUtil.hashMap(
                    "client_sn", BeanUtil.getPropString(request, UpayService.CLIENT_SN),
                    "extended", JsonUtil.toJsonStr(CollectionUtil.hashMap(
                            Transaction.SQB_CSB_TO_WAP_SN, sn
                    )),
                    "payway", BeanUtil.getPropString(request, UpayService.PAYWAY),
                    "operator", BeanUtil.getPropString(request, UpayService.OPERATOR),
                    "subject", BeanUtil.getPropString(request, UpayService.SUBJECT),
                    "terminal_sn", terminalSn,
                    "total_amount", BeanUtil.getPropString(request, UpayService.TOTAL_AMOUNT)
            );
        }else {
            queryParam = new HashMap<>();
            for (String k : request.keySet()) {
                Object value = BeanUtil.getProperty(request, k);
                if(value instanceof Map){
                    queryParam.put(k, JsonUtil.toJsonStr(value));
                }else {
                    queryParam.put(k, String.valueOf(value));
                }
            }
        }
        queryParam.put("return_url", qrReturnHost);
        try{
            String envFlag = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(HEADER_X_ENV_FLAG);
            if(StringUtils.isNotEmpty(envFlag)){
                queryParam.put(HEADER_X_ENV_FLAG, envFlag);
            }
        }catch (Exception e){
            logger.error("get x-env-flag error: " + e.getMessage(), e);
        }
        //待签名源串
        String oriSignStr = buildOriQueryStr(queryParam, false);
        //签名
        String sign = null;
        try {
            sign = WeixinSignature.getMd5Sign(oriSignStr, secret, "utf-8");
        } catch (MpayException e) {
            //不支持的签名算法异常，忽略
            logger.error("sign err, e: ", e);
        }

        return qrHost + "?" + buildOriQueryStr(queryParam, true) + "&sign=" + sign;

    }

    /**
     * 构建源链接query字串
     *
     * @param signParam
     * @param valueUrlEncode
     * @return
     */
    private String buildOriQueryStr(Map<String, String> signParam, boolean valueUrlEncode) {
        List<String> keyList = new ArrayList<>(signParam.keySet());
        Collections.sort(keyList, String.CASE_INSENSITIVE_ORDER);
        StringBuilder paramTemp = new StringBuilder(keyList.size());
        for (String key : keyList) {
            String value = signParam.get(key);
            if (StringUtils.isEmpty(value)) {
                continue;
            }

            if (paramTemp.length() != 0) {
                paramTemp.append("&");
            }

            paramTemp.append(key).append("=");
            if (valueUrlEncode) {
                try {
                    paramTemp.append(URLEncoder.encode(value, "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    //忽略该异常
                    logger.error("Unsupported Encoding, e: ", e);
                }
            } else {
                paramTemp.append(value);
            }
        }

        return paramTemp.toString();
    }

}
