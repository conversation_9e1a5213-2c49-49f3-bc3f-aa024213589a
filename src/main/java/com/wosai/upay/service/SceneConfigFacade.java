package com.wosai.upay.service;

import com.wosai.constant.SceneConfigConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.sceneconfig.SceneConfigCache;
import com.wosai.upay.util.LanguageCaseHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;


@Service
public class SceneConfigFacade {


    private static Logger log = LoggerFactory.getLogger(SceneConfigFacade.class);

    private static int RETRY_TIME = 3;

    public static final String ERROR_CODE = "error_code";
    public static final String ERROR_MESSAGE = "error_message";
    public static final String ERROR_CODE_STANDARD = "error_code_standard";

    public static SceneConfigCache sceneConfigCache = new SceneConfigCache();

    private final String projectName = "upay-gateway";


    private Random random = new Random();

    @Autowired
    private static com.wosai.upay.scene.SceneConfigFacade sceneConfigService;


    /**
     * 获取交易错误码
     *
     * @param model           业务模型 pay：支付；query： 查单；refund：退款 ；cancel：撤单；fix：勾兑
     * @param payway          支付方式 微信、支付宝。。。
     * @param providerCode    支付通道返回码
     * @param providerMessage 支付通道返回信息
     * @return Map
     * key error_code：业务错误码
     * key error_message：返回信息
     * key error_code_standard：标准错误码
     */
    public static Map getBizErrorInfo(String model, int payway, String providerCode, String providerMessage) {
        Map<String, Object> extendMap = new LinkedHashMap<>();
        extendMap.put("model", model);
        extendMap.put("payway", payway);
        com.wosai.upay.scene.service.activity.response.SceneConfigRecord sceneConfigRecord = com.wosai.upay.scene.SceneConfigFacade.getSceneConfigRecord(providerCode, providerMessage, extendMap);
        Map<String, String> bizErrorInfo = buildInfoByUpayGatewayRecord(sceneConfigRecord);
        return filterInfoField(bizErrorInfo);
    }

    /**
     * 获取交易错误码
     *
     * @param sceneErrorCode 支付网关场景错误码 like EP39
     * @param data           业务数据
     * @return Map
     * key error_code：业务错误码
     * key error_message：返回信息
     * key error_code_standard：标准错误码
     */
    public static Map<String, String> getWosaiErrorDefinition(String sceneErrorCode, Map<String, Object> data) {
        if (StringUtils.isBlank(sceneErrorCode)) {
            return null;
        }

        com.wosai.upay.scene.service.activity.response.SceneConfigRecord upayGatewayRecord = com.wosai.upay.scene.SceneConfigFacade.getSceneConfigDefinition(sceneErrorCode);
        if (upayGatewayRecord == null) {
            return null;
        }
        Map<String, String> info = buildInfoByUpayGatewayRecord(upayGatewayRecord);
        info = filterInfoField(info);

        if (null != data && !data.isEmpty()) {
            String errorMessage = info.get(ERROR_MESSAGE);
            for (String key : data.keySet()) {
                if (errorMessage.contains(key)) {
                    errorMessage = errorMessage.replaceAll(key, BeanUtil.getPropString(data, key));
                }
            }
            info.put(ERROR_MESSAGE, errorMessage);
        }
        return info;
    }


    private static Map<String, String> filterInfoField(Map<String, String> bizErrorInfo) {
        Map<String, String> info = new LinkedHashMap<>();
        if (bizErrorInfo == null) {
            return null;
        }
        info.put(ERROR_CODE_STANDARD, bizErrorInfo.get(SceneConfigConstant.SCENE_ERROR_CODE));
        info.put(ERROR_CODE, bizErrorInfo.get(SceneConfigConstant.CODE));
        int languageCase = LanguageCaseHolder.getLanguageCase();
        if (languageCase == TransactionParam.LANGUAGE_CASE_CHINESE) {
            info.put(ERROR_MESSAGE, bizErrorInfo.get(SceneConfigConstant.TO_B_TIPS_ZH));
        } else {
            info.put(ERROR_MESSAGE, bizErrorInfo.get(SceneConfigConstant.TO_B_TIPS_EN));
        }
        return info;
    }


    private static Map<String, String>  buildInfoByUpayGatewayRecord(com.wosai.upay.scene.service.activity.response.SceneConfigRecord upayGatewayRecord) {
        if (upayGatewayRecord == null) {
            return null;
        }

        String sceneErrorCode = upayGatewayRecord.getSceneErrorCode();
        Map<String, String> info = new LinkedHashMap<>();
        info.put(SceneConfigConstant.SCENE_ERROR_CODE, sceneErrorCode);
        info.put(SceneConfigConstant.CODE, upayGatewayRecord.getCode());
        info.put(SceneConfigConstant.TO_B_TIPS_ZH, upayGatewayRecord.getToBTipsZh());
        info.put(SceneConfigConstant.TO_B_TIPS_EN, upayGatewayRecord.getToBTipsEn());
        return info;
    }

}
