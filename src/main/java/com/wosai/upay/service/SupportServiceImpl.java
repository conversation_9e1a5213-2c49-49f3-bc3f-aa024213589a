package com.wosai.upay.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.user.dto.sqbmp.UcUserDTO;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ResponseV1Fields;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.tl.syb.SybConstants;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.pantheon.util.RetryUtil.TimingStrategy;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.service.SharingService;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.*;
import com.wosai.upay.helper.UpayServiceMethodInterceptor;
import com.wosai.upay.model.api.AuthResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.RateLimiterUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.constant.UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING;

/**
 * <AUTHOR> by wkx
 * @date 2018/2/8
 **/
@Service
public class SupportServiceImpl implements SupportService{

    private static final Logger logger = LoggerFactory.getLogger(SupportServiceImpl.class);

    /**
     * 产品编码和通道的映射(同一个产品可能对应多个服务)
     * map: productCode -> providerAuthServiceList
     */
    private final Map<String, List<ProviderAuthService>> providerAuthServiceMap = new HashMap<>(16);

    public SupportServiceImpl(List<ProviderAuthService> providerAuthServices) {
        //将相同productCode的服务添加到同一个列表中
        providerAuthServices.forEach(providerAuthService -> {
            providerAuthServiceMap.computeIfAbsent(providerAuthService.getProductCode(), k -> new ArrayList<>())
                    .add(providerAuthService);
        });
    }

    @Autowired
    private ExternalServiceFacade facade;

    @Autowired
    private WorkflowManager workflowManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TsnGenerator tsnGenerator;

    @Autowired
    DataRepository dataRepository;

    @Autowired
    SharingService sharingService;

    @Autowired
    GatewaySupportService gatewaySupportService;

    @Autowired
    private SodexoWapServiceProvider sodexoWapServiceProvider;

    @Resource(name = "com.wosai.upay.workflow.DirectAlipayV2ServiceProvider")
    private DirectAlipayV2ServiceProvider directAlipayV2ServiceProvider;

    @Autowired
    private ExternalServiceFacade externalServiceFacade;
    @Autowired
    private CryptoService cryptoService;
    @Autowired
    private FuyouTradeFeeSyncWalletProcessor fuyouTradeFeeSyncWalletProcessor;
    @Autowired
    private UpayOrderService upayOrderService;
    @Autowired
    private FinishQuotaTransactionProcessor finishQuotaTransactionProcessor;

    @Resource(name = "confirmPayThreadPool")
    private ScheduledExecutorService confirmPayThreadPool;

    ScheduledExecutorService executor = Executors.newScheduledThreadPool(10);

    public Map<String,Object> queryUserId(Map<String,Object> request){
        String terminalSn= (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String payway = (String)request.get(PAYWAY);
        String dynamicId = (String)request.get(DYNAMIC_ID);
        String dynamicIdType = (String)request.get(DYNAMIC_ID_TYPE);
        String tradeAppId = MapUtil.getString(request, TRADE_APP);
        int paywayCode = UpayUtil.resolvePayway(payway, dynamicId, dynamicIdType);
        if(paywayCode!= Order.PAYWAY_WEIXIN&&paywayCode!=Order.PAYWAY_ALIPAY&&paywayCode!=Order.PAYWAY_ALIPAY2){
            throw new InvalidBarcodeException(UpayErrorScenesConstant.INVALID_BARCODE_QUERY_OPENID, UpayErrorScenesConstant.INVALID_BARCODE_QUERY_OPENID_MESSAGE);
        }
        Map<String, Object> config = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, paywayCode, Order.SUB_PAYWAY_BARCODE, tradeAppId);
        Map<String,Object> result;
        if(paywayCode==Order.PAYWAY_WEIXIN){
            DirectWeixinServiceProvider provider=workflowManager.getServiceProvider(DirectWeixinServiceProvider.class);
            result=provider.queryOpenIdByBarcode(config,dynamicId);
            if (QueryResponse.RESULT_CODE_SUCCESS.equals(String.valueOf(result.get(ResponseFields.RETURN_CODE)))
                    && QueryResponse.RESULT_CODE_SUCCESS.equals(String.valueOf(result.get(ResponseFields.RESULT_CODE)))){
                String openid= BeanUtil.getPropString(result, ResponseFields.OPEN_ID);
                String subOpenId=BeanUtil.getPropString(result,ResponseFields.SUB_OPEN_ID);
                Map<String,Object> data=new HashMap<>();
                data.put(ResponseFields.OPEN_ID,openid);
                data.put(ResponseFields.SUB_OPEN_ID,subOpenId);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_SUCCESS,null, null, null,data));
            }else{
                String errorCode=BeanUtil.getPropString(result,ResponseFields.ERR_CODE);
                String errorMessage=BeanUtil.getPropString(result,ResponseFields.ERR_CODE_DES);
                if(StringUtils.isEmpty(errorCode)){
                    errorMessage=BeanUtil.getPropString(result,ResponseFields.RETURN_MSG);
                }
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_FAIL,errorCode,errorMessage,null));
            }
        }else{
            DirectAlipayV2ServiceProvider provider=workflowManager.getServiceProvider(DirectAlipayV2ServiceProvider.class);
            result=provider.queryUserIdByBarcode(config,dynamicId);
            String userId=BeanUtil.getPropString(result, ResponseV1Fields.USER_ID);
            if(StringUtils.isEmpty(userId)){
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_FAIL, BeanUtil.getPropString(result,ResponseV1Fields.CODE), BeanUtil.getPropString(result,ResponseV1Fields.SUB_MSG), null));
            }else{
                Map<String,Object> data=new HashMap<>();
                data.put(ResponseFields.OPEN_ID,userId);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_SUCCESS,null, null, null,data));
            }
        }
    }

    @Override
    public Map<String, Object> queryUnionUserId(Map<String, Object> request) {
        String terminalSn= (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String payway = (String)request.get(PAYWAY);
        String subPayway = (String)request.get(SUB_PAYWAY);
        String tradeApp = MapUtil.getString(request, TRADE_APP);
        String userAuthCode = (String)request.get(USER_AUTH_CODE);
        String appUpIdentifier = MapUtil.getString(request, APP_UP_IDENTIFIER);
        int paywayCode = Integer.parseInt(payway);
        int subPaywayCode = Integer.parseInt(subPayway);
        Map<String, Object> config = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, paywayCode, subPaywayCode, tradeApp);

        Map<String, Object> extraParams = CollectionUtil.hashMap(Transaction.USER_AUTH_CODE, userAuthCode,Transaction.APP_UP_IDENTIFIER, appUpIdentifier);
        String sn = tsnGenerator.nextSn();
        Map<String, Object> transaction = CollectionUtil.hashMap(
                Transaction.TSN, sn,
                Transaction.PAYWAY, paywayCode,
                Transaction.SUB_PAYWAY, subPaywayCode,
                Transaction.EXTRA_PARAMS, extraParams,
                Transaction.CONFIG_SNAPSHOT, config);
        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        // serviceProvider 非空校验
        if (provider == null) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE);
        }
        Map<String,Object> result = provider.queryUserInfo(transaction);

        String userId = BeanUtil.getPropString(result, com.wosai.mpay.api.unionqrcode.BusinessFields.USER_ID);
        if(StringUtils.isEmpty(userId)){
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_FAIL, null, null, null));
        }else{
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_SUCCESS,null, null, null, result));
        }
    }

    @Override
    public void clearBasicCache(Map<String, Object> body) {
        String key = "BP-Cache-" + BeanUtil.getPropString(body, WOSAI_STORE_ID) + "-" + BeanUtil.getPropString(body, TERMINAL_SN);
        redisTemplate.delete(key);
    }

    @Override
    public Map<String, Object> getWxpayfaceAuthinfo(Map<String, Object> request) {
        String terminalSn= (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String rawdata = (String) request.get(RAWDATA);
        String tradeAppId = MapUtil.getString(request, TRADE_APP);
        Map<String, Object> config = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_BARCODE, tradeAppId);
        Map<String,Object> result;
        // 当前只有微信直连和银联微信支持，其它渠道如需支持，需要先配置app_id对应的支付秘钥
        if(!config.containsKey(TransactionParam.WEIXIN_TRADE_PARAMS)
                && !config.containsKey(TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS)
                && !config.containsKey(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS)){
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        WeixinServiceProvider provider = null;
        if (config.containsKey(TransactionParam.WEIXIN_TRADE_PARAMS)) {
            provider = workflowManager.getServiceProvider(DirectWeixinServiceProvider.class);
        } else if(config.containsKey(TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS)) {
            provider = workflowManager.getServiceProvider(DirectUnionPayWeixinServiceProvider.class);
        } else if(config.containsKey(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS)) {
            provider = workflowManager.getServiceProvider(LklUnionPayWeixinServiceProvider.class);
        }
        result = provider.getWxpayfaceAuthinfo(config, rawdata);
        if (QueryResponse.RESULT_CODE_SUCCESS.equals(String.valueOf(result.get(ResponseFields.RETURN_CODE)))) {
            String app_id = BeanUtil.getPropString(result, ResponseFields.APP_ID);
            String subAppId = BeanUtil.getPropString(result, ResponseFields.SUB_APP_ID);
            String mch_id = BeanUtil.getPropString(result, ResponseFields.MCH_ID);
            String subMchId = BeanUtil.getPropString(result, ResponseFields.SUB_MCH_ID);
            String authInfo = BeanUtil.getPropString(result, ResponseFields.AUTHINFO);
            int expires_in = BeanUtil.getPropInt(result, ResponseFields.EXPIRES_IN);
            Map<String, Object> data = new HashMap<>();
            data.put(ResponseFields.APP_ID,app_id);
            data.put(ResponseFields.SUB_APP_ID,subAppId);
            data.put(ResponseFields.MCH_ID,mch_id);
            data.put(ResponseFields.SUB_MCH_ID,subMchId);
            data.put(ResponseFields.AUTHINFO,authInfo);
            data.put(ResponseFields.EXPIRES_IN,expires_in);

            return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_SUCCESS, null, null, null, data));
        } else {
            String errorCode = BeanUtil.getPropString(result, ResponseFields.ERR_CODE);
            String errorMessage = BeanUtil.getPropString(result, ResponseFields.ERR_CODE_DES);
            if (StringUtils.isEmpty(errorCode)) {
                errorMessage = BeanUtil.getPropString(result, ResponseFields.RETURN_MSG);
            }

            return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_QUERY_FAIL, errorCode, errorMessage, null));
        }
    }

    @Override
    public Map<String, Object> genSn(Map<String, Object> request) {
        String terminalSn = (String)request.get(TERMINAL_SN);
        String storeId = (String)request.get(WOSAI_STORE_ID);
        Map<String, Object> basicParams = facade.getBasicParams(storeId, terminalSn);

        boolean isAllow = facade.isAllowGenOrderSn(BeanUtil.getPropString(basicParams, MerchantConfig.MERCHANT_ID));
        if (!isAllow) {
            throw new UpayBizException(UpayErrorScenesConstant.GEN_SN_NO_PERMISSION
                    , UpayErrorScenesConstant.GEN_SN_NO_PERMISSION_MESSAGE);
        }

        // 单号生成限流
        RateLimiterUtil.verifyGenSnRateLimit();
        String sn = tsnGenerator.nextSn();
        facade.saveGeneratedOrderSn(sn);
        Map<String, Object> resultData = Maps.newHashMap();
        resultData.put(Order.SN, sn);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS
                , null, null, null, resultData));
    }

    @Override
    public Map<String, Object> changeTradeHbfq(Map<String, Object> body) {
        String merchantId = MapUtil.getString(body, MERCHANT_ID);
        String orderSn = MapUtil.getString(body, ORDER_SN);
        int num =  MapUtil.getIntValue(body, HBFQ_NUM);
        int type = MapUtil.getIntValue(body, FQ_TYPE);
        boolean combinationPay = MapUtil.getBooleanValue(body, COMBINATION_PAY);
        long fqAmount = MapUtil.getLongValue(body, FQ_AMOUNT);
        Map<String, Object> orderInfo = dataRepository.getOrderByOrderSn(merchantId, orderSn);
        if(Objects.isNull(orderInfo)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        if(Order.PAYWAY_ALIPAY2 != MapUtil.getIntValue(orderInfo, Order.PAYWAY)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
        }
        Map<String, Object> payTransaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(orderInfo, Order.MERCHANT_ID), orderSn);
        if(Objects.isNull(payTransaction)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.TRANSACTION_NOT_EXIST, UpayErrorScenesConstant.TRANSACTION_NOT_EXIST_MESSAGE);
        }
        if(Transaction.STATUS_SUCCESS != MapUtil.getIntValue(payTransaction, Transaction.STATUS)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }
        long effectiveAmount = MapUtil.getLongValue(payTransaction, Transaction.EFFECTIVE_AMOUNT);
        Map<String, Object> extendedParams = Optional.ofNullable(MapUtil.getMap(payTransaction, Transaction.EXTENDED_PARAMS)).orElse(new HashMap<>());
        Map<String, Object> extendParams = Optional.ofNullable(MapUtil.getMap(extendedParams, BusinessV2Fields.EXTEND_PARAMS)).orElse(new HashMap<>());
        Map<String, Object> extraParams = Optional.ofNullable(MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS)).orElse(new HashMap<>());
        //取出来的时候位置变了
        int orgType = MapUtil.getIntValue(extraParams, Transaction.SQB_FQ_SERVICE_TYPE, 0);
        extendedParams.put(BusinessV2Fields.EXTEND_PARAMS, extendParams);
        //原先的productFlag
        final String orgProductFlag = Optional.ofNullable((String)payTransaction.get(Transaction.PRODUCT_FLAG)).orElse("");
        Integer changeType = null;
        Map<String, Object> transactionChangeInfo = MapUtil.hashMap(DaoConstants.ID, payTransaction.get(DaoConstants.ID),
                Transaction.MERCHANT_ID, payTransaction.get(Transaction.MERCHANT_ID),
                DaoConstants.VERSION, payTransaction.get(DaoConstants.VERSION));
        if(num > 0) {
            String productFlag = Optional.ofNullable((String)payTransaction.get(Transaction.PRODUCT_FLAG)).orElse("");
            extraParams.put(Transaction.SQB_FQ_SERVICE_TYPE, type);
            extraParams.put(Transaction.SQB_FQ_AMOUNT, fqAmount);
            if(combinationPay) {
                extraParams.put(Transaction.SQB_FQ_COMBINATION_PAY, true);
            }
            //花呗分期和信用卡分期不应该同时出现
            if(type == UpayConstant.USE_HBFQ) {
                productFlag = StringUtils.stringRemove(productFlag, ",", ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode());
                transactionChangeInfo.put(Transaction.PRODUCT_FLAG, UpayUtil.stringAppendIfAbsent(productFlag, ProductFlagEnum.HUABEI.getCode()));
            }else if(type == UpayConstant.USE_CREDIT){
                productFlag = StringUtils.stringRemove(productFlag, ",", ProductFlagEnum.HUABEI.getCode());
                transactionChangeInfo.put(Transaction.PRODUCT_FLAG, UpayUtil.stringAppendIfAbsent(productFlag, ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode()));
            }
            Map<String, Object> extraOutFields = Optional.ofNullable(MapUtil.getMap(payTransaction, Transaction.EXTRA_OUT_FIELDS)).orElse(new HashMap<>());
            extraOutFields.put(TransactionParam.HB_FQ, true);
            // 花呗分期发起人，1：商户侧发起分期，2：消费者支付时选择分期  根据修改后的产品标识符来判断 根据是否含有透出给支付宝的参数来判断是否是单通道
            productFlag = MapUtil.getString(transactionChangeInfo, Transaction.PRODUCT_FLAG, "");
            if(productFlag.contains(ProductFlagEnum.HUABEI.getCode())){
                int fqType = extendParams.containsKey(TransactionParam.HB_FQ_SELLER_PERCENT) ? 1 : 2;
                extraOutFields.put(Transaction.HB_FQ_SPONSOR, fqType);
                extraOutFields.put(Transaction.FQ_SPONSOR, fqType);
            }else if(productFlag.contains(ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode())){
                extraOutFields.put(Transaction.FQ_SPONSOR, extendParams.containsKey(TransactionParam.FQ_SELLER_PERCENT) ? 1 : 2);
            }
            transactionChangeInfo.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);

            // 花呗分期数发生变动，需要重新计算手续费
            // 判断原先是花呗分期的参数还是信用卡分期的参数
            int originNum = 0;
            if (orgProductFlag.contains(ProductFlagEnum.HUABEI.getCode()) && extendParams.containsKey(TransactionParam.HB_FQ_NUM)) {
                originNum = MapUtil.getIntValue(extendParams, TransactionParam.HB_FQ_NUM);
            }else if(orgProductFlag.contains(ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode()) && extendParams.containsKey(TransactionParam.FQ_NUM)){
                originNum = MapUtil.getIntValue(extendParams, TransactionParam.FQ_NUM);
            }
            if (originNum != num) {
                extendParams.remove(TransactionParam.HB_FQ_SELLER_PERCENT);
                extendParams.remove(TransactionParam.FQ_SELLER_PERCENT);
                extendedParams.remove(BusinessV2Fields.BUSINESS_PARAMS);
            }
            extendParams.put(TransactionParam.FQ_NUM, num);
            if(type == UpayConstant.USE_HBFQ){
                //更新花呗分期的期数 不存在下单选择花呗分期 支付的时候选择信用卡分期
                extendParams.put(TransactionParam.HB_FQ_NUM, num);
            }
            transactionChangeInfo.put(Transaction.EXTRA_PARAMS, extraParams);
            //如果是信用卡分期应该是去判断fq_seller_percent这个参数存不存在
            if ((orgType == UpayConstant.USE_HBFQ && !extendParams.containsKey(TransactionParam.HB_FQ_SELLER_PERCENT))
                    || (orgType == UpayConstant.USE_CREDIT && !extendParams.containsKey(TransactionParam.FQ_SELLER_PERCENT))) {
                extendParams.put(TransactionParam.HB_FQ_SELLER_PERCENT, 0);
                if (productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode())) {
                    changeType = UpayConstant.CHANGE_TYPE_REMOVE_HBFQ_DISCOUNT;
                }
                long fee = FeeUtil.applyRate(fqAmount, ApolloConfigurationCenterUtil.getBuyerServiceChargeFee(num, type));
                extraParams.put(Transaction.SQB_FQ_SELLER_SERVICE_CHARGE, 0);
                extraParams.put(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, fee);
                if(type == UpayConstant.USE_HBFQ) {
                    extraParams.put(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, 0);
                    extraParams.put(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, fee);
                }
            }else if(productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode())) {
                // 变更花呗手续费相关信息
                extendParams.put(TransactionParam.HB_FQ_SELLER_PERCENT, 100);
                originNum = (originNum == 0) ? 12 : originNum;
                if (originNum != num || effectiveAmount != fqAmount) {
                    changeType = UpayConstant.CHANGE_TYPE_UPDATE_HBFQ_DISCOUNT;
                }
                long fee = FeeUtil.applyRate(fqAmount, ApolloConfigurationCenterUtil.getSellerServiceChargeFee(num, type));
                extraParams.put(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, 0);
                extraParams.put(Transaction.SQB_FQ_SELLER_SERVICE_CHARGE, fee);
                if(type == UpayConstant.USE_HBFQ) {
                    extraParams.put(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, 0);
                    extraParams.put(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, fee);
                }
            }
            transactionChangeInfo.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            transactionChangeInfo.put(Transaction.EXTENDED_PARAMS, extendedParams);
        }else {
            String productFlag = Optional.ofNullable((String)payTransaction.get(Transaction.PRODUCT_FLAG)).orElse("");
            if(MapUtil.isEmpty(extendParams)
                    && !productFlag.contains(ProductFlagEnum.HUABEI.getCode())
                    && !productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode())
                    && !productFlag.contains(ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode())) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, dataRepository.getTransactionDao().get(MapUtil.getString(transactionChangeInfo, DaoConstants.ID))));
            }
            if(productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode())) {
                changeType = UpayConstant.CHANGE_TYPE_REMOVE_HBFQ_DISCOUNT;
            }
            Map<String, Object> extraOutFields = Optional.ofNullable(MapUtil.getMap(payTransaction, Transaction.EXTRA_OUT_FIELDS)).orElse(new HashMap<>());
            extraOutFields.put(TransactionParam.HB_FQ, false);
            transactionChangeInfo.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            productFlag = StringUtils.stringRemove(productFlag, ",", ProductFlagEnum.HUABEI.getCode());
            productFlag = StringUtils.stringRemove(productFlag, ",", ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode());
            transactionChangeInfo.put(Transaction.PRODUCT_FLAG, productFlag);
        }
        dataRepository.getTransactionDao().updatePart(transactionChangeInfo);
        Map<String, Object> transaction = dataRepository.getTransactionDao().get(MapUtil.getString(transactionChangeInfo, DaoConstants.ID));
        transaction.put(CHANGE_TYPE, changeType);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, transaction));
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> changeTradeHbfqSubsidy(Map<String, Object> body) {
        String merchantId = MapUtil.getString(body, MERCHANT_ID);
        String orderSn = MapUtil.getString(body, ORDER_SN);
        int type = MapUtil.getIntValue(body, CHANGE_TYPE);
        Map<String, Object> orderInfo = dataRepository.getOrderByOrderSn(merchantId, orderSn);
        if(Objects.isNull(orderInfo)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        int provider = MapUtil.getIntValue(orderInfo, Order.PROVIDER);
        if(Order.PAYWAY_ALIPAY2 != MapUtil.getIntValue(orderInfo, Order.PAYWAY) || (Order.PROVIDER_DIRECT_UNIONPAY != provider && Order.PROVIDER_LAKALA_UNION_PAY != provider)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
        }
        Map<String, Object> payTransaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(orderInfo, Order.MERCHANT_ID), orderSn);
        if(Objects.isNull(payTransaction)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.TRANSACTION_NOT_EXIST, UpayErrorScenesConstant.TRANSACTION_NOT_EXIST_MESSAGE);
        }
        if(Transaction.STATUS_SUCCESS != MapUtil.getIntValue(payTransaction, Transaction.STATUS)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }
        String productFlag = Optional.ofNullable(MapUtil.getString(payTransaction, Transaction.PRODUCT_FLAG)).orElse("");
        if(!productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode()) && type != UpayConstant.CHANGE_TYPE_REMOVE_HBFQ_DISCOUNT) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
        }
        if(type == UpayConstant.CHANGE_TYPE_UPDATE_HBFQ_DISCOUNT) {
            updateTradeHbfqSubsidyInfo(payTransaction, body);

        }else if(type == UpayConstant.CHANGE_TYPE_REMOVE_HBFQ_DISCOUNT){
            removeTradeHbfqSubsidyInfo(payTransaction, body);

        }
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, dataRepository.getTransactionDao().get(MapUtil.getString(payTransaction, DaoConstants.ID))));
    }

    /**
     *
     * 分期数发生变动，需要重新计算手续费
     *
     * @param payTransaction
     * @param body
     */
    @SuppressWarnings("unchecked")
    private void updateTradeHbfqSubsidyInfo(Map<String, Object> payTransaction, Map<String, Object> body) {
        Map<String, Object> payExtraParams = MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS, new HashMap<>());
        Map<String, Object> payProfitSharing = MapUtil.getMap(payExtraParams, Transaction.PROFIT_SHARING, new HashMap<>());
        List<Map<String, Object>> payReceivers = Optional.ofNullable((List<Map<String, Object>>)payProfitSharing.get(ProfitSharing.RECEIVERS)).orElse(new ArrayList<>());
        Map<String, Object> hbfqReceiver = payReceivers.stream().filter(r -> ProductFlagEnum.HBFQ_DISCOUNT.getCode().equals(MapUtil.getString(r, ProfitSharing.RECEIVER_PRODUCT_FLAG))).findFirst().orElse(new HashMap<>());
        if(MapUtil.isEmpty(hbfqReceiver)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
        }
        Map<String, Object> extendedParams = MapUtil.getMap(payTransaction, Transaction.EXTENDED_PARAMS);
        Map<String, Object> extendParams = MapUtil.getMap(extendedParams, BusinessV2Fields.EXTEND_PARAMS);
        String productFlag = MapUtil.getString(payTransaction, Transaction.PRODUCT_FLAG);

        int hbfqNum = MapUtil.getIntValue(extendParams, TransactionParam.HB_FQ_NUM);
        if(hbfqNum == 0) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
        }
        int fqType = UpayUtil.getFqTypeByProductFlag(productFlag);
        long fqAmount = MapUtil.getLongValue(payExtraParams, Transaction.SQB_FQ_AMOUNT, -1);
        if(fqAmount == -1){
            fqAmount = MapUtil.getLongValue(payTransaction, Transaction.EFFECTIVE_AMOUNT);
        }
        String fee = ApolloConfigurationCenterUtil.getSellerServiceChargeFee(hbfqNum, fqType);
        hbfqReceiver.put(ProfitSharing.RECEIVER_SHARING_AMOUNT, FeeUtil.applyRate(fqAmount, fee));
        hbfqReceiver.remove(ProfitSharing.RECEIVER_AMOUNT);
        payExtraParams.put(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, hbfqReceiver.get(ProfitSharing.RECEIVER_SHARING_AMOUNT));
        Map<String, Object> updatePart = MapUtil.hashMap(DaoConstants.ID, payTransaction.get(DaoConstants.ID),
                Transaction.MERCHANT_ID, payTransaction.get(Transaction.MERCHANT_ID),
                Transaction.EXTRA_PARAMS, payExtraParams,
                Transaction.EXTENDED_PARAMS, extendedParams,
                DaoConstants.VERSION, payTransaction.get(DaoConstants.VERSION)
        );
        dataRepository.getTransactionDao().updatePart(updatePart);

        executor.submit(() ->{
            TimingStrategy.Builder builder = new TimingStrategy.Builder()
                    .setRetry(3, 2000, 2.0)
                    .setJitter(true, 200);
            RetryUtil<Boolean> retryUtil = new RetryUtil<Boolean>()
                    .retry(builder.build())
                    .method(() -> {
                        // 重新进行分账
                        sharingService.manualSharingByUpayTransaction(payTransaction);
                        return true;
                    })
                    .on(throwable -> !com.wosai.profit.sharing.constant.ErrorMessageConstant.SHARING_REPEATED.equals(throwable.getMessage()))
                    .until(succeeded -> succeeded != null && succeeded);
            retryUtil.execute();
        });
        // 更新es汇总（用于报表展示）
        facade.reAnalyzeTrade(payTransaction);
    }

    /**
     *
     * 移除花呗分期商户贴息分账接收方
     *
     * @param payTransaction
     * @param body
     */
    @SuppressWarnings("unchecked")
    private void removeTradeHbfqSubsidyInfo(Map<String, Object> payTransaction, Map<String, Object> body) {
        String productFlag = Optional.ofNullable(MapUtil.getString(payTransaction, Transaction.PRODUCT_FLAG)).orElse("");
        // 移除分账信息
        if(productFlag.contains(ProductFlagEnum.HBFQ_DISCOUNT.getCode())) {
            Map<String, Object> payExtraParams = MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS, new HashMap<>());
            Map<String, Object> payProfitSharing = MapUtil.getMap(payExtraParams, Transaction.PROFIT_SHARING, new HashMap<String, Object>());
            List<Map<String, Object>> payReceivers = Optional.ofNullable((List<Map<String, Object>>)payProfitSharing.get(ProfitSharing.RECEIVERS)).orElse(new ArrayList<>());
            if(!payReceivers.isEmpty()) {
                payReceivers = payReceivers.stream().filter(r -> !ProductFlagEnum.HBFQ_DISCOUNT.getCode().equals(MapUtil.getString(r, ProfitSharing.RECEIVER_PRODUCT_FLAG))).collect(Collectors.toList());
                payProfitSharing.put(ProfitSharing.RECEIVERS, payReceivers);
            }
            if(payReceivers.isEmpty()) {
                payExtraParams.remove(Transaction.PROFIT_SHARING);
            }else {
                payProfitSharing.put(ProfitSharing.MODEL_ID, payReceivers.stream().map(reciver -> MapUtil.getString(reciver, ProfitSharing.MODEL_ID)).collect(Collectors.joining(",")));
            }

            // 变更花呗手续费相关信息
            Map<String, Object> extendedParams = MapUtil.getMap(payTransaction, Transaction.EXTENDED_PARAMS, new HashMap<>());
            Map<String, Object> extraOutFields = MapUtil.getMap(payTransaction, Transaction.EXTRA_OUT_FIELDS);
            if(MapUtil.getBooleanValue(extraOutFields, TransactionParam.HB_FQ, false)) {
                Map<String, Object> extendParams = MapUtil.getMap(extendedParams, BusinessV2Fields.EXTEND_PARAMS, new HashMap<>());
                extendedParams.put(BusinessV2Fields.EXTEND_PARAMS, extendParams);
                int hbfqNum = MapUtil.getIntValue(extendParams, BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM);
                extendParams.put(TransactionParam.HB_FQ_SELLER_PERCENT, 0);
                int fqType = UpayUtil.getFqTypeByProductFlag(productFlag);
                payExtraParams.put(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, FeeUtil.applyRate(MapUtil.getLongValue(payTransaction, Transaction.EFFECTIVE_AMOUNT), ApolloConfigurationCenterUtil.getBuyerServiceChargeFee(hbfqNum, fqType)));
                payExtraParams.put(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, 0);
            }
            productFlag = StringUtils.stringRemove(productFlag, ",", ProductFlagEnum.HBFQ_DISCOUNT.getCode());
            Map<String, Object> updatePart = MapUtil.hashMap(DaoConstants.ID, payTransaction.get(DaoConstants.ID),
                    Transaction.MERCHANT_ID, payTransaction.get(Transaction.MERCHANT_ID),
                    Transaction.EXTRA_PARAMS, payExtraParams,
                    Transaction.EXTENDED_PARAMS, extendedParams,
                    Transaction.PRODUCT_FLAG, productFlag,
                    DaoConstants.VERSION, payTransaction.get(DaoConstants.VERSION)
            );
            dataRepository.getTransactionDao().updatePart(updatePart);
            payTransaction.putAll(updatePart);
        }
        executor.submit(() ->{
            TimingStrategy.Builder builder = new TimingStrategy.Builder()
                    .setRetry(3, 2000, 2.0)
                    .setJitter(true, 200);
            RetryUtil<Boolean> retryUtil = new RetryUtil<Boolean>()
                    .retry(builder.build())
                    .method(() -> {
                        // 重新进行分账
                        sharingService.manualSharingByUpayTransaction(payTransaction);
                        return true;
                    })
                    .on(throwable -> !com.wosai.profit.sharing.constant.ErrorMessageConstant.UPAY_TRANSACTION_NOT_NEED_SHARING.equals(throwable.getMessage()))
                    .until(succeeded -> succeeded != null && succeeded);
            retryUtil.execute();
        });

        // 更新es汇总（用于报表展示）
        facade.reAnalyzeTrade(payTransaction);
    }

    /**
     * 根据终端号和payway查询交易配置
     *
     * @param request
     * @return
     */
    private Map<String, Object> getTradeConfigByTerminalSnAndPayway(Map<String, Object> request) {
        String terminalSn = MapUtils.getString(request, TERMINAL_SN);
        //获取payway
        Integer payway = MapUtils.getInteger(request, UpayService.PAYWAY);
        if (null == payway) {
            logger.error("根据终端号查询交易配置, payway不能为空, terminalSn={}", terminalSn);
            throw new ValidationException("支付方式payway不可为空");
        }
        Integer subPayway = MapUtils.getInteger(request, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_MINI);
        String tradeApp = MapUtils.getString(request, UpayService.TRADE_APP, TransactionParam.TRADE_APP_BASIC_PAY);
        return facade.getAllParamsWithTradeApp(null, terminalSn, payway, subPayway, tradeApp);
    }

    /**
     * 根据产品编码查询对应的通道
     *
     * @param request
     * @return
     */
    private List<ProviderAuthService> getProviderAuthServiceByProductCode(Map<String, Object> request) {
        String productCode = MapUtils.getString(request, Transaction.SQB_PRODUCT_CODE);
        if (StringUtils.isEmpty(productCode)) {
            return Collections.emptyList();
        }

        return providerAuthServiceMap.get(productCode);
    }

    private ProviderAuthService getMatchedProviderAuthService(List<ProviderAuthService> providerAuthServices, Map<String, Object> request, Map<String, Object> tradeConfig) {
        for (ProviderAuthService providerAuthService : providerAuthServices) {
            if (providerAuthService.isMatch(request, tradeConfig)) {
                return providerAuthService;
            }
        }
        String terminalSn = MapUtils.getString(request, TERMINAL_SN);
        Integer payway = MapUtils.getInteger(request, UpayService.PAYWAY);
        Integer subPayway = MapUtils.getInteger(request, UpayService.SUB_PAYWAY);
        logger.error("根据productCode查询授权服务, 未匹配到对应通道, terminalSn={}, payway={}, subPayway={}", terminalSn, payway, subPayway);
        throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
    }

    @Trace
    public Map<String, Object> auth(Map<String, Object> request) {
        //优先根据产品编码来匹配通道
        List<ProviderAuthService> providerAuthServices = getProviderAuthServiceByProductCode(request);
        if (CollectionUtils.isNotEmpty(providerAuthServices)) {
            //命中通道，直接处理
            Map<String, Object> tradeConfig = getTradeConfigByTerminalSnAndPayway(request);
            AuthResponse response = getMatchedProviderAuthService(providerAuthServices, request, tradeConfig)
                    .auth(request, tradeConfig);
            return response.isSuccess()
                    ? UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, response.getData()))
                    : UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, response.getCode(), response.getMessage(), null));
        }

        if (StringUtil.isBlank(MapUtils.getString(request, PAYER_UID))) {
            throw new ValidationException("付款人id不可为空");
        }

        String terminalSn= BeanUtil.getPropString(request, TERMINAL_SN);
        //获取payway, 默认为微信支付方式
        int payway = BeanUtil.getPropInt(request, UpayService.PAYWAY, Order.PAYWAY_WEIXIN);
        String subPayway = BeanUtil.getPropString(request, UpayService.SUB_PAYWAY);
        int subPaywayCode = (subPayway == null ? Order.SUB_PAYWAY_MINI : Integer.parseInt(subPayway));
        // 当前只支持小程序
        if (Order.SUB_PAYWAY_MINI != subPaywayCode) {
            throw new InvalidParamException(UpayErrorScenesConstant.UPAY_AUTH_SUB_PAYWAY_ERROR_MESSAGE);
        }
        Map<String, Object> config = facade.getAllParamsWithTradeApp(null, terminalSn, payway, subPaywayCode, TransactionParam.TRADE_APP_BASIC_PAY);
        Map<String, Object> extended = formatExtended(request, MpayServiceProvider.OP_DEPOSIT_AUTH_APPLY);

        Map<String, Object> result;
        String paramsName = Order.PAYWAY_WEIXIN == payway ? TransactionParam.WEIXIN_MINI_TRADE_PARAMS : TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS;
        // 当前只支持微信支付分 和 支付宝芝麻先享
        if(!config.containsKey(paramsName)){
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        Map<String, Object> tradeParams = MapUtil.getMap(config, paramsName);
        if (Order.PAYWAY_WEIXIN == payway) {
            DirectWeixinV3PartnerPreDepositServiceProvider provider = workflowManager.getServiceProvider(DirectWeixinV3PartnerPreDepositServiceProvider.class);
            result = provider.depositAuthApply(tradeParams, extended);

            int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
            if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
                Map<String, Object> applyResult = new HashMap<>();
                //授权token, 用于前端授权
                applyResult.put(ResponseFields.APPLY_PERMISSIONS_TOKEN, BeanUtil.getPropString(result, ResponseFields.APPLY_PERMISSIONS_TOKEN));
                //授权协议号
                applyResult.put(BusinessFields.AUTHORIZATION_CODE, BeanUtil.getPropString(result, BusinessFields.AUTHORIZATION_CODE));
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, applyResult));
            } else {
                String errorCode = BeanUtil.getPropString(result, ResponseFields.CODE);
                String errorMessage = BeanUtil.getPropString(result, ResponseFields.MESSAGE);

                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, errorCode, errorMessage, null));
            }
        } else {
            DirectAlipayV2PreDepositServiceProvider provider = workflowManager.getServiceProvider(DirectAlipayV2PreDepositServiceProvider.class);
            result = provider.depositAuthApply(tradeParams, extended);
            Map<String, Object> applyResult = new HashMap<>();
            //授权token, 用于前端授权
            applyResult.put(ResponseFields.APPLY_PERMISSIONS_TOKEN, BeanUtil.getPropString(result, ResponseFields.APPLY_PERMISSIONS_TOKEN));
            //授权协议号
            applyResult.put(BusinessFields.AUTHORIZATION_CODE, BeanUtil.getPropString(result, BusinessFields.AUTHORIZATION_CODE));
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, applyResult));
        }
    }

    @Override
    @Trace
    public Map<String, Object> authQuery(Map<String, Object> request) {
        //优先根据产品编码来匹配通道
        List<ProviderAuthService> providerAuthServices = getProviderAuthServiceByProductCode(request);
        if (CollectionUtils.isNotEmpty(providerAuthServices)) {
            //命中通道，直接处理
            Map<String, Object> tradeConfig = getTradeConfigByTerminalSnAndPayway(request);
            AuthResponse response = getMatchedProviderAuthService(providerAuthServices, request, tradeConfig)
                    .authQuery(request, tradeConfig);
            return response.isSuccess()
                    ? UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, response.getData()))
                    : UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, response.getCode(), response.getMessage(), null));
        }

        String authCode = MapUtils.getString(request, AUTHORIZATION_CODE);
        String payerUid = MapUtils.getString(request, PAYER_UID);
        if (StringUtil.isBlank(authCode) && StringUtil.isBlank(payerUid)) {
            throw new ValidationException("授权协议号和付款人id不能同时为空");
        }

        String terminalSn= BeanUtil.getPropString(request, TERMINAL_SN);
        //获取payway, 默认为微信支付方式
        int payway = BeanUtil.getPropInt(request, UpayService.PAYWAY, Order.PAYWAY_WEIXIN);
        String subPayway = BeanUtil.getPropString(request, UpayService.SUB_PAYWAY);
        int subPaywayCode = (subPayway == null ? Order.SUB_PAYWAY_MINI : Integer.parseInt(subPayway));
        Map<String, Object> config = facade.getAllParamsWithTradeApp(null, terminalSn, payway, subPaywayCode, TransactionParam.TRADE_APP_BASIC_PAY);
        Map<String, Object> extended = formatExtended(request, MpayServiceProvider.OP_DEPOSIT_AUTH_QUERY);

        Map<String, Object> result;
        String paramsName = Order.PAYWAY_WEIXIN == payway ? TransactionParam.WEIXIN_MINI_TRADE_PARAMS : TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS;
        // 当前只支持微信支付分 和 支付宝芝麻先享
        if(!config.containsKey(paramsName)){
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        Map<String, Object> tradeParams = MapUtil.getMap(config, paramsName);
        if (Order.PAYWAY_WEIXIN == payway) {
            DirectWeixinV3PartnerPreDepositServiceProvider provider = workflowManager.getServiceProvider(DirectWeixinV3PartnerPreDepositServiceProvider.class);
            result = provider.depositAuthQuery(tradeParams, extended);

            int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
            if (HttpConstant.HTTP_CODE_SUCCESS == httpCode) {
                Map<String, Object> queryResult = (Map<String, Object>) BeanUtil.makeCopy(result);
                queryResult.remove(HttpConstant.HTTP_CODE);
                queryResult.put(ResponseFields.AUTH_STATUS, true);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, queryResult));
            } else {
                String errorCode = BeanUtil.getPropString(result, ResponseFields.CODE);
                String errorMessage = BeanUtil.getPropString(result, ResponseFields.MESSAGE);

                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, errorCode, errorMessage, null));
            }
        } else {
            DirectAlipayV2PreDepositServiceProvider provider = workflowManager.getServiceProvider(DirectAlipayV2PreDepositServiceProvider.class);
            result = provider.depositAuthQuery(tradeParams, extended);

            String returnCode = BeanUtil.getPropString(result, BusinessV2Fields.CODE);//返回状态码
            if (AlipayConstants.V2_RETURN_CODE_SUCCESS.equals(returnCode)) {
                Map<String, Object> queryResult = (Map<String, Object>) BeanUtil.makeCopy(result);
                String agreementStatus = BeanUtil.getPropString(result, BusinessV2Fields.AGREEMENT_STATUS);// 返回授权状态
                boolean authStatus = AlipayConstants.AGREEMENT_STATUS_VALID.equals(agreementStatus);
                queryResult.put(ResponseFields.AUTH_STATUS, authStatus);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, queryResult));
            } else {
                String errorCode = BeanUtil.getPropString(result, BusinessV2Fields.SUB_CODE);
                String errorMessage = BeanUtil.getPropString(result, BusinessV2Fields.SUB_MSG);

                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, errorCode, errorMessage, null));
            }
        }
    }

    @Override
    @Trace
    public Map<String, Object> authTerminate(Map<String, Object> request) {
        //优先根据产品编码来匹配通道
        List<ProviderAuthService> providerAuthServices = getProviderAuthServiceByProductCode(request);
        if (CollectionUtils.isNotEmpty(providerAuthServices)) {
            //命中通道，直接处理
            Map<String, Object> tradeConfig = getTradeConfigByTerminalSnAndPayway(request);
            AuthResponse response = getMatchedProviderAuthService(providerAuthServices, request, tradeConfig)
                    .authTerminate(request, tradeConfig);
            return response.isSuccess()
                    ? UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, response.getData()))
                    : UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, response.getCode(), response.getMessage(), null));
        }

        String authCode = MapUtils.getString(request, AUTHORIZATION_CODE);
        String payerUid = MapUtils.getString(request, PAYER_UID);
        if (StringUtil.isBlank(authCode) && StringUtil.isBlank(payerUid)) {
            throw new ValidationException("授权协议号和付款人id不能同时为空");
        }
        if (StringUtil.isBlank(MapUtils.getString(request, REASON))) {
            throw new ValidationException("解绑原因不能为空");
        }
        String terminalSn= (String)request.get(TERMINAL_SN);
        String subPayway = (String)request.get(UpayService.SUB_PAYWAY);
        int subPaywayCode = (subPayway == null ? Order.SUB_PAYWAY_MINI : Integer.parseInt(subPayway));
        Map<String, Object> config = facade.getAllParamsWithTradeApp(null, terminalSn, Order.PAYWAY_WEIXIN, subPaywayCode, TransactionParam.TRADE_APP_BASIC_PAY);
        Map<String, Object> extended = formatExtended(request, MpayServiceProvider.OP_DEPOSIT_AUTH_TERMINATE);

        Map<String, Object> result;
        // 当前只支持微信支付分
        if(!config.containsKey(TransactionParam.WEIXIN_MINI_TRADE_PARAMS)){
            throw new UpayBizException(UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.INVALID_MERCHANT_CONFIG_ERROR_MESSAGE);
        }
        Map<String, Object> tradeParams = MapUtil.getMap(config, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
        DirectWeixinV3PartnerPreDepositServiceProvider provider = workflowManager.getServiceProvider(DirectWeixinV3PartnerPreDepositServiceProvider.class);
        result = provider.depositAuthTerminate(tradeParams, extended);

        int httpCode = BeanUtil.getPropInt(result, HttpConstant.HTTP_CODE);
        if (HttpConstant.HTTP_CODE_SUCCESS_WITHOUT_RESPONSE == httpCode) {
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, null));
        } else {
            String errorCode = BeanUtil.getPropString(result, ResponseFields.CODE);
            String errorMessage = BeanUtil.getPropString(result, ResponseFields.MESSAGE);

            return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.FAIL, errorCode, errorMessage, null));
        }
    }

    private Map<String, Object> formatExtended(Map<String, Object> request, String op){
        Map<String, Object> extended = new HashMap<>();
        int payway = BeanUtil.getPropInt(request, UpayService.PAYWAY, Order.PAYWAY_WEIXIN);
        switch (op) {
            case MpayServiceProvider.OP_DEPOSIT_AUTH_APPLY:
                copyAuthOverParams(request, payway, extended, Order.PAYWAY_WEIXIN == payway ? WeixinConstants.DEPOSIT_AUTH_APPLY_ALLOWED_FIELDS : AlipayConstants.DEPOSIT_AUTH_APPLY_ALLOWED_FIELDS);
                break;
            case MpayServiceProvider.OP_DEPOSIT_AUTH_QUERY:
                copyAuthOverParams(request, payway, extended, Order.PAYWAY_WEIXIN == payway ? WeixinConstants.DEPOSIT_AUTH_QUERY_ALLOWED_FIELDS : AlipayConstants.DEPOSIT_AUTH_QUERY_ALLOWED_FIELDS);
                break;
            case MpayServiceProvider.OP_DEPOSIT_AUTH_TERMINATE:
                copyAuthOverParams(request, payway, extended, WeixinConstants.DEPOSIT_AUTH_TERMINATE_ALLOWED_FIELDS);
                break;
            default:
                throw new UpayBizException("error");

        }

        return extended;
    }

    private void copyAuthOverParams(Map<String, Object> request, int payway,  Map<String, Object> extended, Set<String> allowedFields) {
        if(request == null || request.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Object> requestParam: request.entrySet()) {
            String key = requestParam.getKey();
            if (PAYER_UID.equals(key) && Order.PAYWAY_WEIXIN == payway){
                key = SUB_OPENID; //微信支付方式 字段转换 payer_uid => sub_openid
            }
            if(allowedFields != null && allowedFields.size() > 0 && !allowedFields.contains(key)){
                continue;
            }
            Object value = requestParam.getValue();
            if (value != null) {
                extended.put(key, value);
            }
        }
    }

    @Override
    public Map<String, Object> changeTradeProfitSharing(Map<String, Object> body) {
        String merchantId = MapUtil.getString(body, MERCHANT_ID);
        String tsn = MapUtil.getString(body, Transaction.TSN);
        Map<String, Object> transaction = dataRepository.getTransactionByTsn(merchantId, tsn);
        if(Objects.isNull(transaction)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.TRANSACTION_NOT_EXIST, UpayErrorScenesConstant.TRANSACTION_NOT_EXIST_MESSAGE);
        }
        if(Transaction.STATUS_SUCCESS != MapUtil.getIntValue(transaction, Transaction.STATUS)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }
        Map<String, Object> extra = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        Map<String, Object> changeProfitShaing = MapUtil.getMap(body, PROFIT_SHARING);
        if(changeProfitShaing == null){
            extra.remove(Transaction.PROFIT_SHARING);
        }else {
            extra.put(Transaction.PROFIT_SHARING, changeProfitShaing);
        }
        Map<String, Object> updatePart = MapUtil.hashMap(DaoConstants.ID, transaction.get(DaoConstants.ID),
                Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                Transaction.EXTRA_PARAMS, extra,
                DaoConstants.VERSION, transaction.get(DaoConstants.VERSION)
        );
        dataRepository.getTransactionDao().updatePart(updatePart);

        // 更新es汇总（用于报表展示）
        facade.reAnalyzeTrade(transaction);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, dataRepository.getTransactionDao().get(MapUtil.getString(transaction, DaoConstants.ID))));
    }

    @Override
    public Map<String, Object> queryHbfqNum(Map<String, Object> request) {
        String terminalSn = MapUtil.getString(request, TERMINAL_SN);
        String wosaiStoreId = MapUtil.getString(request, WOSAI_STORE_ID);
        String sn = MapUtil.getString(request, SN);
        String clientSn= MapUtil.getString(request, CLIENT_SN);

        Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), "query_hbfq_num", Boolean.TRUE);
        String merchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
        Map<String, Object> order = dataRepository.getOrder(merchantId, null, sn, clientSn);
        boolean inDB = true;
        if (order == null) {
            inDB = false;
            order = gatewaySupportService.getOrderBySn(merchantId, clientSn, sn, DataPartitionConst.RECENT_6M);
        }
        if (order == null) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        int payway = MapUtil.getIntValue(order, Order.PAYWAY);
        if (payway != Order.PAYWAY_ALIPAY2) {
            throw new UpayBizException(UpayErrorScenesConstant.QUERY_HBFQ_NUM_NOT_SUPPORT, UpayErrorScenesConstant.QUERY_HBFQ_NUM_NOT_SUPPORT_MESSAGE);
        }
        sn = MapUtil.getString(order, Order.SN);
        Map<String, Object> payTransaction = null;
        if (inDB) {
            payTransaction = dataRepository.getPayTransactionByOrderSn(merchantId, sn);
        } else {
            payTransaction = gatewaySupportService.getPayOrConsumerTransaction(merchantId, sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        if (payTransaction == null) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        if (MapUtil.getIntValue(payTransaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
            throw new UpayBizException(UpayErrorScenesConstant.QUERY_HBFQ_NUM_NOT_SUPPORT, UpayErrorScenesConstant.QUERY_HBFQ_NUM_NOT_SUPPORT_MESSAGE);
        }
        int provider = MapUtil.getIntValue(payTransaction, Transaction.PROVIDER);
        int hbfqNum = 0;
        int hbfqSellerPercent = 0;
        // 1分钟之前的完成交易（已确定分期数）或1分钟之内的完成交易且不支持分期数查询时，使用流水中的分期数
        List<Integer> supportQueryProviders = Arrays.asList(0, Order.PROVIDER_DIRECT_UNIONPAY, Order.PROVIDER_TL, Order.PROVIDER_LAKALA_UNION_PAY);
        if (!supportQueryProviders.contains(provider) || MapUtil.getLongValue(payTransaction, Transaction.FINISH_TIME) < System.currentTimeMillis() - 60 * 1000) {
            Map<String, Object> extendedParams = MapUtil.getMap(payTransaction, Transaction.EXTENDED_PARAMS);
            Map<String, Object> alipayExtendParams = MapUtil.getMap(extendedParams, BusinessV2Fields.EXTEND_PARAMS);
            hbfqNum = MapUtil.getIntValue(alipayExtendParams, BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM);
            hbfqSellerPercent = MapUtil.getIntValue(alipayExtendParams, BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT);
        } else {
            throw new UpayBizException(UpayErrorScenesConstant.QUERY_HBFQ_NUM_NOT_SUPPORT, UpayErrorScenesConstant.QUERY_HBFQ_NUM_NOT_SUPPORT_MESSAGE);
        }
        Map<String, Object> result = MapUtil.hashMap(TransactionParam.HB_FQ_NUM, hbfqNum);
        if(hbfqNum > 0) {
            result .put(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT, hbfqSellerPercent);
        }
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, result));
    }

    /**
     * 索迪斯-支付
     */
    @Override
    public Map<String, Object> sodexoExpenseByToken(Map<String, Object> request) {
        RateLimiterUtil.verification("all", "sodexoExpenseByToken");
        String userId = MapUtil.getString(request, USER_ID);
        if (Objects.isNull(userId)) {
            throw new UpayBizException("用户ID不存在");
        }
        UcUserDTO userInfo = externalServiceFacade.findUserInfo(userId);
        if (Objects.isNull(userInfo)) {
            throw new UpayBizException("用户信息不存在");
        }
        String mobileNumber = userInfo.getCellphone();
        if (org.apache.commons.lang3.StringUtils.isBlank(mobileNumber)) {
            throw new UpayBizException("用户信息内手机号为空");
        }
        String merchantId = MapUtils.getString(request, MERCHANT_ID);
        String clientSn = MapUtils.getString(request, CLIENT_SN);
        Map<String, Object> wapPayRequestMap = MapUtils.getMap(request, WAP_PAY_REQUEST);
        String encryptData = MapUtils.getString(wapPayRequestMap, Transaction.ENCRYPT_DATA);
        if (StringUtil.isBlank(encryptData)) {
            throw new UpayBizException("加密数据为空");
        }
        // 支付
        String orderSn = sodexoWapServiceProvider.expenseByToken(mobileNumber, encryptData, merchantId, clientSn);
        Map<String, Object> result = ImmutableMap.of(
                PAYER_UID, userId,
                ORDER_SN, orderSn
        );
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, result));
    }

    /**
     * 索迪斯-余额查询
     */
    @Override
    public Map<String, Object> sodexoBalanceQuery(Map<String, Object> request) {
        RateLimiterUtil.verification("all", "sodexoBalanceQuery");
        String userId = MapUtils.getString(request, USER_ID);
        UcUserDTO userInfo = externalServiceFacade.findUserInfo(userId);
        if (Objects.isNull(userInfo)) {
            throw new UpayBizException("用户信息不存在");
        }
        String mobileNumber = userInfo.getCellphone();
        if (StringUtil.isBlank(mobileNumber)) {
            throw new UpayBizException("用户信息内手机号为空");
        }

        String storeSn = MapUtils.getString(request, STORE_SN);
        String terminalSn = MapUtil.getString(request, TERMINAL_SN);
        Map<String, Object> tradeParams = externalServiceFacade
                .getAllParamsWithTradeApp(storeSn, terminalSn, Payway.SODEXO.getCode(), SubPayway.MINI.getCode(), null);
        tradeParams = MapUtil.getMap(tradeParams, TransactionParam.SODEXO_TRADE_PARAMS);
        if (MapUtil.isEmpty(tradeParams)) {
            throw new UpayBizException(UpayErrorScenesConstant.UPAY_MERCHANT_NOT_EXISTS_MESSAGE);
        }
        String totalBalance = sodexoWapServiceProvider.queryBalance(mobileNumber, tradeParams);
        Map<String, Object> result = ImmutableMap.of(
                TOTAL_BALANCE, totalBalance,
                USER_ID, userId
        );
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, result));
    }

    @Override
    public Map<String, Object> getAlipayHbfqShareCode(Map<String, Object> request) {
        String terminalSn = MapUtil.getString(request, TERMINAL_SN);
        Map<String, Object> result = directAlipayV2ServiceProvider.getShareCode(terminalSn, request);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, result));
    }

    /**
     * 富友手续费更新并同步余额接口
     *
     * @param request
     * @return
     */
    @Override
    public Boolean updateFuyouTradeFeeSyncWallet(Map<String, Object> request) {
        return fuyouTradeFeeSyncWalletProcessor.execute(request);
    }

    /**
     * 富友同步es与分账
     *
     * @param request
     * @return
     */
    @Override
    public Boolean syncFuyouAnalyzeTradeAndSharing(Map<String, Object> request) {
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        String merchantId = BeanUtil.getPropString(request, MERCHANT_ID);
        if (StringUtils.isEmpty(tsn)) {
            return true;
        }
        // 设置富友手续费
        Map<String, Object> transaction = dataRepository.getTransactionByTsn(merchantId, tsn);
        if (MapUtil.isEmpty(transaction)) {
            return true;
        }
        Map<String, Object> configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        Map extraParamsMap = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        boolean needSyncBrandSettle = StringUtil.isNotEmpty(MapUtil.getString(configSnapshot, TransactionParam.SFT_BRAND_ID));
        boolean needSyncProfitSharing = !needSyncBrandSettle && (extraParamsMap != null && extraParamsMap.containsKey(Transaction.PROFIT_SHARING));

        TimingStrategy.Builder builder = new TimingStrategy.Builder()
                .setRetry(3, 1000, 2.0)
                .setJitter(true, 200);
        RetryUtil<Boolean> retryUtil = new RetryUtil<Boolean>()
                .retry(builder.build())
                .method(() -> {
                    // 更新es汇总（用于报表展示）
                    facade.reAnalyzeTrade(transaction);
                    if (needSyncBrandSettle) {
                        facade.notifyBrandSettleFeeChange(transaction);
                    }
                    if (needSyncProfitSharing) {
                        sharingService.notifyFeeAndResumeSharingTransaction(transaction);
                    }
                    return true;
                })
                .on(throwable -> true)
                .until(succeeded -> succeeded != null && succeeded);
        return retryUtil.execute();
    }

    @Override
    public Map<String, Object> finishQuotaTransaction(Map<String, Object> request) {
        return finishQuotaTransactionProcessor.execute(request);
    }

    @Override
    public Map<String, Object> depositChangeReflect(Map<String, Object> request) {
        String merchantId = MapUtil.getString(request, MERCHANT_ID);
        String orderSn = MapUtil.getString(request, ORDER_SN);
        Map<String, Object> orderInfo = dataRepository.getOrderByOrderSn(merchantId, orderSn);
        int status = BeanUtil.getPropInt(orderInfo, Order.STATUS);
        if (status != Order.STATUS_DEPOSIT_FREEZED) {
            throw new UpayBizException("只可补录预授权成功交易流水");
        }
        // "reflect":"{\"type\":\"deposit\",\"cellphone\":\"13000383539\",\"remark\":\"店小二\"}"
        Map<String, Object> reflectMap;
        Map<String, Object> transaction = dataRepository.getFreezeTransactionByOrderSn(merchantId, orderSn);
        String reflect = BeanUtil.getPropString(transaction, Transaction.REFLECT);
        if (org.apache.commons.lang3.StringUtils.contains(reflect, Transaction.REFLECT_TYPE)) {
            reflectMap = JacksonUtil.toBeanQuietly(reflect, Map.class);
            if (MapUtil.isEmpty(reflectMap)) {
                reflectMap = new HashMap<>();
                reflectMap.put(Transaction.REFLECT_TYPE, TransactionParam.DEPOSIT);
            }
        } else {
            reflectMap = new HashMap<>();
            reflectMap.put(Transaction.REFLECT_TYPE, TransactionParam.DEPOSIT);
            if (StringUtil.isNotEmpty(reflect)) {
                reflectMap.put(Transaction.REFLECT_DEPOSIT_REMARK, reflect);
            }
        }
        String remark = BeanUtil.getPropString(request, Transaction.REFLECT_DEPOSIT_REMARK);
        String cellphone = BeanUtil.getPropString(request, Transaction.REFLECT_DEPOSIT_CELLPHONE);
        if (StringUtil.isNotEmpty(remark)) {
            BeanUtil.setProperty(reflectMap, Transaction.REFLECT_DEPOSIT_REMARK, remark);
        }
        if (StringUtil.isNotEmpty(cellphone)) {
            cellphone = cryptoService.encryptDepositCellphone(cellphone);
            BeanUtil.setProperty(reflectMap, Transaction.REFLECT_DEPOSIT_CELLPHONE, cellphone);
        }
        Map<String, Object> updatePart = MapUtil.hashMap(DaoConstants.ID, transaction.get(DaoConstants.ID),
                Transaction.REFLECT, JacksonUtil.toJsonString(reflectMap),
                DaoConstants.MTIME, System.currentTimeMillis(),
                DaoConstants.VERSION, transaction.get(DaoConstants.VERSION)
        );
        dataRepository.getTransactionDao().updatePart(updatePart);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, updatePart));
    }

    @Override
    public String getTnFromZJTLEncryptBody(String encryptBody) {
        ZJTLCBServiceProvider provider=workflowManager.getServiceProvider(ZJTLCBServiceProvider.class);
        return provider.getOrderSn(encryptBody);
    }

    @Override
    public Map<String, Object> changeTradeFee(Map<String, Object> request) {
        String terminalSn = (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String clientSn= (String)request.get(CLIENT_SN);
        String sn = (String)request.get(SN);
        String tsn = (String)request.get(UpayService.TSN);

        String feeRate = (String) request.get(TransactionParam.FEE_RATE);
        if (!ApolloConfigurationCenterUtil.getSupportChangeFeeFlag()) {
            throw new UpayBizException("不支持的业务操作");
        }
        long feeRateCents = StringUtils.yuan2cents(feeRate);
        if (feeRateCents < 0 || feeRateCents > 10000) {
            throw new UpayBizException("费率参数异常");
        }
        Long fee = MapUtil.getLong(request, TransactionParam.FEE);
        Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        String storeId = BeanUtil.getPropString(basicParams, TransactionParam.STORE_ID);
        String merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        Map<String,Object> order = null;
        Map<String, Object> transaction = null;
        boolean isDBTrade = true;
        // 查数据库
        if(order == null){
            order = dataRepository.getOrder(merchantId, storeId, sn, clientSn);
        }
        //查hbase
        if(order == null){
            order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.RECENT_6M);
            if(null == order) {
                order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.COLD);
            }
            isDBTrade = false;
        }
        if(order == null){
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        String orderSn = (String)order.get(Order.SN);

        if(isDBTrade) {
            transaction = dataRepository.getTransactionByTsn(merchantId, tsn);
        } else {
            List<Map<String, Object>> transactionListByOrderSn = upayOrderService.getOriginalTransactionListByOrderSn(orderSn);
            Optional<Map<String, Object>> optional = transactionListByOrderSn.stream().filter(o -> Objects.equals(tsn, MapUtil.getString(o, Transaction.TSN))).findAny();
            if (optional.isPresent()) {
                transaction = optional.get();
            }
        }
        if(null == transaction){
            throw new OrderNotExistsException(UpayErrorScenesConstant.TRANSACTION_NOT_EXIST, UpayErrorScenesConstant.TRANSACTION_NOT_EXIST_MESSAGE);
        }
        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        if (provider == null) {
            throw new UpayBizException("交易参数异常");
        }
        Map<String, Object> tradeParams = provider.getTradeParams(transaction);
        tradeParams.put(TransactionParam.FEE_RATE, feeRate);
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (fee == null) {
            if (type == Transaction.TYPE_PAYMENT) {
                fee = FeeUtil.reCalculatePayOrPrecreateFee(transaction);
            } else if (type == Transaction.TYPE_REFUND || type == Transaction.TYPE_CANCEL) {
                fee = FeeUtil.calculateHistoryRefundFee(dataRepository, transaction, gatewaySupportService, MapUtil.getLongValue(order, DaoConstants.CTIME), true);
            } else {
                throw new UpayBizException("不支持的流水类型");
            }
        }
        tradeParams.put(TransactionParam.FEE, fee);
        if (isDBTrade) {
            //drds根据merchant_id分区
            Map<String, Object> updatePart = MapUtil.copyInclusive(transaction, DaoConstants.ID, Transaction.MERCHANT_ID, DaoConstants.VERSION, Transaction.CONFIG_SNAPSHOT);
            updatePart.put(DaoConstants.MTIME, System.currentTimeMillis());
            dataRepository.getTransactionDao().updatePart(updatePart);
            // 更新es汇总（用于报表展示）
            facade.reAnalyzeTrade(transaction);
        } else {
            //更新hbase里数据（报表汇总会存在问题）
            Map<String, Object> extraOut = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
            extraOut.remove(Transaction.IN_HBASE);
            transaction.put(DaoConstants.MTIME, System.currentTimeMillis());
            transaction.put(DaoConstants.VERSION, MapUtil.getIntValue(transaction, DaoConstants.VERSION) + 1);
            boolean updateSuccess = upayOrderService.updateTransaction(transaction);
            if(!updateSuccess){
                throw new UpayBizException("更新异常");
            }
        }
        return transaction;
    }

    @Override
    public String getTnFromSPDBEncryptBody(String encryptBody){
        SPDBServiceProvider provider=workflowManager.getServiceProvider(SPDBServiceProvider.class);
        return provider.getOrderSn(encryptBody);
    }


    @Override
    @Trace
    public DeferredResult<Map<String, Object>> confirmPay(Map<String, Object> request) {
        //限流控制
        RateLimiterUtil.verification("all", MpayServiceProvider.OP_CONFIRM_PAY);

        DeferredResult<Map<String, Object>> deferredResult = new DeferredResult<>();
        confirmPayThreadPool.execute(RunnableWrapper.of(() -> {
            Map<String, Object> result;
            try {
                result = doPay(request);
            } catch (Throwable e) {
                result = UpayServiceMethodInterceptor.handleException(e);
                deferredResult.setResult(result);
                return;
            }
            deferredResult.setResult(UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_CONFIRM_PAY_SUCCESS, null, null, null, result)));
        }));
        return deferredResult;
    }


    private Map<String, Object> doPay(Map<String, Object> request) {
        String merchantId = MapUtil.getString(request, MERCHANT_ID);
        String orderSn = MapUtil.getString(request, ORDER_SN);
        Map<String, Object> transaction = dataRepository.getPayTransactionByOrderSn(merchantId, orderSn);
        if (null == transaction) {
            logger.warn("confirmPay: 未查到交易流水日志, merchantId={}, orderSn={}", merchantId, orderSn);
            throw new UpayBizException(UPAY_PROVIDER_STATUS_LIMITING, "订单不存在");
        }

        //校验流水类型、订单状态等
        confirmPayPreCheck(merchantId, orderSn, transaction);

        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        if (provider == null) {
            logger.warn("confirmPay: 未查到provider实现类, merchantId={}, orderSn={}", merchantId, orderSn);
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE);
        }

        //确认支付
        return provider.confirmPay(request, transaction);
    }

    /**
     * 确认支付-前置校验
     *
     * @param transaction
     */
    private void confirmPayPreCheck(String merchantId, String orderSn, Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        if (Transaction.TYPE_PAYMENT != type) {
            logger.warn("confirmPay: 不是支付流水, merchantId={}, orderSn={}, type={}", merchantId, orderSn, type);
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "订单信息异常");
        }

        int status = MapUtil.getIntValue(transaction, Transaction.STATUS);
        if (status != Transaction.STATUS_CREATED && status != Transaction.STATUS_IN_PROG) {
            logger.warn("confirmPay: 订单状态异常, merchantId={}, orderSn={}, status={}", merchantId, orderSn, status);
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "订单状态异常");
        }

        //订单有效期设置为3分钟
        long now = System.currentTimeMillis();
        long end = MapUtil.getLongValue(transaction, DaoConstants.CTIME) + TimeUnit.MINUTES.toMillis(3);
        if (now > end) {
            throw new ProviderStatusException(UPAY_PROVIDER_STATUS_LIMITING, "订单处理超时，请重新操作");
        }
    }
}
