package com.wosai.upay.service;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import com.wosai.profit.sharing.util.UpayConfigCryptoUtil;
import com.wosai.upay.workflow.AbstractPayWorkflow;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.AvroBeanHelper;
import com.wosai.upay.util.FakeRequestUtil;

import net.logstash.logback.marker.Markers;

public class AmqpFacade {
    private static final Logger kafkaLogger = LoggerFactory.getLogger("com.wosai.upay.service.AmqpFacade.Kafka");
    private static final Set<Integer> NEED_PUSH_ORDER_TYPE = CollectionUtil.hashSet(Transaction.TYPE_REFUND, Transaction.TYPE_CANCEL,
            Transaction.TYPE_DEPOSIT_CANCEL, Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_REFUND_REVOKE);
    
    private int tradePartitions;
    private String topic;
    private Executor kafkaMessageSendExecutors = null;
    private int messageSendWaitMaxQueueSize;


    private KafkaProducer<byte[], byte[]> producer;

    public AmqpFacade(int messageSendWaitMaxQueueSize, int  messageSendExecutorConcurrency, String topic, int tradePartitions,Properties kafkaProps){
        this.messageSendWaitMaxQueueSize = messageSendWaitMaxQueueSize;
    	this.topic = topic;
    	this.tradePartitions = tradePartitions;
    	producer = new KafkaProducer<>(kafkaProps);
    	kafkaMessageSendExecutors = Executors.newFixedThreadPool(messageSendExecutorConcurrency);
    }
    
    @SuppressWarnings("unchecked")
    public void notifyScoreService(Map<String, Object> order, Map<String, Object> transaction) {
        Map<String, Object> clonedTransaction = clone(transaction);
        Map<String, Object> clonedOrder = clone(order);
        //多线程下可能又被设置超长值了， 容错，再次truncate buyer信息
        AbstractPayWorkflow.truncTransactionOrOrderBuyerInfo(clonedTransaction);
        AbstractPayWorkflow.truncTransactionOrOrderBuyerInfo(clonedOrder);
        UpayConfigCryptoUtil.encryptConfigSnapshotSensitiveInfo((Map<String, Object>) clonedTransaction.get(Transaction.CONFIG_SNAPSHOT));
        pushOrderInfoToTransaction(clonedOrder, clonedTransaction);
        send(clonedTransaction);
    }
    
    @SuppressWarnings("unchecked")
    public void errorTransactionNotify(Map<String,Object> order, Map<String, Object> transaction) {
        Map<String, Object> clonedTransaction = clone(transaction);
        UpayConfigCryptoUtil.encryptConfigSnapshotSensitiveInfo((Map<String, Object>) clonedTransaction.get(Transaction.CONFIG_SNAPSHOT));
        pushOrderInfoToTransaction(order, clonedTransaction);
        send(clonedTransaction);
    }

    /**
     * 注意，此方法只会深拷贝map类型的value, 其他对象还是浅拷贝
     * @param original
     * @return
     */
    public static Map<String, Object> clone(Map<String, Object> original) {
        if(original == null){
            return null;
        }
        Map<String,Object> target = new HashMap<String,Object>();
        synchronized (original){
            for(String key: original.keySet()){
                Object value = original.get(key);
                if(value instanceof Map){
                    target.put(key, clone((Map<String, Object>) value));
                }else{
                    target.put(key, value);
                }
            }
        }
        return target;
    }

    private void send(Map<String, Object> transaction){
        if(FakeRequestUtil.isFakeRequest()) return;
        asyncSend(transaction, topic, true);
    }
    
    private void asyncSend(final Map<String, Object> transaction, final String topic, boolean ifFailNeedResend) {
    	String merchantId = BeanUtil.getPropString(transaction, DaoConstants.ID);
        final int partition = getPartitionByMerchantId(merchantId, tradePartitions);
    	try {
			if (((ThreadPoolExecutor) kafkaMessageSendExecutors).getQueue().size() > messageSendWaitMaxQueueSize) {
				String transactionJson = JsonUtil.toJsonStr(transaction);
	        	kafkaLogger.error("so many message in queue, message will not be send!  {}, {}", topic , transactionJson);
                dumpToLog(topic, transactionJson);
	        }else{
	            ((ThreadPoolExecutor) kafkaMessageSendExecutors).submit(
	                    new Runnable() {
	                        @Override
	                        public void run() {
	                            try {
	                            	ProducerRecord record = new ProducerRecord(topic, partition, System.currentTimeMillis(), null, AvroBeanHelper.getTransactionBeanFromMap(transaction));
	                            	producer.send(record, new Callback() {
	                    				
	                    				@Override
	                    				public void onCompletion(RecordMetadata metadata, Exception exception) {
	                    					if(null != exception){
	                    						String transactionJson = JsonUtil.toJsonStr(transaction);
	                    						kafkaLogger.error("failed to send message: " + topic + " " + transactionJson, exception);
	                    						dumpToLog(topic, transactionJson);
	                    					}
	                    				}
	                    			});
	                            } catch (Exception e) {
	                            	String transactionJson = JsonUtil.toJsonStr(transaction);
	                            	kafkaLogger.error("failed to send message: " + topic + " " + transactionJson, e);
	                                dumpToLog(topic, transactionJson);
	                            }
	                        }
	                    }
	            );
	        }
        } catch (Exception e) {
        	String transactionJson = JsonUtil.toJsonStr(transaction);
        	kafkaLogger.error("failed to send message: " + topic + " " + transactionJson, e);
            dumpToLog(topic, transactionJson);
        }
    }
    

    private int getPartitionByMerchantId(String merchantId, int tradePartitions){
        return Math.abs(merchantId.hashCode() % tradePartitions);
    }
    
    private void dumpToLog(String topic, Object message) {
        kafkaLogger.info(Markers.appendEntries(CollectionUtil.hashMap(
                "topic", topic,
                "original_message", message
        )), "message need to resend");
    }
    
    
    private void pushOrderInfoToTransaction(Map<String, Object> order, Map<String, Object> transaction) {
        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        // 退款、撤单、预授权完成和预授权撤销时，将订单信息也放到kafka消息中，方便下游系统处理
        if(NEED_PUSH_ORDER_TYPE.contains(transactionType)) {
            Map<String, Object> ext = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(null == ext) {
                ext = new HashMap<String, Object>();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, ext);
            }
            Map<String, Object> orderInfo = (Map<String, Object>) ext.get(Transaction.ORDER_INFO);
            if(null == orderInfo){
                orderInfo = new HashMap<String, Object>();
                ext.put(Transaction.ORDER_INFO, orderInfo);
            }
            orderInfo.putAll(order);
        }
    }
}
