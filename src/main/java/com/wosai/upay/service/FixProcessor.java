package com.wosai.upay.service;

import com.lark.chatbot.message.*;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.OrderNotExistsException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.exception.UpayFixOrderStateError;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.*;
import com.wosai.upay.workflow.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;


@Service
public class FixProcessor {
    public static final Logger logger = LoggerFactory.getLogger(FixProcessor.class);
    private static final String KEY_QUERY_EXPIRE = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.QUERY_EXPIRE);

    @Autowired
    private TsnGenerator tsnGenerator;
    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private TradeCacheService tradeCacheService;
    @Autowired
    private UpayOrderService upayOrderService;
    @Autowired
    private GatewaySupportService gatewaySupportService;
    @Autowired
    private DistributedLock distributedLock;

    public static long FIX_MIN_TIME_ALLOWED = 1000 * 60 * 10; //10分钟
    public static long FIX_MIN_HOUR_TIME_ALLOWED = 1000 * 60 * 60 * 24;//24小时

    /**
     * @param merchantId 有可能为空
     * @param sn
     * @return
     */
    @SuppressWarnings("unchecked")
    public TransactionContext fixPay(String sn, String merchantId, String tradeNo, boolean isManualFix) {
        if (distributedLock.isNotCanOrderFix(sn)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR, UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR_MESSAGE);
        }

        Map<String, Object> order = repository.getOrderByOrderSn(merchantId, sn);
        Map<String, Object> payTransaction = null;
        boolean isHistory = false;
        if (order == null) {
            // 历史交易需要查询hbase，只有在订单只存在支付流水时才进行后续处理
            List<Map<String, Object>> transactionList = upayOrderService.getOriginalTransactionListByOrderSn(sn);
            if (com.wosai.pantheon.util.CollectionUtil.isNotEmpty(transactionList) && transactionList.size() == 1) {
                payTransaction = transactionList.get(0);
                if (MapUtil.getIntValue(payTransaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                    order = gatewaySupportService.getOrderBySnAndDate(MapUtil.getString(payTransaction, Transaction.MERCHANT_ID), 
                            sn, 
                            new SimpleDateFormat("yyyyMMdd").format(new Date(MapUtil.getLongValue(payTransaction, DaoConstants.CTIME))));
                    isHistory = true;
                }
            }
        }
        if (order == null) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(order, Order.SN);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        long ctime = BeanUtil.getPropLong(order, DaoConstants.CTIME);
        int payway = BeanUtil.getPropInt(order, Order.PAYWAY);
        String  provider = BeanUtil.getPropString(order, Order.PROVIDER);
        long oldMtime = BeanUtil.getPropLong(order, DaoConstants.MTIME);

        if(existPaySuccessTransaction(merchantId, orderSn)){
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }

        if(orderStatus == Order.STATUS_PAY_ERROR || orderStatus == Order.STATUS_CREATED || orderStatus == Order.STATUS_PAY_CANCELED || orderStatus == Order.STATUS_CANCELED){
            if (orderStatus == Order.STATUS_CREATED){
                if(!BeanUtil.getPropBoolean(payTransaction, KEY_QUERY_EXPIRE) && System.currentTimeMillis() - ctime < FIX_MIN_TIME_ALLOWED) {
                    throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
                }
            }else if(orderStatus == Order.STATUS_CANCELED && System.currentTimeMillis() - ctime < FIX_MIN_HOUR_TIME_ALLOWED ){
                throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
            }else if(orderStatus == Order.STATUS_PAY_CANCELED){
                logger.warn("fix order, status is PAY_CANCELED: {} {} {}", orderSn, provider, payway);
            }

        }else{
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }

        if (orderStatus == Order.STATUS_CANCELED && BeanUtil.getPropInt(payTransaction,Transaction.STATUS) == Transaction.STATUS_SUCCESS ){
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }
        if (payTransaction == null){
            payTransaction = repository.getPayTransactionByOrderSn(merchantId, sn);
        }
        if (!facade.checkRefundOrFixOriginalClearanceProviderWhereMchUp(payTransaction)) {
            manualFixLarkNotify("订单因小微升级禁止勾兑", String.format("订单号%s", sn));
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_MCH_SWITCH_FIX_FAIL, UpayErrorScenesConstant.ORDER_MCH_SWITCH_FIX_FAIL_MESSAGE);
        }
        final TransactionContext context = workflowManager.createTransactionContext(null, order, payTransaction);
        final Workflow workflow = context.getWorkflow();

        String rcFlag;
        //银联云闪付与网联翼支付&人工手动处理
        boolean orderNeedManualFix = (payway == Order.PAYWAY_LKL_UNIONPAY || (String.valueOf(Order.PROVIDER_NUCC).equals(provider) && payway == Order.PAYWAY_BESTPAY));
        if ((orderNeedManualFix || ApolloConfigurationCenterUtil.getWithoutProviderQueryOrderSns().contains(sn)) && isManualFix) {
            //校验人工手动调账开关是否开启
            checkManualSwitch();
            //钉群通知内容
            String subject = String.format("[支付网关服务通知] %s 隔日订单处理, 订单号: %s 金额: %s 商户: %s"
                    , UpayUtil.getPaywayName(payway)
                    , orderSn
                    , BeanUtil.getPropString(order, Order.ORIGINAL_TOTAL)
                    , BeanUtil.getPropString(order, Order.MERCHANT_ID));
            String content = String.format("处理前订单状态: %s 处理后订单状态: %s 支付通道：%s 支付服务商: %s 支付方式：%s"
                    , orderStatus
                    , Order.STATUS_PAID
                    , BeanUtil.getPropString(order, Order.PROVIDER)
                    , BeanUtil.getPropString(order, Order.PAYWAY)
                    , BeanUtil.getPropString(order, Order.SUB_PAYWAY));
            //手工调账钉群通知
            manualFixLarkNotify(subject, content);
            rcFlag = Workflow.RC_PAY_SUCCESS;
        } else {
            MpayServiceProvider serviceProvider = context.getServiceProvider();
            rcFlag = serviceProvider.query(context);
        }

        if(Workflow.RC_PAY_SUCCESS.equals(rcFlag)){
            if(orderStatus == Order.STATUS_PAY_ERROR) {
                String terminalSnOrStoreSn = BeanUtil.getPropString(context.getTransaction(), (null != BeanUtil.getPropString(order, Order.TERMINAL_ID)) ? Transaction.KEY_TERMINAL_SN: Transaction.KEY_STORE_SN);
                tradeCacheService.removeTradeCache(terminalSnOrStoreSn, MapUtil.getString(order, Order.CLIENT_SN), MapUtil.getString(order, Order.SN));
            }
            if (!isHistory) {
                long currentMtime = BeanUtil.getPropLong(repository.getOrderByOrderSn(merchantId, orderSn), DaoConstants.MTIME);
                if(oldMtime != currentMtime){
                    throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_ALEADY_MODIFYED, UpayErrorScenesConstant.FIX_ORDER_ALEADY_MODIFYED_MESSAGE);
                }
            }
            Map<String,Object> updateOrder = CollectionUtil.hashMap(
                    DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                    Order.MERCHANT_ID, merchantId,
                    Order.NET_ORIGINAL, BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL),
                    Order.NET_EFFECTIVE, BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL),
                    Order.NET_DISCOUNT, BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT),
                    Order.ITEMS, order.get(Order.ITEMS),
                    DaoConstants.VERSION, order.get(DaoConstants.VERSION)
            );
            if (StringUtil.isNotEmpty(tradeNo) &&
                    MapUtil.getIntValue(payTransaction, Transaction.PROVIDER) == Order.PROVIDER_UNIONPAY_OPEN &&
                    MapUtil.getIntValue(payTransaction, Transaction.PAYWAY) == Order.PAYWAY_UNIONPAY) {
                payTransaction.put(Order.TRADE_NO, tradeNo);
                updateOrder.put(Order.TRADE_NO, tradeNo);
            }
            order.putAll(updateOrder);
            payTransaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
            payTransaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
            BeanUtil.setNestedProperty(payTransaction, Transaction.KEY_IS_FIX, true);
            if (isHistory) {
                BeanUtil.setNestedProperty(payTransaction, Transaction.KEY_IN_HBASE, true);
            }

            Map<String, Object> finalOrder = order;
            boolean finalIsHistory = isHistory;
            repository.doInTransaction(new Runnable() {
                @Override
                public void run() {
                    //有可能net相关字段已经被清0，现在恢复
                    PaymentUtil.updateOrderPaymentsNetAmountToAmountTotal((List<Map<String, Object>>) BeanUtil.getNestedProperty(finalOrder, PaymentUtil.ORDER_PAYMENTS_PATH));
                    if (!finalIsHistory) {
                        repository.getOrderDao().updatePart(
                                updateOrder
                        );
                    }
                    workflow.finish(context);
                }
            });
            context.disappear();

        }else{
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_FAIL, UpayErrorScenesConstant.FIX_FAIL_MESSAGE);
        }
        return context;
    }

    public TransactionContext refundRevoke(String sn, String tsn){
        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        Map<String,Object> refundTransaction = repository.getTransactionByTsn(null, tsn);
        boolean isDBTrade = true;
        if(order == null){
            order = upayOrderService.getOrderBySn(null, sn, null);
            isDBTrade = false;
        }
        if(refundTransaction == null){
            List<Map<String,Object>> refundTransactions = gatewaySupportService.getSuccessTransactionList(BeanUtil.getPropString(order, Order.MERCHANT_ID), sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
            if(refundTransactions != null){
                for(Map<String,Object> transaction: refundTransactions){
                    if(BeanUtil.getPropString(transaction, Transaction.TSN).equals(tsn)){
                        refundTransaction = transaction;
                        break;
                    }
                }
            }
            //校验此笔退款是否已被回退
            if(refundTransaction != null){
                String fixClientTsn = BeanUtil.getPropString(refundTransaction, Transaction.CLIENT_TSN) + "-F";
                for(Map<String,Object> transaction: refundTransactions){
                    if(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN).equals(fixClientTsn)){
                        logger.warn("此笔退款已被撤销!");
                        throw new UpayBizException(UpayErrorScenesConstant.REFUND_REVOKE_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.REFUND_REVOKE_STATUS_NOT_SUPPORT_MESSAGE);
                    }
                }
            }

        }
        if(order == null || refundTransaction == null){
            throw  new OrderNotExistsException(UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST, UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST_MESSAGE);
        }

        if (distributedLock.isNotCanOrderFix(MapUtil.getString(refundTransaction, Transaction.TSN))) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR, UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR_MESSAGE);
        }

        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        int transactionStatus = BeanUtil.getPropInt(refundTransaction, Transaction.STATUS);
        if(BeanUtil.getPropInt(refundTransaction, Transaction.TYPE) != Transaction.TYPE_REFUND || transactionStatus != Transaction.STATUS_SUCCESS){
            throw new UpayBizException(UpayErrorScenesConstant.VALIDATION_EXCEPTION_FAIL_ORDER_STATUS, UpayErrorScenesConstant.VALIDATION_EXCEPTION_FAIL_ORDER_STATUS_MESSAGE);
        }
        if(!sn.equals(BeanUtil.getPropString(refundTransaction, Transaction.ORDER_SN))){
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_AND_TSN_NOT_MATCH, UpayErrorScenesConstant.ORDER_AND_TSN_NOT_MATCH_MESSAGE);
        }

        if(orderStatus != Order.STATUS_REFUNDED && orderStatus != Order.STATUS_PARTIAL_REFUNDED){
            throw new UpayBizException(UpayErrorScenesConstant.REFUND_REVOKE_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.REFUND_REVOKE_STATUS_NOT_SUPPORT_MESSAGE);
        }

        if (!facade.checkRefundOrFixOriginalClearanceProviderWhereMchUp(refundTransaction)) {
            manualFixLarkNotify("订单因小微升级禁止勾兑", String.format("订单号%s", sn));
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_MCH_SWITCH_FIX_FAIL, UpayErrorScenesConstant.ORDER_MCH_SWITCH_FIX_FAIL_MESSAGE);
        }
        String clientTsn = BeanUtil.getPropString(refundTransaction, Transaction.CLIENT_TSN) + "-F";
        String nextSn = tsnGenerator.nextSn();
        Map extraOutFields = (Map)refundTransaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(null == extraOutFields) {
            extraOutFields = new HashMap();
        }
        extraOutFields.put(Transaction.ORDER_INFO, CollectionUtil.hashMap(Order.ORIGINAL_TOTAL, order.get(Order.ORIGINAL_TOTAL),
                                                                        Order.EFFECTIVE_TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                                                                        DaoConstants.CTIME, order.get(DaoConstants.CTIME),
                                                                        Order.TRADE_NO, order.get(Order.TRADE_NO)
        ));

        @SuppressWarnings("unchecked")
        final Map<String, Object> fixTransaction = CollectionUtil.hashMap(DaoConstants.ID, UpayUtil.getTransactionIdBySn(nextSn),
                Transaction.TSN, nextSn,
                Transaction.CLIENT_TSN, clientTsn,
                Transaction.TYPE, Transaction.TYPE_REFUND_REVOKE,
                Transaction.ITEMS, refundTransaction.get(Transaction.ITEMS),
                Transaction.SUBJECT, order.get(Order.SUBJECT),
                Transaction.BODY, order.get(Order.BODY),
                Transaction.STATUS, Transaction.STATUS_CREATED,
                Transaction.ORIGINAL_AMOUNT, refundTransaction.get(Transaction.ORIGINAL_AMOUNT),
                Transaction.EFFECTIVE_AMOUNT, refundTransaction.get(Transaction.EFFECTIVE_AMOUNT),
                Transaction.RECEIVED_AMOUNT, refundTransaction.get(Transaction.RECEIVED_AMOUNT),
                Transaction.PAID_AMOUNT, refundTransaction.get(Transaction.PAID_AMOUNT),
                Transaction.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                Transaction.STORE_ID, order.get(Order.STORE_ID),
                Transaction.TERMINAL_ID, order.get(Order.TERMINAL_ID),
                Transaction.PROVIDER, order.get(Order.PROVIDER),
                Transaction.PAYWAY, order.get(Order.PAYWAY),
                Transaction.SUB_PAYWAY, order.get(Order.SUB_PAYWAY),
                Transaction.ORDER_ID, order.get(DaoConstants.ID),
                Transaction.ORDER_SN, order.get(Order.SN),
                Transaction.BUYER_UID, order.get(Order.BUYER_UID),
                Transaction.TRADE_NO, order.get(Order.TRADE_NO),
                Transaction.FINISH_TIME, System.currentTimeMillis(),
                Transaction.PRODUCT_FLAG, refundTransaction.get(Transaction.PRODUCT_FLAG),
                Transaction.EXTRA_OUT_FIELDS, extraOutFields,
                Transaction.CONFIG_SNAPSHOT, refundTransaction.get(Transaction.CONFIG_SNAPSHOT),
                Transaction.OPERATOR, refundTransaction.get(Transaction.OPERATOR),
                Transaction.REFLECT, refundTransaction.get(Transaction.REFLECT),
                Transaction.BUYER_UID, refundTransaction.get(Transaction.BUYER_UID),
                Transaction.BUYER_LOGIN, refundTransaction.get(Transaction.BUYER_LOGIN));
        if(!isDBTrade){
            BeanUtil.setNestedProperty(fixTransaction, Transaction.KEY_IS_HISTORY_TRADE_REFUND, true);
        }
        repository.getTransactionDao().save(fixTransaction);
        final TransactionContext context = workflowManager.createTransactionContext(null, order, fixTransaction);
        final Workflow workflow = workflowManager.matchWorkflow(fixTransaction);
        //todo 查询第3方通道的退款状态, 确认是不是不能原路退回的
        workflow.finish(context);
        return context;
    }

    
    public TransactionContext fixCancelOrRefundToSuccess(String sn, String tsn, Long realTradeFee, List<Map<String,Object>> refundChannelPayments){
        if (distributedLock.isNotCanOrderFix(sn)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR, UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR_MESSAGE);
        }

        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        final Map<String,Object> transaction = repository.getTransactionByTsn(null, tsn);
        boolean isHistoryRefund = false;
        if(null == order && null != transaction && BeanUtil.getPropBoolean(transaction, Transaction.KEY_IS_HISTORY_TRADE_REFUND)) {
            isHistoryRefund = true;
            order = upayOrderService.getOrderBySn(BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID), BeanUtil.getPropString(transaction, Transaction.ORDER_SN), null);
        }

        if(order == null || transaction == null){
            throw  new OrderNotExistsException(UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST, UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST_MESSAGE);
        }

        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(!((type == Transaction.TYPE_CANCEL || type == Transaction.TYPE_REFUND) && transactionStatus != Transaction.STATUS_SUCCESS)){
        	throw new UpayBizException(UpayErrorScenesConstant.FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR_MESSAGE);
        }
        if(!sn.equals(BeanUtil.getPropString(transaction, Transaction.ORDER_SN))){
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_AND_TSN_NOT_MATCH, UpayErrorScenesConstant.ORDER_AND_TSN_NOT_MATCH_MESSAGE);
        }

        if(!isHistoryRefund){
            if (orderStatus != Order.STATUS_REFUND_ERROR 
                    && orderStatus != Order.STATUS_CANCEL_ERROR 
                    && orderStatus != Order.STATUS_REFUND_INPROGRESS
                    && orderStatus != Order.STATUS_CANCEL_INPROGRESS) {
                throw new UpayBizException(UpayErrorScenesConstant.FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR_MESSAGE);
            }
            if ((orderStatus == Order.STATUS_REFUND_INPROGRESS || orderStatus == Order.STATUS_CANCEL_INPROGRESS)
                    && System.currentTimeMillis() - MapUtil.getLongValue(transaction, DaoConstants.CTIME) < FIX_MIN_TIME_ALLOWED) {
                throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
            }
        } else {
            // 订单状态不为退款失败或撤销失败时，必须等10分钟后才能进行勾兑
            if (orderStatus != Order.STATUS_REFUND_ERROR
                    && orderStatus != Order.STATUS_CANCEL_ERROR
                    && System.currentTimeMillis() - MapUtil.getLongValue(transaction, DaoConstants.CTIME) < FIX_MIN_TIME_ALLOWED ) {
                throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
            }
        }

        Map<String, Object> payTransaction = null;
        final String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        String transactionId = BeanUtil.getPropString(transaction, DaoConstants.ID);
        if(!isHistoryRefund) {
            if(!BeanUtil.getPropBoolean(transaction, Transaction.KEY_IS_DEPOSIT)) {
                payTransaction = repository.getPayTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
            }else {
                payTransaction = repository.getConsumeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
            }
        }else {
            payTransaction = gatewaySupportService.getPayOrConsumerTransaction(BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID), BeanUtil.getPropString(transaction, Transaction.ORDER_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        
        //更新支付通道的支付明细，以便能准确计算商户免充值，以及结算金额等信息
        updateTransactionChannelPayment(order, payTransaction, transaction, refundChannelPayments);
        if(!UpayUtil.isFormal(workflowManager, transaction) && BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) != 0){
            long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            long walletChangeAmount = FeeUtil.calculateApproximateWalletChangeAmount(payTransaction,(List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH), originalAmount,originalAmount == BeanUtil.getPropLong(order,Order.ORIGINAL_TOTAL));
            int currentClearanceProvider = MapUtil.getIntValue((Map)payTransaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER);
            // 退款时，需要拿到原始终端的结算通道
            try {
                Map<String, Object>  sourceBasicParams = facade.getBasicParams(MapUtil.getString((Map)payTransaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.STORE_SN), MapUtil.getString((Map)payTransaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_SN));
                currentClearanceProvider = MapUtil.getIntValue(sourceBasicParams, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
            }catch (Exception e) {
            }
            //当余额不足或者有其他错误时，下面的方法会抛异常，交由ServiceMethodInterceptor去处理
            if (!facade.checkRefundOrFixOriginalClearanceProviderWhereMchUp(payTransaction)) {
                manualFixLarkNotify("订单因小微升级禁止勾兑", String.format("订单号%s",sn));
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_MCH_SWITCH_FIX_FAIL, UpayErrorScenesConstant.ORDER_MCH_SWITCH_FIX_FAIL_MESSAGE);
            }
            facade.canCancelOrRefundAndFreezeWalletBalance(merchantId, transactionId, walletChangeAmount, transaction, currentClearanceProvider);
            WalletDeductionContextHolder.setContextHolder(merchantId, transactionId);
        }
        
        // 撤单失败交易可以继续撤单，在处理过程时，先变更表状态，防止出现并发撤单
        if(type == Transaction.TYPE_CANCEL) {
            try {
                repository.getOrderDao().updatePart(
                        CollectionUtil.hashMap(
                                DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                                Order.MERCHANT_ID, merchantId,
                                Order.STATUS, Order.STATUS_CANCEL_INPROGRESS,
                                DaoConstants.VERSION, order.get(DaoConstants.VERSION)
                        )
    
                );
            }catch (DaoVersionMismatchException e) {
                throw new UpayBizException("订单已发生变更，处理失败，请检查");
            }
        }
        final TransactionContext context = workflowManager.createTransactionContext(null, order, transaction);
        //extra_out_fields 中记录 通道侧手续费
        if (Objects.nonNull(realTradeFee)) {
            Map<String, Object> extraOutFields = (Map<String, Object>) transaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (extraOutFields == null) {
                extraOutFields = new HashMap<>();
                transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            }
            extraOutFields.put(Transaction.REAL_TRADE_FEE, realTradeFee);
        } else if (context.getServiceProvider() instanceof HaikeUnionPayServiceProvider || context.getServiceProvider() instanceof ZTKXProvider) {
            MpayServiceProvider serviceProvider = context.getServiceProvider();
            String result = serviceProvider.refundQuery(context);
            if (!Workflow.RC_REFUND_SUCCESS.equals(result)) {
                throw new UpayBizException("退款非成功，处理失败，请检查");
            }
        }
        final Workflow workflow = workflowManager.matchWorkflow(transaction);
        transaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_FIX, true);
        workflow.finish(context);
        return context;
    }

    private boolean existPaySuccessTransaction(String merchantId, String orderSn){
        long count = repository.getTransactionDao().filter(
                Criteria.where(Transaction.ORDER_SN).is(orderSn)
                        .with(Transaction.MERCHANT_ID).is(merchantId)
                        .with(Transaction.TYPE).is(Transaction.TYPE_PAYMENT)
                        .with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)
        ).count();
        return count == 0 ? false : true;
    }


    public void updateTransactionChannelPayment(Map<String,Object> order, Map<String,Object> payTransaction, Map<String,Object> transaction, List<Map<String,Object>> refundChannelPayments){
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT) || BeanUtil.getPropInt(transaction, Transaction.TYPE) == Transaction.TYPE_CANCEL){
            //全额退款或者撤单
            PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, transaction);
        }else{
            //部分退款
            if(refundChannelPayments == null || refundChannelPayments.isEmpty()){
                AbstractServiceProvider.resolveRefundFundByPercent(transaction, payTransaction);
            }else {
                int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
                if(payway == Order.PAYWAY_WEIXIN || payway == Order.PAYWAY_WEIXIN_HK){
                    updateWeixinRefundChannelPayments(payTransaction, transaction, refundChannelPayments);
                }else if(payway == Order.PAYWAY_ALIPAY2){
                    updateAlipayRefundChannelPayments(transaction, refundChannelPayments);
                }
            }

            // 设置received_amount和paid_amount
            logger.debug("set recevied_amount and paid_amount from effective_amount");
            long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            transaction.put(Transaction.RECEIVED_AMOUNT, effectiveAmount);
            transaction.put(Transaction.PAID_AMOUNT, effectiveAmount);
        }
    }


    private void updateWeixinRefundChannelPayments(Map<String,Object> payTransaction, Map<String,Object> transaction, List<Map<String,Object>> refundChannelPayments){
        List<Map<String,Object>> payments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(payTransaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH);
        List<Map<String,Object>> refundPayments = new ArrayList<>();
        for (int i = 0; i < payments.size(); i++) {
            Map<String,Object> refundPayment = (Map<String, Object>) ((HashMap)payments.get(i)).clone();
            refundPayment.put(Transaction.PAYMENT_AMOUNT, 0);
            refundPayments.add(refundPayment);
        }
        refundChannelPayments.forEach(payment -> {
            String couponRefundId = BeanUtil.getPropString(payment, Transaction.PAYMENT_SOURCE);
            long couponRefundFee = BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
            String refundType = BeanUtil.getPropString(payment, Transaction.PAYMENT_TYPE);
            for (int j = 0; j < refundPayments.size(); j++) {
                Map<String,Object> refundPayment = refundPayments.get(j);
                String sourceId = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_SOURCE, "");
                String type = BeanUtil.getPropString(refundPayment, Transaction.PAYMENT_TYPE, "");
                if(sourceId.equals(couponRefundId)){
                    refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                }else if(Payment.TYPE_WALLET_WEIXIN.equals(refundType) || Payment.TYPE_BANKCARD_CREDIT.equals(refundType) || Payment.TYPE_BANKCARD_DEBIT.equals(refundType)){
                    if(type.equals(refundType)){
                        refundPayment.put(Transaction.PAYMENT_AMOUNT, couponRefundFee);
                    }
                }
            }
        });
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
    }


    private void updateAlipayRefundChannelPayments(Map<String,Object> transaction, List<Map<String,Object>> refundChannelPayments){
        List<Map<String,Object>> refundPayments = new ArrayList<>();
        refundChannelPayments.forEach(payment -> {
            String originalType = BeanUtil.getPropString(payment, Transaction.PAYMENT_ORIGIN_TYPE, "");
            String type = BeanUtil.getPropString(payment, Transaction.PAYMENT_TYPE, "");
            long amount = BeanUtil.getPropLong(payment, Transaction.PAYMENT_AMOUNT);
            if(!type.isEmpty() && payment.containsKey(Transaction.PAYMENT_AMOUNT)){
                refundPayments.add(CollectionUtil.hashMap(
                        Transaction.PAYMENT_TYPE, type,
                        Transaction.PAYMENT_ORIGIN_TYPE, originalType,
                        Transaction.PAYMENT_AMOUNT, amount
                ));
            }
        });
        BeanUtil.setNestedProperty(transaction, PaymentUtil.TRANSACTION_CHANNEL_PAYMENTS_PATH, refundPayments);
    }

    private void checkManualSwitch() {
        if (!ApolloConfigurationCenterUtil.isSupportUnionpayManualFix()) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_FAIL
                    , UpayErrorScenesConstant.FIX_FAIL_MESSAGE);
        }
    }

    private void manualFixLarkNotify(String subject, String content) {
        StringBuilder contentTemp = new StringBuilder();
        contentTemp.append("主题：").append(subject).append("\n");
        contentTemp.append("内容：").append(content).append("\n");
        Message message = new TextMessage(contentTemp.toString());
    	facade.sendLarkMessage(message);
    }

    public Map<String, Object> fixOrderStatusIfRefundNotSuccess(String sn){
        if (distributedLock.isNotCanOrderFix(sn)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR, UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR_MESSAGE);
        }
        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        boolean isDBTrade = true;
        if(order == null){
            order = upayOrderService.getOrderBySn(null, sn, null);
            if(null == order) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST, UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST_MESSAGE);
            }
            isDBTrade = false;
        }
        if(Order.STATUS_REFUND_INPROGRESS == BeanUtil.getPropInt(order, Order.STATUS)) {
            // 退款中状态允许在10分钟后勾兑为其它状态
            long lastModifyTime = MapUtil.getIntValue(order, DaoConstants.MTIME);
            if(System.currentTimeMillis() - lastModifyTime < 10 * 60 * 1000) {
                throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT_MESSAGE);
            }
        }else if(Order.STATUS_REFUND_ERROR != BeanUtil.getPropInt(order, Order.STATUS)){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT_MESSAGE);
        }
        String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        int orderStatus = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(order, Order.NET_ORIGINAL) ? Order.STATUS_PAID : Order.STATUS_PARTIAL_REFUNDED;
        // 如果最后一笔退款是预授权交易，且退款金额等于订单剩余金额时，订单状态应该勾兑为预授权完成
        Map<String, Object> transaction = repository.getLatestRefundTransactionByOrderSn(merchantId, sn);
        if (MapUtil.isNotEmpty(transaction) 
                && MapUtil.getBooleanValue((Map)transaction.get(Transaction.EXTRA_OUT_FIELDS), Transaction.IS_DEPOSIT, false) 
                && MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT) == MapUtil.getLongValue(order, Order.NET_ORIGINAL)) {
            orderStatus = Order.STATUS_DEPOSIT_CONSUMED;
        }
        Map orderUpdate = CollectionUtil.hashMap(
                    DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                    Order.MERCHANT_ID, merchantId,
                    Order.STATUS, orderStatus
            );
        order.putAll(orderUpdate);
        if(isDBTrade) {
            repository.getOrderDao().updatePart(orderUpdate);
        }else {
            order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
            order.put(DaoConstants.MTIME, System.currentTimeMillis());
            upayOrderService.updateOrder(order);
        }
        return order;
    }
}
