package com.wosai.upay.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.FakeRequestUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;
import com.wosai.upay.workflow.TransactionContext;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
public class ClientNotifier {

    static {
        //回调的http连接池最大设置为1000
        System.setProperty("pool-" + ClientNotifier.class.getName(), "1000");
    }

    @Autowired
    private ExternalServiceFacade serviceFacade;

    private static final Logger logger = LoggerFactory.getLogger(ClientNotifier.class);

    private static final String SUCCESS = "success";
    private static final String CONTENT_TYPE = "application/json";

    public static final String[] PAYWAYS = {"UNKNOWN", "ALIPAY", "ALIPAY", "WEIXIN" };
    public static final String[] SUB_PAYWAYS = {"UNKNOWN", "BARCODE", "QRCODE", "WAPPAY"};
    public static final String HEAD_AUTHORIZATION = "Authorization";

    private static final String NOTIFY_PUBLIC_KEY_ID = CryptoService.PUBLIC_KEY_ID;//"c0c0a9e2-39bb-4c75-86f1-438923c75189";

    private static final String NOTIFY_PRIVATE_KEY_ID = CryptoService.PRIVATE_KEY_ID;//"e1629a2b-aa53-4456-b432-f1db5d625907";
    
    private static final String NOTIFY_ORDER_SN = "order_sn";
    private static final String NOTIFY_OPERATOR = "operator";
    private static final String NOTIFY_WOSAI_STORE_ID = "wosai_store_id";
    private static final String NOTIFY_STATUS = "status";
    private static final String NOTIFY_ORDER_DETAIL = "order_detail";
    private static final String NOTIFY_SUBJECT = "subject";
    private static final String NOTIFY_PAYWAY = "payway";
    private static final String NOTIFY_SUB_PAY_WAY = "sub_pay_way";
    private static final String NOTIFY_TOTAL_FEE = "total_fee";
    private static final String NOTIFY_ORIGIN_FEE = "origin_fee";
    private static final String NOTIFY_STORE_OWNER_ORDER_SN = "store_owner_order_sn";
    private static final String NOTIFY_TRADE_NO = "trade_no";
    private static final String NOTIFY_PAY_ACCOUNT = "pay_account";
    private static final String NOTIFY_ORDER_PAY_TIME = "order_pay_time";
    private static final String NOTIFY_PAY_ID = "pay_id";
    
    

    @Resource(name = "clientNotifyThreadPool")
    private ScheduledExecutorService executor;
    @Autowired
    private ObjectMapper om;
    private final int connectTimeout = 3000;
    private final int readTimeout = 15000;
    private final long[] delays = {1, 5, 30, 600};

    private final OkHttpClient client = HttpClientUtils.getHttpClient(ClientNotifier.class.getName(),null,null,connectTimeout,readTimeout,400,50);

    
    private static final SafeSimpleDateFormat format = new SafeSimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);

    public void notify(String url, String tsn, Map<String, Object> notification) {
        if(FakeRequestUtil.isFakeRequest()) return;
        if(!isValidUrl(url)){
            logger.warn("not notify , url is not valid , url: {}, data: {}", url, JsonUtil.toJsonStr(notification));
        }
        executor.submit(new NotifyTask(url, tsn, notification));
    }

    public void notify(String url, TransactionContext context) {
        String tsn = BeanUtil.getPropString(context.getTransaction(), Transaction.TSN);
        if(!isValidUrl(url)){
            logger.warn("not notify , url is not valid , url: {}, tsn: {}", url, tsn);
        }
        Map<String, Object> notification;
        if (context.getApiVer() == 1) {
            notification = makeV1Notification(context);
            if (notification != null) {
                notify(url, tsn, notification);
            }
        } else {
            Map<String,Object> configSnapshot = (Map<String, Object>) context.getTransaction().get(Transaction.CONFIG_SNAPSHOT);
            notification = TransactionResponder.makeResponseData(context, !BeanUtil.getPropBoolean(configSnapshot, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
            notification.put(UpayService.PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(context.getOrder(), context.getTransaction()));
            // 电饱饱同步订单标识
            Map<String, Object> extraOutFields = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_OUT_FIELDS);
            if(MapUtil.isNotEmpty(extraOutFields)) {
                MapUtil.addKeysIfNotExist(extraOutFields, notification, Transaction.IS_DEPOSIT_SYNC);
            }
            notify(url, tsn, notification);
        }
    }
    
    @SuppressWarnings("unchecked")
    public Map<String, Object> makeV1Notification(TransactionContext context) {
        Map<String, Object> order = context.getOrder();
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> config = (Map<String, Object>)transaction.get(Transaction.CONFIG_SNAPSHOT);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        int status = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        
        if (orderStatus != Order.STATUS_PAID || status != Transaction.STATUS_SUCCESS) {
            return null;
        }
        int payway = BeanUtil.getPropInt(order, Order.PAYWAY);
        int subPayway = BeanUtil.getPropInt(order, Order.SUB_PAYWAY);
        if (payway != Order.PAYWAY_ALIPAY && payway != Order.PAYWAY_ALIPAY2 && payway != Order.PAYWAY_WEIXIN) {
            return null;
        }
        if (subPayway != Order.SUB_PAYWAY_QRCODE && subPayway != Order.SUB_PAYWAY_WAP && subPayway != Order.SUB_PAYWAY_MINI) {
            return null;
        }

        long total = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL);
        
        return CollectionUtil.hashMap(NOTIFY_ORDER_SN, order.get(Order.SN),
                                      NOTIFY_OPERATOR, transaction.get(Transaction.OPERATOR),
                                      NOTIFY_WOSAI_STORE_ID, BeanUtil.getPropString(config, TransactionParam.STORE_SN),
                                      NOTIFY_STATUS, 1,
                                      NOTIFY_ORDER_DETAIL, order.get(Order.BODY),
                                      NOTIFY_SUBJECT, order.get(Order.SUBJECT),
                                      NOTIFY_PAYWAY, getPaywayString(payway),
                                      NOTIFY_SUB_PAY_WAY, getSubPaywayString(subPayway),
                                      NOTIFY_TOTAL_FEE, total,
                                      NOTIFY_ORIGIN_FEE, StringUtils.cents2yuan(total),
                                      NOTIFY_STORE_OWNER_ORDER_SN, order.get(Order.CLIENT_SN),
                                      NOTIFY_TRADE_NO, transaction.get(Transaction.TRADE_NO),
                                      NOTIFY_PAY_ACCOUNT, transaction.get(Transaction.BUYER_LOGIN),
                                      NOTIFY_ORDER_PAY_TIME, format.format(new Date(BeanUtil.getPropLong(transaction, Transaction.FINISH_TIME))),
                                      NOTIFY_PAY_ID, transaction.get(Transaction.BUYER_UID));

    }


    private static String getPaywayString(int payway) {
        if (payway <=0 || payway >= PAYWAYS.length) {
            return null;
        }else{
            return PAYWAYS[payway];
        }
    }

    private static String getSubPaywayString(int subPayway) {
        if (subPayway <=0 || subPayway >= SUB_PAYWAYS.length) {
            return null;
        }else{
            return SUB_PAYWAYS[subPayway];
        }
    }

    /**
     * 简单判断下是不是合法的url地址
     * @param url
     * @return
     */
    private static boolean isValidUrl(String url){
        if(StringUtil.empty(url)){
            return false;
        }
        if(!url.startsWith("https://") && !url.startsWith("http://")){
            return false;
        }
        return true;
    }

    class NotifyV1Task implements Runnable {
        private String url;
        private String tsn;
        private Map<String, Object> notification;

        NotifyV1Task(String url, String tsn, Map<String, Object> notification) {
            this.url = url;
            this.tsn = tsn;
            this.notification = notification;
        }

        @Override
        public void run() {
            try {
                String requestStr = om.writeValueAsString(notification);
                String authorization = RsaSignature.sign(requestStr, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, serviceFacade.getRsaKeyDataById(NOTIFY_PRIVATE_KEY_ID));
                logger.info("notify v1 request {}", requestStr);
                Request request = new Request.Builder()
                        .url(url).header(HEAD_AUTHORIZATION,authorization)
                        .post(RequestBody.create(MediaType.parse(CONTENT_TYPE + "; charset=utf-8"), requestStr)).build();
                Call call = client.newCall(request);
                call.enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        logger.warn("notify v1 fail, tsn: {}, url: {}, notification: {}, network error: {}", tsn, url, notification, e.getClass() + ":" + e.getMessage());
                    }
                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        try {
                            if(!response.isSuccessful()){
                                logger.warn("notify v1 fail, tsn: {}, url: {}, notification: {}, fail, httpcode is :{}", tsn, url, notification, response.code());
                            }
                            if(response.body() == null){
                                logger.warn("notify v1 success, tsn: {}, url: {}, notification: {}, response is null", tsn, url, notification);
                            }
                            String result =  new String(response.body().bytes(), StandardCharsets.UTF_8);
                            logger.debug("notify v1 success, tsn: {}, url: {} result:{}", tsn, url, result);
                        }finally {
                            response.close();
                        }
                    }
                });
            } catch (Throwable ex) {
                logger.error("notify v1 fail, tsn: {}, url: {}, notification: {}, other fatal error: {}", tsn, url, notification, ex.getClass() + ":" + ex.getMessage());
            }
        }
        
    }

	class NotifyTask implements Runnable {
	    private int iter = 0;
	    private String url;
        private String tsn;
	    private Map<String, Object> notification;
	
	    NotifyTask(String url, String tsn, Map<String, Object> notification) {
	        this.url = url;
            this.tsn = tsn;
	        this.notification = notification;
	    }
	
	    @Override
	    public void run() {
	        try {
                String requestStr = om.writeValueAsString(notification);
                String authorization = RsaSignature.sign(requestStr, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, serviceFacade.getRsaKeyDataById(NOTIFY_PRIVATE_KEY_ID));
                logger.info("notify v2 request {}", requestStr);
                Request request = new Request.Builder()
                        .url(url).header(HEAD_AUTHORIZATION,authorization)
                        .post(RequestBody.create(MediaType.parse(CONTENT_TYPE + "; charset=utf-8"), requestStr)).build();
                Call call = client.newCall(request);
                Runnable retry = ()->{
                    if (iter < delays.length) {
                        executor.schedule(this, delays[iter], TimeUnit.SECONDS);
                        ++iter;
                    }else{
                        logger.error("notify v2 failed to notify {} after {} retries, tsn: {}", url, delays.length, tsn);
                    }
                };
                call.enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        logger.warn("notify v2 fail, tsn: {}, url: {}, notification: {}, network error: {}", tsn, url, notification, e.getClass() + ":" + e.getMessage());
                        retry.run();
                    }
                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        try {
                            if(!response.isSuccessful()){
                                logger.warn("notify v2 fail, tsn: {}, url: {}, notification: {}, code: {}", tsn, url, notification, response.code());
                                retry.run();
                                return;
                            }
                            if(response.body() == null){
                                logger.warn("notify v2 fail, tsn: {}, url: {}, notification: {}, response is null", tsn, url, notification);
                                retry.run();
                                return;
                            }
                            String result =  new String(response.body().bytes(), StandardCharsets.UTF_8);
                            if(SUCCESS.equals(result)){
                                logger.debug("notify v2 success, tsn: {}, url: {}", tsn, url);
                            }else{
                                logger.warn("notify v2 fail, tsn: {}, url: {}, notification: {}, unknown response: {}", tsn, url, notification, result);
                                retry.run();
                            }
                        }finally {
                            response.close();
                        }
                    }
                });
	        } catch (Throwable ex) {
                logger.error("notify v2 fail, tsn: {}, url: {}, notification: {}, other fatal error: {}", tsn, url, notification, ex.getClass() + ":" + ex.getMessage());
	        }
	    }
	    
    }

}
