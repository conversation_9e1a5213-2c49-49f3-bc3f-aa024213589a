package com.wosai.upay.service;


import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.TranLimitError;
import com.wosai.upay.exception.UpayMerchantOverDailyPaywayLimitError;
import com.wosai.upay.exception.UpaySingleTradeOverLimitError;
import com.wosai.upay.exception.UpayStoreOverDailyLimitError;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.workflow.MpayServiceProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

@Component
public class TradingLimitFacade  {
    private static final Logger logger = LoggerFactory.getLogger(TradingLimitFacade.class);

    @Autowired
    PaymentSumService paymentSumService;


    public void checkTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider) {
        checkBankCardTradingLimit(transaction, provider);
        checkMerchantOrStoreTradingLimit(transaction, provider);
        checkPaywayTradingLimit(transaction, provider);
        checkSingleTradingLimit(transaction, provider);
        checkCreditTradingLimit(transaction, provider);
        checkTradeAppIdTradingLimit(transaction, provider);
    }

    /**
     * 校验范围：非直清类型银行卡交易
     * 校验内容：日限额 月限额  新增 pos类型单笔
     */
    private void checkBankCardTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider) {
        Map<String, Object> tradeParams = provider.getTradeParams(transaction);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        int providerV2 = MapUtil.getIntValue(configSnapshot, Transaction.PROVIDER);
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        if (liquidationNextDay && (payway == Order.PAYWAY_BANKCARD && (providerV2 == TradeConfigService.LAKALA_UNION_PAY_V3 || providerV2 == TradeConfigService.PROVIDER_FUYOU))) {
            String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
            //银行卡单笔交易限额
            long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            String merchantBankcardSingleMaxLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_BANKCARD_SINGLE_MAX_LIMIT);
            if (!StringUtils.isEmpty(merchantBankcardSingleMaxLimit)) {
                long bankCardSingleLimit = StringUtils.yuan2cents(merchantBankcardSingleMaxLimit);
                if (amount > bankCardSingleLimit) {
                    logger.debug("merchant {} bank card over single limit {}, current trade amount is {}", merchantId, bankCardSingleLimit, amount);
                    throw new TranLimitError(UpayErrorScenesConstant.UPAY_SINGLE_BANKCARD_OVER_LIMIT_MESSAGE);
                }
            }
            //单日银行卡限额
            String merchantBankDayMaxLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_BANKCARD_DAY_MAX_LIMIT);
            throwExceptionIfMoreThanBankcardLimit(amount, merchantBankDayMaxLimit, PaymentSumService.DAILY_LIMIT_BANKCARD + merchantId, PaymentSumService.TYPE_DAY, UpayConstant.DAY_MAX_BANKCARD_LIMIT);
            //单月银行卡限额
            String merchantBankMonthMaxLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_BANKCARD_MONTH_MAX_LIMIT);
            throwExceptionIfMoreThanBankcardLimit(amount, merchantBankMonthMaxLimit, PaymentSumService.MONTH_LIMIT_BANKCARD + merchantId, PaymentSumService.TYPE_MONTH, UpayConstant.MONTH_MAX_BANKCARD_LIMIT);
        }
    }

    /**
     * 校验范围：非直清类型交易 （刷卡类型不归属于其中
     * 校验内容：商户层级 门店层级的 日限额 月限额
     */
    private void checkMerchantOrStoreTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider) {
        Map<String, Object> tradeParams = provider.getTradeParams(transaction);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        int providerV2 = MapUtil.getIntValue(configSnapshot, Transaction.PROVIDER);
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        if (liquidationNextDay && !(payway == Order.PAYWAY_BANKCARD && (providerV2 == TradeConfigService.LAKALA_UNION_PAY_V3 || providerV2 == TradeConfigService.PROVIDER_FUYOU))) {
            String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
            //校验商户门店当日限额
            String storeDailyMaxSumOfTrans = BeanUtil.getPropString(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS);
            String merchantDailyMaxSumOfTrans = BeanUtil.getPropString(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS);
            if (storeDailyMaxSumOfTrans != null && !TransactionParam.MERCHANT_TRANS_UNLIMITED.equals(storeDailyMaxSumOfTrans)) {
                long limit = StringUtils.yuan2cents(storeDailyMaxSumOfTrans);
                String storeId = BeanUtil.getPropString(transaction, Transaction.STORE_ID);
                long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
                long todaysPaymentTotal = paymentSumService.get(storeId, System.currentTimeMillis());
                if (todaysPaymentTotal + amount > limit) {
                    logger.debug("store {} over daily limit {}, today payment total is {}, current trade amount is {}", storeId, limit, todaysPaymentTotal, amount);
                    throw new UpayStoreOverDailyLimitError(UpayErrorScenesConstant.UPAY_STORE_OVER_DAILY_LIMIT_MESSAGE);
                }
            }
            if (merchantDailyMaxSumOfTrans != null && !TransactionParam.MERCHANT_TRANS_UNLIMITED.equals(merchantDailyMaxSumOfTrans)) {
                long limit = StringUtils.yuan2cents(merchantDailyMaxSumOfTrans);
                long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
                long todayPaymentTotal = paymentSumService.get(merchantId, System.currentTimeMillis());
                if (todayPaymentTotal + amount > limit) {
                    logger.debug("merchant {} over daily limit {}, today payment total is {}, current trade amount is {}", merchantId, limit, todayPaymentTotal, amount);
                    throw new UpayStoreOverDailyLimitError(UpayErrorScenesConstant.UPAY_STORE_OVER_DAILY_LIMIT_MESSAGE);
                }
            }
        }
    }

    /**
     * 校验范围：非直清类型交易
     * 校验内容：支付方式层级的限额 & 云闪付单笔限额
     */
    private void checkPaywayTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider) {
        Map<String, Object> tradeParams = provider.getTradeParams(transaction);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        if (liquidationNextDay) {
            Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
            String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
            Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
            Map<String, Map<String, Object>> paywayMaxSumOfTrans = (Map<String, Map<String, Object>>) BeanUtil.getProperty(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);
            if (paywayMaxSumOfTrans != null) {
                String subPayway = BeanUtil.getPropString(transaction, Transaction.SUB_PAYWAY);
                Map<String, Object> subPaywayMaxSumOfTrans = paywayMaxSumOfTrans.get(payway + "");
                // key 为 空字符串时表示存储的是某个payway的限额, 1: 校验某个payway的限额 2: 校验某个payway的sub_payway的限额
                for (String key : Arrays.asList("", subPayway)) {
                    String paywayMaxSum = com.wosai.pantheon.util.MapUtil.getString(subPaywayMaxSumOfTrans, key); //  注意不能用 BeanUtil.getPropString 来获取 key 为空字符串的值，会获取不到
                    if (!StringUtil.empty(paywayMaxSum) && !TransactionParam.MERCHANT_TRANS_UNLIMITED.equals(paywayMaxSum)) {
                        long limit = StringUtils.yuan2cents(paywayMaxSum);
                        long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
                        String bizId = merchantId + ":" + payway + ":" + key;
                        long todayPaymentTotal = paymentSumService.get(bizId, System.currentTimeMillis());
                        if (todayPaymentTotal + amount > limit) {
                            logger.debug("OverMerchantDailyPaywayMaxSumOfTrans: merchant_id={}, original_amount={}, limit={}, payway={}, weixin_sub_mch_id={}", merchantId, amount, limit, payway, BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_SUB_MCH_ID));
                            throw new UpayMerchantOverDailyPaywayLimitError(UpayErrorScenesConstant.UPAY_MERCHANT_OVER_DAILY_PAYWAY_LIMIT_MESSAGE);
                        }
                    }
                }

            }

            //云闪付境外，累计限额
            if ((payway == Payway.UNIONPAY.getCode())) {
                Map extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
                String sqbWalletName = MapUtil.getString(extraParams, Transaction.SQB_WALLET_NAME);
                if (ApolloConfigurationCenterUtil.getUnionOverseasWallet().contains(sqbWalletName)) {
                    //校验云闪付境外钱包单笔
                    String unionOverSeasWalletSingleTranLimit = MapUtil.getString(configSnapshot, TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT);
                    if (!Objects.isNull(unionOverSeasWalletSingleTranLimit)) {
                        long limtAmount = StringUtils.yuan2cents(unionOverSeasWalletSingleTranLimit);
                        if (limtAmount < BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
                            throw new TranLimitError(UpayErrorScenesConstant.UPAY_DAY_SINGLE_OVER_LIMIT_MESSAGE);
                        }
                    }

                    //校验云闪付境外钱包当日
                    String unionOverSeasWalletDayTranLimit = MapUtil.getString(configSnapshot, TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT);
                    if (!Objects.isNull(unionOverSeasWalletDayTranLimit)) {
                        long limit = StringUtils.yuan2cents(unionOverSeasWalletDayTranLimit);
                        long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
                        String bizId = merchantId + ":" + payway + ":" + TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT;
                        long todayPaymentTotal = paymentSumService.get(bizId, System.currentTimeMillis());
                        if (todayPaymentTotal + amount > limit) {
                            logger.debug("OverMerchantDailyPaywayMaxSumOfTrans: merchant_id={}, original_amount={}, limit={}, payway={}", merchantId, amount, limit, payway);
                            throw new TranLimitError(UpayErrorScenesConstant.UPAY_DAY_PAYWAY_OVER_LIMIT_MESSAGE);
                        }
                    }
                }
            }
        }
    }

    /**
     * 校验范围：所有交易
     * 校验内容：单笔交易限额 根据配置的merchant_config 的 merchant_single_max_of_tran 来进行限额
     */
    private void checkSingleTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider) {
        Map<String, Object> tradeParams = provider.getTradeParams(transaction);
        boolean liquidationNextDay = BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
        //校验单笔交易限额
        Object merchantSingleMaxOfTran = BeanUtil.getProperty(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN);
        if (merchantSingleMaxOfTran != null) {
            if (merchantSingleMaxOfTran instanceof String && liquidationNextDay) {
                long limit = StringUtils.yuan2cents(merchantSingleMaxOfTran.toString());
                long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
                if (amount > limit) {
                    logger.debug("merchant {} over single_tran_limit ,origin_amount {}  limit {}", merchantId, amount, limit);
                    throw new UpaySingleTradeOverLimitError(UpayErrorScenesConstant.UPAY_SINGLE_TRADE_OVER_LIMIT_MESSAGE);
                }
            } else if (merchantSingleMaxOfTran instanceof Map) {
                int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
                String limit = BeanUtil.getPropString(merchantSingleMaxOfTran, String.format("%s.%s", payway, subPayway));
                if (!StringUtil.empty(limit)) {
                    long limtAmount = new BigDecimal(limit).multiply(BigDecimal.valueOf(100)).longValue();
                    if (limtAmount < BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT)) {
                        logger.info("merchant single limit, merchantSn = {}, storeSn = {}, terminalSn = {}, payAmount = {}, limitAmount = {}",
                                BeanUtil.getPropString(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.MERCHANT_SN),
                                BeanUtil.getPropString(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.STORE_SN),
                                BeanUtil.getPropString(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_SN),
                                BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT), limtAmount
                        );
                        UpaySingleTradeOverLimitError bizError = UpayErrorScenesConstant.MERCHANT_SING_LELIMT_ERROR.get(String.format("%s.%s", payway, subPayway));
                        if (null == bizError) {
                            bizError = new UpaySingleTradeOverLimitError(UpayErrorScenesConstant.UPAY_SINGLE_TRADE_OVER_LIMIT_MESSAGE);
                        }
                        throw bizError;
                    }
                }
            }
        }
    }

    /**
     * 校验范围：所有交易
     * 校验内容：信用交易额度：商户层级 和 商户+payway 层级的校验
     */
    private void checkCreditTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider) {
        String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        Integer payway = MapUtil.getInteger(transaction, Transaction.PAYWAY);

        //校验信用交易限制 如果当前金额 + 累计信用额度 超过限额， 不需要管当前是不是信用交易，直接添加禁用参数。
        String merchantCreditDailyLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_DAILY_MAX_CREDIT_LIMIT_TRANS);
        disableMerchantCreditTradeIfNeed(merchantCreditDailyLimit, PaymentSumService.DAILY_LIMIT_PREFIX + merchantId, PaymentSumService.TYPE_DAY, configSnapshot);

        String merchantCreditMonthlyLimit = MapUtil.getString(configSnapshot, TransactionParam.MERCHANT_MONTHLY_MAX_CREDIT_LIMIT_TRANS);
        disableMerchantCreditTradeIfNeed(merchantCreditMonthlyLimit, PaymentSumService.MONTHLY_LIMIT_PREFIX + merchantId, PaymentSumService.TYPE_MONTH, configSnapshot);

        //校验商户 日payway 信用限额
        Map paywayDayCreditLimitMap = MapUtil.getMap(configSnapshot, TransactionParam.PAYWAY_DAY_CREDIT_LIMITS);
        int creditPayway = payway;
        if (payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_ALIPAY2) {
            creditPayway = Order.PAYWAY_ALIPAY2;
        }
        String merchantPaywayDayCreditLimit = MapUtil.getString(paywayDayCreditLimitMap, creditPayway + "");
        disableMerchantCreditTradeIfNeed(merchantPaywayDayCreditLimit, PaymentSumService.DAILY_LIMIT_PAYWAY_PREFIX + merchantId + "|" + creditPayway, PaymentSumService.TYPE_DAY, configSnapshot);

        //校验商户payway 月信用限额
        Map paywayMonthCreditLimitMap = MapUtil.getMap(configSnapshot, TransactionParam.PAYWAY_MONTH_CREDIT_LIMITS);
        String merchantPaywayMonthCreditLimit = MapUtil.getString(paywayMonthCreditLimitMap, creditPayway + "");
        disableMerchantCreditTradeIfNeed(merchantPaywayMonthCreditLimit, PaymentSumService.MONTHLY_LIMIT_PAYWAY_PREFIX + merchantId + "|" + creditPayway, PaymentSumService.TYPE_MONTH, configSnapshot);
    }

    /**
     * 校验范围：所有交易
     * 校验内容：根据trade_app_id 业务上校验
     */
    private void checkTradeAppIdTradingLimit(Map<String, Object> transaction, MpayServiceProvider provider){
        String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
        Map configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP);

        long amount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
        if (Objects.equals(tradeApp, ApolloConfigurationCenterUtil.getPhonePosTradeAppId())) {
            //check单笔限额
            String singleTranLimitAmountYuan = MapUtil.getString(configSnapshot, TransactionParam.PHONE_POS_SINGLE_TRAN_LIMIT);
            if (!StringUtils.isEmpty(singleTranLimitAmountYuan)) {
                long singleTranLimitAmountCents = StringUtils.yuan2cents(singleTranLimitAmountYuan);
                if (amount > singleTranLimitAmountCents) {
                    logger.debug("phone pos: merchant {} over single_tran_limit ,origin_amount {}  limit {}", merchantId, amount, singleTranLimitAmountCents);
                    throw new UpaySingleTradeOverLimitError(UpayErrorScenesConstant.UPAY_SINGLE_TRADE_OVER_LIMIT_MESSAGE);
                }
            }
            String dayTranLimitAmountYuan = MapUtil.getString(configSnapshot, TransactionParam.PHONE_POS_DAY_TRAN_LIMIT);
            if(!StringUtils.isEmpty(dayTranLimitAmountYuan)){
                long dayTranLimitAmount = StringUtils.yuan2cents(dayTranLimitAmountYuan);
                long merchantCurrentTranAmountTotal = paymentSumService.get(PaymentSumService.DAILY_LIMIT_PHONE_POS_PREFIX + merchantId, System.currentTimeMillis(), PaymentSumService.TYPE_DAY);
                if (merchantCurrentTranAmountTotal + amount > dayTranLimitAmount) {
                    throw new TranLimitError(UpayErrorScenesConstant.UPAY_DAY_PAYWAY_OVER_LIMIT_MESSAGE);
                }
            }
        }
    }


    private void throwExceptionIfMoreThanBankcardLimit(Long amount, String bankCardLimit, String bizId, int type, int bankCardLimitType) {
        if(StringUtils.isEmpty(bankCardLimit)){
            return ;
        }
        //Integer.MAX_VALUE 视为无限额，不进行校验
        if(bankCardLimit.equals(TransactionParam.MERCHANT_TRANS_UNLIMITED)){
            return ;
        }
        long merchantBankCardLimit = StringUtils.yuan2cents(bankCardLimit);
        long merchantBankCardTotal = paymentSumService.get(bizId, System.currentTimeMillis(), type);
        if (merchantBankCardTotal + amount >= merchantBankCardLimit) {
            logger.debug("merchant {} bank card over limit {}, bankcard total {}, current trade amount is {}", bizId, merchantBankCardLimit, merchantBankCardTotal, amount);
            if(bankCardLimitType == UpayConstant.DAY_MAX_BANKCARD_LIMIT) {
                throw new TranLimitError(UpayErrorScenesConstant.UPAY_DAY_BANKCARD_OVER_LIMIT_MESSAGE);
            } else if(bankCardLimitType == UpayConstant.MONTH_MAX_BANKCARD_LIMIT) {
                throw new TranLimitError(UpayErrorScenesConstant.UPAY_MONTH_BANKCARD_OVER_LIMIT_MESSAGE);
            }
        }
    }

    private void disableMerchantCreditTradeIfNeed(String creditLimit, String bizId, int type, Map config) {
        if (!Objects.isNull(creditLimit)) {
            long merchantCreditLimit = StringUtils.yuan2cents(creditLimit);
            long merchantCreditTradeTotal = paymentSumService.get(bizId, System.currentTimeMillis(), type);
            if (merchantCreditTradeTotal >= merchantCreditLimit) {
                //禁止信用交易
                config.put(TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_DISABLE);
            }
        }
    }

}
