package com.wosai.upay.service;

import com.lark.chatbot.LarkChatbotClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.lark.chatbot.SendResult;
import com.lark.chatbot.message.Message;
import com.lark.chatbot.message.TextMessage;
import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.market.user.dto.sqbmp.UcUserDTO;
import com.wosai.market.user.service.SqbMpService;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.constant.ErrorMessageConstant;
import com.wosai.profit.sharing.exception.ProfitSharingException;
import com.wosai.profit.sharing.model.SharingBook;
import com.wosai.profit.sharing.model.SharingTransaction;
import com.wosai.profit.sharing.model.request.BeforeSharingRestituteReq;
import com.wosai.profit.sharing.model.request.CancelSharingReq;
import com.wosai.profit.sharing.model.request.SharingRestituteReq;
import com.wosai.profit.sharing.model.request.ValidateAndUpdateProfitSharingRequest;
import com.wosai.profit.sharing.model.response.BeforeSharingRestituteResult;
import com.wosai.profit.sharing.model.response.SharingRestituteResult;
import com.wosai.profit.sharing.service.SharingService;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.service.MiniAppsOpenService;
import com.wosai.sharing.proxy.model.request.BeforeRefundCheckRequest;
import com.wosai.sharing.proxy.service.ProxySharingService;
import com.wosai.upay.activity.model.Activity;
import com.wosai.upay.activity.model.ActivityOrder;
import com.wosai.upay.activity.model.constant.ActivityBusinessField;
import com.wosai.upay.activity.model.constant.ActivityConstant;
import com.wosai.upay.activity.service.ActivityUpayService;
import com.wosai.upay.brandsettle.exception.BrandSettleException;
import com.wosai.upay.brandsettle.exception.enums.BrandSettleRespCodeEnum;
import com.wosai.upay.brandsettle.request.RefundValidateAndFreezeWalletRequest;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.meta.ClearanceProvider;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.Agent;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.ProfitSharing;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.ExternalServiceFacade.TcpPayResult.TcpPay;
import com.wosai.upay.service.ExternalServiceFacade.TcpRefundResult.TcpRefund;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.FakeRequestUtil;
import com.wosai.upay.util.LanguageCaseHolder;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.wallet.constant.ProviderWalletAccountTypeEnum;
import com.wosai.upay.wallet.exception.WalletBalanceFreezeConflictException;
import com.wosai.upay.wallet.exception.WalletBalanceNotEnoughException;
import com.wosai.upay.wallet.exception.WalletInvalidParameterException;
import com.wosai.upay.wallet.request.UnFreezeWalletBalanceReq;
import com.wosai.upay.wallet.request.WalletQueryReq;
import com.wosai.upay.wallet.request.v3.FreezeWalletBalanceReqV3;
import com.wosai.upay.wallet.service.WalletServiceV3;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.TransactionContext;
import com.wosai.web.api.exception.logical.ParameterValidationException;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ExternalServiceFacade {
    private static final Logger logger = LoggerFactory.getLogger(ExternalServiceFacade.class);
    private static final String cardIdPath = ActivityBusinessField.CARD + "." + DaoConstants.ID;
    @Autowired
    private SupportService supportService;
    @Autowired
    @Qualifier("activityUpayService")
    private ActivityUpayService discountService;
    @Autowired
    @Qualifier("activityUpayForB2CService")
    private ActivityUpayService discountForB2CService;
    @Autowired
    @Qualifier("activityUpayForLongTimeService")
    private ActivityUpayService discountForLongTimeService;
    @Autowired
    @Qualifier("walletService")
    private WalletServiceV3 walletService;
    @Autowired
    @Qualifier("fakeWalletService")
    private WalletServiceV3 fakeWalletService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private SharingService sharingService;
    @Autowired
    private DataRepository dataRepository;
    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${lark.webhook}")
    private String larkWebhook;
    @Autowired
    private LarkChatbotClient larkChatbotClient;
    @Value("${tran-es-sync.server}")
    private String transEsSyncServe;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TradeAppFacade tradeAppFacade;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SqbMpService sqbMpService;

    @Autowired
    private TerminalService terminalService;
    @Autowired
    private ProxySharingService proxySharingService;

    @Autowired
    private com.wosai.upay.brandsettle.service.SharingService sftSharingService;
    @Value("${jsonrpc.brand-settle.server}")
    private String brandSettleServer;

    @Autowired
    private MiniAppsOpenService miniAppsOpenService;


    // 存储获取参数时的业务错误，1分钟内返回相同的业务错误。减少接口的调用量
    public static final Cache<String,Exception> TERMINAL_GET_PARAMS_EXCEPTION_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(5000)
            .build();

    public static final Cache<String, Map<String, Object>> AGENT_PARAMS_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(10)
            .build();


    private static final String BP_CACHE_KEY = "BP-Cache";
    private static final String CACHE_KEY_REG = "-";
    private static final String GEN_SN_KEY = "Gen-Sn-";
    
    private AtomicLong sharingRestituteCount = new AtomicLong(0); //为了容错，须限制同一时刻做退款回退的次数
    private long maxSharingRestituteCount = 8; // 为了避免线程池被分账回退业务耗尽，最多只能支持这么多并发的退款回退
    private static final Map<String, Long> TICKET_WARING = new ConcurrentHashMap<>();
    private static final int TICKET_WARING_DELAY_TIME = 5 * 60 * 1000;
    private final Semaphore concurrencySemaphore = new Semaphore(20); // 设置活动服务接口3秒超时最大并发数为20
    /**
     * 富友逆向交易设置间连时间点
     */
    private static final long FUYOU_REVERSE_DATE_LINE = DateUtil.parseDate("20240701", DateUtil.FORMATTER_DATE_INT).getTime();

    /**
     * 判断此笔交易是否需要调用活动服务
     * @param config
     * @param activityDiscount
     * @return
     */
    private static boolean isInvokeActivity(Map<String, Object> config, Boolean activityDiscount) {
        boolean invokeActivity = false;
        String commonSwitchStr = MapUtil.getString(config, TransactionParam.COMMON_SWITCH);
        // 喔噻全量活动开关打开了的情形需要调用
        if(ApolloConfigurationCenterUtil.isApplyTradeWosaiCoprocessor()){
            invokeActivity = true;
        }
        // 商户配置有优惠的情形需要调用
        if(UpayUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_MERCHANT_ACTIIVTY)){
            invokeActivity = true;
        }
        // 交易有上送优惠标识的情形需要调用
        if(!Objects.isNull(activityDiscount)){
            invokeActivity = true;
        }
        // 如果活动服务被熔断了（是否调用活动服务开关关闭了）的情形不调用
        if(!ApolloConfigurationCenterUtil.isApplyTradeCoprocessor()){
            invokeActivity = false;
        }
        return invokeActivity;
    }

    public TcpPayResult consultTcpForPay(String wosaiStoreId,
                                         String city,
                                         String sn,
                                         int payway,
                                         int subPayway,
                                         long originalTotal,
                                         String payerUid,
                                         String weixinAppId,
                                         Map<String, Object> config, String barcode, String terminalSn, String clientSn, List<Map> goodsDetail,int isFormal, int clearanceProvider, Object activityBizExt, Boolean activityDiscount) {
        //是否需要执行后续逻辑: true - 需要; false - 直接返回
        boolean isInvokeActivity = isInvokeActivity(config, activityDiscount);
        if (!isInvokeActivity) {
            return TcpPayResult.VOID;
        }

        Map<String,Object> request = CollectionUtil.hashMap(
                ActivityOrder.ORDER_SN, sn,
                ActivityOrder.ORDER_CTIME, new Date().getTime(),
                ActivityOrder.ORIGINAL_TOTAL, originalTotal,
                ActivityOrder.BUYER_UID, payerUid,
                ActivityOrder.PAYWAY, payway,
                ActivityOrder.SUB_PAYWAY, subPayway,
                ActivityOrder.MERCHANT_ID, config.get(TransactionParam.MERCHANT_ID),
                ActivityOrder.STORE_ID, config.get(TransactionParam.STORE_ID),
                ActivityOrder.TERMINAL_SN, terminalSn,
                ActivityOrder.BARCODE, barcode,
                ActivityOrder.WEIXIN_APP_ID,weixinAppId,
                Order.CLIENT_SN,clientSn,
                UpayConstant.GOODS_DETAILS,goodsDetail,
                ActivityOrder.IS_DIRECT_MERCHANT,isFormal,
                ActivityOrder.CLEARANCE_PROVIDER, clearanceProvider,
                UpayConstant.ACTIVITY_BIZ_EXT, activityBizExt,
                TransactionParam.TRADE_APP, MapUtil.getString(config, TransactionParam.TRADE_APP)
        );
        try {
            Map<String,Object> result = getUpayResultV2(request, activityDiscount);
            if (MapUtil.isEmpty(result)) {
                return TcpPayResult.VOID;
            }
            logger.debug("upay activity pay result: {}", result);
            List<String> productFlagList = (List<String>) BeanUtil.getProperty(result, ActivityBusinessField.PRODUCT_FLAG);
            String productFlag = (productFlagList == null || productFlagList.isEmpty()) ? null : com.wosai.pantheon.util.StringUtil.joinC(UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER, productFlagList);
            String marketUserId = MapUtil.getString(result, ActivityOrder.MARKET_USER_ID);
            if (BeanUtil.getPropInt(result, ActivityBusinessField.RESULT_CODE) == ActivityConstant.RESULT_DISCOUNT) {
                long effectiveAmount = BeanUtil.getPropLong(result, ActivityBusinessField.PAY_AMOUNT);
                TcpPayResult tcpPayResult = new TcpPayResult(true, effectiveAmount, productFlag, marketUserId);
                List<Map<String,Object>> redeemResults = (List<Map<String,Object>>) result.get(ActivityBusinessField.REDEEM_RESULT);
                if(redeemResults != null && redeemResults.size() > 0){
                    for (Map<String, Object> redeemResult : redeemResults) {
                        int source = BeanUtil.getPropInt(redeemResult, ActivityBusinessField.SOURCE);
                        int type = BeanUtil.getPropInt(redeemResult, ActivityBusinessField.TYPE);
                        String originaType = BeanUtil.getPropString(redeemResult, ActivityBusinessField.ORIGINAL_TYPE);
                        String originalName = BeanUtil.getPropString(redeemResult, ActivityBusinessField.ORIGINAL_NAME);
                        long redeemAmount = BeanUtil.getPropLong(redeemResult, ActivityBusinessField.AMOUNT);
                        TcpPay tcpInfo = new TcpPay(redeemAmount, originaType, originalName);
                        
                        if(source == Activity.TYPE_MERCHANT){
                            if(type == ActivityConstant.REDEEM_TYPE_CASH){
                                tcpPayResult.setDiscountWosaiMchAmount(tcpPayResult.getDiscountWosaiMchAmount() + redeemAmount);
                                tcpInfo.setSource(BeanUtil.getPropString(redeemResult, ActivityBusinessField.ACTIVITY_SN));
                                tcpPayResult.getDiscountWosaiMch().add(tcpInfo);
                            }else if(type == ActivityConstant.REDEEM_TYPE_VOUCHER_CARD){
                                tcpPayResult.setHongbaoWosaiMchAmount(tcpPayResult.getHongbaoWosaiMchAmount() + redeemAmount);
                                tcpInfo.setSource((String) BeanUtil.getNestedProperty(redeemResult, cardIdPath));
                                tcpPayResult.getHongbaoWosaiMch().add(tcpInfo);
                            }
                        }else if(source == Activity.TYPE_SYSTEM){
                            if(type == ActivityConstant.REDEEM_TYPE_CASH){
                                tcpPayResult.setDiscountWosaiAmount(tcpPayResult.getDiscountWosaiAmount() + redeemAmount);
                                tcpInfo.setSource(BeanUtil.getPropString(redeemResult, ActivityBusinessField.ACTIVITY_SN));
                                tcpPayResult.getDiscountWosai().add(tcpInfo);

                            }else if(type == ActivityConstant.REDEEM_TYPE_VOUCHER_CARD){
                                tcpPayResult.setHongbaoWosaiAmount(tcpPayResult.getHongbaoWosaiAmount() + redeemAmount);
                                tcpInfo.setSource((String) BeanUtil.getNestedProperty(redeemResult, cardIdPath));
                                tcpPayResult.getHongbaoWosai().add(tcpInfo);
                            }
                        }
                    }

                }
                if(originalTotal != (effectiveAmount + tcpPayResult.getDiscountWosaiAmount() + tcpPayResult.getDiscountWosaiMchAmount() 
                                        + tcpPayResult.getHongbaoWosaiAmount() + tcpPayResult.getHongbaoWosaiMchAmount()) 
                        || effectiveAmount < 0 || effectiveAmount > originalTotal){
                    logger.error("upay activity calculate amount abnormal to order {} when pay, result:{}", sn, result);
                    return TcpPayResult.VOID;
                }
                return tcpPayResult;
            }else{
                return new TcpPayResult(false, 0, productFlag, marketUserId);
            }
        }catch (Throwable ex) {
            logger.error(String.format("Failed to apply upay activity to order %s.", sn), ex);
        }
        return TcpPayResult.VOID;
    }

    private Map getUpayResultV2(Map<String,Object> request, Boolean activityDiscount){

        int subPayway = MapUtil.getIntValue(request, ActivityOrder.SUB_PAYWAY);
        //1、若未上送该字段，则按照现有规则请求
        //2、若上送该字段为false， 表明无优惠，则不请求活动服务
        //3、若上送该字段为true， 表明有优惠，请求活动服务，接口超时设置为3s, 且单节点设置并发数为20，超过该并发则按现有规则请求
        if (Objects.isNull(activityDiscount)) {
            return  Order.SUB_PAYWAY_BARCODE == subPayway ? discountForB2CService.getUpayResultV2(request) : discountService.getUpayResultV2(request);
        } else if (Boolean.TRUE.equals(activityDiscount)) {
            if (concurrencySemaphore.tryAcquire()) {
                try {
                    return discountForLongTimeService.getUpayResultV2(request);
                } finally {
                    concurrencySemaphore.release(); // 释放许可
                }
            } else {
                return  Order.SUB_PAYWAY_BARCODE == subPayway ? discountForB2CService.getUpayResultV2(request) : discountService.getUpayResultV2(request);
            }

        }
        return null;
    }

    public TcpRefundResult consultTcpForRefund(String sn, long refundOriginalAmount) {
        if (ApolloConfigurationCenterUtil.isApplyTradeCoprocessor()) {
            Map<String,Object> request = new HashMap<>();
            request.put(ActivityBusinessField.ORDER_SN, sn);
            request.put(ActivityBusinessField.REFUND_AMOUNT, refundOriginalAmount);
            try {
                Map<String,Object> result = discountService.getUpayRefundResultV2(request);
                logger.debug("upay activity refund result: {}", result);
                if (BeanUtil.getPropInt(result, ActivityBusinessField.RESULT_CODE) == ActivityConstant.RESULT_CODE_REFUND_PERMIT) {
                    long effectiveAmount = BeanUtil.getPropLong(result, ActivityBusinessField.REFUND_AMOUNT);
                    TcpRefundResult tcpRefundResult = new TcpRefundResult(false, effectiveAmount);
                    List<Map<String,Object>> redeemResults = (List<Map<String, Object>>) result.get(ActivityBusinessField.REDEEM_RESULT);
                    if(redeemResults != null && redeemResults.size() > 0){
                        for (Map<String, Object> redeemResult : redeemResults) {
                            int source = BeanUtil.getPropInt(redeemResult, ActivityBusinessField.SOURCE);
                            int type = BeanUtil.getPropInt(redeemResult, ActivityBusinessField.TYPE);
                            String originaType = BeanUtil.getPropString(redeemResult, ActivityBusinessField.ORIGINAL_TYPE);
                            String originalName = BeanUtil.getPropString(redeemResult, ActivityBusinessField.ORIGINAL_NAME);
                            long redeemAmount = BeanUtil.getPropLong(redeemResult, ActivityBusinessField.AMOUNT);
                            TcpRefund tcpInfo = new TcpRefund(redeemAmount, originaType, originalName);
                            
                            if(source == Activity.TYPE_MERCHANT){
                                if(type == ActivityConstant.REDEEM_TYPE_CASH){
                                    tcpRefundResult.setDiscountWosaiMchAmount(tcpRefundResult.getDiscountWosaiMchAmount() + redeemAmount);
                                    tcpInfo.setSource(BeanUtil.getPropString(redeemResult, ActivityBusinessField.ACTIVITY_SN));
                                    tcpRefundResult.getDiscountWosaiMch().add(tcpInfo);
                                }else if(type == ActivityConstant.REDEEM_TYPE_VOUCHER_CARD){
                                    tcpRefundResult.setHongbaoWosaiMchAmount(tcpRefundResult.getHongbaoWosaiMchAmount() + redeemAmount);
                                    tcpInfo.setSource((String) BeanUtil.getNestedProperty(redeemResult, cardIdPath));
                                    tcpRefundResult.getHongbaoWosaiMch().add(tcpInfo);
                                }
                            }else if(source == Activity.TYPE_SYSTEM){
                                if(type == ActivityConstant.REDEEM_TYPE_CASH){
                                    tcpRefundResult.setDiscountWosaiAmount(tcpRefundResult.getDiscountWosaiAmount() + redeemAmount);
                                    tcpInfo.setSource(BeanUtil.getPropString(redeemResult, ActivityBusinessField.ACTIVITY_SN));
                                    tcpRefundResult.getDiscountWosai().add(tcpInfo);

                                }else if(type == ActivityConstant.REDEEM_TYPE_VOUCHER_CARD){
                                    tcpRefundResult.setHongbaoWosaiAmount(tcpRefundResult.getHongbaoWosaiAmount() + redeemAmount);
                                    tcpInfo.setSource((String) BeanUtil.getNestedProperty(redeemResult, cardIdPath));
                                    tcpRefundResult.getHongbaoWosai().add(tcpInfo);
                                }
                            }
                        }

                    }
                    
                    if(refundOriginalAmount != (effectiveAmount + tcpRefundResult.getDiscountWosaiAmount() + tcpRefundResult.getDiscountWosaiMchAmount()
                                + tcpRefundResult.getHongbaoWosaiAmount() + tcpRefundResult.getHongbaoWosaiMchAmount())
                            || effectiveAmount < 0 || effectiveAmount > refundOriginalAmount){
                        logger.error("upay activity calculate amount abnormal to order {} when refund, result {}", sn, result);
                        throw new UpayBizException(UpayErrorScenesConstant.UPAY_ORDER_AMOUNT_NOT_MATCH, UpayErrorScenesConstant.UPAY_ORDER_AMOUNT_NOT_MATCH_MESSAGE);
                    }
                    return tcpRefundResult;
                }else{
                    return new TcpRefundResult(true, 0, BeanUtil.getPropString(result, ActivityBusinessField.REFUND_MSG));
                }
            }
            catch (Exception ex) {
            	if (ex.getClass().getSimpleName().equals("HttpException")){
            		throw new ExternalServiceException(UpayErrorScenesConstant.UPAY_TCP_ORDER_CANNOT_REFUND, UpayErrorScenesConstant.UPAY_TCP_ORDER_CANNOT_REFUND_MESSAGE, ex);
                }else {
                    throw new ExternalServiceException(UpayErrorScenesConstant.UPAY_TCP_ORDER_CANNOT_REFUND, ex.getMessage(), ex);
                }
            }
            
        }else{
            throw new ExternalServiceException(UpayErrorScenesConstant.UPAY_TCP_CLOSE, UpayErrorScenesConstant.UPAY_TCP_CLOSE_MESSAGE);
        }
    }
    
    public Map<String, Object> getBasicParams(String wosaiStoreId, String terminalSn) {
        try {
            Exception existsException = getTerminalGetParamsException(wosaiStoreId, terminalSn, null, null, null);
            if(existsException != null){
                throw existsException;
            }

            // 先从缓存中获取商户基础信息
            String key = StringUtils.join(BP_CACHE_KEY, CACHE_KEY_REG, wosaiStoreId, CACHE_KEY_REG, terminalSn);
            try{
                String content = (String)redisTemplate.boundValueOps(key).get();
                if(!StringUtil.empty(content)){
                    Map basicParams = JsonUtil.jsonStrToObject(content, Map.class);
                    //交易语言参数保存在线程上线文中
                    LanguageCaseHolder.setLanguageCase(basicParams);
                    return basicParams;
                }
            }catch (Exception e){
                logger.error("redis error", e);
            }

            Map<String, Object> context;
            try{
                context = supportService.getBasicParams(wosaiStoreId, terminalSn);
                //交易语言参数保存在线程上线文中
                LanguageCaseHolder.setLanguageCase(context);
            }catch (Exception e){
                setTerminalGetParamsExceptionIfNeeded(wosaiStoreId, terminalSn, null, null, null, e);
                throw e;
            }
            
            // 交易配置3分钟后过期，避免core-b更新商户配置后无法生效
            try{
                if(null != context && context.size() > 0){
                    redisTemplate.boundValueOps(key).set(JsonUtil.toJsonStr(context), 3, TimeUnit.MINUTES);
                }
            }catch (Exception e){
                logger.error("redis error", e);
            }
            
            return context;
        }catch(Exception ex) {
            throw translateCoreBusinessException(ex);
        }
    }

    public Map<String, Object> getAllParamsWithTradeApp(String wosaiStoreId, String terminalSn, int payway, int subPayway, String tradeApp, String bizModel){
        tradeApp = StringUtil.empty(tradeApp) ? TransactionParam.TRADE_APP_BASIC_PAY : tradeApp;
        // 某些业务场景，需要根据配置转换为新的tradeApp去获取交易参数，比如多功能收银线上
        String convertedTradeApp = tradeAppFacade.getConvertedTradeAppByBizModel(tradeApp, bizModel);
        if(convertedTradeApp == null){
            // 不需要转换，直接用原始的tradeApp
            return getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
        }
        Map<String, Object> config = getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, convertedTradeApp, bizModel);
        //改写交易参数里面的trade_app
        if(config != null){
            config.put(TransactionParam.TRADE_APP, tradeApp);
        }
        return config;
    }

    public Map<String, Object> getAllParamsWithTradeApp(String wosaiStoreId, String terminalSn, int payway, int subPayway, String tradeApp) {
        try {
            Exception existsException = getTerminalGetParamsException(wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
            if(existsException != null){
                throw existsException;
            }

            Map<String, Object> config;
            try{
                config = supportService.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
                //交易语言参数保存在线程上线文中
                LanguageCaseHolder.setLanguageCase(config);
            }catch (Exception e){
                setTerminalGetParamsExceptionIfNeeded(wosaiStoreId, terminalSn, payway, subPayway, tradeApp, e);
                throw e;
            }
            // 设置商户基础信息缓存,需要剔除掉交易参数相关的部分
            if(null != config){
                String redisKey = StringUtils.join(BP_CACHE_KEY, CACHE_KEY_REG, wosaiStoreId, CACHE_KEY_REG, terminalSn);
                try{
                    if(StringUtil.empty((String)redisTemplate.boundValueOps(redisKey).get())){
                        // 交易配置3分钟后过期，避免core-b更新商户配置后无法生效
                        Map cacheInfo = new HashMap<>();
                        for (String key : config.keySet()) {
                            if(null != config.get(key)
                                    && !key.contains("trade_param")){
                                cacheInfo.put(key, config.get(key));
                            }
                        }
                        redisTemplate.boundValueOps(redisKey).set(JsonUtil.toJsonStr(cacheInfo), 3, TimeUnit.MINUTES);
                    }
                }catch (Exception e){
                    logger.error("redis error", e);
                }
            }
            
            return config;
        }catch(Exception ex) {
            throw translateCoreBusinessException(ex, payway);
        }
    }

    public int getClearanceProvider(String merchantId){
        try {
            return supportService.getClearanceProvider(merchantId);
        }catch (Exception ex){
            throw translateCoreBusinessException(ex);
        }
    }

    public Map<String,Object> validateAndGetUpdatedProfitSharing(String merchantId, String storeId, Map<String, Object> profitSharing, Map<String,Object> transaction, String tradeApp){
        Integer organization = null;
        boolean isChargeTradeApp = (!StringUtil.empty(tradeApp) && !TransactionParam.TRADE_APP_BASIC_PAY.equals(tradeApp));
        if (isChargeTradeApp) {
            // 部分银行通道未配置clearance_provider，需要做下特殊处理
            Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
            if (provider == null 
                    || Provider.LAKALA.getCode().equals(provider)
                    || Provider.UNION_PAY_DIRECT.getCode().equals(provider)
                    || Provider.UNION_PAY_OPEN.getCode().equals(provider)
                    || Provider.LAKALA_UNION_PAY.getCode().equals(provider)
                    || Provider.LAKALA_UNION_PAY_V3.getCode().equals(provider)
                    || Provider.TONG_LIAN.getCode().equals(provider)
                    || Provider.TL_SYB.getCode().equals(provider)
                    || Provider.HAIKE_UNION_PAY.getCode().equals(provider)
                    || Provider.FUYOU.getCode().equals(provider)) {
                organization = UpayProfitSharingUtil.getOrganizationByTransaction(transaction);
            }
        } else {
            organization = UpayProfitSharingUtil.getOrganizationByTransaction(transaction);
        }
        if(organization == null){
            // 非支付业务交易时，通道不支持分账时，不进行分账
            if (!StringUtil.empty(tradeApp) && !TransactionParam.TRADE_APP_BASIC_PAY.equals(tradeApp)) {
                logger.warn("provider {} can't sharing", MapUtil.getInteger(transaction, Transaction.PROVIDER));
                return null;
            }

            if (profitSharing.get(ProfitSharing.RECEIVERS) == null && profitSharing.containsKey(ProfitSharing.CHARGE)) {
                List<Map<String, Object>> charges = (List)profitSharing.get(ProfitSharing.CHARGE);
                // 储值充值特殊逻辑，如果当前只存在储值充值时，不进行分账；交易时有上送业务通道，当该通道不支持分账时，不进行分账
                if (com.wosai.pantheon.util.CollectionUtil.isNotEmpty(charges)) {
                    if (isChargeTradeApp
                            || (charges.size() == 1
                                    && Objects.equals(MapUtil.getString(charges.get(0), ProfitSharing.CHARGE_FLAG), ProfitSharing.CHARGE_FLAG_CARDRECHARGE))) {
                        logger.warn("provider {} can't sharing, charge info: {}", MapUtil.getInteger(transaction, Transaction.PROVIDER), charges);
                        return null;
                    }
                } else {
                    return null;
                }
            }
            throw new ExternalServiceException(UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT, UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT_MESSAGE);
        }
        try{
            ValidateAndUpdateProfitSharingRequest request = new ValidateAndUpdateProfitSharingRequest();
            request.setMerchantId(merchantId);
            request.setStoreId(storeId);
            request.setProfitSharing(profitSharing);
            request.setOrganization(organization);
            request.setTradeApp(tradeApp);
            return sharingService.validateAndUpdateProfitSharing(request);
        }catch (Exception ex){
            throw translateProfitSharingException(ex);
        }
    }

    public Map<String,Object> validateAndGetUpdatedSFTProfitSharing(String sftBrandId, String merchantId, String storeId, Map<String, Object> profitSharing, Map<String,Object> transaction){
        try{
            com.wosai.upay.brandsettle.request.ValidateAndUpdateProfitSharingRequest request = new com.wosai.upay.brandsettle.request.ValidateAndUpdateProfitSharingRequest();
            request.setBrandId(sftBrandId);
            request.setMerchantId(merchantId);
            request.setStoreId(storeId);
            if (profitSharing != null)
                request.setProfitSharing(JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(profitSharing), com.wosai.upay.brandsettle.request.ProfitSharing.class));

            return sftSharingService.validateAndUpdateProfitSharing(request);
        }catch (Exception ex){
            throw translateProfitSharingException(ex);
        }
    }

    /**
     * 获取商户自动分账配置
     * @param merchantId
     * @param transaction
     * @return
     */
    public Map<String,Object> getProfitSharingForAutoConfigSharing(String merchantId, Map<String,Object> transaction){
        try{
            Map<String,Object> config = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
            int sharingStatus = BeanUtil.getPropInt(config, TransactionParam.SHARING_SWITCH, TransactionParam.STATUS_CLOSED);
            if(sharingStatus != TransactionParam.STATUS_OPENED){
                //分账开关没有打开，则不调用分账服务获取自动的分账配置
                return null;
            }
            Integer organization = UpayProfitSharingUtil.getOrganizationByTransaction(transaction);
            if(organization == null){
                return null;
            }
            return sharingService.getProfitSharingForAutoConfigSharing(merchantId, organization);
        }catch (Exception ex){
            throw translateProfitSharingException(ex);
        }
    }

    /**
     * 判断此次分账是否需要分账回退
     * @param orderSn
     * @return
     */
    public ImmutablePair<Boolean, Map<String, Object>> isNeedSharingRestitute(Map<String, Object> payTransaction, String orderSn, long originalAmount, long refundedAmount, Map<String, Object> refundProfitSharing) {
        try{
            Map<String,Object> extraParams = MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS);
            Map<String,Object> profitSharing = (Map<String, Object>) MapUtil.getMap(extraParams, Transaction.PROFIT_SHARING);
            String sharingApp = MapUtil.getString(profitSharing, Transaction.PROFIT_SHARING_SHARING_APP, Transaction.SHARING_APP_PAY);
            if (Objects.equals(sharingApp, Transaction.SHARING_APP_PAY)) {
                return isNeedPaySharingRestitute(orderSn, originalAmount, refundedAmount, refundProfitSharing, payTransaction);

            } else if (Objects.equals(sharingApp, Transaction.SHARING_APP_SFT)){
                String brandId = MapUtil.getString(MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.SFT_BRAND_ID);
                return isNeedSftSharingRestitute(brandId, orderSn, originalAmount, refundedAmount, refundProfitSharing);
            }
            return ImmutablePair.of(false, null);
        }catch (Exception e){
            throw translateProfitSharingException(e);
        }
    }

    private ImmutablePair<Boolean, Map<String, Object>> isNeedSftSharingRestitute(String brandId, String orderSn, long originalAmount, long refundedAmount, Map<String, Object> refundProfitSharing) {
        com.wosai.upay.brandsettle.request.BeforeSharingRestituteReq req = new com.wosai.upay.brandsettle.request.BeforeSharingRestituteReq();
        req.setOrderSn(orderSn);
        req.setBrandId(brandId);
        req.setRefundProfitSharing(JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(refundProfitSharing), com.wosai.upay.brandsettle.request.ProfitSharing.class));
        req.setOriginalAmount(originalAmount);
        req.setRefundedOriginalAmount(refundedAmount);
        com.wosai.upay.brandsettle.response.BeforeSharingRestituteResult resp = sftSharingService.getInfoBeforeSharingRestitute(req);
        if (resp == null) {
            return ImmutablePair.of(false, null);
        }
        return ImmutablePair.of(resp.getIsRestitute(), JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(resp.getProfitSharing()), Map.class));
    }

    private ImmutablePair<Boolean, Map<String, Object>> isNeedPaySharingRestitute(String orderSn, long originalAmount, long refundedAmount, Map<String, Object> refundProfitSharing, Map<String, Object> payTransaction) {
        BeforeSharingRestituteReq req = new BeforeSharingRestituteReq();
        req.setOrderSn(orderSn);
        req.setRefundProfitSharing(refundProfitSharing);
        req.setOriginalAmount(originalAmount);
        req.setRefundedOriginalAmount(refundedAmount);
        req.setProvider(MapUtil.getInteger(payTransaction, Transaction.PROVIDER));
        req.setPayCtime(MapUtil.getLongValue(payTransaction, DaoConstants.CTIME));
        req.setPayway(MapUtil.getInteger(payTransaction, Transaction.PAYWAY));
        BeforeSharingRestituteResult resp = sharingService.getInfoBeforeSharingRestitute(req);
        if (resp == null) {
            return ImmutablePair.of(false, null);
        }
        return ImmutablePair.of(resp.getRestitute(), resp.getProfitSharing());

    }

    public void setTerminalGetParamsExceptionIfNeeded(String wosaiStoreId, String terminalSn, Integer payway, Integer subPayway, String tradeApp, Exception ex){
        if (ex instanceof CoreTerminalNotExistsException
                || ex instanceof CoreTerminalNotActivatedException
                || ex instanceof CoreStoreNotExistsException
                || ex instanceof CoreStoreNotOnlineException
                || ex instanceof CoreStoreStatusAbnormalException
                || ex instanceof CoreMerchantNotExistsException
                || ex instanceof CoreMerchantStatusAbnormalException
        ){
            String key = StringUtil.join(CACHE_KEY_REG, wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
            TERMINAL_GET_PARAMS_EXCEPTION_CACHE.put(key, ex);
            // payway subPayway有值时，表明获取allParams发生错误, 此时同时设置获取basicParams时的错误。
            if(payway != null && subPayway != null){
                String basicParamsExceptionKey = StringUtil.join(CACHE_KEY_REG, wosaiStoreId, terminalSn, null, null, null);
                TERMINAL_GET_PARAMS_EXCEPTION_CACHE.put(basicParamsExceptionKey, ex);
            }
        }
    }

    public Exception getTerminalGetParamsException(String wosaiStoreId, String terminalSn, Integer payway, Integer subPayway, String tradeApp){
        String key = StringUtil.join(CACHE_KEY_REG, wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
        return TERMINAL_GET_PARAMS_EXCEPTION_CACHE.getIfPresent(key);
    }

    @Cacheable("RsaKeyData")
    public String getRsaKeyDataById(String keyId) {
        try {
            String data = supportService.getRsaKeyDataById(keyId);
            return data;
        }catch(Exception ex) {
            throw translateCoreBusinessException(ex);
        }
    }

    public Map getAlipayV2AppAuthInfo(String authAppId, String storeId){
        try {
            Map data = supportService.getAlipayV2AppAuthInfo(authAppId, storeId);
            if(data == null || data.isEmpty()){
                throw new Exception("获取支付宝授权token失败");
            }
            return data;
        }catch(Exception ex) {
            throw translateCoreBusinessException(ex);
        }
    }

    public Map getMerchantInfo(String merchantId){
        try {
            Map merchant = merchantService.getMerchant(merchantId);
            if(merchant == null || merchant.isEmpty()){
                throw new Exception("获取商户信息失败失败");
            }
            return merchant;
        }catch(Exception ex) {
            throw translateCoreBusinessException(ex);
        }
    }

    public void sharingRestitute(TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        if(!UpayProfitSharingUtil.isPayProfitSharingTransaction(transaction)){
            return;
        }

        try{
            long currCount = sharingRestituteCount.incrementAndGet();
            if(currCount > maxSharingRestituteCount){
                throw new ExternalServiceException("分账业务系统繁忙");
            }
            doSharingRestitute(context);

        }finally {
            sharingRestituteCount.decrementAndGet();
        }
    }

    public void doSharingRestitute(TransactionContext context){
        Map<String,Object> transaction = context.getTransaction();
        SharingRestituteReq req = new SharingRestituteReq();
        String clientSn = BeanUtil.getPropString(transaction, DaoConstants.ID);
        Map<String, Object> refundProfitSharing = MapUtil.getMap(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.PROFIT_SHARING);
        req.setOrderSn(BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
        req.setClientSn(clientSn);
        req.setOriginalAmount(BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT));
        req.setClientTsn(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
        req.setRefundProfitSharing(refundProfitSharing);
        req.setCtime(MapUtil.getLongValue(transaction, DaoConstants.CTIME));
        req.setEffectiveAmount(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
        SharingRestituteResult result = null;
        try{
            result = sharingService.sharingRestitute(req);
        }catch (ProfitSharingException e){
            if(e.getMessage() != null && e.getMessage().equals(ErrorMessageConstant.SHARING_RESTITUTE_SHARING_PAY_STATUS_IS_CLOSE)){
                //原分账失败已关闭，则回退默认当做成功
                return;
            }else{
                throw e;
            }
        }
        // 设置分账回退金额
        resetTransactionProfitSharing(refundProfitSharing, result.getSharingBooks());
        Integer status = result.getSharingRestituteStatus();
        if(status == SharingTransaction.STATUS_SUCCESS || status == SharingTransaction.STATUS_FREEZE_SUCC){
            return;
        }else{
            if(SharingTransaction.FAIL_OR_ERROR_STATUS.contains(status)){
                throw new ExternalServiceException("分账回退错误：" + result.getErrorMessage());
            }
            //轮询9s, 不成功，当做异常
            int [] delays = {1000, 2000, 3000, 3000};
            for(int delay: delays){
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                }
                status = sharingService.querySharingTransactionStatus(clientSn);
                if(status != null && (status == SharingTransaction.STATUS_SUCCESS || status == SharingTransaction.STATUS_FREEZE_SUCC)){
                    return;
                }else if(SharingTransaction.FAIL_OR_ERROR_STATUS.contains(status)){
                    throw new ExternalServiceException("分账回退错误：" + result.getErrorMessage());
                }
            }
            throw new ExternalServiceException("分账回退异常");
        }
    }

    private void resetTransactionProfitSharing(Map<String, Object> refundProfitSharing, List<SharingBook> sharingBooks) {
        //分账回退成功，设置分账回退金额（支付宝直连的回退计算公式和其它方式不一样，通过结算金额计算不了）
        if(com.wosai.pantheon.util.CollectionUtil.isNotEmpty(sharingBooks)) {
            refundProfitSharing.put(SharingTransaction.SHARING_AMOUNT, sharingBooks.stream().mapToLong(sharBook -> sharBook.getAmount()).sum());
            List<Map<String, Object>> receivers = (List<Map<String, Object>>) refundProfitSharing.get(ProfitSharing.RECEIVERS);
            Map<String, Map<String, Object>> receiverIdMap = receivers.stream().collect(Collectors.toMap(map -> MapUtil.getString(map, ProfitSharing.RECEIVER_ID), Function.identity()));
            // 重新设置分账信息中的各分账接收方金额
            for (SharingBook sharingBook : sharingBooks) {
                Map<String, Object> receiver = receiverIdMap.get(sharingBook.getReceiverId());
                if(receiver != null) {
                    receiver.put(ProfitSharing.RECEIVER_AMOUNT, sharingBook.getAmount());
                }
            }
        }
    }
    
    public UpayException translateProfitSharingException(Throwable e){
        if(e instanceof ParameterValidationException){
            return new InvalidParamException(e.getMessage());
        }
        String message = e.getMessage();
        if(ErrorMessageConstant.SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS.equals(message)){
            return new ExternalServiceException(UpayErrorScenesConstant.SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS, UpayErrorScenesConstant.SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS_MESSAGE);
        }else if(ErrorMessageConstant.SHARING_RESTITUTE_WEIXIN_RESTRICT.equals(message)){
            return new ExternalServiceException(UpayErrorScenesConstant.SHARING_RESTITUTE_WEIXIN_RESTRICT, UpayErrorScenesConstant.SHARING_RESTITUTE_WEIXIN_RESTRICT_MESSAGE);
        }else if(ErrorMessageConstant.SHARING_APPLY_STATUS_NOT_PASS.equals(message)){
            return new ExternalServiceException(UpayErrorScenesConstant.PROFIT_SHARING_IN_APPLY_ERROR,UpayErrorScenesConstant.PROFIT_SHARING_IN_APPLY_MESSAGE);
        }else if(ErrorMessageConstant.SHARING_APPLY_STATUS_NOT_PASS_ZHMD.equals(message)){
            return new ExternalServiceException(UpayErrorScenesConstant.PROFIT_SHARING_IN_APPLY_SMART_STORE_ERROR,UpayErrorScenesConstant.PROFIT_SHARING_IN_APPLY_SMART_STORE_MESSAGE);
        }else if (e instanceof BrandSettleException) {
            if (BrandSettleRespCodeEnum.SHARING_RESTITUTE_RECEIVER_WALLET_NOT_ENOUGH.getMsg().equals(message) 
                    || BrandSettleRespCodeEnum.REFUND_WALLET_NOT_ENOUGH.getMsg().equals(message)) {
                return new ExternalServiceException(UpayErrorScenesConstant.WALLET_NOT_ENOUGH, UpayErrorScenesConstant.WALLET_NOT_ENOUGH_MESSAGE);
            }
        }
        logger.warn("unknown profit sharing external service error " + e.getMessage(), e);
        if (e.getClass().getSimpleName().equals("HttpException")){
            return new ExternalServiceException(UpayErrorScenesConstant.DISCONNECT_CALL_PROFIT_SHARING, UpayErrorScenesConstant.DISCONNECT_CALL_PROFIT_SHARING_MESSAGE);
        }else {
            return new ExternalServiceException(UpayErrorScenesConstant.SHARING_COMMON_ERROR, UpayErrorScenesConstant.SHARING_COMMON_ERROR_MESSAGE);
        }
    }

    private UpayException translateCoreBusinessException(Throwable ex) {
        return translateCoreBusinessException(ex, null);
    }

    private UpayException translateCoreBusinessException(Throwable ex, Integer payway) {
        if (ex instanceof CoreTerminalNotExistsException) {
            return new TerminalNotExistsException(UpayErrorScenesConstant.UPAY_TERMINAL_NOT_EXISTS_MESSAGE, ex);
        }else if (ex instanceof CoreTerminalNotActivatedException) {
            String errorScenes = UpayErrorScenesConstant.UPAY_TERMINAL_STATUS_ABNORMAL_NOT_ACTIVATION;
            String errroMessage = UpayErrorScenesConstant.UPAY_TERMINAL_STATUS_ABNORMAL_NOT_ACTIVATION_MESSAGE;
            if(!StringUtil.empty(ex.getMessage())){
                if(ex.getMessage().indexOf("已禁用")>= 0){
                    errorScenes = UpayErrorScenesConstant.UPAY_TERMINAL_STATUS_ABNORMAL_DISABLED;
                    errroMessage = UpayErrorScenesConstant.UPAY_TERMINAL_STATUS_ABNORMAL_DISABLED_MESSAGE;
                }else if(ex.getMessage().indexOf("状态异常")>= 0){
                    errorScenes = UpayErrorScenesConstant.UPAY_TERMINAL_STATUS_ABNORMAL_UNKNOWN;
                    errroMessage = UpayErrorScenesConstant.UPAY_TERMINAL_STATUS_ABNORMAL_UNKNOWN_MESSAGE;
                }
            }
            return new TerminalStatusAbnormalException(errorScenes, errroMessage, ex);
        }else if (ex instanceof CoreStoreNotExistsException) {
            return new StoreNotExistsException(UpayErrorScenesConstant.UPAY_STORE_NOT_EXISTS, UpayErrorScenesConstant.UPAY_STORE_NOT_EXISTS_MESSAGE, ex);
        }else if (ex instanceof CoreStoreNotOnlineException || ex instanceof CoreStoreStatusAbnormalException) {
            return new StoreStatusAbnormalException(UpayErrorScenesConstant.UPAY_STORE_STATUS_ABNORMAL_MESSAGE, ex);
        }else if (ex instanceof CoreMerchantNotExistsException) {
            return new MerchantNotExistsException(UpayErrorScenesConstant.UPAY_MERCHANT_NOT_EXISTS_MESSAGE, ex);
        }else if (ex instanceof CoreMerchantStatusAbnormalException){
            return new MerchantStatusAbnormalException(UpayErrorScenesConstant.UPAY_MERCHANT_STATUS_ABNORMAL_MESSAGE, ex);
        }else if (ex instanceof CoreMerchantConfigAbnormalException){
            switch (ex.getMessage()) {
                case "此收款通道已被关闭，请换用其他收款通道":
                    return new ExternalServiceException(UpayErrorScenesConstant.PROVIDER_NOT_SUPPORT, UpayErrorScenesConstant.PROVIDER_NOT_SUPPORT_MESSAGE);
                case "商户收款权限被关闭，请联系您的客户经理":
                    return new ExternalServiceException(UpayErrorScenesConstant.PAY_STATUS_CLOSED, UpayErrorScenesConstant.PAY_STATUS_CLOSED_MESSAGE);
                case "无拉卡拉商户号，不可发生代结算交易":
                case "无银商商户号，不可发生代结算交易":
                    return new ExternalServiceException(UpayErrorScenesConstant.MERCHANT_BEING_OPENING, UpayErrorScenesConstant.MERCHANT_BEING_OPENING_MESSAGE);
                case "支付宝商户报备中，请耐心等待":
                    return new ExternalServiceException(UpayErrorScenesConstant.WFT_ALIPAY_BEING_OPENING, UpayErrorScenesConstant.WFT_ALIPAY_BEING_OPENING_MESSAGE);
                case "商户交易参数配置异常":
                    return new ExternalServiceException(UpayErrorScenesConstant.PAY_MERCHANT_CONFIG_ERROR, UpayErrorScenesConstant.PAY_MERCHANT_CONFIG_ERROR_MESSAGE);
                case "微信商户报备中，请耐心等待":
                case "此商户未开通微信":
                    return new ExternalServiceException(UpayErrorScenesConstant.PAY_WFT_WEIXIN_NOT_ALLOWED, UpayErrorScenesConstant.PAY_WFT_WEIXIN_NOT_ALLOWED_MESSAGE);
                case "商户正式交易参数配置错误":
                    return new ExternalServiceException(UpayErrorScenesConstant.FORMAL_MERCHNAT_CONFIG_ERROR, UpayErrorScenesConstant.FORMAL_MERCHNAT_CONFIG_ERROR_MESSAGE);
                case "终端归属服务商无该业务权限":
                    return new ExternalServiceException(UpayErrorScenesConstant.TRADE_APP_NO_AUTH, UpayErrorScenesConstant.TRADE_APP_NO_AUTH_MESSAGE);
                case "业务方未配置交易参数":
                    return new ExternalServiceException(UpayErrorScenesConstant.TRADE_APP_NOT_CONFIG, UpayErrorScenesConstant.TRADE_APP_NOT_CONFIG_MESSAGE);
                default:
                    return new ExternalServiceException(UpayErrorScenesConstant.PROVIDER_NO_AUTH, ex.getMessage());
            }
        }else if(ex instanceof CoreUnknownException){
            if(ex.getMessage().equals("支付宝授权token获取失败")){
                return new ExternalServiceException(UpayErrorScenesConstant.PAY_ALIPAY_TOKEN_FAIL, UpayErrorScenesConstant.PAY_ALIPAY_TOKEN_FAIL_MESSAGE);
            }
        }else if (ex instanceof CoreStoreConfigAbnormalException) {
            return new StoreConfigAbnormalException(UpayErrorScenesConstant.UPAY_STORE_CONFIG_STATUS_ABNORMAL_DISABLED
                    , ex.getMessage());
        }else if (ex instanceof CoreTerminalConfigAbnormalException) {
            return new TerminalConfigAbnormalException(UpayErrorScenesConstant.UPAY_TERMINAL_CONFIG_STATUS_ABNORMAL_DISABLED
                    , ex.getMessage());
        } else if (ex instanceof CoreExternalStateException) {
            switch (ex.getMessage()) {
                case CoreExternalStateException.PROVIDER_MCH_CLOSE_MSG:
                    return new UpayBizException(UpayErrorScenesConstant.PROVIDER_MCH_CLOSE, UpayErrorScenesConstant.PROVIDER_MCH_CLOSE_MESSAGE);
                case CoreExternalStateException.PROVIDER_TERMINAL_CLOSE_MSG:
                    return new UpayBizException(UpayErrorScenesConstant.PROVIDER_TERMINAL_CLOSE, UpayErrorScenesConstant.PROVIDER_TERMINAL_CLOSE_MESSAGE);
                case CoreExternalStateException.SUB_MCH_ID_CLOSE_MSG:
                    if (payway != null) {
                        if (Objects.equals(Payway.WEIXIN.getCode(), payway)) {
                            return new UpayBizException(UpayErrorScenesConstant.SOURCE_WX_SUB_MCH_CLOSE, UpayErrorScenesConstant.SOURCE_WX_SUB_MCH_CLOSE_MESSAGE_FORMAT);
                        } else if (Objects.equals(Payway.ALIPAY2.getCode(), payway) || Objects.equals(Payway.ALIPAY.getCode(), payway)) {
                            return new UpayBizException(UpayErrorScenesConstant.SOURCE_ALIPAY_SUB_MCH_CLOSE, UpayErrorScenesConstant.SOURCE_ALIPAY_SUB_MCH_CLOSE_MESSAGE_FORMAT);
                        } else if (Objects.equals(Payway.UNIONPAY.getCode(), payway)) {
                            return new UpayBizException(UpayErrorScenesConstant.SOURCE_UNION_PAY_SUB_MCH_CLOSE, UpayErrorScenesConstant.SOURCE_UNION_PAY_SUB_MCH_CLOSE_MESSAGE_FORMAT);
                        }
                    }
                default:
                    return new UpayBizException(UpayErrorScenesConstant.DEFAULT_EXTERNAL_STATE_CLOSE, ex.getMessage());
            }
        }

        logger.warn("unknown core business external service error" + ex.getMessage(), ex);

        if (ex.getClass().getSimpleName().equals("HttpException")){
            return new ExternalServiceException(UpayErrorScenesConstant.DISCONNECT_CALL_COREB, UpayErrorScenesConstant.DISCONNECT_CALL_COREB_MESSAGE);
        }else {
            return new ExternalServiceException(UpayErrorScenesConstant.DISCONNECT_CALL_COREB, ex.getMessage());
        }
    }

    private UpayException translateUpayWalletException(Throwable ex, Long provider, int payway, int subPayway, boolean isChangeClearanceProvider, boolean isProfitSharingTransaction){
        String errorScenes = UpayErrorScenesConstant.DISCONNECT_CALL_WALLET;
        String errroMessage = UpayErrorScenesConstant.DISCONNECT_CALL_WALLET_MESSAGE;
        Map<String,Object> data = null;
        if(ex instanceof WalletBalanceFreezeConflictException){
            errorScenes = UpayErrorScenesConstant.DISCONNECT_CALL_WALLET;
            errroMessage = UpayErrorScenesConstant.DISCONNECT_CALL_WALLET_MESSAGE;
        }else if(ex instanceof WalletBalanceNotEnoughException){
            if(null != provider && provider == TradeConfigService.PROVIDER_LKLWANMA){
                errorScenes = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_WANMA;
                errroMessage = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_WANMA_MESSAGE;
            }else{
                if(isChangeClearanceProvider) {
                    errorScenes = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_CHANGE_CLEARNANCE_PROVIDER;
                    errroMessage = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_CHANGE_CLEARNANCE_PROVIDER_MESSAGE;
                } else if (isProfitSharingTransaction) {
                    errorScenes = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_CHANGE_PROFIT_SHARING;
                    errroMessage = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_CHANGE_PROFIT_SHARING_MESSAGE;
                } else {
                    errorScenes = UpayErrorScenesConstant.WALLET_NOT_ENOUGH;
                    errroMessage = UpayErrorScenesConstant.WALLET_NOT_ENOUGH_MESSAGE;
                }
            }
        }else if(ex instanceof RefundRequestConflictError){
            errorScenes = UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR;
            errroMessage = UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR_MESSAGE;
        }
        logger.warn("unknown upay wallet external service error" + ex.getMessage(), ex);
        if (!(ex.getClass().getSimpleName().equals("HttpException"))){
        	errroMessage = ex.getMessage();
        }
        return new ExternalServiceException(errorScenes, errroMessage, data);
    }

    public static class TcpPayResult{
        public static class TcpPay{
            private Long amount;
            private String originalType;
            private String originalName;
            private String source;

            public TcpPay(Long amount, String originalType){
                this.amount = amount;
                this.originalType = originalType;
            }

            public TcpPay(Long amount, String originalType, String originalName){
                this.amount = amount;
                this.originalType = originalType;
                this.originalName = originalName;
            }
            
            public Long getAmount() {
                return amount;
            }
            public void setAmount(Long amount) {
                this.amount = amount;
            }
            public String getOriginalType() {
                return originalType;
            }
            public void setOriginalType(String originalType) {
                this.originalType = originalType;
            }
            public String getSource() {
                return source;
            }
            public void setSource(String source) {
                this.source = source;
            }

            public String getOriginalName() {
                return originalName;
            }

            public void setOriginalName(String originalName) {
                this.originalName = originalName;
            }
        }
        public static final TcpPayResult VOID = new TcpPayResult(false, 0);

        private boolean triggered;
        private long effectiveAmount;
        private String productFlag;
        private Long hongbaoWosaiAmount;
        private Long hongbaoWosaiMchAmount;
        private Long discountWosaiAmount;
        private Long discountWosaiMchAmount;
        
        private List<TcpPay> hongbaoWosai;
        private List<TcpPay> hongbaoWosaiMch;
        private List<TcpPay> discountWosai;
        private List<TcpPay> discountWosaiMch;
        //营销用户id(uc_user_id)
        private String marketUserId;
        
        public TcpPayResult(boolean triggered, long effectiveAmount) {
            this.triggered = triggered;
            this.effectiveAmount = effectiveAmount;
        }

        public TcpPayResult(boolean triggered, long effectiveAmount, String productFlags, String marketUserId) {
            this.triggered = triggered;
            this.effectiveAmount = effectiveAmount;
            this.productFlag = productFlags;
            this.marketUserId = marketUserId;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getHongbaoWosaiAmount() {
            return null == hongbaoWosaiAmount ? 0 : hongbaoWosaiAmount;
        }

        public void setHongbaoWosaiAmount(Long hongbaoWosaiAmount) {
            this.hongbaoWosaiAmount = hongbaoWosaiAmount;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getHongbaoWosaiMchAmount() {
            return null == hongbaoWosaiMchAmount ? 0 : hongbaoWosaiMchAmount;
        }

        public void setHongbaoWosaiMchAmount(Long hongbaoWosaiMchAmount) {
            this.hongbaoWosaiMchAmount = hongbaoWosaiMchAmount;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getDiscountWosaiAmount() {
            return null == discountWosaiAmount ? 0: discountWosaiAmount;
        }

        public void setDiscountWosaiAmount(Long discountWosaiAmount) {
            this.discountWosaiAmount = discountWosaiAmount;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getDiscountWosaiMchAmount() {
            return null == discountWosaiMchAmount ? 0 : discountWosaiMchAmount;
        }

        public void setDiscountWosaiMchAmount(Long discountWosaiMchAmount) {
            this.discountWosaiMchAmount = discountWosaiMchAmount;
        }

        public boolean isTriggered() {
            return triggered;
        }
        public void setTriggered(boolean triggered) {
            this.triggered = triggered;
        }
        public long getEffectiveAmount() {
            return effectiveAmount;
        }
        public void setEffectiveAmount(long effectiveAmount) {
            this.effectiveAmount = effectiveAmount;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpPay>
         */
        public List<TcpPay> getHongbaoWosai() {
            if(null == hongbaoWosai) {
                hongbaoWosai = new ArrayList<TcpPay>();
            }
            return hongbaoWosai;
        }

        public void setHongbaoWosai(List<TcpPay> hongbaoWosai) {
            this.hongbaoWosai = hongbaoWosai;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpPay>
         */
        public List<TcpPay> getHongbaoWosaiMch() {
            if(null == hongbaoWosaiMch) {
                hongbaoWosaiMch = new ArrayList<TcpPay>();
            }
            return hongbaoWosaiMch;
        }

        public void setHongbaoWosaiMch(List<TcpPay> hongbaoWosaiMch) {
            this.hongbaoWosaiMch = hongbaoWosaiMch;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpPay>
         */
        public List<TcpPay> getDiscountWosai() {
            if(null == discountWosai) {
                discountWosai = new ArrayList<TcpPay>();
            }
            return discountWosai;
        }

        public void setDiscountWosai(List<TcpPay> discountWosai) {
            this.discountWosai = discountWosai;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpPay>
         */
        public List<TcpPay> getDiscountWosaiMch() {
            if(null == discountWosaiMch) {
                discountWosaiMch = new ArrayList<TcpPay>();
            }
            return discountWosaiMch;
        }

        public void setDiscountWosaiMch(List<TcpPay> discountWosaiMch) {
            this.discountWosaiMch = discountWosaiMch;
        }

        public String getProductFlag() {
            return productFlag;
        }

        public void setProductFlag(String productFlag) {
            this.productFlag = productFlag;
        }

        public String getMarketUserId() {
            return marketUserId;
        }
        public void setMarketUserId(String marketUserId) {
            this.marketUserId = marketUserId;
        }
    }
    
    public static class TcpRefundResult{
        public static final TcpRefundResult DENY = new  TcpRefundResult(true, 0);

        private boolean denied;
        private long effectiveAmount;
        private Long hongbaoWosaiAmount;
        private Long hongbaoWosaiMchAmount;
        private Long discountWosaiAmount;
        private Long discountWosaiMchAmount;
        private List<TcpRefund> hongbaoWosai;
        private List<TcpRefund> hongbaoWosaiMch;
        private List<TcpRefund> discountWosai;
        private List<TcpRefund> discountWosaiMch;
        private String reason;

        public static class TcpRefund{
            private Long amount;
            private String originalType;
            private String originalName;
            private String source;

            public TcpRefund(Long amount, String originalType){
                this.amount = amount;
                this.originalType = originalType;
            }

            public TcpRefund(Long amount, String originalType, String originalName){
                this.amount = amount;
                this.originalType = originalType;
                this.originalName = originalName;
            }
            
            public Long getAmount() {
                return amount;
            }
            public void setAmount(Long amount) {
                this.amount = amount;
            }
            public String getOriginalType() {
                return originalType;
            }
            public void setOriginalType(String originalType) {
                this.originalType = originalType;
            }

            public String getOriginalName() {
                return originalName;
            }

            public void setOriginalName(String originalName) {
                this.originalName = originalName;
            }

            public String getSource() {
                return source;
            }
            public void setSource(String source) {
                this.source = source;
            }
        }

        public TcpRefundResult(boolean denied, long effectiveAmount) {
            this(denied, effectiveAmount, null);
        }

        public TcpRefundResult(boolean denied, long effectiveAmount, String reason) {
            this.denied = denied;
            this.effectiveAmount = effectiveAmount;
            this.reason = reason;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getHongbaoWosaiAmount() {
            return null == hongbaoWosaiAmount ? 0 : hongbaoWosaiAmount;
        }

        public void setHongbaoWosaiAmount(Long hongbaoWosaiAmount) {
            this.hongbaoWosaiAmount = hongbaoWosaiAmount;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getHongbaoWosaiMchAmount() {
            return null == hongbaoWosaiMchAmount ? 0 : hongbaoWosaiMchAmount;
        }

        public void setHongbaoWosaiMchAmount(Long hongbaoWosaiMchAmount) {
            this.hongbaoWosaiMchAmount = hongbaoWosaiMchAmount;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getDiscountWosaiAmount() {
            return null == discountWosaiAmount ? 0 : discountWosaiAmount;
        }

        public void setDiscountWosaiAmount(Long discountWosaiAmount) {
            this.discountWosaiAmount = discountWosaiAmount;
        }

        /**
         * 
         * 为null 时返回0
         * 
         * @return Long
         */
        public Long getDiscountWosaiMchAmount() {
            return null == discountWosaiMchAmount ? 0 : discountWosaiMchAmount;
        }

        public void setDiscountWosaiMchAmount(Long discountWosaiMchAmount) {
            this.discountWosaiMchAmount = discountWosaiMchAmount;
        }

        public boolean isDenied() {
            return denied;
        }

        public void setDenied(boolean denied) {
            this.denied = denied;
        }

        public long getEffectiveAmount() {
            return effectiveAmount;
        }

        public void setEffectiveAmount(long effectiveAmount) {
            this.effectiveAmount = effectiveAmount;
        }

        public String getReason() {
            return reason;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpRefund>
         */
        public List<TcpRefund> getHongbaoWosai() {
            if(null == hongbaoWosai) {
                hongbaoWosai = new ArrayList<TcpRefund>();
            }
            return hongbaoWosai;
        }

        public void setHongbaoWosai(List<TcpRefund> hongbaoWosai) {
            this.hongbaoWosai = hongbaoWosai;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpRefund>
         */
        public List<TcpRefund> getHongbaoWosaiMch() {
            if(null == hongbaoWosaiMch) {
                hongbaoWosaiMch = new ArrayList<TcpRefund>();
            }
            return hongbaoWosaiMch;
        }

        public void setHongbaoWosaiMch(List<TcpRefund> hongbaoWosaiMch) {
            this.hongbaoWosaiMch = hongbaoWosaiMch;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpRefund>
         */
        public List<TcpRefund> getDiscountWosai() {
            if(null == discountWosai) {
                discountWosai = new ArrayList<TcpRefund>();
            }
            return discountWosai;
        }

        public void setDiscountWosai(List<TcpRefund> discountWosai) {
            this.discountWosai = discountWosai;
        }

        /**
         * 
         * 为null 时返回空的集合
         * 
         * @return List<TcpRefund>
         */
        public List<TcpRefund> getDiscountWosaiMch() {
            if(null == discountWosaiMch) {
                discountWosaiMch = new ArrayList<TcpRefund>();
            }
            return discountWosaiMch;
        }

        public void setDiscountWosaiMch(List<TcpRefund> discountWosaiMch) {
            this.discountWosaiMch = discountWosaiMch;
        }
    }

    public boolean checkRefundOrFixOriginalClearanceProviderWhereMchUp(Map transaction) {
        Map merchantSwitchMchTime = tradeConfigService.getMerchantSwitchMchTime(MapUtil.getString(transaction, Transaction.MERCHANT_ID));
        if (merchantSwitchMchTime == null) {
            merchantSwitchMchTime = new HashMap();
        }
        Integer clearanceProvider = MapUtil.getInteger((Map) transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER);
        if (clearanceProvider != null) {
            Long clearanceMchSwitchTime = MapUtil.getLong(merchantSwitchMchTime, String.valueOf(clearanceProvider));
            if (clearanceMchSwitchTime != null) {
                if (MapUtil.getLongValue(transaction, DaoConstants.CTIME) < clearanceMchSwitchTime.longValue()) {
                    return false;
                }
                int provider = tradeConfigService.getClearanceProvider(MapUtil.getString(transaction, Transaction.MERCHANT_ID));
                if (provider == ClearanceProvider.SWITCH.getCode()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 判断待结算商户是否金额足够可以进行撤单与退款操作,并且冻结相应的金额, 如果余额不够，会抛出异常
     *
     */
    public  void canCancelOrRefundAndFreezeWalletBalance(String merchantId, String transactionId, long walletChangeAmount, Map transaction, int currentClearanceProvider){
        Long provider = BeanUtil.getPropLong(transaction, Transaction.PROVIDER);
        boolean isChangeClearanceProvider = false;
        try {
            Integer clearanceProvider = MapUtil.getInteger((Map)transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER);
            if(clearanceProvider == null) {
                throw new WalletInvalidParameterException(UpayErrorScenesConstant.DISCONNECT_CALL_WALLET_MESSAGE);
            }
            isChangeClearanceProvider = clearanceProvider != currentClearanceProvider;
            int accountType = BeanUtil.getPropInt(transaction, Transaction.KEY_WALLET_ACCOUNT_TYPE, ProviderWalletAccountTypeEnum.DEFAULT.getValue());
            getWalletService().freezeWalletBalance(new FreezeWalletBalanceReqV3(merchantId, walletChangeAmount, transactionId, clearanceProvider, accountType));
        }catch (Exception e){
            throw translateUpayWalletException(e, provider, BeanUtil.getPropInt(transaction, Transaction.PAYWAY), BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY), isChangeClearanceProvider, UpayProfitSharingUtil.isPayProfitSharingTransaction(transaction));
        }
    }

    public void canCancelOrRefundAndFreezeWalletBalance(CanCancelOrRefundAndFreezeWalletBalanceReq req) {
        if (!checkRefundOrFixOriginalClearanceProviderWhereMchUp(req.payTransaction)) {
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_MCH_SWITCH_REFUND_FAIL, UpayErrorScenesConstant.ORDER_MCH_SWITCH_REFUND_FAIL_MESSAGE);
        }
        Long provider = BeanUtil.getPropLong(req.payTransaction, Transaction.PROVIDER);
        boolean isChangeClearanceProvider = false;
        try {
            Integer clearanceProvider = MapUtil.getInteger((Map)req.payTransaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER);
            if(clearanceProvider == null) {
                throw new WalletInvalidParameterException(UpayErrorScenesConstant.DISCONNECT_CALL_WALLET_MESSAGE);
            }
            int accountType = BeanUtil.getPropInt(req.payTransaction, Transaction.KEY_WALLET_ACCOUNT_TYPE, ProviderWalletAccountTypeEnum.DEFAULT.getValue());
            isChangeClearanceProvider = clearanceProvider != req.currentClearanceProvider;
            allowedRefundWithProfitBalance(req.merchantId, req.walletChangeAmount, clearanceProvider, req.refundProfitSharing, req.payTransaction, req.refundAllOrCancel);
            getWalletService().freezeWalletBalance(new FreezeWalletBalanceReqV3(req.merchantId, req.walletChangeAmount, req.transactionId, clearanceProvider, accountType));
        }catch (Exception e){
            throw translateUpayWalletException(e, provider, BeanUtil.getPropInt(req.payTransaction, Transaction.PAYWAY), BeanUtil.getPropInt(req.payTransaction, Transaction.SUB_PAYWAY), isChangeClearanceProvider, UpayProfitSharingUtil.isPayProfitSharingTransaction(req.payTransaction));
        }
    }

    public void canCancelOrRefundAndFreezeSFTWalletBalance(String brandId, String merchantId, String orderSn, Integer payway, long tradeRefundAmount) {
        try {
            RefundValidateAndFreezeWalletRequest request = new RefundValidateAndFreezeWalletRequest();
            request.setBrandId(brandId);
            request.setOrderSn(orderSn);
            request.setMerchantId(merchantId);
            request.setRefundAmount(tradeRefundAmount);
            request.setPayway(payway);
            sftSharingService.refundValidateAndFreezeWallet(request);
        }catch (Exception e){
            throw translateProfitSharingException(e);
        }
    }

    /**
     * 为了解决只有一笔交易并且带有分账 前置的余额校验会因为分账还未退款 判断需退回金额不够
     * 如果是当天的交易进行全额退款，并且带有分账，且分账金额加上当前余额足够退的情况：去分账服务查这笔预退的分账金额。
     * 如果即将要退的分账金额与交易所扣分账金额的一致的话，那么才能走撤销
     *
     * @param merchantId
     * @param clearanceProvider
     * @param payTransaction
     * @param refundAllOrCancel
     * @return
     *
     */
    private void allowedRefundWithProfitBalance(String merchantId, long walletChangeAmount, int clearanceProvider, Map refundProfitSharing, Map payTransaction, boolean refundAllOrCancel) {
        try {
            long todayStart = DateTimeUtil.getOneDayStart(System.currentTimeMillis());
            long transactionStart = DateTimeUtil.getOneDayStart(MapUtil.getLongValue(payTransaction, DaoConstants.CTIME));
            String orderSn = MapUtil.getString(payTransaction, Transaction.ORDER_SN);
            if (todayStart != transactionStart || !refundAllOrCancel) {
                //只有当天退款、并且是全额的退款的才会去判断余额是否足够退
                return;
            }
            if (UpayProfitSharingUtil.isPayProfitSharingTransaction(payTransaction)) {
                int accountType = BeanUtil.getPropInt(payTransaction, Transaction.KEY_WALLET_ACCOUNT_TYPE, ProviderWalletAccountTypeEnum.DEFAULT.getValue());
                long actualWithdrawableAmount = getWalletService().getActualWithdrawableAmount(new WalletQueryReq(merchantId, clearanceProvider, accountType));
                if (walletChangeAmount <= actualWithdrawableAmount) {
                    //如果要退的余额小于可提现余额，就不用去提前解冻分账
                    return;
                }
                long payClearingAmount = UpayProfitSharingUtil.getClearingAmountByTransaction(payTransaction);
                long payFeeAmount = UpayProfitSharingUtil.getFeeAmountByTransaction(payTransaction);
                int payway = MapUtil.getIntValue(payTransaction, Transaction.PAYWAY);
                int provider = MapUtil.getIntValue(payTransaction, Transaction.PROVIDER);
                Map<String, Object> extraParams = MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS);
                Map<String, Object> profitSharing = (Map<String, Object>) MapUtil.getMap(extraParams, Transaction.PROFIT_SHARING);
                long paySharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, payClearingAmount, payFeeAmount, UpayProfitSharingUtil.useRound(payway, provider));
                if (actualWithdrawableAmount + paySharingAmount >= walletChangeAmount) {
                    BeforeSharingRestituteReq beforeSharingRestituteReq = new BeforeSharingRestituteReq();
                    beforeSharingRestituteReq.setOrderSn(orderSn);
                    beforeSharingRestituteReq.setOriginalAmount(MapUtil.getLong(payTransaction, Transaction.ORIGINAL_AMOUNT));
                    //全额退款 已退金额只能是0
                    beforeSharingRestituteReq.setRefundedOriginalAmount(0l);
                    beforeSharingRestituteReq.setProvider(MapUtil.getInteger(payTransaction, Transaction.PROVIDER));
                    beforeSharingRestituteReq.setPayCtime(MapUtil.getLongValue(payTransaction, DaoConstants.CTIME));
                    beforeSharingRestituteReq.setPayway(MapUtil.getInteger(payTransaction, Transaction.PAYWAY));
                    beforeSharingRestituteReq.setRefundProfitSharing(refundProfitSharing);
                    BeforeSharingRestituteResult restitute = sharingService.getInfoBeforeSharingRestitute(beforeSharingRestituteReq);
                    Boolean needRestiute = restitute.getRestitute();
                    if (needRestiute) {
                        Map<String, Object> restituteProfitSharing = restitute.getProfitSharing();
                        if (restituteProfitSharing != null && restituteProfitSharing instanceof Map) {
                            List<Map> receivers = (List<Map>) MapUtil.getObject(restituteProfitSharing, ProfitSharing.RECEIVERS);
                            long refundSharingAmount = receivers.stream().mapToLong(o -> MapUtil.getLongValue(o, ProfitSharing.RECEIVER_AMOUNT)).sum();
                            if (refundSharingAmount == paySharingAmount) {
                                CancelSharingReq cancelSharingReq = new CancelSharingReq();
                                cancelSharingReq.setOrderSn(orderSn);
                                cancelSharingReq.setCompulsoryCancel(true);
                                sharingService.cancelSharing(cancelSharingReq);
                            }
                        }
                    }
                }
            }
        } catch (ProfitSharingException e) {
            logger.error("allowedRefundWithProfitBalance sharing error", e);
        } catch (Exception e) {
            logger.error("allowedRefundWithProfitBalance error ", e);
        }
    }

    public void unfreezeWalletBalanceDeduction(String merchantId, String actionId){
        try{
            getWalletService().unFreezeWalletBalance(new UnFreezeWalletBalanceReq(merchantId, actionId));
        }catch (Exception e){
            logger.warn("unknown upay wallet external service error" + e.getMessage(), e);
        }
    }

    private WalletServiceV3 getWalletService() {
        return FakeRequestUtil.isFakeRequest() ? this.fakeWalletService : this.walletService;
    }


    public boolean validateRefundCrossMerchantsPermission(String sourceMerchantId, String targetMerchantId){
        return groupService.validateRefundCrossMerchantsPermission(sourceMerchantId, targetMerchantId);
    }

    public boolean isAllowAcrossStoreRefund(String merchantId) {
        Integer switchStatus = supportService.queryStatus(merchantId, TransactionParam.ACROSS_STORE_REFUND_SWITCH);
        return Objects.isNull(switchStatus) || (switchStatus == TransactionParam.STATUS_OPENED);
    }

    public boolean isAllowGenOrderSn(String merchantId) {
        Integer switchStatus = supportService.queryStatus(merchantId, TransactionParam.GEN_ORDER_SN_SWITCH);
        return Objects.nonNull(switchStatus) && (switchStatus == TransactionParam.STATUS_OPENED);
    }

    public void saveGeneratedOrderSn(String orderSn) {
        redisTemplate.opsForValue().set(GEN_SN_KEY + orderSn, "", 4, TimeUnit.MINUTES);
    }

    public boolean isContainsOrderSn(String orderSn) {
        return Objects.nonNull(redisTemplate.opsForValue().get(GEN_SN_KEY + orderSn));
    }

    public void sendLarkMessage(Message message) {
        for(int i = 0; i< 3; i++) {
            try {
                SendResult result = larkChatbotClient.send(larkWebhook, message);
                if(result.isSuccess()) {
                    break;
                }
            }catch (Exception e) {
                logger.error("error in LarkChatbotUtil.send", e);
            }
        }
    }

    public void sendDispatcherRegisterMessage(String message, boolean isAtAll) {
        TextMessage sendMessage = new TextMessage(String.format("主题：网关注册Dispatcher异常\n内容：%s", message));
        sendMessage.setAtAll(isAtAll);
        sendLarkMessage(sendMessage);
    }

    /**
     * 告警
     *
     * @param subject
     * @param content
     */
    public void sendWarnTalk(String subject, String content) {
        String contentTemp = "主题：" + subject + "\n" +
                "内容：" + content + "\n";
        Message message = new TextMessage(contentTemp);
        sendLarkMessage(message);
    }

    public void sendTicketMessage(String message, boolean isAtAll) {
        TextMessage sendMessage = new TextMessage(String.format("主题：流水序列号产生器异常\n内容：%s", message));
        sendMessage.setAtAll(isAtAll);
        sendLarkMessage(sendMessage);
    }

    public void sendTicketWarning(String prefix, String message) {
        if(!TICKET_WARING.containsKey(prefix) || System.currentTimeMillis() - TICKET_WARING.get(prefix) > TICKET_WARING_DELAY_TIME ) {
            sendTicketMessage(message, false);
            TICKET_WARING.put(prefix, System.currentTimeMillis());
        }
    }

    public void reAnalyzeTrade(Map<String, Object> transaction) {
        String request = JsonUtil.toJsonStr(transaction);
        for(int i = 0; i< 3; i++) {
            try {
                logger.warn("request {}", request);
                String response = WebUtils.doPost(null, null, transEsSyncServe + "support/reanalyze", "application/json", JsonUtil.toJsonStr(transaction).getBytes(), 3000, 3000);
                logger.warn("response {}", response);
                break;
            } catch (MpayApiNetworkError e) {
                logger.warn("error in call tran-es-sync", e);
            }
        }
    }

    public Map<String, Object> getTradeParams(String merchantSn, Integer payway, Integer subPayway) {
        return supportService.getTradeParamsWithMerchant(merchantSn, payway, subPayway);
    }

    public UcUserDTO findUserInfo(String userId) {
        return sqbMpService.getUcUserByUserId(userId);
    }

    public String findUserCellphone(String userId) {
        UcUserDTO info = findUserInfo(userId);
        if (Objects.isNull(info)) {
            return null;
        }
        return info.getCellphone();
    }

    public String getAlipaySubMerchantId(String terminalSn, String tradeApp, String bizModel) {
        if(StringUtils.isEmpty(tradeApp)){
            tradeApp = TransactionParam.TRADE_APP_BASIC_PAY;
        }
        Map<String, Object> paramsMap = getAllParamsWithTradeApp(null, terminalSn, Payway.ALIPAY2.getCode(), Order.SUB_PAYWAY_WAP, tradeApp, bizModel);
        String subMchId = null;
        for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
            if (org.apache.commons.lang3.StringUtils.endsWithIgnoreCase(entry.getKey(), "params")
                    && (entry.getValue() instanceof Map)
                    && ((Map)entry.getValue()).containsKey(TransactionParam.LIQUIDATION_NEXT_DAY)
            ) {
                subMchId = MapUtil.getString((Map) entry.getValue(), TransactionParam.ALIPAY_SUB_MCH_ID);
                break;
            }
        }
        return subMchId;
    }

    public Map<String, Object> getAgentParamMap(String agentName) {
        try {
            return AGENT_PARAMS_CACHE.get(agentName, () -> {
                Map agentMap = tradeConfigService.getAgentByName(agentName);
                return (Map<String, Object>) MapUtil.getMap(agentMap, Agent.PARAMS);
            });
        } catch (Exception e) {
            throw new UpayBizException("获取agent配置参数失败.", e);
        }
    }

    /**
     * 替换交易参数
     * 
     * @param provider
     * @param tradeParams
     */
    public void resetSignParams(MpayServiceProvider serviceProvider, Map<String, Object> transaction) {
        int provider = MapUtil.getIntValue(transaction, Transaction.PROVIDER);
        Map<String, Object> tradeParams = serviceProvider.getTradeParams(transaction);
        if (provider == Provider.UNION_PAY_DIRECT.getCode() || provider == Provider.LAKALA_UNION_PAY.getCode()) {
            String signType = MapUtil.getString(tradeParams, TransactionParam.SIGN_TYPE, TransactionParam.SIGN_TYPE_RSA2);
            if (TransactionParam.SIGN_TYPE_RSA2.equals(signType)) {
                tradeParams.putAll(ApolloConfigurationCenterUtil.getReplaceSm2SignParams(provider));
            }
        }
        // 通用交易参数替换逻辑
        Map<String, Object> configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        Map<String, List<Map<String, Objects>>> replaceTradeParamsConfig = ApolloConfigurationCenterUtil.getTradeParamsReplaceConfig();
        for (Map.Entry<String, List<Map<String, Objects>>> replaceEntry : replaceTradeParamsConfig.entrySet()) {
            // 第一层级，取config_snapshot下的 *_trade_params配置
            String tradeParamsKey = replaceEntry.getKey();
            Object trTradeParams = configSnapshot.get(tradeParamsKey);
            if (trTradeParams == null || !(trTradeParams instanceof Map)) {
                continue;
            }
            List<Map<String, Objects>> tradeParamsValues = replaceEntry.getValue();
            Map<String, Object> trTradeParamsMap = (Map)trTradeParams;
            for (Map<String, Objects> tradeParamsReplaceValue : tradeParamsValues) {
                // 第二层级，取*_trade_params下的key
                String matchkey = MapUtil.getString(tradeParamsReplaceValue, UpayConstant.TRADE_PARAMS_REPLACE_MATCH_KEY);
                String matchValue = MapUtil.getString(trTradeParamsMap, matchkey, "");
                if (StringUtil.empty(matchValue)) {
                    continue;
                }
                List<String> matchValues = (List<String>) MapUtil.getObject(tradeParamsReplaceValue, UpayConstant.TRADE_PARAMS_REPLACE_MATCH_VALUES);
                if (com.wosai.pantheon.util.CollectionUtil.isNotEmpty(matchValues) && matchValues.contains(matchValue)) {
                    Map<String, Object> replaceValue = MapUtil.getMap(tradeParamsReplaceValue, UpayConstant.TRADE_PARAMS_REPLACE_REPLACE, Collections.emptyMap());
                    trTradeParamsMap.putAll(replaceValue);
                }
            }
        }
    }

    /**
     * 设置全局配置
     *
     * @param payTransaction
     */
    public void setLiquidationNextDayConfig(Map<String, Object> payTransaction) {
        Integer provider = BeanUtil.getPropInt(payTransaction, Transaction.PROVIDER);
        //富友 2024-07-01之前的交易才会强制设置
        if (Objects.equals(provider, Order.PROVIDER_FUYOU)) {
            long ctime = BeanUtil.getPropLong(payTransaction, DaoConstants.CTIME);
            if (ctime < FUYOU_REVERSE_DATE_LINE) {
                Map<String, Object> tradeParams = UpayUtil.getTradeParamsFromConfigSnapshot(MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT));
                tradeParams.put(TransactionParam.LIQUIDATION_NEXT_DAY, true);
            }
        }
    }

    public void allowBeforeRefundForAggregated(String merchantId, Integer provider, long payTransactionCtime) {
        BeforeRefundCheckRequest beforeRefundCheckRequest = new BeforeRefundCheckRequest();
        beforeRefundCheckRequest.setMerchantId(merchantId);
        beforeRefundCheckRequest.setProvider(provider);
        beforeRefundCheckRequest.setPayTransactionCtime(payTransactionCtime);
        if (!proxySharingService.allowRefundForAggregate(beforeRefundCheckRequest)) {
            throw new UpayBizException(UpayErrorScenesConstant.SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS, UpayErrorScenesConstant.SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS_MESSAGE);
        }
    }

    /**
     * 获取当前商户激活中的vendorAppAppId类型终端
     * @param merchantId
     * @param vendorAppAppId
     * @return
     */
    public String getOneTerminalSnWithVendorAppAppId(String merchantId, String vendorAppAppId) {
        ListResult terminals = terminalService.findTerminals(new PageInfo(1, 1), CollectionUtil.hashMap(
                Terminal.MERCHANT_ID, merchantId,
                Terminal.STATUS, Terminal.STATUS_ACTIVATED,
                Terminal.VENDOR_APP_APPID, vendorAppAppId
        ));
        if(terminals != null && terminals.getRecords() != null && terminals.getRecords().size() > 0){
            Map<String,Object> terminal = terminals.getRecords().get(0);
            return BeanUtil.getPropString(terminal, Terminal.SN);
        }
        return null;
    }

    public static class CanCancelOrRefundAndFreezeWalletBalanceReq{
        private String merchantId;
        private String transactionId;
        private Long walletChangeAmount;
        private Map payTransaction;
        private Integer currentClearanceProvider;
        private Map refundProfitSharing;
        private Boolean refundAllOrCancel;

        private CanCancelOrRefundAndFreezeWalletBalanceReq() {
        }

        public CanCancelOrRefundAndFreezeWalletBalanceReq(String merchantId, String transactionId, Long walletChangeAmount, Map payTransaction, Integer currentClearanceProvider, Map refundProfitSharing, Boolean refundAllOrCancel) {
            this.merchantId = merchantId;
            this.transactionId = transactionId;
            this.walletChangeAmount = walletChangeAmount;
            this.payTransaction = payTransaction;
            this.currentClearanceProvider = currentClearanceProvider;
            this.refundProfitSharing = refundProfitSharing;
            this.refundAllOrCancel = refundAllOrCancel;
        }
    }

    /**
     * 
     * 通知收付通结算付进行状态变更
     * 
     * 注意：由于AdminService 没有对外提供，所以要使用http方式进行调用
     * 
     * @param transaction
     */
    public void notifyBrandSettleFeeChange(Map<String, Object> transaction) {
        Map<String, Object> request = MapUtil.hashMap("method", "notifyFee", "jsonrpc", "2.0", "id", 0, "params", Arrays.asList(transaction));
        String jsonRequest = JsonUtil.toJsonStr(request);
        for(int i = 0; i< 3; i++) {
            try {
                logger.warn("request {}", jsonRequest);
                String response = WebUtils.doPost(null, null, brandSettleServer + "rpc/admin", "application/json", jsonRequest.getBytes(), 3000, 3000);
                logger.warn("response {}", response);
                break;
            } catch (MpayApiNetworkError e) {
                logger.warn("error in call brand-settle", e);
            }
        }
    
    }

    public String getAccessTokenByAppId(String authorizerAppId) {
        try {
            return miniAppsOpenService.getAuthorizerAccessToken(authorizerAppId);
        }catch(Exception ex) {
            throw translateCoreBusinessException(ex);
        }
    }
}
