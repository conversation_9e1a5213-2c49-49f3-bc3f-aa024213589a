package com.wosai.upay.service;

import java.net.URLEncoder;

public class QrcodeImaging {
    public static final String PATH = "/upay/qrcode";
    
    private String imagingServer = "http://upay.test.shouqianba.com";
    
    public void setImagingServer(String imagingServer) {
        if (imagingServer != null) {
            this.imagingServer = imagingServer;
        }
    }
    
    public String getQrcodeImageUrl(String content) {
        StringBuilder sb = new StringBuilder();
        sb.append(imagingServer).append(PATH).append("?content=");
        sb.append(URLEncoder.encode(content));
        return sb.toString();
    }
}
