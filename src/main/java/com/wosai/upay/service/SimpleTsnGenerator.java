package com.wosai.upay.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Callable;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.PostConstruct;
import javax.annotation.concurrent.GuardedBy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.exception.TsnGeneratorException;
import com.wosai.upay.exception.TsnGeneratorExhaustionException;
import com.wosai.upay.repository.DataRepository;

/**
 *
 * 计数器表建表语句：
 * CREATE TABLE `order_sn_ticket` (
 *      `id` varchar(37) NOT NULL COMMENT 'id',
 *      `prefix` varchar(16) NOT NULL COMMENT '订单号前缀',
 *      `current` bigint(20) DEFAULT NULL COMMENT '当前序号',
 *      `ctime` bigint(20) DEFAULT NULL,
 *      `mtime` bigint(20) DEFAULT NULL,
 *      `version` bigint(20) unsigned NOT NULL,
 *      `deleted` tinyint(1) NOT NULL DEFAULT '0',
 *      PRIMARY KEY (`id`),
 *      UNIQUE KEY `id_UNIQUE` (`id`),
 *      UNIQUE KEY `prefix_UNIQUE` (`prefix`)
 * ) ENGINE=InnoDB  COMMENT='订单号生成器表';
 *
 */

public class SimpleTsnGenerator implements TsnGenerator {
    private static final Logger logger = LoggerFactory.getLogger(SimpleTsnGenerator.class);
    public static final long DEFAULT_BATCH_COUNT = 800;
    private static final Random random           = new Random();
    public static final long MAX_TICKET          = 99999999999L;
    public static final long WARNING_TICKET      = 99500000000L;

    @Autowired
    private DataRepository dataRepository;
    @Autowired
    private ExternalServiceFacade facade;
    public JdbcTemplate jdbcTemplate;
    private volatile List<String> prefix;
    private long retries = 3;
    private static Map<String, SnTicket> prefixSnTicket = new HashMap<>();

    private static String[] permTables = {
            "2076943518",
            "5308217469",
            "9382104675",
            "2534918067",
            "4317586902",
            "7941602538",
            "3759410628",
            "0237596841",
            "7014582936",
            "7498235106",
            "7640235891",
            "3142780596",
    };

    @Override
    public String nextSn() {
        if (prefix == null || prefix.size() == 0) {
            throw new TsnGeneratorException(UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_NOT_CONFIG, UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_NOT_CONFIG_MESSAGE);
        }
        int choice = 0;
        for (int i = 0; i < retries; i++) {
            if (prefix.size() > 1) {
                choice = random.nextInt(prefix.size());
            }
            String currentPrefix = prefix.get(choice);
            SnTicket snTicket = prefixSnTicket.get(currentPrefix);
            try{
                long sn  = snTicket.nextSn();
                return currentPrefix + shuffle(leftPadding(sn + "", '0', 12));
            }catch (TransientDataAccessException ex){
                // continue to retry
                logger.warn("failed to generate tsn due to transient jdbc error", ex);
                try {
                    Thread.sleep(random.nextInt(50));
                } catch (InterruptedException e) {
                    logger.error("流水序列号产生器休眠异常:" + ex.getMessage());
                }
            }catch (Exception ex){
                if(ex instanceof RuntimeException && ex.getCause() instanceof TsnGeneratorExhaustionException) {
                	facade.sendTicketMessage(ex.getMessage(), true);
                    List<String> newPrefix = new ArrayList<>(prefix);
                    newPrefix.remove(currentPrefix);
                    prefix = newPrefix;
                    if(newPrefix.size() == 0) {
                        logger.error("流水序列号产生器异常，当前已无可用的订单生成器！！！");
                        facade.sendTicketMessage("【级别：致命】流水序列号产生器异常，当前已无可用的订单生成器！！！", true);
                        throw new TsnGeneratorException(UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_NOT_CONFIG, UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_NOT_CONFIG_MESSAGE);
                    }
                    continue;
                }
                logger.error("流水序列号产生器异常: " + ex.getMessage());
                throw new TsnGeneratorException(UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_DB_ERROR, UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_DB_ERROR_MESSAGE);
            }
        }
        throw new TsnGeneratorException(UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_RETRY_FAIL, UpayErrorScenesConstant.TSN_GENERATOR_EXCEPTION_RETRY_FAIL_MESSAGE);
    }

    public static String shuffle(String code) {
        StringBuilder sb = new StringBuilder();
        for(int i=1; i<code.length(); ++i) {
            int digit = Integer.parseInt(code.substring(i, i+1));
            sb.append(permTables[i%11].charAt(digit));
        }
        return sb.toString();
    }

    /**
     * 添加前置字符串，左填充。
     * @param origin
     * @param padding
     * @param length
     * @return
     */
    public  static String leftPadding(String origin, char padding, int length){
        if(origin == null){
            return null;
        }
        if(origin.length() < length){
            int time = length - origin.length();
            for (int i=0; i< time; i++){
                origin =  padding + origin;
            }
        }
        return origin;
    }

    public static void updateBatchCount(long newBatchCount) {
        for (SnTicket snTicket : prefixSnTicket.values()) {
            snTicket.updateBatchCount(newBatchCount);
        }
    }

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }



    public void setPrefix(String prefix) {
        this.prefix = Arrays.asList(prefix.split(","));
    }

    public void setRetries(long retries) {
        this.retries = retries;
    }

    @PostConstruct
    public void init(){
        if(this.prefix != null && this.prefix.size() != 0){
            for(String prefixSn: prefix){
                prefixSnTicket.put(prefixSn, new SnTicket(
                        dataRepository, jdbcTemplate, DEFAULT_BATCH_COUNT, prefixSn, facade
                ));
            }
        }
    }
}

class SnTicket{
    private final ReentrantLock lock = new ReentrantLock();

    private DataRepository dataRepository;
    private JdbcTemplate jdbcTemplate;
    
    @GuardedBy("lock,this")
    private long current; //当前订单序号(已使用)
    @GuardedBy("lock,this")
    private long max; //可使用的最大的订单序号(未使用)
    @GuardedBy("lock,this")
    private long next; //下一批次的订单序号 可使用
    
    private String prefix; //订单号前缀
    private long batchCount;
    private ExternalServiceFacade facade;
    


    public SnTicket(DataRepository dataRepository, JdbcTemplate jdbcTemplate, long batchCount, String prefix, ExternalServiceFacade facade){
        this.dataRepository = dataRepository;
        this.jdbcTemplate = jdbcTemplate;
        this.batchCount = batchCount;
        this.prefix = prefix;
        this.facade = facade;
    }

    public long nextSn(){
        long sn;
        final ReentrantLock lock = this.lock;
        synchronized (this) {
            if(current >= max) {
                lock.lock();
                try{
                    if(next == 0) {
                        //1: 第一次来获取订单号时，需要获取可用订单号。
                        //2: 当用完一半订单号，提前获取下一批次可用订单号的线程执行失败后，需要再此做一个获取下一批次的订单号的补救处理。
                        next = getCurrentAndAllocateNextBatchFromDB();
                    }
                    current = next;
                    max = next + batchCount;
                    next = 0;
                } finally {
                    lock.unlock();
                }
            }
            sn = current++;
        }
        if(sn == max - batchCount/2) {
            //当用完一半订单号的时候，提前生成下一批可用的订单号
            lock.lock();
            try {
                next = getCurrentAndAllocateNextBatchFromDB();
            } finally {
                lock.unlock();
            }
        }
        return sn;
    }

    private long getCurrentAndAllocateNextBatchFromDB(){
        return dataRepository.doInTicketTransaction(new Callable<Long>() {
            @Override
            public Long call() throws Exception {
                Map<String, Object> ticket = jdbcTemplate.queryForMap("select * from order_sn_ticket where prefix = ? for update", prefix);
                long currentValue = BeanUtil.getPropLong(ticket, "current");
                long nextMaxValue = currentValue + batchCount;
                if(nextMaxValue > SimpleTsnGenerator.MAX_TICKET) {
                    throw new TsnGeneratorExhaustionException(String.format("【级别：严重】流水序列号产生器 %s 已使用到 %s，将触发序列号第5位值切换，当前已不被使用", prefix, currentValue));
                }else if(nextMaxValue > SimpleTsnGenerator.WARNING_TICKET) {
                	facade.sendTicketWarning(prefix, String.format("【级别：中等】流水序列号产生器 %s 已使用到 %s，将触发序列号第5位值切换", prefix, currentValue));
                }
                jdbcTemplate.update("update order_sn_ticket set current = current + ? where prefix = ? ", batchCount, prefix);
                return currentValue;
            }
        });
    }

    public void updateBatchCount(long newBatchCount) {
        this.batchCount = newBatchCount;
    }
}
