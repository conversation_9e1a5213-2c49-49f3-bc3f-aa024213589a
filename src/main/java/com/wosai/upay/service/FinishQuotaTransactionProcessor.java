package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.InvalidParamException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.model.discount.NotifyFeeDetail;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.workflow.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.model.dao.Transaction.MERCHANT_ID;

/**
 * Description: 额度包余额同步处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/27
 */
@Slf4j
@Component
public class FinishQuotaTransactionProcessor {

    @Resource
    private WorkflowManager workflowManager;
    @Resource
    private DataRepository dataRepository;
    @Resource
    private GatewaySupportService gatewaySupportService;
    @Resource
    private SimpleRedisLock simpleRedisLock;

    /**
     * 通知回调费用详情
     */
    private static final String NOTIFY_FEE_DETAIL = "notify_fee_detail";

    public static final String RESULT = "result";

    public static final String TRANSACTION = "transaction";

    /**
     * 执行器
     *
     * @param request
     * @return
     */
    public Map<String, Object> execute(Map<String, Object> request) {
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        if (StringUtils.isEmpty(tsn)) {
            throw new InvalidParamException("当前tsn为空");
        }
        boolean lockFlag = false;
        String lockName = String.format("FinishQuotaTransactionProcessor:tsn:%s", tsn);
        String lockValue = UUID.randomUUID().toString();
        try {
            lockFlag = simpleRedisLock.tryLock(lockName, lockValue, 30L, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new UpayBizException("加锁失败. lockName=" + lockName);
            }
            RetryUtil.TimingStrategy.Builder builder = new RetryUtil.TimingStrategy.Builder()
                    .setRetry(3, 1000, 2.0)
                    .setJitter(true, 200);
            RetryUtil<Map<String, Object>> retryUtil = new RetryUtil<Map<String, Object>>()
                    .retry(builder.build())
                    .method(() -> {
                        try {
                            return execute0(request);
                        } catch (Throwable e) {
                            log.error("updateFuyouTradeFeeSyncWallet failed. tsn:{}", tsn, e);
                            throw e;
                        }
                    })
                    .on(throwable -> true)
                    .until(resultMap -> MapUtil.getBooleanValue(resultMap, RESULT));
            return retryUtil.execute();
        } finally {
            if (lockFlag) {
                simpleRedisLock.unlock(lockName, lockValue);
            }
        }
    }

    private Map<String, Object> execute0(Map<String, Object> request) {
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        String merchantId = BeanUtil.getPropString(request, MERCHANT_ID);
        // 设置额度包手续费
        Map<String, Object> transaction = dataRepository.getTransactionByTsn(merchantId, tsn);
        if (MapUtil.isEmpty(transaction)) {
            throw new UpayBizException("当前交易流水不存在. tsn:" + tsn);
        }
        String keyIsFeeUpdateFlag = Transaction.KEY_IS_FEE_UPDATE_FLAG;
        //幂等操作，防重复
        if (BeanUtil.getPropBoolean(transaction, keyIsFeeUpdateFlag)) {
            return MapUtil.hashMap(RESULT, true, TRANSACTION, transaction);
        }
        // 支付结果中的状态不为成功，此时更新数据可能会导致数据被覆盖，返回失败，等待下次任务执行
        if (MapUtil.getIntValue(transaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
            throw new UpayBizException("当前交易流水状态不为成功. status:" + MapUtil.getIntValue(transaction, Transaction.STATUS));
        }
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        Map<String, Object> order = getOrder(merchantId, orderSn);
        TransactionContext context = workflowManager.createTransactionContext(null, order, transaction);
        //重置交易流水的费用详情
        resetTransactionFeeDetail(request, transaction, context);
        //手续费更新标识
        BeanUtil.setNestedProperty(transaction, keyIsFeeUpdateFlag, true);
        //-------- 后续完成操作 --------
        Map<String, Object> updatePart = MapUtil.hashMap(DaoConstants.ID, transaction.get(DaoConstants.ID),
                Transaction.CONFIG_SNAPSHOT, transaction.get(Transaction.CONFIG_SNAPSHOT),
                Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                DaoConstants.MTIME, System.currentTimeMillis(),
                DaoConstants.VERSION, MapUtil.getIntValue(transaction, DaoConstants.VERSION)
        );
        //-------- 同步余额log --------
        Workflow workflow = context.getWorkflow();
        //重新计算手续费
        if (workflow instanceof AbstractPayWorkflow) {
            Map<String, Object> tradeParams = context.getServiceProvider().getTradeParams(transaction);
            ((AbstractPayWorkflow) workflow).resetTradeFeeRate(transaction, tradeParams);
        } else if (workflow instanceof DepositConsumeOrderWorkflow) {
            ((DepositConsumeOrderWorkflow) workflow).resetTradeFeeRate(context, order, transaction);
        } else {
            throw new UpayBizException("未知工作流类型. tsn:" + tsn);
        }
        workflow.retrySaveFinishTransaction(context, null, updatePart, false);
        return MapUtil.hashMap(RESULT, true, TRANSACTION, transaction);
    }

    /**
     * 重置交易流水的费用详情
     *
     * @param request 请求体
     */
    private void resetTransactionFeeDetail(Map<String, Object> request, Map<String, Object> transaction, TransactionContext context) {
        String merchantId = MapUtil.getString(transaction, MERCHANT_ID);
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        try {
            NotifyFeeDetail notifyFeeDetail = decodeNotifyFeeDetail(MapUtil.getString(request, NOTIFY_FEE_DETAIL));
            //未使用额度包信息无需重置，直接返回
            if (Objects.isNull(notifyFeeDetail) || Objects.isNull(notifyFeeDetail.getQuotaRecordId())) {
                log.info("未使用额度包. tsn:{},merchantId:{}", tsn, merchantId);
                return;
            }
            Long fee = notifyFeeDetail.getFee();
            if (Objects.isNull(fee)) {
                log.error("额度包手续费回调通知, 手续费为null, merchantId={}, tsn={}", merchantId, tsn);
                throw new UpayBizException("额度包手续费回调通知, 手续费为null");
            }
            Map<String, Object> tradeParams = context.getServiceProvider().getTradeParams(transaction);
            //设置手续费
            BeanUtil.setNestedProperty(tradeParams, TransactionParam.FEE, fee);
            //设置实际费率
            BeanUtil.setNestedProperty(tradeParams, TransactionParam.FEE_RATE, notifyFeeDetail.getActualFeeRate());
            //设置原始费率
            BeanUtil.setNestedProperty(tradeParams, TransactionParam.FEE_RATE_ORIGINAL, notifyFeeDetail.getBaseFeeRate());

            String quotaFeeRate = notifyFeeDetail.getQuotaFeeRate();
            if (!StringUtils.isEmpty(quotaFeeRate)) {
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE_RATE, quotaFeeRate);
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE_RATE_TAG, notifyFeeDetail.getQuotaRecordId() + "");
                //设置额度减免手续费，内部有逻辑要用到KEY_QUOTA_FEE_RATE,KEY_QUOTA_FEE_RATE_TAG字段
                FeeRateProcessor.setPayQuotaFee(transaction);
            }

        } catch (Exception e) {
            log.error("额度包手续费回调通知, 手续费回写异常, merchantId={}, tsn={}", merchantId, tsn, e);
            throw new UpayBizException("额度包手续费回调通知, 手续费回写异常:" + e.getMessage(), e);
        }
    }

    private Map<String, Object> getOrder(String merchantId, String orderSn) {
        Map<String, Object> order = dataRepository.getOrderByOrderSn(merchantId, orderSn);
        //超过1个月查归档记录
        if (Objects.isNull(order)) {
            order = gatewaySupportService.getOrderBySn(merchantId, orderSn, null, DataPartitionConst.RECENT_6M);
        }
        return order;
    }

    private NotifyFeeDetail decodeNotifyFeeDetail(String notifyFeeDetailStr) throws IOException {
        if (StringUtils.isEmpty(notifyFeeDetailStr)) {
            return null;
        }
        return JacksonUtil.toBean(notifyFeeDetailStr, NotifyFeeDetail.class);
    }
}
