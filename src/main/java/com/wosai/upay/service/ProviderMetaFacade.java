package com.wosai.upay.service;

import com.google.common.collect.Lists;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MetaProvider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.ChinaumsServiceProvider;
import com.wosai.upay.workflow.MpayServiceProvider;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/4/16、18:59
 * 云闪付交易1000 配置
 **/

@Component
public class ProviderMetaFacade {

    private static final Logger logger = LoggerFactory.getLogger(ProviderMetaFacade.class);

    private static final long TL_UNIONPAY_LARGE_TRADE_BENCHMARK = 1000 * 100;

    // key provider value trade_support_1000
    private final Map<Integer, Integer> providerTradeSupport1000StatusValues = new HashMap<>();
    /**
     * Map: provider -> profitSharingSkipCheckStatus
     */
    private static final Map<Integer, Integer> profitSharingSkipCheckOfProviderMap = new HashMap<>(64);
    /**
     * 是否支持预授权通道列表
     */
    private static final List<Integer> SUPPORT_DEPOSIT_PROVIDERS = Lists.newArrayList();

    @Autowired
    private BusinssCommonService businssCommonService;


    @PostConstruct
    private void init(){
        load();
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            try{
                load();
            }catch (Exception e){
                logger.error("refresh provider meta config cache error " + e.getMessage(), e);
            }
        }, 10,10, TimeUnit.MINUTES);
    }


    private void load() {
        List<Integer> supportDepositProviders = Lists.newArrayList();
        List<Map<String, Object>> allMetaProviders = businssCommonService.getAllMetaProviders();
        for (Map metaProvider : allMetaProviders) {
            Integer provider = MapUtils.getInteger(metaProvider, DaoConstants.ID);
            Integer tradeSupport = MapUtils.getInteger(metaProvider, MetaProvider.TRADE_SUPPORT_1000);
            Integer profitSharingSkipCheckStatus = MapUtils.getInteger(metaProvider, MetaProvider.PROFIT_SHARING_SKIP_CHECK);
            providerTradeSupport1000StatusValues.put(provider, tradeSupport);
            profitSharingSkipCheckOfProviderMap.put(provider, profitSharingSkipCheckStatus);
            //预授权配置
            boolean supportDeposit = BooleanUtils.toBoolean(MapUtils.getIntValue(metaProvider, MetaProvider.SUPPORT_DEPOSIT), 1, 0);
            if (supportDeposit) {
                supportDepositProviders.add(provider);
            }
        }
        SUPPORT_DEPOSIT_PROVIDERS.clear();
        SUPPORT_DEPOSIT_PROVIDERS.addAll(supportDepositProviders);
    }

    /**
     * 是否支持扫码预授权
     *
     * @param provider
     * @return
     */
    public boolean supportDeposit(Integer provider) {
        return SUPPORT_DEPOSIT_PROVIDERS.contains(provider);
    }

    /**
     * 是否跳过分账强校验
     *
     * @param provider
     * @return
     */
    public static boolean isSkipCheckProfitSharing(Integer provider) {
        if (null == provider) {
            return false;
        }
        Integer status = profitSharingSkipCheckOfProviderMap.get(provider);
        return status != null && status.equals(MetaProvider.SUPPORT);
    }


    public void unionPayLargeTradeVerify(MpayServiceProvider serviceProvider, Map<String, Object> transaction) {

        long amount = MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        // 交易金额<=1000 || 非云闪付支付方式的
        if (amount <= TL_UNIONPAY_LARGE_TRADE_BENCHMARK || payway != Order.PAYWAY_UNIONPAY) {
            return;
        }

        Map config = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        // 直连商户
        if (UpayUtil.isFormalByConfig(config)) {
            // 银商支付通道v2版本 直接放行
            if (ChinaumsServiceProvider.class.equals(serviceProvider.getClass())) {
                // 不做处理
                return;
            }
        }

        Integer provider = serviceProvider.getProvider();
        if (Objects.isNull(provider)) {
            return;
        }

        //特定通道，直接放行，不进行检验
        Integer tradeSupport = MapUtils.getInteger(providerTradeSupport1000StatusValues, provider);
        if(Objects.equals(tradeSupport, MetaProvider.NOT_LIMIT)) {
            return;
        }

        if (Objects.equals(MetaProvider.FORBID, tradeSupport)) {

            throw new UpayBizException(UpayErrorScenesConstant.PAY_LKL_UNIONPAY_AMOUNT_LIMIT
                    , UpayErrorScenesConstant.PAY_LKL_UNIONPAY_AMOUNT_LIMIT_MESSAGE);
        }

        String commonSwitchStr = MapUtil.getString(config, TransactionParam.COMMON_SWITCH);
        Map<String, Object> tradeParams = serviceProvider.getTradeParams(transaction);
        if (MapUtil.isNotEmpty(tradeParams)
                && tradeParams.containsKey(TransactionParam.LADDER_FEE_RATES)
                && isUnionPayLargeOpen(provider, commonSwitchStr)) {
            return;
        }


        throw new UpayBizException(UpayErrorScenesConstant.PAY_LKL_UNIONPAY_AMOUNT_LIMIT
                , UpayErrorScenesConstant.PAY_LKL_UNIONPAY_AMOUNT_LIMIT_MESSAGE);
    }

    private boolean isUnionPayLargeOpen(Integer provider, String commonSwitchStr) {
        if (StringUtils.isEmpty(commonSwitchStr)) {
            return false;
        }
        //商户层级 大额校验
        char currentStatus = commonSwitchStr.charAt(TransactionParam.TYPE_COMMON_SWITCH_SUPPORT_TRADE_1000);
        //已配置
        if (currentStatus != UpayUtil.COMMON_SWTICH_NOT_CONFIGURED) {
            if (currentStatus == UpayUtil.COMMON_SWTICH_OPEN) {
                return true;
            } else {
                return false;
            }
        }
        //未配置商户层级，旧版本兼容

        if (Objects.equals(provider, Order.PROVIDER_TL) || Objects.equals(provider, Order.PROVIDER_TL_SYB)) {
            return UpayUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_TL_UNIONPAY);
        } else if (Objects.equals(provider, Order.PROVIDER_UNIONPAY_OPEN) || Objects.equals(provider, Order.PROVIDER_LAKALA_UNION_PAY_V3)) {
            return UpayUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_LKL_UNIONPAY);
        } else {
            return false;
        }
    }


}
