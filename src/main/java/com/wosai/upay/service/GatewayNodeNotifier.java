package com.wosai.upay.service;

import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.upay.common.util.JacksonUtil;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class GatewayNodeNotifier {
    private static final Logger logger = LoggerFactory.getLogger(GatewayNodeNotifier.class);

    private static final String CONTENT_TYPE = "application/json";

    private final int connectTimeout = 3000;
    private final int readTimeout = 15000;

    private final OkHttpClient client = HttpClientUtils.getHttpClient(GatewayNodeNotifier.class.getName(),
            null,null,connectTimeout,readTimeout,400,50);

    /**
     * 同步通知
     *
     * @param url
     * @param tsn
     * @param notification
     * @return
     * @throws IOException
     */
    public String syncNotify(String url, String tsn, Map<String, Object> notification) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.parse(CONTENT_TYPE + "; charset=utf-8"), JacksonUtil.toJsonString(notification)))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                logger.error("syncNotify failed, tsn={}, url={}, notification={}", tsn, url, JacksonUtil.toJsonString(notification));
                throw new IOException("syncNotify failed");
            }

            if (response.body() == null) {
                logger.warn("syncNotify success, but body is null, tsn={}, url={}, notification={}", tsn, url, JacksonUtil.toJsonString(notification));
                return null;
            }

            // 处理响应数据
            return response.body().string();
        } catch (IOException e) {
            logger.error("syncNotify exception, tsn={}, url={}, notification={}", tsn, url, JacksonUtil.toJsonString(notification));
            throw new IOException("syncNotify exception: " + e.getMessage());
        }
    }
}
