package com.wosai.upay.service;

import com.wosai.upay.model.api.AuthResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-04-27
 */
public interface ProviderAuthService {
    /**
     * 获取产品编码
     *
     * @return
     */
    String getProductCode();

    /**
     * 是否匹配
     *
     * @return
     */
    boolean isMatch(Map<String, Object> request, Map<String, Object> tradeConfig);

    /**
     * 签约
     *
     * @param request
     * @param tradeConfig
     * @return
     */
    AuthResponse auth(Map<String, Object> request, Map<String, Object> tradeConfig);

    /**
     * 签约查询
     *
     * @param request
     * @param tradeConfig
     * @return
     */
    AuthResponse authQuery(Map<String, Object> request, Map<String, Object> tradeConfig);

    /**
     * 解约
     *
     * @param request
     * @param tradeConfig
     * @return
     */
    AuthResponse authTerminate(Map<String, Object> request, Map<String, Object> tradeConfig);
}
