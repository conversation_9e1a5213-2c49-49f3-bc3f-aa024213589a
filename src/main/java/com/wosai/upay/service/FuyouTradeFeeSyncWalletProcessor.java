package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.fuyou.FuyouBusinessFields;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.RetryUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.model.discount.DiscountTransferDetail;
import com.wosai.upay.model.discount.NotifyFeeDetail;
import com.wosai.upay.model.discount.RefundTransferInfo;
import com.wosai.upay.model.discount.TransferInfo;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.model.dao.Transaction.*;

/**
 * Description: 富友手续费回调通知余额处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/6/27
 */
@Slf4j
@Component
public class FuyouTradeFeeSyncWalletProcessor {
    /**
     * 通知回调费用详情
     */
    private static final String NOTIFY_FEE_DETAIL = "notify_fee_detail";

    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository dataRepository;
    @Autowired
    private GatewaySupportService gatewaySupportService;
    @Autowired
    private SimpleRedisLock simpleRedisLock;
    @Autowired
    DepositProvider depositProvider;

    /**
     * 执行器
     *
     * @param request
     * @return
     */
    public Boolean execute(Map<String, Object> request) {
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        if (StringUtils.isEmpty(tsn)) {
            return true;
        }
        boolean lockFlag = false;
        String lockName = String.format("updateFuyouTradeFeeSyncWallet:tsn:%s", tsn);
        String lockValue = UUID.randomUUID().toString();
        try {
            lockFlag = simpleRedisLock.tryLock(lockName, lockValue, 30L, TimeUnit.SECONDS);
            if (!lockFlag) {
                return false;
            }
            RetryUtil.TimingStrategy.Builder builder = new RetryUtil.TimingStrategy.Builder()
                    .setRetry(3, 1000, 2.0)
                    .setJitter(true, 200);
            RetryUtil<Boolean> retryUtil = new RetryUtil<Boolean>()
                    .retry(builder.build())
                    .method(() -> {
                        try {
                            return execute0(request);
                        } catch (Throwable e) {
                            log.error("updateFuyouTradeFeeSyncWallet failed. tsn:{}", tsn, e);
                            throw e;
                        }
                    })
                    .on(throwable -> true)
                    .until(succeeded -> succeeded != null && succeeded);
            return retryUtil.execute();
        } finally {
            if (lockFlag) {
                simpleRedisLock.unlock(lockName, lockValue);
            }
        }
    }

    private Boolean execute0(Map<String, Object> request) {
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        String merchantId = BeanUtil.getPropString(request, MERCHANT_ID);
        // 设置富友手续费
        Map<String, Object> transaction = dataRepository.getTransactionByTsn(merchantId, tsn);
        if (MapUtil.isEmpty(transaction)) {
            return true;
        }
        String isFeeUpdateFlagKey = Transaction.KEY_IS_FEE_UPDATE_FLAG;
        //幂等操作，防重复
        if (BeanUtil.getPropBoolean(transaction, isFeeUpdateFlagKey)) {
            return true;
        }
        // 支付结果中的状态不为成功，此时更新数据可能会导致数据被覆盖，返回失败，等待下次任务执行
        if (MapUtil.getIntValue(transaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
            log.warn("富友流水状态暂未完成 tsn={},status={}", tsn, MapUtil.getIntValue(transaction, Transaction.STATUS));
            return false;
        }
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        Map<String, Object> order = getOrder(merchantId, orderSn);
        TransactionContext context = workflowManager.createTransactionContext(null, order, transaction);

        //重置交易流水的费用详情
        resetTransactionFeeDetail(request, transaction, context);
        Integer type = MapUtil.getInteger(transaction, Transaction.TYPE);
        if (type == Transaction.TYPE_PAYMENT || type == Transaction.TYPE_DEPOSIT_CONSUME) {
            //银行卡设置入账钱包
            UpayUtil.initWalletAccountType(transaction);
            //重新设置tradeApp
            UpayUtil.resetTradeApp(transaction);
        }
        //手续费更新标识
        BeanUtil.setNestedProperty(transaction, isFeeUpdateFlagKey, true);
        Map<String, Object> updatePart = MapUtil.hashMap(DaoConstants.ID, transaction.get(DaoConstants.ID),
                Transaction.CONFIG_SNAPSHOT, transaction.get(Transaction.CONFIG_SNAPSHOT),
                Transaction.PRODUCT_FLAG, transaction.get(Transaction.PRODUCT_FLAG),
                Transaction.EXTRA_OUT_FIELDS, transaction.get(Transaction.EXTRA_OUT_FIELDS),
                Transaction.MERCHANT_ID, transaction.get(Transaction.MERCHANT_ID),
                DaoConstants.MTIME, System.currentTimeMillis(),
                DaoConstants.VERSION, MapUtil.getIntValue(transaction, DaoConstants.VERSION)
        );
        //-------- 同步余额log --------
        context.getWorkflow().retrySaveFinishTransaction(context, null, updatePart, false);
        return true;
    }

    /**
     * 重置交易流水的费用详情
     */
    private void resetTransactionFeeDetail(Map<String, Object> request, Map<String, Object> transaction, TransactionContext context) {
        String merchantId = MapUtil.getString(transaction, MERCHANT_ID);
        String tsn = BeanUtil.getPropString(request, Transaction.TSN);
        try {
            String notifyFeeDetailStr = MapUtil.getString(request, NOTIFY_FEE_DETAIL);
            if (StringUtils.isEmpty(notifyFeeDetailStr)) {
                log.error("富友手续费回调通知, 手续费详情为null, merchantId={}, tsn={}", merchantId, tsn);
                throw new UpayBizException("富友手续费回调通知, 手续费详情为null");
            }

            NotifyFeeDetail notifyFeeDetail = JacksonUtil.toBean(notifyFeeDetailStr, NotifyFeeDetail.class);
            //设置费率模板
            if (!StringUtils.isEmpty(notifyFeeDetail.getReservedSetCd())) {
                BeanUtil.setNestedProperty(transaction, String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, FuyouBusinessFields.RESERVED_SET_CD), notifyFeeDetail.getReservedSetCd());
            }

            Long notifyFee = notifyFeeDetail.getFee();
            if (null == notifyFee) {
                log.error("富友手续费回调通知, 手续费为null, merchantId={}, tsn={}", merchantId, tsn);
                throw new UpayBizException("富友手续费回调通知, 手续费为null");
            }
            int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
            //费率字段
            String feeRateKey = String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.FEE_RATE);
            //交易使用原始费率
            String originalFeeRate = BeanUtil.getPropString(transaction, feeRateKey);
            long fee = calculateFee(context, Math.abs(notifyFee));
            //设置手续费
            BeanUtil.setNestedProperty(transaction, String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.FEE), fee);
            //设置实际费率
            BeanUtil.setNestedProperty(transaction, String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.FEE_RATE), notifyFeeDetail.getActualFeeRate());
            //设置交易使用原始费率（阶梯费率时为计算后的费率值）
            if (type == Transaction.TYPE_PAYMENT) {
                //正向交易可能会有额度包优惠，故这里重新Set
                BeanUtil.setNestedProperty(transaction, String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.FEE_RATE_ORIGINAL), originalFeeRate);
            }
            //设置通道费率
            BeanUtil.setNestedProperty(transaction, String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.CHANNEL_FEE_RATE), notifyFeeDetail.getBaseFeeRate());
            // 记录通道收取的手续费
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_PROVIDER_FEE, notifyFeeDetail.getProviderFee());

            String quotaFeeRate = notifyFeeDetail.getQuotaFeeRate();
            if (!StringUtils.isEmpty(quotaFeeRate)) {
                //存在额度包费率时，增加额度标记
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE_RATE_TAG, notifyFeeDetail.getQuotaRecordId() + "");
                //设置额度包优惠标志(只有支付时才设置)
                if(MapUtil.getIntValue(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT) {
                    BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE_RATE, quotaFeeRate);
                }
            }
            //设置额度包信息
            setQuotaDetailInfo(request, transaction, type);
            //根据业务场景设置银行卡交易是否为外卡、以及外卡类别
            setTransactionBankcardType(notifyFeeDetail, transaction);
        } catch (Exception e) {
            log.error("富友手续费回调通知, 手续费回写异常, merchantId={}, tsn={}, error={}", merchantId, tsn, e.getMessage(), e);
            throw new UpayBizException("富友手续费回调通知, 手续费回写异常:" + e.getMessage());
        }
    }


    /**
     * 如果是银行交易，需要根据手续费回调中的业务场景来判断是否是外卡
     * @param notifyFeeDetail
     * @param transaction
     */
    private void setTransactionBankcardType(NotifyFeeDetail notifyFeeDetail, Map<String, Object> transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if (payway == Payway.BANKCARD.getCode() && !StringUtils.isEmpty(notifyFeeDetail.getReservedBusiCd())) {
            String wildCategory = FuyouBankServiceProvider.DCC_FLG_MAP.get(notifyFeeDetail.getReservedBusiCd());
            if (!StringUtils.isEmpty(wildCategory)) {
                BeanUtil.setNestedProperty(transaction, String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.WILD_CARD_TYPE), wildCategory);
            }
        }
    }

    /**
     * 设置额度包相关信息
     *
     * @param request
     * @param transaction
     * @param type
     * @throws IOException
     */
    private static void setQuotaDetailInfo(Map<String, Object> request, Map<String, Object> transaction, int type) throws IOException {
        //记录下补贴转账相关信息到transaction
        String discountTransferDetailString = MapUtil.getString(request, Transaction.DISCOUNT_TRANSFER_DETAIL);
        if(StringUtils.isEmpty(discountTransferDetailString)) {
           return;
        }
        DiscountTransferDetail discountTransferDetail = JacksonUtil.toBean(discountTransferDetailString, DiscountTransferDetail.class);
        BeanUtil.setNestedProperty(transaction, String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, DISCOUNT_TRANSFER_DETAIL), discountTransferDetail);
        if (Objects.isNull(discountTransferDetail)) {
            return;
        }
        // ---------------------------- 设置当前补贴额度金额 ----------------------------
        //支付
        if (type == Transaction.TYPE_PAYMENT && Objects.nonNull(discountTransferDetail.getQuotaTransInfo())) {
            TransferInfo quotaInfo = discountTransferDetail.getQuotaTransInfo();
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE, quotaInfo.getTransAmount());
        }
        //退款、撤单
        else if ((type == Transaction.TYPE_REFUND || type == Transaction.TYPE_CANCEL)
                && Objects.nonNull(discountTransferDetail.getQuotaRefundInfo())) {
            RefundTransferInfo quotaInfo = discountTransferDetail.getQuotaRefundInfo();
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE, quotaInfo.getRefundTransAmount());
        }
    }

    /**
     * 计算手续费
     *
     * @param context
     * @return
     */
    private long calculateFee(TransactionContext context, long fee) {
        Map<String, Object> transaction = context.getTransaction();
        Map<String, Object> order = context.getOrder();
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        //非预授权完成，直接返回手续费
        //如果是通道类型的预授权，应该直接使用通道返回的手续费
        if (type != Transaction.TYPE_DEPOSIT_CONSUME ||
                (type == TYPE_DEPOSIT_CONSUME && !depositProvider.sqbDeposit(context))) {
            return fee;
        }
        //针对收钱吧预授权完成手续费计算方式（注：全部完成是在workFlow.finish时直接同步了，这里处理的都是部分完成）
        Map<String, Object> freezeTransaction = getFreezeTransaction(order);
        Map<String, Object> freezeTradeParams = context.getServiceProvider().getTradeParams(freezeTransaction);
        long freezeFee = BeanUtil.getPropLong(freezeTradeParams, TransactionParam.FEE);
        //冻结手续费　－　退款手续费
        return freezeFee - fee;
    }

    private Map<String, Object> getOrder(String merchantId, String orderSn) {
        Map<String, Object> order = dataRepository.getOrderByOrderSn(merchantId, orderSn);
        //超过1个月查归档记录
        if (Objects.isNull(order)) {
            order = gatewaySupportService.getOrderBySn(merchantId, orderSn, null, DataPartitionConst.RECENT_6M);
        }
        return order;
    }

    private Map<String, Object> getFreezeTransaction(Map<String, Object> order) {
        final String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        Map<String, Object> freezeTransaction = dataRepository.getFreezeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
        if (Objects.isNull(freezeTransaction)) {
            freezeTransaction = gatewaySupportService.getTransactionByClientTsn(merchantId, BeanUtil.getPropString(order, Order.SN), BeanUtil.getPropString(order, Order.CLIENT_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        return freezeTransaction;
    }
}
