package com.wosai.upay.service;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.exception.OrderNotExistsException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.exception.UpayFixOrderStateError;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class DepositFixProcessor {
    public static final Logger logger = LoggerFactory.getLogger(DepositFixProcessor.class);

    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private TradeCacheService tradeCacheService;
    @Autowired
    private UpayOrderService upayOrderService;
    @Autowired
    private GatewaySupportService gatewaySupportService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private DepositProvider depositProvider;
    @Autowired
    private FixProcessor fixProcessor;
    @Autowired
    private DataRepository dataRepository;
    @Autowired
    private ExternalServiceFacade externalServiceFacade;

    public static long FIX_MIN_TIME_ALLOWED = 1000 * 60 * 10; //10分钟
    public static long FIX_MIN_HOUR_TIME_ALLOWED = 1000 * 60 * 60 * 24;//24小时

    /**
     * @param merchantId 有可能为空
     * @param sn
     * @return
     */
    @SuppressWarnings("unchecked")
    public TransactionContext fixFreeze(String sn, String merchantId) {
        Map<String, Object> order = repository.getOrderByOrderSn(merchantId, sn);
        Map<String, Object> freezeTransaction = null;
        boolean inHbase = false;
        if (order == null) {
            order = upayOrderService.getOrderBySn(merchantId, sn, null);
            if(order == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            freezeTransaction = gatewaySupportService.getLatestTransactionByOrderSn(merchantId, sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
            if(freezeTransaction == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            inHbase = true;
            Map<String,Object> extraOutFields = MapUtil.getMap(freezeTransaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
            extraOutFields.put(Transaction.IN_HBASE, true);
            freezeTransaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }

        merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(order, Order.SN);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        long ctime = BeanUtil.getPropLong(order, DaoConstants.CTIME);
        int payway = BeanUtil.getPropInt(order, Order.PAYWAY);
        String  provider = BeanUtil.getPropString(order, Order.PROVIDER);
        long oldMtime = BeanUtil.getPropLong(order, DaoConstants.MTIME);

        if(!inHbase && existFreezeSuccessTransaction(merchantId, orderSn)){
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }
        if(orderStatus == Order.STATUS_DEPOSIT_CREATED || orderStatus == Order.STATUS_DEPOSIT_FREEZE_ERROR 
                || orderStatus == Order.STATUS_DEPOSIT_FREEZE_CANCELED || orderStatus == Order.STATUS_DEPOSIT_CANCELED){
            if (orderStatus == Order.STATUS_DEPOSIT_CREATED){
                if(System.currentTimeMillis() - ctime < FIX_MIN_TIME_ALLOWED) {
                    throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_NOT_SUPPORT_MESSAGE);
                }
            }else if(orderStatus == Order.STATUS_DEPOSIT_CREATED && System.currentTimeMillis() - ctime < FIX_MIN_HOUR_TIME_ALLOWED ){
                throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
            }else if(orderStatus == Order.STATUS_DEPOSIT_FREEZE_CANCELED){
                logger.warn("fix order, status is DEPOSIT_FREEZE_CANCELED: {} {} {}", orderSn, provider, payway);
            }

        }else{
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }

        if (orderStatus == Order.STATUS_DEPOSIT_CREATED && BeanUtil.getPropInt(freezeTransaction,Transaction.STATUS) == Transaction.STATUS_SUCCESS ){
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE);
        }
        freezeTransaction = inHbase ? freezeTransaction : repository.getFreezeTransactionByOrderSn(merchantId, orderSn);
        final TransactionContext context = workflowManager.createTransactionContext(null, order, freezeTransaction);
        final Workflow workflow = context.getWorkflow();
        externalServiceFacade.resetSignParams(context.getServiceProvider(), freezeTransaction);
        MpayServiceProvider serviceProvider = depositProvider.matchServiceProvider(context);
        String rcFlag = serviceProvider.depositQuery(context);
        if(Workflow.RC_PAY_SUCCESS.equals(rcFlag)){
            if(orderStatus == Order.STATUS_DEPOSIT_FREEZE_ERROR) {
                String terminalSnOrStoreSn = BeanUtil.getPropString(context.getTransaction(), (null != BeanUtil.getPropString(order, Order.TERMINAL_ID)) ? Transaction.KEY_TERMINAL_SN: Transaction.KEY_STORE_SN);
                tradeCacheService.removeTradeCache(terminalSnOrStoreSn, MapUtil.getString(order, Order.CLIENT_SN), MapUtil.getString(order, Order.SN));
            }
            long currentMtime = BeanUtil.getPropLong(repository.getOrderByOrderSn(merchantId, orderSn), DaoConstants.MTIME);
            if(!inHbase && oldMtime != currentMtime){
                throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_ORDER_ALEADY_MODIFYED, UpayErrorScenesConstant.FIX_ORDER_ALEADY_MODIFYED_MESSAGE);
            }
            freezeTransaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
            freezeTransaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
            BeanUtil.setNestedProperty(freezeTransaction, Transaction.KEY_IS_FIX, true);
            final String finalMerchantId = merchantId;
            if(!inHbase) {
                //有可能net相关字段已经被清0，现在恢复
                PaymentUtil.updateOrderPaymentsNetAmountToAmountTotal((List<Map<String, Object>>) BeanUtil.getNestedProperty(order, PaymentUtil.ORDER_PAYMENTS_PATH));
                Map<String, Object> orderUpdate = CollectionUtil.hashMap(
                        DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                        Order.MERCHANT_ID, finalMerchantId,
                        Order.NET_ORIGINAL, BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL),
                        Order.NET_EFFECTIVE, BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL),
                        Order.NET_DISCOUNT, BeanUtil.getPropLong(order, Order.TOTAL_DISCOUNT),
                        Order.ITEMS, order.get(Order.ITEMS)
                        );
                repository.doInTransaction(new Runnable() {
                    @Override
                    public void run() {
                        repository.getOrderDao().updatePart(orderUpdate);
                    }
                });
                order.putAll(orderUpdate);
            }
            workflow.finish(context);
        }else{
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.FIX_FAIL, UpayErrorScenesConstant.FIX_FAIL_MESSAGE);
        }
        return context;
    }

    private boolean existFreezeSuccessTransaction(String merchantId, String orderSn){
        long count = repository.getTransactionDao().filter(
                Criteria.where(Transaction.ORDER_SN).is(orderSn)
                        .with(Transaction.MERCHANT_ID).is(merchantId)
                        .with(Transaction.TYPE).is(Transaction.TYPE_DEPOSIT_FREEZE)
                        .with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)
        ).count();
        return count == 0 ? false : true;
    }

    public Map<String, Object> fixConsumeToSuccess(String sn, String tsn, List<Map<String,Object>> refundChannelPayments) {
        if (distributedLock.isNotCanOrderFix(sn)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR, UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR_MESSAGE);
        }
        Map<String,Object> transaction = repository.getTransactionByTsn(null, tsn);
        Map<String, Object> order = null;
        boolean inHbase = false;
        if(transaction == null) {
            order = upayOrderService.getOrderBySn(null, sn, null);
            if(order == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            if(System.currentTimeMillis() - MapUtil.getLongValue(order, DaoConstants.CTIME) > 6 * 30 * 24 * 60 * 60 * 1000L) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            List<Map<String, Object>> transactions = upayOrderService.getOriginalTransactionListByOrderSn(MapUtil.getString(order, Order.SN));
            if(transactions == null || transactions.isEmpty()) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            for (Map<String, Object> tmpTransaction : transactions) {
                if(Objects.equals(MapUtil.getString(tmpTransaction, Transaction.TSN), tsn)) {
                    transaction = tmpTransaction;
                    break;
                }
            }
            if(transaction == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            
            inHbase = true;
            Map<String,Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS, new HashMap());
            extraOutFields.put(Transaction.IN_HBASE, true);
            extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CONSUME, true);
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        if(!inHbase) {
            order = repository.getOrderByOrderSn(MapUtil.getString(transaction, Transaction.MERCHANT_ID), sn);
            // 如果订单间隔一个月做预授权完成，完成失败后在隔日之后勾兑时，会报订单不存在（数据不在在线库中，已被删除）
            if(order == null) {
                order = upayOrderService.getOrderBySn(MapUtil.getString(transaction, Transaction.MERCHANT_ID), sn, null);
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_HISTORY_DEPOSIT_CONSUME, true);
            }
        }
        if(null == order){
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        int type = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        if(type != Transaction.TYPE_DEPOSIT_CONSUME || transactionStatus == Transaction.STATUS_SUCCESS){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_CONSUME_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_CONSUME_ORDER_STATUS_ERROR_MESSAGE);
        }
        if(!sn.equals(BeanUtil.getPropString(transaction, Transaction.ORDER_SN))){
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_AND_TSN_NOT_MATCH, UpayErrorScenesConstant.ORDER_AND_TSN_NOT_MATCH_MESSAGE);
        }

        if(Order.STATUS_DEPOSIT_CONSUME_ERROR != orderStatus && Order.STATUS_DEPOSIT_CONSUME_INPROGRESS != orderStatus && Order.STATUS_DEPOSIT_FREEZED != orderStatus){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_CONSUME_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_CONSUME_ORDER_STATUS_ERROR_MESSAGE);
        }else if(Order.STATUS_DEPOSIT_CONSUME_INPROGRESS == orderStatus && System.currentTimeMillis() - BeanUtil.getPropLong(transaction, DaoConstants.CTIME) < FIX_MIN_TIME_ALLOWED) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_CONSUME_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_CONSUME_ORDER_STATUS_ERROR_MESSAGE);
        }
        final TransactionContext context = workflowManager.createTransactionContext(null, order, transaction);
        final Workflow workflow = workflowManager.matchWorkflow(transaction);
        externalServiceFacade.resetSignParams(context.getServiceProvider(), transaction);
        String result = "";
        if (depositProvider.sqbDeposit(context)) {  //收钱吧预授权这里不做查询。勾兑回来就是成功的
            result = Workflow.RC_CONSUME_SUCCESS;
        } else {
            if(Order.PAYWAY_ALIPAY2 == BeanUtil.getPropInt(order, Order.PAYWAY)) {
                result = Workflow.RC_PAY_SUCCESS.equals(context.getServiceProvider().query(context)) ? Workflow.RC_CONSUME_SUCCESS : Workflow.RC_CONSUME_FAIL;
            }else {
                result = context.getServiceProvider().depositQuery(context);
            }
        }
        if(!Workflow.RC_CONSUME_SUCCESS.equals(result)){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_CONSUME_QUERY_ERROR, UpayErrorScenesConstant.FIX_CONSUME_QUERY_ERROR_MESSAGE);
        }
        if (depositProvider.sqbDeposit(context)) {
            final String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
            Map<String, Object> freezeTransaction = dataRepository.getFreezeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
            long refundAmount = BeanUtil.getPropLong(transaction, Transaction.KEY_DEPOSIT_REFUND_EFFECTIVE_AMOUNT);
            //收钱吧预授权发生退款
            if (refundAmount > 0L) {
                fixProcessor.updateTransactionChannelPayment(order, freezeTransaction, transaction, refundChannelPayments);
                if (CollectionUtils.isEmpty(refundChannelPayments)) {
                    logger.warn("收钱吧预授权完成勾兑,refundChannelPayments为空,tsn:{}", transaction.get(Transaction.TSN));
                }
            }
        }
        transaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_FIX, true);
        workflow.finish(context);
        context.disappear();
        return UpayUtil.apiSuccess(UpayUtil.bizResponse("SUCCESS", null, null, null, order));
    }
    
    public Map<String, Object> fixOrderIfConsumeNotSuccess(String sn) {
        boolean isDBTrade = true;
        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        if(order == null){
            order = upayOrderService.getOrderBySn(null, sn, null);
            if(order == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            isDBTrade = false;
        }
        Map<String, Object> transaction = gatewaySupportService.getLatestTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        if(Order.STATUS_DEPOSIT_CONSUME_ERROR != orderStatus && Order.STATUS_DEPOSIT_CONSUME_INPROGRESS != orderStatus){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT_MESSAGE);
        }else if(Order.STATUS_DEPOSIT_CONSUME_INPROGRESS == orderStatus && System.currentTimeMillis() - BeanUtil.getPropLong(transaction, DaoConstants.CTIME) < FIX_MIN_TIME_ALLOWED) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT, UpayErrorScenesConstant.FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT_MESSAGE);
        }
        String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        Map<String, Object> orderUpdate = CollectionUtil.hashMap(
                        DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                        Order.MERCHANT_ID, merchantId,
                        Order.STATUS, Order.STATUS_DEPOSIT_FREEZED
                );
        order.putAll(orderUpdate);
        if(isDBTrade) {
            orderUpdate.put(DaoConstants.VERSION, order.get(DaoConstants.VERSION));
            repository.getOrderDao().updatePart(orderUpdate);
        }else {
            try {
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
        }
        return UpayUtil.apiSuccess(UpayUtil.bizResponse("SUCCESS", null, null, null, order));
    }

    public Map<String, Object> fixCancel(String sn) {
        if (distributedLock.isNotCanOrderFix(sn)) {
            throw new UpayFixOrderStateError(UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR, UpayErrorScenesConstant.ORDER_FIX_CONCURRENCE_ERROR_MESSAGE);
        }
        boolean inDB = true;
        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        if(order == null){
            order = upayOrderService.getOrderBySn(null, sn, null);
            if(order == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            inDB = false;
        }
        Map<String, Object> transaction = gatewaySupportService.getLatestTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
        if(MapUtil.getInteger(transaction, Transaction.TYPE) != Transaction.TYPE_DEPOSIT_CANCEL) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        if(Order.STATUS_DEPOSIT_CANCEL_ERROR != orderStatus && Order.STATUS_DEPOSIT_CANCEL_INPROGRESS != orderStatus && Order.STATUS_DEPOSIT_FREEZED != orderStatus){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }else if(Order.STATUS_DEPOSIT_CANCEL_INPROGRESS == orderStatus && System.currentTimeMillis() - BeanUtil.getPropLong(transaction, DaoConstants.CTIME) < FIX_MIN_TIME_ALLOWED) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }
        if(!inDB) {
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_HISTORY_DEPOSIT_CANCEL, true);
        }
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_FIX, true);
        final TransactionContext context = workflowManager.createTransactionContext(null, order, transaction);
        final Workflow workflow = workflowManager.matchWorkflow(transaction);
        workflow.finish(context);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse("SUCCESS", null, null, null, order));
    }

    public Map<String, Object> fixOrderIfCancelNotSuccess(String sn) {
        boolean isDBTrade = true;
        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        if(order == null){
            order = upayOrderService.getOrderBySn(null, sn, null);
            if(order == null) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            isDBTrade = false;
        }
        Map<String, Object> transaction = gatewaySupportService.getLatestTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
        if(MapUtil.getInteger(transaction, Transaction.TYPE) != Transaction.TYPE_DEPOSIT_CANCEL) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        if(Order.STATUS_DEPOSIT_CANCEL_ERROR != orderStatus && Order.STATUS_DEPOSIT_CANCEL_INPROGRESS != orderStatus && Order.STATUS_DEPOSIT_FREEZED != orderStatus){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }else if(Order.STATUS_DEPOSIT_CANCEL_INPROGRESS == orderStatus && System.currentTimeMillis() - BeanUtil.getPropLong(transaction, DaoConstants.CTIME) < FIX_MIN_TIME_ALLOWED) {
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR, UpayErrorScenesConstant.FIX_ORDER_STATUS_ERROR_MESSAGE);
        }
        String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        Map<String, Object> orderUpdate = CollectionUtil.hashMap(
                        DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                        Order.MERCHANT_ID, merchantId,
                        Order.STATUS, Order.STATUS_DEPOSIT_FREEZED
                );
        order.putAll(orderUpdate);
        if(isDBTrade) {
            repository.getOrderDao().updatePart(orderUpdate);
        }else {
            try {
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("update order hbase data error, data = {} , ex= {}", order, e);
            }
        }
        return UpayUtil.apiSuccess(UpayUtil.bizResponse("SUCCESS", null, null, null, order));
    }
}
