package com.wosai.upay.service;

import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.helper.UpayServiceAnnotation;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.validation.PropNotBothNull;
import com.wosai.upay.validation.PropNotEmpty;
import com.wosai.upay.validation.PropPattern;
import com.wosai.upay.validation.PropSize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;

/**
 * <AUTHOR> by wkx
 * @date 2018/2/8
 **/
@UpayServiceAnnotation
@Validated
public interface SupportService {

    String WOSAI_STORE_ID = "wosai_store_id";
    String TERMINAL_SN = "terminal_sn";
    String DYNAMIC_ID = "dynamic_id";
    String DYNAMIC_ID_TYPE = "dynamic_id_type";
    String PAYWAY = "payway";
    String SUB_PAYWAY = "sub_payway";
    String RAWDATA = "rawdata";
    String TRADE_APP = "trade_app";
    String ORDER_SN = "order_sn";
    String HBFQ_NUM = "num";
    String FQ_TYPE = "type";
    String FQ_AMOUNT = "fq_amount";
    String COMBINATION_PAY = "combination_pay";
    String CHANGE_TYPE = "change_type";
    String PROFIT_SHARING = UpayService.PROFIT_SHARING;
    String MERCHANT_ID = "merchant_id";
    String PAYER_UID = UpayService.PAYER_UID;
    String SUB_OPENID = "sub_openid";
    String AUTHORIZATION_CODE = "authorization_code";
    String REASON = "reason";
    String CLIENT_SN = "client_sn";
    String SN = "sn";

    String USER_ID = "user_id";

    String STORE_SN = "store_sn";

    String TOTAL_BALANCE = "total_balance";

    String USER_AUTH_CODE = "user_auth_code";
    String APP_UP_IDENTIFIER = "app_up_identifier";

    String AUTH_TOKEN = "auth_token";

    String WAP_PAY_REQUEST = Transaction.WAP_PAY_REQUEST;


    Map<String,Object> queryUserId( @PropSize(value=DYNAMIC_ID, nullable=false, min=1, max=32, message="{value}付款条码必填，不可超过{max}字符")
                                    @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
                                                    Map<String, Object> request);

    Map<String,Object> queryUnionUserId(
            @PropNotEmpty.List({
                    @PropNotEmpty(value=USER_AUTH_CODE, message="{value}授权码不可为空"),
                    @PropNotEmpty(value=APP_UP_IDENTIFIER, message="{value}银联支付标识不可为空"),
            })
            @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
                                            Map<String, Object> request);

    void clearBasicCache(@PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")Map<String, Object> body);

    /**
     * 获取微信刷脸凭证
     * @param request
     * @return
     */
    Map<String, Object> getWxpayfaceAuthinfo(@PropSize(value=RAWDATA, nullable=false, min=1, max=2048, message="{value}人脸数据必填，不可超过{max}字符")
            @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
                                                     Map<String, Object> request);

    Map<String, Object> genSn( @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻门店号和{value2}喔噻终端序列号不能同时为空")
                                            Map<String, Object> request);

    Map<String, Object> changeTradeHbfq(
            @PropNotEmpty.List({
                @PropNotEmpty(value=MERCHANT_ID, message="{value}商户id不可为空"),
                @PropNotEmpty(value=ORDER_SN, message="{value}订单号不可为空"),
            })
            @PropPattern(value=HBFQ_NUM, nullable=false, regex="[0-9]\\d{0,2}", message="{value}花呗分期数为整数，长度不超过2位")
            Map<String, Object> body);

    Map<String, Object> changeTradeHbfqSubsidy(
            @PropNotEmpty.List({
                @PropNotEmpty(value=MERCHANT_ID, message="{value}商户id不可为空"),
                @PropNotEmpty(value=ORDER_SN, message="{value}订单号不可为空"),
                @PropNotEmpty(value=CHANGE_TYPE, message="{value}变更方式不可为空"),
            })
            Map<String, Object> body);

    /**
     * 微信支付分免确认模式 商户预授权(签约)
     * @param request
     * @return
     */
    Map<String, Object> auth(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = TERMINAL_SN, message = "{value}终端sn不可为空")
            }) Map<String, Object> request);

    /**
     * 微信支付分免确认模式 商户预授权(签约)查询
     * @param request
     * @return
     */
    Map<String, Object> authQuery(Map<String, Object> request);

    /**
     * 微信支付分免确认模式 商户预授权(签约)关系解除
     * @param request
     * @return
     */
    Map<String, Object> authTerminate(Map<String, Object> request);

    Map<String, Object> changeTradeProfitSharing(
            @PropNotEmpty.List({
                @PropNotEmpty(value=MERCHANT_ID, message="{value}商户id不可为空"),
                @PropNotEmpty(value=Transaction.TSN, message="{value}订单号不可为空")
            })
            Map<String, Object> body);

    Map<String, Object> queryHbfqNum(@PropNotBothNull.List({
                                        @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                        @PropNotBothNull(value=CLIENT_SN, value2=SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                                      })Map<String, Object> request);

    /**
     * 索迪斯-支付
     */
    Map<String, Object> sodexoExpenseByToken(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = USER_ID, message = "{value}用户ID为空"),
                    @PropNotEmpty(value = MERCHANT_ID, message = "{value}商户号为空"),
                    @PropNotEmpty(value = CLIENT_SN, message = "{value}商户订单号为空"),
                    @PropNotEmpty(value = WAP_PAY_REQUEST, message = "{value}回调信息为空")
            }) Map<String, Object> request);

    /**
     * 索迪斯-余额查询
     */
    Map<String, Object> sodexoBalanceQuery(
            @PropNotBothNull.List({
                @PropNotBothNull(value=STORE_SN, value2=TERMINAL_SN, message="{value}门店号和{value2}终端序列号不能同时为空"),
            })
            @PropNotEmpty.List({
                @PropNotEmpty(value = USER_ID, message = "{value}用户ID为空")
            }) Map<String, Object> request);


    /**
     * 支付宝-花呗分期吱口令码申领
     */
    Map<String, Object> getAlipayHbfqShareCode(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = TERMINAL_SN, message = "{value}终端SN为空")
            }) Map<String, Object> request);

    /**
     * 富友手续费更新并同步余额接口
     *
     * @param request
     * @return
     */
    Boolean updateFuyouTradeFeeSyncWallet(Map<String, Object> request);

    /**
     * 额度包交易流水完成接口
     * 用于额度包完全后置处理
     *
     * @param request
     * @return
     */
    Map<String, Object> finishQuotaTransaction(Map<String, Object> request);

    /**
     * 富友同步es与分账
     *
     * @param request
     * @return
     */
    Boolean syncFuyouAnalyzeTradeAndSharing(Map<String, Object> request);

    /**
     * 预授权补录备注/手机
     *
     * @param request
     */
    Map<String, Object> depositChangeReflect(@PropNotEmpty.List({
            @PropNotEmpty(value = MERCHANT_ID, message = "{value}商户号为空"),
            @PropNotEmpty(value = ORDER_SN, message = "{value}订单sn为空"),
    }) Map<String, Object> request);

    String getTnFromZJTLEncryptBody(String encryptBody);

    /**
     * 更新交易手续费 谨慎调用
     * @param request
     * @return
     */
    Map<String, Object> changeTradeFee(@PropNotEmpty.List({
            @PropNotEmpty(value = TERMINAL_SN, message = "{value}终端号不可为空"),
            @PropNotEmpty(value = SN, message = "{value}订单号不可为空"),
            @PropNotEmpty(value = UpayService.TSN, message = "{value}流水号不可为空"),
            @PropNotEmpty(value = TransactionParam.FEE_RATE, message = "{value}费率不可为空")
    }) Map<String, Object> request);

    String getTnFromSPDBEncryptBody(String encryptBody);

    /**
     * 确认支付
     *
     * @param request
     * @return
     */
    DeferredResult<Map<String, Object>> confirmPay(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MERCHANT_ID, message = "{value}商户id不可为空"),
                    @PropNotEmpty(value = ORDER_SN, message = "{value}订单号不可为空")}
            ) Map<String, Object> request);
}
