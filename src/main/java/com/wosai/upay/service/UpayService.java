package com.wosai.upay.service;

import com.wosai.upay.helper.UpayServiceAnnotation;
import com.wosai.upay.validation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * 支付网关接口定义。参数校验规则定义在接口方法上。校验失败的错误提示可以支持i18n。具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 * <AUTHOR>
 *
 */
@UpayServiceAnnotation
@Validated
public interface UpayService {
    String API_VER = "api_ver";
    String WOSAI_STORE_ID = "wosai_store_id";
    String TERMINAL_SN = "terminal_sn";
    String CLIENT_IP = "client_ip";
    String CLIENT_SN = "client_sn";
    String CLIENT_TSN = "client_tsn";
    String SN = "sn";
    String TSN = "tsn";
    String REFUND_REQUEST_NO = "refund_request_no";
    String DYNAMIC_ID = "dynamic_id";
    String DYNAMIC_ID_TYPE = "dynamic_id_type";
    String SUBJECT = "subject";
    String BODY = "body";
    String DESCRIPTION = "description";
    String TOTAL_AMOUNT = "total_amount";
    String REFUND_AMOUNT = "refund_amount";
    String PAYWAY = "payway";
    String SUB_PAYWAY = "sub_payway";
    String LONGITUDE = "longitude";
    String LATITUDE = "latitude";
    String EXTENDED = "extended";
    String GOODS_DETAILS = "goods_details";
    String REFLECT = "reflect";
    String PROFIT_SHARING = "profit_sharing";
    String OPERATOR = "operator";
    String DEVICE_ID = "device_id";
    String PAYER_UID = "payer_uid";
    String NOTIFY_URL = "notify_url";
    String CARD = "card";
    String CARDSN = "cardSn";
    String TRACK2 = "track2";
    String PIN = "pin";
    String NFC = "nfc";
    String PAYMENT_LIST = "payment_list";
    String SQB_ORIGIN = "sqb_origin"; //请求来源
    String TRADE_APP = "trade_app"; // 业务方
    String TRADE_DATE = "trade_date";

    String FLAG_APPLY_TRADE_COPROCESSOR = "flag_apply_trade_coprocessor";
    String FLAG_RETURN_PAYMENT_LIST = "flag_return_payment_list";
    String REFUND_CHANNEL_PAYMENTS = "refund_channel_payments";
    String FLAG_IS_MANUAL_FIX = "flag_is_manual_fix";
    String FLAG_IS_HISTORY_FIX = "flag_is_history_fix";
    String REAL_TRADE_FEE = "real_trade_fee";
    String FLAG_QUERY_PAY_OR_DEPOSIT_FREEZE = "flag_query_pay_or_deposit_freeze";
    String SCOPE = "__scope";



    DeferredResult<Map<String, Object>> pay(@PropNotEmpty(value=CLIENT_SN, message="{value}商户订单号不可为空")
                            @PropSize.List({
                                @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号必填，不可超过{max}字符"),
                                @PropSize(value=SUBJECT, nullable=false, min=1, max=64, message="{value}标题必填，不可超过{max}字符"),
                                    @PropSize(value=OPERATOR,  max=45, message="{value}收银员不可超过{max}字符"),
                                @PropSize(value=DESCRIPTION, max=255, message="{value}详情不可超过{max}字符")
                            })
                            @PropPattern(value=TOTAL_AMOUNT, nullable=false, regex="[1-9]\\d{0,9}", message="{value}金额为大于0的整数，长度不超过10位，以分为单位")
                            @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
                            @PropNotNullOnProp.List({
                                    @PropNotNullOnProp(value = LONGITUDE, value2 = LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                                    @PropNotNullOnProp(value = LATITUDE, value2 = LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

                            })
                            @PropIsMap.List({
                                    @PropIsMap(value=EXTENDED, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式"),
                                    @PropIsMap(value=PROFIT_SHARING, nullable = true, emptyable = true, message = "{value}分账参数必须为Map格式")
                            })
                            Map<String, Object> request, HttpServletResponse response);

    DeferredResult<Map<String, Object>> multiplePay(@PropNotEmpty.List({
                                                        @PropNotEmpty(value=CLIENT_SN, message="{value}商户订单号不可为空"),
                                                        @PropNotEmpty(value=TERMINAL_SN, message="{value}终端号不可为空")})
                                                            Map<String, Object> request, HttpServletResponse response);

    Map<String, Object> query(@PropNotBothNull.List({
                                @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                @PropNotBothNull(value=CLIENT_SN, value2=SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                              })
                              @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号必填，不可超过{max}字符")
                              Map<String, Object> request);

    Map<String, Object> cancel(@PropNotBothNull.List({
                                   @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                   @PropNotBothNull(value=CLIENT_SN, value2=SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符")
                               Map<String, Object> request);

    Map<String, Object> revoke(@PropNotBothNull.List({
                                    @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                    @PropNotBothNull(value=CLIENT_SN, value2=SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符")
                               Map<String, Object> request);

    /**
     * 内部接口
     * @param request
     * @return
     */
    Map<String, Object> reconcileRevoke(@PropNotBothNull.List({
                                            @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                            @PropNotBothNull(value=CLIENT_SN, value2=SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符")
                               Map<String, Object> request);


    DeferredResult<Map<String, Object>> precreate(@PropNotEmpty(value=CLIENT_SN, message="{value}商户订单号不可为空")
                                  @PropSize.List({
                                      @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号必填，不可超过{max}字符"),
                                      @PropSize(value=SUBJECT, nullable=false, min=1, max=64, message="{value}标题必填，不可超过{max}字符"),
                                      @PropSize(value=DESCRIPTION, max=255, message="{value}详情不可超过{max}字符"),
                                      @PropSize(value=OPERATOR, max=45, message="{value}收银员不可超过{max}字符")
                                  })
                                  @PropPattern.List({
                                      @PropPattern(value=TOTAL_AMOUNT, nullable=false, regex="[1-9]\\d{0,9}", message="{value}金额为大于0的整数，长度不超过10位，以分为单位"),
                                      @PropPattern(value=PAYWAY, nullable=false, regex="1|2|3|4|5|6|7|8|17|18|21|22|27|28|29|105", message="{value}支付通道为整数, 1:支付宝 3:微信 4:百付宝 5:京东钱包 6:QQ钱包 7: 苹果支付 17:银联二维码 18:翼支付 21:网银支付 22:索迪斯 27:微企付 28:校园卡 29:澳门通 105:储值卡支付"),
                                      @PropPattern(value=SUB_PAYWAY, nullable=true, regex="2|3|4|5|6", message="{value}支付模式为整数, 2: QRCODE 3:WAP 4:MINI ,5:APP ,6:H5 默认为2"),
                                  })
                                  @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
                                  @PropNotNullOnProp.List({
                                          @PropNotNullOnProp(value = LONGITUDE, value2 = LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                                          @PropNotNullOnProp(value = LATITUDE, value2 = LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

                                  })
                                  @PropIsMap.List({
                                          @PropIsMap(value=EXTENDED, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式"),
                                          @PropIsMap(value=PROFIT_SHARING, nullable = true, emptyable = true, message = "{value}分账参数必须为Map格式")
                                  })
                                  Map<String, Object> request, HttpServletResponse response);

    Map<String, Object> refund(@PropNotBothNull.List({
                                   @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                   @PropNotBothNull(value=CLIENT_SN, value2=SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize.List({
                                   @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符"),
                                   @PropSize(value=OPERATOR, max=45, message="{value}收银员不可超过{max}字符"),
                                   @PropSize(value=REFUND_REQUEST_NO, nullable=false, min=1, max=31, message="{value}退款序列号必填，不可超过{max}字符")
                               })
                               @PropPattern(value=REFUND_AMOUNT, nullable=false, regex="\\d{1,10}", message="{value}金额为整数，长度不超过10位，以分为单位")
                               @PropMin(value = REFUND_AMOUNT, min=1, message="{value}金额必须大于0")
                               Map<String, Object> request);

    void notify(String upayOrderNumber, Map<String, Object> notification);

    /**
     * 付款恢复成功，用于我们这边记录为付款失败， 第三方通道为付款成功
     * 内部接口 不对外提供
     * @param request
     * @return
     */
    Map<String,Object> fix(@PropNotEmpty(value=SN, message="{value}订单号不可为空")
                           Map<String, Object> request);

    /**
     * 退款撤销，用于处理我们这边记录为退款成功，第三方通道由于用户账户或者卡异常，不能原路退回的情况。
     * 内部接口 不对外提供
     * @param request
     * @return
     */
    Map<String, Object> refundRevoke(@PropNotEmpty.List({
                                        @PropNotEmpty(value=SN, message="{value}订单号不可为空"),
                                        @PropNotEmpty(value=TSN, message="{value}流水号不可为空")
                                    })
                                     Map<String, Object> request);

    /**
     * 退款或者撤单恢复成功，用于处理我们这边记录为退款错误或者撤单错误，第三方通道是成功的情况。
     * 内部接口 不对外提供
     * @param request
     * @return
     */
    Map<String, Object> fixCancelOrRefund(@PropNotEmpty.List({
                                                @PropNotEmpty(value=SN, message="{value}订单号不可为空"),
                                                @PropNotEmpty(value=TSN, message="{value}流水号不可为空")
                                        })
                                        Map<String, Object> request);

    /**
     * 修改订单状态为订单的正常状态 已支付或者已部分退款, 用于处理第三方退款失败后，我们系统的订单状态记录为退款错误的情况。
     * 当部分退款，如果上一笔退款失败原因是未知错误，那么，系统暂时不允许继续退款，需要人工确认退款结果，然后修改订单状态
     * 才能继续退款， 提供此接口， 避免直接操作数据库修改订单状态
     * 内部接口 不对外提供
     * @param request
     */
    Map<String, Object> fixOrderStatusIfRefundNotSuccess(@PropNotEmpty(value = SN, message = "{value}订单号不可为空")
                                          Map<String, Object> request);

    /**
     * 修改订单状态为已支付, 用于处理第三方撤单失败后，我们系统的订单状态记录为撤单错误的情况。
     * 当过了撤单期限后，永远都撤单不成功了
     * 需要修改订单状态为已支付，才能继续退款， 提供此接口， 避免直接操作数据库修改订单状态
     * 内部接口 不对外提供
     * @param request
     */
    Map<String, Object> fixOrderStatusToPaidIfCancelNotSuccess(@PropNotEmpty(value = SN, message = "{value}订单号不可为空")
                                                                 Map<String, Object> request);

    Map<String, Object> monitor();

    /**
     * 修改系统运行参数开关
     * @param flags
     *  flag_apply_trade_coprocessor true|false 打开关闭活动服务
     */
    void updateFlags(Map<String,Object> flags);

    DeferredResult<Map<String, Object>> preCreateQr(@PropNotEmpty(value=CLIENT_SN, message="{value}商户订单号不可为空")
                                   @PropSize.List({
                                           @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号必填，不可超过{max}字符"),
                                           @PropSize(value=SUBJECT, nullable=false, min=1, max=64, message="{value}标题必填，不可超过{max}字符"),
                                           @PropSize(value=DESCRIPTION, max=255, message="{value}详情不可超过{max}字符"),
                                           @PropSize(value=OPERATOR,nullable = false, max=45, message="{value}收银员必填，不可超过{max}字符")
                                   })
                                   @PropPattern.List({
                                           @PropPattern(value=TOTAL_AMOUNT, nullable=false, regex="[1-9]\\d{0,9}", message="{value}金额为大于0的整数，长度不超过10位，以分为单位"),
                                           @PropPattern(value=PAYWAY, nullable=true, regex="1|2|3|4|5|6|8|17|18|21|105", message="{value}支付通道为整数, 1:支付宝 3:微信 4:百付宝 5:京东钱包 6:QQ钱包 17:银联二维码 18:翼支付 21:网银支付 105:储值卡支付"),
                                           @PropPattern(value=SUB_PAYWAY, nullable=true, regex="2|3|4|5|6", message="{value}支付模式为整数, 2: QRCODE 3:WAP 4:MINI ,5:APP ,6:H5 默认为2"),
                                   })
                                   @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
                                   @PropNotNullOnProp.List({
                                           @PropNotNullOnProp(value = LONGITUDE, value2 = LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                                           @PropNotNullOnProp(value = LATITUDE, value2 = LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

                                   })
                                   @PropIsMap.List({
                                           @PropIsMap(value=EXTENDED, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式"),
                                           @PropIsMap(value=PROFIT_SHARING, nullable = true, emptyable = true, message = "{value}分账参数必须为Map格式")
                                   })
                                           Map<String, Object> request);

    Map<String, Object> coldTradeRefund(@PropNotBothNull.List({
                                   @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                               })
                               @PropNotEmpty.List({
                                   @PropNotEmpty(value=SN, message="{value}收钱吧订单号不可为空"),
                                   @PropNotEmpty(value=TRADE_DATE, message="{value}交易时间不可为空")
                               })
                               @PropSize.List({
                                   @PropSize(value=OPERATOR, max=45, message="{value}收银员不可超过{max}字符"),
                                   @PropSize(value=REFUND_REQUEST_NO, nullable=false, min=1, max=31, message="{value}退款序列号必填，不可超过{max}字符")
                               })
                               @PropPattern.List({
                                   @PropPattern(value=REFUND_AMOUNT, nullable=false, regex="\\d{1,10}", message="{value}金额为整数，长度不超过10位，以分为单位"),
                                   @PropPattern(value=TRADE_DATE, nullable=false, regex="^\\d{8}$", message="{value}交易日期格式错误")
                               })
                               @PropMin(value = REFUND_AMOUNT, min=1, message="{value}金额必须大于0")
                               Map<String, Object> request);

    
    /**
     * 生成先享后付订单
     * 
     * @param params
     * @return
     */
    Map<String, Object> createFitnessOrder(
                                @PropSize.List({
                                    @PropSize(value=CLIENT_SN, min=1, max=32, message="{value}商户订单号必填，不可超过{max}字符"),
                                    @PropSize(value=SUBJECT, nullable=false, min=1, max=64, message="{value}标题必填，不可超过{max}字符"),
                                })
                                @PropNotEmpty.List({
                                    @PropNotEmpty(value=TERMINAL_SN, message="{value}终端序列号不能为空"),
                                    @PropNotEmpty(value=CLIENT_SN, message="{value}商户订单号不可为空"),
                                    @PropNotEmpty(value=TRADE_APP, message="{value}业务方标识不可为空"),
                                    @PropNotEmpty(value="channel_no", message="{value}支付宝订单号不可为空"),
                                })
                                @PropPattern.List({
                                    @PropPattern(value=TOTAL_AMOUNT, nullable=false, regex="\\d{1,10}", message="{value}金额为整数，长度不超过10位，以分为单位"),
                                })
                                Map<String, Object> request);
}
