package com.wosai.upay.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.TransactionContext;
import com.wosai.upay.workflow.WorkflowManager;

@Service
public class TradeCacheService {
    private static Logger logger = LoggerFactory.getLogger(TradeCacheService.class);
    
    private static final Set<String> ORDER_CACHE_KEYS = CollectionUtil.hashSet(Order.SN, Order.CLIENT_SN, Order.PAYWAY, Order.SUB_PAYWAY, Order.STATUS, Order.ORIGINAL_TOTAL, Order.NET_ORIGINAL, Order.SUBJECT, 
            Order.BODY, Order.STORE_ID, Order.TERMINAL_ID, Order.TCP_MODIFIED, Order.ITEMS);
    
    private static final Set<String> TRANSACTION_CACHE_KEYS = CollectionUtil.hashSet(Transaction.TYPE, Transaction.CLIENT_TSN, DaoConstants.CTIME, Transaction.STATUS, Transaction.BUYER_LOGIN, 
            Transaction.BUYER_UID, Transaction.TRADE_NO, Transaction.FINISH_TIME, Transaction.CHANNEL_FINISH_TIME, Transaction.OPERATOR, Transaction.REFLECT, Transaction.PROVIDER_RESPONSE, 
            Transaction.EXTRA_OUT_FIELDS, Transaction.BIZ_ERROR_CODE, Transaction.ORIGINAL_AMOUNT, Transaction.ITEMS);
    
    private static final Set<String> BLOBS_KEY = CollectionUtil.hashSet(Order.ITEMS, Transaction.PROVIDER_RESPONSE, Transaction.EXTRA_OUT_FIELDS, Transaction.BIZ_ERROR_CODE);
    
    private static final String PREFIX_TRADE_KEY = "tradecache-";
    private static final String DEFAULT_SEPARATE = "-";
    private static final String END_TRANSACTION_KEY = ":transaction";
    private static final String END_ORDER_KEY = ":order";
    private static final String PREFIX_CLIENT_SN_MAPPING_KEY = "tradecache-sn:";
    private static final String PREFIX_HIS_REFUND_CLIENT_SN_KEY = "tradecache-his-refund-client-sn:";
    private static final String PREFIX_HIS_REFUND_SN_KEY = "tradecache-his-refund-sn:";
    private static final String PREFIX_CROSS_MERCHANT_REFUND_KEY = "cross-merchant-refund-";

    // 交易cache key，用于撤单
    private static final String PREFIX_PAY_HOST = "pay-host:";

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private WorkflowManager workflowManager;

    /**
     * 记录历史退款订单信息
     * @param terminalSnOrStoreSn
     * @param clientSn
     * @param sn
     */
    public void setHistoryRefundInfoCache(String terminalSnOrStoreSn, String clientSn, String sn){
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            String clientSnKey = PREFIX_HIS_REFUND_CLIENT_SN_KEY + terminalSnOrStoreSn + DEFAULT_SEPARATE + clientSn;
            String snKey = PREFIX_HIS_REFUND_SN_KEY + terminalSnOrStoreSn + DEFAULT_SEPARATE +  sn;
            try{
                redisTemplate.executePipelined((RedisCallback) connection -> {
                    if(clientSn != null){
                        connection.setEx(clientSnKey.getBytes(), ApolloConfigurationCenterUtil.getHistoryRefundInfoCacheTimeout(),  clientSn.getBytes());
                    }
                    if(sn != null){
                        connection.setEx(snKey.getBytes(), ApolloConfigurationCenterUtil.getHistoryRefundInfoCacheTimeout(),  sn.getBytes());
                    }
                    return null;
                });
            }catch (Exception e){
                logger.info("error in putHistoryRefundInfoCache", e);
            }
        }
    }


    /**
     * 判断是否是一笔历史退款
     * @param terminalSnOrStoreSn
     * @param clientSn
     * @param sn
     * @return
     */
    public boolean isHistoryRefund(String terminalSnOrStoreSn, String clientSn, String sn){
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()){
            String clientSnKey = PREFIX_HIS_REFUND_CLIENT_SN_KEY + terminalSnOrStoreSn + DEFAULT_SEPARATE + clientSn;
            String snKey = PREFIX_HIS_REFUND_SN_KEY + terminalSnOrStoreSn + DEFAULT_SEPARATE +  sn;
            boolean historyRefund = false;
            try{
                if(clientSn != null){
                    historyRefund = redisTemplate.hasKey(clientSnKey);
                }
                if(!historyRefund && sn != null){
                    historyRefund = redisTemplate.hasKey(snKey);
                }
                return historyRefund;
            }catch (Exception e){
                logger.info("error in isHistoryRefundByClientSn", e);
            }
            return false;
        }else{
            return false;
        }

    }

    
    public void putTradeCache(String terminalSnOrStoreSn, String clientSn, TransactionContext context) {
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            try {
                Map<byte[], byte[]> orderCacheMap = convertToByteCacheMap(context.getOrder(), ORDER_CACHE_KEYS);
                Map<byte[], byte[]> transactionCacheMap = convertToByteCacheMap(context.getTransaction(), TRANSACTION_CACHE_KEYS);
                byte[] transactionCacheKey = StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn, END_TRANSACTION_KEY).getBytes();
                byte[] orderCacheKey = StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn, END_ORDER_KEY).getBytes();
                
                redisTemplate.executePipelined((RedisCallback) connection -> {
                    connection.hMSet(orderCacheKey, orderCacheMap);
                    connection.expire(orderCacheKey, ApolloConfigurationCenterUtil.getTradeCacheTimeout());
                    
                    connection.hMSet(transactionCacheKey, transactionCacheMap);
                    connection.expire(transactionCacheKey, ApolloConfigurationCenterUtil.getTradeCacheTimeout());
                    
                    connection.setEx(StringUtils.join(PREFIX_CLIENT_SN_MAPPING_KEY, MapUtil.getString(context.getOrder(), Order.SN)).getBytes(), ApolloConfigurationCenterUtil.getTradeCacheTimeout(),  clientSn.getBytes());
       
                    return null;
                });
                context.setCache(true);
            }catch (Exception e) {
                logger.info("error in putTradeCache", e);
            }
        }
    }
    
    public void updateTradeCache(String terminalSnOrStoreSn, String clientSn, Map updateOrderInfo, Map updateTransactionInfo) {
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            if(null != updateOrderInfo) {
                try {
                    String orderKey = StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn , END_ORDER_KEY);
                    redisTemplate.boundHashOps(orderKey).putAll(convertToStringCacheMap(updateOrderInfo, ORDER_CACHE_KEYS));
                }catch (Exception e) {
                    logger.info("error in update order cache", e);
                }
            }
            
            if(null != updateTransactionInfo) {
                try {
                    String transactionKey = StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn, END_TRANSACTION_KEY);
                    redisTemplate.boundHashOps(transactionKey).putAll(convertToStringCacheMap(updateTransactionInfo, TRANSACTION_CACHE_KEYS));
                }catch (Exception e) {
                    logger.info("error in update transaction cache", e);
                }
            }
        }
    }
    
    public void removeTradeCache(String terminalSnOrStoreSn, String clientSn, String orderSn) {
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            try {
                redisTemplate.delete(
                        Arrays.asList(StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn , END_ORDER_KEY), 
                                      StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn, END_TRANSACTION_KEY),
                                      StringUtils.join(PREFIX_CLIENT_SN_MAPPING_KEY, orderSn)
                                )
                        );
            }catch (Exception e) {
                logger.info("failed to redisTemplate.delete, error = {}", e);
            }
        }
    }
    
    public TransactionContext getTradeCache(String terminalSnOrStoreSn, String clientSn, String orderSn) {
        if(StringUtils.empty(clientSn)) {
            if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
                Object redisClientSn = redisTemplate.boundValueOps(StringUtils.join(PREFIX_CLIENT_SN_MAPPING_KEY, orderSn)).get();
                if(null == redisClientSn || StringUtils.isEmpty(redisClientSn.toString())) {
                    return null;
                }
                clientSn = redisClientSn.toString();
            } else {
                return null;
            }
        }
        
        TransactionContext context = workflowManager.lookupTransactionContext(terminalSnOrStoreSn, clientSn);
        if(null == context && ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            Map order = null;
            Map transaction = null;
            try {
                String orderKey = StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn , END_ORDER_KEY);
                BoundHashOperations val = redisTemplate.boundHashOps(orderKey);
                order = val.entries();
                if(null != order && !order.isEmpty()) {
                    for (String key : BLOBS_KEY) {
                        if(null != order.get(key)) {
                            order.put(key, JsonUtil.jsonStrToObject(order.get(key).toString(), Map.class));
                        }
                    }

                    String transactionKey = StringUtils.join(PREFIX_TRADE_KEY, terminalSnOrStoreSn, DEFAULT_SEPARATE, clientSn, END_TRANSACTION_KEY);
                    val = redisTemplate.boundHashOps(transactionKey);
                    transaction = val.entries();
                    if(null != transaction && !transaction.isEmpty()) {
                        for (String key : BLOBS_KEY) {
                            if(null != transaction.get(key)) {
                                transaction.put(key, JsonUtil.jsonStrToObject(transaction.get(key).toString(), Map.class));
                            }
                        }
                        if(transaction.get(Transaction.REFLECT) != null) {
                            try {
                                transaction.put(Transaction.REFLECT, JsonUtil.jsonStringToObject(transaction.get(Transaction.REFLECT).toString(), Map.class));
                            }catch (Exception e) {
                                logger.info("convert transaction.reflect to map fail", e);
                            }
                        }
                    }
                }
            }catch (Exception e) {
                logger.info("error in getTradeCache", e);
            }
            
            if(null != transaction && !transaction.isEmpty()) {
                context = new TransactionContext(order, transaction);
            }
        }
        return context;
    }
    
    private Map<String, String> convertToStringCacheMap(Map<String,Object> changInfo, Set<String> keys) {
        Map<String, String> cacheMap = null;
        if(null == changInfo || null == keys) {
            return cacheMap;
        }
        cacheMap = new HashMap<String, String>();
        for (String column : changInfo.keySet()) {
            if(null == changInfo.get(column) || !keys.contains(column)) {
                continue;
            }
            if(BLOBS_KEY.contains(column)) {
                changInfo.put(column, JsonUtil.toJsonStr(changInfo.get(column)));
            }else {
                cacheMap.put(column, changInfo.get(column).toString());
            }
        }
        
        return cacheMap;
    }

    private Map<byte[], byte[]> convertToByteCacheMap(Map<String,Object> changInfo, Set<String> keys) {
        Map<byte[], byte[]> cacheMap = null;
        if(null == changInfo || null == keys) {
            return cacheMap;
        }
        cacheMap = new HashMap<byte[], byte[]>();
        for (String column : changInfo.keySet()) {
            if(null == changInfo.get(column) || !keys.contains(column)) {
                continue;
            }
            if(BLOBS_KEY.contains(column)) {
                cacheMap.put(column.getBytes(), JsonUtil.toJsonStr(changInfo.get(column)).getBytes());
            }else {
                cacheMap.put(column.getBytes(), changInfo.get(column).toString().getBytes());
            }
        }
        
        return cacheMap;
    }

    public void putCrossMerchantRefundCache(String terminalOrStoreSn, String sn, String merchantId) {
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            try {
                String crossMerchantRefundKey = StringUtils.join(PREFIX_CROSS_MERCHANT_REFUND_KEY, terminalOrStoreSn, DEFAULT_SEPARATE, sn , END_ORDER_KEY);
                redisTemplate.executePipelined((RedisCallback) connection -> {
                    connection.setEx(crossMerchantRefundKey.getBytes(), 1800L, merchantId.getBytes());
                    return null;
                });

            }catch (Exception e) {
                logger.info("error in putCrossMerchantRefundCache", e);
            }
        }
    }

    public String getCrossMerchantRefundCache(String terminalOrStoreSn, String sn) {
        if(ApolloConfigurationCenterUtil.isTradeCacheEnable()) {
            try {
                String orderKey = StringUtils.join(PREFIX_CROSS_MERCHANT_REFUND_KEY, terminalOrStoreSn, DEFAULT_SEPARATE, sn , END_ORDER_KEY);
                return (String)redisTemplate.boundValueOps(orderKey).get();
            }catch (Exception e) {
                logger.info("error in getCrossMerchantRefundCache", e);
            }
        }
        return null;
    }
}
