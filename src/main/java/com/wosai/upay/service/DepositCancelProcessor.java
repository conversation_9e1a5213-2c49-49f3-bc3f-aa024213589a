package com.wosai.upay.service;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.TransactionContext;
import com.wosai.upay.workflow.Workflow;
import com.wosai.upay.workflow.WorkflowManager;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

@Service
public class DepositCancelProcessor {
    private static Logger logger = LoggerFactory.getLogger(DepositCancelProcessor.class);
    public static long CANCEL_MIN_TIME_ALLOWED = 1000 * 60 * 10; //10分钟
    
    public static long RECONCILE_CANCEL_B2C_TIME_ALLOWED = 1000 * 60 * 1; // b2c勾兑撤单时间为1分钟
    public static long RECONCILE_CANCEL_DEFAULT_TIME_ALLOWED = 1000 * 60 * 4; // 默认勾兑撤单时间为4分钟

    @Autowired
    private TsnGenerator tsnGenerator;
    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private ProviderManager providerManager;
    @Autowired
    private TradeCacheService tradeCacheService;
    @Autowired
    private GatewaySupportService gatewaySupportService;
    @Autowired
    private UpayOrderService upayOrderService;
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private DistributedLock distributedLock;

    /**
     * @param sn 订单序列号
     * @param clientSn 商户订单序列号
     * @param basicParams 基本交易参数
     * @return
     */
    @Transactional(isolation=Isolation.REPEATABLE_READ)
    public TransactionContext abortOrReverse(String sn, String clientSn, String cancelType, Map<String, Object> basicParams, Map<String, Object> extended, String operator) {

        String merchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(basicParams, TransactionParam.STORE_ID);
        String terminalId = MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID);
        boolean transactionUseOrderTerminalInfo = false;
        boolean isDBTrade = true;
        if(StringUtil.empty(terminalId)){
            transactionUseOrderTerminalInfo = true;
        }
        Map<String, Object> order = repository.getOrder(merchantId, storeId, sn, clientSn);
        //对接了拉卡拉或者兴业银行第三方支付通道，sn传的值有可能是拉卡拉或者兴业的订单号
        if( order == null && !StringUtils.isEmpty(sn)) {
            Criteria criteria = Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.TRADE_NO).is(sn);
            long  size  = repository.getOrderDao().filter(criteria).count();
            if(size > 1){
                throw new OrderNotExistsException(UpayErrorScenesConstant.TRADE_NO_REPEAT, UpayErrorScenesConstant.TRADE_NO_REPEAT_MESSAGE);
            }
            order = repository.getOrderDao().filter(criteria).fetchOne();
        }

        if (order == null) {
            order = upayOrderService.getOrderBySn(merchantId, sn, clientSn);
            if(null == order) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            isDBTrade = false;
        }
        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), BeanUtil.getPropInt(order, Order.SUB_PAYWAY));
        String orderTeminalId = BeanUtil.getPropString(order, Order.TERMINAL_ID);
        String orderStoreId = BeanUtil.getPropString(order, Order.STORE_ID);
        String orderId = BeanUtil.getPropString(order, DaoConstants.ID);
        String orderSn = BeanUtil.getPropString(order, Order.SN);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);

        // 撤单类型为勾兑时，只允许处理订单状态处于初始状态，且已经过了订单的有效时间，勾兑后订单状态为DEPOSIT_FREEZE_CANCELED:4300
        if(Transaction.CANCEL_TYPE_RECONCILE.equals(cancelType) && (orderStatus == Order.STATUS_DEPOSIT_CREATED || orderStatus == Order.STATUS_DEPOSIT_FREEZE_ERROR)){
            TransactionContext context = workflowManager.lookupTransactionContext(orderId);
            long ctime = BeanUtil.getPropLong(order, DaoConstants.CTIME);
            int subPayway = BeanUtil.getPropInt(order, Order.SUB_PAYWAY);
            if((subPayway == Order.SUB_PAYWAY_BARCODE && (ctime + RECONCILE_CANCEL_B2C_TIME_ALLOWED) > System.currentTimeMillis())
                    || (subPayway != Order.SUB_PAYWAY_BARCODE && (ctime + RECONCILE_CANCEL_DEFAULT_TIME_ALLOWED) > System.currentTimeMillis())){
                throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG, UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG_MESSAGE);
            }
            if(context == null){
                Map transaction = repository.getFreezeTransactionByOrderSn(merchantId, orderSn);
                context = workflowManager.spawnTransactionContext(null, order, transaction);
            }
            
            Map extoutFields = (Map) context.getTransaction().get(Transaction.EXTRA_OUT_FIELDS);
            if(null != extoutFields && BeanUtil.getPropBoolean(extoutFields, Transaction.QUERY_EXPIRE)){
                BeanUtil.setNestedProperty(context.getTransaction(), UpayUtil.getBizErrorCodeKey(MpayServiceProvider.OP_PAY),
                        SceneConfigFacade.getWosaiErrorDefinition(UpayErrorScenesConstant.QUERY_EXPRIE_ERROR, null));
            }
            
            // 移除缓存中的交易数据
            if(BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_DEPOSIT_FREEZE_ERROR) {
                String terminalSnOrStoreSn = BeanUtil.getPropString(context.getTransaction(), (null != BeanUtil.getPropString(order, Order.TERMINAL_ID)) ? Transaction.KEY_TERMINAL_SN: Transaction.KEY_STORE_SN);
                tradeCacheService.removeTradeCache(terminalSnOrStoreSn, MapUtil.getString(order, Order.CLIENT_SN), MapUtil.getString(order, Order.SN));
            }
            
            context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_ERROR_RECOVERY));
            workflowManager.raise(context, Workflow.RC_CANCEL_SUCCESS);
            return context;
        }
        
        if (orderStatus == Order.STATUS_DEPOSIT_CREATED) {
            TransactionContext context = workflowManager.lookupTransactionContext(orderId);
            if (context == null) {
                long ctime = BeanUtil.getPropLong(order, DaoConstants.CTIME);
                if(System.currentTimeMillis() - ctime < CANCEL_MIN_TIME_ALLOWED){
                    throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG, UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG_MESSAGE);
                }else{
                    return doCancelOrderWorkflow(merchantId, transactionUseOrderTerminalInfo ? orderStoreId : storeId, transactionUseOrderTerminalInfo ? orderTeminalId : terminalId, orderId, orderSn, cancelType, order, false, isDBTrade, extended, operator);
                }
            }else {
                workflowManager.raise(context, Workflow.RC_ABORT);
                return context;
            }
        } else if (orderStatus == Order.STATUS_DEPOSIT_FREEZED || orderStatus == Order.STATUS_DEPOSIT_CANCEL_ERROR) {
            return doCancelOrderWorkflow(merchantId, transactionUseOrderTerminalInfo ? orderStoreId : storeId, transactionUseOrderTerminalInfo ? orderTeminalId : terminalId, orderId, orderSn, cancelType, order, true, isDBTrade, extended, operator);

        } else if (orderStatus == Order.STATUS_DEPOSIT_FREEZE_ERROR) {
            return doCancelOrderWorkflow(merchantId, transactionUseOrderTerminalInfo ? orderStoreId : storeId, transactionUseOrderTerminalInfo ? orderTeminalId : terminalId, orderId, orderSn, cancelType, order, false, isDBTrade, extended, operator);

        } else if (orderStatus == Order.STATUS_DEPOSIT_FREEZE_CANCELED || orderStatus == Order.STATUS_DEPOSIT_CANCELED) {
            throw new UpayCancelOrderNoop(UpayErrorScenesConstant.DEPOSIT_CANCELED, UpayErrorScenesConstant.DEPOSIT_CANCELED_MESSAGE);
        }else{
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
        }
    }
    
    /**
     * @param merchantId 商户ID
     * @param storeId 门店ID
     * @param terminalId 终端ID
     * @param orderId 订单ID
     * @param orderSn 订单序列号
     * @param order 订单对象
     * @param fundChange 本次撤单是否要有账面金额变动
     * @return
     */
    private TransactionContext doCancelOrderWorkflow(String merchantId,
                                                     String storeId,
                                                     String terminalId,
                                                     String orderId,
                                                     String orderSn,
                                                     String cancelType,
                                                     Map<String, Object> order,
                                                     boolean fundChange,
                                                     boolean isDbTrade,
                                                     Map<String, Object> extended,
                                                     String operator
                                                     ) {
        Map<String, Object> freezeTransaction;
        if(isDbTrade) {
            freezeTransaction = repository.getFreezeTransactionByOrderSn(merchantId, orderSn);
        }else {
            freezeTransaction = gatewaySupportService.getTransactionByClientTsn(merchantId, BeanUtil.getPropString(order, Order.SN), BeanUtil.getPropString(order, Order.CLIENT_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        if(null == freezeTransaction) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }

        // 撤单次数超过10次后，不再允许撤单
        long count = repository.getDepositCancelTransactionCountByOrderSn(merchantId, orderSn);
        if(count >= 10){
        	throw new UpayCancelOrderNoop(UpayErrorScenesConstant.CANCEL_REPEAT, UpayErrorScenesConstant.CANCEL_REPEAT_MESSAGE);
        }
        
        String tsn = tsnGenerator.nextSn();
        String transactionId = UpayUtil.getTransactionIdBySn(tsn);

        // 移除缓存中的交易数据
        if(BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_DEPOSIT_FREEZE_ERROR) {
            String terminalSnOrStoreSn = BeanUtil.getPropString(freezeTransaction, (null != BeanUtil.getPropString(order, Order.TERMINAL_ID)) ? Transaction.KEY_TERMINAL_SN: Transaction.KEY_STORE_SN);
            tradeCacheService.removeTradeCache(terminalSnOrStoreSn, MapUtil.getString(order, Order.CLIENT_SN), MapUtil.getString(order, Order.SN));

        }
        // 银行卡预授权验证
        ImmutablePair<Boolean, TransactionContext> bankCardConfrim = bankcardConfrim(merchantId, orderSn, order, freezeTransaction, isDbTrade);
        if (bankCardConfrim.getLeft()) {
            return bankCardConfrim.getValue();
        }
        Map extraOutFields = (Map)freezeTransaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(null == extraOutFields) {
            extraOutFields = new HashMap();
        }
        extraOutFields.put(Transaction.ORDER_INFO, CollectionUtil.hashMap(Order.ORIGINAL_TOTAL, order.get(Order.ORIGINAL_TOTAL),
                                                                        Order.EFFECTIVE_TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                                                                        DaoConstants.CTIME, order.get(DaoConstants.CTIME),
                                                                        Order.TRADE_NO, order.get(Order.TRADE_NO)
        ));

        Map<String,Object> freezeExtraParams = MapUtil.getMap(freezeTransaction, Transaction.EXTRA_PARAMS);
        Map<String,Object> extraParams = BeanUtil.getPart(freezeExtraParams, Arrays.asList(Transaction.SQB_PRODUCT_CODE));
        // 拷贝参数到流水
        UpayUtil.copyIfExists(freezeExtraParams, extraParams, Transaction.COPY_EXTRA_PARAMS_KEYS);
        if (!StringUtil.empty(cancelType)){
            extraParams.put(Transaction.CANCEL_TYPE, cancelType);
        }


        
        @SuppressWarnings("unchecked")
        Map<String, Object> cancelTransaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                Transaction.TSN, tsn,
                Transaction.CLIENT_TSN, order.get(Order.CLIENT_SN) + "-C",
                Transaction.TYPE, Transaction.TYPE_DEPOSIT_CANCEL,
                Transaction.ITEMS, null,
                Transaction.SUBJECT, order.get(Order.SUBJECT),
                Transaction.BODY, order.get(Order.BODY),
                Transaction.STATUS, Transaction.STATUS_CREATED,
                Transaction.ORIGINAL_AMOUNT, MapUtil.getLong(order, Order.ORIGINAL_TOTAL),
                Transaction.EFFECTIVE_AMOUNT, MapUtil.getLong(order,Order.EFFECTIVE_TOTAL),
                Transaction.MERCHANT_ID, merchantId,
                Transaction.STORE_ID, storeId,
                Transaction.TERMINAL_ID, terminalId,
                Transaction.PROVIDER, order.get(Order.PROVIDER),
                Transaction.PAYWAY, order.get(Order.PAYWAY),
                Transaction.SUB_PAYWAY, order.get(Order.SUB_PAYWAY),
                Transaction.ORDER_ID, orderId,
                Transaction.ORDER_SN, orderSn,
                Transaction.PRODUCT_FLAG, freezeTransaction.get(Transaction.PRODUCT_FLAG),
                Transaction.CONFIG_SNAPSHOT, freezeTransaction.get(Transaction.CONFIG_SNAPSHOT),
                Transaction.EXTRA_OUT_FIELDS, extraOutFields,
                Transaction.BUYER_UID, freezeTransaction.get(Transaction.BUYER_UID),
                Transaction.BUYER_LOGIN, freezeTransaction.get(Transaction.BUYER_LOGIN),
                Transaction.EXTRA_PARAMS, extraParams,
                Transaction.REFLECT, freezeTransaction.get(Transaction.REFLECT),
                Transaction.OPERATOR, operator,
                Transaction.EXTENDED_PARAMS, extended);


        MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(cancelTransaction);

        UpayUtil.configAlipayV2AuthInfo(serviceProvider, facade, cancelTransaction
                , storeId, MapUtil.getLongValue(order, DaoConstants.CTIME));

        if (!fundChange) {
            cancelTransaction.put(Transaction.ORIGINAL_AMOUNT, 0L);
            cancelTransaction.put(Transaction.EFFECTIVE_AMOUNT, 0L);
            Map<String,Object> tradeParams = serviceProvider.getTradeParams(cancelTransaction);
            if(tradeParams.containsKey(TransactionParam.FEE)){
                tradeParams.put(TransactionParam.FEE, 0);
            }
            if(PaymentUtil.isNewTradeCoprocessorOrder(order)){
                Map<String,Object> items = (Map<String, Object>) cancelTransaction.get(Transaction.ITEMS);
                if(items == null){
                    items = new HashMap<>();
                    cancelTransaction.put(Transaction.ITEMS, items);
                }
                items.put(Transaction.PAYMENTS, new ArrayList<>());
            }
        }
        PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(freezeTransaction, cancelTransaction);
        facade.resetSignParams(serviceProvider, cancelTransaction);
        repository.getTransactionDao().save(cancelTransaction);
        @SuppressWarnings("unchecked")
        Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                Order.MERCHANT_ID, merchantId,
                Order.STATUS, Order.STATUS_DEPOSIT_CANCEL_INPROGRESS);
        if(isDbTrade) {
            repository.getOrderDao().updatePart(orderUpdate);
        }else {
            extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CANCEL, Boolean.TRUE);
            order.putAll(orderUpdate);
            order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
            order.put(DaoConstants.MTIME, System.currentTimeMillis());
            try {
                upayOrderService.updateOrder(order);
            }catch (Exception e) {
                logger.error("error in update order hbase data, data = {}, ex = {}", order, e);
            }
        }
        // 勾兑撤单时，如果订单状态仍处于创建或者处理中状态时，说明这部分交易是因为网关系统突然宕机导致交易失败，需要将原先的支付流水的状态改为失败
        if(Transaction.CANCEL_TYPE_RECONCILE.equals(cancelType) 
                && (BeanUtil.getPropInt(freezeTransaction, Transaction.STATUS) == Transaction.STATUS_CREATED 
                        || BeanUtil.getPropInt(freezeTransaction, Transaction.STATUS) == Transaction.STATUS_IN_PROG)){
            Map<String, Object> payOrderUpdate = CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(freezeTransaction, DaoConstants.ID),
                    Transaction.MERCHANT_ID, BeanUtil.getPropString(freezeTransaction, Transaction.MERCHANT_ID),
                    Transaction.STATUS, Transaction.STATUS_ERROR_RECOVERY);
            repository.getTransactionDao().updatePart(payOrderUpdate);
        }

        TransactionContext context = workflowManager.startWorkflow(null, null, order, cancelTransaction);
        return context;
    
    }

    private ImmutablePair<Boolean, TransactionContext> bankcardConfrim(String merchantId, String orderSn, Map<String, Object> order, Map<String, Object> freezeTransaction , boolean isDbTrade){
        if (MapUtil.getIntValue(order, Transaction.PAYWAY) == Order.PAYWAY_BANKCARD) {
            // 银行卡预授权是由设备发起，需要确认订单状态后才能进行取消
            if (MapUtil.getIntValue(freezeTransaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
                throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
            }
            if (MapUtil.getIntValue(order, Order.STATUS) == Order.STATUS_DEPOSIT_CANCEL_ERROR) {
                Map<String, Object> lastTransaction = repository.getLatestTransactionByOrderSn(merchantId, orderSn);
                if (lastTransaction == null) {
                    return ImmutablePair.of(false, null);
                }
                // 订单号已被其它服务修改，当前任务不执行
                if(distributedLock.isNotCanOrderFix(orderSn)) {
                    throw new UpayRefundOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
                }
                MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(lastTransaction);
                TransactionContext context = workflowManager.spawnTransactionContext(null, order, lastTransaction, serviceProvider);
                String rc = serviceProvider.depositQuery(context);
                Map<String, Object> orderUpdate = MapUtil.hashMap(DaoConstants.ID, order.get(DaoConstants.ID),
                        Transaction.MERCHANT_ID, order.get(Transaction.MERCHANT_ID),
                        DaoConstants.VERSION, order.get(DaoConstants.VERSION)
                    );
                if (!isDbTrade) {
                    Map extraOutFields = (Map)lastTransaction.get(Transaction.EXTRA_OUT_FIELDS);
                    if (extraOutFields == null) {
                        extraOutFields = new HashMap<>();
                        lastTransaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                    }
                    extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CANCEL, Boolean.TRUE);
                }
                Consumer<Map<String, Object>> orderUpdateCs = (update) -> {
                    try {
                        if (isDbTrade) {
                            repository.doInTransaction(() -> {
                                repository.getOrderDao().updatePart(orderUpdate);
                            });
                            order.putAll(orderUpdate);
                            order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                            order.put(DaoConstants.MTIME, System.currentTimeMillis());

                        } else {
                            order.putAll(orderUpdate);
                            order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                            order.put(DaoConstants.MTIME, System.currentTimeMillis());
                            upayOrderService.updateOrder(order);
                        }
                    } catch (DaoVersionMismatchException e) {
                        throw new UpayDepositOrderError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
                    }
                };
                if (Workflow.RC_CANCEL_SUCCESS.equals(rc)) {
                    // 订单已处理成功，按照成功处理
                    context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
                    workflowManager.raise(context, Workflow.RC_CANCEL_SUCCESS);
                    throw new UpayCancelOrderNoop(UpayErrorScenesConstant.CANCEL_REPEAT, UpayErrorScenesConstant.CANCEL_REPEAT_MESSAGE);

                } else if (Workflow.RC_RETRY.equals(rc)) {
                    // 订单仍处于处理中，修改当前订单状态，设备上重新发起下单
                    orderUpdate.put(Order.STATUS, Order.STATUS_DEPOSIT_CANCEL_INPROGRESS);
                    orderUpdateCs.accept(orderUpdate);
                    lastTransaction.put(Transaction.STATUS, Transaction.STATUS_CREATED);
                    context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
                    workflowManager.raise(context, Workflow.RC_RETRY);
                    return ImmutablePair.of(true, context);

                } else if (Workflow.RC_TRADE_DISCARD.equals(rc)) {
                    // 订单已明确失败，将之前订单变更为失败，重新走撤销流程
                    orderUpdate.put(Order.STATUS, Order.STATUS_DEPOSIT_FREEZED);
                    orderUpdateCs.accept(orderUpdate);
                    return ImmutablePair.of(false, null);
                } else {
                    throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
                }
            }
        }
        return ImmutablePair.of(false, null);
    }
}