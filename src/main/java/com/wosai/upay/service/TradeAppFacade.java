package com.wosai.upay.service;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.bean.model.TradeAppConfig;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.InvalidParamException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.EmojiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wujianwei on 2023/12/18.
 */
@Component
public class TradeAppFacade {
    private static final Logger logger = LoggerFactory.getLogger(TradeAppFacade.class);

    public static final String TRADE_APP_BASIC = "1";
    public static final String TRADE_APP_SELF_CATERING = "7"; //收钱吧自营外卖 线上测试环境值是一致的

    public static final String SPLIT_CHAR = ",";

    public static final String SPLIT_BIZ_MODEL = ":biz_model:";
    public static final String SPLIT_TERMINAL_CATEGORY = ":terminal_category:";
    public static final String SPLIT_SUB_PAYWAY = ":sub_payway:";




    @Autowired
    private TradeConfigService tradeConfigService;

    private Map<String, TradeAppConfig> tradeAppConfigMap = new HashMap<>();

    @PostConstruct
    private void init(){
        load();
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            try{
                load();
            }catch (Exception e){
                logger.error("refresh trade app config cache error " + e.getMessage(), e);
            }
        }, 10,10, TimeUnit.MINUTES);
    }

    private void load(){
        List<TradeAppConfig> all = tradeConfigService.getAllTradeAppConfig();
        tradeAppConfigMap = all.stream().collect(Collectors.toMap(r -> r.getTradeApp() + "", Function.identity()));
    }

    /**
     * 校验传值
     * @param tradeApp
     * @param extraParams
     */
    public void checkTradeApp(String tradeApp, Map<String,Object> extraParams){
        tradeApp = tradeApp == null ? TRADE_APP_BASIC : tradeApp;
        if(!ApolloConfigurationCenterUtil.isCheckTradeApp(tradeApp)){
            return;
        }
        TradeAppConfig config = tradeAppConfigMap.get(tradeApp);
        if(config == null){
            throw new InvalidParamException("不合法的支付应用");
        }
        String payPath = MapUtil.getString(extraParams, Transaction.SQB_PAY_PATH);
        if(payPath != null){
            Set<String> payPaths = config.getPayPaths();
            for (String s : payPath.split(SPLIT_CHAR)) {
                if(notContains(payPaths, s)){
                    throw new InvalidParamException("不合法的支付路径值");
                }
            }
        }
        String bizModel = MapUtil.getString(extraParams, Transaction.SQB_BIZ_MODEL);
        if(bizModel != null){
            Set<String> bizModels = config.getBizModels();
            for (String s : bizModel.split(SPLIT_CHAR)) {
                if(notContains(bizModels, s)){
                    throw new InvalidParamException("不合法的业务模式值");
                }
            }
        }
    }

    /**
     * 根据业务模式，获取trade app的映射值
     * @param tradeApp
     * @param bizModel
     * @return
     */
    public String getConvertedTradeAppByBizModel(String tradeApp, String bizModel){
        if(bizModel == null){
            return null;
        }
        TradeAppConfig config = tradeAppConfigMap.get(tradeApp);
        if(config == null || config.getBizModelTradeApp() == null){
            return null;
        }
        for (String s : bizModel.split(SPLIT_CHAR)) {
            String newTradeApp = MapUtil.getString(config.getBizModelTradeApp(), s);
            if(StringUtil.isNotEmpty(newTradeApp)){
                return newTradeApp;
            }
        }
        return null;
    }

    /**
     * 根据 tradeApp 配置的商品名称取值规则，获取新的商品名称
     * @param tradeApp
     * @param bizModel
     * @param subject
     * @param storeName
     * @return
     */
    public String getNewSubject(String tradeApp, String bizModel, String subject, String storeName){
        tradeApp = tradeApp == null ? TRADE_APP_BASIC : tradeApp;
        TradeAppConfig config = tradeAppConfigMap.get(tradeApp);
        String newSubject = subject;
        if(config == null){
            return EmojiUtil.filterEmoji(newSubject);
        }
        Integer subjectRuleType = config.getSubjectRuleType();
        if(Objects.equals(subjectRuleType, TradeAppConfig.SUBJECT_RULE_CUSTOM)){
            // 自定义不改写商品名称
            newSubject =  subject;
        }else if(Objects.equals(subjectRuleType, TradeAppConfig.SUBJECT_RULE_STORE_NAME)){
            // 配置为门店名称，则返回门店名称当做商品名称
            // 容错判断门店名称是否为空
            if(!StringUtil.isEmpty(storeName)){
                newSubject = storeName;
            }
        }else if(Objects.equals(subjectRuleType, TradeAppConfig.SUBJECT_RULE_DEFAULT)){
            // 根据规则配置获取商品名称
            String subjectByRuleDefault = getSubjectByRuleTypeDefault(tradeApp, bizModel, config);
            if(subjectByRuleDefault != null){
                newSubject = subjectByRuleDefault;
            }
        }
        return EmojiUtil.filterEmoji(newSubject);
    }

    /**
     * 根据默认规则获取商品名称
     * @param tradeApp 不为空
     * @param bizModel
     * @param config
     * @return
     */
    private String getSubjectByRuleTypeDefault(String tradeApp, String bizModel, TradeAppConfig config){
        Map<String, String> subjectRule = config.getSubjectRule();
        if(subjectRule == null){
            return null;
        }
        if(bizModel != null && !bizModel.contains(SPLIT_CHAR)){
            String bizModelSubject = subjectRule.get(bizModel);
            if(bizModelSubject != null){
                return bizModelSubject;
            }
        }
        return subjectRule.get(tradeApp);
    }


    /**
     * 按照trade_app方式生成product_flag, 以便下游使用
     * @param transaction
     * @return
     */
    public List<String> genProductFlags(Map<String, Object> transaction){
        String tradeApp = MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.TRADE_APP);
        String bizModel = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.SQB_BIZ_MODEL);
        String terminalCategory = MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_CATEGORY);
        Map<String, String> config = ApolloConfigurationCenterUtil.getProductFlagsConfig();
        List<String> flags = new ArrayList<>();
        //根据trade_app映射 values 指可以允许配置多个flag
        String flag = config.get(tradeApp);
        if(flag != null){
            flags.addAll(Arrays.asList(flag.split(SPLIT_CHAR)));
        }
        // 根据trade_app、sqb_biz_model 映射
        if(bizModel != null){
            for (String model : bizModel.split(SPLIT_CHAR)) {
                String key = tradeApp + SPLIT_BIZ_MODEL + model;
                flag = config.get(key);
                if(flag != null){
                    flags.addAll(Arrays.asList(flag.split(SPLIT_CHAR)));
                }
            }
        }
        // 根据trade_app、terminal_category 映射
        if(terminalCategory != null){
            flag = config.get(tradeApp + SPLIT_TERMINAL_CATEGORY + terminalCategory);
            if(flag != null){
                flags.addAll(Arrays.asList(flag.split(SPLIT_CHAR)));
            }
        }

        int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);

        // 根据trade_app、 sub_payway 映射
        flag = config.get(tradeApp + SPLIT_SUB_PAYWAY + subPayway);
        if(flag != null){
            flags.add(flag);
        }

        // 设置小程序
        if(subPayway == Order.SUB_PAYWAY_MINI){
            flags.add(ProductFlag.MINI.getCode());
        }

        //  设置储值核销
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(payway == Order.PAYWAY_PREPAID_CARD){
            flags.add(ProductFlag.PREPAID_CARD.getCode());
        }

        // 设置平台外卖
        String sqbScene = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.SQB_SCENE);
        if(TRADE_APP_SELF_CATERING.equals(tradeApp)){
            if(sqbScene != null && !sqbScene.contains(ProductFlag.MERCHANT_MINI.getCode())){
                flags.add(ProductFlag.PLATFORM_DELIVERY.getCode());
            }
        }

        // 设置直接收款 , 基础支付业务且非储值核销、非礼品卡 交易
        if (Order.PAYWAY_PREPAID_CARD != payway && Order.PAYWAY_GIFT_CARD != payway) {
            List<String> basicPaymentTradeApps = ApolloConfigurationCenterUtil.getBasicPaymentTradeApps();
            if (basicPaymentTradeApps.contains(tradeApp)) {
                flags.add(ProductFlag.DIRECT_PAY.getCode());
            }
        }

        return flags;
    }


    private boolean notContains(@Nullable Set<String> collection, String element){
        if(collection == null){
            return true;
        }
        return !collection.contains(element);
    }

}
