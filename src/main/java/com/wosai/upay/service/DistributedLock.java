package com.wosai.upay.service;

import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR> Date: 2022/6/9 Time: 1:40 下午
 */
@Component
public class DistributedLock {
    private static final Logger LOGGER = LoggerFactory.getLogger(DistributedLock.class);
    private static final String NAMESPACE = "upay-gateway:";
    private static final String ORDER_FIX_PREFIX = NAMESPACE + "fix:";
    private static final Pair<Long, TimeUnit> ORDER_FIX_EXPIRE_PAIR = Pair.of(30L, TimeUnit.SECONDS);

    @Autowired
    private StringRedisTemplate redisTemplate;

    public boolean isCanOrderFix(String tsn) {
        try {
            String key = ORDER_FIX_PREFIX + tsn;
            String value = UUID.randomUUID().toString();
            redisTemplate.execute(new RedisCallback<Boolean>() {
                @Override
                public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                    connection.set(key.getBytes(), value.getBytes(), Expiration.from(ORDER_FIX_EXPIRE_PAIR.getLeft()
                            , ORDER_FIX_EXPIRE_PAIR.getRight()), RedisStringCommands.SetOption.SET_IF_ABSENT);
                    return null;
                }
            });

            String result = redisTemplate.opsForValue().get(key);
            return Objects.equals(result, value);
        } catch (Throwable t) {
            LOGGER.error("判断是否可以勾兑异常, 异常栈: ", t);
        }
        return true;
    }

    public boolean isNotCanOrderFix(String tsn) {
        return !isCanOrderFix(tsn);
    }

}
