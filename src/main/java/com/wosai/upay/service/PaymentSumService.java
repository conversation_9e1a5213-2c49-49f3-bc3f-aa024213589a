package com.wosai.upay.service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.SafeSimpleDateFormat;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.stereotype.Service;

@Service
public class PaymentSumService {

    public static final String DAILY_LIMIT_PREFIX = "daily_limit";

    public static final String DAILY_LIMIT_PAYWAY_PREFIX = "daily_limit_payway_";

    public static final String MONTHLY_LIMIT_PREFIX = "monthly_limit";

    public static final String MONTHLY_LIMIT_PAYWAY_PREFIX = "monthly_limit_payway_";

    public static final String DAILY_LIMIT_BANKCARD = "daily_limit_bankcard_";

    public static final String MONTH_LIMIT_BANKCARD = "monthly_limit_bankcard_";

    public static final String DAILY_LIMIT_PHONE_POS_PREFIX = "daily_limit_phone_pos_";

    public static final int TYPE_DAY = 1;
    public static final int TYPE_MONTH = 2;

    private SafeSimpleDateFormat format;

    @Autowired
    private StringRedisTemplate redisTemplate;
    
    public PaymentSumService() {
        this.format = new SafeSimpleDateFormat("yyyy-MM-dd");
        this.format.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
    }

    private String getDateStr(long time) {
        return format.format(new Date(time));
    }

    private String getMonthStart() {
        return this.format.format(DateTimeUtil.getMonthStart(new Date()));
    }

    private String getKey(String bizId, long time) {
        return StringUtils.join(bizId, "|", getDateStr(time));
    }


    private String getKeyByType(String bizId, long time, int type) {
        String suffix = "";
        if(type == TYPE_DAY) {
            suffix = getDateStr(time);
        } else {
            suffix = getMonthStart();
        }
    	return StringUtils.join(bizId, "|", suffix);
    }

    public void increment(List<Increment> increments){
        if(increments == null || increments.isEmpty()){
            return;
        }
        List<Object> results = redisTemplate.executePipelined((RedisCallback<Long>) connection -> {
            for (int i = 0; i < increments.size(); i++) {
                Increment increment = increments.get(i);
                String key = getKeyByType(increment.getBizId(), increment.getTime(), increment.getType());
                connection.incrBy(key.getBytes(), increment.getDelta());
            }
            return null;
        });
        //首次插入，需要设置过期时间，否则不设置。
        List<Pair<Integer,String>> redisKeyAndTypes = new ArrayList<>();
        for (int i = 0; i < increments.size(); i++) {
            Increment increment = increments.get(i);
            long newValue = (long) results.get(i);
            if(newValue == increment.getDelta()){
                String key = getKeyByType(increment.getBizId(), increment.getTime(), increment.getType());
                redisKeyAndTypes.add(Pair.of(increment.getType(), key));
            }
        }
        if (!redisKeyAndTypes.isEmpty()) {
            redisTemplate.executePipelined((RedisCallback<?>) connection -> {
                for (Pair<Integer, String> pair: redisKeyAndTypes) {
                    String key = pair.getRight();
                    if (pair.getLeft().equals(TYPE_DAY)) {
                        connection.expire(key.getBytes(), TimeoutUtils.toSeconds(1, TimeUnit.DAYS));
                    } else {
                        connection.expire(key.getBytes(), TimeoutUtils.toSeconds(31, TimeUnit.DAYS));
                    }
                }
                return null;
            });
        }


    }

    public long get(String bizId, long time) {
       return get(bizId, time, TYPE_DAY);
    }


    public long get(String bizId, long time, int type) {
        String key = getKeyByType(bizId, time, type);
        String value = redisTemplate.opsForValue().get(key);
        if (value == null) {
            return 0;
        } else {
            return Long.parseLong(value);
        }
    }

    public static class Increment{
        private String bizId;
        private long time;
        private long delta;
        private Integer type;

        public Increment(String bizId, long time, long delta) {
            this.bizId = bizId;
            this.time = time;
            this.delta = delta;
            this.type = TYPE_DAY;
        }

        public Increment(String bizId, long time, long delta,int type) {
            this.bizId = bizId;
            this.time = time;
            this.delta = delta;
            this.type = type; //类型1 日 2 月
        }

        public String getBizId() {
            return bizId;
        }

        public long getTime() {
            return time;
        }

        public long getDelta() {
            return delta;
        }

        public int getType(){
            return type;
        }
    }
}
