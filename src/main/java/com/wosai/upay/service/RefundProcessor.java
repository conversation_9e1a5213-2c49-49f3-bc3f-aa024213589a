package com.wosai.upay.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.NonSqbOrder;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.ExternalServiceFacade.TcpRefundResult;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.*;
import com.wosai.upay.wallet.constant.ProviderWalletAccountTypeEnum;
import com.wosai.upay.workflow.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Service
public class RefundProcessor {
    private static final Logger logger = LoggerFactory.getLogger(RefundProcessor.class);
    @Autowired
    private TsnGenerator tsnGenerator;
    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private ProviderManager providerManager;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private UpayOrderService upayOrderService;
    @Autowired
    private TradeCacheService tradeCacheService;
    @Autowired
    private GatewaySupportService gatewaySupportService;
    @Autowired
    private SimpleRedisLock simpleRedisLock;
    @Autowired
    private DistributedLock distributedLock;
    /**
     *
     * @param operator
     * @param sn
     * @param clientSn
     * @param requestNo
     * @param refundAmount
     * @param extended
     * @param reflect
     * @param basicParams
     * @return
     */
    @Transactional(isolation=Isolation.REPEATABLE_READ)
    public TransactionContext refund(Map request, String operator, String sn, String clientSn, String requestNo, String refundAmount, Map<String,Object> extended, List<Map<String,Object>> goodsDetails, Object reflect, Map<String, Object> basicParams, Map<String, Object> refundProfitSharing) {
        String terminalSn = (String) request.get(UpayService.TERMINAL_SN);
        String wosaiStoreId = (String) request.get(UpayService.WOSAI_STORE_ID);
        String terminalOrStoreSn = (terminalSn != null? terminalSn: wosaiStoreId);
        String apiVer = (String) request.get(UpayService.API_VER);
        String merchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(basicParams, TransactionParam.STORE_ID);
        String terminalId = MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID);
        String tradeDate = MapUtil.getString(request, UpayService.TRADE_DATE);
        boolean transactionUseOrderTerminalInfo = false;
        boolean isDBTrade = true;
        boolean refundByDate = !StringUtil.empty(tradeDate);
        if(StringUtil.empty(terminalId)){
            transactionUseOrderTerminalInfo = true;
        }
        if(refundProfitSharing != null){
            checkRefundProfitSharing(Long.parseLong(refundAmount), refundProfitSharing);
        }
        Map<String, Object> order = null;
        if (!refundByDate) {
            order = repository.getOrder(merchantId, storeId, sn, clientSn);
            if(null == order) {
                order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.RECENT_6M);
                isDBTrade = false;
            }
        }
        Map<String, Object> extraParams = new HashMap<>();
        setSqbInnerParam(extended, extraParams);
        Map<String, Object> payTransaction = null;
        int historyTradeRefundConfig = BeanUtil.getPropInt(basicParams, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_CLOSE);
        boolean refundNonSqbOrder = BeanUtil.getPropBoolean(basicParams, TransactionParam.REFUND_NON_SQB_ORDER);
        boolean isRefundNonSqbOrder = false;
        boolean isCrossMerchantRefund = false;
        Map<String,Object> nonSqbOrder = null;
        // 1、校验商户能否进行跨主体退款
        if (order == null && !StringUtil.empty(sn) && !refundByDate){
            /*
             * 跨商户退款。这个逻辑本不应该放在这里:
             *     1.业务上有安全风险
             *     2.技术上，网关会额外依赖 user-service服务去判断集团商户是否有权限退款
             * 然鹅，反抗无效。。。
             */
            order = repository.getOrderByOrderSn(null, sn);
            if(null == order){
                order = gatewaySupportService.getOrderBySn(null, sn, null, DataPartitionConst.RECENT_6M);
                isDBTrade = false;
            }else{
                isDBTrade = true;
            }

            if (order != null){
                String currMerchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
                String oriMerchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
                if (!currMerchantId.equals(oriMerchantId) && facade.validateRefundCrossMerchantsPermission(currMerchantId,oriMerchantId)){
                    transactionUseOrderTerminalInfo = true;
                    merchantId = oriMerchantId;
                    @SuppressWarnings("unchecked")
                    Map<String, Object> crossMerchantRefundInfo = CollectionUtil.hashMap(Transaction.MERCHANT_ID, currMerchantId,
                            Transaction.STORE_ID, storeId,
                            Transaction.OPERATOR, operator,
                            Transaction.TERMINAL_ID, terminalId);

                    extraParams.put(Transaction.CROSS_MERCHANT_REFUND,crossMerchantRefundInfo);
                    isCrossMerchantRefund = true;
                }else{
                    throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR_MESSAGE);
                }
            }
        }

        // 2、校验商户能否进行历史订单退款
        if(null == order && historyTradeRefundConfig == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN) {
            if (!refundByDate) {
                order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.COLD);
            } else {
                order = gatewaySupportService.getOrderBySnAndDate(merchantId, sn, tradeDate);
            }
            isDBTrade = false;
        }
        
        // 3、查询下单和预授权完成流水
        if(null != order) {
            if(isDBTrade){
                payTransaction = repository.getPayTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
                if(null == payTransaction) {
                    payTransaction = repository.getConsumeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
                }
            } else {
                payTransaction = gatewaySupportService.getPayOrConsumerTransaction(merchantId, BeanUtil.getPropString(order, Order.SN), MapUtil.getLong(order, DaoConstants.CTIME));
                if (refundByDate && MapUtil.getIntValue(payTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_FREEZE) {
                    throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, "此笔交易不支持退款！");
                }
            }
        }


        // 4、跨服务商主体退款(非喔噻订单退款)，只允许退直连交易
        if(null == order && refundNonSqbOrder && !refundByDate) {
            List<Map<String, Object>> nonSqbOrders = repository.getNonSqbOrderByClientSn(clientSn);
            if(null == nonSqbOrders || nonSqbOrders.size() > 1) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            nonSqbOrder = nonSqbOrders.get(0);
            String sourceTerminal = MapUtil.getString(nonSqbOrder, NonSqbOrder.TERMINAL_SN);
            if(StringUtil.empty(sourceTerminal)) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }

            String currMerchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
            String oriMerchantId = BeanUtil.getPropString(nonSqbOrder, NonSqbOrder.MERCHANT_ID);

            if(MapUtil.getBooleanValue(nonSqbOrder, NonSqbOrder.IS_SYNC)) {
                // 订单已经退款，需要通过订单号查询新终端下是否存在交易
                order = repository.getOrder(oriMerchantId, null, sn, clientSn);
                if(null != order) {
                    payTransaction = repository.getPayTransactionByOrderSn(oriMerchantId, BeanUtil.getPropString(order, Order.SN));
                }
            }else {
                int payway = MapUtil.getIntValue(request, UpayService.PAYWAY, Order.PAYWAY_ALIPAY);
                int subPayway = MapUtil.getIntValue(request, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_BARCODE);
                Map tradeConfig = facade.getAllParamsWithTradeApp(null, sourceTerminal, payway, subPayway, TransactionParam.TRADE_APP_BASIC_PAY);

                if(!(tradeConfig.containsKey(TransactionParam.ALIPAY_V1_TRADE_PARAMS)
                        || tradeConfig.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS))) {
                    throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
                }
                order = mockOrder(request, tradeConfig, payway, subPayway, nonSqbOrder);
                payTransaction = mockPayTransaction(order, tradeConfig, nonSqbOrder);
            }

            if(!merchantId.equals(oriMerchantId)) {
                transactionUseOrderTerminalInfo = true;
                merchantId = oriMerchantId;
                @SuppressWarnings("unchecked")
                Map<String, Object> crossMerchantRefundInfo = CollectionUtil.hashMap(Transaction.MERCHANT_ID, currMerchantId,
                        Transaction.STORE_ID, storeId,
                        Transaction.OPERATOR, operator,
                        Transaction.TERMINAL_ID, terminalId);
                extraParams.put(Transaction.CROSS_MERCHANT_REFUND,crossMerchantRefundInfo);
                if(null != payTransaction && null == payTransaction.get(Transaction.EXTRA_PARAMS)) {
                    payTransaction.put(Transaction.EXTRA_PARAMS, extraParams);
                }
            }
            isRefundNonSqbOrder = true;
            isDBTrade = true;
        }


        if(null == order){
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }else if(null == payTransaction) {
            // gatewaySupportService.getPayOrConsumerTransaction 只返回了状态为2000的流水，这种应该返回订单状态异常，而不是订单不存在
            throw new UpayRefundOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE, UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE_MESSAGE);
        }
        resetTransactionIfTlToSyb(wosaiStoreId, terminalSn, basicParams, payTransaction);
        //5、判断商户通联交易参数重置之后是否支持退款
        int resetProvider = MapUtil.getIntValue(payTransaction, Transaction.PROVIDER);
        if (Provider.TONG_LIAN.getCode() == resetProvider) {
            //重置之后还是通联不允许退款
            throw new UpayRefundOrderStateError(UpayErrorScenesConstant.TL_PROVIDER_CANNOT_REFUND, UpayErrorScenesConstant.TL_PROVIDER_CANNOT_REFUND_MESSAGE);
        }

        // 院校通通道，不允许退款
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_HOPE_EDU) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.REFUND_FAIL, "院校通不支持退款");
        }

        //6、判断商户是否允许跨门店退款
        acrossStoreRefundProcess(merchantId, storeId, payTransaction);

        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), BeanUtil.getPropInt(order, Order.SUB_PAYWAY));

        String orderId = BeanUtil.getPropString(order, DaoConstants.ID);
        String orderSn = BeanUtil.getPropString(order, Order.SN);
        String orderTerminalId = BeanUtil.getPropString(order, Order.TERMINAL_ID);
        String orderStoreId = BeanUtil.getPropString(order, Order.STORE_ID);
        clientSn = BeanUtil.getPropString(order, Order.CLIENT_SN);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);

        if(orderStatus == Order.STATUS_DEPOSIT_CONSUMED) {
            // 预授权交易通过同步接口处理后不允许退款
            Map<String, Object> extraOutFields = com.wosai.pantheon.util.MapUtil.getMap(payTransaction, Transaction.EXTRA_OUT_FIELDS);
            if(com.wosai.pantheon.util.MapUtil.getBooleanValue(extraOutFields, Transaction.IS_DEPOSIT_SYNC, false)) {
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, "此笔交易不支持退款！");
            }
        }
        if (MapUtil.getIntValue(payTransaction, Transaction.PAYWAY) == Order.PAYWAY_BANKCARD
                && MapUtil.getIntValue(payTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
            // 银行卡预授权验证
            ImmutablePair<Boolean, TransactionContext> bankCardConfrim = bankcardConfrim(merchantId, orderSn, order, isDBTrade);
            if (bankCardConfrim.getLeft()) {
                return bankCardConfrim.getValue();
            }
            orderStatus = MapUtil.getIntValue(order, Order.STATUS);
        }
        if (orderStatus == Order.STATUS_PAID || orderStatus == Order.STATUS_PARTIAL_REFUNDED || orderStatus == Order.STATUS_REFUND_ERROR || orderStatus == Order.STATUS_DEPOSIT_CONSUMED) {
            long originalAmount = Long.parseLong(refundAmount);
            long netOriginal = BeanUtil.getPropLong(order, Order.NET_ORIGINAL);
            long netEffective = BeanUtil.getPropLong(order, Order.NET_EFFECTIVE);
            long originalTotal = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL);

            //索迪斯不支持多次部分退款
            if(BeanUtil.getPropInt(order, Order.PAYWAY, 0) == Order.PAYWAY_SODEXO
                    && BeanUtil.getPropInt(order, Order.STATUS) != Order.STATUS_PAID) {
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, "此笔交易不支持退款！");
            }
            // 富士康富圈圈退款是异步处理，网关暂不支持撤单
            if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_FOXCONN) {
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, "此笔交易不支持退款！");
            }
            // 建行福利卡不支持退款
            if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_CCB_GIFT_CARD) {
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, "此笔交易不支持退款！");
            }
            // 富友通道0-3点时只支持退当日交易
            if(!fuyouTradingTimeAllowed(payTransaction)) {
                throw new UpayBizException(UpayErrorScenesConstant.FUYOU_TRADING_TIME_ALLOWED, UpayErrorScenesConstant.FUYOU_TRADING_TIME_ALLOWED_MESSAGE);
            }
            // 单笔交易的退款笔数限制
            if(repository.getOrderRefundCount(merchantId, orderSn) > ApolloConfigurationCenterUtil.getMaxRefundCount()) {
                throw new UpayBizException(UpayErrorScenesConstant.REFUND_OVER_DAILY_LIMIT_ERROR, UpayErrorScenesConstant.REFUND_OVER_DAILY_LIMIT_ERROR_MESSAGE);
            }
            String tradeApp = MapUtil.getString(MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT, new HashMap()), TransactionParam.TRADE_APP);
            if (MapUtil.getIntValue(payTransaction, Transaction.PROVIDER) == Provider.LAKALA_UNION_PAY_V3.getCode()
                    && MapUtil.getIntValue(payTransaction, Transaction.PAYWAY) == Payway.BANKCARD.getCode()
                    && Objects.equals(tradeApp, ApolloConfigurationCenterUtil.getPhonePosTradeAppId())) {
                throw new UpayBizException(UpayErrorScenesConstant.PHONE_POS_CANNOT_REFUND, UpayErrorScenesConstant.PHONE_POS_CANNOT_REFUND_MESSAGE);
            }

            if(originalAmount > netOriginal){
                throw new RefundableAmountNotEnough(UpayErrorScenesConstant.UPAY_REFUND_ORDER_NOOP, UpayErrorScenesConstant.UPAY_REFUND_ORDER_NOOP_MESSAGE);
            }
            int refundableDays = getMaxRefundableDays(historyTradeRefundConfig, order.get(Order.PROVIDER) == null ? null : BeanUtil.getPropLong(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY));
            if(System.currentTimeMillis() - BeanUtil.getPropLong(order, DaoConstants.CTIME) > UpayConstant.MILLISECOND_OF_DAY * refundableDays){
                throw new UpayRefundOverDateLimitException(UpayErrorScenesConstant.UPAY_REFUND_OVER_DATE_LIMIT, 
                		UpayErrorScenesConstant.UPAY_REFUND_OVER_DATE_LIMIT_MESSAGE.replace("#message#", refundableDays+""),
                		CollectionUtil.hashMap("#message#", refundableDays));
            }
            if(MapUtil.getString(payTransaction, Transaction.PRODUCT_FLAG,"").contains(ProductFlag.FIENESS.getCode())){
                // 先享后付-周期付订单需要全额退款
                if (originalAmount !=netOriginal) {
                    throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, "此笔交易不支持部分退款！");
                }
            }
            long effectiveAmount = originalAmount;
            List<Map<String,Object>> transactionPayments = new ArrayList<>();

            long promotionAmount = 0;
            // 1、解析业务方上送的优惠明细
            List<Map<String, Object>> sqbPromotionDetails = (List<Map<String, Object>>) BeanUtil.getNestedProperty(extraParams, Transaction.SQB_PROMOTION_DETAIL);
            if (CollectionUtils.isNotEmpty(sqbPromotionDetails)) {
                transactionPayments = PaymentUtil.buildTransactionRefundOrCancelPayments(order, sqbPromotionDetails);
            }
            UpayUtil.validateRefundOrCancelPromotionDetail(order, transactionPayments);
            promotionAmount = transactionPayments.stream().mapToLong(map -> MapUtil.getLong(map, Payment.AMOUNT)).sum();
            effectiveAmount = effectiveAmount - promotionAmount; //减去业务方上送的优惠金额

            if (BeanUtil.getPropBoolean(order, Order.TCP_MODIFIED, false)) {
                TcpRefundResult result = facade.consultTcpForRefund(orderSn, effectiveAmount);
                if (result.isDenied()) {
                    String reason = result.getReason();
                    throw new UpayTcpOrderNotRefundable(UpayErrorScenesConstant.UPAY_ORDER_NOT_ALLOWED, UpayErrorScenesConstant.UPAY_ORDER_NOT_ALLOWED_MESSAGE, CollectionUtil.hashMap(
                            "#message#", StringUtil.empty(reason) ? UpayErrorScenesConstant.UPAY_ORDER_NOT_ALLOWED_MESSAGE : reason
                    ));
                }
                effectiveAmount = result.getEffectiveAmount();
                transactionPayments.addAll(PaymentUtil.buildTransactionPaymentsForRefundOrCancel(result));
            }
            if (effectiveAmount > netEffective || effectiveAmount < 0) {
                throw new UpayTcpOrderRefundError(UpayErrorScenesConstant.UPAY_TCP_ORDER_AMOUNT_NOT_MATCH, UpayErrorScenesConstant.UPAY_TCP_ORDER_AMOUNT_NOT_MATCH_MESSAGE);
            }
            Map config = MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT);
            extended = UpayUtil.getMergedExtendedWithGoodsDetails(goodsDetails, extended, BeanUtil.getPropInt(order, Order.PAYWAY), config, Transaction.TYPE_REFUND);
            extraParams = (extraParams == null) ? new HashMap<String, Object>(): extraParams;
            //校验退款标识
            UpayUtil.validateRefundFlag(payTransaction, extended, extraParams);
            //设置liquidationNextDay
            facade.setLiquidationNextDayConfig(payTransaction);
            String tsn = tsnGenerator.nextSn();
            String transactionId = UpayUtil.getTransactionIdBySn(tsn);
            boolean isFormal = UpayUtil.isFormal(payTransaction);
            boolean refundAllOrCancel = originalAmount == BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL);
            String commonSwitch = MapUtil.getString(basicParams, TransactionParam.COMMON_SWITCH);
            //如果是归集类的商户，退款前需要判断当天的归集任务已处理完毕才可进行
            if (UpayUtil.isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_AGGREGATION_CONFIG)) {
                facade.allowBeforeRefundForAggregated(merchantId, MapUtil.getInteger(payTransaction, Transaction.PROVIDER), MapUtil.getLongValue(payTransaction, DaoConstants.CTIME));
            }
            //如果是非正式交易，那么退款需要加锁，防止同一时刻，并发进行恶意退款
            //是否是喔噻清算
            if(!isFormal){
                int currentClearanceProvider = MapUtil.getIntValue(basicParams, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
                // 跨商户主体退款时，需要拿到原始终端的结算通道
                if(transactionUseOrderTerminalInfo && extraParams.containsKey(Transaction.CROSS_MERCHANT_REFUND)) {
                    try {
                        Map<String, Object> sourceBasicParams = facade.getBasicParams(MapUtil.getString((Map)payTransaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.STORE_SN), MapUtil.getString((Map)payTransaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.TERMINAL_SN));
                        currentClearanceProvider = MapUtil.getIntValue(sourceBasicParams, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
                    }catch (Exception e) {
                    }
                }
                //全额退款 从支付流水中取fee
                long walletChangeAmount = FeeUtil.calculateApproximateWalletChangeAmount(payTransaction,transactionPayments, originalAmount , refundAllOrCancel);
                //当余额不足或者有其他错误时，下面的方法会抛异常，交由ServiceMethodInterceptor去处理
                ExternalServiceFacade.CanCancelOrRefundAndFreezeWalletBalanceReq req = new ExternalServiceFacade.CanCancelOrRefundAndFreezeWalletBalanceReq(merchantId, transactionId, walletChangeAmount, payTransaction, currentClearanceProvider, refundProfitSharing, refundAllOrCancel);
                facade.canCancelOrRefundAndFreezeWalletBalance(req);
                WalletDeductionContextHolder.setContextHolder(merchantId, transactionId);
            }
            // 收付通非分账交易，需要校验商户余额
            Map<String,Object> payExtraParams = (Map<String, Object>) payTransaction.get(Transaction.EXTRA_PARAMS);
            String sftBrandId = MapUtil.getString(config, TransactionParam.SFT_BRAND_ID);
            if (!StringUtil.empty(sftBrandId) && (payExtraParams == null || !payExtraParams.containsKey(Transaction.PROFIT_SHARING))) {
                facade.canCancelOrRefundAndFreezeSFTWalletBalance(sftBrandId, merchantId, orderSn, MapUtil.getIntValue(payTransaction, Transaction.PAYWAY), effectiveAmount);
            }
            String clientTsn = String.format("%s-%s", clientSn, requestNo);
            Map<String, Object> existingRefund = repository.getTransactionDao().filter(Criteria.where(Transaction.MERCHANT_ID).is(merchantId)
                                                                                       .with(Transaction.CLIENT_TSN).is(clientTsn)
                                                                                       .with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)).fetchOne();
            if (existingRefund != null) {
                throw new UpayRefundOrderNoop(UpayErrorScenesConstant.UPAY_REFUND_REPEAT, UpayErrorScenesConstant.UPAY_REFUND_REPEAT_MESSAGE);
            }

            //如果订单状态是退款错误 ,并且此次退款是部分退款，则获取上一次部分退款的状态，如果上一次部分退款是未知错误，则不允许退款
            if(orderStatus == Order.STATUS_REFUND_ERROR && originalAmount != BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL)){
                Map<String,Object> latestPartialRefundTransaction = repository.getLatestPartialRefundTransactionByOrderSn(merchantId
                        , orderSn, originalTotal);
                if(latestPartialRefundTransaction != null){
                    UpayBizError error = UpayUtil.getBizError(latestPartialRefundTransaction, MpayServiceProvider.OP_REFUND);
                    if(error != null && UpayBizError.UNEXPECTED_PROVIDER_ERROR.getName().equals(error.getName())){
                        throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR_MESSAGE);
                    }
                }
            }

            if(payExtraParams != null){
                //海外云闪付,下游判断
                String sqbWalletName = MapUtil.getString(payExtraParams, Transaction.SQB_WALLET_NAME);
                if(ApolloConfigurationCenterUtil.getUnionOverseasWallet().contains(sqbWalletName)) {
                    extraParams.put(Transaction.SQB_WALLET_NAME, sqbWalletName);
                }
                // 拷贝参数到流水
                UpayUtil.copyIfExists(payExtraParams, extraParams, Transaction.COPY_EXTRA_PARAMS_KEYS);

                //拷贝分账参数到流水
                if(payExtraParams.containsKey(Transaction.PROFIT_SHARING)){
                    long tradeRefundAmount = originalTotal - netOriginal;
                    // 预授权完成后的退款金额应该要使用支付交易金额来计算
                    if (MapUtil.getIntValue(payTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                        tradeRefundAmount = MapUtil.getLongValue(payTransaction, Transaction.ORIGINAL_AMOUNT) - netOriginal;
                    }
                    ImmutablePair<Boolean, Map<String, Object>> result = facade.isNeedSharingRestitute(payTransaction, orderSn, originalAmount, tradeRefundAmount, refundProfitSharing);
                    //使用分账服务返回的分账回退信息
                    Map<String,Object> profitSharing = result.getRight();
                    if(result.getLeft()){
                        addPayInfoToProfitSharing(payTransaction, profitSharing);
                        extraParams.put(Transaction.PROFIT_SHARING, profitSharing);
                    }else {
                        if (profitSharing != null && (MapUtil.getBooleanValue(profitSharing, ProfitSharing.PROFIT_REFUND_TRANSACTION_FLAG) 
                                || MapUtil.getBooleanValue(profitSharing, ProfitSharing.SAME_DAY_CANCEL_PROFIT)
                                || !StringUtil.empty(sftBrandId))) {
                            extraParams.put(Transaction.PROFIT_SHARING, profitSharing);
                        }
                    }
                }
                //拷贝内部业务参数到流水
                if (payExtraParams.containsKey(Transaction.SQB_INNER_BIZ)) {
                    String sqbInnerBiz = StringUtils.EMPTY;
                    String refundSqbInnerBiz = MapUtils.getString(extraParams, Transaction.SQB_INNER_BIZ);
                    String paySqbInnerBiz = MapUtils.getString(payExtraParams, Transaction.SQB_INNER_BIZ);
                    if (StringUtils.isNotEmpty(refundSqbInnerBiz)) {
                        sqbInnerBiz = sqbInnerBiz + refundSqbInnerBiz;
                    }
                    if (StringUtils.isNotEmpty(paySqbInnerBiz)) {
                        if (StringUtils.isEmpty(sqbInnerBiz) || sqbInnerBiz.endsWith(";")) {
                            sqbInnerBiz = sqbInnerBiz + paySqbInnerBiz;
                        } else {
                            sqbInnerBiz = sqbInnerBiz + ";" + paySqbInnerBiz;
                        }
                    }
                    extraParams.put(Transaction.SQB_INNER_BIZ, sqbInnerBiz);
                }
                //花呗分期金额处理
                if (payExtraParams.containsKey(Transaction.SQB_FQ_AMOUNT)) {
                    if(MapUtil.getBooleanValue(payExtraParams, Transaction.SQB_FQ_COMBINATION_PAY, false)){
                        extraParams.put(Transaction.SQB_FQ_COMBINATION_PAY, true);
                    }
                    long fqAmount = MapUtil.getLongValue(payExtraParams, Transaction.SQB_FQ_AMOUNT);
                    if (fqAmount != 0) {
                        //当有红包优惠的时候effective_total<original_total
                        long orderOriginalTotal = MapUtil.getLongValue(order, Order.ORIGINAL_TOTAL);
                        long orderNetOriginal = MapUtil.getLongValue(order, Order.NET_ORIGINAL);
                        long refundAmountValue = Long.parseLong(refundAmount);
                        if(orderOriginalTotal == refundAmountValue){
                            extraParams.put(Transaction.SQB_FQ_AMOUNT, fqAmount);
                        }else if(orderNetOriginal == refundAmountValue){
                            long currentRefundFqAmount = fqAmount;
                            List<Map<String, Object>> refundTransactions = repository.getRefundTransactionsByOrderSn(merchantId, orderSn);
                            for (Map<String, Object> refundTransaction : refundTransactions) {
                                Object extraObject = refundTransaction.get(Transaction.EXTRA_PARAMS);
                                if (extraObject != null && extraObject instanceof Map) {
                                    Map<String, Object> refundExtraParam = (Map<String, Object>) extraObject;
                                    long refundFqAmount = MapUtil.getLongValue(refundExtraParam, Transaction.SQB_FQ_AMOUNT);
                                    currentRefundFqAmount -= refundFqAmount;
                                }
                            }
                            extraParams.put(Transaction.SQB_FQ_AMOUNT, currentRefundFqAmount);
                        }else {
                            long amountLong = BigDecimal.valueOf((refundAmountValue * 1.0D / orderOriginalTotal) * fqAmount).setScale(0, BigDecimal.ROUND_DOWN).longValue();
                            extraParams.put(Transaction.SQB_FQ_AMOUNT, amountLong);
                        }
                    }
                }
                //支付宝代扣信息,下游判断
                String barcodeType = MapUtil.getString(payExtraParams, Transaction.BARCODE_TYPE);
                if(UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
                    extraParams.put(Transaction.BARCODE_TYPE, barcodeType);
                    extraParams.put(Transaction.BARCODE, MapUtil.getString(payExtraParams, Transaction.BARCODE));
                }
            }
            // 是否需要删除extendParams标识（退款时需要插入交易花呗分期数据，用于sp和账本展示，但不需要上送到支付源）
            boolean needRemoveExtendedParams = false;
            //设置海外参数等
            Map<String,Object> extraOutFields = new HashMap<>();
            Map<String, Object> payExtraOutFields = (Map<String, Object>) payTransaction.get(Transaction.EXTRA_OUT_FIELDS);
            if (payExtraOutFields != null) {
                MapUtil.addKeysIfNotExist(payExtraOutFields, extraOutFields, Transaction.OVERSEAS, Transaction.COMBO_ID, Transaction.NUCC_BESTPAY, TransactionParam.HB_FQ, Transaction.HB_FQ_SPONSOR,
                        Transaction.FQ_SPONSOR, Transaction.WELFARE_ACCOUNT_ID, Transaction.REFER_NUMBER, Transaction.AUTH_NO, Transaction.BATCH_BILL_NO, Transaction.SYS_TRACE_NO, Transaction.SUBSCRIPTION_NO,
                        Transaction.CONSUME_TIME, Transaction.DEPOSIT_TYPE, Transaction.WILD_CARD_TYPE, Transaction.WALLET_ACCOUNT_TYPE, Transaction.WX_INSTALLMENT_INFO);
                Object huabeiFQ = payExtraOutFields.get(TransactionParam.HB_FQ);
                if (huabeiFQ != null) {
                    Map<String, Object> payExtendedParams = (Map<String, Object>) payTransaction.get(Transaction.EXTENDED_PARAMS);
                    if(payExtendedParams != null && payExtendedParams.containsKey(BusinessV2Fields.EXTEND_PARAMS)) {
                        needRemoveExtendedParams = true;
                        extended = (extended == null) ? new HashMap<>() : extended;
                        extended.put(BusinessV2Fields.EXTEND_PARAMS, payExtendedParams.get(BusinessV2Fields.EXTEND_PARAMS));
                    }
                    MapUtil.addKeysIfNotExist(MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS, new HashMap<>()), extraParams, Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, Transaction.SQB_FQ_SELLER_SERVICE_CHARGE, Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, Transaction.SQB_FQ_SERVICE_TYPE);
                }
                // 银行卡交易存放payment信息，用于退款时参数上送
                if (MapUtil.getIntValue(payTransaction, Transaction.PAYWAY) == Payway.BANKCARD.getCode()) {
                    MapUtil.addKeysIfNotExist(payExtraOutFields, extraOutFields, Transaction.PAYMENTS, Transaction.PAY_SETTLE_DATE);
                }
                // 银行转账存放的付款人信息，返回给业务方
                if (MapUtil.getIntValue(payTransaction, Transaction.PAYWAY) == Payway.BANKACCOUNT.getCode()) {
                    MapUtil.addKeysIfNotExist(payExtraOutFields, extraOutFields, Transaction.PAYER_INFO);
                }
                //填充优惠额度ID
                String quotaId = MapUtil.getString(payExtraOutFields, Transaction.QUOTA_FEE_RATE_TAG);
                if (StringUtils.isNotEmpty(quotaId)) {
                    extraOutFields.put(Transaction.QUOTA_FEE_RATE_TAG, quotaId);
                    extraOutFields.put(Transaction.QUOTA_FEE_RATE, MapUtil.getString(payExtraOutFields, Transaction.QUOTA_FEE_RATE));
                }
            }

            if (Objects.equals(Provider.FUYOU.getCode(), MapUtil.getIntValue(payTransaction, Transaction.PROVIDER))
                    && Objects.equals(Payway.BANKCARD.getCode(), MapUtil.getIntValue(payTransaction, Transaction.PAYWAY))
                    && DateTimeUtil.getOneDayStart(MapUtil.getLongValue(payTransaction, DaoConstants.CTIME)) == DateTimeUtil.getOneDayStart(System.currentTimeMillis())
                    && Objects.equals(ProviderWalletAccountTypeEnum.CROSS_CARD.getValue(), MapUtil.getIntValue(extraOutFields, Transaction.WALLET_ACCOUNT_TYPE))
                    && refundAllOrCancel) {
                //富友外卡存在当天汇率问题，目前不支持当天全额退款
                throw new UpayRefundOrderNoop(UpayErrorScenesConstant.FUYOU_WILD_CARD_CAN_NOT_REFUND, UpayErrorScenesConstant.FUYOU_WILD_CARD_CAN_NOT_REFUND_MESSAGE);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                    Order.MERCHANT_ID, merchantId,
                    Order.STATUS, Order.STATUS_REFUND_INPROGRESS,
                    DaoConstants.VERSION, order.get(DaoConstants.VERSION));

            if(isDBTrade){
                try{
                    if(order.get(DaoConstants.VERSION) == null) {
                        final Map tmpOrder = order;
                        final Map tmpTransaction = payTransaction;

                        final Map nonSqlOrderUpdate = CollectionUtil.hashMap(DaoConstants.ID, nonSqbOrder.get(DaoConstants.ID),
                                NonSqbOrder.IS_SYNC, NonSqbOrder.IS_SYNC_TRUE,
                                DaoConstants.VERSION, nonSqbOrder.get(DaoConstants.VERSION)
                                );
                        repository.doInTransaction(() ->{
                            repository.getOrderDao().save(tmpOrder);
                            repository.getTransactionDao().save(tmpTransaction);
                            repository.getNonSqbOrderDao().updatePart(nonSqlOrderUpdate);
                        });
                    }else{
                        repository.getOrderDao().updatePart(orderUpdate);
                        order.put(Order.STATUS, Order.STATUS_REFUND_INPROGRESS);
                    }
                }catch (DaoVersionMismatchException e) {
                    // 订单号已被其它服务修改，当前任务不执行
                    throw new UpayRefundOrderNoop(UpayErrorScenesConstant.UPAY_REFUND_REPEAT, UpayErrorScenesConstant.UPAY_REFUND_REPEAT_MESSAGE);
                }
            }else {
                if(!simpleRedisLock.tryLock(SimpleRedisLock.LOCK_KEY_HISTORY_REFUND_PREFIX + orderSn, tsn, 5, TimeUnit.MINUTES)) {
                    // 订单号已被其它服务修改，当前任务不执行
                    throw new UpayRefundOrderNoop(UpayErrorScenesConstant.UPAY_REFUND_REPEAT, UpayErrorScenesConstant.UPAY_REFUND_REPEAT_MESSAGE);
                }
                if (refundByDate) {
                    extraOutFields.put(Transaction.USE_APPROXIMATE_FEE, Boolean.TRUE);
                }
                extraOutFields.put(Transaction.IS_HISTORY_TRADE_REFUND, Boolean.TRUE);
                order.putAll(orderUpdate);
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                try {
                    upayOrderService.updateOrder(order);
                }catch (Exception e) {
                    logger.error("error in update order hbase data, data = {}, ex = {}", order, e);
                }
                //历史退款的订单信息存入缓存
                tradeCacheService.setHistoryRefundInfoCache(terminalOrStoreSn, BeanUtil.getPropString(order, Order.CLIENT_SN), BeanUtil.getPropString(order, Order.SN));

            }
            
            if(isRefundNonSqbOrder){
                extraOutFields.put(Transaction.REFUND_NON_SQB_ORDER, isRefundNonSqbOrder);
            }

            // 退款流水中需要标识原支付流水是预授权 并且存储预授权消费金额
            if(Transaction.TYPE_DEPOSIT_CONSUME == BeanUtil.getPropInt(payTransaction, Transaction.TYPE)) {
                extraOutFields.put(Transaction.IS_DEPOSIT, true);
                extraOutFields.put(Transaction.DEPOSIT_CONSUME_EFFECTIVE_AMOUNT, BeanUtil.getPropLong(payTransaction,Transaction.EFFECTIVE_AMOUNT));
                MapUtil.addKeysIfNotExist(payExtraOutFields, extraOutFields, Transaction.CONSUME_ORDER_SN, Transaction.ENTRUST_PAY);
            }
            Boolean isMchChannelCouponSubsidy = (Boolean) BeanUtil.getNestedProperty(payTransaction, Transaction.KEY_IS_IS_MCH_CHANNEL_COUPON_SUBSIDY);
            if(isMchChannelCouponSubsidy != null){
                extraOutFields.put(Transaction.IS_MCH_CHANNEL_COUPON_SUBSIDY, isMchChannelCouponSubsidy);
            }

            String unionPayChannelType = (String) BeanUtil.getNestedProperty(payTransaction, Transaction.KEY_UNION_PAY_CHANNEL_TYPE);
            if(unionPayChannelType != null){
                extraOutFields.put(Transaction.UNION_PAY_CHANNEL_TYPE, unionPayChannelType);
            }
            // 存放订单信息，给下游系统使用
            extraOutFields.put(Transaction.ORDER_INFO, CollectionUtil.hashMap(Order.ORIGINAL_TOTAL, order.get(Order.ORIGINAL_TOTAL),
                                                                              Order.EFFECTIVE_TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                                                                              DaoConstants.CTIME, order.get(DaoConstants.CTIME),
                                                                              Order.TRADE_NO, order.get(Order.TRADE_NO)
                    ));

            // 礼品卡服务需要上送退款时使用的设备号
            if(MapUtil.getIntValue(order, Order.PAYWAY) == Order.PAYWAY_GIFT_CARD && !StringUtil.empty(terminalSn)) {
                extended = (extended == null) ? new HashMap<String, Object>() : extended;
                extended.put(TransactionParam.TERMINAL_ID, terminalSn);
                extended.put(TransactionParam.STORE_ID, MapUtil.getString(basicParams, TransactionParam.STORE_SN));
            }
            @SuppressWarnings("unchecked")
            Map<String, Object> refundTransaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                    Transaction.TSN, tsn,
                    Transaction.CLIENT_TSN, clientTsn,
                    Transaction.TYPE, Transaction.TYPE_REFUND,
                    Transaction.ITEMS, PaymentUtil.isNewTradeCoprocessorOrder(order) ? CollectionUtil.hashMap(Transaction.PAYMENTS, transactionPayments) : null,
                    Transaction.SUBJECT, order.get(Order.SUBJECT),
                    Transaction.BODY, order.get(Order.BODY),
                    Transaction.STATUS, Transaction.STATUS_CREATED,
                    Transaction.ORIGINAL_AMOUNT, originalAmount,
                    Transaction.EFFECTIVE_AMOUNT, effectiveAmount,
                    Transaction.MERCHANT_ID, merchantId,
                    Transaction.STORE_ID, transactionUseOrderTerminalInfo ? orderStoreId : storeId,
                    Transaction.TERMINAL_ID, transactionUseOrderTerminalInfo ? orderTerminalId : terminalId,
                    Transaction.PROVIDER, payTransaction.get(Transaction.PROVIDER),
                    Transaction.PAYWAY, order.get(Order.PAYWAY),
                    Transaction.SUB_PAYWAY, order.get(Order.SUB_PAYWAY),
                    Transaction.PRODUCT_FLAG, payTransaction.get(Transaction.PRODUCT_FLAG),
                    Transaction.ORDER_ID, orderId,
                    Transaction.ORDER_SN, orderSn,
                    Transaction.OPERATOR, operator,
                    Transaction.EXTENDED_PARAMS, extended,
                    Transaction.REFLECT, reflect,
                    Transaction.CONFIG_SNAPSHOT, payTransaction.get(Transaction.CONFIG_SNAPSHOT),
                    Transaction.EXTRA_OUT_FIELDS, extraOutFields,
                    Transaction.BUYER_UID,payTransaction.get(Transaction.BUYER_UID),
                    Transaction.BUYER_LOGIN,payTransaction.get(Transaction.BUYER_LOGIN));

            //拷贝支付的扩展信息到退款流水
            copyFromPayExtraParams(extraParams, payTransaction);

            // 跨门店退款时，更新终端信息和门店信息到快照中
            if (!transactionUseOrderTerminalInfo && !Objects.equals(orderTerminalId, terminalId)) {
                //富友刷卡跨门店退款时必须用当前商户已激活的终端进行
                resetRefundTermIdIfUseOtherTerminalType(wosaiStoreId, basicParams, refundTransaction);
                Map<String, Object> configSnapshot = MapUtil.getMap(refundTransaction, Transaction.CONFIG_SNAPSHOT);
                configSnapshot.putAll(MapUtil.copyInclusive(basicParams, TransactionParam.STORE_ID, TransactionParam.STORE_SN, TransactionParam.STORE_NAME,
                        TransactionParam.TERMINAL_ID, TransactionParam.TERMINAL_SN, TransactionParam.TERMINAL_NAME, TransactionParam.TERMINAL_VENDOR_APP_APPID,
                        TransactionParam.TERMINAL_CATEGORY));
            }
            if (!extraParams.isEmpty()){
                refundTransaction.put(Transaction.EXTRA_PARAMS, extraParams);
            }

            MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(refundTransaction);

            UpayUtil.configAlipayV2AuthInfo(serviceProvider, facade, refundTransaction
                    , storeId, MapUtil.getLongValue(order, DaoConstants.CTIME));

            Map<String,Object> tradeParams = serviceProvider.getTradeParams(refundTransaction);
            boolean isFullRefund = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(refundTransaction, Transaction.ORIGINAL_AMOUNT);
            boolean needCalculateFee = true;
            if(isFullRefund && tradeParams.containsKey(TransactionParam.FEE)){
                needCalculateFee = false;
            }

            if (isFormal) {
                int payway = MapUtil.getIntValue(refundTransaction, UpayService.PAYWAY, Order.PAYWAY_ALIPAY);
                int subPayway = MapUtil.getIntValue(refundTransaction, UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_BARCODE);
                Map isNeedRefundFeeFlagMap = MapUtil.getMap(basicParams, TransactionParam.IS_NEED_REFUND_FEE_FLAG);
                Boolean isNeedRefund = (Boolean) BeanUtil.getNestedProperty(isNeedRefundFeeFlagMap
                        , payway + "." + subPayway);
                if (Objects.nonNull(isNeedRefund) && !isNeedRefund) {
                    needCalculateFee = true;
                    extraOutFields.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, false);
                }
            }

            if(needCalculateFee){
                Long fee = null;
                if(isDBTrade){
                    fee = FeeUtil.calculateRefundOrCancelFee(repository, refundTransaction);
                }else {
                    fee = FeeUtil.calculateHistoryRefundFee(repository, refundTransaction, gatewaySupportService, MapUtil.getLongValue(order, DaoConstants.CTIME), refundByDate);
                }
                tradeParams.put(TransactionParam.FEE, fee);
            }
            facade.resetSignParams(serviceProvider, refundTransaction);
            repository.getTransactionDao().save(refundTransaction);
            if(needRemoveExtendedParams) {
                extended.remove(BusinessV2Fields.EXTEND_PARAMS);
            }
            if (isCrossMerchantRefund){
                tradeCacheService.putCrossMerchantRefundCache(terminalOrStoreSn, sn, merchantId);
            }
            TransactionContext context = workflowManager.startWorkflow(apiVer, terminalOrStoreSn, order, refundTransaction);
            return context;
        }else if(orderStatus == Order.STATUS_REFUNDED){
            throw new UpayRefundOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_ALL_REFUND, UpayErrorScenesConstant.UPAY_REFUND_ALL_REFUND_MESSAGE);
        }else {
            throw new UpayRefundOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE, UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE_MESSAGE);
        }

    }

    private void copyFromPayExtraParams(Map<String, Object> refundExtraParams, Map<String, Object> payTransaction) {
        //拷贝sqbUserId到退款流水的extraParams中
        String sqbUserId = BeanUtil.getPropString(payTransaction, Transaction.KEY_SQB_USER_ID);
        if (StringUtils.isNotEmpty(sqbUserId)) {
            refundExtraParams.put(Transaction.SQB_USER_ID, sqbUserId);
        }
        String productCode = BeanUtil.getPropString(payTransaction, Transaction.KEY_SQB_PRODUCT_CODE);
        if (TransactionParam.SQB_PRODUCT_CODE_WEIXIN_CYCLE.equals(productCode)) {
            //如果是微信周期代扣款，则需要把产品code拷贝到退款流水中
            refundExtraParams.put(Transaction.SQB_PRODUCT_CODE, productCode);
        }
    }

    private Map<String,Object> mockOrder(Map request, Map tradeConfig, int paywayCode, int subPaywayCode, Map nonSqbOrder) {
        String sn = tsnGenerator.nextSn();
        String orderId = UpayUtil.getOrderIdBySn(sn);
        String orderSnOrClientSn = BeanUtil.getPropString(request, UpayService.CLIENT_SN, BeanUtil.getPropString(request, UpayService.SN));
        long total = BeanUtil.getPropLong(nonSqbOrder, NonSqbOrder.AMOUNT);
        Map<String, Object> order = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                DaoConstants.CTIME, System.currentTimeMillis(),
                Order.SN, sn,
                Order.CLIENT_SN, orderSnOrClientSn,
                Order.SUBJECT, BeanUtil.getPropString(request, UpayService.SUBJECT, BeanUtil.getPropString(tradeConfig, TransactionParam.STORE_NAME)),
                Order.BODY , BeanUtil.getPropString(request, UpayService.BODY, BeanUtil.getPropString(tradeConfig, TransactionParam.STORE_NAME)),
                Order.STATUS, Order.STATUS_PAID,
                Order.TCP_MODIFIED, false,
                Order.ORIGINAL_TOTAL, total,
                Order.NET_ORIGINAL, total,
                Order.EFFECTIVE_TOTAL, total,
                Order.NET_EFFECTIVE, total,
                Order.PAYWAY, paywayCode,
                Order.SUB_PAYWAY, subPaywayCode,
                Order.TRADE_NO, nonSqbOrder.get(NonSqbOrder.TRADE_NO),
                Order.MERCHANT_ID, tradeConfig.get(TransactionParam.MERCHANT_ID),
                Order.STORE_ID, tradeConfig.get(TransactionParam.STORE_ID),
                Order.TERMINAL_ID, tradeConfig.get(TransactionParam.TERMINAL_ID),
                Order.OPERATOR, request.get(UpayService.OPERATOR),
                Order.REFLECT, request.get(UpayService.REFLECT));

        return order;
    }

    private Map<String, Object> mockPayTransaction(Map<String, Object> order, Map tradeConfig, Map nonSqbOrder){
        String sn = MapUtil.getString(order, Order.SN);
        int paywayCode = MapUtil.getIntValue(order, Order.PAYWAY);
        int subPaywayCode = MapUtil.getIntValue(order, Order.SUB_PAYWAY);

        String transactionId = UpayUtil.getTransactionIdBySn(sn);
        Map<String, Object> transaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                Transaction.TSN, sn,
                Transaction.CLIENT_TSN, order.get(Order.CLIENT_SN),
                Transaction.TYPE, Transaction.TYPE_PAYMENT,
                Transaction.SUBJECT, order.get(UpayService.SUBJECT),
                Transaction.BODY, order.get(UpayService.BODY),
                Transaction.STATUS, Transaction.STATUS_SUCCESS,
                Transaction.ORIGINAL_AMOUNT, order.get(Order.ORIGINAL_TOTAL),
                Transaction.EFFECTIVE_AMOUNT, order.get(Order.ORIGINAL_TOTAL),
                Transaction.RECEIVED_AMOUNT, order.get(Order.ORIGINAL_TOTAL),
                Transaction.PAID_AMOUNT, order.get(Order.ORIGINAL_TOTAL),
                Transaction.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                Transaction.STORE_ID, order.get(Order.STORE_ID),
                Transaction.TERMINAL_ID, order.get(Order.TERMINAL_ID),
                Transaction.OPERATOR, order.get(Order.OPERATOR),
                Transaction.PAYWAY, paywayCode,
                Transaction.SUB_PAYWAY, subPaywayCode,
                Transaction.ORDER_ID, order.get(DaoConstants.ID),
                Transaction.ORDER_SN, sn,
                Transaction.EXTRA_OUT_FIELDS, CollectionUtil.hashMap(Transaction.REFUND_NON_SQB_ORDER, true),
                Transaction.EXTRA_PARAMS, null,
                Transaction.EXTENDED_PARAMS, null,
                Transaction.REFLECT, order.get(UpayService.REFLECT),
                Transaction.CONFIG_SNAPSHOT, tradeConfig,
                Transaction.TRADE_NO, nonSqbOrder.get(NonSqbOrder.TRADE_NO),
                Transaction.CHANNEL_FINISH_TIME, nonSqbOrder.get(NonSqbOrder.FINISH_TIME),
                Transaction.PROVIDER, order.get(Order.PROVIDER),
                Transaction.FINISH_TIME, order.get(DaoConstants.CTIME),
                DaoConstants.CTIME, order.get(DaoConstants.CTIME));

        if(!StringUtil.empty((String)nonSqbOrder.get(NonSqbOrder.CHANNEL_TRADE_NO))) {
            ((Map)transaction.get(Transaction.EXTRA_OUT_FIELDS)).put(Transaction.CHANNEL_TRADE_NO, nonSqbOrder.get(NonSqbOrder.CHANNEL_TRADE_NO));
        }

        MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(transaction);
        if (serviceProvider == null) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE);
        }
        // 设置手续费
        Map<String, Object> tradeParams = serviceProvider.getTradeParams(transaction);
        tradeParams.put(TransactionParam.ACTIVE, true);
        long fee = FeeUtil.calculatePayOrPrecreateFee(transaction);
        if(fee < 0){
            logger.error("手续费小于0 fee: {}, transaction: {}", fee, transaction);
            throw new UpayBizException(UpayErrorScenesConstant.VALIDATION_EXCEPTION_CONFIG_ERROR, UpayErrorScenesConstant.VALIDATION_EXCEPTION_CONFIG_ERROR_MESSAGE);
        }
        tradeParams.put(TransactionParam.FEE, fee);

        if(paywayCode == Order.PAYWAY_ALIPAY) {
            // 重写支付宝payway
            if(!AlipayV1ServiceProvider.NAME.equals(serviceProvider.getName())
                    && !AlipayWapServiceProvider.NAME.equals(serviceProvider.getName())
                    && !AlipayOverseasServiceProvider.NAME.equals(serviceProvider.getName())){
                if(AlipayIntlServiceProvider.NAME.equals(serviceProvider.getName())) {
                    order.put(Order.PAYWAY, Order.PAYWAY_ALIPAY_INTL);
                    transaction.put(Order.PAYWAY, Order.PAYWAY_ALIPAY_INTL);

                }else {
                    order.put(Order.PAYWAY, Order.PAYWAY_ALIPAY2);
                    transaction.put(Order.PAYWAY, Order.PAYWAY_ALIPAY2);

                }
            }

            transaction.put(Transaction.PROVIDER, serviceProvider.getProvider());
            order.put(Order.PROVIDER, serviceProvider.getProvider());
        }
        return transaction;
    }


    /**
     * 获取退款的最大可退天数
     * @param provider
     * @param payway
     * @return
     */
    private int getMaxRefundableDays(Integer historyTradeRefundConfig, Long provider,  int payway) {
        if(null == historyTradeRefundConfig || historyTradeRefundConfig == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_CLOSE){
            String maxDay = null;
            try{
                Map<String,Object> paywayMaxRefundableDay = systemConfigService.getSystemConfigContentByName(UpayConstant.SYSTEM_CONFIG_NAME_PAYWAY_MAX_REFUNDABLE_DAYS);
                maxDay = BeanUtil.getPropString(paywayMaxRefundableDay, provider + ":" + payway);
                if(maxDay == null){
                    maxDay = BeanUtil.getPropString(paywayMaxRefundableDay, provider + ":" + payway);
                }
                if(maxDay == null){
                    maxDay = BeanUtil.getPropString(paywayMaxRefundableDay, payway + "");
                }
                if(maxDay == null){
                    maxDay = MapUtils.getString(paywayMaxRefundableDay, "");
                }
            }catch (Exception e){
                logger.error("get max refund days config error: " + e.getMessage(), e);
            }
            return StringUtil.empty(maxDay) ? UpayConstant.DEFAULT_MAX_REFUNDABLE_DAYS : Integer.parseInt(maxDay);
        }
        
        return Integer.MAX_VALUE;
    }

    public void acrossStoreRefundProcess(String merchantId, String storeId, Map<String, Object> payTransaction) {
        if (StringUtils.isBlank(merchantId) || StringUtils.isBlank(storeId)
                || MapUtils.isEmpty(payTransaction)) {
            return;
        }

        boolean allow = facade.isAllowAcrossStoreRefund(merchantId);
        if (!allow) {
            if (!StringUtils.equalsIgnoreCase(storeId
                    , BeanUtil.getPropString(payTransaction, TransactionParam.STORE_ID))) {
                throw new UpayBizException(UpayErrorScenesConstant.ACROSS_STORE_REFUND_CLOSE
                        , UpayErrorScenesConstant.ACROSS_STORE_REFUND_CLOSE_MESSAGE);
            }
        }
    }

    /**
     *  分账信息里面新增一些支付时候的信息，下游业务需要这些信息，方便业务处理
     * @param payTransaction
     * @param profitSharing
     */
    private void addPayInfoToProfitSharing(Map<String,Object> payTransaction, Map<String,Object> profitSharing){
        if(profitSharing != null && !profitSharing.isEmpty()){
            profitSharing.put(ProfitSharing.SHARING_PAY_CLEARING_AMOUNT, UpayProfitSharingUtil.getClearingAmountByTransaction(payTransaction));
        }
    }

    /**
     * 退款分账回退金额校验
     * @param refundProfitSharing
     */
    private void checkRefundProfitSharing(long refundAmount, Map<String,Object> refundProfitSharing){
        if(refundProfitSharing == null){
            return;
        }
        List<Map<String,Object>> receivers = (List<Map<String, Object>>) MapUtil.getObject(refundProfitSharing, ProfitSharing.RECEIVERS);
        if(receivers == null || receivers.size() == 0){
            return;
        }
        long sumRefundSharingAmount = receivers.stream().mapToLong(r -> MapUtil.getLongValue(r, ProfitSharing.RECEIVER_SHARING_AMOUNT)).sum();
        if(sumRefundSharingAmount > refundAmount){
            throw  new ExternalServiceException(UpayErrorScenesConstant.SHARING_RESTITUTE_AMOUNT_LIMIT_ERROR, UpayErrorScenesConstant.SHARING_RESTITUTE_AMOUNT_LIMIT_MESSAGE);
        }
    }

    /**
     * 拷贝指定参数到extraParams，无法全部拷贝extended中的指定字段到extraParams，因为extended中的很多字段已经被下游业务所使用
     *
     * @param extended
     * @param extraParams
     */
    private void setSqbInnerParam(Map<String, Object> extended, Map<String, Object> extraParams) {
        if (Objects.isNull(extended) || Objects.isNull(extraParams)) {
            return;
        }
        for (String key : Transaction.SQB_REFUND_EXTENDED_KEYS) {
            if (extended.containsKey(key)) {
                extraParams.put(key, extended.get(key));
                extended.remove(key);
            }
        }
    }

    /**
     * 富友交易限制时间限制　0~3点只允许退当日交易
     *
     * @param transaction　交易流水记录
     * @return
     */
    boolean fuyouTradingTimeAllowed(Map<String, Object> transaction) {
        if (BeanUtil.getPropInt(transaction, Transaction.PROVIDER, 0) != Order.PROVIDER_FUYOU) {
            return true;
        }
        //往后延5分钟以免出现边界情况，解决跨天校验问题。例：商户01.01 23:59发起退款，我们向富友侧次日01.02 00:00发起请求
        LocalDateTime now = LocalDateTime.now().plusMinutes(5);
        LocalDate dayDateLine = now.toLocalDate();
        LocalDateTime beginDateLine = LocalDateTime.of(dayDateLine, LocalTime.of(0, 0, 0, 0));
        LocalDateTime endDateLine = LocalDateTime.of(dayDateLine, LocalTime.of(3, 40, 0, 0));
        //只验证0~3:40发起的交易
        if (!(now.isAfter(beginDateLine) && now.isBefore(endDateLine))) {
            return true;
        }
        long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
        //只允许退当日交易
        long beginDateEpochMilli = LocalDateTimeUtil.toEpochMilli(beginDateLine);
        return ctime >= beginDateEpochMilli;
    }

    /**
     * 设置退款流水为收银宝的
     */
    private void resetTransactionIfTlToSyb(String wosaiStoreId, String terminalSn, Map<String, Object> basicParams, Map<String, Object> payTransaction) {
        int currentClearanceProvider = MapUtil.getIntValue(basicParams, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        boolean isTlSwitchToSybFlag = UpayUtil.isTlSwitchToSyb(currentClearanceProvider, payTransaction);
        if(isTlSwitchToSybFlag) {
            Map<String, Object> payExtraParams = (Map<String, Object>) payTransaction.get(Transaction.EXTRA_PARAMS);
            if (payExtraParams != null) {
                if (payExtraParams.containsKey(Transaction.PROFIT_SHARING)) {
                    throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR, UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_ERROR_MESSAGE);
                }
            }
            Map configSnapshot = MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT);
            Integer payway = MapUtil.getInteger(payTransaction, Transaction.PAYWAY);
            Integer subPayway = MapUtil.getInteger(payTransaction, Transaction.SUB_PAYWAY);
            String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP, TransactionParam.TRADE_APP_BASIC_PAY);
            Map tradeConfig = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
            Map sybTradeParams = MapUtil.getMap(tradeConfig, TransactionParam.TL_SYB_TRADE_PARAMS);
            Map payTlTradeParams = MapUtil.getMap(configSnapshot, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
            //需要copy过来的参数
            List<String> copyFieldList = Arrays.asList(
                    TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID,
                    TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID,
                    TransactionParam.UNION_PAY_CHANNEL_ID,
                    TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID,
                    TransactionParam.UNION_PAY_TL_UNION_MCH_ID,
                    TransactionParam.ACTIVE,
                    TransactionParam.FEE_RATE,
                    TransactionParam.FEE
            );
            for (String key : copyFieldList) {
                if (payTlTradeParams.containsKey(key)) {
                    sybTradeParams.put(key, payTlTradeParams.get(key));
                }
            }
            configSnapshot.put(TransactionParam.TL_SYB_TRADE_PARAMS, sybTradeParams);
            //tlProviderSerivce里是根据KEY UNION_PAY_TL_TRADE_PARAMS 不为null 去匹配的，需要移除
            configSnapshot.remove(TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
            //设置标签 走新的退款接口
            configSnapshot.put(UpayConstant.TL_SWITCH_SYB_FLAG, true);
            //修改成收银宝 匹配对应的providerService
            payTransaction.put(Transaction.PROVIDER, Provider.TL_SYB.getCode());
            configSnapshot.put(TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_SYB);
            configSnapshot.put(TransactionParam.PROVIDER, Provider.TL_SYB.getCode());
        }
    }

    private ImmutablePair<Boolean, TransactionContext> bankcardConfrim(String merchantId, String orderSn, Map<String, Object> order , boolean isDbTrade){
        if (MapUtil.getIntValue(order, Order.STATUS) == Order.STATUS_REFUND_ERROR) {
            Map<String, Object> lastTransaction = repository.getLatestTransactionByOrderSn(merchantId, orderSn);
            if (lastTransaction == null) {
                return ImmutablePair.of(false, null);
            }
            // 订单号已被其它服务修改，当前任务不执行
            if(distributedLock.isNotCanOrderFix(orderSn)) {
                throw new UpayRefundOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE, UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE_MESSAGE);
            }
            MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(lastTransaction);
            TransactionContext context = workflowManager.spawnTransactionContext(null, order, lastTransaction, serviceProvider);
            String rc = serviceProvider.depositQuery(context);
            Map<String, Object> orderUpdate = MapUtil.hashMap(DaoConstants.ID, order.get(DaoConstants.ID), 
                    Transaction.MERCHANT_ID, order.get(Transaction.MERCHANT_ID),
                    DaoConstants.VERSION, order.get(DaoConstants.VERSION)
                );
            if (!isDbTrade) {
                Map extraOutFields = (Map)lastTransaction.get(Transaction.EXTRA_OUT_FIELDS);
                if (extraOutFields == null) {
                    extraOutFields = new HashMap<>();
                    lastTransaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                }
                extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CONSUME, Boolean.TRUE);
            }
            rc= Workflow.RC_TRADE_DISCARD;
            Consumer<Map<String, Object>> orderUpdateCs = (update) -> {
                try {
                    if (isDbTrade) {
                        repository.doInTransaction(() -> {
                            repository.getOrderDao().updatePart(orderUpdate);
                        });
                        order.putAll(orderUpdate);
                        order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                        order.put(DaoConstants.MTIME, System.currentTimeMillis());

                    } else {
                        order.putAll(orderUpdate);
                        order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                        order.put(DaoConstants.MTIME, System.currentTimeMillis());
                        upayOrderService.updateOrder(order);
                    }
                } catch (DaoVersionMismatchException e) {
                    throw new UpayRefundOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE, UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE_MESSAGE);
                }
            };
            if (Workflow.RC_REFUND_SUCCESS.equals(rc)) {
                // 订单已处理成功，按照成功处理
                context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
                workflowManager.raise(context, Workflow.RC_REFUND_SUCCESS);
                throw new UpayRefundOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_ALL_REFUND, UpayErrorScenesConstant.UPAY_REFUND_ALL_REFUND_MESSAGE);

            } else if (Workflow.RC_RETRY.equals(rc)) {
                // 订单仍处于处理中，修改当前订单状态，设备上重新发起下单
                orderUpdate.put(Order.STATUS, Order.STATUS_REFUND_INPROGRESS);
                lastTransaction.put(Transaction.STATUS, Transaction.STATUS_IN_PROG);
                orderUpdateCs.accept(orderUpdate);
                context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
                workflowManager.raise(context, Workflow.RC_RETRY);
                return ImmutablePair.of(true, context);

            } else if (Workflow.RC_TRADE_DISCARD.equals(rc)) {
                // 订单已明确失败，将之前订单变更为失败，重新走退款流程
                orderUpdate.put(Order.STATUS, Order.STATUS_DEPOSIT_CONSUMED);
                orderUpdateCs.accept(orderUpdate);
                return ImmutablePair.of(false, null);
            } else {
                throw new UpayCancelOrderStateError(UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE, UpayErrorScenesConstant.UPAY_REFUND_INVALID_ORDER_STATE_MESSAGE);
            }
        }
        return ImmutablePair.of(false, null);
    }

    private void resetRefundTermIdIfUseOtherTerminalType(String wosaiStoreId, Map<String, Object> basicParams, Map<String, Object> refundTransaction) {
        try {
            if (refundTransaction != null && MapUtils.getIntValue(refundTransaction, Transaction.PROVIDER) == Provider.FUYOU.getCode() && MapUtils.getIntValue(refundTransaction, Transaction.PAYWAY) == Payway.BANKCARD.getCode()) {
                Map<String, List<Integer>> vendorT9ProviderMap = ApolloConfigurationCenterUtil.getVendorT9ProviderMap();
                Map configSnapshot = MapUtils.getMap(refundTransaction, Transaction.CONFIG_SNAPSHOT, new HashMap());
                String terminalVendorAppAppId = MapUtil.getString(configSnapshot, TransactionParam.TERMINAL_VENDOR_APP_APPID);
                String payTerminalSn = BeanUtil.getPropString(refundTransaction, Transaction.KEY_TERMINAL_SN);
                boolean useCurrentTerminalFlag = false;
                if (vendorT9ProviderMap.containsKey(terminalVendorAppAppId)) {
                    try {
                        facade.getBasicParams(wosaiStoreId, payTerminalSn);
                    } catch (Exception e) {
                        useCurrentTerminalFlag = true;
                        logger.error("富友原支付订单终端已失效", e);
                    }
                } else {
                    useCurrentTerminalFlag = true;
                }
                if (useCurrentTerminalFlag) {
                    //原支付流水的终端已解绑 不能使用原支付的终端进行解绑
                    String currentTerminalVendorAppAppId = MapUtil.getString(basicParams, TransactionParam.TERMINAL_VENDOR_APP_APPID, "");
                    String terminalSn = MapUtil.getString(basicParams, TransactionParam.TERMINAL_SN, "");
                    if (!vendorT9ProviderMap.containsKey(currentTerminalVendorAppAppId)) {
                        //富友非t9类型的终端的退款 需要找到当前正在激活的终端号进行
                        String merchantId = MapUtil.getString(refundTransaction, Transaction.MERCHANT_ID);
                        terminalSn = facade.getOneTerminalSnWithVendorAppAppId(merchantId, currentTerminalVendorAppAppId);
                    }
                    if (!StringUtil.empty(terminalSn)) {
                        Integer payway = MapUtil.getInteger(refundTransaction, Transaction.PAYWAY);
                        Integer subPayway = MapUtil.getInteger(refundTransaction, Transaction.SUB_PAYWAY);
                        String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP, TransactionParam.TRADE_APP_BASIC_PAY);
                        //获取其他终端交易参数
                        Map<String, Object> tradeParams = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
                        Map fuyouOtherTradeParams = MapUtil.getMap(tradeParams, TransactionParam.FUYOU_TRADE_PARAMS);
                        String replaceTermId = MapUtil.getString(fuyouOtherTradeParams, TransactionParam.FUYOU_BANK_TERM_ID);
                        //分别替换掉这里的终端号再发起config_snapshot.fuyou_trade_params.term_id
                        if (!StringUtil.empty(replaceTermId)) {
                            Map refundFuyouTradeParams = MapUtil.getMap(configSnapshot, TransactionParam.FUYOU_TRADE_PARAMS);
                            if (!StringUtil.empty(MapUtil.getString(refundFuyouTradeParams, TransactionParam.FUYOU_BANK_TERM_ID, ""))) {
                                refundFuyouTradeParams.put(TransactionParam.FUYOU_BANK_TERM_ID, replaceTermId);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("富友跨终端退款替换异常{}", e);
        }

    }
}
