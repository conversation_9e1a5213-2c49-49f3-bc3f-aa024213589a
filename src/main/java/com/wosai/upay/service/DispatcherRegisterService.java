package com.wosai.upay.service;

import java.util.Collections;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wosai.config.HeraSm2Config;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

@Component
public class DispatcherRegisterService {
    private static final Logger logger = LoggerFactory.getLogger(DispatcherRegisterService.class);
    public static String podIp = System.getenv("ACK_POD_IP");
    public static String podName = System.getenv("HOSTNAME");
    public static String port = System.getProperty("jetty.port", "8080");
    private volatile boolean offline = false;
    private static final String EVENT_REGISTER = "register";
    private static final String EVENT_OFFLINE = "offline";

    @Autowired
    ExternalServiceFacade facade;

    @Autowired
    HeraSm2Config heraSm2Config;

    @PostConstruct
    public void checkInfo() {
        // 启动后注册任务放到服务器环境变量中，防止误操作导致服务被注册到线上
        if (!"true".equals(System.getenv(EVENT_REGISTER))) {
            return;
        }
        if (StringUtils.isEmpty(podIp)) {
            facade.sendDispatcherRegisterMessage("未找到可用的服务器IP，网关注册dispatcher失败，请检查服务配置", true);
            throw new RuntimeException("未找到可用的服务器IP，网关注册dispatcher失败，请检查服务配置");
        }
        if (StringUtils.isEmpty(podName)) {
            podName = podIp;
        }
    }

    /**
     * 注册服务
     */
    @PostConstruct
    public void register() {
        // 启动后注册任务放到服务器环境变量中，防止误操作导致服务被注册到线上
        if (!"true".equals(System.getenv(EVENT_REGISTER))) {
            return;
        }
        new Thread(() -> {
            // 执行到任务时，容器并没有完全启动成功，此时调用网关接口会返回“Connection refused”，需要等待监控检查通过后再继续执行
            checkServiceHealth();
            String response = callDispatcher(EVENT_REGISTER);
            if ("success".equals(response)) {
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    offline();
                }));
            }
        }).start();
    }

    /**
     * 服务健康检查
     */
    public void checkServiceHealth() {
        // 由于sm2注册时间较长，需要等注册成功后才能进行后续的处理
        long sleepSkipTime = System.currentTimeMillis() + 30 * 1000;
        while(!heraSm2Config.isLoadSuccess()) {
            if (System.currentTimeMillis() > sleepSkipTime) {
                break;
            }
            // sm2未加载完成，休眠500毫秒
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
            }
        }

        String response = null;
        for (int i = 0; i < 10; i++) {
            try {
                response = WebUtils.doGet(null, null, String.format("http://%s:%s/upay/v2/echo", podIp, port), Collections.emptyMap(), "utf-8", 500, 500);
            } catch (Exception e) {
            }
            if ("{\"message\":\"Hi, Upay!\"}".equals(response)) {
                break;
            }
            // 服务未完全启动成功，休眠500毫秒
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
            }
        }
        if (!"{\"message\":\"Hi, Upay!\"}".equals(response)) {
            logger.warn("服务健康检查失败");
        }
    }

    /**
     * 服务下线
     * @return
     */
    public String offline() {
        // 服务未开启注册或者服务已经成功下线
        if (!"true".equals(System.getenv(EVENT_REGISTER)) || offline) {
            return "success";
        }
        String response = callDispatcher(EVENT_OFFLINE);
        if ("success".equals(response)) {
            offline = true;
        }
        return response;
    }

    private String callDispatcher(String event) {
        String response = null;
        for (int i = 0; i < 10; i++) {
            Map<String, Object> request = buildRequest(event);
            try {
                response = WebUtils.doPost(null, null, "http://upay-dispatcher/admin/service-registry","application/json", JsonUtil.toJsonStr(request).getBytes(), 3000, 3000);
            } catch (Exception e) {
                logger.warn("调用dispatcher注册服务失败", e);
            }
            if ("success".equals(response)) {
                break;
            }
        }
        if (!"success".equals(response)) {
            facade.sendDispatcherRegisterMessage(String.format("网关注册dispatcher失败，请排查原因\npodName: %s\nip: %s\nport: %s\nevent: %s\nresponse: %s",
                    podName, podIp, port, event, response), true);
        } else {
            logger.warn("事件 {} 注册dispatcher成功", event);
        }
        return response;
    }

    @SuppressWarnings("rawtypes")
    private Map<String, Object> buildRequest(String event){
        Map request = MapUtil.hashMap("name", podName, "ts", System.currentTimeMillis() + "", "ip", podIp, "event", event, "port", port);
        try {
            request.put("sign", WeixinSignature.getSign(request,
                    ApolloConfigurationCenterUtil.getDispatcherRegisterKey(), "utf-8"));
        } catch (MpayException e) {
        }
        return request;
    }
}
