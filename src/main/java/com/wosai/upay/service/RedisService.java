package com.wosai.upay.service;

import com.wosai.upay.common.util.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description RedisService
 * @Date 2021/6/29 3:49 PM
 */
@Service
public class RedisService {
    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void deleteCache(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                redisTemplate.delete(pre + key);
            }
        } catch (Exception e) {
            logger.error("删除缓存异常pre[{}],key[{}] ", pre, key, e);
        }
    }

    public void deleteCacheLike(String like) {
        try {
            Set<String> keys = redisTemplate.keys(like);
            for (String key : keys) {
                deleteCache("", key);
            }
        } catch (Exception e) {
            logger.error("like[{}] ", like, e);
        }
    }

    public boolean hasCache(String pre, String key) {
        try {
            return redisTemplate.hasKey(pre + key);
        } catch (Exception e) {
            logger.error("判断缓存是否存在异常pre[{}],key[{}] ", pre, key, e);
        }
        return false;
    }

    public void setCache(String pre, String key, String value) {
        try {
            if (hasCache(pre, key)) {
                redisTemplate.delete(pre + key);
            }
            redisTemplate.boundValueOps(pre + key).set(value);
        } catch (Exception e) {
            logger.error("设置缓存异常pre[{}],key[{}],value[{}] ", pre, key, value, e);
        }
    }

    public void setCache(String pre, String key, String value, TimeUnit timeUnit, long time) {
        try {
            if (hasCache(pre, key)) {
                redisTemplate.delete(pre + key);
            }
            redisTemplate.boundValueOps(pre + key).set(value, time, timeUnit);
        } catch (Exception e) {
            logger.error("设置缓存异常pre[{}],key[{}],value[{}] ", pre, key, value, e);
        }
    }

    public Map getCacheMap(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                if(str != null){
                    return JacksonUtil.toBean(str, Map.class);
                }
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }




    public String getCacheString(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                return redisTemplate.boundValueOps(pre + key).get();
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    public List getCacheList(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return JacksonUtil.toBean(str, List.class);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    public boolean getCacheBoolean(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return Boolean.valueOf(str);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return false;
    }

    public Integer getCacheInteger(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return Integer.valueOf(str);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    public Long getCacheLong(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return Long.valueOf(str);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }
}
