package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.common.utils.WosaiNumberUtils;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.eventbus.EventBus;
import com.wosai.fsm.StateLabel;
import com.wosai.middleware.hera.toolkit.trace.ConsumerWrapper;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.constant.ModelConstant;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.controller.DepositController;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.meta.SqbScene;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.exception.*;
import com.wosai.upay.helper.UpayServiceMethodInterceptor;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.ProfitSharing;
import com.wosai.upay.model.api.*;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.*;
import com.wosai.upay.workflow.*;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.wosai.constant.UpayConstant.*;
import static com.wosai.upay.util.UpayUtil.isFormalByConfig;
import static com.wosai.upay.util.UpayUtil.processRequestPoi;

@Service
public class UpayServiceImpl implements UpayService {
    private static final Logger logger = LoggerFactory.getLogger(UpayServiceImpl.class);
    private static final String TRANSACTION_QRCODE = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.QRCODE);
    private static final String TRANSACTION_WAP_PAY_REQUEST = String.format("%s.%s", Transaction.EXTRA_OUT_FIELDS, Transaction.WAP_PAY_REQUEST);
    private static final long TL_UNIONPAY_LARGE_TRADE_BENCHMARK = 1000 * 100;
    @Autowired
    private TsnGenerator tsnGenerator;
    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private CancelProcessor cancelProcessor;
    @Autowired
    private RefundProcessor refundProcessor;
    @Autowired
    protected PaymentSumService paymentSumService;

    @Autowired
    private FixProcessor fixProcessor ;
    
    @Autowired
    private ExternalServiceFacade facade;
    
    @Autowired
    private QrcodeImaging qrcodeImaging;

    @Autowired
    private ObjectMapper om;

    @Autowired
    private WorkflowDriver driver;

    @Autowired
    private ProviderManager providerManager;

    @Autowired
    EventBus eventBus;

    @Autowired
    SimpleRedisLock simpleRedisLock;

    @Autowired
    private UpayThreadPoolManager threadPoolManager;
    
    @Autowired
    private TradeCacheService tradeCacheService;

    @Autowired
    private CSBCompatibilityService csbCompatibilityService;

    @Autowired
    private GatewaySupportService gatewaySupportService;

    @Autowired
    private FeeRateProcessor feeRateProcessor;
    @Autowired
    private CryptoService cryptoService;
    @Autowired
    private DepositProvider depositProvider;
    @Autowired
    private TradeAppFacade tradeAppFacade;

    @Autowired
    private ProviderMetaFacade providerMetaFacade;
    @Autowired
    private TradingLimitFacade tradingLimitFacade;

    @Autowired
    protected ClientNotifier clientNotifier;
    @Resource
    private BusinssCommonService businssCommonService;


    @SuppressWarnings("unchecked")
    @Override
    public DeferredResult<Map<String, Object>> pay(Map<String, Object> request, HttpServletResponse httpResponse) {
        boolean isDeposit =  BeanUtil.getPropBoolean(request, DepositService.IS_DEPOSIT, false);
        // 解析payway
        String terminalSn= (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        
        String clientSn = (String)request.get(CLIENT_SN);
        String totalAmount = (String)request.get(TOTAL_AMOUNT);
        
        String payway = (String)request.get(PAYWAY);
        String dynamicId = (String)request.get(DYNAMIC_ID);
        String dynamicIdType = (String)request.get(DYNAMIC_ID_TYPE);
        if (!UpayConstant.DYNAMIC_ID_TYPE_BANKCARD.equals(dynamicIdType) && StringUtils.empty(dynamicId)) {
            throw new UpayBizException("dynamic_id付款条码必填");
        }
        if (dynamicIdType == null && (dynamicId == null || dynamicId.length() > 32)) {
            throw new UpayBizException("dynamic_id付款条码必填，不可超过32字符");
        }

        String operator = (String)request.get(OPERATOR);

        String subject = (String)request.get(SUBJECT);
        String body = (String)request.get(DESCRIPTION);
        String deviceId = (String)request.get(DEVICE_ID);
        String clientIp = (String)request.get(CLIENT_IP);
        String tradeApp = MapUtil.getString(request, TRADE_APP);
        List<Map<String,Object>> goodsDetails = (List<Map<String, Object>>) request.get(GOODS_DETAILS);
        Map<String,Object> extended = UpayUtil.formatExtended(request.get(EXTENDED), om);
        Map<String,Object> profitSharing = UpayUtil.formatExtended(request.get(PROFIT_SHARING), om);
        Object reflect = encryptReflect(request.get(REFLECT), isDeposit);
        String notifyUrl = (String)request.get(NOTIFY_URL);
        int paywayCode = UpayUtil.resolvePayway(payway, dynamicId, dynamicIdType);
        int subPaywayCode = Order.SUB_PAYWAY_BARCODE;

        Map<String, Object> extraParams = CollectionUtil.hashMap(Transaction.BARCODE, dynamicId);
        UpayUtil.processRequestPoi(request, extraParams);
        if (deviceId != null){
            extraParams.put(Transaction.DEVICE_ID, deviceId);
        }
        if (notifyUrl != null) {
            extraParams.put(Transaction.NOTIFY_URL, notifyUrl);
        }
        if (dynamicIdType != null){
            extraParams.put(Transaction.BARCODE_TYPE, dynamicIdType);
        }
        if (!StringUtils.empty(clientIp)){
            if(clientIp.indexOf(",") > 0) {
                clientIp = clientIp.split(",")[0];
            }
            extraParams.put(Transaction.CLIENT_IP, clientIp);
        }
        setExtraParam(extended,extraParams);

        String bizModel = MapUtil.getString(extraParams, Transaction.SQB_BIZ_MODEL);
        Map<String, Object> config = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, paywayCode, subPaywayCode, tradeApp, bizModel);
        // 改写商品名称
        subject = tradeAppFacade.getNewSubject(tradeApp, bizModel, subject, MapUtil.getString(config, TransactionParam.STORE_NAME));
        //以配置内payway为准
        paywayCode = resetUnionPayway(config, paywayCode);

        extended = UpayUtil.getMergedExtendedWithGoodsDetails(goodsDetails, extended, paywayCode, config, Transaction.TYPE_PAYMENT);

        // pay接口当终端类型不等于10的时候删除对应终端上送的secret_text和encrypt_rand_num
        Map termInfoMap = MapUtil.getMap(config, TransactionParam.TRADE_EXT_TERM_INFO);
        String termType = MapUtil.getString(termInfoMap, TERM_TYPE);

        if (MapUtil.isNotEmpty(extended) && (UpayConstant.BARCODE_PAYMENT_SUPPLEMENT_ACCEPTANCE_TERMINAL.equals(termType) || termType == null)) {
            extended.remove(TERMINAL_SECRET_TEXT);
            extended.remove(TERMINAL_ENCRYPT_RAND_NUM);
        }
        long total = Long.parseLong(totalAmount);

        // 商户限流
        RateLimiterUtil.verification(BeanUtil.getPropString(config, TransactionParam.MERCHANT_SN), !isDeposit ? MpayServiceProvider.OP_PAY : MpayServiceProvider.OP_DEPOSIT_FREEZE, Boolean.FALSE);
        // 校验trade_app 相关的传参是否合法
        tradeAppFacade.checkTradeApp(tradeApp, extraParams);
        String sqbGenSn = BeanUtil.getPropString(extraParams, Transaction.SQB_GEN_SN);
        //判断是否使用客户端上送的sn
        String sn = isUseClientSn(config, sqbGenSn) ? sqbGenSn : tsnGenerator.nextSn();
        // 保存订单
        String orderId = UpayUtil.getOrderIdBySn(sn);
        Map<String, Object> order = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                                                           DaoConstants.CTIME, new Date().getTime(),
                                                           Order.SN, sn,
                                                           Order.CLIENT_SN, clientSn,
                                                           Order.SUBJECT, subject,
                                                           Order.BODY , body,
                                                           Order.STATUS, !isDeposit ? Order.STATUS_CREATED : Order.STATUS_DEPOSIT_CREATED,
                                                           Order.TCP_MODIFIED, false,
                                                           Order.ORIGINAL_TOTAL, total,
                                                           Order.NET_ORIGINAL, total,
                                                           Order.EFFECTIVE_TOTAL, total,
                                                           Order.NET_EFFECTIVE, total,
                                                           Order.PAYWAY, paywayCode,
                                                           Order.SUB_PAYWAY, subPaywayCode,
                                                           Order.MERCHANT_ID, config.get(TransactionParam.MERCHANT_ID),
                                                           Order.STORE_ID, config.get(TransactionParam.STORE_ID),
                                                           Order.TERMINAL_ID, config.get(TransactionParam.TERMINAL_ID),
                                                           Order.OPERATOR, operator,
                                                           Order.REFLECT, reflect);

        // 保存交易流水
        String transactionId = UpayUtil.getTransactionIdBySn(sn);

        Map<String, Object> transaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                                                                 Transaction.TSN, sn,
                                                                 Transaction.CLIENT_TSN, clientSn,
                                                                 Transaction.TYPE, !isDeposit ? Transaction.TYPE_PAYMENT : Transaction.TYPE_DEPOSIT_FREEZE,
                                                                 Transaction.SUBJECT, subject,
                                                                 Transaction.BODY, body,
                                                                 Transaction.STATUS, Transaction.STATUS_CREATED,
                                                                 Transaction.ORIGINAL_AMOUNT, total,
                                                                 Transaction.EFFECTIVE_AMOUNT, total,
                                                                 Transaction.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                 Transaction.STORE_ID, order.get(Order.STORE_ID),
                                                                 Transaction.TERMINAL_ID, order.get(Order.TERMINAL_ID),
                                                                 Transaction.OPERATOR, order.get(Order.OPERATOR),
                                                                 Transaction.PAYWAY, paywayCode,
                                                                 Transaction.SUB_PAYWAY, subPaywayCode,
                                                                 Transaction.ORDER_ID, orderId,
                                                                 Transaction.ORDER_SN, sn,
                                                                 Transaction.EXTRA_PARAMS, extraParams,
                                                                 Transaction.EXTENDED_PARAMS, extended,
                                                                 Transaction.REFLECT, reflect,
                                                                 Transaction.CONFIG_SNAPSHOT, config);

        if(isDeposit) {
            transaction.put(Transaction.EXTRA_OUT_FIELDS, CollectionUtil.hashMap(Transaction.IS_DEPOSIT, true));
        }
        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        // serviceProvider 非空校验
        validateServiceProvider(provider);

        //判断商户是否支持预撤单， 如果支持，判断此笔订单是否已经预撤销，如果已撤销，返回支付失败
        verifyPayPreCancel(config, clientSn);

        // 由于银联二维码的交易费率和额度相关，目前我们系统不支持按单笔交易金额设置费率，因此需要暂时对银联二维码单笔交易做限额，以确保费率统一。
        //云闪付大额交易验证
        providerMetaFacade.unionPayLargeTradeVerify(provider, transaction);

        // 设置预授权权限并校验
        setDepositValidate(isDeposit, paywayCode, transaction, provider);
        
        //重写支付宝payway
        updatePaywayIfIsAlipay(order, transaction, provider);

        //校验交易是否超限等
        validate(transaction, provider);

        //商户重复订单号校验，部分商户允许重复订单号
        updateDuplicateClientSn(config, order, transaction);
        
        //解析收钱吧红包
        Boolean activityDiscount = MapUtil.getBoolean(extraParams, Transaction.SQB_ACTIVITY_DISCOUNT);
        consultSQBDiscounts(config, order, transaction, terminalSn, paywayCode, subPaywayCode, total, null, dynamicId, provider, goodsDetails, isDeposit, activityDiscount);

        //提前设置激活的是哪套交易参数以及设置provider以及手续费
        useTradeParamAndSetProvider(transaction, order, provider);

        //设置分账信息
        setProfitSharing(facade, transaction, profitSharing, tradeApp, isDeposit);

        //生成产品标识
        genProductFlagAndComboId(transaction, provider);

        // 通道限流
        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), subPaywayCode);

        //保证order, transaction的ctime都一样，以便数据根据时间迁移后，订单与流水能在相同的时间段内。
        updateOrderAndTransactionTheSameTime(order, transaction);

        repository.save(order, transaction);

        String terminalOrStoreSn = (terminalSn != null? terminalSn: wosaiStoreId);
        DeferredResult<Map<String,Object>> response = new DeferredResult<>();
        Consumer<TransactionContext> returnConsumer = context -> {
            if(response.isSetOrExpired()){
                return;
            }
            try{
                handleContextException(context);
                Map<String,Object> result = payResponse(context, !BeanUtil.getPropBoolean(config, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));

                // 预授权交易需要变更响应中的result_code
                if(result != null && isDeposit){
                    Map bizResponse = (Map) BeanUtil.getProperty(result, CommonResponse.BIZ_RESPONSE);
                    if(null != bizResponse) {
                        bizResponse.put(CommonResponse.RESULT_CODE, DepositController.PAY_DEPOSIT_RESPONSE_MAPPING.get(bizResponse.get(CommonResponse.RESULT_CODE)));
                    }
                }
                httpResponse.addCookie(new Cookie(Transaction.ORDER_SN, sn));
                response.setResult(result);
            }catch (Exception e){
                response.setResult(UpayServiceMethodInterceptor.handleException(e));
            }
        };
        TransactionContext context = workflowManager.startWorkflow((String)request.get(API_VER), terminalOrStoreSn, order, transaction, provider, ConsumerWrapper.of(returnConsumer));
        return response;
    }

    private boolean isUseClientSn(Map<String, Object> config, String sqbGenSn) {
        Integer switchStatus = MapUtil.getInteger(config, TransactionParam.GEN_ORDER_SN_SWITCH);
        boolean isAllowGenOrderSn = Objects.nonNull(switchStatus) && (switchStatus == TransactionParam.STATUS_OPENED);
        if (Objects.nonNull(sqbGenSn) && sqbGenSn.length() > 0) {
            if (isAllowGenOrderSn && facade.isContainsOrderSn(sqbGenSn)) {
                return true;
            }
            throw new UpayBizException(UpayErrorScenesConstant.GEN_SN_ILLEGAL
                    , UpayErrorScenesConstant.GEN_SN_ILLEGAL_MESSAGE);
        }
        return false;
    }

    @Override
    public DeferredResult<Map<String, Object>> multiplePay(Map<String, Object> request, HttpServletResponse httpResponse) {
        String terminalSn= (String)request.get(TERMINAL_SN);
        String clientSn = (String)request.get(CLIENT_SN);
        String dynamicId = (String)request.get(DYNAMIC_ID);
        String tradeApp = (String)request.get(TRADE_APP);

        Map<String, Object> basicParams = facade.getBasicParams(null, terminalSn);
        String storeId = BeanUtil.getPropString(basicParams, TransactionParam.STORE_ID);
        String merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        // 商户限流
        RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_PAY, Boolean.FALSE);

        Map<String, Object> order;
        Map<String, Object> transaction;

        order = repository.getOrder(merchantId, storeId, null, clientSn);
        if (Objects.isNull(order)) {
            throw new UpayBizException("原订单不存在");
        }
        if(Order.STATUS_PAY_CANCELED != MapUtil.getIntValue(order, Order.STATUS)){
            throw new UpayBizException("只有明确失败的才支持重新支付");
        }
        String sn = (String) order.get(Order.SN);
        transaction = repository.getLatestTransactionByOrderSn(merchantId, sn);

        if (Objects.isNull(transaction)) {
            throw new UpayBizException("原流水不存在");
        }
        order.put(Order.STATUS, Order.STATUS_CREATED);
        transaction.put(Transaction.STATUS, Transaction.STATUS_CREATED);

        if (Objects.nonNull(dynamicId) && dynamicId.length() > 0) {
            BeanUtil.setNestedProperty(transaction, Transaction.EXTRA_PARAMS + "." + Transaction.BARCODE, dynamicId);
        }

        Map<String, Object> config = facade.getAllParamsWithTradeApp(
                MapUtil.getString(transaction, Transaction.STORE_ID)
                , terminalSn
                , MapUtil.getIntValue(transaction, Transaction.PAYWAY)
                , MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY)
                , tradeApp);
        transaction.put(Transaction.CONFIG_SNAPSHOT, config);

        // 重置订单金额
        order.put(Order.NET_ORIGINAL, order.get(Order.ORIGINAL_TOTAL));
        order.put(Order.NET_EFFECTIVE, order.get(Order.EFFECTIVE_TOTAL));
        repository.getOrderDao().updatePart(MapUtil.copyInclusive(order, DaoConstants.ID, Order.NET_ORIGINAL, Order.NET_EFFECTIVE, DaoConstants.VERSION));
        order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION) + 1);
        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        // serviceProvider 非空校验
        validateServiceProvider(provider);
        //重写支付宝payway
        updatePaywayIfIsAlipay(order, transaction, provider);
        //提前设置激活的是哪套交易参数以及设置provider以及手续费
        useTradeParamAndSetProvider(transaction, order, provider);
        // 设置是否重复支付标识
        BeanUtil.setNestedProperty(transaction, Transaction.KEY_IS_MULTIPLE_PAY, true);
        DeferredResult<Map<String,Object>> response = new DeferredResult<>();
        ConsumerWrapper<TransactionContext> returnConsumer = ConsumerWrapper.of(context -> {
            if(response.isSetOrExpired()){
                return;
            }
            try{
                handleContextException(context);
                Map<String,Object> result = payResponse(context, !BeanUtil.getPropBoolean(config, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
                httpResponse.addCookie(new Cookie(Transaction.ORDER_SN, sn));
                response.setResult(result);
            }catch (Exception e){
                response.setResult(UpayServiceMethodInterceptor.handleException(e));
            }
        });
        TransactionContext context = workflowManager.startWorkflow((String)request.get(API_VER), terminalSn, order, transaction, provider, returnConsumer);
        return response;
    }

    @Override
    public Map<String, Object> query(Map<String, Object> request) {
        String terminalSn = (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String clientSn= (String)request.get(CLIENT_SN);
        String refundRequestNo= (String)request.get(REFUND_REQUEST_NO);
        String sn = (String)request.get(SN);
        String scope = (String)request.get(SCOPE);
        Boolean flagQueryOrDepositFreeze = (Boolean) request.get(FLAG_QUERY_PAY_OR_DEPOSIT_FREEZE);

        Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        // 商户限流
        RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_QUERY, Boolean.FALSE);
        boolean queryDB = true;
        boolean queryHbase = true;
        if(!StringUtil.empty(scope)) {
            queryDB = UpayConstant.SCOPE_DB.equals(scope);
            queryHbase = UpayConstant.SCOPE_HBASE.equals(scope);
        }
        String storeId = BeanUtil.getPropString(basicParams, TransactionParam.STORE_ID);
        String merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        Map<String,Object> order = null;
        Map<String, Object> transaction = null;
        boolean isDBTrade = true;
        //查缓存
        TransactionContext context = tradeCacheService.getTradeCache(StringUtils.isEmpty(terminalSn) ? wosaiStoreId : terminalSn, clientSn, sn);
        if(context != null){
            order = context.getOrder();
            transaction = context.getTransaction();
        }
        // 查数据库
        if(order == null && queryDB){
            order = repository.getOrder(merchantId, storeId, sn, clientSn);
        }
        // 查c2b-wap
        if(order == null && queryDB){
            Map<String, Object> csbRequest = csbCompatibilityService.queryCSBRequest(clientSn, sn, StringUtil.empty(terminalSn) ? wosaiStoreId : terminalSn);
            if(!MapUtil.isEmpty(csbRequest)){
                return csbCompatibilityService.mockQueryResponse(csbRequest, basicParams);
            }
        }
        //查hbase
        if(order == null && queryHbase){
            String terminalOrStoreSn = (terminalSn != null? terminalSn: wosaiStoreId);
            boolean isHistoryRefund = tradeCacheService.isHistoryRefund(terminalOrStoreSn, clientSn, sn);
            boolean fromPosProxyAndQueryBySn = UpayConstant.SQB_ORIGIN_POS_PROXY.equals(BeanUtil.getPropString(request, SQB_ORIGIN)) && !StringUtils.empty(sn);

            boolean queryHistory = ApolloConfigurationCenterUtil.getQueryHistoryEnable() || isHistoryRefund || fromPosProxyAndQueryBySn;
            if (queryHistory) {
                // 跨商户退款后，支持一段时间内通过退款终端号查询交易
                if (!fromPosProxyAndQueryBySn && !StringUtil.empty(sn)){
                    String crossMerchantId = tradeCacheService.getCrossMerchantRefundCache(terminalOrStoreSn, sn);
                    if (!StringUtil.empty(crossMerchantId)){
                        merchantId = crossMerchantId;
                        logger.info("query cross merchant refund, sn:{}, merchantId:{}", sn, crossMerchantId);
                    }
                }
                order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.RECENT_6M);
                if(null == order && isHistoryRefund) {
                    order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.COLD);
                }
                isDBTrade = false;
            }
        }

        if(order == null){
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }

        String orderSn = (String)order.get(Order.SN);
        // 上送client_tsn时，只查询退款交易，退款订单号格式为"client_sn-refund_request_no"
        if(!StringUtil.empty(refundRequestNo)){
            // 优先查询数据库（Hbase默认只会查询出15笔数据，如果相同的退款单号超过15笔，则查询出的退款流水不是最后一次的退款流水）
            transaction = repository.getTransactionByOrderSnAndClientTsn(merchantId, orderSn, String.format("%s-%s", BeanUtil.getPropString(order, Order.CLIENT_SN), refundRequestNo));
            if(transaction == null && !isDBTrade) {
        	    transaction = gatewaySupportService.getTransactionByClientTsn(merchantId, orderSn, String.format("%s-%s", BeanUtil.getPropString(order, Order.CLIENT_SN), refundRequestNo), MapUtil.getLongValue(order, DaoConstants.CTIME));
        	}
        } else if (Objects.equals(Boolean.TRUE, flagQueryOrDepositFreeze)) {
            if(isDBTrade) {
                transaction = repository.getTransactionByOrderSnAndClientTsn(merchantId, orderSn, BeanUtil.getPropString(order, Order.CLIENT_SN));
            } else {
                transaction = gatewaySupportService.getTransactionByClientTsn(merchantId, orderSn, BeanUtil.getPropString(order, Order.CLIENT_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
            }
        } else if (null == transaction) {
            if (isDBTrade) {
                transaction = repository.getLatestTransactionByOrderSn(merchantId, orderSn);
            } else {
                transaction = gatewaySupportService.getLatestTransactionByOrderSn(merchantId, orderSn, MapUtil.getLongValue(order, DaoConstants.CTIME));
            }
        }
        
        if(null == transaction){
            throw new OrderNotExistsException(UpayErrorScenesConstant.TRANSACTION_NOT_EXIST, UpayErrorScenesConstant.TRANSACTION_NOT_EXIST_MESSAGE);
        }
        
        return queryResponse(order, transaction,
                !BeanUtil.getPropBoolean(basicParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
    }

    @Override
    public Map<String, Object> cancel(Map<String, Object> request) {
        return abortOrReverse(request, Transaction.CANCEL_TYPE_DEVICE);
    }

    @Override
    public Map<String, Object> revoke(Map<String, Object> request) {
        return abortOrReverse(request, Transaction.CANCEL_TYPE_CASHIER);
    }

    @Override
    public Map<String, Object> reconcileRevoke(Map<String, Object> request) {
        return abortOrReverse(request, Transaction.CANCEL_TYPE_RECONCILE);
    }

    private Map<String, Object> abortOrReverse(Map<String, Object> request, String cancelType) {
        String terminalSn = (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String clientSn= (String)request.get(CLIENT_SN);
        String sn = (String)request.get(SN);
        Map<String,Object> extended = UpayUtil.formatExtended(request.get(EXTENDED), om);
        Object reflect = request.get(REFLECT);
        Map<String, Object> basicParams = null;
        
        try{
            basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        }catch (TerminalStatusAbnormalException|StoreStatusAbnormalException|MerchantStatusAbnormalException e) {
            if(!Transaction.CANCEL_TYPE_RECONCILE.equals(cancelType)) {
                throw e;
            }
            Map<String, Object> order = repository.getOrderByOrderSn(null, sn);
            if(null == order) {
                throw e;
            }
            Map<String,Object> transaction = repository.getPayTransactionByOrderSn(BeanUtil.getPropString(order, Order.MERCHANT_ID), sn);
            if(null == transaction) {
                throw e;
            }
            Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
            if(!(BeanUtil.getPropString(configSnapshot, TransactionParam.TERMINAL_SN).equals(terminalSn) 
                    || BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_SN).equals(wosaiStoreId))) {
                throw e;
            }
            basicParams = CollectionUtil.hashMap(
                    Order.MERCHANT_ID, BeanUtil.getPropString(order, Order.MERCHANT_ID),
                    Order.STORE_ID, BeanUtil.getPropString(order, Order.STORE_ID),
                    Order.TERMINAL_ID, BeanUtil.getPropString(order, Order.TERMINAL_ID)
            );
        }
        
        // 商户限流
        if(basicParams.containsKey(TransactionParam.MERCHANT_SN)) {
            RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_CANCEL, Boolean.FALSE);
        }

        String merchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(basicParams, TransactionParam.STORE_ID);
        String terminalId = MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID);
        Map<String, Object> order;
        try {
            order = getOrder(sn, clientSn, merchantId, storeId);
        } catch (OrderNotExistsException ex) {
            throw ex;
        }

        //如果订单不存在，判断是否允许预撤单
        if (MapUtil.isEmpty(order)) {
            boolean canPreCancel = doPreCancel(basicParams, clientSn, merchantId, storeId, terminalId);
            if (canPreCancel) {
                return preCancelResponse(request);
            }
        }

        if (MapUtil.isEmpty(order)) {
            Map<String, Object> csbRequest = csbCompatibilityService.queryCSBRequest(clientSn, sn, StringUtil.empty(terminalSn) ? wosaiStoreId : terminalSn);
            if (MapUtil.isEmpty(csbRequest)) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            Map<String,Object> response = csbCompatibilityService.mockCancelResponse(csbRequest, basicParams);
            csbCompatibilityService.removeCSBRequestCacheWhenCancel(csbRequest);
            return response;
        }

        String cancelLimit = MapUtil.getString(extended, Transaction.SQB_CANCEL_LIMIT);
        int orderStatus = MapUtil.getIntValue(order, Order.STATUS);
        if (CANCEL_LIMIT_EXISTS.equals(cancelLimit)) {
            //已创建存在的订单不允许撤单
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_CREATED_CAN_NOT_CANCEL, UpayErrorScenesConstant.ORDER_CREATED_CAN_NOT_CANCEL_MESSAGE);
        }

        if (CANCEL_LIMIT_PAID.equals(cancelLimit) && Order.STATUS_PAID == orderStatus) {
            //已支付的订单不允许撤单
            throw new UpayBizException(UpayErrorScenesConstant.ORDER_PAID_CAN_NOT_CANCEL, UpayErrorScenesConstant.ORDER_PAID_CAN_NOT_CANCEL_MESSAGE);
        }

        TransactionContext context = cancelProcessor.abortOrReverse(order, cancelType, extended, reflect, basicParams);

        context.waitUntilShouldReturn(5000);
        
        int status = BeanUtil.getPropInt(context.getTransaction(), Transaction.STATUS);
        int type = BeanUtil.getPropInt(context.getTransaction(), Transaction.TYPE);
        if (type == Transaction.TYPE_PAYMENT && status == Transaction.STATUS_SUCCESS) {
            context = cancelProcessor.abortOrReverse(order, cancelType, extended, reflect, basicParams);
            context.waitUntilShouldReturn(5000);
        }
        handleContextException(context);
        return cancelResponse(context, !BeanUtil.getPropBoolean(basicParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
    }

    @SuppressWarnings("unchecked")
    @Override
    public DeferredResult<Map<String, Object>> precreate(Map<String, Object> request, HttpServletResponse httpResponse) {
        boolean wapIsFromCsb = csbCompatibilityService.wapRequestIsOriginalC2bRequest(request);
        if(wapIsFromCsb){
            request = csbCompatibilityService.mergeOriginalC2bRequest(request);
        }

        boolean isDeposit =  BeanUtil.getPropBoolean(request, DepositService.IS_DEPOSIT, false);
        String terminalSn= (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String clientSn = (String)request.get(CLIENT_SN);
        String totalAmount = (String)request.get(TOTAL_AMOUNT);
        String clientIp = (String)request.get(CLIENT_IP);
        String payway = (String)request.get(PAYWAY);
        String subPayway = (String)request.get(SUB_PAYWAY);
        String subject = (String)request.get(SUBJECT);
        String body = (String)request.get(DESCRIPTION);
        String operator = (String)request.get(OPERATOR);
        String tradeApp = MapUtil.getString(request, TRADE_APP);
        int paywayCode = Integer.parseInt(payway);
        int subPaywayCode = (subPayway==null? Order.SUB_PAYWAY_QRCODE: Integer.parseInt(subPayway));
        List<Map<String,Object>> goodsDetails = (List<Map<String, Object>>) request.get(GOODS_DETAILS);
        Map<String,Object> extended = UpayUtil.formatExtended(request.get(EXTENDED), om);
        Map<String,Object> profitSharing = UpayUtil.formatExtended(request.get(PROFIT_SHARING), om);
        Object reflect = encryptReflect(request.get(REFLECT), isDeposit);
        String payerUid = (String)request.get(PAYER_UID);
        String notifyUrl = (String)request.get(NOTIFY_URL);
        Map<String, Object> extraParams = CollectionUtil.hashMap();
        processRequestPoi(request, extraParams);
        if (payerUid != null) {
            extraParams.put(Transaction.PAYER_UID, payerUid);
        }
        if (notifyUrl != null) {
            extraParams.put(Transaction.NOTIFY_URL, notifyUrl);
        }
        if (!StringUtils.empty(clientIp)){
            if(clientIp.indexOf(",") > 0) {
                clientIp = clientIp.split(",")[0];
            }
            extraParams.put(Transaction.CLIENT_IP, clientIp);
        }
        String bizModel = MapUtil.getString(extended, Transaction.SQB_BIZ_MODEL);
        boolean csbNeedToWap = csbCompatibilityService.csbRequestIsNeedCsbCompatibleProcess(request, null);
        Map<String, Object> config = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, paywayCode, csbNeedToWap ? Order.SUB_PAYWAY_WAP : subPaywayCode, tradeApp, bizModel);
        if (!csbNeedToWap) {
            csbNeedToWap = csbCompatibilityService.csbRequestIsNeedCsbCompatibleProcess(request, config);
            if (csbNeedToWap) {
                config = facade.getAllParamsWithTradeApp(wosaiStoreId, terminalSn, paywayCode, Order.SUB_PAYWAY_WAP, tradeApp, bizModel);
            }
        }
        Map<String,Object> requestCopy = null;
        if(csbNeedToWap){
            request.put(UpayService.SUB_PAYWAY, Order.SUB_PAYWAY_WAP + "");
            requestCopy = JSON.parseObject(JSON.toJSONString(request));
        }

        setExtraParam(extended,extraParams);

        Map<String, Object> finalConfig = config;
        extended = UpayUtil.getMergedExtendedWithGoodsDetails(goodsDetails, extended, paywayCode, config, Transaction.TYPE_PAYMENT);

        long total = Long.parseLong(totalAmount);

        //判断商户是否支持预撤单， 如果支持，判断此笔订单是否已经预撤销，如果已撤销，返回支付失败
        verifyPayPreCancel(config, clientSn);

        // 商户限流
        RateLimiterUtil.verification(BeanUtil.getPropString(config, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_PRECREATE, Boolean.FALSE);
        // 改写商品名称
        subject = tradeAppFacade.getNewSubject(tradeApp, bizModel, subject, MapUtil.getString(config, TransactionParam.STORE_NAME));
        // 校验trade_app 相关的传参是否合法
        tradeAppFacade.checkTradeApp(tradeApp, extraParams);
        String sqbGenSn = BeanUtil.getPropString(extraParams, Transaction.SQB_GEN_SN);
        String sn = wapIsFromCsb ? BeanUtil.getPropString(extraParams, Transaction.SQB_CSB_TO_WAP_SN)
                : isUseClientSn(config, sqbGenSn) ? sqbGenSn : tsnGenerator.nextSn();
        // c2b 转 wap
        if (csbNeedToWap) {
            return csbCompatibilityService.processCSBRequestAndMockResponse(sn, requestCopy, config);
        }

        String orderId = UpayUtil.getOrderIdBySn(sn);
        Map<String, Object> order = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                                                           DaoConstants.CTIME, new Date().getTime(),
                                                           Order.SN, sn,
                                                           Order.CLIENT_SN, clientSn,
                                                           Order.SUBJECT, subject,
                                                           Order.BODY , body,
                                                           Order.STATUS, !isDeposit ? Order.STATUS_CREATED : Order.STATUS_DEPOSIT_CREATED,
                                                           Order.TCP_MODIFIED, false,
                                                           Order.ORIGINAL_TOTAL, total,
                                                           Order.NET_ORIGINAL, total,
                                                           Order.EFFECTIVE_TOTAL, total,
                                                           Order.NET_EFFECTIVE, total,
                                                           Order.PAYWAY, paywayCode,
                                                           Order.SUB_PAYWAY, subPaywayCode,
                                                           Order.MERCHANT_ID, config.get(TransactionParam.MERCHANT_ID),
                                                           Order.STORE_ID, config.get(TransactionParam.STORE_ID),
                                                           Order.TERMINAL_ID, config.get(TransactionParam.TERMINAL_ID),
                                                           Order.OPERATOR, operator,
                                                           Order.REFLECT, reflect);
        // 保存交易流水
        String transactionId = UpayUtil.getTransactionIdBySn(sn);

        Map<String, Object> transaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                                                                 Transaction.TSN, sn,
                                                                 Transaction.CLIENT_TSN, clientSn,
                                                                 Transaction.TYPE, !isDeposit ? Transaction.TYPE_PAYMENT : Transaction.TYPE_DEPOSIT_FREEZE,
                                                                 Transaction.SUBJECT, subject,
                                                                 Transaction.BODY, body,
                                                                 Transaction.STATUS, Transaction.STATUS_CREATED,
                                                                 Transaction.ORIGINAL_AMOUNT, total,
                                                                 Transaction.EFFECTIVE_AMOUNT, total,
                                                                 Transaction.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                 Transaction.STORE_ID, order.get(Order.STORE_ID),
                                                                 Transaction.TERMINAL_ID, order.get(Order.TERMINAL_ID),
                                                                 Transaction.OPERATOR, order.get(Order.OPERATOR),
                                                                 Transaction.PAYWAY, paywayCode,
                                                                 Transaction.SUB_PAYWAY, subPaywayCode,
                                                                 Transaction.ORDER_ID, orderId,
                                                                 Transaction.ORDER_SN, sn,
                                                                 Transaction.EXTRA_PARAMS, extraParams,
                                                                 Transaction.EXTENDED_PARAMS, extended,
                                                                 Transaction.REFLECT, reflect,
                                                                 Transaction.CONFIG_SNAPSHOT, config);
        if(isDeposit) {
            transaction.put(Transaction.EXTRA_OUT_FIELDS, CollectionUtil.hashMap(Transaction.IS_DEPOSIT, true));
        }
        
        MpayServiceProvider provider = workflowManager.matchServiceProvider(transaction);
        // serviceProvider 非空校验
        validateServiceProvider(provider);

        // 由于银联二维码的交易费率和额度相关，目前我们系统不支持按单笔交易金额设置费率，因此需要暂时对银联二维码单笔交易做限额，以确保费率统一。

        //云闪付大额交易验证
        providerMetaFacade.unionPayLargeTradeVerify(provider, transaction);

        // 预授权权限校验
        setDepositValidate(isDeposit, paywayCode, transaction, provider);

        //重写支付宝payway
        updatePaywayIfIsAlipay(order, transaction, provider);

        //校验交易是否超限等
        validate(transaction, provider);
        
        //商户重复订单号校验，部分商户允许重复订单号
        updateDuplicateClientSn(config, order, transaction);

        //解析收钱吧红包
        Boolean activityDiscount = MapUtil.getBoolean(extraParams, Transaction.SQB_ACTIVITY_DISCOUNT);
        consultSQBDiscounts(config, order, transaction, terminalSn, paywayCode, subPaywayCode, total, payerUid, null, provider, goodsDetails, isDeposit, activityDiscount);


        //提前设置激活的是哪套交易参数以及设置provider以及手续费
        useTradeParamAndSetProvider(transaction, order, provider);

        //设置分账信息
        setProfitSharing(facade, transaction, profitSharing, tradeApp, isDeposit);

        //生成产品标识
        genProductFlagAndComboId(transaction, provider);

        // 通道限流
        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), subPaywayCode);

        //保证order, transaction的ctime都一样，以便数据根据时间迁移后，订单与流水能在相同的时间段内。
        updateOrderAndTransactionTheSameTime(order, transaction);

        repository.save(order, transaction);

        String terminalOrStoreSn = (terminalSn != null? terminalSn: wosaiStoreId);
        DeferredResult<Map<String,Object>> response = new DeferredResult<>();
        ConsumerWrapper<TransactionContext> returnConsumer = ConsumerWrapper.of(context -> {
            if(response.isSetOrExpired()){
                return;
            }
            try{
                handleContextException(context);
                Map<String,Object> result = precreateResponse(context, !BeanUtil.getPropBoolean(finalConfig, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
                httpResponse.addCookie(new Cookie(Transaction.ORDER_SN, sn));
                // 预授权交易需要变更响应中的result_code
                if(result != null && isDeposit){
                    Map bizResponse = (Map) BeanUtil.getProperty(result, CommonResponse.BIZ_RESPONSE);
                    if(null != bizResponse) {
                        bizResponse.put(CommonResponse.RESULT_CODE, DepositController.PAY_DEPOSIT_RESPONSE_MAPPING.get(bizResponse.get(CommonResponse.RESULT_CODE)));
                    }
                }
                response.setResult(result);
            }catch (Exception e){
                response.setResult(UpayServiceMethodInterceptor.handleException(e));
            }
        });
        TransactionContext context = workflowManager.startWorkflow((String)request.get(API_VER), terminalOrStoreSn, order, transaction, provider, returnConsumer);
        return response;
    }

    @Override
    public Map<String, Object> refund(Map<String, Object> request) {
        String terminalSn = (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        String clientSn= (String)request.get(CLIENT_SN);
        String sn = (String)request.get(SN);
        String refundRequestNo = (String)request.get(REFUND_REQUEST_NO);
        String refundAmount = (String)request.get(REFUND_AMOUNT);
        String operator = (String)request.get(OPERATOR);
        List<Map<String,Object>> goodsDetails = (List<Map<String, Object>>) request.get(GOODS_DETAILS);
        Map<String,Object> extended = UpayUtil.formatExtended(request.get(EXTENDED), om);
        Object reflect = request.get(REFLECT);
        Map<String,Object> refundProfitSharing = (Map<String, Object>) request.get(PROFIT_SHARING);
        
        Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        // 商户限流
        RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_REFUND, Boolean.FALSE);
        
        TransactionContext context = refundProcessor.refund(request, operator, sn, clientSn, refundRequestNo, refundAmount, extended, goodsDetails, reflect, basicParams, refundProfitSharing);
        context.waitUntilReturn(5000);
        handleContextException(context);
        return refundResponse(context, !BeanUtil.getPropBoolean(basicParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
    }

    @Override
    public Map<String, Object> fix( Map<String, Object> request) {
        String sn = (String)request.get(SN);
        String terminalSn= (String)request.get(TERMINAL_SN);
        String wosaiStoreId = (String)request.get(WOSAI_STORE_ID);
        boolean isManualFix = BeanUtil.getPropBoolean(request, FLAG_IS_MANUAL_FIX, false);

        String tradeNo = BeanUtil.getPropString(request, Transaction.TRADE_NO, "");
        String merchantId = null;
        if(!StringUtil.empty(terminalSn) || !StringUtil.empty(wosaiStoreId)){
            try{
                Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
                merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
            }catch (Exception e){
                //ignore exception
                logger.warn("get basic params error when fix order: " + e.getMessage(), e);
            }
        }
        TransactionContext context = fixProcessor.fixPay(sn, merchantId, tradeNo, isManualFix);

        return fixResponse(context, true);
    }

    @Override
    public Map<String, Object> refundRevoke(Map<String, Object> request) {
        String sn = (String)request.get(SN);
        String tsn = (String)request.get(TSN);
        TransactionContext context = fixProcessor.refundRevoke(sn, tsn);
        return fixResponse(context, true);
    }

    @Override
    public Map<String, Object> fixCancelOrRefund(Map<String, Object> request) {
        String sn = (String)request.get(SN);
        String tsn = (String)request.get(TSN);
        Long realTradeFee = MapUtil.getLong(request, REAL_TRADE_FEE);
        List<Map<String,Object>> refundChannelPayments = (List<Map<String, Object>>) request.get(REFUND_CHANNEL_PAYMENTS);
        TransactionContext context = fixProcessor.fixCancelOrRefundToSuccess(sn, tsn, realTradeFee, refundChannelPayments);
        return fixResponse(context, true);
    }

    @Override
    public Map<String, Object> fixOrderStatusIfRefundNotSuccess(Map<String, Object> request) {
        String sn = (String)request.get(SN);
        Map<String, Object> order = fixProcessor.fixOrderStatusIfRefundNotSuccess(sn);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, order));
    }

    @Override
    public Map<String, Object> fixOrderStatusToPaidIfCancelNotSuccess(Map<String, Object> request) {
        String sn = (String)request.get(SN);
        Map<String,Object> order = repository.getOrderByOrderSn(null, sn);
        if(order == null){
        	throw new OrderNotExistsException(UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST, UpayErrorScenesConstant.CANCEL_ORDER_NOT_EXIST_MESSAGE);
        }
        if(Order.STATUS_CANCEL_ERROR != BeanUtil.getPropInt(order, Order.STATUS)){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_TO_PAID_ORDER_STATUS_NOT_MATCH, UpayErrorScenesConstant.FIX_ORDER_STATUS_TO_PAID_ORDER_STATUS_NOT_MATCH_MESSAGE);
        }
        String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        //撤单失败状态的订单之前应该是支付成功的。在此做一下检查，避免出现资金损失。
        Map<String,Object> payTransaction = repository.getPayTransactionByOrderSn(merchantId, sn);
        if(BeanUtil.getPropInt(payTransaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS){
            throw new UpayBizException("订单状态错误，不支持勾兑，请检查");
        }
        if(BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) != BeanUtil.getPropLong(order, Order.NET_ORIGINAL) ){
            throw new UpayBizException(UpayErrorScenesConstant.FIX_ORDER_STATUS_TO_PAID_ORDER_AMOUNT_NOT_MATCH, UpayErrorScenesConstant.FIX_ORDER_STATUS_TO_PAID_ORDER_AMOUNT_NOT_MATCH_MESSAGE);
        }
        try {
            repository.getOrderDao().updatePart(
                    CollectionUtil.hashMap(
                            DaoConstants.ID, BeanUtil.getPropString(order, DaoConstants.ID),
                            Order.MERCHANT_ID, merchantId,
                            Order.STATUS, Order.STATUS_PAID,
                           DaoConstants.VERSION, order.get(DaoConstants.VERSION)
                    )
                 );
        }catch (DaoVersionMismatchException e) {
            throw new UpayBizException("订单已发生变更，处理失败，请检查");
        }
        order = repository.getOrderByOrderSn(merchantId, sn);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CommonResponse.SUCCESS, null, null, null, order));
    }

    @Override
    public void notify(String upayOrderNumber, Map<String, Object> notification) {
        TransactionContext context = workflowManager.lookupTransactionContext(upayOrderNumber);
        if(context == null){
            //处理客户端通知失败原因
            if (notification.containsKey(UpayConstant.CLIENT_NOTIFY_STATUS)) {
                updateTransactionWithProviderErrorInfo(notification);
            }
            return;
        }
        if(!context.isFakeRequest() && FakeRequestUtil.isFakeRequest()){
            //为了资金安全, 如果原交易为真实请求，那么回调不允许为压测请求
            return;
        }
        String result;
        try {
            notification.put(TransactionContext.class.getName(), context);
            if (depositProvider.sqbDeposit(context)) {
                //收钱吧预授权不做处理，防止多线程并发改写
                return;
            } else {
                MpayServiceProvider provider = context.getServiceProvider();
                if (notification.containsKey(UpayConstant.CLIENT_NOTIFY_STATUS)) {
                    //通知网关交易状态后，网关需去通道查询一次结果
                    String notifyStatus = MapUtil.getString(notification, UpayConstant.CLIENT_NOTIFY_STATUS);
                    if (CommonResponse.IN_PROCESS.equals(notifyStatus)) {
                        //支付中状态，通常用来传递支付过程中产生的数据
                        result = provider.explainNotification(notification);
                    } else {
                        result = provider.query(context);
                    }

                    //若通知交易状态为失败或进行中, 且查询结果非成功，即将结果置为失败，后续走勾兑流程
                    if (CommonResponse.FAIL.equals(notifyStatus) && !Workflow.RC_PAY_SUCCESS.equals(result)) {
                        result = Workflow.RC_ERROR;
                        //设置异常信息
                        setTransactionContextErrorInfo(context, notification, provider);
                    }
                } else {
                    result = provider.explainNotification(notification);
                }
            }
            context = workflowManager.lookupTransactionContext(upayOrderNumber);
            if (context != null && result != null) {
                workflowManager.raise(context, result);
            }
        } finally {
            notification.remove(TransactionContext.class.getName());
        }
    }

    private void updateTransactionWithProviderErrorInfo(Map notification) {
        //通过orderSn获取订单信息
        String orderSn = MapUtil.getString(notification, Transaction.ORDER_SN);
        Map<String, Object> order = repository.getOrderByOrderSn( null,orderSn);
        if (Objects.isNull(order)) {
            return;
        }
        //通知超过订单创建后10分钟
        long ctime = BeanUtil.getPropLong(order, DaoConstants.CTIME);
        if(System.currentTimeMillis() - ctime > 1000 * 60 * 10) {
            return;
        }
        //获取交易信息
        String merchantId = MapUtil.getString(order, Order.MERCHANT_ID);
        Map transaction = repository.getPayTransactionByOrderSn(merchantId, orderSn);
        if (Objects.isNull(transaction)) {
            return;
        }
        //处理客户端通知失败原因
        String notifyMsg = MapUtil.getString(notification, UpayConstant.CLIENT_NOTIFY_MSG);
        String notifyStatus = MapUtil.getString(notification, CLIENT_NOTIFY_STATUS);
        Map<String, Object> map = new LinkedHashMap<>();
        map.put(UpayConstant.CLIENT_NOTIFY_MSG, notifyMsg);
        map.put(CLIENT_NOTIFY_STATUS, notifyStatus);
        String op = MpayServiceProvider.OP_PRECREATE;
        //更新交易表provider_error_info字段
        Map<String, Object> providerErrorInfo = MapUtil.getMap(transaction, Transaction.PROVIDER_ERROR_INFO);
        if (providerErrorInfo == null) {
            providerErrorInfo = new HashMap<>();
            transaction.put(Transaction.PROVIDER_ERROR_INFO, providerErrorInfo);
        }
        providerErrorInfo.put(op, map);
        //持久化数据库
        repository.getTransactionDao().updatePart(transaction);
    }

    private static void handleContextException(TransactionContext context) {
        if (context.getException() instanceof UpaySystemError) {
            throw (UpaySystemError)context.getException();
        }
    }
    private static Map<String, Object> payResponse(TransactionContext context, boolean returnPayerInfo) {
        Map<String, Object> transaction = context.getTransaction();
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(context, returnPayerInfo);
        data.put(PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(context.getOrder(), context.getTransaction()));
        if (transactionStatus == Transaction.STATUS_SUCCESS) {
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PAY_SUCCESS, null, null, null, data));

        }else if (transactionStatus == Transaction.STATUS_FAIL_CANCELED) {
            UpayBizError error = getBizError(transaction, MpayServiceProvider.OP_PAY);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PAY_FAIL, error.getName(), error.getStandardName(), error.getMessage(), data));

        }else if(transactionStatus == Transaction.STATUS_IN_PROG || context.isForceReturn()){
            // 对于forceReturn的交易，其流水状态可能仍处于CREATED状态，需要转换成IN_PROG
            data.put(Transaction.STATUS, StateLabel.fromId(Transaction.STATUS_IN_PROG).getName());
            String errStandardName = null;
            String errMessage = null;
            if (BeanUtil.getNestedProperty(transaction,Transaction.BIZ_ERROR_CODE + "."+ MpayServiceProvider.OP_PAY) !=null){
                UpayBizError  error = UpayUtil.getBizError(transaction, MpayServiceProvider.OP_PAY);
                errStandardName = error.getStandardName();
            }else{
                Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(UpayErrorScenesConstant.ORDER_IN_PROG, null);
                errStandardName = BeanUtil.getPropString(configInfo, ApolloConfigurationCenterUtil.ERROR_CODE_STANDARD);
            }
            //为了防止第三方直接根据error_code来判断订单状态，in_prog的订单，error_code 设置为null
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PAY_IN_PROGRESS, null, errStandardName, errMessage, data));
        }
        else if (transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_PRE_SUCCESS) {
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PAY_IN_PROGRESS, null, null, null, data));

        }else if (transactionStatus == Transaction.STATUS_ERROR_RECOVERY) {
            UpayBizError error = getBizError(transaction, MpayServiceProvider.OP_PAY);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PAY_FAIL_IN_PROGRESS, error.getName(), error.getStandardName(), error.getMessage(), data));
        }else {
            UpayBizError error = getBizError(transaction, MpayServiceProvider.OP_PAY);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PAY_FAIL_ERROR, error.getName(), error.getStandardName(), error.getMessage(), data));
        }
    }
    
    private static Map<String, Object> cancelResponse(TransactionContext context, boolean returnPayerInfo) {
        Map<String, Object> transaction = context.getTransaction();
        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(context, returnPayerInfo);

        if (transactionType == Transaction.TYPE_PAYMENT) {
            if (transactionStatus == Transaction.STATUS_FAIL_CANCELED || transactionStatus == Transaction.STATUS_ABORTED) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_ABORT_SUCCESS, null, null, null, data));
                                                                     
            }else if (transactionStatus == Transaction.STATUS_IN_PROG || transactionStatus == Transaction.STATUS_ERROR_RECOVERY || transactionStatus == Transaction.STATUS_ABORTING) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_ABORT_IN_PROGRESS, null, null, null, data));

            }else if (transactionStatus == Transaction.STATUS_SUCCESS || transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_PRE_SUCCESS){
                throw new WorkflowInvalidState(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);

            }else {
                UpayBizError error = getBizError(transaction, MpayServiceProvider.OP_CANCEL);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_ABORT_ERROR, error.getName(), error.getStandardName(), error.getMessage(), data));
            }
        }else if (transactionType == Transaction.TYPE_CANCEL) {
            if (transactionStatus == Transaction.STATUS_SUCCESS) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_SUCCESS, null, null, null, data));
            }else if (transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_IN_PROG || transactionStatus == Transaction.STATUS_PRE_SUCCESS) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_IN_PROGRESS, null, null, null, data));
            }else{
                UpayBizError error = getBizError(transaction, MpayServiceProvider.OP_CANCEL);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_ERROR, error.getName(), error.getStandardName(), error.getMessage(), data));
            }
        }else {
            throw new WorkflowInvalidState(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
    }

    private Map<String, Object> preCancelResponse(Map<String, Object> request) {
        String clientSn = MapUtil.getString(request, CLIENT_SN);
        String terminalSn = MapUtil.getString(request, TERMINAL_SN);
        Map<String, Object> data = MapUtil.hashMap(Order.CLIENT_SN, clientSn);
        Map<String,Object> extended = UpayUtil.formatExtended(request.get(EXTENDED), om);
        String notifyUrl = MapUtil.getString(extended, Transaction.SQB_CANCEL_NOTIFY_URL);
        String cancelReason = MapUtil.getString(extended, Transaction.SQB_CANCEL_REASON);
        Map<String, Object> notification = MapUtil.hashMap(TERMINAL_SN, terminalSn, CancelResponse.OUT_TRADE_NO, clientSn,
                CancelResponse.OUT_TRADE_NO, clientSn, CancelResponse.CANCEL_REASON, cancelReason, CancelResponse.CANCEL_TIME, String.valueOf(System.currentTimeMillis()));
        clientNotifier.notify(notifyUrl , "", notification);
        return UpayUtil.apiSuccess(UpayUtil.bizResponse(CancelResponse.RESULT_CODE_CANCEL_SUCCESS, null, null, null, data));
    }

    private static Map<String, Object> refundResponse(TransactionContext context, boolean returnPayerInfo) {
        Map<String, Object> transaction = context.getTransaction();
        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(context, returnPayerInfo);
        data.put(PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(context.getOrder(), context.getTransaction()));
        if (transactionType == Transaction.TYPE_REFUND) {
            if (transactionStatus == Transaction.STATUS_SUCCESS) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(RefundResponse.RESULT_CODE_REFUND_SUCCESS, null, null, null, data));
            }else if (transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_IN_PROG || transactionStatus == Transaction.STATUS_PRE_SUCCESS) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(RefundResponse.RESULT_CODE_REFUND_IN_PROGRESS, null, null, null, data));
            }else{
                UpayBizError error = getBizError(transaction, MpayServiceProvider.OP_REFUND);
                String resultCode = UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(error.getStandardName()) ? RefundResponse.RESULT_CODE_REFUND_ERROR : RefundResponse.RESULT_CODE_REFUND_FAIL;
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(resultCode, error.getName(), error.getStandardName(), error.getMessage(), data));
            }
        }else {
            throw new WorkflowInvalidState(UpayErrorScenesConstant.REFUND_FAIL, UpayErrorScenesConstant.REFUND_FAIL_MESSAGE);
        }
    }
    
    private static Map<String, Object> fixResponse(TransactionContext context, boolean returnPayerInfo) {
        return queryResponse(context.getOrder(), context.getTransaction(), returnPayerInfo);
    }

    private Map<String, Object> precreateResponse(TransactionContext context, boolean returnPayerInfo) {
        Map<String, Object> transaction = context.getTransaction();
        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(context, returnPayerInfo);
        data.put(PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(context.getOrder(), context.getTransaction()));
        String qrCode = BeanUtil.getPropString(transaction, TRANSACTION_QRCODE);
        Object wapPayRequest = BeanUtil.getNestedProperty(transaction, TRANSACTION_WAP_PAY_REQUEST);
        if (qrCode != null) {
            data.put(PayResponse.QR_CODE, qrCode);
            data.put(PayResponse.QR_CODE_IMAGE_URL, qrcodeImaging.getQrcodeImageUrl(qrCode));;
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PRECREATE_SUCCESS, null, null, null, data));
        }else if (wapPayRequest != null) {
            data.put(PayResponse.WAP_PAY_REQUEST, wapPayRequest);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PRECREATE_SUCCESS, null, null, null, data));
        }else {
            String op = Transaction.TYPE_DEPOSIT_FREEZE == transactionType ? MpayServiceProvider.OP_DEPOSIT_PREFREEZE : MpayServiceProvider.OP_PRECREATE;
            UpayBizError error = UpayUtil.getBizError(transaction, op);
            if (transactionStatus == Transaction.STATUS_FAIL_CANCELED) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PRECREATE_FAIL, error.getName(), error.getStandardName(), error.getMessage(), data));

            }else if (transactionStatus == Transaction.STATUS_FAIL_ERROR ||
                    transactionStatus == Transaction.STATUS_FAIL_IO_1 ||
                    transactionStatus == Transaction.STATUS_FAIL_PROTOCOL_1 || 
                    transactionStatus == Transaction.STATUS_FAIL_IO_3 ||
                    transactionStatus == Transaction.STATUS_FAIL_PROTOCOL_3 ) {

                return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PRECREATE_FAIL_ERROR, error.getName(), error.getStandardName(), error.getMessage(), data));

            }else if (transactionStatus == Transaction.STATUS_ERROR_RECOVERY) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse(PayResponse.RESULT_CODE_PRECREATE_FAIL_IN_PROGRESS, error.getName(), error.getStandardName(), error.getMessage(), data));

            }else {
            	throw new UpayBizException(UpayErrorScenesConstant.WORKFLOW_INVALID_STATE, UpayErrorScenesConstant.WORKFLOW_INVALID_STATE_MESSAGE);
            }
        }
    }

    public static UpayBizError getBizError(Map<String, Object> transaction, String op) {
        return getBizError(transaction, op, false);
    }

    public static UpayBizError getBizError(Map<String, Object> transaction, String op, boolean usePrecreate) {
        Object bizError = transaction.get(Transaction.BIZ_ERROR_CODE);
        if (bizError == null) {
        	return UpayBizError.fromCode(UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode());
        }else if (bizError instanceof Map){
            Object opBizError = BeanUtil.getProperty(bizError, op);
            if (usePrecreate && opBizError == null) {
                opBizError = BeanUtil.getProperty(bizError, MpayServiceProvider.OP_PRECREATE);
            }
            if(opBizError == null){
                return UpayBizError.fromCode(UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode());
            }else if(opBizError instanceof Map){
                return UpayBizError.fromMap((Map) opBizError);
            }else if(opBizError instanceof UpayBizError){
                return (UpayBizError) opBizError;
            }else{
            	try{
            		return UpayBizError.fromCode(BeanUtil.getPropInt(bizError, op, UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode()));
            	}catch (NumberFormatException e) {
            		return UpayBizError.fromCode(UpayBizError.UNEXPECTED_PROVIDER_ERROR.getCode());
				}
            }

        }else if (bizError instanceof Integer) {
            return UpayBizError.fromCode((Integer)bizError);
        } else {
            return UpayBizError.fromCode(Integer.valueOf(bizError+""));
        }
    }

    private static Map<String, Object> queryResponse(Map<String, Object> order, Map<String, Object> latestTransaction, boolean returnPayerInfo) {
        int transactionType = BeanUtil.getPropInt(latestTransaction, Transaction.TYPE);
        int transactionStatus = BeanUtil.getPropInt(latestTransaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(order, latestTransaction, returnPayerInfo);
        data.put(PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(order, latestTransaction));
        String op = UpayUtil.getOpByTransType(transactionType);
        if (Transaction.notFailed(transactionStatus)) {
            String errStandardName = null;
            String errMessage = null;
            if (transactionStatus == Transaction.STATUS_IN_PROG){
                if (BeanUtil.getNestedProperty(latestTransaction,Transaction.BIZ_ERROR_CODE + "."+ op) !=null){
                    UpayBizError  error = UpayUtil.getBizError(latestTransaction, op);
                    errStandardName = error.getStandardName();
                }else{
                        Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(UpayErrorScenesConstant.ORDER_IN_PROG, null);
                    errStandardName = BeanUtil.getPropString(configInfo, ApolloConfigurationCenterUtil.ERROR_CODE_STANDARD);
                }
            }
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_SUCCESS, null, errStandardName, errMessage, data));
        } else {

            // 交易正在状态机中处理，流水表状态已更新成失败，因此时流水表中的支付通道的异常信息和订单表状态还未入库，所以使用配置中心的默认返回码
        	if(BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_CREATED && MpayServiceProvider.OP_PAY.equals(op)){
                Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(UpayErrorScenesConstant.ORDER_PAY_FAILED_ERROR, null);
            	UpayBizError bizError = new UpayBizError(0, BeanUtil.getPropString(configInfo, ApolloConfigurationCenterUtil.ERROR_CODE, "EXTERNAL_SERVICE_EXCEPTION"),
            			BeanUtil.getPropString(configInfo, ApolloConfigurationCenterUtil.ERROR_CODE_STANDARD), 
            			BeanUtil.getPropString(configInfo, ApolloConfigurationCenterUtil.ERROR_MESSAGE, UpayErrorScenesConstant.ORDER_PAY_FAILED_ERROR_MESSAGE));
            	
            	BeanUtil.setNestedProperty(latestTransaction, UpayUtil.getBizErrorCodeKey(op), bizError);
        	}

            // 预下单取precreate结果
            boolean usePrecreate = false;
            if (transactionType == Transaction.TYPE_PAYMENT && MapUtil.getIntValue(order, Order.SUB_PAYWAY) != Order.SUB_PAYWAY_BARCODE) {
                usePrecreate = true;
            }
            UpayBizError error = getBizError(latestTransaction, op, usePrecreate);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse(QueryResponse.RESULT_CODE_SUCCESS, error.getName(), error.getStandardName(), error.getMessage(), data));
        }
    }



    /**
     * 设置哪一套交易参数被使用, 以及计算手续费  以及初始化provider的值
     * @param transaction
     * @param order
     */
    private void useTradeParamAndSetProvider(Map<String, Object> transaction, Map<String, Object> order, MpayServiceProvider provider){
        Map<String, Object> tradeParams = provider.getTradeParams(transaction);
        Integer providerCode = provider.getProvider();
        if (provider instanceof DefaultSwiftPassServiceProvider) {
            providerCode = ((DefaultSwiftPassServiceProvider)provider).getProvider(transaction);
        }
        if(tradeParams != null){
            if(null != BeanUtil.getProperty(tradeParams, TransactionParam.PARAMS_BANKCARD_FEE)){
                Map feeRateFlag = (Map) BeanUtil.getProperty(tradeParams, MerchantConfig.CHANNEL_FEE_RATE_TAG);
                if (MapUtil.isNotEmpty(feeRateFlag)) {
                    tradeParams.put(TransactionParam.FEE_RATE_TAG, feeRateFlag);
                    logger.info("ordersn = {}, channel_fee_rate_tag = {}", BeanUtil.getPropString(transaction, Transaction.ORDER_SN), feeRateFlag);
                }
            } else if(null != BeanUtil.getProperty(tradeParams, TransactionParam.LADDER_FEE_RATES)){
                long originalTotal = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
                String feeRate = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE);
                Map feeRateFlag = (Map) BeanUtil.getProperty(tradeParams, TransactionParam.FEE_RATE_TAG);
                long min = 0;
                long max = 0;
                for (Map config : (List<Map>)BeanUtil.getProperty(tradeParams, TransactionParam.LADDER_FEE_RATES)) {
                    min = Math.round(MapUtil.getDouble(config, TransactionParam.LADDER_FEE_RATE_MIN, 0.00) * 100) ;
                    max = Math.round(MapUtil.getDouble(config, TransactionParam.LADDER_FEE_RATE_MAX, 0.00) * 100);
                    max = (0 == max) ? Long.MAX_VALUE : max;
                    if(originalTotal > min && originalTotal <= max){
                        feeRate = BeanUtil.getPropString(config, TransactionParam.FEE_RATE);
                        feeRateFlag = (Map) BeanUtil.getProperty(tradeParams, TransactionParam.LADDER_FEE_RATE_TAG);
                    }
                }
                tradeParams.put(TransactionParam.FEE_RATE_ORIGINAL, BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE));
                tradeParams.put(TransactionParam.FEE_RATE, feeRate);
                if (MapUtil.isNotEmpty(feeRateFlag)) {
                    tradeParams.put(TransactionParam.FEE_RATE_TAG, feeRateFlag);
                }
                logger.info("ordersn = {}, ladder_fee_rates = {}", BeanUtil.getPropString(transaction, Transaction.ORDER_SN), BeanUtil.getProperty(tradeParams, TransactionParam.LADDER_FEE_RATES));
                tradeParams.remove(TransactionParam.LADDER_FEE_RATES);
            }
            //设置资金渠道或资金渠道阶梯费率为最大值
            resetChannelOrChannelLadderFeeToMax(tradeParams, transaction, providerCode);
            //处理智慧门店收费试点费率上浮逻辑
            processSmartServiceFeeUp(transaction, tradeParams);
            //优惠额度费率
            feeRateProcessor.quotaFeeRate(tradeParams, transaction, providerCode);
            tradeParams.put(TransactionParam.ACTIVE, true);
            long fee = FeeUtil.calculatePayOrPrecreateFee(transaction);
            if(fee < 0){
                logger.error("手续费小于0 fee: {}, transaction: {}", fee, transaction);
                throw new UpayBizException(UpayErrorScenesConstant.VALIDATION_EXCEPTION_CONFIG_ERROR, UpayErrorScenesConstant.VALIDATION_EXCEPTION_CONFIG_ERROR_MESSAGE);
            }
            tradeParams.put(TransactionParam.FEE, fee);
        }
        transaction.put(Transaction.PROVIDER, providerCode);
        order.put(Order.PROVIDER, providerCode);
    }

    /**
     * 如果是alipay v1，则根据交易参数，如果是走支付宝2.0接口，则同步修改订单，流水里面的payway为alipay2.0
     * @param order
     * @param transaction
     */
    private void updatePaywayIfIsAlipay(Map<String, Object> order, Map<String, Object> transaction, MpayServiceProvider serviceProvider){
        int payway = BeanUtil.getPropInt(order, Order.PAYWAY);
        if(payway == Order.PAYWAY_ALIPAY){
            if(!AlipayV1ServiceProvider.NAME.equals(serviceProvider.getName()) && !AlipayWapServiceProvider.NAME.equals(serviceProvider.getName())  && !AlipayOverseasServiceProvider.NAME.equals(serviceProvider.getName())){
                if(AlipayIntlServiceProvider.NAME.equals(serviceProvider.getName())) {
                    order.put(Order.PAYWAY, Order.PAYWAY_ALIPAY_INTL);
                    transaction.put(Order.PAYWAY, Order.PAYWAY_ALIPAY_INTL);
                    
                }else {
                    order.put(Order.PAYWAY, Order.PAYWAY_ALIPAY2);
                    transaction.put(Order.PAYWAY, Order.PAYWAY_ALIPAY2);
                    
                }
            }
        }
    }
    
    /**
     * 
     * 交易使用的provider不能为空
     * 
     * @param provider
     */
    private void validateServiceProvider(MpayServiceProvider provider) {
        if (provider == null) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE);
        }
    }
    

    /**
     * 校验交易是否超限
     * @param transaction
     */
    private void validate(Map<String, Object> transaction, MpayServiceProvider provider){
        //金额限额相关的校验
        tradingLimitFacade.checkTradingLimit(transaction, provider);

        //检测是否是非支付宝且传入花呗参数
        if (BeanUtil.getPropInt(transaction,Transaction.PAYWAY)!= Order.PAYWAY_ALIPAY2 && BeanUtil.getPropInt(transaction,Transaction.SUB_PAYWAY) == Order.SUB_PAYWAY_BARCODE) {
            Map extend = (Map) transaction.get(Transaction.EXTENDED_PARAMS);
            if (extend != null){
                if (extend.containsKey(BusinessV2Fields.EXTEND_PARAMS)) {
                    Object extendParamsObj = extend.get(BusinessV2Fields.EXTEND_PARAMS);
                    if (extendParamsObj instanceof Map) {
                        Map<String, Object> extendParams = (Map<String, Object>) extendParamsObj;
                        if (extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM) || extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT)) {
                            throw new UpayBizException(UpayErrorScenesConstant.HUABEI_QRCODE_TYPE_ERROE, UpayErrorScenesConstant.HUABEI_QRCODE_TYPE_ERRO_ERROR_MESSAGE);
                        }
                    }
                }
            }
        }else if(Order.PAYWAY_ALIPAY2 == BeanUtil.getPropInt(transaction,Transaction.PAYWAY)) {
            //检验花呗的参数信息是否正确
            Map extend = (Map) transaction.get(Transaction.EXTENDED_PARAMS);
            int fqType = 0;
            if (extend != null) {
                int hbfqNum = 0;
                if (extend.containsKey(BusinessV2Fields.EXTEND_PARAMS)) {
                    Object extendParamsObj = extend.get(BusinessV2Fields.EXTEND_PARAMS);
                    if (extendParamsObj instanceof Map) {
                        Map<String, Object> extendParams = (Map<String, Object>) extendParamsObj;
                        if (extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM)) {
                            try {
                                hbfqNum = Integer.parseInt(String.valueOf(extendParams.get(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM)));
                                fqType = UpayConstant.USE_HBFQ;
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_HUABEI_FQ_NUM_PARAM_ERROR_MESSAGE);
                            }
                        }
                        if (extendParams.containsKey(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT)) {
                            try {
                                Double.parseDouble(String.valueOf(extendParams.get(BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT)));
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_HUABEI_FQ_PERCENT_PARAM_ERROR_MESSAGE);
                            }
                        }
                        if (extendParams.containsKey(TransactionParam.FQ_NUM)) {
                            try {
                                hbfqNum = Integer.parseInt(String.valueOf(extendParams.get(TransactionParam.FQ_NUM)));
                                fqType = UpayConstant.USE_CREDIT;
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_NUM_PARAM_ERROR_MESSAGE);
                            }
                        }
                        if (extendParams.containsKey(TransactionParam.FQ_SELLER_PERCENT)) {
                            try {
                                Double.parseDouble(String.valueOf(extendParams.get(TransactionParam.FQ_SELLER_PERCENT)));
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_PERCENT_PARAM_ERROR_MESSAGE);
                            }
                        }

                    }
                    Object extraParamsObj = transaction.get(Transaction.EXTRA_PARAMS);
                    if(hbfqNum > 0 && extraParamsObj instanceof Map){
                        Map extraParams = (Map) extraParamsObj;
                        extraParams.put(Transaction.SQB_FQ_AMOUNT, MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT));
                        if(fqType != 0){
                            extraParams.put(Transaction.SQB_FQ_SERVICE_TYPE, fqType);
                        }
                        if(extraParams.containsKey(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE)) {
                            try {
                                Long.parseLong(String.valueOf(extraParams.get(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE)));
                                extraParams.putIfAbsent(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, extraParams.get(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE));
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_HUABEI_FQ_BUYER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE);
                            }
                        } else if (fqType == UpayConstant.USE_HBFQ && extend instanceof Map){
                            // 花呗分期码是从支付宝侧选择后跳转到门店码，商户如果将分期开关给关闭了，接口就不会返回分期信息，导致前端无法计算分期金额，此时要设置分期金额
                            Object businessParams = extend.get(BusinessV2Fields.BUSINESS_PARAMS);
                            if (businessParams instanceof Map && ((Map)businessParams).containsKey(BusinessV2Fields.FQ_SIGNATURE)) {
                                extraParams.putIfAbsent(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, FeeUtil.applyRate(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT), ApolloConfigurationCenterUtil.getSellerServiceChargeFee(hbfqNum, fqType)));
                                extraParams.putIfAbsent(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, extraParams.get(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE));
                            }
                        }
                        if(extraParams.containsKey(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE)) {
                            try {
                                Long.parseLong(String.valueOf(extraParams.get(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE)));
                                extraParams.putIfAbsent(Transaction.SQB_FQ_SELLER_SERVICE_CHARGE, extraParams.get(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE));
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_HUABEI_FQ_SELLER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE);
                            }
                        }
                        if(extraParams.containsKey(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE)) {
                            try {
                                Long.parseLong(String.valueOf(extraParams.get(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE)));
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_BUYER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE);
                            }
                        } else if (fqType == UpayConstant.USE_CREDIT && extend instanceof Map){
                            // 花呗分期码是从支付宝侧选择后跳转到门店码，商户如果将分期开关给关闭了，接口就不会返回分期信息，导致前端无法计算分期金额，此时要设置分期金额
                            Object businessParams = extend.get(BusinessV2Fields.BUSINESS_PARAMS);
                            if (businessParams instanceof Map && ((Map)businessParams).containsKey(BusinessV2Fields.FQ_SIGNATURE)) {
                                extraParams.putIfAbsent(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, extraParams.get(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE));
                            }
                        }
                        if(extraParams.containsKey(Transaction.SQB_FQ_SELLER_SERVICE_CHARGE)) {
                            try {
                                Long.parseLong(String.valueOf(extraParams.get(Transaction.SQB_FQ_SELLER_SERVICE_CHARGE)));
                            } catch (NumberFormatException ex) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_SELLER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE);
                            }
                        }
                    }
                }
                // 分期商户贴息参数校验
                if(extend.containsKey(BusinessV2Fields.BUSINESS_PARAMS)) {
                    Object businessParamsObj = extend.get(BusinessV2Fields.BUSINESS_PARAMS);
                    if (businessParamsObj instanceof Map) {
                        if("Y".equals(MapUtil.getString(((Map) businessParamsObj), BusinessV2Fields.ENABLE_THIRDPARTY_SUBSIDY))) {
                            if(!(provider instanceof DirectUnionPayAlipayV2ServiceProvider || provider instanceof DirectUnionPayAlipayV2WapServiceProvider
                                    || provider instanceof LklUnionPayAlipayV2ServiceProvider || provider instanceof LklUnionPayAlipayV2WapServiceProvider)) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_THIRDPARTY_SUBSIDY_ERROR_MESSAGE);
                            }
                            Map<String, Object> hbfqDiscount = MapUtil.getMap((Map)transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT);
                            if(MapUtil.isEmpty(hbfqDiscount)) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_THIRDPARTY_SUBSIDY_ERROR_MESSAGE);
                            }

                            long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
                            if(effectiveAmount < MapUtil.getLongValue(hbfqDiscount, TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT_MIN)
                                    || effectiveAmount > MapUtil.getLongValue(hbfqDiscount, TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT_MAX)
                                    || !Optional.ofNullable(MapUtil.getString(hbfqDiscount, TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT_NUMS)).orElse("").contains(hbfqNum + "")) {
                                throw new InvalidParamException(UpayErrorScenesConstant.UPAY_FQ_THIRDPARTY_SUBSIDY_ERROR_MESSAGE);
                            }
                        }
                    }
                }
            }
        }
    }






    private String getWeixinAppIdOfPayer(Map<String, Object> transaction, MpayServiceProvider provider){
        if(BeanUtil.getPropInt(transaction, Transaction.PAYWAY) != Order.PAYWAY_WEIXIN){
            return null;
        }else{
            if(provider != null){
                Map<String, Object> tradeParams = provider.getTradeParams(transaction);
                int subPayway = BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY);
                if(provider instanceof DirectWeixinWapOrMiniServiceProvider){
                    return ((DirectWeixinWapOrMiniServiceProvider)provider).isUserOfSubAppid(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), tradeParams, subPayway) ? BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID) : BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_APP_ID);
                }else{
                    String weixinSubAPPId = BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID);
                    if(!StringUtil.empty(weixinSubAPPId)) {
                        return weixinSubAPPId;
                    }else {
                        return BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_APP_ID);
                    }
                }
            }else{
                return null;
            }
        }

    }

    @Override
    public Map<String, Object> monitor() {
        Map info = new HashMap();
        //event bus
        if(eventBus != null){
            info.put("eventBus", eventBus.getInfo());
        }
        //workflow
        if(driver != null){
            info.put("workflowDriver", threadPoolManager.getInfo());
        }
        return info;
    }

    @Override
    public void updateFlags(Map<String,Object> flags) {
        if(flags == null){
            return;
        }
        if(flags.containsKey(FLAG_APPLY_TRADE_COPROCESSOR)){
            boolean applyTradeCoprocessor = BeanUtil.getPropBoolean(flags, FLAG_APPLY_TRADE_COPROCESSOR, false);
            logger.debug("change the applyTcp value to {}", applyTradeCoprocessor);
            ApolloConfigurationCenterUtil.setApplyTradeCoprocessor(applyTradeCoprocessor);

        }
    }

    @Override
    public DeferredResult<Map<String, Object>> preCreateQr(Map<String, Object> request) {
        return csbCompatibilityService.mockPrecreateResponse(null,request,null,1000 * 60 * 15,true);
    }

    private void updateDuplicateClientSn(Map<String, Object> config, Map<String, Object> order, Map<String, Object> transaction) {
    	int type = BeanUtil.getPropInt(config, TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER);
    	String clientSn = BeanUtil.getPropString(order, Order.CLIENT_SN);
    	if(type == TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER_ONE ){
    		Map<String, Object> lastOrder = repository.getOrderByClientSn(BeanUtil.getPropString(order, Order.MERCHANT_ID), clientSn);
    		if(null != lastOrder ){
    			int status = BeanUtil.getPropInt(lastOrder, Order.STATUS);
    			if(status == Order.STATUS_CREATED
    					|| status == Order.STATUS_CANCEL_INPROGRESS
    					|| status == Order.STATUS_REFUND_INPROGRESS){
    				throw new UpayBizException(UpayErrorScenesConstant.PAY_ORDER_AlEADY_IN_PROG, UpayErrorScenesConstant.PAY_ORDER_AlEADY_IN_PROG_MESSAGE);
    				
    			}else if(status == Order.STATUS_PAY_ERROR
    					|| status == Order.STATUS_PAY_CANCELED){
    				Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(lastOrder, DaoConstants.ID),
    						Order.MERCHANT_ID, BeanUtil.getPropString(order, Order.MERCHANT_ID),
    						Order.CLIENT_SN, clientSn + DateUtil.formatDate(new Date(BeanUtil.getPropLong(order, DaoConstants.CTIME)), "yyMMddHHmmss"));
    				repository.getOrderDao().updatePart(orderUpdate);
    				
    			}
    		}
    	}else if(type == TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER_INFINITE){
    		Map<String, Object> lastOrder = repository.getOrderByClientSn(BeanUtil.getPropString(order, Order.MERCHANT_ID), clientSn);
    		if(null != lastOrder){
    			Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(lastOrder, DaoConstants.ID),
    					Order.MERCHANT_ID, BeanUtil.getPropString(order, Order.MERCHANT_ID),
	                    Order.CLIENT_SN, clientSn + DateUtil.formatDate(new Date(BeanUtil.getPropLong(order, DaoConstants.CTIME)), "yyMMddHHmmss"));
	            repository.getOrderDao().updatePart(orderUpdate);
	            
    		}
    	} else {
            // mis特殊处理逻辑，当订单状态为非支付成功状态时，变更外部订单号
            String sqbScene = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS), Transaction.SQB_SCENE);
            if (!StringUtil.empty(sqbScene) && sqbScene.contains(SqbScene.MIS_PUSH_TRADE.getCode())) {
                String merchantId = MapUtil.getString(order, Order.MERCHANT_ID);
                Map<String, Object> lastOrder = repository.getOrderByClientSn(merchantId, clientSn);
                if (lastOrder != null) {
                    int status = MapUtil.getIntValue(lastOrder, Order.STATUS);
                    // 订单状态不为支付成功时，才进行变更
                    if (status == Order.STATUS_CREATED || status == Order.STATUS_PAY_ERROR || status == Order.STATUS_PAY_CANCELED) {
                        Map<String, Object> lastTransaction = repository.getPayTransactionByOrderSn(merchantId, MapUtil.getString(lastOrder, Order.SN));
                        String transactionSqbScene = MapUtil.getString(MapUtil.getMap(lastTransaction, Transaction.EXTRA_PARAMS), Transaction.SQB_SCENE);
                        // 流水中也存在mis_push_trade 时，才进行变更
                        if (MapUtil.getIntValue(lastTransaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS 
                                && !StringUtil.empty(transactionSqbScene) && transactionSqbScene.contains(SqbScene.MIS_PUSH_TRADE.getCode())) {
                            String newClientSn = org.apache.commons.lang3.StringUtils.rightPad(clientSn, 20, "").trim() + DateUtil.formatDate(new Date(BeanUtil.getPropLong(order, DaoConstants.CTIME)), "yyMMddHHmmss");
                            Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, MapUtil.getString(lastOrder, DaoConstants.ID),
                                    Order.MERCHANT_ID, merchantId,
                                    Order.CLIENT_SN, newClientSn);
                            repository.getOrderDao().updatePart(orderUpdate);
                        }
                    }
                }
            }
        }

	}

    /**
     * 从透传字段中拷贝收钱吧定义的透传参数存放到extra_params里面，这些字段不透传到支付通道
     * @param extend
     * @param extraParams
     */
    private void setExtraParam(Map extend, Map extraParams){
        if(extend == null) return;
        for(String key: Transaction.SQB_EXTENDED_KEYS){
            if(extend.containsKey(key)){
                extraParams.put(key, extend.get(key));
                extend.remove(key);
            }

        }
    }

    private String formatSqbPaySource(String paySource) {
        if (StringUtil.empty(paySource)) {
            return paySource;
        }
        if (paySource.startsWith("wx:")) {
            paySource = paySource.replace("wx:", "");
        }
        if (paySource.startsWith("my:")) {
            paySource = paySource.replace("my:", "");
        }
        return paySource;
    }

    /**
     * 设置分账信息，如果此订单不支持分账，则不记录分账信息到流水里面
     * @param transaction
     * @param profitSharing
     */
    public static  void setProfitSharing(ExternalServiceFacade facade, Map<String,Object> transaction, Map<String,Object> profitSharing, String tradeApp, boolean isDeposit){
        //收钱吧预授权
        if (isDeposit && Objects.equals(BeanUtil.getPropString(transaction, Transaction.KEY_DEPOSIT_TYPE), TransactionParam.DEPOSIT_SQB)) {
            return;
        }
        String sharingApp = MapUtil.getString(profitSharing, Transaction.PROFIT_SHARING_SHARING_APP, Transaction.SHARING_APP_PAY);
        if (Objects.equals(sharingApp, Transaction.SHARING_APP_PAY)) {
            setPayProfitSharing(facade, transaction, profitSharing, tradeApp, isDeposit);

        } else if (Objects.equals(sharingApp, Transaction.SHARING_APP_SFT)) {
            setSftProfitSharing(facade, transaction, profitSharing, tradeApp, isDeposit);

        } else {
            throw new ExternalServiceException(UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT, UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT_MESSAGE);
        }
        
    }

    private static void setPayProfitSharing(ExternalServiceFacade facade, Map<String,Object> transaction, Map<String,Object> profitSharing, String tradeApp, boolean isDeposit) {
        Map<String,Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            extraParams = new HashMap<>();
            transaction.put(Transaction.EXTRA_PARAMS, extraParams);
        }
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        boolean support = false;
        int clearanceProvider = BeanUtil.getPropInt(transaction.get(Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        if(isFormalByConfig(configSnapshot)){
            if(clearanceProvider == TransactionParam.CLEARANCE_PROVIDER_SYB){
                support = true;
            } else if(payway == ModelConstant.PAYWAY_ALIPAY2 || payway == ModelConstant.PAYWAY_WEIXIN || payway == ModelConstant.PAYWAY_PREPAID_CARD){
                support = true;
            }
        }else {
            // 间连通道是否支持分账
            if(ModelConstant.INDIRECT_SUPPORT_CLEARANCE_PROVIDER.contains(clearanceProvider)){
                support = true;
            }
        }
        String currency = AbstractServiceProvider.getTradeCurrency(configSnapshot);
        if(!TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)){
            support = false;
        }
        String sharingFlag = BeanUtil.getPropString(profitSharing, ProfitSharing.SHARING_FLAG);
        if(!support){
            Integer provider = MapUtils.getInteger(transaction, Transaction.PROVIDER);
            if(!ProviderMetaFacade.isSkipCheckProfitSharing(provider) && profitSharing != null
                    && !profitSharing.isEmpty() && ProfitSharing.SHARING_FLAG_ENABLE.equals(sharingFlag)){
                //交易不支持分账，但是又上送了分账参数，则报错
                logger.warn("unsupported profit sharing transaction, tid: {}", transaction.get(DaoConstants.ID));
                throw new ExternalServiceException(UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT, UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT_MESSAGE);
            }else{
                //交易不支持分账，没有上送分账参数或者分账参数标明不分账，则不做任何处理
                return;
            }
        }else{
            String merchantId = BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID);
            boolean hasCharge = profitSharing != null ? profitSharing.containsKey(ProfitSharing.CHARGE) : false;
            boolean businessTradeApp = !StringUtil.empty(tradeApp) && !TransactionParam.TRADE_APP_BASIC_PAY.equals(tradeApp);
            if(businessTradeApp 
                    || (profitSharing != null && !profitSharing.isEmpty() && (ProfitSharing.SHARING_FLAG_ENABLE.equals(sharingFlag) || StringUtil.empty(sharingFlag) || hasCharge))){
                //用返回值更新
                profitSharing = facade.validateAndGetUpdatedProfitSharing(merchantId, MapUtil.getString(transaction, Transaction.STORE_ID), profitSharing, transaction, tradeApp);
                if (profitSharing != null && profitSharing.size() > 0) {
                    extraParams.put(Transaction.PROFIT_SHARING, profitSharing);
                }
            }else{
                //调用分账服务获取是否有配置分账
                profitSharing = facade.getProfitSharingForAutoConfigSharing(merchantId, transaction);
                if(profitSharing != null && profitSharing.size() > 0){
                    extraParams.put(Transaction.PROFIT_SHARING, profitSharing);
                }
            }
            // 添加花呗分期商家贴息分账
            String enableThirdpartySubsidy = BeanUtil.getPropString(transaction, ProductFlagEnum.HBFQ_DISCOUNT.getKey());
            if("Y".equals(enableThirdpartySubsidy)) {
                int hbfqNum = BeanUtil.getPropInt(transaction, Transaction.EXTENDED_PARAMS +"." + BusinessV2Fields.EXTEND_PARAMS + "." + BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM, 12);
                Map<String, Object> hbfqProfitShaing = ApolloConfigurationCenterUtil.getHbfqProfitSharingInfoTemplate();
                if(MapUtil.isEmpty(hbfqProfitShaing)) {
                    return;
                }
                if(profitSharing == null) {
                    profitSharing = new HashMap<String, Object>();
                }
                List<Map<String, Object>> payReceivers = Optional.ofNullable((List<Map<String, Object>>)profitSharing.get(ProfitSharing.RECEIVERS)).orElse(new ArrayList<>());
                List<Map<String, Object>> hbfqReceivers = Optional.ofNullable((List<Map<String, Object>>)hbfqProfitShaing.get(ProfitSharing.RECEIVERS)).orElse(new ArrayList<>());
                if(hbfqReceivers.size() == 0) {
                    return;
                }
                Map<String, Object> hbfqReceiver = (Map<String, Object>) ((HashMap<String, Object>)hbfqReceivers.get(0)).clone();
                String payReceiverIds = payReceivers.stream().map(receiver -> MapUtil.getString(receiver, ProfitSharing.RECEIVER_ID)).collect(Collectors.joining(","));
                if(payReceiverIds.contains(MapUtil.getString(hbfqReceiver, ProfitSharing.RECEIVER_ID))) {
                    return;
                }
                String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG, "");
                int type = UpayUtil.getFqTypeByProductFlag(productFlag);
                hbfqReceiver.put(ProfitSharing.RECEIVER_SHARING_AMOUNT, FeeUtil.applyRate(MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT), ApolloConfigurationCenterUtil.getSellerServiceChargeFee(hbfqNum, type)));
                payReceivers.add(hbfqReceiver);
                profitSharing.put(ProfitSharing.RECEIVERS, payReceivers);
                
                if(!profitSharing.containsKey(ProfitSharing.SHARING_FLAG)) {
                    profitSharing.put(ProfitSharing.SHARING_FLAG, hbfqProfitShaing.get(ProfitSharing.SHARING_FLAG));
                }
                String payModelId = Optional.ofNullable(MapUtil.getString(profitSharing, ProfitSharing.MODEL_ID)).orElse("");
                String hbfqDiscountModelId = MapUtil.getString(hbfqProfitShaing, ProfitSharing.MODEL_ID);
                profitSharing.put(ProfitSharing.MODEL_ID, StringUtils.stringAppend(payModelId, ",", hbfqDiscountModelId, false));
                String paySharingType = Optional.ofNullable(MapUtil.getString(profitSharing, ProfitSharing.SHARING_TYPE)).orElse("");
                String hbfqDiscountSharingType = MapUtil.getString(hbfqProfitShaing, ProfitSharing.SHARING_TYPE);
                if(StringUtils.empty(paySharingType)) {
                    paySharingType = hbfqDiscountSharingType;
                }
                profitSharing.put(ProfitSharing.SHARING_TYPE, paySharingType);
                extraParams.put(Transaction.PROFIT_SHARING, profitSharing);
            }
            // 直连支付宝分账延迟结算需要冻结资金
            if(MapUtil.getInteger(transaction, Transaction.PROVIDER) == null 
                    && payway == ModelConstant.PAYWAY_ALIPAY2
                    && MapUtil.getBooleanValue(profitSharing, ProfitSharing.DELAYED)) {
                Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
                if(extendedParams == null) {
                    extendedParams = new HashMap<String, Object>();
                    transaction.put(Transaction.EXTENDED_PARAMS, extendedParams);
                }
                Map<String, Object> extendParams = (Map<String, Object>) MapUtil.getMap(extendedParams, BusinessV2Fields.EXTEND_PARAMS);
                if(extendParams == null) {
                    extendParams = new HashMap<String, Object>();
                    extendedParams.put(BusinessV2Fields.EXTEND_PARAMS, extendParams);
                }
                extendParams.put(BusinessV2Fields.ROYALTY_FREEZE, "true");
            }
        }
    }

    private static void setSftProfitSharing(ExternalServiceFacade facade, Map<String,Object> transaction, Map<String,Object> profitSharing, String tradeApp, boolean isDeposit) {
        Map<String,Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
        if(extraParams == null){
            extraParams = new HashMap<>();
            transaction.put(Transaction.EXTRA_PARAMS, extraParams);
        }
        Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
        boolean support = !StringUtil.empty(MapUtil.getString(configSnapshot, TransactionParam.SFT_BRAND_ID));
        if(!support){
            if(profitSharing != null && !profitSharing.isEmpty()){
                //交易不支持分账，但是又上送了分账参数，则报错
                logger.warn("unsupported profit sharing transaction, tid: {}", transaction.get(DaoConstants.ID));
                throw new ExternalServiceException(UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT, UpayErrorScenesConstant.PROFIT_SHARING_NOT_SUPPORT_MESSAGE);
            }else{
                //交易不支持分账，没有上送分账参数或者分账参数标明不分账，则不做任何处理
                return;
            }
        }else{
            //用返回值更新
            profitSharing = facade.validateAndGetUpdatedSFTProfitSharing(MapUtil.getString(configSnapshot, TransactionParam.SFT_BRAND_ID), MapUtil.getString(transaction, Transaction.MERCHANT_ID), MapUtil.getString(transaction, Transaction.STORE_ID), profitSharing, transaction);
            if (profitSharing != null && profitSharing.size() > 0) {
                extraParams.put(Transaction.PROFIT_SHARING, profitSharing);
            }
        }
    }

    private void updateOrderAndTransactionTheSameTime(Map<String,Object> order, Map<String,Object> transaction){
        long currTime = System.currentTimeMillis();
        order.put(DaoConstants.CTIME, currTime);
        transaction.put(DaoConstants.CTIME, currTime);
    }


    private void consultSQBDiscounts(Map<String, Object> config, Map<String, Object> order, Map<String, Object> transaction, String terminalSn, 
            int paywayCode, int subPaywayCode, long total, String payerUid, String barcode, MpayServiceProvider provider,List<Map<String,Object>> goodsDetails, boolean isDeposit, Boolean activityDiscount){

        //正式活动不参与wosai的系统优惠优惠
        List<Map> sqbGoodsDetail = new ArrayList<>();
        if (goodsDetails != null) {
            for (Map goodsDetail : goodsDetails) {
                if (BeanUtil.getPropInt(goodsDetail, UpayConstant.PROMOTION_TYPE) != UpayConstant.PROMOTION_TYPE_SQB) {
                    continue;
                }
                sqbGoodsDetail.add(goodsDetail);
            }
        }
        //收钱吧预授权
        if (isDeposit && Objects.equals(BeanUtil.getPropString(transaction, Transaction.KEY_DEPOSIT_TYPE), TransactionParam.DEPOSIT_SQB)) {
            return;
        }
        if(Transaction.TYPE_DEPOSIT_FREEZE == MapUtil.getIntValue(transaction, Transaction.TYPE)) {
            return ;
        }

        List<Map<String, Object>> transactionPayments = new ArrayList<>();
        long promotionAmount = 0;
        // 1、解析业务方上送的优惠明细
        List<Map<String, Object>> sqbPromotionDetails = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, Transaction.KEY_SQB_PROMOTION_DETAIL);
        if (CollectionUtils.isNotEmpty(sqbPromotionDetails)) {
            transactionPayments = PaymentUtil.buildTransactionPayments(sqbPromotionDetails);
            promotionAmount = transactionPayments.stream().mapToLong(map -> MapUtil.getLong(map, Payment.AMOUNT)).sum();
        }
        total = total - promotionAmount; //减去业务方上送的优惠金额
        if (total <= 0) {
            logger.error("sqb promotion total amount abnormal to order {} when pay, detail is:{}", BeanUtil.getPropString(order, Order.SN), sqbPromotionDetails);
            throw new UpayBizException(UpayErrorScenesConstant.UPAY_ORDER_AMOUNT_NEED_GT_ZERO, UpayErrorScenesConstant.UPAY_ORDER_AMOUNT_NEED_GT_ZERO_MESSAGE);
        }

        Object activityBizExt = BeanUtil.getNestedProperty(transaction, Transaction.KEY_SQB_ACTIVITY_BIZ_EXT);
        boolean isFormal = UpayUtil.isFormalByTradeParams(provider.getTradeParams(transaction));
        int clearanceProvider = MapUtil.getIntValue(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        ExternalServiceFacade.TcpPayResult result = facade.consultTcpForPay((String) config.get(TransactionParam.STORE_SN),
                (String) config.get(TransactionParam.STORE_CITY),
                BeanUtil.getPropString(order, Order.SN),
                paywayCode,
                subPaywayCode,
                total,
                payerUid,
                getWeixinAppIdOfPayer(transaction, provider),
                config, barcode, terminalSn, (String) order.get(Order.CLIENT_SN), sqbGoodsDetail,isFormal?1:0, clearanceProvider, activityBizExt, activityDiscount);
        if(!StringUtil.empty(result.getProductFlag())){
            String original = (String) BeanUtil.getNestedProperty(transaction, Transaction.KEY_SQB_PRODUCT_FLAG);
            String newValue = StringUtil.empty(original) ? result.getProductFlag() : original + UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER + result.getProductFlag();
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_SQB_PRODUCT_FLAG, newValue);
        }
        long effectiveTotal = total;
        if (result.isTriggered()) {
            //除了储值核销外，直连交易不容许参加wosai优惠
            if (isFormal && paywayCode != Order.PAYWAY_PREPAID_CARD && ((result.getDiscountWosaiAmount() != null && result.getDiscountWosaiAmount() != 0)
                    || (result.getHongbaoWosaiAmount() != null && result.getHongbaoWosaiAmount() != 0))) {
                logger.error("formal merchant can not get wosai discount:{}", transaction);
                return;
            }

            order.put(Order.TCP_MODIFIED, true);
            effectiveTotal = result.getEffectiveAmount();
            transactionPayments.addAll(PaymentUtil.buildTransactionPaymentsForPay(result));

            // 存在优惠时，商户花呗分期手续费重新设置
            if(Order.PAYWAY_ALIPAY2 == BeanUtil.getPropInt(transaction,Transaction.PAYWAY)) {
                Map extend = (Map) transaction.get(Transaction.EXTENDED_PARAMS);
                if (extend != null) {
                    if (extend.containsKey(BusinessV2Fields.EXTEND_PARAMS)) {
                        Object extendParamsObj = extend.get(BusinessV2Fields.EXTEND_PARAMS);
                        if (extendParamsObj instanceof Map) {
                            Map<String, Object> extendParams = (Map<String, Object>) extendParamsObj;
                            int hbfqNum = MapUtil.getIntValue(extendParams, BusinessV2Fields.EXTEND_PARAMS_HB_FQ_NUM);
                            int hbfqPercent = MapUtil.getIntValue(extendParams, BusinessV2Fields.EXTEND_PARAMS_HB_FQ_SELLER_PERCENT);
                            Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
                            int fqType = 0;
                            Object fqChanel = BeanUtil.getNestedProperty(transaction, ProductFlagEnum.CREDIT_CARD_INSTALMENT.getKey());
                            if (fqChanel != null && fqChanel.equals(UpayConstant.CREDIT_FQ_CHANNELS)) {
                                fqType = UpayConstant.USE_CREDIT;
                            }else if (BeanUtil.getNestedProperty(transaction, ProductFlagEnum.HUABEI.getKey()) != null) {
                                fqType = UpayConstant.USE_HBFQ;
                            }
                            if (hbfqPercent == 100) {
                                long rate = FeeUtil.applyRate(effectiveTotal, ApolloConfigurationCenterUtil.getSellerServiceChargeFee(hbfqNum, fqType));
                                if(fqType == UpayConstant.USE_HBFQ) {
                                    extraParams.put(Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, rate);
                                }
                                extraParams.put(Transaction.SQB_FQ_SELLER_SERVICE_CHARGE, rate);
                            } else {
                                long rate = FeeUtil.applyRate(effectiveTotal, ApolloConfigurationCenterUtil.getBuyerServiceChargeFee(hbfqNum, fqType));
                                if (fqType == UpayConstant.USE_HBFQ) {
                                    extraParams.put(Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, rate);
                                }
                                extraParams.put(Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, rate);
                            }
                        }
                    }
                }
            }
        }
        // 更新交易、订单支付信息
        if (CollectionUtils.isNotEmpty(transactionPayments)) {
            List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(transactionPayments);
            Map<String, Object> orderItems = (Map<String, Object>) order.get(Order.ITEMS);
            if (orderItems == null) {
                orderItems = new HashMap<>();
                order.put(Order.ITEMS, orderItems);
            }
            orderItems.put(Order.PAYMENTS, orderPayments);

            Map<String, Object> transactionItems = (Map<String, Object>) transaction.get(Transaction.ITEMS);
            if (transactionItems == null) {
                transactionItems = new HashMap<>();
                transaction.put(Transaction.ITEMS, transactionItems);
            }
            transactionItems.put(Transaction.PAYMENTS, transactionPayments);
            order.put(Order.EFFECTIVE_TOTAL, effectiveTotal);
            order.put(Order.NET_EFFECTIVE, effectiveTotal);
            transaction.put(Transaction.EFFECTIVE_AMOUNT, effectiveTotal);
        }
        if(!StringUtil.empty(result.getMarketUserId()) && StringUtil.empty(BeanUtil.getPropString(transaction, Transaction.KEY_SQB_USER_ID))){
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_SQB_USER_ID, result.getMarketUserId());
        }
    }

    /**
     * 加密
     *
     * @param reflect
     * @return
     */
    private Object encryptReflect(Object reflect, boolean isDeposit) {
        if (!((reflect instanceof String) && isDeposit)) {
            return reflect;
        }
        String reflectStr = (String) reflect;
        if (!org.apache.commons.lang3.StringUtils.contains(reflectStr, TransactionParam.DEPOSIT)) {
            return reflect;
        }
        try {
            Map reflectMap = JacksonUtil.toBean(reflectStr, Map.class);
            String cellphone = MapUtil.getString(reflectMap, Transaction.REFLECT_DEPOSIT_CELLPHONE);
            if (!StringUtil.empty(cellphone)) {
                cellphone = cryptoService.encryptDepositCellphone(cellphone);
            }
            reflectMap.put(Transaction.REFLECT_DEPOSIT_CELLPHONE, cellphone);
            return JacksonUtil.toJsonString(reflectMap);
        } catch (Exception e) {
            logger.error("encryptReflect failed. reflect:{}", reflect, e);
            throw new UpayBizException("预授权reflect异常", e);
        }
    }

    private void setDepositValidate(boolean isDeposit, int paywayCode, Map<String, Object> transaction, MpayServiceProvider serviceProvider) {
        if (isDeposit) {
            // 银商云闪付和拉卡拉开放平台是平台支持,没有配置单独的key
            if (serviceProvider instanceof ChinaumsEPayServiceProvider
                    || serviceProvider instanceof LakalaOpenV3BankCardServiceProvider
                    || serviceProvider instanceof FuyouBankServiceProvider
                    || serviceProvider instanceof TLSybBankServiceProvider) {
                return;
            }
            String depositType = getDepositType(paywayCode, transaction, serviceProvider);
            if (Objects.isNull(depositType)) {
                Map depositConfig = (Map) BeanUtil.getNestedProperty(transaction, Transaction.CONFIG_SNAPSHOT + "." + TransactionParam.DEPOSIT);
                if (MapUtil.isEmpty(depositConfig)) {
                    throw new UpayBizException(UpayErrorScenesConstant.DEPOSIT_LIMITER, UpayErrorScenesConstant.DEPOSIT_LIMITER_MESSAGE);
                } else {
                    throw new UpayBizException(UpayErrorScenesConstant.DEPOSIT_NOT_SUPPORT, UpayErrorScenesConstant.DEPOSIT_NOT_SUPPORT_MESSAGE);
                }
            }
            //设置预授权类型
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_DEPOSIT_TYPE, depositType);
        }
    }

    /**
     * 获取预授权类型
     *
     * @return
     */
    private String getDepositType(int paywayCode, Map<String, Object> transaction, MpayServiceProvider serviceProvider) {
        Map configSnapshot = (Map) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        Map depositConfig = (Map) configSnapshot.get(TransactionParam.DEPOSIT);
        //直连预授权
        if (Objects.isNull(serviceProvider.getProvider())) {
            if (paywayCode == Order.PAYWAY_ALIPAY || paywayCode == Order.PAYWAY_ALIPAY2 || paywayCode == Order.PAYWAY_WEIXIN) {
                String depositType = paywayCode == Order.PAYWAY_WEIXIN ? TransactionParam.DEPOSIT_WX : TransactionParam.DEPOSIT_ALI;
                if (BeanUtil.getPropInt(depositConfig, depositType) == TransactionParam.DEPOSIT_OPEN) {
                    return depositType;
                }
            }
            return null;
        }
        //间连预授权
        //拉卡拉通道间连预授权
        if ((paywayCode == Order.PAYWAY_ALIPAY || paywayCode == Order.PAYWAY_ALIPAY2)
                && configSnapshot.containsKey(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS)
                && BeanUtil.getPropInt(depositConfig, TransactionParam.DEPOSIT_ALI) == TransactionParam.DEPOSIT_OPEN) {
            return TransactionParam.DEPOSIT_ALI;
        }
        //收钱吧间连预授权
        if (providerMetaFacade.supportDeposit(serviceProvider.getProvider())
                && BeanUtil.getPropInt(depositConfig, TransactionParam.DEPOSIT_SQB) == TransactionParam.DEPOSIT_OPEN) {
            return TransactionParam.DEPOSIT_SQB;
        }
        return null;
    }


    /**
     * 生成优惠产品标识
     *
     * @param transaction
     */
    private void genProductFlagAndComboId(Map<String, Object> transaction, MpayServiceProvider provider) {
        List<Map<String, Object>> transactionPayments
                = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, PaymentUtil.TRANSACTION_PAYMENTS_PATH);
        Set<String> flags = new HashSet<>();
        if (CollectionUtils.isNotEmpty(transactionPayments)) {
            for (Map<String, Object> payment : transactionPayments) {
                String type = BeanUtil.getPropString(payment, Payment.ORIGIN_TYPE);
                if(ProductFlagEnum.REDPACK_DISCOUNT.contain(type)){
                    flags.add(ProductFlagEnum.REDPACK_DISCOUNT.getCode());
                }
                if(ProductFlagEnum.MARKET_PROGRAM_DISCOUNT.contain(type)){
                    flags.add(ProductFlagEnum.MARKET_PROGRAM_DISCOUNT.getCode());
                }
                for (ProductFlagEnum scene : UpayConstant.ACTIVITY_SCENES) {
                    if(scene.contain(type)){
                        flags.add(scene.getCode());
                    }
                }
            }
        }
        //生成花呗分期产品标识
        if (BeanUtil.getNestedProperty(transaction, ProductFlagEnum.HUABEI.getKey()) != null) {
            flags.add(ProductFlagEnum.HUABEI.getCode());
        }
        //添加花呗贴息产品标识
        String enableThirdpartySubsidy = BeanUtil.getPropString(transaction, ProductFlagEnum.HBFQ_DISCOUNT.getKey());
        if("Y".equals(enableThirdpartySubsidy)) {
            flags.add(ProductFlagEnum.HBFQ_DISCOUNT.getCode());
        }
        //添加信用卡分期标识
        Object fqChanel = BeanUtil.getNestedProperty(transaction, ProductFlagEnum.CREDIT_CARD_INSTALMENT.getKey());
        if (fqChanel != null && fqChanel.equals(UpayConstant.CREDIT_FQ_CHANNELS)) {
            flags.add(ProductFlagEnum.CREDIT_CARD_INSTALMENT.getCode());
        }

        //生成分期2.0标识
        if (BeanUtil.getNestedProperty(transaction, ProductFlagEnum.FQ_VERSION_2.getKey()) != null) {
            flags.add(ProductFlagEnum.FQ_VERSION_2.getCode());
        }
        //添加储值核销标识
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        if(payway == Order.PAYWAY_PREPAID_CARD){
            flags.add(ProductFlagEnum.PREPAID_CARD.getCode());
        }
        //添加预授权类型
        String depositType = BeanUtil.getPropString(transaction, ProductFlagEnum.DEPOSIT_TYPE_SQB.getKey());
        if (!StringUtil.empty(depositType)) {
            if (Objects.equals(depositType, TransactionParam.DEPOSIT_SQB)) {
                flags.add(ProductFlagEnum.DEPOSIT_TYPE_SQB.getCode());
            } else {
                flags.add(ProductFlagEnum.DEPOSIT_TYPE_PAY_SOURCE.getCode());
            }
        }
        Map<String, Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        if(MapUtil.isNotEmpty(extraParams)) {
            String sqbScene = (String) BeanUtil.getNestedProperty(transaction, Transaction.KEY_SQB_SCENE);
            // 添加产品标识
            String value;
            for (ProductFlagEnum flag : UpayConstant.REQUEST_SCENES) {
                if(Transaction.KEY_SQB_SCENE.equals(flag.getKey())){
                    value = sqbScene;
                }else{
                    value = (String) BeanUtil.getNestedProperty(transaction, flag.getKey());
                }
                if (value != null && flag.contain(value)) {
                    flags.add(flag.getCode());
                }
            }
            //解析透传参数或者活动服务返回的product_flag
            String sqbProductFlag = (String) BeanUtil.getNestedProperty(transaction, Transaction.KEY_SQB_PRODUCT_FLAG);
            if(sqbProductFlag != null){
                String [] codes = sqbProductFlag.split(UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER);
                for (int i = 0; i < codes.length; i++) {
                    String code = codes[i];
                    if(UpayConstant.VALID_PRODUCT_FLAG_CODES.contains(code)){
                        flags.add(code);
                    }
                }
            }
        }

        String comboId = null;
        //处理费率原因与套餐id
        Map<String, String> feeRateTagMap = MapUtil.getMap(provider.getTradeParams(transaction)
                , TransactionParam.FEE_RATE_TAG);
        String subPayway = MapUtil.getString(transaction, Transaction.SUB_PAYWAY);
        if (Objects.nonNull(subPayway) && subPayway.length() > 0) {
            String feeRateTag = MapUtil.getString(feeRateTagMap, subPayway);
            if (Objects.nonNull(feeRateTag) && feeRateTag.length() > 0) {
                //旧的值为 product_flag, 新的为 combo_id:product_flag 或者 combo_id:
                if(feeRateTag.contains(":")){
                    int index = feeRateTag.indexOf(":");
                    comboId = feeRateTag.substring(0, index);
                    String flag = feeRateTag.substring(index + 1);
                    if(com.wosai.pantheon.util.StringUtil.isNotEmpty(flag)){
                        flags.add(flag);
                    }
                }else{
                    flags.add(feeRateTag);
                }
            }
        }
        // 新增通过trade_app等信息映射的product_flags
        flags.addAll(tradeAppFacade.genProductFlags(transaction));

        //活动服务不直接返回product_flag后，需要根据新增的product_flag反算旧的值，以便下游业务不受影响
        if(!flags.isEmpty()){
            for (Map.Entry<String, String> entry : UpayConstant.PRODUCT_FLAG_NEW_OLDS.entrySet()) {
                String newFlag = entry.getKey();
                String oldFlag = entry.getValue();
                if(flags.contains(newFlag) && !flags.contains(oldFlag)){
                    flags.add(oldFlag);
                }
            }
        }
        //记录套餐id
        if(!StringUtils.isEmpty(comboId)){
            BeanUtil.setNestedProperty(transaction, Transaction.KEY_COMBO_ID, comboId);
        }

        transaction.put(Transaction.PRODUCT_FLAG, flags.isEmpty() ? null : com.wosai.pantheon.util.StringUtil.joinC(UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER, flags));
    }


    /**
     * 处理智慧门店收费试点逻辑， 手续费上浮
     * @param transaction
     * @param tradeParams
     */
    private void processSmartServiceFeeUp(Map<String,Object> transaction, Map<String, Object> tradeParams){
        Map<String,Object> extraParams = MapUtil.getMap(transaction, Transaction.EXTRA_PARAMS);
        String sqbScene = MapUtil.getString(extraParams, Transaction.SQB_SCENE);
        if(sqbScene == null || !sqbScene.contains("service_fee_up")){
            //没有上送此场景值，则不做任何业务处理
            return;
        }
        Map<String,Object> configSnapshot = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String vendorAppAppId = MapUtil.getString(configSnapshot, TransactionParam.TERMINAL_VENDOR_APP_APPID);
        int clearanceProvider = MapUtil.getIntValue(configSnapshot, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        boolean isJjzTerminal = ApolloConfigurationCenterUtil.getJjzVendorAppAppid().equals(vendorAppAppId);
        boolean notFormal = MapUtil.getBooleanValue(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY);
        boolean isLakalaOrTl = clearanceProvider == TransactionParam.CLEARANCE_PROVIDER_LKL || clearanceProvider == TransactionParam.CLEARANCE_PROVIDER_TL;
        boolean isAlipayOrWeixin = payway == Order.PAYWAY_ALIPAY2 || payway == Order.PAYWAY_ALIPAY || payway == Order.PAYWAY_WEIXIN;
        boolean match = isJjzTerminal && notFormal && isLakalaOrTl && isAlipayOrWeixin;
        if(!match){
            //不满足费率上浮条件，去除掉扩展参数里面的费率上浮场景值
            sqbScene = sqbScene.replaceAll(" ", "").replace(",service_fee_up", "").replace("service_fee_up,", "").replace("service_fee_up", "");
            if(sqbScene.isEmpty()){
                extraParams.remove(Transaction.SQB_SCENE);
            }else{
                extraParams.put(Transaction.SQB_SCENE, sqbScene);
            }
            return;
        }
        //满足条件上浮费率, 通过yuan2cents 转换相关小数类型的费率为long类型后相加，再转换成小数类型
        long upFeeRate = StringUtils.yuan2cents(ApolloConfigurationCenterUtil.getJjzFeeRateUp());
        if(tradeParams.containsKey(TransactionParam.FEE_RATE_ORIGINAL)){
            String feeRateOriginal = MapUtil.getString(tradeParams, TransactionParam.FEE_RATE_ORIGINAL);
            tradeParams.put(TransactionParam.FEE_RATE_ORIGINAL, StringUtils.cents2yuan(upFeeRate + StringUtils.yuan2cents(feeRateOriginal)));
        }
        String feeRateOriginal = MapUtil.getString(tradeParams, TransactionParam.FEE_RATE);
        tradeParams.put(TransactionParam.FEE_RATE, StringUtils.cents2yuan(upFeeRate + StringUtils.yuan2cents(feeRateOriginal)));
    }

    private int resetUnionPayway(Map<String, Object> config, int defaultPayway) {
        if (MapUtil.isEmpty(config)) {
            return defaultPayway;
        }
        String paywayCode = (String) config.get(TransactionParam.HIT_PAYWAY);

        int payway = WosaiNumberUtils.toInt(paywayCode, defaultPayway);
        if (defaultPayway != payway && logger.isInfoEnabled()) {
            logger.info("method[resetUnionPayway] payway[{}>>{}]", defaultPayway, payway);
        }
        return payway;
    }

    @Override
    public Map<String, Object> coldTradeRefund(Map<String, Object> request) {
        return refund(request);
    }

    /**
     * 设置当前费率为费率最大值，用于提前占用活动金额
     * 
     * @param tradeParams
     * @param transaction
     * @param provider
     */
    private void resetChannelOrChannelLadderFeeToMax(Map<String, Object> tradeParams, Map<String, Object> transaction, Integer provider) {
        // 设置资金渠道费率最大值
        Map<String, Map<String, Object>> bankcardFee = MapUtil.getMap(tradeParams, TransactionParam.PARAMS_BANKCARD_FEE);
        if (bankcardFee != null) {
            long maxFeeRate = 0;
            for(Map<String, Object> config : bankcardFee.values()) {
                String feeRate = BeanUtil.getPropString(config, TransactionParam.FEE);
                if (!StringUtils.isEmpty(feeRate)) {
                    maxFeeRate = Math.max(maxFeeRate, StringUtils.yuan2cents(feeRate));
                    tradeParams.put(TransactionParam.FEE_RATE, StringUtils.cents2yuan(maxFeeRate));
                }
            }
            return;
        }
        // 设置资金渠道阶梯费率最大值
        Map<String, List<Map<String, Object>>> channelLadderFeeRate = MapUtil.getMap(tradeParams, TransactionParam.CHANNEL_LADDER_FEE_RATES);
        if (channelLadderFeeRate != null) {
            long maxFeeRate = 0;
            long amount = MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT);
            for (List<Map<String, Object>> configs : channelLadderFeeRate.values()) {
                for (Map<String, Object> config : configs) {
                    String feeRate = BeanUtil.getPropString(config, TransactionParam.FEE_RATE);
                    long min = MapUtil.getLongValue(config, TransactionParam.LADDER_FEE_RATE_MIN, 0);
                    long max = MapUtil.getLongValue(config, TransactionParam.LADDER_FEE_RATE_MAX, Integer.MAX_VALUE);
                    if (!StringUtils.isEmpty(feeRate) && amount > min && amount <= max) {
                        maxFeeRate = Math.max(maxFeeRate, StringUtils.yuan2cents(feeRate));
                        tradeParams.put(TransactionParam.FEE_RATE, StringUtils.cents2yuan(maxFeeRate));
                    }
                }
            }
            return;
        }
    }

    @Override
    public Map<String, Object> createFitnessOrder(Map<String, Object> request) {
        String terminalSn = MapUtil.getString(request, TERMINAL_SN);
        String channelNo = MapUtil.getString(request, "channel_no");
        String clientSn = MapUtil.getString(request, CLIENT_SN);
        String tradeApp = MapUtil.getString(request, TRADE_APP);
        long totalAmount = MapUtil.getLongValue(request, TOTAL_AMOUNT);
        String subject = MapUtil.getString(request, SUBJECT);
        Map profitSharing = MapUtil.getMap(request, PROFIT_SHARING);
        Map<String, Object> config = facade.getAllParamsWithTradeApp(null, terminalSn, Order.PAYWAY_ALIPAY2, Order.SUB_PAYWAY_MINI, tradeApp);
        Map<String, Object> tradeParams = MapUtil.getMap(config, TransactionParam.FITNESS_PARAMS);
        if (tradeParams == null) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG, UpayErrorScenesConstant.UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE);
        }
        Map<String, Object> filterConfig = new HashMap<>();
        config.entrySet().forEach(o -> {
            if (o.getKey().endsWith("_trade_params")) {
                return;
            }
            filterConfig.put(o.getKey(), o.getValue());
        });
        config = filterConfig;
        config.put(TransactionParam.PROVIDER, null);
        // 订单使用订购时的手续费费率
        tradeParams.remove(TransactionParam.FITNESS_PARAMS_PRODUCTS);
        tradeParams.put(TransactionParam.LIQUIDATION_NEXT_DAY, false);
        //替换成fitness_trade_params作为交易参数
        config.remove(TransactionParam.FITNESS_PARAMS);
        config.put(TransactionParam.FITNESS_TRADE_PARAMS, tradeParams);
        String sn = tsnGenerator.nextSn();
        String orderId = UpayUtil.getOrderIdBySn(sn);
        // 设置直接收款 , 基础支付业务且非储值核销 交易
        List<String> flags = Lists.newArrayList(ProductFlag.FIENESS.getCode());
        List<String> basicPaymentTradeApps = ApolloConfigurationCenterUtil.getBasicPaymentTradeApps();
        if (basicPaymentTradeApps.contains(tradeApp)) {
            flags.add(ProductFlag.DIRECT_PAY.getCode());
        }
        Map<String, Object> order = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                                                           DaoConstants.CTIME, new Date().getTime(),
                                                           Order.SN, sn,
                                                           Order.CLIENT_SN, clientSn,
                                                           Order.SUBJECT, subject,
                                                           Order.BODY , null,
                                                           Order.STATUS, Order.STATUS_PAID,
                                                           Order.TCP_MODIFIED, false,
                                                           Order.ORIGINAL_TOTAL, totalAmount,
                                                           Order.NET_ORIGINAL, totalAmount,
                                                           Order.EFFECTIVE_TOTAL, totalAmount,
                                                           Order.NET_EFFECTIVE, totalAmount,
                                                           Order.PAYWAY, Order.PAYWAY_ALIPAY2,
                                                           Order.SUB_PAYWAY, Order.SUB_PAYWAY_MINI,
                                                           Order.MERCHANT_ID, config.get(TransactionParam.MERCHANT_ID),
                                                           Order.STORE_ID, config.get(TransactionParam.STORE_ID),
                                                           Order.TERMINAL_ID, config.get(TransactionParam.TERMINAL_ID),
                                                           Order.TRADE_NO, channelNo,
                                                           Order.OPERATOR, null,
                                                           Order.REFLECT, null);
        // 保存交易流水
        String transactionId = UpayUtil.getTransactionIdBySn(sn);
        Map<String, Object> transaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                                                                 Transaction.TSN, sn,
                                                                 Transaction.CLIENT_TSN, clientSn,
                                                                 Transaction.TYPE, Transaction.TYPE_PAYMENT,
                                                                 Transaction.SUBJECT, subject,
                                                                 Transaction.BODY, null,
                                                                 Transaction.STATUS, Transaction.STATUS_SUCCESS,
                                                                 Transaction.ORIGINAL_AMOUNT, totalAmount,
                                                                 Transaction.EFFECTIVE_AMOUNT, totalAmount,
                                                                 Transaction.MERCHANT_ID, order.get(Order.MERCHANT_ID),
                                                                 Transaction.STORE_ID, order.get(Order.STORE_ID),
                                                                 Transaction.TERMINAL_ID, order.get(Order.TERMINAL_ID),
                                                                 Transaction.OPERATOR, order.get(Order.OPERATOR),
                                                                 Transaction.PAYWAY, Order.PAYWAY_ALIPAY2,
                                                                 Transaction.SUB_PAYWAY, Order.SUB_PAYWAY_MINI,
                                                                 Transaction.TRADE_NO, channelNo,
                                                                 Transaction.ORDER_ID, orderId,
                                                                 Transaction.ORDER_SN, sn,
                                                                 Transaction.EXTRA_PARAMS, MapUtil.hashMap(Transaction.PROFIT_SHARING, profitSharing),
                                                                 Transaction.EXTENDED_PARAMS, null,
                                                                 Transaction.REFLECT, null,
                                                                 Transaction.PRODUCT_FLAG, com.wosai.pantheon.util.StringUtil.joinC(UpayConstant.TRANSACTION_PRODUCT_FLAG_DELIMITER, flags),
                                                                 Transaction.CONFIG_SNAPSHOT, config);

        // 只处理订单状态为成功的订单
        TransactionContext context = workflowManager.createTransactionContext(terminalSn,order,transaction);
        MpayServiceProvider provider = context.getServiceProvider();

        if (!Workflow.RC_PAY_SUCCESS.equals(provider.query(context))) {
            throw new UpayClientException(UpayErrorScenesConstant.UPAY_BIZ_ERROR, UpayErrorScenesConstant.UPAY_BIZ_ERROR_MESSAGE);
        }

        //提前设置激活的是哪套交易参数以及设置provider以及手续费
        useTradeParamAndSetProvider(transaction, order, provider);
        
        //保证order, transaction的ctime都一样，以便数据根据时间迁移后，订单与流水能在相同的时间段内。
        updateOrderAndTransactionTheSameTime(order, transaction);

        repository.save(order, transaction);
        context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
        workflowManager.raise(context, Workflow.RC_PAY_SUCCESS);
        return payResponse(context, false);
    }

    /**
     * 支付接口校验是否已经预撤单
     * @param config
     */
    private void verifyPayPreCancel(Map<String, Object> config, String clientSn) {


        String commonSwitchStr = MapUtil.getString(config, TransactionParam.COMMON_SWITCH);
        if (UpayUtil.isCanPreCancel(commonSwitchStr)) {

            //判断是否存在预撤单记录
            String merchantId = MapUtil.getString(config, Transaction.MERCHANT_ID); //商户id
            String storeId = MapUtil.getString(config, Transaction.STORE_ID); //门店id

            Map<String, Object> preCancelOrder = repository.getPreCancelOrder(merchantId, storeId, clientSn);
            if (MapUtil.isNotEmpty(preCancelOrder)) {
                //存在预撤销订单，直接拒绝，抛异常
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_PRE_CANCELED_CAN_NOT_PAY
                        , UpayErrorScenesConstant.ORDER_PRE_CANCELED_CAN_NOT_PAY_MESSAGE);
            }
        }
    }

    /**
     * 撤单时获取原订单信息， 若订单不存在，即判断是否可以预撤单(若预撤单，即返回null); 反之，返回订单信息
     * @param sn
     * @param clientSn
     * @param merchantId
     * @param storeId
     * @return
     */
    private Map<String, Object> getOrder(String sn, String clientSn, String merchantId, String storeId) {
        Map<String, Object> order = repository.getOrder(merchantId, storeId, sn, clientSn);
        //对接了拉卡拉或者兴业银行第三方支付通道，sn传的值有可能是拉卡拉或者兴业的订单号
        if( order == null && !StringUtils.isEmpty(sn)) {
            Criteria criteria = Criteria.where(Order.MERCHANT_ID).is(merchantId).with(Order.TRADE_NO).is(sn);
            long  size  = repository.getOrderDao().filter(criteria).count();
            if(size > 1){
                throw new OrderNotExistsException(UpayErrorScenesConstant.TRADE_NO_REPEAT, UpayErrorScenesConstant.TRADE_NO_REPEAT_MESSAGE);
            }
            order = repository.getOrderDao().filter(criteria).fetchOne();
        }
        if(null == order) {
            order = gatewaySupportService.getOrderBySn(merchantId, sn, clientSn, DataPartitionConst.RECENT_6M);
            if(null != order) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.UPAY_CANCEL_OVER_DATE_LIMIT, UpayErrorScenesConstant.UPAY_CANCEL_OVER_DATE_LIMIT_MESSAGE);
            }
        }

        return order;
    }

    private boolean doPreCancel(Map<String, Object> basicParams, String clientSn, String merchantId, String storeId, String terminalId) {
        // 校验是否允许预撤单
        String commonSwitchStr = MapUtil.getString(basicParams, TransactionParam.COMMON_SWITCH);
        if (UpayUtil.isCanPreCancel(commonSwitchStr)) {
            Map<String, Object> preCancelOrder = repository.getPreCancelOrder(merchantId, storeId, clientSn);
            if (MapUtil.isEmpty(preCancelOrder)) {
                //记录下预撤销订单， 并返回撤单成功
                String id = UUID.randomUUID().toString();
                Map<String, Object> preCanceledOrder = MapUtil.hashMap(DaoConstants.ID, id,
                        Order.CLIENT_SN, clientSn, Order.MERCHANT_ID, merchantId,
                        Order.STORE_ID, storeId, Order.TERMINAL_ID, terminalId);

                repository.getPreCancelOrderDao().save(preCanceledOrder);
            }
            return true;
        }

        return false;
    }


    private void setTransactionContextErrorInfo(TransactionContext context, Map<String, Object> notification, MpayServiceProvider provider) {
        //改写信息
        String notifyMsg = MapUtil.getString(notification, UpayConstant.CLIENT_NOTIFY_MSG);
        Map<String, Object> map = new LinkedHashMap<>();
        map.put(UpayConstant.CLIENT_NOTIFY_STATUS, CommonResponse.FAIL);
        map.put(UpayConstant.CLIENT_NOTIFY_MSG, notifyMsg);//返回信息
        long type = BeanUtil.getPropLong(context.getTransaction(), Transaction.TYPE);
        if(type != Transaction.TYPE_PAYMENT && type != Transaction.TYPE_DEPOSIT_CONSUME){
            return;
        }
        int subPayway = MapUtil.getIntValue(context.getTransaction(), Order.SUB_PAYWAY);
        String op = Order.SUB_PAYWAY_BARCODE == subPayway || provider.getProvider() == Order.PROVIDER_JY_CARD
                ? provider.OP_PAY
                : provider.OP_PRECREATE;

        // 设置通道返回信息
        com.wosai.upay.util.MapUtil.removeNullValues(map);
        Map<String, Object> providerErrorInfo = MapUtil.getMap(context.getTransaction(), Transaction.PROVIDER_ERROR_INFO);
        if (providerErrorInfo == null) {
            providerErrorInfo = new HashMap<>();
            context.getTransaction().put(Transaction.PROVIDER_ERROR_INFO, providerErrorInfo);
        }
        providerErrorInfo.put(op, map);

        // 设置转换后的收钱吧异常信息
        Map<String, Object> bizErrorCode = MapUtil.getMap(context.getTransaction(), Transaction.BIZ_ERROR_CODE);
        if (bizErrorCode == null) {
            bizErrorCode = new HashMap<>();
            context.getTransaction().put(Transaction.BIZ_ERROR_CODE, bizErrorCode);
        }
        UpayBizError bizError = UpayBizError.getBizErrorByField(op, MapUtil.getIntValue(context.getTransaction(), Transaction.PAYWAY), CommonResponse.FAIL, notifyMsg);
        if (bizError != null && !UpayBizError.UNEXPECTED_PROVIDER_ERROR.getStandardName().equals(bizError.getStandardName())) {
            bizErrorCode.put(op, bizError);
        } else {
            bizErrorCode.put(op, UpayBizError.unexpectedProviderError(StringUtil.empty(notifyMsg) ? UpayBizError.UNEXPECTED_PROVIDER_ERROR.getMessage() : notifyMsg, false));
        }
    }
}
