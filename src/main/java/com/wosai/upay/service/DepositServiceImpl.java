package com.wosai.upay.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.PaymentUtil;
import com.wosai.upay.util.RateLimiterUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.TransactionContext;
import com.wosai.upay.workflow.UpayBizError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.service.UpayService.PROFIT_SHARING;

@Service
public class DepositServiceImpl implements DepositService {
    
    @Autowired
    private DataRepository repository;
    @Autowired
    private DepositConsumeProcessor depositConsumeProcessor;
    @Autowired
    private DepositCancelProcessor depositCancelProcessor;
    @Autowired
    private DepositFixProcessor depositFixProcessor;
    @Autowired
    private DepositSyncProcessor depositSyncProcessor;
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private ObjectMapper om;

    @Override
    public Map<String, Object> cancel(Map<String, Object> request) {
        return abortOrReverse(request, Transaction.CANCEL_TYPE_DEVICE);
    }
    
    @Override
    public Map<String, Object> reconcileRevoke(Map<String, Object> request) {
        return abortOrReverse(request, Transaction.CANCEL_TYPE_RECONCILE);
    }

    private Map<String, Object> abortOrReverse(Map<String, Object> request, String cancelType) {
        String terminalSn = (String)request.get(UpayService.TERMINAL_SN);
        String wosaiStoreId = (String)request.get(UpayService.WOSAI_STORE_ID);
        String clientSn= (String)request.get(UpayService.CLIENT_SN);
        String sn = (String)request.get(UpayService.SN);
        String operator = (String)request.get(UpayService.OPERATOR);
        Map<String,Object> extended = UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);

        Map<String, Object> basicParams = null;
        
        try{
            basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        }catch (TerminalStatusAbnormalException|StoreStatusAbnormalException|MerchantStatusAbnormalException e) {
            if(!Transaction.CANCEL_TYPE_RECONCILE.equals(cancelType )) {
                throw e;
            }
            Map<String, Object> order = repository.getOrderByOrderSn(null, sn);
            if(null == order) {
                throw e;
            }
            Map<String,Object> transaction = repository.getFreezeTransactionByOrderSn(BeanUtil.getPropString(order, Order.MERCHANT_ID), sn);
            if(null == transaction) {
                throw e;
            }
            Map<String,Object> configSnapshot = (Map<String, Object>) transaction.get(Transaction.CONFIG_SNAPSHOT);
            if(!(BeanUtil.getPropString(configSnapshot, TransactionParam.TERMINAL_SN).equals(terminalSn) 
                    || BeanUtil.getPropString(configSnapshot, TransactionParam.STORE_SN).equals(wosaiStoreId))) {
                throw e;
            }
            basicParams = CollectionUtil.hashMap(
                    Order.MERCHANT_ID, BeanUtil.getPropString(order, Order.MERCHANT_ID),
                    Order.STORE_ID, BeanUtil.getPropString(order, Order.STORE_ID),
                    Order.TERMINAL_ID, BeanUtil.getPropString(order, Order.TERMINAL_ID)
            );
        }
        
        // 商户限流
        if(basicParams.containsKey(TransactionParam.MERCHANT_SN)) {
            RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_DEPOSIT_CANCEL, Boolean.FALSE);
        }
        
        TransactionContext context = depositCancelProcessor.abortOrReverse(sn, clientSn, cancelType, basicParams, extended, operator);
        context.waitUntilShouldReturn(5000);
        handleContextException(context);
        return cancelResponse(context, !BeanUtil.getPropBoolean(basicParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
    }

    @Override
    public Map<String, Object> consume( Map<String, Object> request) {
        String terminalSn = (String)request.get(UpayService.TERMINAL_SN);
        String wosaiStoreId = (String)request.get(UpayService.WOSAI_STORE_ID);
        String clientSn= (String)request.get(UpayService.CLIENT_SN);
        String sn = (String)request.get(UpayService.SN);
        String consumeAmount = (String)request.get(CONSUME_AMOUNT);
        String operator = (String)request.get(UpayService.OPERATOR);
        Object extendedObj = request.get(UpayService.EXTENDED);
        Map<String,Object> extended = new HashMap<>();
        if (extendedObj != null) {
            extended = UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);
        }
        String notifyUrl = (String)request.get(NOTIFY_URL);
        Map<String, Object> extraParams = new HashMap<>();
        if(notifyUrl != null){
            extraParams.put(Transaction.NOTIFY_URL, notifyUrl);
        }
        Map<String,Object> profitSharing = (Map<String, Object>) request.get(PROFIT_SHARING);
        Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
        // 商户限流
        RateLimiterUtil.verification(BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_SN), MpayServiceProvider.OP_DEPOSIT_CONSUME, Boolean.FALSE);
        
        TransactionContext context = depositConsumeProcessor.consume(operator, sn, clientSn, extended ,consumeAmount, profitSharing, basicParams, extraParams);
        context.waitUntilReturn(5000);
        handleContextException(context);
        return consumeResponse(context, !BeanUtil.getPropBoolean(basicParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false));
    }
    
    private Map<String, Object> consumeResponse(TransactionContext context, boolean returnPayerInfo) {
        Map<String, Object> transaction = context.getTransaction();
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(context, returnPayerInfo);
        data.put(UpayService.PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(context.getOrder(), context.getTransaction()));

        if (transactionStatus == Transaction.STATUS_SUCCESS) {
            return UpayUtil.apiSuccess(UpayUtil.bizResponse("CONSUME_SUCCESS", null, null, null, data));
        }else if (transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_IN_PROG || transactionStatus == Transaction.STATUS_PRE_SUCCESS) {
            return UpayUtil.apiSuccess(UpayUtil.bizResponse("CONSUME_IN_PROGRESS", null, null, null, data));
        }else{
            UpayBizError error = UpayUtil.getBizError(transaction, MpayServiceProvider.OP_DEPOSIT_CONSUME);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse("CONSUME_ERROR", error.getName(), error.getStandardName(), error.getMessage(), data));
        }
    }

    public Map<String, Object> sync( Map<String, Object> request) {
        String terminalSn= (String)request.get(UpayService.TERMINAL_SN);
        String wosaiStoreId = (String)request.get(UpayService.WOSAI_STORE_ID);
        String clientSn = (String)request.get(UpayService.CLIENT_SN);
        String sn = (String)request.get(UpayService.SN);
        String merchantId = null;
        if(!StringUtil.empty(terminalSn) || !StringUtil.empty(wosaiStoreId)){
            Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
            merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        }
        if(StringUtil.empty(merchantId)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        Object extendedObj = request.get(UpayService.EXTENDED);
        Map<String,Object> extended = new HashMap<>();
        if (extendedObj != null) {
            extended = UpayUtil.formatExtended(request.get(UpayService.EXTENDED), om);
        }
        TransactionContext context = depositSyncProcessor.sync(merchantId, clientSn, sn, extended);
        context.waitUntilReturn(5000);
        handleContextException(context);
        return queryResponse(context.getOrder(), context.getTransaction(), context.getTransaction(), false);
    }
    
    private static void handleContextException(TransactionContext context) {
        if (context.getException() instanceof UpaySystemError) {
            throw (UpaySystemError)context.getException();
        }
    }
    
    private static Map<String, Object> cancelResponse(TransactionContext context, boolean returnPayerInfo) {
        Map<String, Object> transaction = context.getTransaction();
        int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
        int transactionStatus = BeanUtil.getPropInt(transaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(context, returnPayerInfo);

        if (transactionType == Transaction.TYPE_DEPOSIT_FREEZE) {
            if (transactionStatus == Transaction.STATUS_FAIL_CANCELED || transactionStatus == Transaction.STATUS_ABORTED) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse("DEPOSIT_CANCEL_ABORT_SUCCESS", null, null, null, data));
                                                                     
            }else if (transactionStatus == Transaction.STATUS_IN_PROG || transactionStatus == Transaction.STATUS_ERROR_RECOVERY || transactionStatus == Transaction.STATUS_ABORTING) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse("DEPOSIT_CANCEL_ABORT_IN_PROGRESS", null, null, null, data));

            }else if (transactionStatus == Transaction.STATUS_SUCCESS || transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_PRE_SUCCESS){
                throw new WorkflowInvalidState(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);

            }else {
                UpayBizError error = UpayUtil.getBizError(transaction, MpayServiceProvider.OP_CANCEL);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse("DEPOSIT_CANCEL_ABORT_ERROR", error.getName(), error.getStandardName(), error.getMessage(), data));
            }
        }else if (transactionType == Transaction.TYPE_DEPOSIT_CANCEL) {
            if (transactionStatus == Transaction.STATUS_SUCCESS) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse("DEPOSIT_CANCEL_SUCCESS", null, null, null, data));
            }else if (transactionStatus == Transaction.STATUS_CREATED || transactionStatus == Transaction.STATUS_IN_PROG || transactionStatus == Transaction.STATUS_PRE_SUCCESS) {
                return UpayUtil.apiSuccess(UpayUtil.bizResponse("DEPOSIT_CANCEL_IN_PROGRESS", null, null, null, data));
            }else{
                UpayBizError error = UpayUtil.getBizError(transaction, MpayServiceProvider.OP_CANCEL);
                return UpayUtil.apiSuccess(UpayUtil.bizResponse("DEPOSIT_CANCEL_ERROR", error.getName(), error.getStandardName(), error.getMessage(), data));
            }
        }else {
            throw new WorkflowInvalidState(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
    }
    
    @Override
    public Map<String, Object> fix(Map<String, Object> request) {
        String sn = (String)request.get(UpayService.SN);
        String terminalSn= (String)request.get(UpayService.TERMINAL_SN);
        String wosaiStoreId = (String)request.get(UpayService.WOSAI_STORE_ID);
        String merchantId = null;
        if(!StringUtil.empty(terminalSn) || !StringUtil.empty(wosaiStoreId)){
            Map<String, Object> basicParams = facade.getBasicParams(wosaiStoreId, terminalSn);
            merchantId = BeanUtil.getPropString(basicParams, TransactionParam.MERCHANT_ID);
        }
        if(StringUtil.empty(merchantId)) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        TransactionContext context = depositFixProcessor.fixFreeze(sn, merchantId);

        return fixResponse(context, true);
    }
   
    private static Map<String, Object> fixResponse(TransactionContext context, boolean returnPayerInfo) {
        return queryResponse(context.getOrder(), context.getTransaction(), context.getTransaction(), returnPayerInfo);
    }
    
    private static Map<String, Object> queryResponse(Map<String, Object> order, Map<String, Object> latestTransaction, Map<String, Object> payTransaction, boolean returnPayerInfo) {
        int transactionType = BeanUtil.getPropInt(latestTransaction, Transaction.TYPE);
        int transactionStatus = BeanUtil.getPropInt(latestTransaction, Transaction.STATUS);
        Map<String, Object> data = TransactionResponder.makeResponseData(order, latestTransaction, returnPayerInfo);
        data.put(UpayService.PAYMENT_LIST, PaymentUtil.buildPaymentListForQueryAndPayAndRefundResponse(order, latestTransaction));

        String op = UpayUtil.getOpByTransType(transactionType);
        if (Transaction.notFailed(transactionStatus)) {
            String errStandardName = null;
            String errMessage = null;
            if (transactionStatus == Transaction.STATUS_IN_PROG){
                if (BeanUtil.getNestedProperty(latestTransaction,Transaction.BIZ_ERROR_CODE + "."+ op) !=null){
                    UpayBizError  error = UpayUtil.getBizError(latestTransaction, op);
                    errStandardName = error.getStandardName();
                }else{
                        Map configInfo = SceneConfigFacade.getWosaiErrorDefinition(UpayErrorScenesConstant.ORDER_IN_PROG, null);
                    errStandardName = BeanUtil.getPropString(configInfo, ApolloConfigurationCenterUtil.ERROR_CODE_STANDARD);
                }
            }
            return UpayUtil.apiSuccess(UpayUtil.bizResponse("SUCCESS", null, errStandardName, errMessage, data));
        } else {
            UpayBizError error = UpayUtil.getBizError(latestTransaction, op);
            return UpayUtil.apiSuccess(UpayUtil.bizResponse("SUCCESS", error.getName(), error.getStandardName(), error.getMessage(), data));
        }
    }
    
    @Override
    public Map<String, Object> fixConsumeToSuccess(Map<String, Object> request) {
        String sn = (String)request.get(UpayService.SN);
        String tsn = (String)request.get(UpayService.TSN);
        List<Map<String,Object>> refundChannelPayments = (List<Map<String, Object>>) request.get(REFUND_CHANNEL_PAYMENTS);
        return depositFixProcessor.fixConsumeToSuccess(sn, tsn, refundChannelPayments);
    }

    @Override
    public Map<String, Object> fixOrderIfConsumeNotSuccess(Map<String, Object> request) {
        return depositFixProcessor.fixOrderIfConsumeNotSuccess((String)request.get(UpayService.SN));
    }

    @Override
    public Map<String, Object> fixCancel(Map<String, Object> request) {
        return depositFixProcessor.fixCancel((String)request.get(UpayService.SN));
    }

    @Override
    public Map<String, Object> fixOrderIfCancelNotSuccess(Map<String, Object> request) {
        return depositFixProcessor.fixOrderIfCancelNotSuccess((String)request.get(UpayService.SN));
    }
}
