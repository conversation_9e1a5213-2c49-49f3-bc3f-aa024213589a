package com.wosai.upay.service;

import com.wosai.mpay.util.CipherUtils;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.transaction.util.CryptoUtil;
import com.wosai.upay.util.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
public class CryptoService {
    private final static Logger logger = LoggerFactory.getLogger(CryptoService.class);

    public static final String PUBLIC_KEY_ID = "c0c0a9e2-39bb-4c75-86f1-438923c75189";

    public static final String PRIVATE_KEY_ID = "e1629a2b-aa53-4456-b432-f1db5d625907";
    @Value("${deposit.reflect.rsa_key.id}")
    private String depositRsaId;
    @Autowired
    private ExternalServiceFacade serviceFacade;

    public String encrypt(String message) {
        String publicKey = serviceFacade.getRsaKeyDataById(PUBLIC_KEY_ID);
        if (StringUtils.isBlank(publicKey)) {
            throw new UpayBizException("公钥未配置");
        }
        try {
            return CipherUtils.rsaEncryptOAEP(message, StandardCharsets.UTF_8.name(),
                    Base64.decode(publicKey));
        } catch (Exception e) {
            logger.error("加密失败. message:{}", message, e);
            throw new UpayBizException("加密失败", e);
        }
    }

    public String decrypt(String message) {
        String privateKey = serviceFacade.getRsaKeyDataById(PRIVATE_KEY_ID);
        if (StringUtils.isBlank(privateKey)) {
            throw new UpayBizException("私钥未配置");
        }
        try {
            return CipherUtils.rsaDecryptOAEP(message, StandardCharsets.UTF_8.name(), Base64.decode(privateKey));
        } catch (Exception e) {
            logger.error("解密失败. message:{}", message, e);
            throw new UpayBizException("解密失败", e);
        }
    }

    public String encryptDepositCellphone(String cellphone) {
        if (StringUtils.isEmpty(cellphone)) {
            return cellphone;
        }
        String rsaKey = serviceFacade.getRsaKeyDataById(depositRsaId);
        return CryptoUtil.encrypt(cellphone, rsaKey);
    }
}
