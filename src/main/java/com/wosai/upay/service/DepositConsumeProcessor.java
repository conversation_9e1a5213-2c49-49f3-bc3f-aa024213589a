package com.wosai.upay.service;


import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.*;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

@Service
public class DepositConsumeProcessor {
    private static final Logger logger = LoggerFactory.getLogger(DepositConsumeProcessor.class);
    @Autowired
    private TsnGenerator tsnGenerator;
    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private ProviderManager providerManager;
    @Autowired
    private GatewaySupportService gatewaySupportService;
    @Autowired
    private UpayOrderService upayOrderService;
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private DistributedLock distributedLock;

    private final static List<String> CONSUME_COPY_EXTRA_PARAMS = Arrays.asList(Transaction.SQB_REFUND_FLAG, Transaction.PROFIT_SHARING, Transaction.NOTIFY_URL, Transaction.BARCODE, Transaction.POI, Transaction.SQB_STATION, Transaction.CLIENT_IP, Transaction.SQB_PRODUCT_CODE);
    @Autowired
    private FeeRateProcessor feeRateProcessor;
    /**
     *
     * @param operator
     * @param sn
     * @param clientSn
     * @param consumeAmount
     * @param basicParams
     * @return
     */
    @Transactional(isolation=Isolation.REPEATABLE_READ)
    public TransactionContext consume(String operator, String sn, String clientSn, Map<String,Object> extended,String consumeAmount, Map<String, Object> profitSharing, Map<String, Object> basicParams,Map<String,Object> requestExtraParams) {
        String merchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(basicParams, TransactionParam.STORE_ID);
        String terminalId = MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID);
        String terminalSn = MapUtil.getString(basicParams, TransactionParam.TERMINAL_SN);
        boolean isDBTrade = true;
        boolean transactionUseOrderTerminalInfo = false;
        if(StringUtil.empty(terminalId)){
            transactionUseOrderTerminalInfo = true;
        }
        Map<String, Object> order = repository.getOrder(merchantId, storeId, sn, clientSn);
        if (order == null) {
            order = upayOrderService.getOrderBySn(merchantId, sn, clientSn);
            if(null == order) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            isDBTrade = false;
        }
        
        Map<String, Object> freezeTransaction ;
        if(isDBTrade) {
            freezeTransaction = repository.getFreezeTransactionByOrderSn(merchantId, BeanUtil.getPropString(order, Order.SN));
        }else {
            freezeTransaction = gatewaySupportService.getTransactionByClientTsn(merchantId, BeanUtil.getPropString(order, Order.SN), BeanUtil.getPropString(order, Order.CLIENT_SN), MapUtil.getLongValue(order, DaoConstants.CTIME));
        }
        
        if(null == freezeTransaction) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        int provider = MapUtil.getIntValue(freezeTransaction, Transaction.PROVIDER);
        int payway = MapUtil.getIntValue(freezeTransaction, Transaction.PAYWAY);

        //刷卡类型的预授权是与富友通道交互的，此类型的交易并不会有手续费通知。
        if (provider == Order.PROVIDER_FUYOU && !Objects.equals(payway, Payway.BANKCARD.getCode())) {
            //富友预授权完成需要等待预授权冻结回调通知处理成功，因为手续费要以富友侧为准
            boolean isFeeUpdateFlag = BeanUtil.getPropBoolean(freezeTransaction, Transaction.KEY_IS_FEE_UPDATE_FLAG);
            if (!isFeeUpdateFlag) {
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_CONSUME_ERROR, UpayErrorScenesConstant.ORDER_CAN_NOT_CONSUME_ERROR_MESSAGE);
            }
        }
        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), BeanUtil.getPropInt(order, Order.SUB_PAYWAY));

        String orderId = BeanUtil.getPropString(order, DaoConstants.ID);
        String orderSn = BeanUtil.getPropString(order, Order.SN);
        String orderTerminalId = BeanUtil.getPropString(order, Order.TERMINAL_ID);
        String orderStoreId = BeanUtil.getPropString(order, Order.STORE_ID);
        clientSn = BeanUtil.getPropString(order, Order.CLIENT_SN);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        // 银行卡预授权验证
        ImmutablePair<Boolean, TransactionContext> bankCardConfrim = bankcardConfrim(merchantId, orderSn, consumeAmount, order, freezeTransaction, isDBTrade);
        if (bankCardConfrim.getLeft()) {
            return bankCardConfrim.getValue();
        }
        if (orderStatus == Order.STATUS_DEPOSIT_FREEZED) {
            long originalAmount = consumeAmount!=null?Long.parseLong(consumeAmount):BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL);
            long effectiveTotal = BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL);
            
            if(originalAmount > effectiveTotal){
                throw new RefundableAmountNotEnough(UpayErrorScenesConstant.DEPOSIT_CONSUME_AMOUNT_ORDER_NOOP, UpayErrorScenesConstant.DEPOSIT_CONSUME_AMOUNT_ORDER_NOOP_MESSAGE);
            }
            
            String tsn = tsnGenerator.nextSn();
            String transactionId = UpayUtil.getTransactionIdBySn(tsn);
            
            Map extraOutFields = (Map)freezeTransaction.get(Transaction.EXTRA_OUT_FIELDS);
            if(null == extraOutFields) {
                extraOutFields = new HashMap();
            }
            extraOutFields.put(Transaction.ORDER_INFO, CollectionUtil.hashMap(Order.ORIGINAL_TOTAL, order.get(Order.ORIGINAL_TOTAL),
                                                                            Order.EFFECTIVE_TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                                                                            DaoConstants.CTIME, order.get(DaoConstants.CTIME),
                                                                            Order.TRADE_NO, order.get(Order.TRADE_NO)
            ));
            //删除冻结带过来的手续费回调成功标识
            extraOutFields.remove(Transaction.IS_FEE_UPDATE_FLAG);
            if(Order.PAYWAY_ALIPAY2 == BeanUtil.getPropInt(freezeTransaction, Transaction.PAYWAY)) {
                Map<String, Object> lastTransaction = null;
                if(isDBTrade) {
                    lastTransaction = repository.getLatestTransactionByOrderSn(merchantId, orderSn);
                }else {
                    lastTransaction = gatewaySupportService.getLatestTransactionByOrderSn(merchantId, orderSn, MapUtil.getLongValue(order, DaoConstants.CTIME));
                }
                // 支付宝完成失败后，可以变更金额，但需要变更订单号。因为hbase接口查询不出真正的最后一笔记录（没做排序），没法通过数据来做区分（需要拿到最后一次的上送预授权完成单号），所以加了下面的金额限制
                if(MapUtil.getIntValue(lastTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                    if(MapUtil.getLongValue(lastTransaction, Transaction.ORIGINAL_AMOUNT) != originalAmount) {
                        throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_CONSUME_AMOUNT, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_CONSUME_AMOUNT_MESSAGE);
                    }
                }
            }
            Map<String,Object> freezeExtraParams = MapUtil.getMap(freezeTransaction, Transaction.EXTRA_PARAMS);
            Map<String,Object> extraParams = BeanUtil.getPart(freezeExtraParams, CONSUME_COPY_EXTRA_PARAMS);
            // 拷贝参数到流水
            UpayUtil.copyIfExists(freezeExtraParams, extraParams, Transaction.COPY_EXTRA_PARAMS_KEYS);
            extraParams.putAll(requestExtraParams);


            @SuppressWarnings("unchecked")
            Map<String, Object> consumeTransaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                    Transaction.TSN, tsn,
                    Transaction.CLIENT_TSN, BeanUtil.getPropString(freezeTransaction, Transaction.CLIENT_TSN) + "-DC",
                    Transaction.TYPE, Transaction.TYPE_DEPOSIT_CONSUME,
                    Transaction.SUBJECT, order.get(Order.SUBJECT),
                    Transaction.BODY, order.get(Order.BODY),
                    Transaction.STATUS, Transaction.STATUS_CREATED,
                    Transaction.ORIGINAL_AMOUNT, originalAmount,
                    Transaction.EFFECTIVE_AMOUNT, originalAmount,
                    Transaction.MERCHANT_ID, merchantId,
                    Transaction.STORE_ID, transactionUseOrderTerminalInfo ? orderStoreId : storeId,
                    Transaction.TERMINAL_ID, transactionUseOrderTerminalInfo ? orderTerminalId : terminalId,
                    Transaction.PROVIDER, order.get(Order.PROVIDER),
                    Transaction.PAYWAY, order.get(Order.PAYWAY),
                    Transaction.SUB_PAYWAY, order.get(Order.SUB_PAYWAY),
                    Transaction.ORDER_ID, orderId,
                    Transaction.ORDER_SN, orderSn,
                    Transaction.OPERATOR, operator,
                    Transaction.PRODUCT_FLAG, freezeTransaction.get(Transaction.PRODUCT_FLAG),
                    Transaction.CONFIG_SNAPSHOT, freezeTransaction.get(Transaction.CONFIG_SNAPSHOT),
                    Transaction.BUYER_LOGIN, freezeTransaction.get(Transaction.BUYER_LOGIN),
                    Transaction.BUYER_UID, freezeTransaction.get(Transaction.BUYER_UID),
                    Transaction.ITEMS, freezeTransaction.get(Transaction.ITEMS),
                    Transaction.EXTRA_OUT_FIELDS, extraOutFields,
                    Transaction.EXTRA_PARAMS, extraParams,
                    Transaction.BUYER_UID, freezeTransaction.get(Transaction.BUYER_UID),
                    Transaction.BUYER_LOGIN, freezeTransaction.get(Transaction.BUYER_LOGIN),
                    Transaction.REFLECT, freezeTransaction.get(Transaction.REFLECT),
                    Transaction.EXTENDED_PARAMS, extended);
            fixTermId(terminalSn, consumeTransaction);
            UpayServiceImpl.setProfitSharing(facade, consumeTransaction, profitSharing, null, true);
            MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(consumeTransaction);

            // 免密支付特殊处理：免密支付必须要等上一次扣款明确失败后才能继续扣款，否则会重复扣款
            if(serviceProvider instanceof ChinaumsEPayServiceProvider) {
                Map<String, Object> lastTransaction = repository.getLatestTransactionByOrderSn(merchantId, orderSn);
                if(lastTransaction != null && MapUtil.getIntValue(lastTransaction, Transaction.TYPE) == Transaction.TYPE_DEPOSIT_CONSUME) {
                    TransactionContext context = workflowManager.spawnTransactionContext(null, order, lastTransaction, serviceProvider);
                    String status = serviceProvider.depositQuery(context);
                    if(status == Workflow.RC_PAY_SUCCESS) {
                        Workflow workflow = context.getWorkflow();
                        lastTransaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
                        workflow.finish(context);
                        return context;
                    }else if(status == Workflow.RC_IN_PROG || status != Workflow.RC_TRADE_CANCELED){
                        // 订单状态处于支付中或调用接口异常，返回错误不处理
                        throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
                    }
                }
            }
            UpayUtil.configAlipayV2AuthInfo(serviceProvider, facade, consumeTransaction
                    , storeId, MapUtil.getLongValue(order, DaoConstants.CTIME));
            Map<String,Object> tradeParams = serviceProvider.getTradeParams(consumeTransaction);
            //优惠额度费率
            Integer providerCode = serviceProvider.getProvider();
            boolean isQuotaFeeRate = feeRateProcessor.quotaFeeRate(tradeParams, consumeTransaction, providerCode);

            boolean needCalculateFee = isNeedCalculateFee(order, consumeTransaction, tradeParams, isQuotaFeeRate);
            if(needCalculateFee){
                Long fee = FeeUtil.calculateDepositConsumeFee(consumeTransaction);
                if(serviceProvider instanceof DirectWeixinServiceProvider 
                        || serviceProvider instanceof DirectWeixinWapOrMiniServiceProvider) {
                    long calFee = FeeUtil.calculateWeixinDepositConsumeFee(freezeTransaction, consumeTransaction);
                    // 如果计算出来的差异值过大，还是使用原先计算出来的手续费
                    if(Math.abs(fee - calFee) <= ApolloConfigurationCenterUtil.getConsumeFeeMaxDifferenceValue()) {
                        fee = calFee;
                    }
                }
                tradeParams.put(TransactionParam.FEE, fee);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                    Order.MERCHANT_ID, merchantId,
                    Order.STATUS, Order.STATUS_DEPOSIT_CONSUME_INPROGRESS,
                    DaoConstants.VERSION, order.get(DaoConstants.VERSION));
            if(isDBTrade) {
                try {
                    repository.getOrderDao().updatePart(orderUpdate);
                }catch (DaoVersionMismatchException e) {
                    throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
                }
            }else {
                extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CONSUME, Boolean.TRUE);
                order.putAll(orderUpdate);
                order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                order.put(DaoConstants.MTIME, System.currentTimeMillis());
                try {
                    upayOrderService.updateOrder(order);
                }catch (Exception e) {
                    logger.error("error in update order hbase data, data = {}, ex = {}", order, e);
                }
            }
            facade.resetSignParams(serviceProvider, consumeTransaction);
            repository.getTransactionDao().save(consumeTransaction);

            Map<String, Object> configSnapshot = (Map)freezeTransaction.get(Transaction.CONFIG_SNAPSHOT);
            String terminalOrStoreSn = (null == freezeTransaction.get(Transaction.TERMINAL_ID)) ? MapUtil.getString(configSnapshot, TransactionParam.STORE_SN) : MapUtil.getString(configSnapshot, TransactionParam.TERMINAL_SN);
            TransactionContext context = workflowManager.startWorkflow(null, terminalOrStoreSn, order, consumeTransaction);
            return context;
        }else if(orderStatus == Order.STATUS_DEPOSIT_CONSUMED){
            throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_ALEADY_CONSUME, UpayErrorScenesConstant.DEPOSIT_CONSUME_ALEADY_CONSUME_MESSAGE);
        } else if (orderStatus == Order.STATUS_DEPOSIT_CANCELED) {
            throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CANCELED_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CANCELED_ORDER_STATE_MESSAGE);
        } else {
            throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
        }

    }

    private boolean isNeedCalculateFee(Map<String, Object> order, Map<String, Object> consumeTransaction, Map<String, Object> tradeParams, boolean isQuotaFeeRate) {
        if (isQuotaFeeRate) {
            return true;
        }
        boolean isFullConsume = BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL) == BeanUtil.getPropLong(consumeTransaction, Transaction.ORIGINAL_AMOUNT);
        boolean needCalculateFee = true;
        if(isFullConsume && tradeParams.containsKey(TransactionParam.FEE)){
            needCalculateFee = false;
        }
        return needCalculateFee;
    }

    /**
     * 预授权完成场景下，原做预授权时候的终端，有可能会被解绑了，解绑后原交易里面的term_id 在银联已经被解绑了，完成的时候就会报 终端已注销 错误，故用完成的终端对应的term_id上送通道
     * @param terminalSn 预授权完成时的终端
     * @param consumeTransaction
     */
    private void fixTermId(String terminalSn, Map<String,Object> consumeTransaction){
        if(terminalSn == null){
            return;
        }
        Map configSnapshot = MapUtil.getMap(consumeTransaction, Transaction.CONFIG_SNAPSHOT);
        try {
            int payway = MapUtil.getIntValue(consumeTransaction, Transaction.PAYWAY);
            int subPayway = MapUtil.getIntValue(consumeTransaction, Transaction.SUB_PAYWAY);
            String tradeApp = MapUtil.getString(configSnapshot, TransactionParam.TRADE_APP, TransactionParam.TRADE_APP_BASIC_PAY);
            Map<String, Object> config = facade.getAllParamsWithTradeApp(null, terminalSn, payway, subPayway, tradeApp);
            //更新终端信息
            Object newTermId = MapUtil.getString(config, TransactionParam.TRADE_EXT_TERM_ID);
            Object newTermInfo = MapUtil.getMap(config, TransactionParam.TRADE_EXT_TERM_INFO);
            if((newTermId != null || newTermInfo != null) && Objects.equals(MapUtil.getString(configSnapshot, TransactionParam.PROVIDER), MapUtil.getString(config, TransactionParam.PROVIDER))){
                configSnapshot.put(TransactionParam.TRADE_EXT_TERM_ID, newTermId);
                configSnapshot.put(TransactionParam.TRADE_EXT_TERM_INFO, newTermInfo);
            }
        }catch (Throwable t){
            logger.error("fix term id error " + t.getMessage(), t);
        }
    }

    private ImmutablePair<Boolean, TransactionContext> bankcardConfrim(String merchantId, String orderSn, String consumeAmount, Map<String, Object> order, Map<String, Object> freezeTransaction , boolean isDbTrade){
        if (MapUtil.getIntValue(order, Transaction.PAYWAY) == Order.PAYWAY_BANKCARD) {
            // 银行卡预授权是由设备发起，需要确认订单状态后才能进行完成
            if (MapUtil.getIntValue(freezeTransaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
                throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
            }
            if (MapUtil.getIntValue(order, Order.STATUS) == Order.STATUS_DEPOSIT_CONSUME_ERROR) {
                Map<String, Object> lastTransaction = repository.getLatestTransactionByOrderSn(merchantId, orderSn);
                if (lastTransaction == null) {
                    return ImmutablePair.of(false, null);
                }
                long originalAmount = consumeAmount!=null?Long.parseLong(consumeAmount):MapUtil.getLongValue(order, Order.EFFECTIVE_TOTAL);
                if (originalAmount != MapUtil.getLongValue(lastTransaction, Transaction.EFFECTIVE_AMOUNT)) {
                    throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
                }
                // 订单号已被其它服务修改，当前任务不执行
                if(distributedLock.isNotCanOrderFix(orderSn)) {
                    throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
                }
                MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(lastTransaction);
                TransactionContext context = workflowManager.spawnTransactionContext(null, order, lastTransaction, serviceProvider);
                String rc = serviceProvider.depositQuery(context);
                Map<String, Object> orderUpdate = MapUtil.hashMap(DaoConstants.ID, order.get(DaoConstants.ID),
                        Transaction.MERCHANT_ID, order.get(Transaction.MERCHANT_ID),
                        DaoConstants.VERSION, order.get(DaoConstants.VERSION)
                    );
                if (!isDbTrade) {
                    Map extraOutFields = (Map)lastTransaction.get(Transaction.EXTRA_OUT_FIELDS);
                    if (extraOutFields == null) {
                        extraOutFields = new HashMap<>();
                        lastTransaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
                    }
                    extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CONSUME, Boolean.TRUE);
                }
                Consumer<Map<String, Object>> orderUpdateCs = (update) -> {
                    try {
                        if (isDbTrade) {
                            repository.doInTransaction(() -> {
                                repository.getOrderDao().updatePart(orderUpdate);
                            });
                            order.putAll(orderUpdate);
                            order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                            order.put(DaoConstants.MTIME, System.currentTimeMillis());

                        } else {
                            order.putAll(orderUpdate);
                            order.put(DaoConstants.VERSION, MapUtil.getIntValue(order, DaoConstants.VERSION, 1) + 1);
                            order.put(DaoConstants.MTIME, System.currentTimeMillis());
                            upayOrderService.updateOrder(order);
                        }
                    } catch (DaoVersionMismatchException e) {
                        throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
                    }
                };
                if (Workflow.RC_CONSUME_SUCCESS.equals(rc)) {
                    // 订单已处理成功，按照成功处理，提交任务成功后返回报错信息
                    context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
                    workflowManager.raise(context, Workflow.RC_CONSUME_SUCCESS);
                    throw new UpayDepositOrderError(UpayErrorScenesConstant.DEPOSIT_CONSUME_ALEADY_CONSUME, UpayErrorScenesConstant.DEPOSIT_CONSUME_ALEADY_CONSUME_MESSAGE);

                } else if (Workflow.RC_RETRY.equals(rc)) {
                    // 订单仍处于处理中，修改当前订单状态，设备上重新发起下单
                    orderUpdate.put(Order.STATUS, Order.STATUS_DEPOSIT_CONSUME_INPROGRESS);
                    lastTransaction.put(Transaction.STATUS, Transaction.STATUS_IN_PROG);
                    orderUpdateCs.accept(orderUpdate);
                    context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_IN_PROG));
                    workflowManager.raise(context, Workflow.RC_IN_PROG);
                    return ImmutablePair.of(true, context);

                } else if (Workflow.RC_TRADE_DISCARD.equals(rc)) {
                    // 订单已明确失败，将之前订单变更为失败，重新走撤销流程
                    orderUpdate.put(Order.STATUS, Order.STATUS_DEPOSIT_FREEZED);
                    orderUpdateCs.accept(orderUpdate);
                    return ImmutablePair.of(false, null);
                } else {
                    throw new UpayCancelOrderStateError(UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE);
                }
            }
        }
        return ImmutablePair.of(false, null);
    }
}
