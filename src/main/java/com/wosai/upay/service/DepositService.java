package com.wosai.upay.service;

import com.wosai.upay.helper.UpayServiceAnnotation;
import com.wosai.upay.validation.*;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

@UpayServiceAnnotation
@Validated
public interface DepositService {
    String IS_DEPOSIT = "is_deposit";
    String CONSUME_REQUEST_NO = "consume_request_no";
    String CONSUME_AMOUNT = "consume_amount";
    String NOTIFY_URL = "notify_url";

    String REFUND_CHANNEL_PAYMENTS = "refund_channel_payments";

    Map<String, Object> cancel(@PropNotBothNull.List({
                                    @PropNotBothNull(value=UpayService.WOSAI_STORE_ID, value2=UpayService.TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                    @PropNotBothNull(value=UpayService.CLIENT_SN, value2=UpayService.SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize.List({
                                       @PropSize(value=UpayService.CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符"),
                                       @PropSize(value=UpayService.OPERATOR,  max=45, message="{value}收银员不可超过{max}字符")
                               })
                               Map<String, Object> request);

    Map<String, Object> consume(@PropNotBothNull.List({
                                    @PropNotBothNull(value=UpayService.WOSAI_STORE_ID, value2=UpayService.TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                    @PropNotBothNull(value=UpayService.CLIENT_SN, value2=UpayService.SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize.List({
                                   @PropSize(value=UpayService.CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符"),
                                   @PropSize(value=UpayService.OPERATOR, max=45, message="{value}收银员不可超过{max}字符"),
                               })
                               @PropPattern(value=CONSUME_AMOUNT, nullable=false, regex="\\d{1,10}", message="{value}金额为整数，长度不超过10位，以分为单位")
                               @PropMin(value = CONSUME_AMOUNT, min=1, message="{value}金额必须大于0")
                               Map<String, Object> request);

    Map<String, Object> reconcileRevoke(@PropNotBothNull.List({
                                    @PropNotBothNull(value=UpayService.WOSAI_STORE_ID, value2=UpayService.TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                    @PropNotBothNull(value=UpayService.CLIENT_SN, value2=UpayService.SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                               })
                               @PropSize(value=UpayService.CLIENT_SN, min=1, max=32, message="{value}商户订单号不可超过{max}字符")
                               Map<String, Object> request);

    Map<String, Object> sync(@PropNotBothNull.List({
                                @PropNotBothNull(value=UpayService.WOSAI_STORE_ID, value2=UpayService.TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                                @PropNotBothNull(value=UpayService.CLIENT_SN, value2=UpayService.SN, message="{value}商户订单号和{value2}喔噻订单号不能同时为空")
                           })
                           Map<String, Object> request);

    Map<String, Object> fix(Map<String, Object> params);
    
    /**
     * 预授权完成恢复成功，用于处理我们这边记录为预授权完成错误，第三方通道是成功的情况。
     * 内部接口 不对外提供
     * @param request
     */
    Map<String, Object> fixConsumeToSuccess(@PropNotEmpty.List({
                                    @PropNotEmpty(value = UpayService.SN, message = "{value}订单号不可为空"),
                                    @PropNotEmpty(value = UpayService.TSN, message = "{value}预授权完成流水号不可为空")
                               })
                               Map<String, Object> request);
    
    /**
     * 修改订单状态为订单的正常状态 已支付或者已部分退款, 用于处理第三方退款失败后，我们系统的订单状态记录为退款错误的情况。
     * 当部分退款，如果上一笔退款失败原因是未知错误，那么，系统暂时不允许继续退款，需要人工确认退款结果，然后修改订单状态
     * 才能继续退款， 提供此接口， 避免直接操作数据库修改订单状态
     * 内部接口 不对外提供
     * @param request
     */
    Map<String, Object> fixOrderIfConsumeNotSuccess(@PropNotEmpty(value = UpayService.SN, message = "{value}订单号不可为空")
                                Map<String, Object> request);

    /**
     * 预授权撤销订单勾兑为预授权撤销成功
     * 内部接口，不对外提供
     * @param request
     */
    Map<String, Object> fixCancel(Map<String, Object> params);

    /**
     * 预授权撤销订单勾兑为预授权冻结
     * 内部接口，不对外提供
     * @param params
     */
    Map<String, Object> fixOrderIfCancelNotSuccess(Map<String, Object> params);

    
}
