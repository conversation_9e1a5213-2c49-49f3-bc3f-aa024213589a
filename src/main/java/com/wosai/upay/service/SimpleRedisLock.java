package com.wosai.upay.service;

import com.wosai.data.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by jianfree on 17/10/16.
 * 基于redis实现的简易分布式锁
 * 复杂的实现可参考 http://redis.io/topics/distlock
 *
 */
@Service
public class SimpleRedisLock{
    public static final Logger logger = LoggerFactory.getLogger(SimpleRedisLock.class);
    public static final String LOCK_KEY_PREFIX = "lock.";
    public static final String LOCK_KEY_HISTORY_REFUND_PREFIX = "history.refund.";

    @Autowired
    private RedisTemplate redisTemplate;

    private ScheduledExecutorService executorService = Executors.newScheduledThreadPool(2);




    /**
     * @see http://redis.io/commands/set
     * @param resourceName
     * @param time
     * @param unit
     * @return
     */
    public boolean tryLock(final String resourceName, final String resourceValue, final long time, final TimeUnit unit){
        final String lockKey = LOCK_KEY_PREFIX + resourceName;
        redisTemplate.execute(new RedisCallback() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                connection.set(lockKey.getBytes(), resourceValue.getBytes(), Expiration.from(time, unit), RedisStringCommands.SetOption.SET_IF_ABSENT);
                return null;
            }
        });
        String value = (String) redisTemplate.opsForValue().get(lockKey);
        if(resourceValue.equals(value)){
            return true;
        }else{
            return false;
        }
    }

    /**
     * @see http://redis.io/commands/set
     *
     */
    public void unlock(String resourceName, String resourceValue) {
        unlock(resourceName, resourceValue, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 延迟unlock
     * @param delayTime
     * @param unit
     * @return
     */
    public void unlock(final String resourceName, final String resourceValue, long delayTime, TimeUnit unit){
        final String lockKey = LOCK_KEY_PREFIX + resourceName;
        if(StringUtil.empty(lockKey)){
            return;
        }
        if(delayTime <= 0){
            doUnlock(lockKey, resourceValue);
        }else{
            executorService.schedule(new Runnable() {
                @Override
                public void run() {
                   doUnlock(lockKey, resourceValue);
                }
            }, delayTime, unit);
        }

    }

    private void doUnlock(final String lockKey, final String resourceValue){
        String value = (String) redisTemplate.opsForValue().get(lockKey);
        if(resourceValue.equals(value)){
            redisTemplate.delete(lockKey);
        }
    }


}
