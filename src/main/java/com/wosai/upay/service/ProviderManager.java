package com.wosai.upay.service;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.springframework.stereotype.Service;

import com.wosai.upay.exception.ManagementException;
import com.wosai.upay.exception.ProviderStatusException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.RateLimiterUtil;

@Service
public class ProviderManager {
    public Map<String, Map<String, Object>> status() {
        return RateLimiterUtil.getProviderRateLimiter();
    }

    public Map<String, Map<String, Object>> update(Map<String, Map<String, Object>> update) throws ManagementException {
        for (String provider: update.keySet()) {
            RateLimiterUtil.getProviderRateLimiter().put(provider, update.get(provider));
        }
        return status();
    }

    /**
     * 
     * 对支付通道下的支付方式和支付通道的限流
     * 
     * @param provider 支付通道，直连为0
     * @param payway
     * @param subPayway
     * @throws UpayBizException
     */
    public void verify(int provider, int payway, int subPayway) throws UpayBizException {
        String paywayKey = StringUtil.join(ApolloConfigurationCenterUtil.DEFAULT_REGEX_KEY, provider, payway);
        List<String> verifyKeys = Arrays.asList(StringUtil.join(ApolloConfigurationCenterUtil.DEFAULT_REGEX_KEY, paywayKey, subPayway), paywayKey);
        for (String verify : verifyKeys) {
            if (!status().containsKey(verify)) {
                //throw new InvalidPaywayException("暂时不支持这种支付通道", provider);
                //没有明确配置，默认允许。
                continue;
            }
            int allowPct = Integer.parseInt(status().get(verify).get("percentage").toString());
            Map<String,Object> config = status().get(verify);
            if (allowPct >= 100) {
                return;
            } else if (allowPct <= 0) {
                throw new ProviderStatusException(UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMIT, BeanUtil.getPropString(config, "message", UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMIT_MESSAGE));
            } else {
                if (throwCoin(allowPct)) {
                    return;
                }else{
                    throw new ProviderStatusException(UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING, BeanUtil.getPropString(config, "message", UpayErrorScenesConstant.UPAY_PROVIDER_STATUS_LIMITING_MESSAGE));
                }
            }
        }
    }
    
    private static Random random = new Random();
    private static boolean throwCoin(int headPct) {
        int randInt = random.nextInt(100);
        if (randInt < headPct) {
            return true;
        }else{
            return false;
        }
    }
    
}
