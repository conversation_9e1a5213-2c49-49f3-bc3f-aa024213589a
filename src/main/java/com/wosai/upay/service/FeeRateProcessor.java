package com.wosai.upay.service;

import com.google.common.collect.ImmutableList;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.service.DiscountQuotaService;
import com.wosai.trade.service.activity.response.DiscountQuotaResponse;
import com.wosai.trade.service.enums.QuotaActivityScenesTypeEnum;
import com.wosai.trade.service.request.PreDeductDiscountQuotaRequest;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.FeeUtil;
import com.wosai.upay.util.UpayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Description: 费率处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/8/8
 */
@Component
public class FeeRateProcessor {

    private static final Logger logger = LoggerFactory.getLogger(FeeRateProcessor.class);
    @Resource
    private DiscountQuotaService quotaService;
    @Resource
    private GatewaySupportService gatewaySupportService;
    @Resource
    private DataRepository repository;

    public static final String PAY_SOURCE_SQB_NPOS = "sqb_npos";

    public static final List<Integer> QUOTA_TYPE_PAYMENT_LIST = ImmutableList.of(Transaction.TYPE_DEPOSIT_CONSUME, Transaction.TYPE_PAYMENT);

    /**
     * 在交易完成后才需要处理额度包优惠的通道列表
     */
    public static final List<Integer> PROVIDERS_OF_PROCESS_QUOTA_AFTER_FINISHED = ImmutableList.of(Order.PROVIDER_FUYOU);

    /**
     * 优惠额度费率
     *
     * @param tradeParams
     * @param transaction
     */
    public boolean quotaFeeRate(Map<String, Object> tradeParams, Map<String, Object> transaction, Integer provider) {
        //是否支持
        if (!hasQuotaFeeRate(transaction)) {
            return false;
        }

        String merchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
        String tid = MapUtil.getString(transaction, DaoConstants.ID);

        Map config = MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT);
        String commonSwitchStr = MapUtil.getString(config, TransactionParam.COMMON_SWITCH);
        if (UpayUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_QUOTA_APPLY_ACTIVITY)) {
            try {
                //获取套餐ID
                String comboId = null;
                Map<String, String> feeRateTagMap = MapUtil.getMap(tradeParams, TransactionParam.FEE_RATE_TAG);
                String subPayway = MapUtil.getString(transaction, Transaction.SUB_PAYWAY);
                if (Objects.nonNull(subPayway) && subPayway.length() > 0) {
                    String feeRateTag = MapUtil.getString(feeRateTagMap, subPayway);
                    if (Objects.nonNull(feeRateTag) && feeRateTag.length() > 0) {
                        //旧的值为 product_flag, 新的为 combo_id:product_flag 或者 combo_id:
                        if (feeRateTag.contains(":")) {
                            comboId = feeRateTag.substring(0, feeRateTag.indexOf(":"));
                        }
                    }
                }
                String feeRate = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE);
                long feeRateCents = StringUtils.yuan2cents(feeRate);
                if (feeRateCents == 0) {
                    return false;
                }
                //是否异步记录余额
                if (hasAsyncWalletLog(transaction, tradeParams, commonSwitchStr)) {
                    BeanUtil.setNestedProperty(transaction, Transaction.KEY_ASYNC_WALLET_LOG, true);
                    return false;
                }
                //判断该通道是否需要在交易完成后才处理额度包优惠
                if (PROVIDERS_OF_PROCESS_QUOTA_AFTER_FINISHED.contains(provider)) {
                    return false;
                }
                //------------ 基础额度包统一走预扣减 ------------
                DiscountQuotaResponse response = quotaService.preDeductDiscountQuota(buildPreDeductQuotaRequest(comboId, tradeParams, transaction, provider));
                if (!response.isUsable()) {
                    return false;
                }
                if (org.apache.commons.lang3.StringUtils.isBlank(response.getFeeRate()) ||
                        response.getQuotaRecordId() == null) {
                    return false;
                }

                //减免费率
                String decreaseFeeRate = response.getFeeRate();
                //最终费率
                long actFeeRate = feeRateCents - StringUtils.yuan2cents(decreaseFeeRate);
                if (actFeeRate < 0) {
                    tradeParams.put(TransactionParam.FEE_RATE, "0.00");
                } else {
                    tradeParams.put(TransactionParam.FEE_RATE, StringUtils.cents2yuan(actFeeRate));
                }
                tradeParams.put(TransactionParam.FEE_RATE_ORIGINAL, feeRate);
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE_RATE, response.getFeeRate());
                BeanUtil.setNestedProperty(transaction, Transaction.KEY_QUOTA_FEE_RATE_TAG, String.valueOf(response.getQuotaRecordId()));
                return true;
            } catch (Exception e) {
                logger.error(String.format("获取优惠费率信息异常,商户ID %s tid %s ", merchantId, tid), e);
            }
        }
        return false;
    }

    /**
     * 重新设置正向交易额度减免手续费
     *
     * @param transaction
     */
    public static void setPayQuotaFee(Map<String, Object> transaction) {
        try {
            Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
            String quotaFeeRateTag = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE_TAG);
            String quotaFeeRate = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE);
            //存在额度包标识再做
            if (StringUtils.isEmpty(quotaFeeRateTag) || StringUtils.isEmpty(quotaFeeRate)) {
                return;
            }
            //富友通道计算逻辑在notify-pay
            int provider = MapUtil.getIntValue(transaction, Transaction.PROVIDER);
            if (provider == Provider.FUYOU.getCode()) {
                return;
            }
            Long quotaFee = FeeUtil.calculateQuotaFee(transaction);
            setQuotaFee(transaction, quotaFee);
        } catch (Exception e) {
            logger.error("重新设置正向交易额度减免手续费异常. tsn={}", MapUtil.getString(transaction, Transaction.TSN), e);
        }
    }

    /**
     * 重置逆向交易额度回退手续费
     *
     * @param currentRefundTransaction
     */
    public void setRefundQuotaFee(Map<String, Object> currentRefundTransaction) {
        String tsn = MapUtil.getString(currentRefundTransaction, Transaction.TSN);
        try {
            Map<String, Object> extraOutFields = MapUtil.getMap(currentRefundTransaction, Transaction.EXTRA_OUT_FIELDS);
            String quotaFeeRateTag = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE_TAG);
            String quotaFeeRate = MapUtil.getString(extraOutFields, Transaction.QUOTA_FEE_RATE);
            int provider = MapUtil.getIntValue(currentRefundTransaction, Transaction.PROVIDER);
            //存在额度包标识再做
            if (StringUtils.isEmpty(quotaFeeRateTag) || StringUtils.isEmpty(quotaFeeRate)) {
                return;
            }
            //富友通道额度回退是通过在notify-pay服务计算回调
            if (provider == Provider.FUYOU.getCode()) {
                return;
            }
            long quotaFee = calculateRollbackQuotaFee(currentRefundTransaction);
            setQuotaFee(currentRefundTransaction, quotaFee);
        } catch (Throwable e) {
            logger.info("重置逆向交易额度回退手续费异常. tsn={}", tsn, e);
        }
    }

    /**
     * 计算回退额度包减免手续费
     * 本次回退手续费 = 支付优惠额度*(本次退款金额/交易金额)
     *
     * @param currentRefundTransaction 　当前退款交易流水
     * @return
     */
    private long calculateRollbackQuotaFee(Map<String, Object> currentRefundTransaction) {
        String currentRefundTsn = MapUtil.getString(currentRefundTransaction, Transaction.TSN);
        List<Map<String, Object>> transactions = getSuccessTransactionList(currentRefundTransaction);
        logger.info("计算回退额度包减免手续费. tsn={}, 流水记录数={}", currentRefundTsn, transactions.size());
        Map<String, Object> payTransaction = filterPayTransaction(transactions);
        //实际支付金额
        long payAmount = FeeUtil.calculatePayerAmount(payTransaction);
        //实际退款金额
        long refundAmount = FeeUtil.calculatePayerAmount(currentRefundTransaction);
        //交易减免手续费
        long payQuotaFee = FeeUtil.calculateQuotaFee(payTransaction);
        int type = MapUtil.getIntValue(currentRefundTransaction, Transaction.TYPE);
        //本次回退手续费 = 支付优惠额度*(本次退款金额/交易金额)　按比例退款
        long rollbackQuotaFee = Math.round(payQuotaFee * (refundAmount * 1.0d / payAmount));
        //无额度包标识与额度包费率值时不处理
        if (CollectionUtils.isEmpty(transactions) || type == Transaction.TYPE_CANCEL) {
            return rollbackQuotaFee;
        }
        //历史退款统计总和
        long totalHistoryRefundAmount = 0L;
        //历史额度包回退统计总和
        long totalHistoryQuotaFee = 0L;
        //历史逆向交易明细 tsn -> quotaFee
        Map<String, Long> historyRollbackMap = new HashMap<>(transactions.size());
        for (Map<String, Object> transaction : transactions) {
            Integer status = MapUtil.getInteger(transaction, Transaction.STATUS);
            int currentType = MapUtil.getIntValue(transaction, Transaction.TYPE);
            String tsn = MapUtil.getString(transaction, Transaction.TSN);
            //排除非成功状态流水
            if (!Objects.equals(Transaction.STATUS_SUCCESS, status)) {
                continue;
            }
            //排除非退款、撤单流水
            if (!(currentType == Transaction.TYPE_REFUND || currentType == Transaction.TYPE_CANCEL)) {
                continue;
            }
            //排除当前交易流水
            if (Objects.equals(currentRefundTsn, tsn)) {
                continue;
            }
            long historyQuotaFee = BeanUtil.getPropLong(transaction, Transaction.KEY_QUOTA_FEE);
            //存量额度包回退未记录quotaFee，故这里重新计算
            if (historyQuotaFee <= 0L) {
                long historyRefundAmount = FeeUtil.calculatePayerAmount(transaction);
                historyQuotaFee = Math.round(payQuotaFee * (historyRefundAmount * 1.0d / payAmount));
            }
            //设置
            historyRollbackMap.put(tsn, historyQuotaFee);
            totalHistoryQuotaFee += historyQuotaFee;
            totalHistoryRefundAmount += FeeUtil.calculatePayerAmount(transaction);
        }
        // 当　最后一笔退款　且　正向交易优惠额度 - 历史累计回退额度总和 - 本次回退额度 != 0　时，则取差额
        boolean hasLastRefund = totalHistoryRefundAmount + refundAmount >= payAmount;
        long diff = payQuotaFee - totalHistoryQuotaFee - rollbackQuotaFee;
        logger.info("退款回退优惠额度,本次回退额度={}, 历史累计回退额度={}, 正向交易优惠额度={},是否最后一笔={},差异={}",
                rollbackQuotaFee, historyRollbackMap, payQuotaFee, hasLastRefund, diff);
        if (hasLastRefund && diff != 0L) {
            rollbackQuotaFee = payQuotaFee - totalHistoryQuotaFee;
            logger.warn("退款回退优惠额度,当前流水是最后一笔退款流水时，则取差额. 本次回退额度={}, 历史累计回退额度总和={}, 历史累计回退额度={}, 正向交易优惠额度={}",
                    rollbackQuotaFee, totalHistoryQuotaFee, historyRollbackMap, payQuotaFee);
        }
        //当　历史回退额度包减免手续费 + 当前回退减免手续费 > 支付减免手续费　时，则取差额
        if (totalHistoryQuotaFee + rollbackQuotaFee > payQuotaFee) {
            rollbackQuotaFee = payQuotaFee - totalHistoryQuotaFee;
            logger.warn("退款回退优惠额度,本次回退额度超出最大回退额度. 本次回退额度={}, 历史累计回退额度总和={}, 历史累计回退额度={}, 正向交易优惠额度={}",
                    rollbackQuotaFee, totalHistoryQuotaFee, historyRollbackMap, payQuotaFee);
        }
        return rollbackQuotaFee;
    }

    /**
     * 设置额度减免/回退手续费
     *
     * @param transaction
     * @param quotaFee
     */
    private static void setQuotaFee(Map<String, Object> transaction, Long quotaFee) {
        Map<String, Object> extraOutFiles = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        if (Objects.isNull(extraOutFiles)) {
            Map<String, Object> extraOutFields = CollectionUtil.hashMap(Transaction.QUOTA_FEE, quotaFee);
            transaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
            return;
        }
        extraOutFiles.put(Transaction.QUOTA_FEE, quotaFee);
    }

    private PreDeductDiscountQuotaRequest buildPreDeductQuotaRequest(String comboId, Map<String, Object> tradeParams, Map<String, Object> transaction, Integer provider) {
        PreDeductDiscountQuotaRequest request = new PreDeductDiscountQuotaRequest();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(comboId)) {
            request.setComboId(Long.parseLong(comboId));
        }
        // N7设备碰一下
        QuotaActivityScenesTypeEnum scenesTypeEnum = null;
        String paySource = BeanUtil.getPropString(transaction, String.format("%s.%s", Transaction.EXTRA_PARAMS, Transaction.SQB_PAY_SOURCE));
        if (Objects.equals(paySource, PAY_SOURCE_SQB_NPOS)) {
            scenesTypeEnum = QuotaActivityScenesTypeEnum.N7;
        }
        request.setFeeRate(BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE));
        request.setTid(BeanUtil.getPropString(transaction, DaoConstants.ID));
        request.setMerchantId(BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID));
        request.setOriginalAmount(BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT));
        request.setPayWay(BeanUtil.getPropInt(transaction, Transaction.PAYWAY));
        request.setSubPayWay(BeanUtil.getPropInt(transaction, Transaction.SUB_PAYWAY));
        request.setTradeApp(MapUtil.getString(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.TRADE_APP));
        //直连
        request.setFormal(UpayUtil.isFormalByTradeParams(tradeParams));
        request.setProvider(provider);
        request.setVendorAppAppId(BeanUtil.getPropString(transaction, Transaction.KEY_TERMINAL_VENDOR_APP_APPID));
        request.setScenesType(scenesTypeEnum);
        request.setScenesCode(BeanUtil.getPropString(transaction, Transaction.KEY_TERMINAL_SN));
        return request;
    }

    /**
     * 是否支持额度包费率
     *
     * @param transaction
     * @return
     */
    private boolean hasQuotaFeeRate(Map<String, Object> transaction) {
        //收钱吧预授权暂不支持
        String depositType = BeanUtil.getPropString(transaction, Transaction.KEY_DEPOSIT_TYPE);
        if (Objects.equals(depositType, TransactionParam.DEPOSIT_SQB)) {
            return false;
        }
        Integer type = MapUtil.getInteger(transaction, Transaction.TYPE);
        return QUOTA_TYPE_PAYMENT_LIST.contains(type);
    }

    /**
     * 是否异步记录余额
     *
     * @param transaction
     * @param commonSwitchStr
     * @return
     */
    private boolean hasAsyncWalletLog(Map<String, Object> transaction, Map<String, Object> tradeParams, String commonSwitchStr) {
        // payWay
        int payWay = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        // 间连间清
        boolean liquidationNextDay = BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY);
        // n-pos额度包活动
        boolean nPosSwitch = UpayUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_QUOTA_ACTIVITY_N_POS);
        // 报名额度包活动
        boolean useQuotaActivity = UpayUtil.isCommonSwitchOpen(commonSwitchStr, TransactionParam.TYPE_COMMON_SWITCH_QUOTA_APPLY_ACTIVITY);
        // 使用资金渠道或资金渠道阶梯费率
        boolean useChannelFee = tradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null || tradeParams.get(TransactionParam.CHANNEL_LADDER_FEE_RATES) != null;
        // 非间连间清无需异步记录余额
        if (!liquidationNextDay) {
            return false;
        }
        // n-pos额度包活动 统一异步记录余额
        if (payWay == Payway.ALIPAY2.getCode() && nPosSwitch) {
            return true;
        }
        // 有额度包活动 且 使用资金渠道或资金渠道阶梯费率
        return useQuotaActivity && useChannelFee;
    }

    /**
     * 获取成功交易流水列表
     *
     * @param transaction
     * @return
     */
    private List<Map<String, Object>> getSuccessTransactionList(Map<String, Object> transaction) {
        String merchantId = BeanUtil.getPropString(transaction, Transaction.MERCHANT_ID);
        String orderSn = BeanUtil.getPropString(transaction, Transaction.ORDER_SN);
        Map<String, Object> extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        long orderCtime = BeanUtil.getPropLong(extraOutFields, String.format("%s.%s", Transaction.ORDER_INFO, DaoConstants.CTIME));
        boolean isHistoryRefund = MapUtil.getBooleanValue(extraOutFields, Transaction.IS_HISTORY_TRADE_REFUND, false);
        if (isHistoryRefund) {
            return gatewaySupportService.getSuccessTransactionList(merchantId, orderSn, orderCtime);
        }
        return CollectionUtil.iterator2list(repository.getTransactionDao().filter(
                Criteria.where(Transaction.MERCHANT_ID).is(merchantId)
                        .with(Transaction.ORDER_SN).is(orderSn)
                        .with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)
        ).fetchAll());
    }

    /**
     * 筛选出正向交易流水
     *
     * @param transactionList
     * @return
     */
    private Map<String, Object> filterPayTransaction(List<Map<String, Object>> transactionList) {
        return transactionList.stream().filter(transaction -> {
            int status = MapUtil.getIntValue(transaction, Transaction.STATUS);
            int currentType = MapUtil.getIntValue(transaction, Transaction.TYPE);
            //排除非成功状态流水
            if (!Objects.equals(Transaction.STATUS_SUCCESS, status)) {
                return false;
            }
            return currentType == Transaction.TYPE_PAYMENT || currentType == Transaction.TYPE_DEPOSIT_CONSUME;
        }).findAny().orElse(null);
    }
}
