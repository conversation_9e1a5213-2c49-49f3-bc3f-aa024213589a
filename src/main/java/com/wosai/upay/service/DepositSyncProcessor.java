package com.wosai.upay.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.wosai.upay.workflow.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.exception.DepositSynclOrderStateError;
import com.wosai.upay.exception.OrderNotExistsException;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.UpayUtil;


@Component
public class DepositSyncProcessor {
    private static Logger logger = LoggerFactory.getLogger(DepositSyncProcessor.class);
    @Autowired
    DataRepository dataRepository;
    @Autowired
    UpayOrderService upayOrderService;
    @Autowired
    GatewaySupportService gatewaySupportService;
    @Autowired
    ProviderManager providerManager;
    @Autowired
    WorkflowManager workflowManager;
    @Autowired
    ExternalServiceFacade facade;

    public TransactionContext sync(String merchantId, String clientSn, String sn, Map<String, Object> extended) {
        boolean orderInDB = true;
        boolean transactionInDB = true;
        Map<String, Object> order = dataRepository.getOrder(merchantId, null, sn, clientSn);
        if(order == null) {
            order = upayOrderService.getOrderBySn(merchantId, sn, clientSn);
            if(null == order) {
                throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
            }
            orderInDB = false;
        }
        sn = MapUtil.getString(order, Order.SN);
        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), BeanUtil.getPropInt(order, Order.SUB_PAYWAY));

        // 非预授权冻结成功订单不允许同步
        if(MapUtil.getIntValue(order, Order.STATUS) != Order.STATUS_DEPOSIT_FREEZED) {
            throw new DepositSynclOrderStateError(UpayErrorScenesConstant.DEPOSIT_SYNC_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_SYNC_INVALID_ORDER_STATE_MESSAGE);
        }
        Map<String, Object> lastConsumeTransaction = dataRepository.getLatestTransactionByOrderSn(merchantId, sn);
        int payway = MapUtil.getIntValue(order, Order.PAYWAY);
        // 支付宝预授权可能需要同步所有预授权失败流水，提前查询出所有流水信息
        List<Map<String, Object>> consumeTransactionList = (payway == Order.PAYWAY_ALIPAY2) ? gatewaySupportService.getAlipayDepositConsumeTransactionList(merchantId, sn, MapUtil.getLongValue(order, DaoConstants.CTIME)) : null;
        if(lastConsumeTransaction == null) {
            if(payway == Order.PAYWAY_ALIPAY2) {
                if(consumeTransactionList!= null && consumeTransactionList.size() > 0) {
                    lastConsumeTransaction = consumeTransactionList.get(consumeTransactionList.size() -1);
                }
            }else {
                lastConsumeTransaction = gatewaySupportService.getLatestTransactionByOrderSn(merchantId, sn, MapUtil.getLongValue(order, DaoConstants.CTIME));
            }
            transactionInDB = false;
        }
        if(lastConsumeTransaction == null) {
            throw new OrderNotExistsException(UpayErrorScenesConstant.ORDER_NOT_EXIST, UpayErrorScenesConstant.ORDER_NOT_EXIST_MESSAGE);
        }
        // 最后一笔交易流水必须为预授权完成交易流水（即交易在预授权完成失败的情况下才能发起同步）
        if(MapUtil.getIntValue(lastConsumeTransaction, Transaction.TYPE) != Transaction.TYPE_DEPOSIT_CONSUME) {
            throw new DepositSynclOrderStateError(UpayErrorScenesConstant.DEPOSIT_SYNC_INVALID_ORDER_STATE, UpayErrorScenesConstant.DEPOSIT_SYNC_INVALID_ORDER_STATE_MESSAGE);
        }
        order.put(Order.STATUS, Order.STATUS_DEPOSIT_CONSUME_INPROGRESS);
        lastConsumeTransaction.put(Transaction.STATUS, Transaction.STATUS_CREATED);
        TransactionContext context = workflowManager.createTransactionContext(sn, order, lastConsumeTransaction);
        //  当前只有直连渠道支持订单同步
        if(!(context.getServiceProvider() instanceof DirectAlipayV2ServiceProvider 
                || context.getServiceProvider() instanceof DirectAlipayV2WapServiceProvider
                || context.getServiceProvider() instanceof DirectWeixinV3PreDepositServiceProvider)) {
            throw new UpayBizException(UpayErrorScenesConstant.DEPOSIT_SYNC_ERROR, UpayErrorScenesConstant.DEPOSIT_SYNC_ERROR_MESSAGE);
        }
        // 支付宝订单需要先将使用tsn作为out_trade_no的预授权完成流水都同步一遍(即extra_out_fields中带有consume_order_sn的流水)
        if(payway == Order.PAYWAY_ALIPAY2 && !(context.getServiceProvider() instanceof DirectAlipayV2PreDepositServiceProvider)) {
            lastConsumeTransaction = null;
            for (int i = consumeTransactionList.size() - 1; i >=0; i--) {
                Map<String, Object> consumeTransaction = consumeTransactionList.get(i);
                String tradeNo = MapUtil.getString(consumeTransaction, Transaction.TRADE_NO);
                // 部分预授权完成未设置外部订单号，交易同步时需要这个订单号，先进行查单
                if(StringUtil.empty(tradeNo)) {
                    TransactionContext syncContext = workflowManager.createTransactionContext(sn, order, consumeTransaction);
                    UpayUtil.configAlipayV2AuthInfo(syncContext.getServiceProvider(), facade, consumeTransaction, MapUtil.getString(consumeTransaction, Transaction.STORE_ID), MapUtil.getLongValue(order, DaoConstants.CTIME));
                    syncContext.getServiceProvider().query(syncContext);
                    tradeNo = MapUtil.getString(consumeTransaction, Transaction.TRADE_NO);
                    if(StringUtil.empty(tradeNo)) {
                        logger.warn("流水{} 支付宝订单号为空，不支持预授权完成同步", MapUtil.getString(consumeTransaction, Transaction.TSN));
                        continue;
                    }
                }
                // 有时做支付宝预授权后没有外部订单号（例如：返回授权单已关闭 或调用失败），此时应该拿最后有外部订单号的这一笔预授权完成交易做同步
                if(lastConsumeTransaction == null) {
                    lastConsumeTransaction = consumeTransaction;
                    if(transactionInDB) {
                        Map<String, Object> dbData = dataRepository.getTransactionByTsn(merchantId, MapUtil.getString(lastConsumeTransaction, Transaction.TSN));
                        transactionInDB = (dbData != null) ? true : false;
                    }
                    continue;
                }

                // 预授权数据同步，不校验同步结果
                Map<String, Object> extraOutFields = (Map<String, Object>)consumeTransaction.get(Transaction.EXTRA_OUT_FIELDS);
                String consumeOrderSn = BeanUtil.getPropString(extraOutFields, Transaction.CONSUME_ORDER_SN);
                if(!StringUtil.empty(consumeOrderSn)) {
                    TransactionContext syncContext = workflowManager.createTransactionContext(sn, order, consumeTransaction);
                    UpayUtil.configAlipayV2AuthInfo(syncContext.getServiceProvider(), facade, consumeTransaction, MapUtil.getString(consumeTransaction, Transaction.STORE_ID), MapUtil.getLongValue(order, DaoConstants.CTIME));
                    syncContext.getServiceProvider().depositSync(syncContext);
                }
            }
            if(lastConsumeTransaction == null) {
                throw new UpayBizException(UpayErrorScenesConstant.DEPOSIT_SYNC_ERROR, UpayErrorScenesConstant.DEPOSIT_SYNC_ERROR_MESSAGE);
            }
            lastConsumeTransaction.put(Transaction.STATUS, Transaction.STATUS_CREATED);
            context = workflowManager.createTransactionContext(sn, order, lastConsumeTransaction);
        }
        // 支持自定义扩展参数上送
        if(MapUtil.isNotEmpty(extended)) {
            Map<String, Object> transactionExtended = (Map<String, Object>) lastConsumeTransaction.get(Transaction.EXTENDED_PARAMS);
            if(transactionExtended == null) {
                transactionExtended = new HashMap<String, Object>();
                lastConsumeTransaction.put(Transaction.EXTENDED_PARAMS, transactionExtended);
            }
            transactionExtended.putAll(extended);
        }
        UpayUtil.configAlipayV2AuthInfo(context.getServiceProvider(), facade, lastConsumeTransaction, MapUtil.getString(lastConsumeTransaction, Transaction.STORE_ID), MapUtil.getLongValue(order, DaoConstants.CTIME));

        // 同步订单信息
        String result = context.getServiceProvider().depositSync(context);
        if(!Workflow.RC_CONSUME_SUCCESS.equals(result)) {
            throw new UpayBizException(UpayErrorScenesConstant.DEPOSIT_SYNC_ERROR, UpayErrorScenesConstant.DEPOSIT_SYNC_ERROR_MESSAGE);
        }

        lastConsumeTransaction.put(Transaction.PAID_AMOUNT, lastConsumeTransaction.get(Transaction.EFFECTIVE_AMOUNT));
        lastConsumeTransaction.put(Transaction.RECEIVED_AMOUNT, lastConsumeTransaction.get(Transaction.EFFECTIVE_AMOUNT));
        lastConsumeTransaction.put(Transaction.STATUS, Transaction.STATUS_SUCCESS);
        lastConsumeTransaction.put(Transaction.FINISH_TIME, System.currentTimeMillis());
        // 添加订单同步标识，用于订单展示和退款限制
        Map<String, Object> extraOutFields = MapUtil.getMap(lastConsumeTransaction, Transaction.EXTRA_OUT_FIELDS);
        if(extraOutFields == null) {
            extraOutFields = new HashMap<String, Object>();
            lastConsumeTransaction.put(Transaction.EXTRA_OUT_FIELDS, extraOutFields);
        }
        extraOutFields.put(Transaction.IS_DEPOSIT_SYNC, true);
        if(!orderInDB) {
            extraOutFields.put(Transaction.IS_HISTORY_DEPOSIT_CONSUME, true);
        }
        if(!transactionInDB) {
            extraOutFields.put(Transaction.IN_HBASE, true);
        }
        workflowManager.raise(context, Workflow.RC_CONSUME_SUCCESS);
        return context;
    }

}
