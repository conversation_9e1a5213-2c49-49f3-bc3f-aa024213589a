package com.wosai.upay.service;

import com.wosai.constant.UpayConstant;
import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.*;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.ExternalServiceFacade.TcpRefundResult;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.*;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.TransactionContext;
import com.wosai.upay.workflow.Workflow;
import com.wosai.upay.workflow.WorkflowManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class CancelProcessor {
    public static long CANCEL_MIN_TIME_ALLOWED = 1000 * 60 * 10; //10分钟
    
    public static long RECONCILE_CANCEL_B2C_TIME_ALLOWED = 1000 * 60 * 1; // b2c勾兑撤单时间为1分钟
    public static long RECONCILE_CANCEL_DEFAULT_TIME_ALLOWED = 1000 * 60 * 4; // 默认勾兑撤单时间为4分钟

    @Autowired
    private TsnGenerator tsnGenerator;
    @Autowired
    private WorkflowManager workflowManager;
    @Autowired
    private DataRepository repository;
    @Autowired
    private ExternalServiceFacade facade;
    @Autowired
    private ProviderManager providerManager;
    @Autowired
    private TradeCacheService tradeCacheService;
    @Autowired
    private GatewaySupportService gatewaySupportService;

    /**
     * @param order 订单信息
     * @param extended 透传参数
     * @param reflect 反射参数
     * @param basicParams 基本交易参数
     * @return
     */
    @Transactional(isolation=Isolation.REPEATABLE_READ)
    public TransactionContext abortOrReverse(Map<String, Object> order, String cancelType, Map<String, Object> extended, Object reflect, Map<String, Object> basicParams) {
        String merchantId = MapUtil.getString(basicParams, TransactionParam.MERCHANT_ID);
        String storeId = MapUtil.getString(basicParams, TransactionParam.STORE_ID);
        String terminalId = MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID);
        boolean transactionUseOrderTerminalInfo = false;
        if(StringUtil.empty(terminalId)){
            transactionUseOrderTerminalInfo = true;
        }

        providerManager.verify(BeanUtil.getPropInt(order, Order.PROVIDER), BeanUtil.getPropInt(order, Order.PAYWAY), BeanUtil.getPropInt(order, Order.SUB_PAYWAY));
        String orderTeminalId = BeanUtil.getPropString(order, Order.TERMINAL_ID);
        String orderStoreId = BeanUtil.getPropString(order, Order.STORE_ID);
        String orderId = BeanUtil.getPropString(order, DaoConstants.ID);
        String orderSn = BeanUtil.getPropString(order, Order.SN);
        int orderStatus = BeanUtil.getPropInt(order, Order.STATUS);
        
        // 撤单类型为勾兑，且已经过了订单的有效时间后，勾兑后订单状态为PAY_CANCELED:1300
        if(Transaction.CANCEL_TYPE_RECONCILE.equals(cancelType)){
            if((orderStatus == Order.STATUS_CREATED || orderStatus == Order.STATUS_PAY_ERROR)) {
                TransactionContext context = workflowManager.lookupTransactionContext(orderId);
                if(context == null){
                    Map transaction = repository.getPayTransactionByOrderSn(merchantId, orderSn);
                    context = workflowManager.spawnTransactionContext(null, order, transaction);
                }
                
                // 移除缓存中的交易数据
                if(BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_PAY_ERROR) {
                    String terminalSnOrStoreSn = BeanUtil.getPropString(context.getTransaction(), (null != BeanUtil.getPropString(order, Order.TERMINAL_ID)) ? Transaction.KEY_TERMINAL_SN: Transaction.KEY_STORE_SN);
                    tradeCacheService.removeTradeCache(terminalSnOrStoreSn, MapUtil.getString(order, Order.CLIENT_SN), MapUtil.getString(order, Order.SN));
                }
                
                Map extoutFields = (Map) context.getTransaction().get(Transaction.EXTRA_OUT_FIELDS);
                if(null != extoutFields && BeanUtil.getPropBoolean(extoutFields, Transaction.QUERY_EXPIRE)){
                    BeanUtil.setNestedProperty(context.getTransaction(), UpayUtil.getBizErrorCodeKey(MpayServiceProvider.OP_PAY),
                            SceneConfigFacade.getWosaiErrorDefinition(UpayErrorScenesConstant.QUERY_EXPRIE_ERROR, null));
                }
                // 将勾兑撤销的存到流水中extraParam里
                Map<String, Object> extraParams = MapUtil.getMap(context.getTransaction(), Transaction.EXTRA_PARAMS, new HashMap());
                extraParams.put(Transaction.CANCEL_TYPE, cancelType);
                BeanUtil.setNestedProperty(context.getTransaction(), Transaction.EXTRA_PARAMS, extraParams);
                context.setCurrentStateLabel(StateLabel.fromId(Transaction.STATUS_ERROR_RECOVERY));
                workflowManager.raise(context, Workflow.RC_CANCEL_SUCCESS);
                return context;
            }else {
                throw new UpayCancelOrderNoop(UpayErrorScenesConstant.CANCEL_REPEAT, UpayErrorScenesConstant.CANCEL_REPEAT_MESSAGE);
            }
            
        }
        //银联网银支付 企业网银支付不支持撤单，普通的网银支付支持撤单(支持当日撤单)，在此统一处理此通道下的交易不支持撤单.
        if(Order.PAYWAY_BANKCARD == BeanUtil.getPropInt(order, Order.PAYWAY) && Order.PROVIDER_UNIONPAY_ONLINE == BeanUtil.getPropInt(order, Order.PROVIDER)){
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        //拉卡拉云闪付交易交易处于失败状态时，不能进行关单或撤销，在此调整为非成功状态时，不允许做撤销操作.
        if(Order.PAYWAY_UNIONPAY == BeanUtil.getPropInt(order, Order.PAYWAY) 
                && Order.PROVIDER_LAKALA_UNION_PAY_V3 == BeanUtil.getPropInt(order, Order.PROVIDER)
                && Order.STATUS_PAID != BeanUtil.getPropInt(order, Order.STATUS)
                && Order.STATUS_CANCEL_ERROR != BeanUtil.getPropInt(order, Order.STATUS)) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        //云闪付交易不支持隔日撤单
        if(Order.PAYWAY_LKL_UNIONPAY == BeanUtil.getPropInt(order, Order.PAYWAY) && DateTimeUtil.getOneDayEnd(BeanUtil.getPropLong(order, DaoConstants.CTIME)) < System.currentTimeMillis()){
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_NOT_SUPPORT_NEXT_DAY_CANCEL, UpayErrorScenesConstant.CANCEL_ORDER_NOT_SUPPORT_NEXT_DAY_CANCEL_MESSAGE);
        }
        // 建行福利卡不支持撤销
        if (Order.PAYWAY_CCB_GIFT_CARD == BeanUtil.getPropInt(order, Order.PAYWAY) && Order.PROVIDER_CCB_GIFT_CARD == BeanUtil.getPropInt(order, Order.PROVIDER)){
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        if (Order.PAYWAY_JD == BeanUtil.getPropInt(order, Order.PAYWAY)){
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        if (Order.PROVIDER_TL_SYB == BeanUtil.getPropInt(order, Order.PROVIDER)){
            Map transaction = repository.getPayTransactionByOrderSn(merchantId, orderSn);
            if (MapUtil.getIntValue(transaction, Transaction.STATUS) != Transaction.STATUS_SUCCESS) {
                throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
            }
        }
        //直连翼支付交易(rsa签名方式)只允许对支付成功的交易进行撤单
        if(Order.PAYWAY_BESTPAY == BeanUtil.getPropInt(order, Order.PAYWAY) && BeanUtil.getPropString(order, Order.PROVIDER) == null &&  Order.STATUS_PAID != BeanUtil.getPropInt(order, Order.STATUS)){
            Map transaction = repository.getPayTransactionByOrderSn(merchantId, orderSn);
            String signType = BeanUtil.getPropString(transaction, Transaction.KEY_BESTPAY_TRADE_SIGNTYPE);
            if (TransactionParam.SIGN_TYPE_RSA.equals(signType)){
                throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
            }
        }

        // 富士康富圈圈退款是异步处理，网关暂不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_FOXCONN) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
        }

        // 工行通道，网关暂不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_ICBCBANK) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        // 通联s2p 不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Provider.TL_S2P.getCode()) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        // 易宝 不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Provider.YOP.getCode()) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        // 国通 不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Provider.GUOTONG.getCode()) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }
        // 华夏银行通道，数币交易暂不支持撤单
        if(Order.PAYWAY_DCEP == BeanUtil.getPropInt(order, Order.PAYWAY) && BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_HXBANK) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        // 交行通道，暂不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_BOCOM) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_ZJTLCB && Order.STATUS_PAID != BeanUtil.getPropInt(order, Order.STATUS) && Order.STATUS_CANCEL_ERROR != BeanUtil.getPropInt(order, Order.STATUS)) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        //福建农信通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_FJNX) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        //锦医一卡通通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_JY_CARD) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        //江苏银行通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_JSB) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        // 中投科信通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_ZTKX) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        //泸州银行通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_LZCCB) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        // 新中新通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_XZX) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        // 院校通通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_HOPE_EDU) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        // Airwallex 不支持撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Provider.AIRWALLEX.getCode()) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        //腾讯微卡通道，不允许撤单
        if(BeanUtil.getPropInt(order, Order.PROVIDER, 0) == Order.PROVIDER_WECARD) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_FAIL, UpayErrorScenesConstant.CANCEL_FAIL_MESSAGE);
        }

        if (orderStatus == Order.STATUS_CREATED) {
            TransactionContext context = workflowManager.lookupTransactionContext(orderId);
            if (context == null) {
                long ctime = BeanUtil.getPropLong(order, DaoConstants.CTIME);
                if(System.currentTimeMillis() - ctime < CANCEL_MIN_TIME_ALLOWED){
                    throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG, UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG_MESSAGE);
                }else{
                    return doCancelOrderWorkflow(merchantId, transactionUseOrderTerminalInfo ? orderStoreId : storeId, transactionUseOrderTerminalInfo ? orderTeminalId : terminalId, orderId, orderSn, cancelType, order, extended, reflect, false, basicParams);
                }
            }else {
                Map<String, Object> transaction = context.getTransaction();
                int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
                int subPayway = MapUtil.getIntValue(transaction, Transaction.SUB_PAYWAY);
                if(payway == Order.PAYWAY_ALIPAY2 && subPayway == Order.SUB_PAYWAY_QRCODE){
                    throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG, UpayErrorScenesConstant.CANCEL_ORDER_IN_PAY_PROG_MESSAGE);
                }

                if (Order.PAYWAY_BANKACCOUNT == payway) {
                    Map<String, Object> extendedParams = MapUtil.getMap(transaction, Transaction.EXTENDED_PARAMS, new HashMap<>());
                    if (MapUtil.isNotEmpty(extended)) {
                        extendedParams.putAll(extended);
                    }
                }
                workflowManager.raise(context, Workflow.RC_ABORT);
                return context;
            }
        } else if (orderStatus == Order.STATUS_PAID || orderStatus == Order.STATUS_CANCEL_ERROR) {
            //分账订单只能在支付成功之前撤单
            Map<String,Object> payTransaction = repository.getPayTransactionByOrderSn(merchantId, orderSn);
            if(payTransaction != null && UpayProfitSharingUtil.isPayProfitSharingTransaction(payTransaction)){
                throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_HAS_PROFIT_SHARING, UpayErrorScenesConstant.CANCEL_ORDER_HAS_PROFIT_SHARING_MESSAGE);
            }
            if (MapUtil.getIntValue(payTransaction, Transaction.PROVIDER) == Provider.LAKALA_UNION_PAY_V3.getCode()
                    && MapUtil.getIntValue(payTransaction, Transaction.PAYWAY) == Payway.BANKCARD.getCode()
                    && Objects.equals(MapUtil.getString(MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT, new HashMap()), TransactionParam.TRADE_APP), ApolloConfigurationCenterUtil.getPhonePosTradeAppId())) {
                throw new UpayBizException(UpayErrorScenesConstant.PHONE_POS_CANNOT_CANCEL, UpayErrorScenesConstant.PHONE_POS_CANNOT_CANCEL_MESSAGE);
            }
            return doCancelOrderWorkflow(merchantId, transactionUseOrderTerminalInfo ? orderStoreId : storeId, transactionUseOrderTerminalInfo ? orderTeminalId : terminalId, orderId, orderSn, cancelType, order, extended, reflect, true, basicParams);
        } else if (orderStatus == Order.STATUS_PAY_ERROR) {
            return doCancelOrderWorkflow(merchantId, transactionUseOrderTerminalInfo ? orderStoreId : storeId, transactionUseOrderTerminalInfo ? orderTeminalId : terminalId, orderId, orderSn, cancelType, order, extended, reflect, false, basicParams);

        }else if (orderStatus == Order.STATUS_PAY_CANCELED || orderStatus == Order.STATUS_CANCELED) {
            throw new UpayCancelOrderNoop(UpayErrorScenesConstant.CANCEL_REPEAT, UpayErrorScenesConstant.CANCEL_REPEAT_MESSAGE);
            
        }else{
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE, UpayErrorScenesConstant.CANCEL_INVALID_ORDER_STATE_MESSAGE);
        }
    }
    
    /**
     * @param merchantId 商户ID
     * @param storeId 门店ID
     * @param terminalId 终端ID
     * @param orderId 订单ID
     * @param orderSn 订单序列号
     * @param order 订单对象
     * @param extended 透传参数
     * @param reflect 反射参数
     * @param fundChange 本次撤单是否要有账面金额变动
     * @return
     */
    private TransactionContext doCancelOrderWorkflow(String merchantId,
                                                     String storeId,
                                                     String terminalId,
                                                     String orderId,
                                                     String orderSn,
                                                     String cancelType,
                                                     Map<String, Object> order,
                                                     Map<String, Object> extended,
                                                     Object reflect,
                                                     boolean fundChange,
                                                     Map<String, Object> basicParams) {
        List<Map<String,Object>> transactionPayments = new ArrayList<>();
        long promotionAmount = 0;
        // 1、解析业务方上送的优惠明细
        List<Map<String, Object>> sqbPromotionDetails = (List<Map<String, Object>>) BeanUtil.getNestedProperty(extended, Transaction.SQB_PROMOTION_DETAIL);
        if (!CollectionUtils.isEmpty(sqbPromotionDetails)) {
            transactionPayments = PaymentUtil.buildTransactionRefundOrCancelPayments(order, sqbPromotionDetails);
        }
        UpayUtil.validateRefundOrCancelPromotionDetail(order, transactionPayments);
        promotionAmount = transactionPayments.stream().mapToLong(map -> MapUtil.getLong(map, Payment.AMOUNT)).sum();
        Long effectiveAmount = MapUtils.getLongValue(order, Order.ORIGINAL_TOTAL);
        effectiveAmount = effectiveAmount - promotionAmount; //减去业务方上送的优惠金额
        List<Map<String,Object>> payments = new ArrayList<>();
        if (BeanUtil.getPropBoolean(order, Order.TCP_MODIFIED, false)) {
            TcpRefundResult result = facade.consultTcpForRefund(orderSn, effectiveAmount);
            if (result.isDenied()) {
                String reason = result.getReason();
                throw new UpayTcpOrderNotRefundable(UpayErrorScenesConstant.UPAY_ORDER_NOT_ALLOWED, UpayErrorScenesConstant.UPAY_ORDER_NOT_ALLOWED_MESSAGE, CollectionUtil.hashMap(
                        "#message#", StringUtil.empty(reason) ? UpayErrorScenesConstant.UPAY_ORDER_NOT_ALLOWED_MESSAGE : reason
                ));
            }
            effectiveAmount = result.getEffectiveAmount();

            payments = PaymentUtil.buildTransactionPaymentsForRefundOrCancel(result);
        }
        if (effectiveAmount != BeanUtil.getPropLong(order, Order.EFFECTIVE_TOTAL)) {
            throw new UpayTcpOrderRefundError(UpayErrorScenesConstant.REFUND_AMOUNT_ERROR, UpayErrorScenesConstant.REFUND_AMOUNT_ERROR_MESSAGE);
        }
        transactionPayments.addAll(payments);
        // 撤单次数超过10次后，不再允许撤单
        long count = repository.getCancelTransactionCountByOrderSn(merchantId, orderSn);
        if(count >= 10){
        	throw new UpayCancelOrderNoop(UpayErrorScenesConstant.CANCEL_REPEAT, UpayErrorScenesConstant.CANCEL_REPEAT_MESSAGE);
        }
        
        String tsn = tsnGenerator.nextSn();
        String transactionId = UpayUtil.getTransactionIdBySn(tsn);
        Map<String, Object> payTransaction = repository.getPayTransactionByOrderSn(merchantId, orderSn);
        //设置liquidationNextDay全局配置
        facade.setLiquidationNextDayConfig(payTransaction);

        Map<String, Object> extraParams = new HashMap<String, Object>();
        UpayUtil.validateRefundFlag(payTransaction, extended, extraParams);
        //是否是喔噻清算
        if(!UpayUtil.isFormal(payTransaction)){
            //校验商户是否切换清算通道
            int clearanceProvider = BeanUtil.getPropInt(basicParams, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
            int tradeClearanceProvider = BeanUtil.getPropInt(payTransaction,Transaction.KEY_CLEARANCE_PROVIDER,TradeConfigService.CLEARANCE_PROVIDER_LKL);
            if(tradeClearanceProvider != clearanceProvider){
                throw new UpayBizException(UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_CANCEL_ERROR, UpayErrorScenesConstant.ORDER_CAN_NOT_REFUND_CANCEL_ERROR_MESSAGE);
            }
        }

        if(!UpayUtil.isFormal(workflowManager, payTransaction) && fundChange){
            long walletChangeAmount = FeeUtil.calculateApproximateWalletChangeAmount(payTransaction, transactionPayments, BeanUtil.getPropLong(order, Order.ORIGINAL_TOTAL), true);
            //当余额不足或者有其他错误时，下面的方法会抛异常，交由ServiceMethodInterceptor去处理
            ExternalServiceFacade.CanCancelOrRefundAndFreezeWalletBalanceReq req = new ExternalServiceFacade.CanCancelOrRefundAndFreezeWalletBalanceReq(merchantId, transactionId, walletChangeAmount, payTransaction, MapUtil.getIntValue(basicParams, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL), null, true);
            facade.canCancelOrRefundAndFreezeWalletBalance(req);
            WalletDeductionContextHolder.setContextHolder(merchantId, transactionId);
        }
        // 收付通非分账交易，需要校验商户余额
        Map<String, Object> payConfigSnapshot = MapUtil.getMap(payTransaction, Transaction.CONFIG_SNAPSHOT);
        //如果是归集类的商户，退款前需要判断当天的归集任务已处理完毕才可进行
        String commonSwitch = MapUtil.getString(basicParams, TransactionParam.COMMON_SWITCH);
        //如果是归集类的商户，退款前需要判断当天的归集任务已处理完毕才可进行
        if (UpayUtil.isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_AGGREGATION_CONFIG)) {
            facade.allowBeforeRefundForAggregated(merchantId, MapUtil.getInteger(payTransaction, Transaction.PROVIDER), MapUtil.getLongValue(payTransaction, DaoConstants.CTIME));
        }
        String sftBrandId = MapUtil.getString(payConfigSnapshot, TransactionParam.SFT_BRAND_ID);
        if (!StringUtil.empty(sftBrandId) && fundChange && (extraParams == null || !extraParams.containsKey(Transaction.PROFIT_SHARING))) {
            long tradeRefundAmount = MapUtil.getLongValue(payTransaction, Transaction.EFFECTIVE_AMOUNT);
            facade.canCancelOrRefundAndFreezeSFTWalletBalance(sftBrandId, merchantId, orderSn, MapUtil.getIntValue(payTransaction, Transaction.PAYWAY), tradeRefundAmount);
        }

        // 移除缓存中的交易数据
        if(BeanUtil.getPropInt(order, Order.STATUS) == Order.STATUS_PAY_ERROR) {
            String terminalSnOrStoreSn = BeanUtil.getPropString(payTransaction, (null != BeanUtil.getPropString(order, Order.TERMINAL_ID)) ? Transaction.KEY_TERMINAL_SN: Transaction.KEY_STORE_SN);
            tradeCacheService.removeTradeCache(terminalSnOrStoreSn, MapUtil.getString(order, Order.CLIENT_SN), MapUtil.getString(order, Order.SN));
        }

        // 是否需要删除extendParams标识（退款时需要插入交易花呗分期数据，用于sp和账本展示，但不需要上送到支付源）
        boolean needRemoveExtendedParams = false;
        //撤单的也加入海外字段 加入花呗标志
        Map<String,Object> extraOutFields = new HashMap<>();
        Map<String, Object> payExtraOutFields = (Map<String, Object>) payTransaction.get(Transaction.EXTRA_OUT_FIELDS);
        if(payExtraOutFields != null){
            Map<String,Object> overseas = (Map<String, Object>) payExtraOutFields.get(Transaction.OVERSEAS);
            if(overseas != null){
                extraOutFields.put(Transaction.OVERSEAS, overseas);
            }
            Object comboId = payExtraOutFields.get(Transaction.COMBO_ID);
            if(comboId != null){
                extraOutFields.put(Transaction.COMBO_ID, comboId);
            }
            Map<String,Object> netUnionBestPay = (Map<String, Object>) payExtraOutFields.get(Transaction.NUCC_BESTPAY);
            if(!CollectionUtils.isEmpty(netUnionBestPay)){
                extraOutFields.put(Transaction.NUCC_BESTPAY, netUnionBestPay);
            }
            Object huabeiFQ = payExtraOutFields.get(TransactionParam.HB_FQ);
            if (huabeiFQ != null) {
                extraOutFields.put(TransactionParam.HB_FQ, huabeiFQ);
                MapUtil.addKeysIfNotExist(payExtraOutFields, extraOutFields, Transaction.HB_FQ_SPONSOR, Transaction.FQ_SPONSOR);
                Map<String, Object> payExtendedParams = (Map<String, Object>) payTransaction.get(Transaction.EXTENDED_PARAMS);
                if(payExtendedParams != null && payExtendedParams.containsKey(BusinessV2Fields.EXTEND_PARAMS)) {
                    needRemoveExtendedParams = true;
                    extended = (extended == null) ? new HashMap<>() : extended;
                    extended.put(BusinessV2Fields.EXTEND_PARAMS, payExtendedParams.get(BusinessV2Fields.EXTEND_PARAMS));
                }
                MapUtil.addKeysIfNotExist(MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS, new HashMap<>()), extraParams, Transaction.SQB_HB_FQ_SELLER_SERVICE_CHARGE, Transaction.SQB_HB_FQ_BUYER_SERVICE_CHARGE, 
                        Transaction.SQB_FQ_SELLER_SERVICE_CHARGE, Transaction.SQB_FQ_BUYER_SERVICE_CHARGE, Transaction.SQB_FQ_SERVICE_TYPE, Transaction.WILD_CARD_TYPE);
            }

            //海外云闪付,下游判断
            Map payExtraParams = MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS);
            String sqbWalletName = MapUtil.getString(payExtraParams, Transaction.SQB_WALLET_NAME);
            if(ApolloConfigurationCenterUtil.getUnionOverseasWallet().contains(sqbWalletName) ) {
                extraOutFields.put(Transaction.SQB_WALLET_NAME, sqbWalletName);
            }
            //填充优惠额度ID
            String quotaId = MapUtil.getString(payExtraOutFields, Transaction.QUOTA_FEE_RATE_TAG);
            if (StringUtils.isNotEmpty(quotaId)) {
                extraOutFields.put(Transaction.QUOTA_FEE_RATE_TAG, quotaId);
                extraOutFields.put(Transaction.QUOTA_FEE_RATE, MapUtil.getString(payExtraOutFields, Transaction.QUOTA_FEE_RATE));
                extraOutFields.put(Transaction.QUOTA_FEE, MapUtil.getString(payExtraOutFields, Transaction.QUOTA_FEE));
            }
        }
        Boolean isMchChannelCouponSubsidy = (Boolean) BeanUtil.getNestedProperty(payTransaction, Transaction.KEY_IS_IS_MCH_CHANNEL_COUPON_SUBSIDY);
        if(isMchChannelCouponSubsidy != null){
            extraOutFields.put(Transaction.IS_MCH_CHANNEL_COUPON_SUBSIDY, isMchChannelCouponSubsidy);
        }

        // 存放订单信息，用于下游系统使用
        extraOutFields.put(Transaction.ORDER_INFO, CollectionUtil.hashMap(Order.ORIGINAL_TOTAL, order.get(Order.ORIGINAL_TOTAL),
                                                                          Order.EFFECTIVE_TOTAL, order.get(Order.EFFECTIVE_TOTAL),
                                                                          DaoConstants.CTIME, order.get(DaoConstants.CTIME),
                                                                          Order.TRADE_NO, order.get(Order.TRADE_NO)
                ));

        // 礼品卡服务需要上送退款时使用的设备号
        String terminalSn = MapUtil.getString(basicParams, TransactionParam.TERMINAL_SN);
        if(MapUtil.getIntValue(order, Order.PAYWAY) == Order.PAYWAY_GIFT_CARD && !StringUtil.empty(terminalSn)) {
            extended = (extended == null) ? new HashMap<String, Object>() : extended;
            extended.put(TransactionParam.TERMINAL_ID, terminalSn);
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> cancelTransaction = CollectionUtil.hashMap(DaoConstants.ID, transactionId,
                Transaction.TSN, tsn,
                Transaction.CLIENT_TSN, order.get(Order.CLIENT_SN) + "-C",
                Transaction.TYPE, Transaction.TYPE_CANCEL,
                Transaction.ITEMS, null,
                Transaction.SUBJECT, order.get(Order.SUBJECT),
                Transaction.BODY, order.get(Order.BODY),
                Transaction.STATUS, Transaction.STATUS_CREATED,
                Transaction.ORIGINAL_AMOUNT, order.get(Order.ORIGINAL_TOTAL),
                Transaction.EFFECTIVE_AMOUNT, order.get(Order.EFFECTIVE_TOTAL),
                Transaction.MERCHANT_ID, merchantId,
                Transaction.STORE_ID, storeId,
                Transaction.TERMINAL_ID, terminalId,
                Transaction.PROVIDER, order.get(Order.PROVIDER),
                Transaction.PAYWAY, order.get(Order.PAYWAY),
                Transaction.SUB_PAYWAY, order.get(Order.SUB_PAYWAY),
                Transaction.ORDER_ID, orderId,
                Transaction.ORDER_SN, orderSn,
                Transaction.PRODUCT_FLAG, payTransaction.get(Transaction.PRODUCT_FLAG),
                Transaction.EXTENDED_PARAMS, extended,
                Transaction.REFLECT, reflect,
                Transaction.CONFIG_SNAPSHOT, payTransaction.get(Transaction.CONFIG_SNAPSHOT),
                Transaction.EXTRA_OUT_FIELDS,extraOutFields,
                Transaction.BUYER_UID, payTransaction.get(Transaction.BUYER_UID),
                Transaction.BUYER_LOGIN, payTransaction.get(Transaction.BUYER_LOGIN));
        // 跨门店退款时，更新终端信息和门店信息到快照中
        if (Objects.nonNull(MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID)) 
                && !Objects.equals(order.get(Order.TERMINAL_ID), MapUtil.getString(basicParams, TransactionParam.TERMINAL_ID))) {
            Map<String, Object> configSnapshot = MapUtil.getMap(cancelTransaction, Transaction.CONFIG_SNAPSHOT);
            configSnapshot.putAll(MapUtil.copyInclusive(basicParams, TransactionParam.STORE_ID, TransactionParam.STORE_SN, TransactionParam.STORE_NAME, 
                    TransactionParam.TERMINAL_ID, TransactionParam.TERMINAL_SN, TransactionParam.TERMINAL_NAME, TransactionParam.TERMINAL_VENDOR_APP_APPID,
                    TransactionParam.TERMINAL_CATEGORY));
        }
        if (!StringUtil.empty(cancelType)){
            extraParams.put(Transaction.CANCEL_TYPE, cancelType);
        }

        //拷贝sqbUserId到撤单流水的extraParams中
        extraParams.put(Transaction.SQB_USER_ID, BeanUtil.getPropString(payTransaction, Transaction.KEY_SQB_USER_ID));

        //海外云闪付,下游判断
        Map payExtraParams = MapUtil.getMap(payTransaction, Transaction.EXTRA_PARAMS);
        String sqbWalletName = MapUtil.getString(payExtraParams, Transaction.SQB_WALLET_NAME);
        if(ApolloConfigurationCenterUtil.getUnionOverseasWallet().contains(sqbWalletName) ) {
            extraParams.put(Transaction.SQB_WALLET_NAME, sqbWalletName);
        }

        //支付宝代扣信息,下游判断
        String barcodeType = MapUtil.getString(payExtraParams, Transaction.BARCODE_TYPE);
        if(UpayConstant.DYNAMIC_ID_TYPE_ALIPAY_DEBIT.equals(barcodeType)) {
            extraParams.put(Transaction.BARCODE_TYPE, barcodeType);
            extraParams.put(Transaction.BARCODE, MapUtil.getString(payExtraParams, Transaction.BARCODE));
        }

        // 拷贝参数到流水
        UpayUtil.copyIfExists(payExtraParams, extraParams, Transaction.COPY_EXTRA_PARAMS_KEYS);

        if(!extraParams.isEmpty()) {
            cancelTransaction.put(Transaction.EXTRA_PARAMS, extraParams);
        }
        if (!transactionPayments.isEmpty() && PaymentUtil.isNewTradeCoprocessorOrder(order)){
            Map<String,Object> items = (Map<String, Object>) cancelTransaction.get(Transaction.ITEMS);
            if(items == null){
                items = new HashMap<>();
                cancelTransaction.put(Transaction.ITEMS, items);
            }
            items.put(Transaction.PAYMENTS, transactionPayments);
        }
        if (!fundChange) {
            cancelTransaction.put(Transaction.ORIGINAL_AMOUNT, 0L);
            cancelTransaction.put(Transaction.EFFECTIVE_AMOUNT, 0L);
            MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(cancelTransaction);
            Map<String,Object> tradeParams = serviceProvider.getTradeParams(cancelTransaction);
            if(tradeParams.containsKey(TransactionParam.FEE)){
                tradeParams.put(TransactionParam.FEE, 0);
            }
            if(PaymentUtil.isNewTradeCoprocessorOrder(order)){
                Map<String,Object> items = (Map<String, Object>) cancelTransaction.get(Transaction.ITEMS);
                if(items == null){
                    items = new HashMap<>();
                    cancelTransaction.put(Transaction.ITEMS, items);
                }
                items.put(Transaction.PAYMENTS, new ArrayList<>());
            }
        }
        PaymentUtil.copyChannelPaymentsToCancelOrFullRefundTransaction(payTransaction, cancelTransaction);
        MpayServiceProvider serviceProvider = workflowManager.matchServiceProvider(cancelTransaction);

        UpayUtil.configAlipayV2AuthInfo(serviceProvider, facade, cancelTransaction
                , storeId, MapUtil.getLongValue(order, DaoConstants.CTIME));

        Map<String,Object> tradeParams = serviceProvider.getTradeParams(cancelTransaction);
        if(!tradeParams.containsKey(TransactionParam.FEE)){
            Long fee = FeeUtil.calculateRefundOrCancelFee(repository, cancelTransaction);
            tradeParams.put(TransactionParam.FEE, fee);
        }
        facade.resetSignParams(serviceProvider, cancelTransaction);

        @SuppressWarnings("unchecked")
        Map<String, Object> orderUpdate = CollectionUtil.hashMap(DaoConstants.ID, orderId,
                Order.MERCHANT_ID, merchantId,
                Order.STATUS, Order.STATUS_CANCEL_INPROGRESS,
                DaoConstants.VERSION, order.get(DaoConstants.VERSION));
        try {
            repository.doInTransaction(() ->{
                repository.getOrderDao().updatePart(orderUpdate);
                repository.getTransactionDao().save(cancelTransaction);
            });
        }catch (DaoVersionMismatchException e) {
            throw new UpayCancelOrderStateError(UpayErrorScenesConstant.CANCEL_ORDER_IN_PROG, UpayErrorScenesConstant.CANCEL_ORDER_IN_PROG_MESSAGE);
        }
        
        // 勾兑撤单时，如果订单状态仍处于创建或者处理中状态时，说明这部分交易是因为网关系统突然宕机导致交易失败，需要将原先的支付流水的状态改为失败
        if(Transaction.CANCEL_TYPE_RECONCILE.equals(cancelType) 
                && (BeanUtil.getPropInt(payTransaction, Transaction.STATUS) == Transaction.STATUS_CREATED 
                        || BeanUtil.getPropInt(payTransaction, Transaction.STATUS) == Transaction.STATUS_IN_PROG)){
            Map<String, Object> payOrderUpdate = CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(payTransaction, DaoConstants.ID),
                    Transaction.MERCHANT_ID, BeanUtil.getPropString(payTransaction, Transaction.MERCHANT_ID),
                    Transaction.STATUS, Transaction.STATUS_ERROR_RECOVERY);
            repository.getTransactionDao().updatePart(payOrderUpdate);
        }

        if(needRemoveExtendedParams) {
            extended.remove(BusinessV2Fields.EXTEND_PARAMS);
        }
        TransactionContext context = workflowManager.startWorkflow(null, null, order, cancelTransaction);
        return context;

    }
}