package com.wosai.config;

import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.spec.InvalidKeySpecException;

import org.bouncycastle.crypto.CryptoException;

import com.wosai.mpay.api.cmbcbank.CmbcBankSignUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.SM2Util;

public class HeraSm2Config {
    private boolean loadSuccess;

    /**
     * 对接hera后，由于类加载的问题，会导致首次交易时，签名耗时在20多秒左右，所以启动时提前做加载
     */
    public void loadBouncycastleClass() {
        try {
            SM2Util.unionpaySign("****************", "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgJVaVN41nyD4fbV157XZcHKMC85zK3FbjA7wQdWy6vkKgCgYIKoEcz1UBgi2hRANCAAS7mm1hTITFqaFSyRlUDR1+ciDXfxwFfK0jsOsw8RBvRjoeGYIF7Ht5FZ1xwI5XLBYMgS11TJEwY0xADERSQlHr", "wosai");
            CmbcBankSignUtil.getSign("TUlJRGl3SUJBVEJIQmdvcWdSelBWUVlCQkFJQkJnY3FnUnpQVlFGb0JERHk1MTAvRE01bkxCS3o3aEViaHFORU4yZm1KZGdObHdyVQ0KQXE1d1Z2UEdlQTJuSllKQ3RpeEs2bUFtUDNEZjVGd3dnZ003QmdvcWdSelBWUVlCQkFJQkJJSURLekNDQXljd2dnTExvQU1DQVFJQw0KQlRBV1F6WW9NQXdHQ0NxQkhNOVZBWU4xQlFBd0t6RUxNQWtHQTFVRUJoTUNRMDR4SERBYUJnTlZCQW9NRTBOR1EwRWdVMDB5SUZSRg0KVTFRZ1QwTkJNakV3SGhjTk1qSXdNekF5TURjd09ESXlXaGNOTWpjd016QXlNRGN3T0RJeVdqQnlNUXN3Q1FZRFZRUUdFd0pEVGpFTg0KTUFzR0ExVUVDZ3dFUTAxQ1F6RVNNQkFHQTFVRUN3d0pRMDFDUTE5RVEwMVRNUmt3RndZRFZRUUxEQkJQY21kaGJtbDZZWFJwYjI1aA0KYkMweE1TVXdJd1lEVlFRRERCd3dNekExUUZwS1RrWklNREF4UU9hMWp1V05sK1dJaHVpaGpFQXhNRmt3RXdZSEtvWkl6ajBDQVFZSQ0KS29FY3oxVUJnaTBEUWdBRWY5UnB5aWk5VkpmUU94OExzNXArV0dRb0tFdjhUSUU2NURWWDJQRm9iUStxZEJEZlVMZml6WEQ1Szdrbw0KVjBRcitlSjN1Y1dVYjEzWng3bkdOdnlDQ3FPQ0FaRXdnZ0dOTUI4R0ExVWRJd1FZTUJhQUZPSit0aEM3bE9zVjVxN1JGUXIvNk5lZw0KVnptZE1FZ0dBMVVkSUFSQk1EOHdQUVlJWUlFY2h1OHFBZ0l3TVRBdkJnZ3JCZ0VGQlFjQ0FSWWphSFIwY0RvdkwzZDNkeTVqWm1OaA0KTG1OdmJTNWpiaTkxY3k5MWN5MHhNeTVvZEcwd2dkUUdBMVVkSHdTQnpEQ0J5VEF2b0MyZ0s0WXBhSFIwY0Rvdkx6SXhNQzQzTkM0MA0KTWk0ekwwOURRVEl4TDFOTk1pOWpjbXd5TVRBM015NWpjbXd3Z1pXZ2daS2dnWStHZ1l4c1pHRndPaTh2TWpFd0xqYzBMalF5TGpFdw0KT2pNNE9TOURUajFqY213eU1UQTNNeXhQVlQxVFRUSXNUMVU5UTFKTUxFODlRMFpEUVNCVFRUSWdWRVZUVkNCUFEwRXlNU3hEUFVOTw0KUDJObGNuUnBabWxqWVhSbFVtVjJiMk5oZEdsdmJreHBjM1EvWW1GelpUOXZZbXBsWTNSamJHRnpjejFqVWt4RWFYTjBjbWxpZFhScA0KYjI1UWIybHVkREFMQmdOVkhROEVCQU1DQS9nd0hRWURWUjBPQkJZRUZKbi9jSTB3K2pBbzlnQld1ZE9PSkJ1MUVpU3FNQjBHQTFVZA0KSlFRV01CUUdDQ3NHQVFVRkJ3TUNCZ2dyQmdFRkJRY0RCREFNQmdncWdSelBWUUdEZFFVQUEwZ0FNRVVDSVFDV2duUTNyTWNYNUIvRg0KSEtCUVk1VlJzM1hUeXpMaHR1RzQrdm1FcEk4MWVnSWdXVHU2ZnRIalNlb0g4ZVBwb0E3Si8rdFhBK2pNUDdmZ2g4bFA2Z09PRHdRPQ==", "123abc", "****************");
        } catch (CryptoException | NoSuchAlgorithmException | NoSuchProviderException | InvalidKeySpecException | MpayException e) {
            throw new RuntimeException("SM2初始化配置出错", e);
        }
        loadSuccess = true;
    }

    public boolean isLoadSuccess() {
        return loadSuccess;
    }
}
