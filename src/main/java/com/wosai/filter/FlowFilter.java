package com.wosai.filter;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.UpayUtil;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class FlowFilter implements Filter {
    private static final String FAKE_FLAG = "fake";
    private static final String FAKE_REQUEST = "1";
    private static final String RESPONSE_FAKE_REFUSE_RESULT = JSONObject.toJSONString(UpayUtil.apiSuccess(UpayUtil.bizResponse("REQUEST_FAIL", null, "禁止fake数据进入", null))) ;
    private static final String RESPONSE_RATE_LIMIT_REFUSE_RESULT = JSONObject.toJSONString(UpayUtil.apiSuccess(UpayUtil.bizResponse("REQUEST_FAIL", null, "服务器正忙,请稍后重试", null))) ;
    
    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        boolean isFakeRequest = FAKE_REQUEST.equals(request.getHeader(FAKE_FLAG));
        if (!ApolloConfigurationCenterUtil.getFakeRequestEnable() && isFakeRequest) {
            writeJsonResult((HttpServletResponse) servletResponse, RESPONSE_FAKE_REFUSE_RESULT);
            return;
        }
        if (ApolloConfigurationCenterUtil.getRateLimiterEnable()) {
            if (!ApolloConfigurationCenterUtil.rateLimiter.tryAcquire()) {
                writeJsonResult((HttpServletResponse) servletResponse, RESPONSE_RATE_LIMIT_REFUSE_RESULT);
                return;
            }
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {
    }

    private void writeJsonResult(HttpServletResponse response, String jsonStr) throws IOException {
        response.setHeader("content-type", "application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();
        out.print(jsonStr);
        out.flush();
    }
}
