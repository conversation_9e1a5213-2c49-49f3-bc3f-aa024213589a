package com.wosai.trace;


import java.util.function.Supplier;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.Trace;

public class TimedSupplier<V> {
    private Supplier<V> supplier;
    private String name;

    public TimedSupplier(String name, Supplier<V> supplier) {
        this.name = name;
        this.supplier = supplier;
    }

    public static <V> TimedSupplier<V> of(String name, Supplier<V> r) {
        return new TimedSupplier(name, r);
    }

    public V call() {
        return callInTime(name);
    }

    @Timed(value = "arg[0]")
    @Trace
    public V callInTime(String name) {
        return this.supplier.get();
    }
}
