package com.wosai.data.jackson;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.ValueInstantiator;
import com.fasterxml.jackson.databind.node.NullNode;
import com.wosai.data.CastException;
import com.wosai.data.Row;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.support.DefaultConversionService;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.Map;

/**
 * 使用wosai-common-data包的服务，对外的api包，包含了RowDeserializer类，里面依赖的ConversionService是spring 4以上才有的。如果使用的是spring 3那么需要自定义RowDeserializer修改 ConversionService的实现。
 * 故在应用里面新建此类。 由于容器对classes里面的类的优先级优于lib包下面类的加载，故应用启动的时候，会加载此类。
 */
public class RowDeserializer extends JsonDeserializer<Object> {

    private ConversionService conversionService = new DefaultConversionService();
    private Class<?> clazz;
    private JavaType javaType;
    private BeanDescription beanDescription;

    public RowDeserializer(Class<?> clazz, JavaType javaType, BeanDescription beanDescription) {
        this.clazz = clazz;
        this.javaType = javaType;
        this.beanDescription = beanDescription;
    }

    @Override
    public Object deserialize(
            JsonParser p,
            DeserializationContext ctxt
    ) throws IOException, JsonProcessingException {
        try {
            ObjectCodec codec = p.getCodec();
            JsonNode tree = codec.readTree(p);
            return deserializeObject(p, ctxt, codec, tree);
        } catch (Exception ex) {
            if (ex instanceof IOException) {
                throw (IOException) ex;
            }
            throw new JsonMappingException(p, "Object deserialize error", ex);
        }
    }

    protected Object deserializeObject(
            JsonParser p,
            DeserializationContext ctxt,
            ObjectCodec codec,
            JsonNode node
    ) throws IOException {
        if (!node.isObject()) {
            throw new JsonParseException(p, "Row deserializer only works on json object");
        }

        ValueInstantiator vi = ctxt.getFactory().findValueInstantiator(ctxt, beanDescription);
        Row row = (Row) vi.createUsingDefault(ctxt);

        Iterator<Map.Entry<String, JsonNode>> iterator = node.fields();
        while(iterator.hasNext()) {
            Map.Entry<String, JsonNode> entry = iterator.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            row.put(key, fromJsonNode(value));
        }

        fixFields(row, p, codec);

        return row;
    }

    private static Object fromJsonNode(JsonNode node) {
        if (node.isBoolean()) {
            return node.booleanValue();
        } else if (node.isNumber()) {
            return node.numberValue();
        } else if (node.isTextual()) {
            return node.textValue();
        } else if (node.isNull()) {
            return null;
        } else {
            return node;
        }
    }

    private void fixFields(
            Row record,
            JsonParser p,
            ObjectCodec codec
    ) throws JsonProcessingException {
        Map<String, Method> getters = record._getters();
        Map<String, Method> setters = record._setters();
        for (String field: getters.keySet()) {
            Method getter = getters.get(field);
            Method setter = setters.get(field);
            if (setter != null) {
                Object to;
                try {
                    getter.invoke(record);
                } catch (IllegalAccessException | IllegalArgumentException e) {
                    throw new JsonMappingException(p, "unable to invoke getter", e);
                } catch (InvocationTargetException e) {
                    Throwable targetException = e.getTargetException();
                    if (targetException instanceof CastException) {
                        CastException castException = (CastException)targetException;
                        Object from = castException.from();
                        Class<?> toClazz = castException.toClazz();

                        if (conversionService.canConvert(from.getClass(), toClazz)) {
                            to = conversionService.convert(from, toClazz);
                        } else if (JsonNode.class.isAssignableFrom(from.getClass())
                                && ((JsonNode)from).isContainerNode()) {
                            to = codec.treeToValue((JsonNode)from, toClazz);
                        } else if (NullNode.class.isAssignableFrom(from.getClass())) {
                            to = null;
                        } else {
                            throw new JsonMappingException(
                                    p, String.format("unable to convert %s to %s", from, toClazz)
                            );
                        }

                        if (to != null) {
                            try {
                                setter.invoke(record, to);
                            } catch (IllegalAccessException
                                    | IllegalArgumentException
                                    | InvocationTargetException ex ) {
                                throw new JsonMappingException(p, "unable to invoke setter", ex);
                            }
                        }
                    }
                }
            }
        }
    }

}
