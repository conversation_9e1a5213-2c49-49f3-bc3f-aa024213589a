package com.wosai.constant;

import com.google.common.collect.ImmutableList;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.dao.Order;

import java.util.*;

/**
 * Created by jianfree on 20/4/17.
 */
public class UpayConstant {
    public static long MILLISECOND_OF_SECOND = 1000;
    public static long MILLISECOND_OF_MINUTES = MILLISECOND_OF_SECOND * 60;
    public static long MILLISECOND_OF_HOUR = MILLISECOND_OF_MINUTES * 60;
    public static long MILLISECOND_OF_DAY = MILLISECOND_OF_HOUR * 24;
    public static long MILLISECOND_OF_WEEK = MILLISECOND_OF_DAY * 7;

    public static int DEFAULT_MAX_REFUNDABLE_DAYS = 90; //默认的可退款的最大天数

    public static int DAY_MAX_BANKCARD_LIMIT = 1;
    public static int MONTH_MAX_BANKCARD_LIMIT = 2;

    // 条码支付受理终端
    public static final String BARCODE_PAYMENT_ACCEPTANCE_TERMINAL = "10";


    // 条码支付辅助受理终端
    public static final String BARCODE_PAYMENT_SUPPLEMENT_ACCEPTANCE_TERMINAL = "11";

    public static final String TERM_TYPE = "term_type";

    public static final String SYSTEM_CONFIG_NAME_TRADE_PARAMS_KEY_PROVIDER = "trade_params_key_provider"; //trade_params_key与provider的映射关系
    public static final String SYSTEM_CONFIG_NAME_PAYWAY_MAX_REFUNDABLE_DAYS = "payway_max_refundable_days"; //payway的最大可退款天数

    /** buyer_uid 字段的最大长度 **/
    public static final int MAX_LENGHT_OF_BUYER_UID_COLUMN = 45;
    /** buyer_login 字段的最大长度 **/
    public static final int MAX_LENGHT_OF_BUYER_LOGIN_COLUMN = 45;


    public static final String DYNAMIC_ID_TYPE_ALIPAY_AUTH = "22"; //支付宝预授权订单号
    public static final String DYNAMIC_ID_TYPE_ALIPAY_FT_OFFLINE = "33"; //支付宝离线刷脸支付（离线扣款场景，需要商户开通周期扣款权限）
    public static final String DYNAMIC_ID_TYPE_ALIPAY_FT = "34"; //支付宝刷脸支付
    public static final String DYNAMIC_ID_TYPE_ALIPAY_DEBIT = "35"; //支付宝代扣

    public static final String DYNAMIC_ID_TYPE_BANKCARD = "36"; //银行卡刷卡

    public static final int PROMOTION_TYPE_NO = 0; //无优惠
    public static final int PROMOTION_TYPE_CHANNEL = 1; //支付机构优惠
    public static final int PROMOTION_TYPE_SQB = 2; //收钱吧优惠

    //撤单限制， exists - 已创建过的订单不允许撤单; paid - 已支付的订单不允许撤单
    public static final String CANCEL_LIMIT_EXISTS = "exists";
    public static final String CANCEL_LIMIT_PAID = "paid";

    public static final String GOODS = "goods";
    public static final String GOODS_DETAIL = "goods_detail";
    public static final String REFUND_GOODS_DETAIL = "refund_goods_detail";
    public static final String DETAIL = "detail";
    public static final String GOODS_DETAILS = "goods_details";
    public static final String GOODS_ID = "goods_id";
    public static final String GOODS_NAME = "goods_name";
    public static final String PRICE = "price";
    public static final String QUANTITY = "quantity";
    public static final String PROMOTION_TYPE = "promotion_type";
    public static final String REFUND_QUANTITY = "refund_quantity";
    public static final String REFUND_AMOUNT = "refund_amount";
    public static final String BODY = "body";
    public static final String BESTPAY_GOODS_DETAIL = "goodsDetail";
    public static final String BESTPAY_V2_GOODS_DETAIL = "goodsDetails";

    public static final String CHINAUMS_GOODS_ID = "goodsId";
    public static final String CHINAUMS_GOODS_NAME = "goodsName";

    public static final String BESTPAY_GOODS_ID = "goodsId";
    public static final String BESTPAY_V2_GOODS_ID = "id";
    public static final String BESTPAY_GOODS_NAME = "goodsName";
    public static final String BESTPAY_V2_GOODS_NAME = "name";
    public static final String BESTPAY_GOODS_CATEGORY = "goodsCategory";
    public static final String BESTPAY_UNIT_PRICE = "unitPrice";

    public static final String SYB_UNION_PAY_ORDER_INFO = "orderInfo";
    public static final String SYB_UNION_PAY_GOODS_INFO = "goodsInfo";
    public static final String SYB_UNION_PAY_ORDER_INFO_TITLE = "title";
    public static final String SYB_UNION_PAY_ORDER_INFO_DESCRIPTION = "description";
    public static final String UNION_PAY_GOODS_INFO_ID = "id";
    public static final String UNION_PAY_GOODS_INFO_NAME = "name";
    public static final String UNION_PAY_GOODS_INFO_QUANTITY = "quantity";
    public static final String UNION_PAY_GOODS_INFO_PRICE = "price";

    public static final String ENTPAY_GOOD_NAME = "good_name";
    public static final String ENTPAY_GOOD_NUMBER = "good_number";
    public static final String ENTPAY_GOOD_AMOUNT = "good_amount";

    public static final String TRACE_ID_KEY = "trace_id_key";

    public static final String ACTIVITY_BIZ_EXT = "activity_biz_ext";
    
    public static final String SPAN_NAME = "name";
    public static final String SPAN_RESULT = "result";
    public static final String SPAN_ERROR = "error";

    
    // UpayBizError
    public static final String CODE = "code";
    public static final String ERROR_CODE = "error_code";
    public static final String NAME = "name";
    public static final String STANDARD_NAME = "standardName";
    public static final String MESSAGE = "message";
    

    public static final String REQUEST_SYSTEM_HTTP_SERVLET_RESPONSE = "__response";

    public static final String CHARSET_UTF8 = "UTF-8";


    public static final String TRANSACTION_PRODUCT_FLAG_DELIMITER = ",";

    public static final String SQB_ORIGIN_POS_PROXY = "PosProxy";

    public static final String SCOPE_DB = "db";
    public static final String SCOPE_HBASE = "hbase";

    // apollo默认退款文案响应key
    public static final String DEFAULT_REFUND_FLAG_KEY = "default";

    // 间连花呗分期商家贴息处理标识
    public static final int CHANGE_TYPE_UPDATE_HBFQ_DISCOUNT = 1;      // 添加花呗分期分账信息
    public static final int CHANGE_TYPE_REMOVE_HBFQ_DISCOUNT = 2;   // 移除花呗分期分账信息
    public static final int USE_HBFQ = 1;
    public static final int USE_CREDIT = 2;

    public static final String CREDIT_FQ_CHANNELS = "alipayfq_cc";
    public static final String TL_SWITCH_SYB_FLAG = "tl_switch_syb";

    // 终端密文
    public static final String TERMINAL_SECRET_TEXT = "secret_text";

    // 加密随机数
    public static final String TERMINAL_ENCRYPT_RAND_NUM = "encrypt_rand_num";

    public static Set<Integer> fqTypeSet = new HashSet<>(Arrays.asList(USE_HBFQ, USE_CREDIT));

    //weixin v3版本的goods_detail

    public static final String VX_V3_GOODS_DETAIL = "goods_detail";

    public static final String VX_V3_MERCHANT_GOODS_ID = "merchant_goods_id";
    public static final String VX_V3_WECHATPAY_GOODS_ID = "wechatpay_goods_id";
    public static final String VX_V3_GOODS_NAME = "goods_name";
    public static final String VX_V3_QUANTITY = "quantity";
    public static final String VX_V3_UNIT_PRICE = "unit_price";
    public static final String VX_V3_REFUND_AMOUNT = "refund_amount";
    public static final String VX_V3_REFUND_QUANTITY = "refund_quantity";

    // 通过sqb_product_flag上送产品标识
    public static Set<String> VALID_PRODUCT_FLAG_CODES = new HashSet<>(Arrays.asList(
            ProductFlagEnum.MARKET_PROGRAM_DISCOUNT.getCode(), ProductFlagEnum.REDPACK_DISCOUNT.getCode(), ProductFlagEnum.HUABEI.getCode(), 
            ProductFlagEnum.UFOOD.getCode(), ProductFlagEnum.DBB.getCode(),ProductFlagEnum.JJZ_WM.getCode(), 
            ProductFlagEnum.JJZ_ZQ.getCode(), ProductFlagEnum.I_STORE.getCode(), ProductFlagEnum.PREPAID_CARD_BUY.getCode(),
            ProductFlagEnum.MINI.getCode(), ProductFlagEnum.COUPON.getCode(), ProductFlagEnum.MEMBER_CARD.getCode(),
            ProductFlagEnum.DISCOUNT.getCode(), ProductFlagEnum.ITEM_ACTIVITY.getCode(), ProductFlagEnum.SECOND_HALF_OFF.getCode(),
            ProductFlagEnum.SQB_COUPON.getCode(), ProductFlagEnum.ACQUIRING_BIZ.getCode(), ProductFlagEnum.COMBINED_PAYMENT.getCode()
    ));

    //活动服务返回的需要额外映射的场景值
    public static Set<ProductFlagEnum> ACTIVITY_SCENES = new HashSet<>(
            Arrays.asList(
                    ProductFlagEnum.COUPON, ProductFlagEnum.MEMBER_CARD, ProductFlagEnum.DISCOUNT,
                    ProductFlagEnum.ITEM_ACTIVITY, ProductFlagEnum.SECOND_HALF_OFF, ProductFlagEnum.SQB_COUPON
            )
    );

    // 自定义参数上送产品标识
    public static Set<ProductFlagEnum> REQUEST_SCENES = new HashSet<>(Arrays.asList(
            ProductFlagEnum.MARKET_PROGRAM_DISCOUNT, ProductFlagEnum.REDPACK_DISCOUNT, 
            ProductFlagEnum.JJZ_WM, ProductFlagEnum.JJZ_ZQ, ProductFlagEnum.UFOOD, 
            ProductFlagEnum.DBB, ProductFlagEnum.I_STORE, ProductFlagEnum.PREPAID_CARD_BUY,
            ProductFlagEnum.MINI, ProductFlagEnum.FORMPAY_GENERAL, ProductFlagEnum.FORMPAY_EDU,
            ProductFlagEnum.SMART_FEE_UP, ProductFlagEnum.ISTORE_ORDER, ProductFlagEnum.ISTORE_ORDER_PHONE,
            ProductFlagEnum.ISTORE_ORDER_CASHREGISTER, ProductFlagEnum.ISTORE_DELIVERY, ProductFlagEnum.ISTORE_TAKEOUT,
            ProductFlagEnum.CAMPUS, ProductFlagEnum.CAMPUS_DELIVERY, ProductFlagEnum.THIRDPARTY_DELIVERY,
            ProductFlagEnum.COUPON_BUY, ProductFlagEnum.MEMBER_CARD_BUY, ProductFlagEnum.GROUP_NOTE,
            ProductFlagEnum.ACQUIRING_BIZ, ProductFlagEnum.COMBINED_PAYMENT, ProductFlagEnum.MERCHANT_MINI
    ));

    // 点单外卖新旧product flag值映射
    public static Map<String,String> PRODUCT_FLAG_NEW_OLDS = MapUtil.hashMap(
            ProductFlagEnum.ISTORE_ORDER.getCode(), ProductFlagEnum.UFOOD.getCode(),
            ProductFlagEnum.ISTORE_ORDER_PHONE.getCode(), ProductFlagEnum.UFOOD.getCode(),
            ProductFlagEnum.ISTORE_ORDER_CASHREGISTER.getCode(), ProductFlagEnum.UFOOD.getCode(),
            ProductFlagEnum.ISTORE_DELIVERY.getCode(), ProductFlagEnum.JJZ_WM.getCode(),
            ProductFlagEnum.ISTORE_TAKEOUT.getCode(), ProductFlagEnum.JJZ_WM.getCode(),
            ProductFlagEnum.CAMPUS.getCode(), ProductFlagEnum.JJZ_WM.getCode(),
            ProductFlagEnum.CAMPUS_DELIVERY.getCode(), ProductFlagEnum.JJZ_WM.getCode(),
            ProductFlagEnum.THIRDPARTY_DELIVERY.getCode(), ProductFlagEnum.JJZ_WM.getCode()
    );

    /**
     * 收钱吧预授权支付通道
     */
    public static final List<Integer> SQB_DEPOSIT_LIST = ImmutableList.of(
            Order.PROVIDER_UNIONPAY_OPEN, Order.PROVIDER_LAKALA_UNION_PAY,
            Order.PROVIDER_HAIKE_UNION_PAY, Order.PROVIDER_LAKALA_UNION_PAY_V3
    );

    // provider 分组基数，用于定位交易通道下标
    public static final int PROVIDER_GROUPS_INDEX_BASE = 1000;
    // provider 分组默认大小
    public static final int DEFAULT_PROVIDER_GROUPS_SIZE = 30;
    // 直连 provider 分组下标
    public static final int DIRECT_PROVIDER_GROUPS_INDEX = 0;

    // apollo config_snapshot 交易配置
    public static final String TRADE_PARAMS_REPLACE_MATCH_KEY = "match_key";
    public static final String TRADE_PARAMS_REPLACE_MATCH_VALUES = "match_values";
    public static final String TRADE_PARAMS_REPLACE_REPLACE = "replace";

    // 客户端通知网关交易状态
    public static final String CLIENT_NOTIFY_STATUS = "client_notify_status";
    public static final String CLIENT_NOTIFY_MSG = "client_notify_msg";
    public static final String CLIENT_NOTIFY_ACCOUNT_INFO = "client_notify_account_info";

    //标准日期格式
    public static final String STANDARD_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final int ACCESS_TOKEN_EXPIRE_TIME_BUFFER = 3 * 60;
    public static final String SHORT_DATETIME_FORMAT = "yyyyMMddHHmmss";

    public static final String EXTENDED_SUB_APPID = "sub_appid";

}
