package com.wosai.constant;

import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.meta.SqbScene;
import com.wosai.upay.model.dao.Transaction;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR> Date: 2019-07-12 Time: 14:37
 */
public enum ProductFlagEnum {

    MARKET_PROGRAM_DISCOUNT(ProductFlag.MARKET_PROGRAM_DISCOUNT, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(
            SqbScene.JJZ_MARKET_PROGRAM_DISCOUNT.getCode(), SqbScene.JJZ_COMMON_DISCOUNT.getCode(), SqbScene.JJZ_TIME_DISCOUNT.getCode(),
            SqbScene.JJZ_COLLECTION_DISCOUNT.getCode(), SqbScene.JJZ_PRICE_DISCOUNT.getCode(), SqbScene.JJZ_DISCOUNT_CARD.getCode(),
            SqbScene.JJZ_WEEK_CARD.getCode(), SqbScene.JJZ_MONTH_CARD.getCode(), SqbScene.JJZ_COUPON_CARD.getCode(),
            SqbScene.JJZ_PRICE_CARD.getCode(), SqbScene.JJZ_RETURN_CARD.getCode(), SqbScene.COUPON.getCode(),
            SqbScene.MEMBER_CARD.getCode(), SqbScene.DISCOUNT.getCode(), SqbScene.ITEM_ACTIVITY.getCode(),
            SqbScene.SECOND_HALF_OFF.getCode(), SqbScene.SQB_COUPON.getCode()
    ))),
    REDPACK_DISCOUNT(ProductFlag.REDPACK_DISCOUNT, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(
            SqbScene.REDPACK_DISCOUNT.getCode()
    ))),
    HUABEI(ProductFlag.HUABEI, "extended_params.extend_params.hb_fq_num", Collections.emptySet()),
    UFOOD(ProductFlag.UFOOD, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.UFOOD.getCode()))),
    DBB(ProductFlag.DBB, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.DBB.getCode()))),
    JJZ_WM(ProductFlag.JJZ_WM, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.JJZ_WM.getCode()))),
    JJZ_ZQ(ProductFlag.JJZ_ZQ, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.JJZ_ZQ.getCode()))),
    I_STORE(ProductFlag.I_STORE, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.I_STORE.getCode()))),
    MINI(ProductFlag.MINI, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.MINI.getCode()))),
    HBFQ_DISCOUNT(ProductFlag.HBFQ_DISCOUNT, "extended_params.business_params.enable_thirdparty_subsidy", Collections.emptySet()),
    FORMPAY_GENERAL(ProductFlag.FORMPAY_GENERAL, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.FORMPAY_GENERAL.getCode()))),
    FORMPAY_EDU(ProductFlag.FORMPAY_EDU, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.FORMPAY_EDU.getCode()))),
    SMART_FEE_UP(ProductFlag.SMART_FEE_UP, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.SMART_FEE_UP.getCode()))),

    ISTORE_ORDER(ProductFlag.ISTORE_ORDER, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ISTORE_ORDER.getCode()))),
    ISTORE_ORDER_PHONE(ProductFlag.ISTORE_ORDER_PHONE, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ISTORE_ORDER_PHONE.getCode()))),
    ISTORE_ORDER_CASHREGISTER(ProductFlag.ISTORE_ORDER_CASHREGISTER, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ISTORE_ORDER_CASHREGISTER.getCode()))),
    ISTORE_DELIVERY(ProductFlag.ISTORE_DELIVERY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ISTORE_DELIVERY.getCode()))),
    ISTORE_TAKEOUT(ProductFlag.ISTORE_TAKEOUT, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ISTORE_TAKEOUT.getCode()))),
    CAMPUS(ProductFlag.CAMPUS, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.CAMPUS.getCode()))),
    CAMPUS_DELIVERY(ProductFlag.CAMPUS_DELIVERY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.CAMPUS_DELIVERY.getCode()))),
    THIRDPARTY_DELIVERY(ProductFlag.THIRDPARTY_DELIVERY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.THIRDPARTY_DELIVERY.getCode()))),
    PREPAID_CARD_BUY(ProductFlag.PREPAID_CARD_BUY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.PREPAID_CARD_BUY.getCode()))),
    PREPAID_CARD(ProductFlag.PREPAID_CARD, Transaction.KEY_SQB_SCENE, new HashSet<>()),
    COUPON_BUY(ProductFlag.COUPON_BUY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.COUPON_BUY.getCode()))),
    COUPON(ProductFlag.COUPON, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.COUPON.getCode()))),
    MEMBER_CARD_BUY(ProductFlag.MEMBER_CARD_BUY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.MEMBER_CARD_BUY.getCode()))),
    MEMBER_CARD(ProductFlag.MEMBER_CARD, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.MEMBER_CARD.getCode()))),
    DISCOUNT(ProductFlag.DISCOUNT, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.DISCOUNT.getCode()))),
    ITEM_ACTIVITY(ProductFlag.ITEM_ACTIVITY, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ITEM_ACTIVITY.getCode()))),
    SECOND_HALF_OFF(ProductFlag.SECOND_HALF_OFF, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.SECOND_HALF_OFF.getCode()))),
    SQB_COUPON(ProductFlag.SQB_COUPON, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.SQB_COUPON.getCode()))),
    GROUP_NOTE(ProductFlag.GROUP_NOTE, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.GROUP_NOTE.getCode()))),
    CREDIT_CARD_INSTALMENT(ProductFlag.CREDIT_CARD_INSTALMENT,"extended_params.extend_params.fq_channels",Collections.emptySet()),
    ACQUIRING_BIZ(ProductFlag.ACQUIRING_BIZ, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.ACQUIRING_BIZ.getCode()))),
    COMBINED_PAYMENT(ProductFlag.COMBINED_PAYMENT, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.COMBINED_PAYMENT.getCode()))),
    MERCHANT_MINI(ProductFlag.MERCHANT_MINI, Transaction.KEY_SQB_SCENE, new HashSet<>(Arrays.asList(SqbScene.MERCHANT_MINI.getCode()))),
    FQ_VERSION_2(ProductFlag.FQ_VERSION_2, "extended_params.business_params.preConsultId", Collections.emptySet()),

    DEPOSIT_TYPE_SQB(ProductFlag.DEPOSIT_TYPE_SQB, Transaction.KEY_DEPOSIT_TYPE, Collections.emptySet()),

    DEPOSIT_TYPE_PAY_SOURCE(ProductFlag.DEPOSIT_TYPE_PAY_SOURCE, Transaction.KEY_DEPOSIT_TYPE, Collections.emptySet()),
    /*刷卡类型 todo key*/
    BANK_CARD_CROSS_SOURCE(ProductFlag.CROSS_CARD_PAY, Transaction.KEY_WALLET_ACCOUNT_TYPE, Collections.emptySet()),
    BANK_CARD_LOCAL_SOURCE(ProductFlag.LOCAL_CARD_PAY, Transaction.KEY_WALLET_ACCOUNT_TYPE, Collections.emptySet()),
    BANK_CARD_CROSS_INDIRECT_SOURCE(ProductFlag.CROSS_CARD_PAY_INDIRECT, Transaction.KEY_WALLET_ACCOUNT_TYPE, Collections.emptySet()),

    ;

    private String code;

    private String desc;

    private Set<String> bizFlags;

    private String key;

    ProductFlagEnum(ProductFlag productFlag, String key, Set<String> bizFlags) {
        this.code = productFlag.getCode();
        this.desc = productFlag.getName();
        this.key = key;
        this.bizFlags = bizFlags;
    }

    ProductFlagEnum(String code, String desc, String key, Set<String> bizFlags) {
        this.code = code;
        this.desc = desc;
        this.key = key;
        this.bizFlags = bizFlags;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }


    public boolean contain(String bizFlag){
        if(bizFlags == null || StringUtils.isBlank(bizFlag)) {
            return false;
        }
        String[] bizFlagArr = bizFlag.split(",");
        for (String flag : bizFlagArr) {
            if (bizFlags.contains(flag.trim())) {
                return true;
            }
        }
        return false;
    }
}
