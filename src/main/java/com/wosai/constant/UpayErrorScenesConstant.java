package com.wosai.constant;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.exception.UpaySingleTradeOverLimitError;
import com.wosai.upay.model.dao.Order;

import java.util.Map;

public class UpayErrorScenesConstant {
	public static final String CLIENT_SN_CONFLICT = "EP01"; //上送的商户订单号重复 // ERROR_SCENES_0001
	public static final String CLIENT_SN_CONFLICT_MESSAGE = "该笔交易异常，请咨询客服"; //CLIENT_SN_CONFLICT

	public static final String DISCONNECT_CALL_COREB = "EP02"; //交易时连接core-b出错 // ERROR_SCENES_0002
	public static final String DISCONNECT_CALL_COREB_MESSAGE = "该笔交易异常，请稍后重试"; //DISCONNECT_CALL_COREB

	public static final String INVALID_BARCODE_QUERY_OPENID = "EP03"; //通过顾客付款条码信息来获取用户的open id时，按喔噻的预设规则做初步校验格式不通过 // ERROR_SCENES_0003
	public static final String INVALID_BARCODE_QUERY_OPENID_MESSAGE = "请让顾客展示正确的付款码，重新收款"; //INVALID_BARCODE_QUERY_OPENID

	public static final String INVALID_BARCODE_GUESS = "EP03"; //支付环节，支付前校验条码的有效性，格式不符合规则 // ERROR_SCENES_0004
	public static final String INVALID_BARCODE_GUESS_MESSAGE = "请让顾客展示正确的付款码，重新收款"; //INVALID_BARCODE_GUESS

	public static final String INVALID_BARCODE_ERROR_PAYWAY = "EP04"; //通过条码规则反查的支付源和终端指定的支付源不一致 // ERROR_SCENES_0005
	public static final String INVALID_BARCODE_ERROR_PAYWAY_MESSAGE = "请让顾客展示正确的付款码，重新收款"; //INVALID_BARCODE_ERROR_PAYWAY

	public static final String INVALID_BARCODE_MERCHANT_CONFIG_ERROR = "EP05"; //通过顾客付款条码信息来获取用户的open id，未设置商户微信或支付宝直连交易参数，code需调整，客服转技术 // ERROR_SCENES_0006

	public static final String INVALID_BARCODE_MERCHANT_CONFIG_ERROR_MESSAGE = "收款服务尚未开通，请联系您的客户经理"; //INVALID_BARCODE_MERCHANT_CONFIG_ERROR

	public static final String UPAY_MERCHANT_NOT_EXISTS = "EP06"; //所有支付接口，喔噻商户号在系统不存在 // ERROR_SCENES_0007

	public static final String UPAY_MERCHANT_NOT_EXISTS_MESSAGE = "商户信息异常，请咨询客服"; //UPAY_MERCHANT_NOT_EXISTS

	public static final String UPAY_MERCHANT_STATUS_ABNORMAL = "EP07"; //所有支付接口，喔噻商户在喔噻系统中状态异常，如被关闭、禁用 // ERROR_SCENES_0008
	public static final String UPAY_MERCHANT_STATUS_ABNORMAL_MESSAGE = "商户信息异常，请联系您的客户经理"; //UPAY_MERCHANT_STATUS_ABNORMAL

	public static final String UPAY_PROVIDER_STATUS_LIMIT = "EQ63"; //所有支付接口退款接口，支付通道出现异常（配置在内存中）// ERROR_SCENES_0009
	public static final String UPAY_PROVIDER_STATUS_LIMIT_MESSAGE = "收款服务暂不可用，请让顾客使用其他支付方式"; //UPAY_PROVIDER_STATUS_LIMIT

	public static final String UPAY_PROVIDER_STATUS_LIMITING = "EP54"; //所有支付接口退款接口，支付通道出现异常（配置在内存中）// ERROR_SCENES_0010
	public static final String UPAY_PROVIDER_STATUS_LIMITING_MESSAGE = "该笔交易异常，请稍后重试"; //UPAY_PROVIDER_STATUS_LIMITING

	public static final String UPAY_STORE_NOT_EXISTS = "EP09"; //所有支付接口，喔噻门店号在系统不存在 // ERROR_SCENES_0011
	public static final String UPAY_STORE_NOT_EXISTS_MESSAGE = "门店信息异常，请咨询客服"; //UPAY_STORE_NOT_EXISTS

	public static final String UPAY_STORE_STATUS_ABNORMAL = "EP10"; //喔噻门店状态异常，喔噻系统内被关闭或禁用 // ERROR_SCENES_0012
	public static final String UPAY_STORE_STATUS_ABNORMAL_MESSAGE = "门店信息异常，请联系您的客户经理"; //UPAY_STORE_STATUS_ABNORMAL

	public static final String UPAY_TERMINAL_NOT_EXISTS = "EP11"; //喔噻终端在喔噻系统内不存在 // ERROR_SCENES_0013
	public static final String UPAY_TERMINAL_NOT_EXISTS_MESSAGE = "该设备不可用，请联系您的客户经理"; //UPAY_TERMINAL_NOT_EXISTS

	public static final String UPAY_TERMINAL_STATUS_ABNORMAL_NOT_ACTIVATION = "EP55"; //喔噻终端状态异常，未激活 // ERROR_SCENES_0014
	public static final String UPAY_TERMINAL_STATUS_ABNORMAL_NOT_ACTIVATION_MESSAGE = "该设备不可用，请联系您的客户经理"; //UPAY_TERMINAL_STATUS_ABNORMAL_NOT_ACTIVATION

	public static final String UPAY_TERMINAL_STATUS_ABNORMAL_DISABLED = "EP22"; //被禁用 // ERROR_SCENES_0015
	public static final String UPAY_TERMINAL_STATUS_ABNORMAL_DISABLED_MESSAGE = "该设备不可用，请联系您的客户经理"; //UPAY_TERMINAL_STATUS_ABNORMAL_DISABLED

	public static final String UPAY_TERMINAL_STATUS_ABNORMAL_UNKNOWN = "EP12"; //除未激活和禁用，正常，之外的其他状态 // ERROR_SCENES_0016
	public static final String UPAY_TERMINAL_STATUS_ABNORMAL_UNKNOWN_MESSAGE = "该设备不可用，请联系您的客户经理"; //UPAY_TERMINAL_STATUS_ABNORMAL_UNKNOWN

	public static final String TSN_GENERATOR_EXCEPTION_NOT_CONFIG = "EP13"; //支付接口，系统配置参数问题，无法生成流水号 // ERROR_SCENES_0017
	public static final String TSN_GENERATOR_EXCEPTION_NOT_CONFIG_MESSAGE = "该笔交易异常，请稍后重试"; //TSN_GENERATOR_EXCEPTION_NOT_CONFIG

	public static final String TSN_GENERATOR_EXCEPTION_DB_ERROR = "EP63"; //支付接口，流水号配置参数数据库读取异常 // ERROR_SCENES_0018
	public static final String TSN_GENERATOR_EXCEPTION_DB_ERROR_MESSAGE = "该笔交易异常，请稍后重试"; //TSN_GENERATOR_EXCEPTION_DB_ERROR

	public static final String TSN_GENERATOR_EXCEPTION_RETRY_FAIL = "EP14"; //原因同上，连续失败3次后提示 // ERROR_SCENES_0019
	public static final String TSN_GENERATOR_EXCEPTION_RETRY_FAIL_MESSAGE = "该笔交易异常，请让顾客使用其他支付方式"; //TSN_GENERATOR_EXCEPTION_RETRY_FAIL

	public static final String UPAY_CLIENT_ERROR_FAIL_NO_AUTH = "EP15"; //支付退款撤单接口被调用了不支持的功能，比如没有H5支付能力传了H5支付 // ERROR_SCENES_0020
	public static final String UPAY_CLIENT_ERROR_FAIL_NO_AUTH_MESSAGE = "该笔交易异常，请咨询客服"; //UPAY_CLIENT_ERROR_FAIL_NO_AUTH

	public static final String UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG = "EP16"; //支付退款撤单订单无法进行交易（出现在商户交易参数配置错误的情况下，么有配置服务商的agent参数）// ERROR_SCENES_0021
	public static final String UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG_MESSAGE = "商户信息异常，请咨询客服"; //UPAY_CLIENT_ERROR_ERROR_MERCHANT_CONFIG

	public static final String UPAY_SINGLE_TRADE_OVER_LIMIT = "EP17"; //单笔限额（金额在商户配置表中）// ERROR_SCENES_0022
	public static final String UPAY_SINGLE_TRADE_OVER_LIMIT_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_TRADE_OVER_LIMIT
	public static final String UPAY_SINGLE_BANKCARD_OVER_LIMIT_MESSAGE = "该笔交易超过银行卡单笔金额限制，请让顾客使用其他支付方式";
	public static final String UPAY_DAY_BANKCARD_OVER_LIMIT_MESSAGE = "该商户本日累计银行卡交易金额超限，请让顾客使用其他支付方式";
	public static final String UPAY_MONTH_BANKCARD_OVER_LIMIT_MESSAGE = "该商户本月累计银行卡交易金额超限，请让顾客使用其他支付方式";


	public static final String UPAY_DAY_PAYWAY_OVER_LIMIT_MESSAGE = "该商户本日交易方式交易金额超限，请让顾客使用其他支付方式";

	public static final String UPAY_DAY_SINGLE_OVER_LIMIT_MESSAGE = "该商户单笔交易方式交易金额超限，请让顾客使用其他支付方式";


	public static final String UPAY_STORE_OVER_DAILY_LIMIT = "EP18"; //商户每日限额 // ERROR_SCENES_0023
	public static final String UPAY_STORE_OVER_DAILY_LIMIT_MESSAGE = "今日收款总额度已超限，请联系您的客户经理"; //UPAY_STORE_OVER_DAILY_LIMIT

	public static final String VALIDATION_EXCEPTION_NOT_SUPPORT = "EP19"; //预下单时翼支付不支持CSB // ERROR_SCENES_0024
	public static final String VALIDATION_EXCEPTION_NOT_SUPPORT_MESSAGE = "该收款方式不可用，请使用门店码或扫描顾客付款码进行收款"; //VALIDATION_EXCEPTION_NOT_SUPPORT

	public static final String VALIDATION_EXCEPTION_CONFIG_ERROR = "EP20"; //支付接口交易参数费率配置小于0 // ERROR_SCENES_0025
	public static final String VALIDATION_EXCEPTION_CONFIG_ERROR_MESSAGE = "商户信息异常，请咨询客服"; //VALIDATION_EXCEPTION_CONFIG_ERROR

	public static final String WORKFLOW_INVALID_STATE = "EP21"; //预下单支付通道状态码返回了成功但是没有返回条码，需提示失败 // ERROR_SCENES_0026
	public static final String WORKFLOW_INVALID_STATE_MESSAGE = "该笔交易异常，请稍后重试"; //WORKFLOW_INVALID_STATE

	public static final String UPAY_BIZ_ERROR = "EP98"; //支付接口、退款、查证、撤单所有的接口的通用错误码，一般为技术错误信息 // ERROR_SCENES_0027
	public static final String UPAY_BIZ_ERROR_MESSAGE = "操作失败，请稍后重试"; //UPAY_BIZ_ERROR

	public static final String TRADE_TIMEOUT = "EP33"; //交易超时自动撤单 // ERROR_SCENES_0028
	public static final String TRADE_TIMEOUT_MESSAGE = "收款超时，请重试"; //TRADE_TIMEOUT

	public static final String DISCONNECT_CALL_WALLET = "EP64"; //退款时连接不上余额服务 // ERROR_SCENES_0029
	public static final String DISCONNECT_CALL_WALLET_MESSAGE = "暂不能退款，请稍后重试"; //DISCONNECT_CALL_WALLET

	public static final String VALIDATION_EXCEPTION_FAIL_ORDER_STATUS = "EP35"; //退款撤销的场景，只能撤销流水类型为退款且流水状态为成功的订单 // ERROR_SCENES_0030
	public static final String VALIDATION_EXCEPTION_FAIL_ORDER_STATUS_MESSAGE = "该订单不支持撤销，请选择正确的订单"; //VALIDATION_EXCEPTION_FAIL_ORDER_STATUS

	public static final String WALLET_NOT_ENOUGH_WANMA = "EP36"; //万马渠道退款但是余额总额不足 // ERROR_SCENES_0031
	public static final String WALLET_NOT_ENOUGH_WANMA_MESSAGE = "今日新收款余额小于退款额，请足额后重试"; //WALLET_NOT_ENOUGH_WANMA

	public static final String WALLET_NOT_ENOUGH = "EP36"; //除万马之外的渠道退款但是余额总额不足 // ERROR_SCENES_0032
	public static final String WALLET_NOT_ENOUGH_MESSAGE = "今日新收款余额小于退款额，请足额后重试"; //WALLET_NOT_ENOUGH

	public static final String SUB_WALLET_NOT_ENOUGH = "EP56"; //威富通渠道退款，余额下子账户余额不足 // ERROR_SCENES_0033
	public static final String SUB_WALLET_NOT_ENOUGH_MESSAGE = "今日#message#收款余额小于退款额，请足额后重试。"; //SUB_WALLET_NOT_ENOUGH

	public static final String UPAY_TCP_CLOSE = "EP37"; //退款时，有红包已经核销，但是网关未启用红包服务，提示不能退款 // ERROR_SCENES_0034
	public static final String UPAY_TCP_CLOSE_MESSAGE = "该订单有红包优惠，不支持退款"; //UPAY_TCP_CLOSE

	public static final String UPAY_ORDER_AMOUNT_NEED_GT_ZERO = "EP917"; //金额需大于0元
	public static final String UPAY_ORDER_AMOUNT_NEED_GT_ZERO_MESSAGE = "金额需大于0元，请联系客服";

	public static final String UPAY_ORDER_PAY_SQB_PROMOTION_DETAIL_ERROR = "EP918"; //交易时，发现上送的优惠校验不通过，可能是字段值为空或唯一性校验不通过，提示该错 // ERROR_SCENES_0035
	public static final String UPAY_ORDER_PAY_SQB_PROMOTION_DETAIL_ERROR_MESSAGE = "交易上送优惠异常，请联系客服";

	public static final String UPAY_ORDER_REFUND_OR_CANCEL_SQB_PROMOTION_DETAIL_ERROR = "EP919"; //退款或撤单时，发现上送的优惠与原订单优惠不一致，提示该错 // ERROR_SCENES_0035
	public static final String UPAY_ORDER_REFUND_OR_CANCEL_SQB_PROMOTION_DETAIL_ERROR_MESSAGE = "退款或撤单上送优惠异常，请联系客服";

	public static final String UPAY_ORDER_AMOUNT_NOT_MATCH = "EP58"; //退款时，发现已经核销的优惠金额与交易时的优惠金额不一致，有逻辑错误，提示该错 // ERROR_SCENES_0035
	public static final String UPAY_ORDER_AMOUNT_NOT_MATCH_MESSAGE = "退款异常，请联系客服"; //UPAY_ORDER_AMOUNT_NOT_MATCH
	
	public static final String UPAY_REFUND_ORDER_NOOP = "EP39"; //退款金额超过了该笔订单的可退金额 // ERROR_SCENES_0036
	public static final String UPAY_REFUND_ORDER_NOOP_MESSAGE = "退款金额大于订单金额，请确认"; //UPAY_REFUND_ORDER_NOOP

	public static final String UPAY_REFUND_REPEAT = "EP40"; //单笔退款重复发起调用 // ERROR_SCENES_0037
	public static final String UPAY_REFUND_REPEAT_MESSAGE = "该订单退款已经在处理中，请稍候"; //UPAY_REFUND_REPEAT

	public static final String UPAY_REFUND_ALL_REFUND = "EP41"; //退款时，订单已经是全额退款完成 // ERROR_SCENES_0038
	public static final String UPAY_REFUND_ALL_REFUND_MESSAGE = "该订单已全额退款"; //UPAY_REFUND_ALL_REFUND

	public static final String UPAY_REFUND_INVALID_ORDER_STATE = "EP42"; //这笔订单不为支付成功，发起了退款 // ERROR_SCENES_0039
	public static final String UPAY_REFUND_INVALID_ORDER_STATE_MESSAGE = "该订单收款未成功，不支持退款"; //UPAY_REFUND_INVALID_ORDER_STATE

	public static final String UPAY_REFUND_OVER_DATE_LIMIT = "EP43"; //超过最大退款时间（超时时间配置在数据库）// ERROR_SCENES_0040
	public static final String UPAY_REFUND_OVER_DATE_LIMIT_MESSAGE = "#message#天之前的订单不允许退款，请用其他方式退款"; //ERROR_SCENES_0040

	public static final String UPAY_ORDER_NOT_ALLOWED = "EP37"; //一笔订单使用了红包核销，不能撤单 // ERROR_SCENES_0041
	public static final String UPAY_ORDER_NOT_ALLOWED_MESSAGE = "该笔订单有红包核销，不支持退款"; //ERROR_SCENES_0041

	public static final String LKL_PROVIDER_CANNOT_REFUND = "EP44"; //拉卡拉渠道交易目前不允许退款 // ERROR_SCENES_0042
	public static final String LKL_PROVIDER_CANNOT_REFUND_MESSAGE = "该交易不支持退款，请用其他方式退款"; //LKL_PROVIDER_CANNOT_REFUND

	public static final String UPAY_TCP_ORDER_CANNOT_REFUND = "EP37"; //参与了红包或立减优惠活动不能退款，或活动网关抛的其他异常导致不能退款，提示此错误 // ERROR_SCENES_0043
	public static final String UPAY_TCP_ORDER_CANNOT_REFUND_MESSAGE = "该笔订单有红包核销，不支持退款"; //UPAY_TCP_ORDER_CANNOT_REFUND

	public static final String UPAY_TCP_ORDER_AMOUNT_NOT_MATCH = "EP34"; //撤单时，带优惠核销的订单，发现撤单金额和实际支付金额不一致 // ERROR_SCENES_0044
	public static final String UPAY_TCP_ORDER_AMOUNT_NOT_MATCH_MESSAGE = "该订单数据异常无法撤销，请咨询客服"; //UPAY_TCP_ORDER_AMOUNT_NOT_MATCH

	public static final String REFUND_AMOUNT_ERROR = "EP39"; //退款时，退款金额大于实际收款金额 // ERROR_SCENES_0045
	public static final String REFUND_AMOUNT_ERROR_MESSAGE = "退款金额大于订单金额，请确认"; //REFUND_AMOUNT_ERROR

	public static final String REFUND_FAIL = "EP45"; //退款失败，请重试！	// ERROR_SCENES_0046
	public static final String REFUND_FAIL_MESSAGE = "退款失败，请重试"; //REFUND_FAIL

	public static final String CANCEL_REPEAT = "EP47"; //重复撤单 // ERROR_SCENES_0047
	public static final String CANCEL_REPEAT_MESSAGE = "该订单撤销正在处理中，请稍候"; //CANCEL_REPEAT

	public static final String DEPOSIT_CANCELED = "EP916"; //重复撤单 // ERROR_SCENES_0916
	public static final String DEPOSIT_CANCELED_MESSAGE = "该笔已经做完预授权撤销成功，无需操作此订单"; //DEPOSIT_CANCELED

	public static final String CANCEL_ORDER_IN_PAY_PROG = "EP48"; //订单正在支付中，created状态，等待支付源返回时，其他服务器 节点发起撤单且没有超时10分钟，不允许撤单 // ERROR_SCENES_0048
	public static final String CANCEL_ORDER_IN_PAY_PROG_MESSAGE = "该订单正在处理中，暂不支持撤销"; //CANCEL_ORDER_IN_PAY_PROG

	public static final String CANCEL_INVALID_ORDER_STATE = "EP49"; //订单状态是除以上状态外的状态比如已退款，不允许撤单 // ERROR_SCENES_0049
	public static final String CANCEL_INVALID_ORDER_STATE_MESSAGE = "该订单不支持撤销，请选择正确的订单"; //UPAY_CANCEL_INVALID_ORDER_STATE

	public static final String CANCEL_FAIL = "EP49"; //业务处理异常（门店码csb不支持撤单？）// ERROR_SCENES_0050
	public static final String CANCEL_FAIL_MESSAGE = "该订单不支持撤销，请选择正确的订单"; //CANCEL_FAIL

	public static final String TRADE_NO_REPEAT = "EG04"; //撤单、退款、勾兑、查单时，喔噻是多笔订单号但是第三方交易订单号存在重复 // ERROR_SCENES_0051
	public static final String TRADE_NO_REPEAT_MESSAGE = "订单号不存在"; //ERROR_SCENES_0052

	public static final String ORDER_NOT_EXIST = "EG11"; //撤单、退款、勾兑、查单时，喔噻系统内没有发现该订单 // ERROR_SCENES_0052
	public static final String ORDER_NOT_EXIST_MESSAGE = "订单号不存在"; //ORDER_NOT_EXIST

	public static final String CANCEL_ORDER_NOT_EXIST = "EG12"; //在退款撤单和勾兑退款和撤单交易时，喔噻系统内没有发现该订单 // ERROR_SCENES_0053
	public static final String CANCEL_ORDER_NOT_EXIST_MESSAGE = "订单号不存在"; //CANCEL_ORDER_NOT_EXIST

	public static final String REFUND_FAILED_PROVIDER = "EP45"; //调用支付通道退款出错 // ERROR_SCENES_0054
	public static final String REFUND_FAILED_PROVIDER_MESSAGE = "退款失败，请重试"; //REFUND_FAILED_PROVIDER
	
	public static final String FIX_ORDER_STATUS_ERROR = "EG01"; //勾兑时只能勾兑状态为异常的订单，如 pay error， pay cancel（time out），created（pay + precreate），实时扫描10分钟之内的订单转入勾兑服务，如果是订单发现是成功的，没必要做勾兑就返回这个错误 // ERROR_SCENES_0055
	public static final String FIX_ORDER_STATUS_ERROR_MESSAGE = "成功交易无需勾兑"; //FIX_ORDER_STATUS_ERROR

	public static final String FIX_ORDER_NOT_SUPPORT = "EG02"; //服务不同节点，订单处理中，不允许勾兑 // ERROR_SCENES_0056
	public static final String FIX_ORDER_NOT_SUPPORT_MESSAGE = "该订单未完成不支持勾兑"; //ERROR_SCENES_0056

	public static final String FIX_ORDER_STATUS_NOT_SUPPORT = "EG03"; //比如对已经用户cancel的订单做勾兑，报此错误 // ERROR_SCENES_0057
	public static final String FIX_ORDER_STATUS_NOT_SUPPORT_MESSAGE = "该订单不支持勾兑"; //FIX_ORDER_STATUS_NOT_SUPPORT

	public static final String REFUND_REVOKE_STATUS_NOT_SUPPORT = "EG05"; //针对撤单和退款场景，勾兑撤单和退款交易流水时，如果流水不符合以上类型且流水状态为处理成功时返回此错误 // ERROR_SCENES_0058
	public static final String REFUND_REVOKE_STATUS_NOT_SUPPORT_MESSAGE = "该订单不支持勾兑"; //REFUND_REVOKE_STATUS_NOT_SUPPORT

	public static final String ORDER_AND_TSN_NOT_MATCH = "EG06"; //针对撤单和退款的场景，勾兑交易订单号和流水号不一致，返回此错误；退款撤销时，也会有这个错误 // ERROR_SCENES_0059
	public static final String ORDER_AND_TSN_NOT_MATCH_MESSAGE = "该订单不支持勾兑"; //ORDER_AND_TSN_NOT_MATCH

	public static final String FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR = "EG07"; //针对撤单和退款场景，勾兑时发现流水状态不为refund error或cancel error，返回此错误 // ERROR_SCENES_0060
	public static final String FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR_MESSAGE = "该订单不支持勾兑"; //FIX_CANCEL_OR_REFUND_ORDER_STATUS_ERROR

	public static final String FIX_ORDER_STATUS_TO_PAID_ORDER_AMOUNT_NOT_MATCH = "EG08"; //对已撤单失败的交易状态恢复为支付成功时，校验订单金额，不一致 // ERROR_SCENES_0061
	public static final String FIX_ORDER_STATUS_TO_PAID_ORDER_AMOUNT_NOT_MATCH_MESSAGE = "勾兑确认订单金额不一致"; //FIX_ORDER_STATUS_TO_PAID_ORDER_AMOUNT_NOT_MATCH

	public static final String FIX_ORDER_STATUS_TO_PAID_ORDER_STATUS_NOT_MATCH = "EG09"; //退款和撤单的勾兑服务，订单状态不是退款失败、退款撤销失败，不能退款勾兑 // ERROR_SCENES_0062
	public static final String FIX_ORDER_STATUS_TO_PAID_ORDER_STATUS_NOT_MATCH_MESSAGE = "该订单不支持勾兑"; //FIX_ORDER_STATUS_TO_PAID_ORDER_STATUS_NOT_MATCH

	public static final String FIX_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT= "EG10"; //退款失败的勾兑 // ERROR_SCENES_0063
	public static final String FIX_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT_MESSAGE = "该订单不支持勾兑"; //ERROR_SCENES_0063

	public static final String PROVIDER_NOT_SUPPORT = "EP65"; //交易通道被关闭（sub_payway 级别 ） // ERROR_SCENES_0064
	public static final String PROVIDER_NOT_SUPPORT_MESSAGE = "暂不支持此收款方式，请联系您的客户经理"; //PROVIDER_NOT_SUPPORT

	public static final String PROVIDER_NO_AUTH = "EP66"; //交易渠道没有开启交易通道的权限（例如：中信渠道只开启了微信支付，在使用支付宝支付时，会提示该错误) // ERROR_SCENES_0065
	public static final String PROVIDER_NO_AUTH_MESSAGE = ""; //ERROR_SCENES_0066

	public static final String MERCHANT_BEING_OPENING = "EP67"; //间连商户没有拉卡拉结算账户 // ERROR_SCENES_0066
	public static final String MERCHANT_BEING_OPENING_MESSAGE = "商户还在开通中，请稍后再试"; //MERCHANT_BEING_OPENING

	public static final String WFT_ALIPAY_BEING_OPENING = "EP68"; //如果走威富通的交易， 并且支付宝报备没有成功，那么不允许交易 // ERROR_SCENES_0067
	public static final String WFT_ALIPAY_BEING_OPENING_MESSAGE = "支付宝收款开通中，请耐心等待"; //WFT_ALIPAY_BEING_OPENING

	public static final String PAY_ALIPAY_TOKEN_FAIL = "EP69"; //支付宝授权token获取失败 // ERROR_SCENES_0068
	public static final String PAY_ALIPAY_TOKEN_FAIL_MESSAGE = "商户信息异常，请咨询客服"; //PAY_ALIPAY_TOKEN_FAIL

	public static final String PAY_MERCHANT_CONFIG_ERROR = "EP70"; //交易参数配置出现问题，商户没有入网成功，或者商户切交易参数时配置出错 // ERROR_SCENES_0069
	public static final String PAY_MERCHANT_CONFIG_ERROR_MESSAGE = "商户信息异常，请咨询客服"; //PAY_MERCHANT_CONFIG_ERROR

	public static final String PAY_WFT_WEIXIN_NOT_ALLOWED = "EP71"; //走威富通通道的微信交易不能用大商户进行交易 // ERROR_SCENES_0070
	public static final String PAY_WFT_WEIXIN_NOT_ALLOWED_MESSAGE = "商户微信收款开通中，请联系您的客户经理"; //PAY_WFT_WEIXIN_NOT_ALLOWED_

	public static final String PAY_ORDER_AlEADY_IN_PROG = "EP72"; //允许商户重复订单号交易且订单初一支付中时提示该错误 // ERROR_SCENES_0071
	public static final String PAY_ORDER_AlEADY_IN_PROG_MESSAGE = "订单还在处理中, 请稍后重试"; //PAY_ORDER_AlEADY_IN_PROG

	public static final String PAY_LKL_UNIONPAY_AMOUNT_LIMIT = "EP73"; //拉卡拉银联二维码限额1000 // ERROR_SCENES_0072
	public static final String PAY_LKL_UNIONPAY_AMOUNT_LIMIT_MESSAGE = "该支付方式单笔付款金额不得超过1000元"; //PAY_LKL_UNIONPAY_AMOUNT_LIMIT

	public static final String FIX_ORDER_ALEADY_MODIFYED = "EP74"; //重复勾兑造成数据不一致时提示此错误 // ERROR_SCENES_0073
	public static final String FIX_ORDER_ALEADY_MODIFYED_MESSAGE = "订单已被修复，请确认订单状态"; //FIX_ORDER_ALEADY_MODIFYED

	public static final String FIX_FAIL = "EP75"; //订单勾兑失败时返回此错误信息 // ERROR_SCENES_0074
	public static final String FIX_FAIL_MESSAGE = "该订单不可被修正"; //FIX_FAIL
	
	public static final String REFUND_ORDER_STATUS_ERROR = "EP76"; // ERROR_SCENES_0075
	public static final String REFUND_ORDER_STATUS_ERROR_MESSAGE = "该订单暂不能退款，请咨询客服"; //REFUND_ORDER_STATUS_ERROR

	public static final String ORDER_CAN_NOT_REFUND_ERROR = "EP97"; //商户切换交易通道，且原通道支付金额不足以进行 // ERROR_SCENES_0076
	public static final String ORDER_CAN_NOT_REFUND_ERROR_MESSAGE = "订单暂不能退款，请咨询客服。"; //ORDER_CAN_NOT_REFUND_ERROR

	public static final String UPAY_SINGLE_LIMIT_WEIXIN_BARCODE_ERROR = "EP92"; // ERROR_SCENES_0077
	public static final String UPAY_SINGLE_LIMIT_WEIXIN_BARCODE_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_WEIXIN_BARCODE_ERROR
	
	public static final String UPAY_SINGLE_LIMIT_WEIXIN_QRCODE_ERROR = "EP93"; // ERROR_SCENES_0078
	public static final String UPAY_SINGLE_LIMIT_WEIXIN_QRCODE_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_WEIXIN_QRCODE_ERROR

	public static final String UPAY_SINGLE_LIMIT_WEIXIN_WAP_ERROR = "EP93"; // ERROR_SCENES_0079
	public static final String UPAY_SINGLE_LIMIT_WEIXIN_WAP_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_WEIXIN_WAP_ERROR
	
	public static final String UPAY_SINGLE_LIMIT_WEIXIN_MINI_ERROR = "EP93"; // ERROR_SCENES_0080
	public static final String UPAY_SINGLE_LIMIT_WEIXIN_MINI_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_WEIXIN_MINI_ERROR

	public static final String UPAY_SINGLE_LIMIT_ALIPAY_BARCODE_ERROR = "EP94"; // ERROR_SCENES_0081
	public static final String UPAY_SINGLE_LIMIT_ALIPAY_BARCODE_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_ALIPAY_BARCODE_ERROR

	public static final String UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR = "EP95"; // ERROR_SCENES_0082
	public static final String UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR

	public static final String UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR = "EP95"; // ERROR_SCENES_0083
	public static final String UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR
	
	public static final String UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR = "EP95"; // ERROR_SCENES_0084
	public static final String UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR_MESSAGE = "该笔交易金额超限，请让顾客使用其他支付方式"; //UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR

	public static final String ORDER_PAY_FAILED_ERROR = "EP105"; // ERROR_SCENES_0085
	public static final String ORDER_PAY_FAILED_ERROR_MESSAGE = "该笔交易正在处理中，请稍后重试"; //ORDER_PAY_FAILED_ERROR
	
	public static final String TRANSACTION_NOT_EXIST = "EG11"; // 通过退款订单号未找到退款流水 // ERROR_SCENES_0086
	public static final String TRANSACTION_NOT_EXIST_MESSAGE = "退款订单不存在"; //TRANSACTION_NOT_EXIST

	public static final String FORMAL_MERCHNAT_CONFIG_ERROR = "EP788"; //商户正式交易参数配置错误 // ERROR_SCENES_0087
	public static final String FORMAL_MERCHNAT_CONFIG_ERROR_MESSAGE = "商户信息异常，请联系您的客户经理"; //PAY_WFT_WEIXIN_NOT_ALLOWED_

	public static final String ORDER_IN_PROG = "EP104"; // ERROR_SCENES_0088
	public static final String ORDER_IN_PROG_MESSAGE = "交易未完成，等待顾客输入密码";//ORDER_IN_PROG
	
	public static final String RATE_LIMITER = "EP123"; // ERROR_SCENES_0089
    public static final String RATE_LIMITER_MESSAGE = "业务繁忙，请稍后再试";//RATE_LIMITER
    
    public static final String QUERY_EXPRIE_ERROR = "EP124"; // ERROR_SCENES_0090
    public static final String QUERY_EXPRIE_MESSAGE = "支付失败";//QUERY_EXPRIE_ERROR

	public static final String DEPOSIT_LIMITER = "EP127"; // ERROR_SCENES_0091
    public static final String DEPOSIT_LIMITER_MESSAGE = "预授权未开通，请联系客户经理";//DEPOSIT_LIMITER

	public static final String DEPOSIT_NOT_SUPPORT  = "EP128"; // ERROR_SCENES_0092
    public static final String DEPOSIT_NOT_SUPPORT_MESSAGE = "商户信息异常，请联系客户经理";//DEPOSIT_NOT_SUPPORT
    
    public static final String DEPOSIT_CONSUME_AMOUNT_ORDER_NOOP = "EP129"; // ERROR_SCENES_0093
    public static final String DEPOSIT_CONSUME_AMOUNT_ORDER_NOOP_MESSAGE = "完成金额大于订单金额，请确认"; //DEPOSIT_CONSUME_AMOUNT_ORDER_NOOP
    
    public static final String DEPOSIT_CONSUME_ALEADY_CONSUME = "EP130"; // ERROR_SCENES_0094
    public static final String DEPOSIT_CONSUME_ALEADY_CONSUME_MESSAGE = "该订单已预授权完成"; //DEPOSIT_CONSUME_ALEADY_CONSUME

    public static final String DEPOSIT_CONSUME_INVALID_ORDER_STATE = "EP131"; // ERROR_SCENES_0095
    public static final String DEPOSIT_CONSUME_INVALID_ORDER_STATE_MESSAGE = "该订单收款状态异常，不支持预授权完成"; //DEPOSIT_CONSUME_INVALID_ORDER_STATE

    public static final String FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT= "EP132"; // ERROR_SCENES_0096
    public static final String FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT_MESSAGE = "该订单不支持勾兑"; //FIX_DEPOSIT_ORDER_STATUS_IF_REFUND_NOT_SUCCESS_NOT_SUPPORT

    public static final String FIX_CONSUME_ORDER_STATUS_ERROR= "EP133"; // ERROR_SCENES_0097
    public static final String FIX_CONSUME_ORDER_STATUS_ERROR_MESSAGE = "该订单不支持勾兑"; //FIX_CONSUME_ORDER_STATUS_ERROR

	public static final String HUABEI_QRCODE_TYPE_ERROE= "EP25"; // ERROR_SCENES_0098
	public static final String HUABEI_QRCODE_TYPE_ERRO_ERROR_MESSAGE = "请使用支付宝付款码";


	public static final String UPAY_TERMINAL_CONFIG_STATUS_ABNORMAL_DISABLED = "EP136"; // ERROR_SCENES_0099
	public static final String UPAY_TERMINAL_CONFIG_STATUS_ABNORMAL_DISABLED_MESSAGE = "该终端收款功能已关闭，请使用其他终端收款";


	public static final String UPAY_STORE_CONFIG_STATUS_ABNORMAL_DISABLED = "EP137"; // ERROR_SCENES_0100
	public static final String UPAY_STORE_CONFIG_STATUS_ABNORMAL_DISABLED_MESSAGE = "该门店收款功能已关闭";

	public static final String SHORT_URL_SERVER_ERR = "EP780"; // ERROR_SCENES_0101
	public static final String SHORT_URL_SERVER_ERR_MESSAGE = "短连接服务暂不可用";

	public static final String ORDER_CAN_NOT_REFUND_CANCEL_ERROR = "EP165"; //商户切换清算通道，不容许进行退款和撤单 // ERROR_SCENES_0102
	public static final String ORDER_CAN_NOT_REFUND_CANCEL_ERROR_MESSAGE = "订单暂不能退款或撤单，请咨询客服。"; //ORDER_CAN_NOT_REFUND_CANCEL_ERROR

    public static final String CANCEL_ORDER_HAS_PROFIT_SHARING = "EP781"; // ERROR_SCENES_0103
	public static final String CANCEL_ORDER_HAS_PROFIT_SHARING_MESSAGE = "分账订单已支付成功，不允许撤单, 请退款";

	public static final String UPAY_MERCHANT_OVER_DAILY_PAYWAY_LIMIT = "EP139"; //商户每日支付方式限额 // ERROR_SCENES_0104
	public static final String UPAY_MERCHANT_OVER_DAILY_PAYWAY_LIMIT_MESSAGE = "今日该支付方式收款总额度已超限，请使用其他支付方式"; //UPAY_MERCHANT_OVER_DAILY_PAYWAY_LIMIT

	public static final String CANCEL_ORDER_NOT_SUPPORT_NEXT_DAY_CANCEL = "EP782"; //商户每日支付方式限额 // ERROR_SCENES_0105
	public static final String CANCEL_ORDER_NOT_SUPPORT_NEXT_DAY_CANCEL_MESSAGE = "此订单不支持隔日撤销";

	public static final String INVALID_MERCHANT_CONFIG_ERROR = "EP142"; //商户不是直连/银联间连商户，客服转技术 // ERROR_SCENES_0106
    public static final String INVALID_MERCHANT_CONFIG_ERROR_MESSAGE = "当前业务不支持，请联系您的客户经理"; // NOT_SUPPORT

    public static final String REFUND_OVER_DAILY_LIMIT_ERROR = "EP143"; //商户退款测试超限，需要采取其它方式退款 // ERROR_SCENES_0107
    public static final String REFUND_OVER_DAILY_LIMIT_ERROR_MESSAGE = "退款次数已超限，请使用其他方式退款"; // REFUND_OVER_DAILY_LIMIT

    public static final String CANCEL_ORDER_IN_PROG = "EP149"; //撤销订单正在勾兑处理中，需要稍后再进行撤单 // ERROR_SCENES_0108
    public static final String CANCEL_ORDER_IN_PROG_MESSAGE = "订单正在处理中，请稍后再试"; //CANCEL_ORDER_IN_PROG

	public static final String PAY_STATUS_CLOSED = "EP140"; // ERROR_SCENES_00106
	public static final String PAY_STATUS_CLOSED_MESSAGE = "商户收款权限被关闭，请联系您的客户经理";

	public static final String UPAY_DEPOSIT_CONSUMER_OVER_DATE_LIMIT = "EP154"; // ERROR_SCENES_0116
    public static final String UPAY_DEPOSIT_CONSUMER_OVER_DATE_LIMIT_MESSAGE = "超过1年的预授权订单不允许预授权完成";

    public static final String UPAY_CANCEL_OVER_DATE_LIMIT = "EP152"; //超过最大撤单时间 // ERROR_SCENES_0117
    public static final String UPAY_CANCEL_OVER_DATE_LIMIT_MESSAGE = "超过30天之前的订单不允许撤单";

	public static final String ACROSS_STORE_REFUND_CLOSE = "EP150"; // ERROR_SCENES_0115
	public static final String ACROSS_STORE_REFUND_CLOSE_MESSAGE = "该商户不允许跨门店退款";

	public static final String GEN_SN_ILLEGAL = "EP783"; // ERROR_SCENES_0118
	public static final String GEN_SN_ILLEGAL_MESSAGE = "无效的收钱吧单号";

	public static final String GEN_SN_NO_PERMISSION = "EP784"; // ERROR_SCENES_0119
	public static final String GEN_SN_NO_PERMISSION_MESSAGE = "暂无权限生成单号";

	public static final String FIX_CONSUME_QUERY_ERROR= "EP785"; // ERROR_SCENES_0120
	public static final String FIX_CONSUME_QUERY_ERROR_MESSAGE = "支付通道查询失败或者该订单未消费完成";

    public static final String UPAY_DEPOSIT_CANCEL_OVER_DATE_LIMIT = "EP155"; //超过最大撤单时间 // ERROR_SCENES_0121
    public static final String UPAY_DEPOSIT_CANCEL_OVER_DATE_LIMIT_MESSAGE = "超过1年的预授权订单不允许撤单";

    public static final String REFUND_FLAG_NOT_MATCH = "EP144"; // ERROR_SCENES_0109
    public static final String REFUND_FLAG_NOT_MATCH_MESSAGE = "退款或者撤单失败，请核实上送的退款标识是否正确";

    public static final String DISCONNECT_CALL_PROFIT_SHARING = "EP786"; //交易时连接分账服务出错 // ERROR_SCENES_0110
    public static final String DISCONNECT_CALL_PROFIT_SHARING_MESSAGE = "该笔交易异常，请稍后重试"; //DISCONNECT_CALL_PROFIT_SHARING

	public static final String PROFIT_SHARING_NOT_SUPPORT = "EP145"; //商户当前收单机构不支持分账 // ERROR_SCENES_0111
	public static final String PROFIT_SHARING_NOT_SUPPORT_MESSAGE = "商户当前收单机构不支持分账"; //PROFIT_SHARING_NOT_SUPPORT

	public static final String SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS = "EP146"; //原支付订单分账还未完成，不允许进行分账回退 // ERROR_SCENES_0112
	public static final String SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS_MESSAGE = "支付订单分账中，退款失败，请稍后再试"; //SHARING_RESTITUTE_NOT_ALLOW_AS_SHARING_PAY_IN_PROGRESS

	public static final String SHARING_RESTITUTE_WEIXIN_RESTRICT = "EP147";//请在微信分账完成1个小时之后再进行退款回退 // ERROR_SCENES_0113
	public static final String SHARING_RESTITUTE_WEIXIN_RESTRICT_MESSAGE = "支付订单分账中，退款失败，请1个小时后再发起退款";//SHARING_RESTITUTE_WEIXIN_RESTRICT

	public static final String SHARING_COMMON_ERROR = "EP148";//分账通用错误 // ERROR_SCENES_0114
	public static final String SHARING_COMMON_ERROR_MESSAGE = "该笔交易异常，请咨询客服";//SHARING_COMMON_ERROR

    public static final String TRADE_APP_NO_AUTH = "EP156";// 业务方无权限 // ERROR_SCENES_0122
    public static final String TRADE_APP_NO_AUTH_MESSAGE = "该笔交易异常，请咨询客服";//TRADE_APP_NO_AUTH

    public static final String TRADE_APP_NOT_CONFIG = "EP157";//终端未配置交易参数 // ERROR_SCENES_0123
    public static final String TRADE_APP_NOT_CONFIG_MESSAGE = "该笔交易异常，请咨询客服";//TRADE_APP_NOT_CONFIG

    public static final String SHARING_RESTITUTE_AMOUNT_LIMIT_ERROR = "EP173"; //分账回退金额超出限制 // ERROR_SCENES_0125
    public static final String SHARING_RESTITUTE_AMOUNT_LIMIT_MESSAGE = "分账回退金额不能大于退款金额"; //分账回退金额超出限制

    public static final String REFUND_ENTRANCE_ERROR = "EP191"; // ERROR_SCENES_0126
    public static final String REFUND_ENTRANCE_ERROR_MESSAGE = "退款失败或撤单失败，请从储值入口发起退款";

    public static final String DEPOSIT_SYNC_INVALID_ORDER_STATE = "EP194"; //订单状态状态异常，不允许同步 // ERROR_SCENES_0127
    public static final String DEPOSIT_SYNC_INVALID_ORDER_STATE_MESSAGE = "该订单不支持同步，请选择正确的订单"; //DEPOSIT_SYNC_INVALID_ORDER_STATE

    public static final String DEPOSIT_SYNC_ERROR = "EP195"; //订单不支持同步 // ERROR_SCENES_0128
    public static final String DEPOSIT_SYNC_ERROR_MESSAGE = "订单同步失败，请稍后再试"; //DEPOSIT_SYNC_ERROR

    public static final String DEPOSIT_CONSUME_INVALID_CONSUME_AMOUNT = "EP203"; // ERROR_SCENES_0129
    public static final String DEPOSIT_CONSUME_INVALID_CONSUME_AMOUNT_MESSAGE = "预授权完成金额异常，请稍后再试"; //DEPOSIT_CONSUME_INVALID_CONSUME_AMOUNT

	public static final String ORDER_FIX_CONCURRENCE_ERROR = "EP203"; // ERROR_SCENES_0129
	public static final String ORDER_FIX_CONCURRENCE_ERROR_MESSAGE = "勾兑并发异常";

	public static final String PROFIT_SHARING_IN_APPLY_ERROR = "EP213"; // ERROR_SCENES_0130
	public static final String PROFIT_SHARING_IN_APPLY_MESSAGE = "分账开通中，请稍后再试";

	public static final String PROFIT_SHARING_IN_APPLY_SMART_STORE_ERROR = "EP212"; // ERROR_SCENES_0131
	public static final String PROFIT_SHARING_IN_APPLY_SMART_STORE_MESSAGE = "分账参数异常，请联系客户经理";

    public static final String QUERY_HBFQ_NUM_NOT_SUPPORT = "EP214"; // ERROR_SCENES_0132
    public static final String QUERY_HBFQ_NUM_NOT_SUPPORT_MESSAGE = "订单暂不支持花呗分期查询，请稍后再试"; //QUERY_HBFQ_NUM_NOT_SUPPORT

    public static final String WALLET_NOT_ENOUGH_CHANGE_CLEARNANCE_PROVIDER = "EP215"; //切清算通道，原通道余额不足 // ERROR_SCENES_0133
    public static final String WALLET_NOT_ENOUGH_CHANGE_CLEARNANCE_PROVIDER_MESSAGE = "该笔交易对应通道余额不足，不允许退款"; //WALLET_NOT_ENOUGH_CHANGE_CLEARNANCE_PROVIDER

	public static final String DEPOSIT_CANCELED_ORDER_STATE = "EP906";

	public static final String DEPOSIT_CANCELED_ORDER_STATE_MESSAGE = "预授权交易已经撤销，不支持发起完成"; //DEPOSIT_CANCELED_ORDER_STATE

    public static final String UPAY_HUABEI_FQ_NUM_PARAM_ERROR_MESSAGE = "花呗分期分期数参数错误";
	public static final String UPAY_FQ_NUM_PARAM_ERROR_MESSAGE = "分期分期数参数错误";
	public static final String UPAY_HUABEI_FQ_PERCENT_PARAM_ERROR_MESSAGE = "花呗分期商家手续费比例参数错误";
	public static final String UPAY_FQ_PERCENT_PARAM_ERROR_MESSAGE = "分期商家手续费比例参数错误";
	public static final String UPAY_HUABEI_FQ_BUYER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE = "花呗分期买家手续费参数错误";
	public static final String UPAY_FQ_BUYER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE = "分期买家手续费参数错误";
	public static final String UPAY_HUABEI_FQ_SELLER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE = "花呗分期卖家手续费参数错误";
	public static final String UPAY_FQ_SELLER_SERVICE_CHARGE_PARAM_ERROR_MESSAGE = "分期卖家手续费参数错误";
	public static final String UPAY_FQ_THIRDPARTY_SUBSIDY_ERROR_MESSAGE = "不支持分期商家贴息，请稍后再试";
	public static final String UPAY_FQ_TYPE_SERVICE_CHARGE_PARAM_ERROR_MESSAGE = "分期类型参数错误";
	public static final String UPAY_AUTH_SUB_PAYWAY_ERROR_MESSAGE = "商户预授权(签约)支付方式错误，只支持小程序方式";

	public static final String UPAY_HUABEI_FQ_SHARECODE = "EP787"; // ERROR_SCENES_0134
	public static final String UPAY_HUABEI_FQ_SHARECODE_MESSAGE = "获取吱口令失败";

    public static final String WALLET_NOT_ENOUGH_CHANGE_PROFIT_SHARING = "EP236"; //商户存在分账，余额不足 // ERROR_SCENES_0135
    public static final String WALLET_NOT_ENOUGH_CHANGE_PROFIT_SHARING_MESSAGE = "账户当前余额小于退款金额，请足额后重试"; //商户存在分账，余额不足以退款

	public static final String TL_PROVIDER_CANNOT_REFUND = "EP815"; //通联渠道交易目前不允许退款
	public static final String TL_PROVIDER_CANNOT_REFUND_MESSAGE = "该交易不支持退款，请用其他方式退款"; //TL_PROVIDER_CANNOT_REFUND

	public static final String FUYOU_TRADING_TIME_ALLOWED = "EP289";
	public static final String FUYOU_TRADING_TIME_ALLOWED_MESSAGE = "富友收单机构0－3点时不能退历史交易，请3点后重试";

	public static final String ORDER_CAN_NOT_CONSUME_ERROR = "EP816";
	public static final String ORDER_CAN_NOT_CONSUME_ERROR_MESSAGE = "订单暂不能完成预授权，请咨询客服。";

	public static final String PHONE_POS_CANNOT_REFUND = "EP843";//手机刷卡pos不支持退款
	public static final String PHONE_POS_CANNOT_REFUND_MESSAGE = "该交易不支持退款，请用其他方式退款";

	public static final String PHONE_POS_CANNOT_CANCEL = "EP844";//手机刷卡pos不支持撤销
	public static final String PHONE_POS_CANNOT_CANCEL_MESSAGE = "该订单不支持撤销，请选择正确的订单";

	public static final String ORDER_CREATED_CAN_NOT_CANCEL = "EP829";
	public static final String ORDER_CREATED_CAN_NOT_CANCEL_MESSAGE = "订单已创建，不允许撤单";

	public static final String ORDER_PAID_CAN_NOT_CANCEL = "EP830";
	public static final String ORDER_PAID_CAN_NOT_CANCEL_MESSAGE = "订单已支付，不允许撤单";

	public static final String ORDER_PRE_CANCELED_CAN_NOT_PAY = "EP831";
	public static final String ORDER_PRE_CANCELED_CAN_NOT_PAY_MESSAGE = "订单已被预撤单，拒绝支付 ";

	public static final String ORDER_MCH_SWITCH_REFUND_FAIL = "EP244";
	public static final String ORDER_MCH_SWITCH_REFUND_FAIL_MESSAGE = "该交易不支持退款，请用其他方式退款";

	public static final String ORDER_MCH_SWITCH_FIX_FAIL = "EP245";
	public static final String ORDER_MCH_SWITCH_FIX_FAIL_MESSAGE = "该交易不支持勾兑";


	public static final String PROVIDER_MCH_CLOSE = "EP246";//收单子商户号被禁用
	public static final String PROVIDER_MCH_CLOSE_MESSAGE = "商户暂不支持移动支付，请使用其他支付方式";

	public static final String PROVIDER_TERMINAL_CLOSE = "EP247";//收单虚拟终端号被禁用
	public static final String PROVIDER_TERMINAL_CLOSE_MESSAGE = "商户终端暂不支持移动支付，请使用其他支付方式";

	public static final String SOURCE_WX_SUB_MCH_CLOSE = "EP248"; //微信支付源子商户号被禁用
	public static final String SOURCE_WX_SUB_MCH_CLOSE_MESSAGE_FORMAT = "商户微信收款权限被关闭，请建议顾客先更换其他方式付款，您可联系您的客户经理协助处理";

	public static final String SOURCE_ALIPAY_SUB_MCH_CLOSE = "EP249"; //支付宝支付源子商户号被禁用
	public static final String SOURCE_ALIPAY_SUB_MCH_CLOSE_MESSAGE_FORMAT = "商户支付宝收款权限被关闭，请建议顾客先更换其他方式付款，您可联系您的客户经理协助处理";

	public static final String SOURCE_UNION_PAY_SUB_MCH_CLOSE = "EP250"; //云闪付支付源子商户号被禁用
	public static final String SOURCE_UNION_PAY_SUB_MCH_CLOSE_MESSAGE_FORMAT = "商户银联二维码收款权限被关闭，请建议顾客先更换其他方式付款，您可联系您的客户经理协助处理";

	public static final String DEFAULT_EXTERNAL_STATE_CLOSE = "EP251";
	public static final String DEFAULT_EXTERNAL_STATE_CLOSE_MESSAGE_FORMAT = "商户收款权限被关闭，请建议顾客先更换其他方式付款，您可联系您的客户经理协助处理";



	public static final String FUYOU_WILD_CARD_CAN_NOT_REFUND = "EP915";
	public static final String FUYOU_WILD_CARD_CAN_NOT_REFUND_MESSAGE = "外卡交易因汇率差暂不支持全额退款，请保留至少 0.01 元后重试。";

	public static final Map<String,UpaySingleTradeOverLimitError> MERCHANT_SING_LELIMT_ERROR = CollectionUtil.hashMap(String.format("%s.%s", Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_BARCODE), new UpaySingleTradeOverLimitError(REFUND_ORDER_STATUS_ERROR, REFUND_ORDER_STATUS_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_QRCODE), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_WAP), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY2, Order.SUB_PAYWAY_BARCODE), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_BARCODE_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_BARCODE_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY2, Order.SUB_PAYWAY_QRCODE), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_QRCODE_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY2, Order.SUB_PAYWAY_WAP), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_WAP_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_ALIPAY2, Order.SUB_PAYWAY_MINI), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR, UPAY_SINGLE_LIMIT_ALIPAY_MINI_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_BARCODE), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_WEIXIN_BARCODE_ERROR, UPAY_SINGLE_LIMIT_WEIXIN_BARCODE_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_QRCODE), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_WEIXIN_QRCODE_ERROR, UPAY_SINGLE_LIMIT_WEIXIN_QRCODE_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_WAP), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_WEIXIN_WAP_ERROR, UPAY_SINGLE_LIMIT_WEIXIN_WAP_ERROR_MESSAGE),
			String.format("%s.%s", Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI), new UpaySingleTradeOverLimitError(UPAY_SINGLE_LIMIT_WEIXIN_MINI_ERROR, UPAY_SINGLE_LIMIT_WEIXIN_MINI_ERROR_MESSAGE));
}
