package com.wosai.net;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/25.
 * 参考 https://github.com/nginx/nginx/blob/master/src/stream/ngx_stream_upstream_round_robin.c 实现
 */
public class Upstream {
    public static final Logger logger = LoggerFactory.getLogger(Upstream.class);
    private List<Peer> peers = new ArrayList<>();

    public Upstream(){

    }


    public Peer getPeer(){
        if(peers == null || peers.size() == 0){
            return null;
        }
        if(peers.size() == 1){
            return peers.get(0);
        }else{
            Peer peer = roundRobin();
            if(peer != null){
                return peer;
            }else{
                //随机取一个
                Long randomIndex = System.currentTimeMillis() % peers.size();
                return peers.get(randomIndex.intValue());
            }
        }
    }

    public void freePeer(Peer peer, boolean success){
        if(peers == null || peers.size() <= 1){
            return;
        }
        boolean logDisable = false;
        peer.getLock().lock();
        try{
            if(!success){
                long now = System.currentTimeMillis();
                peer.setFails(peer.getFails() + 1);
                peer.setAccessTime(now);
                peer.setCheckTime(now);
                if(peer.getMaxFails() != 0){
                    int effectiveWeight = peer.getEffectiveWeight()  - peer.getWeight() / peer.getMaxFails();
                    peer.setEffectiveWeight(effectiveWeight <= 0 ? 0 : effectiveWeight);
                    //打印30次日志
                    if (peer.getFails() >= peer.getMaxFails() && peer.getFails() <= peer.getMaxFails() + 30) {
                        logDisable = true;
                    }
                }
            }else{
                // mark peer live if check passed
                if(peer.getAccessTime() < peer.getCheckTime()){
                    peer.setFails(0);
                }
            }
        }finally {
           peer.getLock().unlock();
        }
        if (logDisable) {
            logger.warn("upstream peer temporarily disabled, peer: {}", peer);
        }
    }

    private synchronized Peer roundRobin(){
        //加权轮询算法
        Peer best = null;
        long now = System.currentTimeMillis();
        int total = 0;
        for(Peer peer : peers){
            if(peer.isDown()){
                continue;
            }
            boolean releaseLockInFinally = true;
            peer.getLock().lock();
            try {
                //在failTimeout时间内失败次数大于等于配置的最大失败次数，不选取此peer
                if(peer.getMaxFails() != 0 && peer.getFails() >= peer.getMaxFails() && now - peer.getCheckTime() <= peer.getFailTimeout() * 1000){
                    continue;
                }
                peer.setCurrentWeight(peer.getCurrentWeight() + peer.getEffectiveWeight());
                total = total + peer.getEffectiveWeight();
                //之前由于失败权重被降低后，此时慢慢的增加其权重
                if(peer.getEffectiveWeight() < peer.getWeight()){
                    peer.setEffectiveWeight(peer.getEffectiveWeight() + 1);
                }
                if(best == null || peer.getCurrentWeight() > best.getCurrentWeight()){
                    if(best != null){
                        //不被选中后，释放锁
                        best.getLock().unlock();
                    }
                    best = peer;
                    releaseLockInFinally = false; //待最终选择完毕后，再释放锁
                }
            }finally {
                if(releaseLockInFinally){
                    peer.getLock().unlock();
                }
            }

        }
        if(best == null){
            return null;
        }
        best.setCurrentWeight(best.getCurrentWeight() - total);
        //设置检查时间
        if(now - best.getCheckTime() > best.getFailTimeout() * 1000){
            best.setCheckTime(now);
        }
        best.getLock().unlock();//释放锁
        return best;
    }

    public List<Peer> getPeers() {
        return peers;
    }

    public void setPeers(List<Peer> peers) {
        if(peers == null){
            return;
        }
        this.peers = peers.stream().filter(peer -> peer.getWeight() > 0 && !peer.isDown()).collect(Collectors.toList());
        peers.forEach(peer -> {
            peer.setCurrentWeight(0);
            peer.setEffectiveWeight(peer.getWeight());
        });
    }
}
