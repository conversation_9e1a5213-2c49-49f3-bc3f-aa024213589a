package com.wosai.net;



/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/31.
 */
public class UpstreamPeerContextHolder {
    private static ThreadLocal<Peer> peerTl = new ThreadLocal<>();
    private static ThreadLocal<Upstream> upstreamTl = new ThreadLocal<>();

    public static void setContextHolder(Upstream upstream, Peer peer){
        upstreamTl.set(upstream);
        peerTl.set(peer);
    }

    public static void remove(){
        peerTl.remove();
        upstreamTl.remove();
    }

    public static void finish(boolean success){
        Upstream upstream = upstreamTl.get();
        Peer peer = peerTl.get();
        if(upstream != null && peer != null){
            upstream.freePeer(peer, success);
        }
    }
}
