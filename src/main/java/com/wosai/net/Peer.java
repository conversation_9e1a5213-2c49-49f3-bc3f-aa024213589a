package com.wosai.net;


import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/28.
 */
public class Peer {
    /**  配置属性start **/
    private String server;
    private int weight = 1;
    private boolean down = false;
    private int maxFails = 1; //default 1
    private int failTimeout = 10; //seconds default 10 seconds
    private boolean skipEnAndDecrypt = false;
    /**  配置属性end **/

    private Lock lock = new ReentrantLock();

    private int currentWeight;
    private int effectiveWeight;
    private long checkTime;
    private long accessTime;
    private int fails;



    public Peer(){

    }

    public Peer(String server, int weight) {
        this.server = server;
        this.weight = weight;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        if(server != null){
            server = server.trim();
        }
        this.server = server;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public int getCurrentWeight() {
        return currentWeight;
    }

    public void setCurrentWeight(int currentWeight) {
        this.currentWeight = currentWeight;
    }

    public int getEffectiveWeight() {
        return effectiveWeight;
    }

    public void setEffectiveWeight(int effectiveWeight) {
        this.effectiveWeight = effectiveWeight;
    }

    public boolean isDown() {
        return down;
    }

    public void setDown(boolean down) {
        this.down = down;
    }

    public boolean isSkipEnAndDecrypt() {
        return skipEnAndDecrypt;
    }

    public void setSkipEnAndDecrypt(boolean skipEnAndDecrypt) {
        this.skipEnAndDecrypt = skipEnAndDecrypt;
    }

    public int getMaxFails() {
        return maxFails;
    }

    public void setMaxFails(int maxFails) {
        this.maxFails = maxFails;
    }

    public int getFailTimeout() {
        return failTimeout;
    }

    public void setFailTimeout(int failTimeout) {
        this.failTimeout = failTimeout;
    }

    public long getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(long checkTime) {
        this.checkTime = checkTime;
    }

    public int getFails() {
        return fails;
    }

    public void setFails(int fails) {
        this.fails = fails;
    }

    public long getAccessTime() {
        return accessTime;
    }

    public void setAccessTime(long accessTime) {
        this.accessTime = accessTime;
    }

    public Lock getLock() {
        return lock;
    }

    @Override
    public String toString() {
        return "Peer{" +
                "server='" + server + '\'' +
                ", weight=" + weight +
                ", down=" + down +
                ", skipEnAndDecrypt=" + skipEnAndDecrypt +
                ", maxFails=" + maxFails +
                ", failTimeout=" + failTimeout +
                ", currentWeight=" + currentWeight +
                ", effectiveWeight=" + effectiveWeight +
                ", checkTime=" + checkTime +
                ", accessTime=" + accessTime +
                ", fails=" + fails +
                '}';
    }
}
