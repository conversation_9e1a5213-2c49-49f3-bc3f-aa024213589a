<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         id="WebApp_ID" version="3.0">

  <display-name>Spring MVC</display-name>
  <description>Spring MVC web application</description>

  <context-param>
    <param-name>spring.profiles.active</param-name>
    <param-value>mongodb</param-value>
    <!-- Available profiles:
	 <param-value>jdbc</param-value>
	 <param-value>jpa</param-value> (in the case of plain JPA)
	 <param-value>spring-data-jpa</param-value> (in the case of Spring Data JPA)
    -->
  </context-param>

  <!--
      - Location of the XML file that defines the root application context.
      - Applied by ContextLoaderServlet.
  -->
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:spring/business-config.xml, classpath:spring/tools-config.xml</param-value>
  </context-param>
  <context-param>
    <param-name>logbackDisableServletContainerInitializer</param-name>
    <param-value>true</param-value>
  </context-param>
  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>

  <!--
      - Servlet that dispatches request to registered handlers (Controller implementations).
  -->
  <servlet>
    <servlet-name>dispatcher</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath:spring/mvc-core-config.xml</param-value>
    </init-param>
      <async-supported>true</async-supported>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet-mapping>
    <servlet-name>dispatcher</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>

   <filter>
       <filter-name>encodingFilter</filter-name>
       <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
       <async-supported>true</async-supported>
       <init-param>
           <param-name>encoding</param-name>
           <param-value>UTF-8</param-value>
       </init-param>
       <init-param>
           <param-name>forceEncoding</param-name>
           <param-value>true</param-value>
       </init-param>
   </filter>
   <filter-mapping>
       <filter-name>encodingFilter</filter-name>
       <url-pattern>/*</url-pattern>
   </filter-mapping>

   <filter>
       <filter-name>flowFilter</filter-name>
       <filter-class>com.wosai.filter.FlowFilter</filter-class>
       <async-supported>true</async-supported>
   </filter>
   <filter-mapping>
       <filter-name>flowFilter</filter-name>
       <url-pattern>/upay/v2/*</url-pattern>
   </filter-mapping>
</web-app>
