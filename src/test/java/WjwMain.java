import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.abc.*;
import com.wosai.mpay.api.abc.RequestBuilder;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.cmbapp.GMSSLContext;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.util.UpayConfigCryptoUtil;
import com.wosai.upay.model.kafka.Transaction;
import com.wosai.upay.service.AmqpFacade;
import com.wosai.upay.util.*;
import com.wosai.upay.workflow.CcbWapServiceProvider;
import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import lombok.SneakyThrows;
import okhttp3.*;
import okhttp3.internal.Util;
import org.apache.avro.data.Json;
import org.apache.zookeeper.KeeperException;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.wosai.mpay.api.alipay.AlipayV2NewClient.SIGN_TYPE_HAIKE;

/**
 * Created by wujianwei on 2022/3/4.
 */
public class WjwMain {

    public static class BeanTest{
        private String name;
    }

    @SneakyThrows
    public static void testAbc(){
        System.out.println(GMSSLContext.class);

        String gateway = "https://bjpay.echase.cn/gateway/bmpapi/postrans";
//        String gateway = "https://bjuat.echase.cn/gateway/bmpapi/postrans";
        String key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2iCJ36duFr0Ke5M37zZzZJ4kK\nmRApr42H8rG1mpuCQNvNmgSKl3L+0P1mqQf3/0RrseB9G8BKNwlqBQSz28L9ynWQ\nv4Hq1LTgHtEjQc0w73ZJ32JeOmfq8oor84YxjNDNJu3RHNNkuOyM7lFr3BtYkaIa\nIAvLc8LeO2tx4nvRTwIDAQAB";
        key = key.replaceAll("\\n", "");
        ABCClient client = new ABCClient();
        String countno = "950120000289713";
        com.wosai.mpay.api.abc.RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ABCRequestFields.PAYMODE, ABCConstant.QRCODE);
        //默认为查询
        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.QUERY);
        requestBuilder.set(ABCRequestFields.COUNTNO, countno);
        String tsn = "TEST" + System.currentTimeMillis() + "";
//        tsn = "7895036861054735";
        requestBuilder.set(ABCRequestFields.TERMINAL_SERIALNO, tsn);

        long amount = 1;
        String orderAmount = StringUtils.cents2yuan(amount);
        requestBuilder.set(ABCRequestFields.AMOUNT, orderAmount);

        String tradeTime = DateUtil.formatDate(new Date(), ABCConstant.DATE_SIMPLE_FORMAT);
        String tradeTime2 = DateUtil.formatDate(new Date(), ABCConstant.DATE_TIME_SIMPLE_FORMAT);
        requestBuilder.setPlatformField(ABCRequestFields.REQ_DATE, tradeTime);
        requestBuilder.setPlatformField(ABCRequestFields.REQ_TIME, tradeTime2);
        requestBuilder.setPlatformField(ABCRequestFields.PROV_CODE, ABCConstant.AREA_CODE);
        requestBuilder.setPlatformField(ABCRequestFields.SRC_AREA_CODE, ABCConstant.AREA_CODE);
        requestBuilder.setPlatformField(ABCRequestFields.ACCESS_TYPE, ABCConstant.ACCESS_TYPE);
        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.PAY);

        //b2c 支付 加上二维码
        requestBuilder.set(ABCRequestFields.AUTH_CODE, "287413321788945991");
        Map<String, Object> call = client.call(requestBuilder, gateway, key, ABCConstant.HEADERS);
        System.out.println(call);

    }

    @SneakyThrows
    public static void test1(){
        String key = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAN5zbq5KEMm6wB1B\n" +
                "FlRPADZ1ezA0PKIUgR4DHwUiwBiZIZk7grz0FDP4b8mqJsoItqdtHUma9gtWjjy8\n" +
                "nRQrDSy80H8kdfje1+6y3fjN4C58u8DTh9Wu3gm+nbdYr5Gw5EHAkNRq7B1baBTl\n" +
                "Fl1XOCZxCVs2+lNT7Ov+w5i/VL95AgMBAAECgYEAk/JLw2kJH1GwIgS+2BWuu4mJ\n" +
                "A7dKODXQ5egHgn/pKLL3sJTuz8K5iRMLLiGXKLmA2Xtthh2k1Eed0/FRuy8i08fS\n" +
                "Ga4hQIiWb8KK31+0aOOhD4tX4Yy1l8TPmsa+sht2ChFOIVdLoR9OgEgILMlboaHh\n" +
                "2S3PILpBcillgrxrJN0CQQDxMqurZ6EheC7jbc3ZnhSssua1ITCFvYhntY3sQfrc\n" +
                "bWKBr+crNXti1HtNqdogNd6qUGLl0p8C/uAUl9wWfz5fAkEA7Bo8yq8G2PqP01KU\n" +
                "G0OeV/qApHHtrMnD70CjyTrAK9c5+vnicIJREQeEnuLcX934vHEBAvM9pMU2fEP4\n" +
                "LhghJwJAKgPBk6IBl2rv//3olbfLsimXtoWJhkJ1uFsP1c/Iew0y05RomEZAr/6D\n" +
                "XAvySxoXJ2PJVm9R85uiPPQXCnQv0QJBANRTUCJSPs04n8lCdTEDoU2rWK8KSdze\n" +
                "4hayQdkAvbtvZZP/zxSxDShMY4bjq52HKF6HHYrCpsgcFsCv+2uwhXcCQQC/rbSF\n" +
                "wSqnTAdINfIWO9A+8RPpnNYKNt7KkRssGgJicZ7g3fLz/mTPsMRTIBlcXwtCc1Gf\n" +
                "aQAybD0vqMiiMwbJ";
        key  = key.replaceAll("\\\\n", "");
        String s = "MDAwNHsiYW1vdW50IjoiMC4wMSIsInRyYW5zdHlwZSI6IjEiLCJtYWNkYXRhIjoiWkhvdTNmZEFBd1R4bHg2RU1OeU9oSkpMazZGVDN6eTdUWURLSkdnMFo2ZjhTTE85Zy9CVG9qZWhJZmFGSGsyL1dzVEQrY205WTRrakNvYzk1Z2UvMWxMV2RwLzRGdzd5QnF3VkdUV29yVzMwRlNKNnJ2UVdKRXJERUtRYm03eU9BdUdFRzdpd2dzbjBCdVVjeXhYRG5PTFU4VzRoaXBieVU0VzB3RWFGUk80PSIsInBheW1vZGUiOiIyIiwiY291bnRubyI6Ijk1MDEyMDAwMDI4OTcxMyIsInRlcm1pbmFsX3NlcmlhbG5vIjoiNzg5NTIyOTg1NTUyNDA5MSIsImF1dGhfY29kZSI6IjI4NzQxMzMyMTc4ODk0NTk5MSJ9";
//        String decrypt = RSAEncrypt.decrypt(s, key.replaceAll("", ""));
        String decrypt = new String(RsaSignature.decryptByPrivateKey(s.getBytes(), key));
        System.out.println(decrypt);
    }



    @SneakyThrows
    public static void main(String[] args) {
//        testAbc();
    fun1();
//fun2();

//        test1();
//        String s = "{\n" +
//                "  \"4\":\"a1,a9\",\n" +
//                "  \"2\":\"a1,a4,a9\",\n" +
//                "  \"7\":\"a1,a7,a9\",\n" +
//                "  \"5\":\"aa\",\n" +
//                "  \"14\":\"ag\",\n" +
//                "  \"13\":\"a1,a9,ca\",\n" +
//                "  \"12\":\"a5\",\n" +
//                "  \"18\":\"af\",\n" +
//                "  \"2:sub_payway:4\":\"c1\",\n" +
//                "  \"2:terminal_category:106\":\"c2\",\n" +
//                "  \"2:terminal_category:114\":\"c3\",\n" +
//                "  \"7:biz_model:self_delivery\":\"c4\",\n" +
//                "  \"7:biz_model:third_party_delivery\":\"c4\",\n" +
//                "  \"7:biz_model:sqb_delivery\":\"c4\",\n" +
//                "  \"7:biz_model:self_pickup\":\"c5\",\n" +
//                "  \"4:biz_model:self_delivery\":\"c6,c7\",\n" +
//                "  \"4:biz_model:self_pickup\":\"c6,c7\",\n" +
//                "  \"4:biz_model:sqb_delivery\":\"c6,c7\",\n" +
//                "  \"4:biz_model:third_party_delivery\":\"c8\"\n" +
//                "}";
//
//        Map<String,String> map = JsonUtil.jsonStrToObject(s, Map.class);
//        for (Object ke : map.keySet()) {
//            System.out.println(ke + ": " + JsonUtil.objectToJsonString(map.get(ke).split(",")));
//        }


//        ObjectMapper objectMapper = new ObjectMapper();
//        String storeName = "heloo\t";
//        Map<String,Object> map = new HashMap<>();
//        map.put("store_name", storeName);
//        map.put("store_sn", "2342");
//        map.put("config_snapshot", MapUtil.hashMap(
//                "name", storeName
//        ));
//        System.out.println(storeName);
//        System.out.println(objectMapper.writeValueAsString(map));
////        System.out.println(Base64.encode(new ObjectMapper().writeValueAsBytes(map)));
////        System.out.println(JsonUtil.jsonStringToObject(JsonUtil.toJsonStr(map), Map.class));
////        System.out.println(objectMapper.readValue(objectMapper.writeValueAsString(map), Map.class));
////        System.out.println(JSON.toJSONString(map));
//        System.out.println(Json.parseJson(JSON.toJSONString(map)));
////        String s = "{\"currency\":\"MYR\",\"merchant_country\":\"MYS\",\"merchant_id\":\"6bea774b-1f68-4b65-9fa0-2b2b049ced53\",\"merchant_name\":\"MUHAMMAD ISYRAF BIN MD SHUKOR\",\"merchant_sn\":\"9680002952168\",\"rhb_trade_params\":{\"active\":true,\"fee\":7,\"fee_rate\":\"1\",\"liquidation_next_day\":false,\"mch_id\":\"\",\"merchant\":\"MUHAMMAD ISYRAF BIN MD SHUKOR\"},\"store_id\":\"35298770-d0d7-4526-9dbc-753a8baa7bbf\",\"store_name\":\"MUHAMMAD ISYRAF BIN MD SHUKOR(OUTLET 4)\t\",\"store_sn\":\"9580000000640602\",\"terminal_id\":\"a1c0cae3-70a0-4a1f-9e66-9dbec1048bba\",\"terminal_name\":\"beez00014555\",\"terminal_sn\":\"900339210002302976\"}\n";
//        String s = "{\"store_sn\":\"2342\",\"config_snapshot\":{\"name\":\"heloo   \"},\"store_name\":\"heloo   \"}\n";
//        System.out.println(objectMapper.readValue(s, Map.class));
//        System.out.println(objectMapper.readValue("{\"store_sn\":\"2342\",\"config_snapshot\":{\"name\":\"heloo\\t\"},\"store_name\":\"heloo\\t\"}", Map.class));

//        System.out.println(CcbWapServiceProvider.getBeginAndEndDatetime(MapUtil.hashMap(
//                "ctime",1662985550571l
//        )));;
//        List<Integer> a = new ArrayList<>();
//        int b = 2;
//        a.contains(b);
//
//        System.out.println(Base64.encode("wujianwei:love1".getBytes()));


//        WeixinClient client = new WeixinClient();
//        client.setConnectTimeout(2000);
//        client.setReadTimeout(12000);
//        ExecutorService ex = Executors.newFixedThreadPool(200);
//        for (int i = 0; i < 1000; i ++) {
//            ex.submit(() -> {
//                RequestBuilder builder = new RequestBuilder();
//                builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
//                builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
//                builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
//                builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
//                builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
//                Map<String,Object> request = builder.build();
//                Map<String, Object> result;
//                String url = "https://httpbin.org/delay/11";
//                String requestStr = JsonUtil.toJsonStr(request);
//                Request.Builder requestBuilder = new Request.Builder()
//                        .url(url)
//                        .post(RequestBody.create(MediaType.parse("application/json"), requestStr));
//                Request req = requestBuilder.build();
//
//                HttpClientUtils.getHttpClient("weixin", null, null, 2000, 12000).newCall(req).enqueue(new Callback() {
//                    @Override
//                    public void onFailure(Call call, IOException e) {
//                        e.printStackTrace();
//                    }
//
//                    @Override
//                    public void onResponse(Call call, Response response) throws IOException {
//                        System.out.println(response.body().string());
//                        Util.closeQuietly(response);
//                    }
//                });
//            });
//        }


    }

    @SneakyThrows
    public static void fun1(){
        String s = " {\n" +
                "      \"subject\": \"F牛肉饭邬建伟测试环境测试\",\n" +
                "      \"received_amount\": 1500,\n" +
                "      \"buyer_login\": \"oVxsc1TAGML8lMASrlCqVq6WOGHI\",\n" +
                "      \"merchant_id\": \"c685200e-9178-4e3c-917b-3e8e5000206a\",\n" +
                "      \"body\": null,\n" +
                "      \"type\": 30,\n" +
                "      \"mtime\": 1729051703917,\n" +
                "      \"extended_params\": {},\n" +
                "      \"tsn\": \"7895220220193937\",\n" +
                "      \"product_flag\": null,\n" +
                "      \"operator\": \"01\",\n" +
                "      \"extra_out_fields\": {\n" +
                "        \"combo_id\": \"1\",\n" +
                "        \"is_offset\": true,\n" +
                "        \"is_default_poi\": false,\n" +
                "        \"weixin_appid\": \"wx72534f3638c59073\",\n" +
                "        \"payments\": [\n" +
                "          {\n" +
                "            \"type\": \"WALLET_WEIXIN\",\n" +
                "            \"origin_type\": \"OTHERS\",\n" +
                "            \"amount\": 1500\n" +
                "          }\n" +
                "        ],\n" +
                "        \"wallet_account_type\": 1\n" +
                "      },\n" +
                "      \"reflect\": null,\n" +
                "      \"provider\": 1033,\n" +
                "      \"original_amount\": 1500,\n" +
                "      \"ctime\": *************,\n" +
                "      \"biz_error_code\": null,\n" +
                "      \"id\": \"t7895220220193937\",\n" +
                "      \"terminal_id\": \"f8cc4bce-19eb-4be0-9058-77187db300c0\",\n" +
                "      \"store_id\": \"a0673488-6689-42e9-85a1-213464eb03fd\",\n" +
                "      \"client_tsn\": \"0E7504DF85B0AA55B35EE01D954BAE98\",\n" +
                "      \"provider_error_info\": {\n" +
                "        \"pay\": {\n" +
                "          \"return_code\": \"SUCCESS\",\n" +
                "          \"return_msg\": \"成功\",\n" +
                "          \"result_code\": \"SUCCESS\"\n" +
                "        }\n" +
                "      },\n" +
                "      \"extra_params\": {\n" +
                "        \"barcode\": \"131235635883149070\",\n" +
                "        \"poi\": {\n" +
                "          \"longitude\": \"121.5730754\",\n" +
                "          \"latitude\": \"29.8090123\"\n" +
                "        },\n" +
                "        \"client_ip\": \"**************\",\n" +
                "        \"sqb_ip\": \"**************\",\n" +
                "        \"sqb_station\": \"460,0,22477,209814849\"\n" +
                "      },\n" +
                "      \"payway\": 3,\n" +
                "      \"version\": 2,\n" +
                "      \"finish_time\": 1729051703909,\n" +
                "      \"sub_payway\": 1,\n" +
                "      \"nfc_card\": null,\n" +
                "      \"config_snapshot\": {\n" +
                "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "        \"merchant_id\": \"c685200e-9178-4e3c-917b-3e8e5000206a\",\n" +
                "        \"merchant_sn\": \"1680006670274\",\n" +
                "        \"merchant_name\": \"杭州中泰餐饮服务有限公司（宁波城市学院）\",\n" +
                "        \"merchant_country\": \"CHN\",\n" +
                "        \"currency\": \"CNY\",\n" +
                "        \"longitude\": \"121.569918\",\n" +
                "        \"latitude\": \"29.808124\",\n" +
                "        \"district_code\": \"330212\",\n" +
                "        \"store_id\": \"a0673488-6689-42e9-85a1-213464eb03fd\",\n" +
                "        \"store_sn\": \"1580000007406911\",\n" +
                "        \"store_client_sn\": null,\n" +
                "        \"store_name\": \"F牛肉饭\",\n" +
                "        \"store_city\": \"宁波市\",\n" +
                "        \"extra\": {\n" +
                "          \"offsetInfo\": \"{\\\"street_address\\\":\\\"府山西路431号\\\",\\\"province\\\":\\\"浙江省\\\",\\\"city\\\":\\\"绍兴市\\\",\\\"complete_address\\\":\\\"府山西路431号\\\",\\\"district\\\":\\\"越城区\\\",\\\"latitude\\\":\\\"30.002439\\\",\\\"longitude\\\":\\\"120.570676\\\"}\"\n" +
                "        },\n" +
                "        \"terminal_id\": \"f8cc4bce-19eb-4be0-9058-77187db300c0\",\n" +
                "        \"terminal_sn\": \"100036140040622525\",\n" +
                "        \"terminal_name\": \"F牛肉饭\",\n" +
                "        \"terminal_vendor_app_appid\": \"2021022600003614\",\n" +
                "        \"terminal_category\": 102,\n" +
                "        \"clearance_provider\": 2,\n" +
                "        \"pay_status\": 1,\n" +
                "        \"common_switch\": \"00000000000200002222222222222222\",\n" +
                "        \"merchant_daily_max_credit_limit_trans\": null,\n" +
                "        \"merchant_monthly_max_credit_limit_trans\": null,\n" +
                "        \"union_over_seas_wallet_single_tran_limit\": **********,\n" +
                "        \"union_over_seas_wallet_day_tran_limit\": **********,\n" +
                "        \"payway_day_credit_limits\": null,\n" +
                "        \"payway_month_credit_limits\": null,\n" +
                "        \"is_need_refund_fee_flag\": null,\n" +
                "        \"hit_payway\": null,\n" +
                "        \"provider\": 1033,\n" +
                "        \"lkl_up_trade_params\": {\n" +
                "          \"cert_id\": \"**********\",\n" +
                "          \"fee_rate\": \"0.25\",\n" +
                "          \"sign_type\": \"SM2\",\n" +
                "          \"channel_id\": \"32631798\",\n" +
                "          \"public_key\": \"6de82fb2-497f-47a2-8df9-e9c9715fb65f\",\n" +
                "          \"weixin_appid\": \"wxd23604aba7ed0487\",\n" +
                "          \"merchant_name\": \"杭州中泰餐饮服务有限公司\",\n" +
                "          \"weixin_appkey\": \"c4b3764f913442937c5f12cc21de6acb\",\n" +
                "          \"weixin_mch_id\": \"**********\",\n" +
                "          \"up_private_key\": \"e60fb433-61db-4fa7-b871-1bc893d25e4f\",\n" +
                "          \"provider_mch_id\": \"822331058120UPY\",\n" +
                "          \"weixin_sub_appid\": \"wx72534f3638c59073\",\n" +
                "          \"weixin_sub_mch_id\": \"595280122\",\n" +
                "          \"liquidation_next_day\": true,\n" +
                "          \"weixin_sub_appsecret\": \"03dc3555893ef91f82088aedea393131111a\",\n" +
                "          \"weixin_mini_sub_appid\": \"wxccbcac9a3ece5112\",\n" +
                "          \"weixin_mini_sub_appsecret\": \"\",\n" +
                "          \"is_affiliated\": false,\n" +
                "          \"original_provider_mch_id\": \"\",\n" +
                "          \"fee_rate_tag\": {\n" +
                "            \"1\": \"1:\"\n" +
                "          },\n" +
                "          \"active\": true,\n" +
                "          \"fee\": 4\n" +
                "        },\n" +
                "        \"term_info\": {\n" +
                "          \"term_id\": \"K0885163\",\n" +
                "          \"term_type\": null,\n" +
                "          \"serial_num\": null\n" +
                "        },\n" +
                "        \"term_id\": \"K0885163\",\n" +
                "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
                "        \"trade_app\": \"1\"\n" +
                "      },\n" +
                "      \"deleted\": false,\n" +
                "      \"effective_amount\": 1500,\n" +
                "      \"paid_amount\": 1500,\n" +
                "      \"trade_no\": \"4200059286202410167571329267\",\n" +
                "      \"channel_finish_time\": 1729051703000,\n" +
                "      \"order_id\": \"o7895220220193937\",\n" +
                "      \"items\": null,\n" +
                "      \"order_sn\": \"7895220220193937\",\n" +
                "      \"buyer_uid\": \"oGFfks_ATIq3h3YhFtJ8kbYZRpQo\",\n" +
                "      \"status\": 0\n" +
                "    }";
        ObjectMapper objectMapper = new ObjectMapper();
        Map map = JsonUtil.jsonStringToObject(s, Map.class);
        Transaction transaction = AvroBeanHelper.getTransactionBeanFromMap(JsonUtil.jsonStringToObject(s, Map.class));

        System.out.println(transaction.toByteBuffer().array().length);
//        ObjectMapper objectMapper = new ObjectMapper();
//
//        Map clone = AmqpFacade.clone(map);
//        UpayConfigCryptoUtil.encryptConfigSnapshotSensitiveInfo((Map<String, Object>) clone.get(Transaction.CONFIG_SNAPSHOT));
//
//        Map<String,Object> configSnapshot = (Map) BeanUtil.getNestedProperty(clone, Transaction.CONFIG_SNAPSHOT + "." + "extra");
//        System.out.println(configSnapshot);
//        byte[] bytes = objectMapper.writeValueAsBytes(configSnapshot);
//        Map<String,Object> map1 = objectMapper.readValue(new String(bytes), Map.class);
//        System.out.println(map1);
//



    }

    private static Map convertToMap(ByteBuffer buffer) {
        if(null == buffer) {
            return null;
        }
        Charset charset = null;
        CharsetDecoder decoder = null;
        CharBuffer charBuffer = null;
        try
        {
            charset = Charset.forName("UTF-8");
            decoder = charset.newDecoder();
            charBuffer = decoder.decode(buffer.asReadOnlyBuffer());
            return JsonUtil.jsonStrToObject(charBuffer.toString(), Map.class);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    public static void fun2() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();

        // 创建一个Map，其中一个value是JSON格式的字符串
        Map<String, Object> map = new HashMap<>();
        map.put("key1", "value1");
        map.put("key2", "{\"street_address\":\"府山西路431号xxx\"}"); // JSON格式的字符串

        // 将Map序列化为JSON字符串
        String jsonString = objectMapper.writeValueAsString(map);
        System.out.println("Serialized JSON String: " + jsonString);

        // 反序列化回Map
        Map<String, Object> deserializedMap = objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});

        // 打印反序列化后的Map
        System.out.println("Deserialized Map: " + deserializedMap);

        // 直接反序列化key2
        Map<String, Object> nestedMap = objectMapper.readValue((String) deserializedMap.get("key2"), new TypeReference<Map<String, Object>>() {});
        System.out.println("Nested Map: " + nestedMap);
    }
}
