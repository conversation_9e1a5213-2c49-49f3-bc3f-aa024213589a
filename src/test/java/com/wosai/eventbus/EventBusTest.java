package com.wosai.eventbus;

import java.io.IOException;

import org.junit.Test;

import com.wosai.eventbus.EventBus.EventLoopMultiple;

public class EventBusTest {

    static class SimpleEvent implements Event {

        private String name;
        private int key;
        private Object arg;
        
        public SimpleEvent(String name, int key, Object arg) {
            this.name = name;
            this.key = key;
            this.arg = arg;
        }
        public SimpleEvent(String name, String arg) {
            this(name, 0, arg);
        }
        public SimpleEvent(String name) {
            this(name, null);
        }
        @Override
        public int getKey() {
            return key;
        }
    
        @Override
        public String getName() {
            return name;
        }
    
        public Object getArg() {
            return arg;
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(key).append(":").append(name).append("[");
            if(arg == null) {
                sb.append("null");
            }else{
                sb.append(",").append(arg);
            }
            sb.append("]");
            return sb.toString();
        }
    }
    
    @Test
    public void test() throws InterruptedException {
        EventBus bus = new EventBus();
        bus.post(new SimpleEvent("success"));

        bus.subscribe("success", new EventListener() {
            @Override
            public void handle(Event event) {
                System.out.println("on event " + event + " " + Thread.currentThread().getId());
                bus.post(new SimpleEvent("postSuccess", null));
                bus.post(new SimpleEvent("final", 1, "first final"));

            }
        });
        bus.subscribe("postSuccess", new EventListener() {
            @Override
            public void handle(Event event) {
                System.out.println("on event " + event + " " + Thread.currentThread().getId());
                bus.post(new SimpleEvent("final", "second final"));
            }
        });
        bus.subscribe("final", new EventListener() {
            @Override
            public void handle(Event event) {
                System.out.println("handler 1 on event " + event + " " + Thread.currentThread().getId());
            }
        });
        bus.subscribe("final", new EventListener() {
            @Override
            public void handle(Event event) {
                System.out.println("handler 2 on event " + event + " " + Thread.currentThread().getId());
            }
        });
        bus.subscribe("ioex", new EventListener() {
            @Override
            public void handle(Event event) {
                throw new RuntimeException(new IOException("test"));
            }
        });
        
        bus.start();
        bus.start();
        bus.getInfo();
        bus.post(new SimpleEvent("success"));
        
        // ioex test
        bus.post(new SimpleEvent("ioex"));
        
        // too many
        for(int i= 0; i< 50; i++) {
            bus.post(new SimpleEvent("postSuccess"));
        }
        bus.getConcurrency();
        Thread.sleep(4000);
        bus.shutdown();
        bus.shutdown();
        
        EventBus bus2 = new EventBus(2, 10);
        bus2.start();
        bus2.post(new SimpleEvent("tlb", "success"));
        bus2.shutdown();
        
        EventLoopMultiple loopMultiple = new EventLoopMultiple(1, 10, 30);
        loopMultiple.getCount();
        loopMultiple.getEnd();
        loopMultiple.getStart();
        loopMultiple.toString();
    }

}
