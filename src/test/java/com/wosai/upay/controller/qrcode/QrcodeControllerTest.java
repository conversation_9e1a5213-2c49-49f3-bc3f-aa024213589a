package com.wosai.upay.controller.qrcode;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.Test;
import org.springframework.http.MediaType;

import com.wosai.upay.controller.BaseTestController;

public class QrcodeControllerTest extends BaseTestController{

    /**
     * 
     * 生成二维码
     * 
     * @throws Exception
     */
    @Test
    public void qrcode() throws Exception {
        mockMvc.perform(get("/upay/qrcode")
                        .param("content","test")
                        .param("size", "300"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.IMAGE_PNG_VALUE));
    }
}
