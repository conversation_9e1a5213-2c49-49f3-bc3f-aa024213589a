package com.wosai.upay.controller.admin;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.Test;
import org.springframework.http.MediaType;

import com.wosai.upay.controller.BaseTestController;

public class AdminControllerTest extends BaseTestController{
    
    @Test
    public void health() throws Exception {
        mockMvc.perform(get("/upay/admin/health"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8_VALUE))
            .andExpect(content().string("true"));
    }
    
    @Test
    public void backupHealth() throws Exception {
        mockMvc.perform(get("/upay/admin/backupHealth"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8_VALUE))
            .andExpect(content().string("true"));
    }
}
