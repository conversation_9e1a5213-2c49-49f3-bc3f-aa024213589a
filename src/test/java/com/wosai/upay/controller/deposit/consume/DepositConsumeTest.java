package com.wosai.upay.controller.deposit.consume;

import static org.junit.Assert.assertNotNull;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.SupportUtil;

public class DepositConsumeTest extends BaseTestController{
    @MockBean
    UpayOrderService supportService;
    @MockBean
    GatewaySupportService gatewaySupportService;
    
    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
    }

    /**
     * 
     * 支付宝2.0 历史库线上预授权完成成功
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_history_deposit_consume()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_alipay_history_deposit_consume_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_alipay_history_deposit_consume_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 删除数据
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getFreezeTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        assertNotNull(order);
        assertNotNull(transaction);
        dataRepository.getOrderDao().delete(MapUtil.getString(order, DaoConstants.ID));
        dataRepository.getTransactionDao().delete(MapUtil.getString(transaction, DaoConstants.ID));
        
        // 设置mock
        PowerMockito.when(supportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(order);
        PowerMockito.when(gatewaySupportService.getTransactionByClientTsn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);
        PowerMockito.when(gatewaySupportService.getLatestTransactionByOrderSn(Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);

        AtomicInteger updateCnt = new AtomicInteger();
        PowerMockito.when(supportService.updateOrder(Mockito.anyMap())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) throws Throwable {
                updateCnt.addAndGet(1);
                return true;
            }
        });
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"sign\": \"jfAz0Yi0OUvAPqYTzA0DLysx0ri++yf7o/lkHOHaG1Zy2fHBf3j4WM\\n+sJWHZUuyInt6V+wn+6IP9AmwRTKi+GGdWjPrsfBjXqR7H5aBnLhMsAltV7v4cYjhug\\nuAqh4WkaJO6v6CfdybDpzHlxE6Thoucnad+OsjdCXkNd1g3UuU=\\n\",\n" + 
                "    \"Alipay_trade_pay_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"处理成功\",\n" + 
                "        \"trade_no\": \"2013112011001004330000121536\",\n" + 
                "        \"out_trade_no\": \"2003259247767988\",\n" + 
                "        \"buyer_user_id\": \"2088102122524333\",\n" + 
                "        \"buyer_logon_id\": \"***********\",\n" + 
                "        \"buyer_pay_amount\": \"0.01\",\n" + 
                "        \"total_amount\": \"0.01\",\n" + 
                "        \"receipt_amount\": \"0.01\",\n" + 
                "        \"gmt_payment\": \"2020-04-14 11:27:46\",\n" + 
                "        \"store_name\": \"证大五道口店\",\n" + 
                "        \"fund_bill_list\": [{\n" + 
                "            \"fund_channel\": \"PCREDIT\",\n" + 
                "            \"amount\": \"0.01\"\n" + 
                "        }]\n" + 
                "    }\n" + 
                "}"
        );
        request.put("consume_amount", "1");
        resultString = postPerform("test_alipay_history_deposit_consume_with_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_SUCCESS");
        
        Assert.assertEquals("hbase 更新次数", 2, updateCnt.get());
    }
    
    /**
     * 
     * 支付宝2.0 历史库线上预授权完成失败后勾兑
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_history_deposit_consume_fix_success()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_alipay_history_deposit_consume_fix_success_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_alipay_history_deposit_consume_fix_success_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 删除数据
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getFreezeTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        assertNotNull(order);
        assertNotNull(transaction);
        dataRepository.getOrderDao().delete(MapUtil.getString(order, DaoConstants.ID));
        dataRepository.getTransactionDao().delete(MapUtil.getString(transaction, DaoConstants.ID));
        
        // 设置mock
        PowerMockito.when(supportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(order);
        PowerMockito.when(gatewaySupportService.getTransactionByClientTsn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);
        PowerMockito.when(gatewaySupportService.getLatestTransactionByOrderSn(Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);

        AtomicInteger updateCnt = new AtomicInteger();
        PowerMockito.when(supportService.updateOrder(Mockito.anyMap())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) throws Throwable {
                updateCnt.addAndGet(1);
                return true;
            }
        });
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"sign\": \"jfAz0Yi0OUvAPqYTzA0DLysx0ri++yf7o/lkHOHaG1Zy2fHBf3j4WM\\n+sJWHZUuyInt6V+wn+6IP9AmwRTKi+GGdWjPrsfBjXqR7H5aBnLhMsAltV7v4cYjhug\\nuAqh4WkaJO6v6CfdybDpzHlxE6Thoucnad+OsjdCXkNd1g3UuU=\\n\",\n" + 
                "    \"Alipay_trade_pay_response\": {\n" + 
                "        \"code\": \"40004\",\n" + 
                "        \"msg\": \"处理失败\",\n" + 
                "        \"sub_code\": \"ACQ.TRADE_HAS_CLOSE\",\n" +
                "        \"trade_no\": \"2013112011001004330000121536\",\n" + 
                "        \"out_trade_no\": \"2003259247767988\",\n" + 
                "        \"buyer_user_id\": \"2088102122524333\",\n" + 
                "        \"buyer_logon_id\": \"***********\",\n" + 
                "        \"buyer_pay_amount\": \"0.01\",\n" + 
                "        \"total_amount\": \"0.01\",\n" + 
                "        \"receipt_amount\": \"0.01\",\n" + 
                "        \"gmt_payment\": \"2020-04-14 11:27:46\",\n" + 
                "        \"store_name\": \"证大五道口店\",\n" + 
                "        \"fund_bill_list\": [{\n" + 
                "            \"fund_channel\": \"PCREDIT\",\n" + 
                "            \"amount\": \"0.01\"\n" + 
                "        }]\n" + 
                "    }\n" + 
                "}"
        );

        // 预授权完成失败
        request.put("consume_amount", "1");
        resultString = postPerform("test_alipay_history_deposit_consume_fix_success_with_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_ERROR");
        Assert.assertEquals("consume hbase 更新次数", 2, updateCnt.get());
        Map consomeTransaction = dataRepository.getConsumeTransactionByOrderSn(MapUtil.getString(transaction, Transaction.MERCHANT_ID), MapUtil.getString(transaction, Transaction.ORDER_SN));
        
        // 预授权完成勾兑
        request.put("tsn", MapUtil.getString(consomeTransaction, Transaction.TSN));
        request.put("sn", MapUtil.getString(transaction, Transaction.ORDER_SN));
        
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_trade_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"buyer_logon_id\": \"yin***@126.com\",\n" + 
                "        \"buyer_pay_amount\": \"0.01\",\n" + 
                "        \"buyer_user_id\": \"****************\",\n" + 
                "        \"fund_bill_list\": [{\n" + 
                "            \"amount\": \"0.01\",\n" + 
                "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                "        }],\n" + 
                "        \"invoice_amount\": \"0.01\",\n" + 
                "        \"out_trade_no\": \"****************\",\n" + 
                "        \"point_amount\": \"0.00\",\n" + 
                "        \"receipt_amount\": \"0.01\",\n" + 
                "        \"send_pay_date\": \"2020-11-09 18:33:49\",\n" + 
                "        \"total_amount\": \"0.01\",\n" + 
                "        \"trade_no\": \"2020110922001490171412469797\",\n" + 
                "        \"trade_status\": \"TRADE_SUCCESS\"\n" + 
                "    },\n" + 
                "    \"sign\": \"QLOwbClAweTjmVNMy03AcRc5BGsYdF0WpU9xTMAfrW0MgpRSB2iJ+DZpBIKe872MhY68tMO9b2X4oLz3SBgrUkgbwoQjYH2baMAsgk7iupbLMbzQkkTusKoEeh1xfE3gIkq8fWslO0Kskb+Ek6mOUDheqD1C390zri4dUy6d1Vk=\"\n" + 
                "}"
        );
        resultString = postPerform("test_alipay_history_deposit_consume_fix_success_with_fix", "/upay/v2/deposit/fixConsumeToSuccess", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CommonResponse.SUCCESS,
                "biz_response.data.status", Order.STATUS_DEPOSIT_CONSUMED);
        Assert.assertEquals("fix hbase 更新次数", 3, updateCnt.get());

        
    }
    
    /**
     * 
     * 支付宝2.0 历史库线上预授权完成失败后勾兑
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_history_deposit_consume_fix_not_success()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_alipay_history_deposit_consume_fix_not_success_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_alipay_history_deposit_consume_fix_not_success_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 删除数据
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getFreezeTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        assertNotNull(order);
        assertNotNull(transaction);
        dataRepository.getOrderDao().delete(MapUtil.getString(order, DaoConstants.ID));
        dataRepository.getTransactionDao().delete(MapUtil.getString(transaction, DaoConstants.ID));
        
        // 设置mock
        PowerMockito.when(supportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(order);
        PowerMockito.when(gatewaySupportService.getTransactionByClientTsn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);
        PowerMockito.when(gatewaySupportService.getLatestTransactionByOrderSn(Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);

        AtomicInteger updateCnt = new AtomicInteger();
        PowerMockito.when(supportService.updateOrder(Mockito.anyMap())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) throws Throwable {
                updateCnt.addAndGet(1);
                return true;
            }
        });
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"sign\": \"jfAz0Yi0OUvAPqYTzA0DLysx0ri++yf7o/lkHOHaG1Zy2fHBf3j4WM\\n+sJWHZUuyInt6V+wn+6IP9AmwRTKi+GGdWjPrsfBjXqR7H5aBnLhMsAltV7v4cYjhug\\nuAqh4WkaJO6v6CfdybDpzHlxE6Thoucnad+OsjdCXkNd1g3UuU=\\n\",\n" + 
                "    \"Alipay_trade_pay_response\": {\n" + 
                "        \"code\": \"40004\",\n" + 
                "        \"msg\": \"处理失败\",\n" + 
                "        \"sub_code\": \"PAYER_USER_STATUS_LIMIT\",\n" +
                "        \"trade_no\": \"2013112011001004330000121536\",\n" + 
                "        \"out_trade_no\": \"2003259247767988\",\n" + 
                "        \"buyer_user_id\": \"2088102122524333\",\n" + 
                "        \"buyer_logon_id\": \"***********\",\n" + 
                "        \"buyer_pay_amount\": \"0.01\",\n" + 
                "        \"total_amount\": \"0.01\",\n" + 
                "        \"receipt_amount\": \"0.01\",\n" + 
                "        \"gmt_payment\": \"2020-04-14 11:27:46\",\n" + 
                "        \"store_name\": \"证大五道口店\",\n" + 
                "        \"fund_bill_list\": [{\n" + 
                "            \"fund_channel\": \"PCREDIT\",\n" + 
                "            \"amount\": \"0.01\"\n" + 
                "        }]\n" + 
                "    }\n" + 
                "}"
        );

        // 预授权完成失败
        request.put("consume_amount", "1");
        resultString = postPerform("test_alipay_history_deposit_consume_fix_not_success_with_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_ERROR");
        Assert.assertEquals("consume hbase 更新次数", 2, updateCnt.get());
        Map consomeTransaction = dataRepository.getConsumeTransactionByOrderSn(MapUtil.getString(transaction, Transaction.MERCHANT_ID), MapUtil.getString(transaction, Transaction.ORDER_SN));
        
        // 预授权完成勾兑
        request.put("tsn", MapUtil.getString(consomeTransaction, Transaction.TSN));
        request.put("sn", MapUtil.getString(transaction, Transaction.ORDER_SN));
        resultString = postPerform("test_alipay_history_deposit_consume_fix_not_success_with_fix", "/upay/v2/deposit/fixOrderIfConsumeNotSuccess", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CommonResponse.SUCCESS,
                "biz_response.data.status", Order.STATUS_DEPOSIT_FREEZED);
        Assert.assertEquals("fix hbase 更新次数", 3, updateCnt.get());

        
    }

    /**
     * 
     * 支付宝2.0 预授权完成
     * 
     * 期望结果：预授权完成成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_deposit_consume_with_query()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_alipay_deposit_consume_with_query", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_alipay_deposit_consume_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );

        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return "{\n"
                            + "    \"alipay_trade_pay_response\": {\n"
                            + "        \"msg\": \"Business Failed\",\n"
                            + "        \"code\": \"40004\",\n"
                            + "        \"sub_msg\": \"支付失败，交易已经支付，请勿重复收款 [UP0531002]\",\n"
                            + "        \"sub_code\": \"ACQ.TRADE_HAS_SUCCESS\",\n"
                            + "        \"cert_id\": \"**********\",\n"
                            + "        \"sign_type\": \"RSA2\"\n"
                            + "    },\n"
                            + "    \"sign\": \"AgHNb+DQi+F6DbJg+QtaiJuRbOQw0740cPHKcW+RCWrA68oiMdSKUDrBIx8ZEztrzkfIT3uDFsqhADZLDWxhNmTih3Y+Bd2wLz52v+M4OyOjEbgPWHQtK1coKOG5MIte493yQbgnVyW4f68v5sHEkVx0+aixR1bVwKwPxj15kJqcfcJYWBelWOliY3G2iuHiCD/s/pInjbbI86mFl7c2UCXVdgPQN+1FpqSCNINzltRJH6ezsPtrejo71QbTqDQi4kX9yLez2pN1OoHJOV1HD0tbrsVlwY9aC7GYHo/xCJo8dO9QddBccRPm8MuxGcpyzfP+ty36RT07hfsdcDmCuQ==\"\n"
                            + "}";
                } else if(AlipayV2Methods.ALIPAY_TRADE_QUERY.equals(method)) {
                    return "{\n"
                            + "    \"alipay_trade_query_response\": {\n"
                            + "        \"msg\": \"Success\",\n"
                            + "        \"code\": \"10000\",\n"
                            + "        \"subject\": \"洗浴预授\",\n"
                            + "        \"cert_id\": \"**********\",\n"
                            + "        \"buyer_user_id\": \"****************\",\n"
                            + "        \"send_pay_date\": \"2022-07-06 18:19:15\",\n"
                            + "        \"invoice_amount\": \"185.00\",\n"
                            + "        \"fund_bill_list\": [{\n"
                            + "            \"amount\": \"0.01\",\n"
                            + "            \"fund_channel\": \"ALIPAYACCOUNT\"\n"
                            + "        }],\n"
                            + "        \"sub_merchant_id\": \"****************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"total_amount\": \"0.01\",\n"
                            + "        \"trade_status\": \"TRADE_SUCCESS\",\n"
                            + "        \"trade_no\": \"2022070622001429821455358011\",\n"
                            + "        \"buyer_logon_id\": \"152******47\",\n"
                            + "        \"receipt_amount\": \"0.01\",\n"
                            + "        \"point_amount\": \"0.00\",\n"
                            + "        \"buyer_pay_amount\": \"0.01\",\n"
                            + "        \"sign_type\": \"RSA2\"\n"
                            + "    },\n"
                            + "    \"sign\": \"YCXpfr1LxcbPK7vE2L6ctOG29UXRWabE/zm6WkDkj7hsYPqVDyHWr/weNykaLoCxoutwaahWu6ZBSJ55k4PI+GqYJfupzASfoReq7chQ3p0g0o78PsQ+g2Q3mW/CzYgPqiXvYnMrFxOvS8CCm49iJdd2nnepxjhKHrlTGRCD0uPk4pPbi0zmIw6l1Bz6Jeey5OLTRDRSWBTuHB/XvcBYNwFaKE1/lh17+3A0YkwJo31Tjqhrsw01IM6dg1AAKhzn3WtkRzco3p/kToBIbU/Ahu70D22CVsIQzYfnaQTkgjYRa+oV/oOYhjrrcAagBdWBjYn/pqjgB9H4ai18QISJYA==\"\n"
                            + "}";
                } else {
                    return "";
                }
            }
        });
        request.put("consume_amount", "1");
        resultString = postPerform("test_alipay_deposit_consume_with_query", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_SUCCESS");
    }

    /**
     * 
     * 支付宝2.0 预授权完成
     * 
     * 期望结果：预授权完成成功
     * 
     * @throws Exception
     */
    @Test
    public void test_union_alipay_deposit_consume_with_query()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UP_DIRECT_TRADE_PARAMS_ALIPAY));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        
     // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"1320191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        String resultString = postPerform("test_union_alipay_deposit_consume_with_query", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        Thread.sleep(6100);
        resultString = postPerform("test_union_alipay_deposit_consume_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );

        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return "{\n"
                            + "    \"alipay_trade_pay_response\": {\n"
                            + "        \"msg\": \"Business Failed\",\n"
                            + "        \"code\": \"40004\",\n"
                            + "        \"sub_msg\": \"支付失败，交易已经支付，请勿重复收款 [UP0531002]\",\n"
                            + "        \"sub_code\": \"ACQ.TRADE_HAS_SUCCESS\",\n"
                            + "        \"cert_id\": \"**********\",\n"
                            + "        \"sign_type\": \"RSA2\"\n"
                            + "    },\n"
                            + "    \"sign\": \"AgHNb+DQi+F6DbJg+QtaiJuRbOQw0740cPHKcW+RCWrA68oiMdSKUDrBIx8ZEztrzkfIT3uDFsqhADZLDWxhNmTih3Y+Bd2wLz52v+M4OyOjEbgPWHQtK1coKOG5MIte493yQbgnVyW4f68v5sHEkVx0+aixR1bVwKwPxj15kJqcfcJYWBelWOliY3G2iuHiCD/s/pInjbbI86mFl7c2UCXVdgPQN+1FpqSCNINzltRJH6ezsPtrejo71QbTqDQi4kX9yLez2pN1OoHJOV1HD0tbrsVlwY9aC7GYHo/xCJo8dO9QddBccRPm8MuxGcpyzfP+ty36RT07hfsdcDmCuQ==\"\n"
                            + "}";
                } else if(AlipayV2Methods.ALIPAY_TRADE_QUERY.equals(method)) {
                    return "{\n"
                            + "    \"alipay_trade_query_response\": {\n"
                            + "        \"msg\": \"Success\",\n"
                            + "        \"code\": \"10000\",\n"
                            + "        \"subject\": \"洗浴预授\",\n"
                            + "        \"cert_id\": \"**********\",\n"
                            + "        \"buyer_user_id\": \"****************\",\n"
                            + "        \"send_pay_date\": \"2022-07-06 18:19:15\",\n"
                            + "        \"invoice_amount\": \"185.00\",\n"
                            + "        \"fund_bill_list\": [{\n"
                            + "            \"amount\": \"0.01\",\n"
                            + "            \"fund_channel\": \"ALIPAYACCOUNT\"\n"
                            + "        }],\n"
                            + "        \"sub_merchant_id\": \"****************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"total_amount\": \"0.01\",\n"
                            + "        \"trade_status\": \"TRADE_SUCCESS\",\n"
                            + "        \"trade_no\": \"132022070622001429821455358011\",\n"
                            + "        \"buyer_logon_id\": \"152******47\",\n"
                            + "        \"receipt_amount\": \"0.01\",\n"
                            + "        \"point_amount\": \"0.00\",\n"
                            + "        \"buyer_pay_amount\": \"0.01\",\n"
                            + "        \"sign_type\": \"RSA2\"\n"
                            + "    },\n"
                            + "    \"sign\": \"YCXpfr1LxcbPK7vE2L6ctOG29UXRWabE/zm6WkDkj7hsYPqVDyHWr/weNykaLoCxoutwaahWu6ZBSJ55k4PI+GqYJfupzASfoReq7chQ3p0g0o78PsQ+g2Q3mW/CzYgPqiXvYnMrFxOvS8CCm49iJdd2nnepxjhKHrlTGRCD0uPk4pPbi0zmIw6l1Bz6Jeey5OLTRDRSWBTuHB/XvcBYNwFaKE1/lh17+3A0YkwJo31Tjqhrsw01IM6dg1AAKhzn3WtkRzco3p/kToBIbU/Ahu70D22CVsIQzYfnaQTkgjYRa+oV/oOYhjrrcAagBdWBjYn/pqjgB9H4ai18QISJYA==\"\n"
                            + "}";
                } else {
                    return "";
                }
            }
        });
        request.put("consume_amount", "1");
        resultString = postPerform("test_union_alipay_deposit_consume_with_query", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_SUCCESS");
    }
}
