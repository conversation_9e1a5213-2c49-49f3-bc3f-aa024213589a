package com.wosai.upay.controller.deposit.prefreeze;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class BankcardPreFreezeTest extends BaseTestController{
    @Before
    public void init() {
        Map<String, Object> allParams = SupportUtil.buildGetAllParams(SupportUtil.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS);
        allParams.put("deposit", MapUtil.hashMap("bankcard", 1));
        mockGetAllPrams(allParams);
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
    }

    @Test
    public void testPrefreeze() throws Exception {
        // 设置返回结果
        String batchBillNo = "000136";
        String sysTraceNo = "00002";
        String authNo = "000162";
        String referNumber = "************";
        String buyerUid = "6221****5811";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            return "{\n"
                    + "    \"code\": \"000000\",\n"
                    + "    \"msg\": \"成功\",\n"
                    + "    \"resp_data\": {\n"
                    + "        \"pay_order_no\": null,\n"
                    + "        \"out_order_no\": \"7896259230587900\",\n"
                    + "        \"out_trade_no\": null,\n"
                    + "        \"trans_merchant_no\": \"822731070110B6S\",\n"
                    + "        \"trans_term_no\": \"E1830013\",\n"
                    + "        \"merchant_no\": \"822731070110B6S\",\n"
                    + "        \"term_no\": \"E1830013\",\n"
                    + "        \"total_amount\": \"50000\",\n"
                    + "        \"order_trade_info_list\": [{\n"
                    + "            \"log_no\": \"23************\",\n"
                    + "            \"trade_no\": \"23************\",\n"
                    + "            \"trade_ref_no\": \"" + referNumber + "\",\n"
                    + "            \"trade_type\": \"TRAN_PREAUTH\",\n"
                    + "            \"trade_status\": \"SUCCESS\",\n"
                    + "            \"trade_amount\": \"50000\",\n"
                    + "            \"payer_amount\": \"50000\",\n"
                    + "            \"user_id1\": null,\n"
                    + "            \"user_id2\": null,\n"
                    + "            \"trade_time\": \"2023-07-07 00:26:10\",\n"
                    + "            \"acc_trade_no\": null,\n"
                    + "            \"payer_account_no\": \""+ buyerUid +"\",\n"
                    + "            \"payer_name\": null,\n"
                    + "            \"payer_account_bank\": \"********\",\n"
                    + "            \"acc_type\": \"C\",\n"
                    + "            \"pay_mode\": \"INSERT\",\n"
                    + "            \"client_batch_no\": \"" + batchBillNo + "\",\n"
                    + "            \"client_seq_no\": \"" + sysTraceNo + "\",\n"
                    + "            \"settle_merchant_no\": \"822731070110B6S\",\n"
                    + "            \"settle_term_no\": \"E1830013\",\n"
                    + "            \"trade_remark\": \"\",\n"
                    + "            \"tran_acc_issino\": \"********\",\n"
                    + "            \"busi_mode\": \"BS1_IN_CARD\"\n"
                    + "        },\n"
                    + "        {\n"
                    + "            \"log_no\": \"23************\",\n"
                    + "            \"trade_no\": \"23************\",\n"
                    + "            \"trade_ref_no\": \"" + referNumber + "\",\n"
                    + "            \"trade_type\": \"TRAN_COMPLETE\",\n"
                    + "            \"trade_status\": \"SUCCESS\",\n"
                    + "            \"trade_amount\": \"10000\",\n"
                    + "            \"payer_amount\": \"10000\",\n"
                    + "            \"user_id1\": null,\n"
                    + "            \"user_id2\": null,\n"
                    + "            \"trade_time\": \"2023-07-07 00:26:10\",\n"
                    + "            \"acc_trade_no\": null,\n"
                    + "            \"payer_account_no\": \""+ buyerUid +"\",\n"
                    + "            \"payer_name\": null,\n"
                    + "            \"payer_account_bank\": \"********\",\n"
                    + "            \"acc_type\": \"C\",\n"
                    + "            \"pay_mode\": \"INSERT\",\n"
                    + "            \"client_batch_no\": \"" + batchBillNo + "\",\n"
                    + "            \"client_seq_no\": \"" + sysTraceNo + "\",\n"
                    + "            \"settle_merchant_no\": \"822731070110B6S\",\n"
                    + "            \"settle_term_no\": \"E1830013\",\n"
                    + "            \"trade_remark\": \"\",\n"
                    + "            \"tran_acc_issino\": \"********\",\n"
                    + "            \"busi_mode\": \"BS1_IN_CARD\"\n"
                    + "        },\n"
                    + "        {\n"
                    + "            \"log_no\": \"23************\",\n"
                    + "            \"trade_no\": \"23************\",\n"
                    + "            \"trade_ref_no\": \"" + referNumber + "\",\n"
                    + "            \"trade_type\": \"TRAN_COMPLETE_REVOKE\",\n"
                    + "            \"trade_status\": \"SUCCESS\",\n"
                    + "            \"trade_amount\": \"10000\",\n"
                    + "            \"payer_amount\": \"10000\",\n"
                    + "            \"user_id1\": null,\n"
                    + "            \"user_id2\": null,\n"
                    + "            \"trade_time\": \"2023-07-07 00:26:10\",\n"
                    + "            \"acc_trade_no\": null,\n"
                    + "            \"payer_account_no\": \""+ buyerUid +"\",\n"
                    + "            \"payer_name\": null,\n"
                    + "            \"payer_account_bank\": \"********\",\n"
                    + "            \"acc_type\": \"C\",\n"
                    + "            \"pay_mode\": \"INSERT\",\n"
                    + "            \"client_batch_no\": \"" + batchBillNo + "\",\n"
                    + "            \"client_seq_no\": \"" + sysTraceNo + "\",\n"
                    + "            \"settle_merchant_no\": \"822731070110B6S\",\n"
                    + "            \"settle_term_no\": \"E1830013\",\n"
                    + "            \"trade_remark\": \"\",\n"
                    + "            \"tran_acc_issino\": \"********\",\n"
                    + "            \"busi_mode\": \"BS1_IN_CARD\"\n"
                    + "        }],\n"
                    + "        \"dcc_flg\": null,\n"
                    + "        \"dcc_chg_tm\": null,\n"
                    + "        \"aut_cod\": \"" + authNo + "\",\n"
                    + "        \"bil_rat\": null,\n"
                    + "        \"bil_ccy\": null,\n"
                    + "        \"bil_amt\": null\n"
                    + "    }\n"
                    + "}";
        });

        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_BANKCARD, Order.SUB_PAYWAY_QRCODE, "mock");
        request.put(UpayService.TOTAL_AMOUNT, "50000");
        String resultString = postPerform("testPrefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        Thread.sleep(8000);
        resultString = postPerform("testPrefreeze_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", buyerUid,
                        "biz_response.data.payer_uid", buyerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.batch_bill_no", batchBillNo,
                        "biz_response.data.sys_trace_no", sysTraceNo,
                        "biz_response.data.auth_no", authNo,
                        "biz_response.data.refer_number", referNumber
                );

        // 预授权完成
        request.put(DepositService.CONSUME_REQUEST_NO, System.currentTimeMillis() + "");
        request.put(DepositService.CONSUME_AMOUNT, "10000");
        resultString = postPerform("test_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);
        Thread.sleep(8000);
        resultString = postPerform("test_consume_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", buyerUid,
                        "biz_response.data.payer_uid", buyerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.toString(),
                        "biz_response.data.batch_bill_no", batchBillNo,
                        "biz_response.data.sys_trace_no", sysTraceNo,
                        "biz_response.data.auth_no", authNo,
                        "biz_response.data.refer_number", referNumber
                );

        // 预授权完成退款
        request.put(UpayService.REFUND_REQUEST_NO, System.currentTimeMillis() + "");
        request.put(UpayService.REFUND_AMOUNT, "10000");
        resultString = postPerform("test_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);
        Thread.sleep(8000);
        resultString = postPerform("test_refund_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", buyerUid,
                        "biz_response.data.payer_uid", buyerUid,
                        "biz_response.data.order_status", Order.Status.REFUNDED.toString(),
                        "biz_response.data.batch_bill_no", batchBillNo,
                        "biz_response.data.sys_trace_no", sysTraceNo,
                        "biz_response.data.auth_no", authNo,
                        "biz_response.data.refer_number", referNumber
                );
    }
}
