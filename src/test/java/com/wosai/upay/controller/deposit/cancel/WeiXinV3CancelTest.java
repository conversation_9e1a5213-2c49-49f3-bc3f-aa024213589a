package com.wosai.upay.controller.deposit.cancel;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.weixin.WeixinV3Client;
import com.wosai.mpay.util.HmacSignature;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.SupportUtil;

public class WeiXinV3CancelTest extends BaseTestController {
    @MockBean
    WeixinV3Client weixinV3Client;
    
    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_WEIXIN_KEY);
    }

    @Test
    public void testPreFreezeCancel() throws Exception {
        // 预授权冻结成功
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"1230000109\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"1230000109\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "PREFREEZE_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("testPreFreezeCance_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );

        String cancelResult = "{\"http_code\":400,\"code\":\"PARAM_ERROR\",\"message\":\"此out_order_no已存在\"}";
        String queryResult = "{\"appid\":\"wx141c266174366ccf\",\"attach\":\"\",\"location\":{\"start_location\":\"深圳市阅莲轩手作甜品\"},\"mchid\":\"1574519271\",\"need_collection\":true,\"notify_url\":\"http://gateway.shouqianba.com/upay/v2/notify/weixinV3/84b581cf5a1b5682a00b7a9cc5edd1d8/5520d%267895235722950206%263%261580000002596741%2617032ac1-cc98-4a8f-83ec-42e69ad24018%264%261602156752142%269900%260%263\",\"openid\":\"ow3mX5FwCOgzDW2qvIbCxIuLpHaI\",\"order_id\":\"1000000000202010081941648050305\",\"out_order_no\":\"7895235722950206\",\"post_discounts\":[],\"post_payments\":[{\"description\":\"2元／小时，5分钟内免费，每24小时封顶费20元\",\"name\":\"电饱饱共享充电宝\"}],\"risk_fund\":{\"amount\":9900,\"description\":\"\",\"name\":\"DEPOSIT\"},\"service_id\":\"00002000000000157865268295736578\",\"service_introduction\":\"电饱饱共享充电宝\",\"state\":\"REVOKED\",\"state_description\":\"\",\"time_range\":{\"start_time\":\"20201008193235\"},\"total_amount\":0,\"http_code\":200}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(cancelResult, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResult, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("testPreFreezeCancel_with_cancel", "/upay/v2/deposit/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "DEPOSIT_CANCEL_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_CANCELED.toString()
        );
        
        //
    }
}
