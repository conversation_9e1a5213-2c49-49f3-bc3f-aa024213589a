package com.wosai.upay.controller.deposit.prefreeze.alipay;

import static org.junit.Assert.assertNotNull;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.BusinessV1Fields;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.Digest;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.SupportUtil;

public class AlipayV2PreFreezeTest extends BaseTestController{
    @MockBean
    GatewaySupportService gatewaySupportService;

    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
    }

    /**
     * 
     * 支付宝2.0 线上预授权冻结成功
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_success_with_query()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success_with_query", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"****************\",\n" + 
                "        \"out_request_no\": \"****************\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_prefreeze_success_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权冻结成功
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_success_with_query()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_precreate_success_with_query", "/upay/v2/deposit/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"****************\",\n" + 
                "        \"out_request_no\": \"****************\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_precreate_success_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权冻结成功 - 结果通知方式
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_success_with_notify()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success_with_notify_1", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 查单接口返回支付中
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"40004\",\n" + 
                "        \"msg\": \"Business Failed\",\n" + 
                "        \"sub_code\": \"AUTH_ORDER_NOT_EXIST\",\n" + 
                "        \"sub_msg\": \"支付宝资金授权订单不存在\"\n" + 
                "    },\n" + 
                "    \"sign\": \"DiOKR9cv5ivojuRcjVkUbJlb7clPQFFPx/Ew9wi11bh9aILAMZp4cnJ5KiMjW2pxxxKVJDJZdHKJFYdYCOkuFXD/3WSAV+wqOmrsxC9nn33uvDkmhNe1P5l0wkCf/1+eWE0C1Uspn1WmEgcRskyxLQp9K4XB4WDsN/Gp9+moVU4=\"\n" + 
                "}");
        
        // 通知支付接口，订单已支付成功
        /* FIXME 通过perform方式调用/upay/v2/notify/alipaywap/返回406
        String freezeSuccess = "amount=0.01&app_id=2015102000490218&auth_app_id=2019100868125859&auth_no=2019112910002001690552103692&charset=UTF-8&credit_amount=0.01&fund_amount=0.00&gmt_create=2019-11-29%2009%3A39%3A31&gmt_trans=2019-11-29%2009%3A39%3A36&notify_id=2019112900222093937028701430699358&notify_time=2019-11-29%2009%3A40%3A15&notify_type=fund_auth_freeze&operation_id=20191129974867636905&operation_type=FREEZE&out_order_no=****************&out_request_no=****************&payer_logon_id=177****6394&payer_user_id=2088702321776694&pre_auth_type=CREDIT_AUTH&rest_amount=0.01&rest_credit_amount=0.01&rest_fund_amount=0.00&sign=TTBGMFFM9LUgDg20aAOofiL%2BqJ7gN5WnIsjn0rxiS6hjOu8%2F1f7N5XNRs9jd5tFz2wpgPzkdvHz814gwjSsJuqJLKcqRKFJR6R0EAJ2%2FQPwyFjo8hbIWhum1Ecgd1VEENVox5c%2BoVNhii2Me0ypDtf8Qa%2BpnMN9zyr6zC%2BG%2BI4Q%3D&sign_type=RSA&status=SUCCESS&total_freeze_amount=0.01&total_freeze_credit_amount=0.01&total_freeze_fund_amount=0.00&total_pay_amount=0.00&total_pay_credit_amount=0.00&total_pay_fund_amount=0.00&total_unfreeze_amount=0.00&total_unfreeze_credit_amount=0.00&total_unfreeze_fund_amount=0.00&version=1.0";
//        resultString = postPerform("test_prefreeze_success_with_notify_2", "/upay/v2/notify/alipaywap/"+ Digest.md5(StringUtils.join(request.getString(UpayService.TERMINAL_SN), "-", request.getString(UpayService.CLIENT_SN)).getBytes()) +"/abcdefg", MediaType.APPLICATION_FORM_URLENCODED_VALUE, freezeSuccess, true);
        mockMvc.perform(post("/upay/v2/notify/alipaywap/"+  +"/abcdefg")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .accept(MediaType.ALL)
                .param("amount", "0.01")
                .param("app_id", "2015102000490218")
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andReturn();
        */
        Map<String, String> signResponse = CollectionUtil.hashMap("gmt_trans", DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss"),
                                "status", "SUCCESS",
                                "payer_logon_id", payerLogin,
                                "payer_user_id", payerUid,
                                "amount", "0.30",
                                "auth_no", authNo,
                                "charset", "UTF-8",
                                "out_order_no", BeanUtil.getPropString(result, "biz_response.data.sn")
                );
        
        signResponse.put("sign", AlipaySignature.rsaSign(signResponse, SupportUtil.RSA_PRIVATE_KEY, "utf-8"));
        signResponse.put("sign_type", "RSA");
        Map<String, Object> response = new HashMap<String, Object>(signResponse);
        mockGetRsaKeyDataById(SupportUtil.RSA_PUBLIC_KEY);
        notifyController.alipayWapNotify(response, Digest.md5(StringUtils.join(request.getString(UpayService.TERMINAL_SN), "-", request.getString(UpayService.CLIENT_SN)).getBytes()));
        
        Thread.sleep(3000);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        resultString = postPerform("test_prefreeze_success_with_query_3", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权完成成功
     * 
     * 期望结果：预授权完成成功
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_consume_success()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String requestTradeNo = MapUtil.getString(postRequest, BusinessV1Fields.OUT_TRADE_NO, "123456789");
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else {
                    Map bizContent = null;
                    if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof Map) {
                        bizContent = (Map)postRequest.get(ProtocolV2Fields.BIZ_CONTENT);
                    }else if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof String) {
                        bizContent = JsonUtil.jsonStrToObject(postRequest.get(ProtocolV2Fields.BIZ_CONTENT).toString(), Map.class);
                    }
                    String outTradeNo = MapUtil.getString(bizContent, BusinessV2Fields.OUT_TRADE_NO);
                    return "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                            "        \"buyer_pay_amount\": \"0.01\",\n" + 
                            "        \"buyer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"" + outTradeNo +"\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"KNyrfMQOtwI+An56lBpcJevgCSs/3SIum0MQhXl0lBgYILpOnsESVj9ik5XZsphlaHBmKXjGsyaQUgbWUdcn5uqz2GKdRiUn5dPJuZ2dMmtTVJTIGW+EtbWqsmtAu31iZ1wFHBnMJRDbTcszwuHS9B+BijoVnCGq7PLt1+nSFpc=\"\n" + 
                            "}";
                }
            }
        });
        Thread.sleep(6100);
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("test_prefreeze_success_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 校验预授权完成时上送的商户订单号是否正确
        String sn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Filter<Map<String, Object>> filter = dataRepository.getTransactionDao().filter(
                Criteria.where(Transaction.ORDER_SN).is(sn)
                        .with(Transaction.TYPE).is(Transaction.TYPE_DEPOSIT_CONSUME)
                        .with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)
        );
        Map<String,Object> consumerTransaction = filter.fetchOne();
        assertNotNull("consumer transaction", consumerTransaction);
        assertEquals(consumerTransaction, Transaction.TRADE_NO, consumerTransaction.get(Transaction.ORDER_SN));
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权完成失败（明确失败），单独状态置为deposit_freeze
     * 
     * 期望结果：返回CONSUMER_ERROR，订单状态是DEPOSIT_FREEZED
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_consume_fail()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else {
                    Map bizContent = null;
                    if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof Map) {
                        bizContent = (Map)postRequest.get(ProtocolV2Fields.BIZ_CONTENT);
                    }else if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof String) {
                        bizContent = JsonUtil.jsonStrToObject(postRequest.get(ProtocolV2Fields.BIZ_CONTENT).toString(), Map.class);
                    }
                    List<String> errCodes = AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.stream().collect(Collectors.toList());
                    String outTradeNo = MapUtil.getString(bizContent, BusinessV2Fields.OUT_TRADE_NO);
                    return "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"40004\",\n" + 
                            "        \"msg\": \"Business Failed\",\n" + 
                            "        \"sub_code\": \""+ errCodes.get(ThreadLocalRandom.current().nextInt(errCodes.size())) +"\",\n" + 
                            "        \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                            "        \"buyer_pay_amount\": \"0.01\",\n" + 
                            "        \"buyer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"" + outTradeNo +"\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"KNyrfMQOtwI+An56lBpcJevgCSs/3SIum0MQhXl0lBgYILpOnsESVj9ik5XZsphlaHBmKXjGsyaQUgbWUdcn5uqz2GKdRiUn5dPJuZ2dMmtTVJTIGW+EtbWqsmtAu31iZ1wFHBnMJRDbTcszwuHS9B+BijoVnCGq7PLt1+nSFpc=\"\n" + 
                            "}";
                }
            }
        });
        Thread.sleep(6100);
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("test_prefreeze_success_fail", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_ERROR",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
                );
    }
    
    /**
     * 
     * 支付宝2.0 预授权订单同步
     * 
     * @throws Exception
     */
    @Test
    public void test_sync()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_sync", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else {
                    Map bizContent = null;
                    if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof Map) {
                        bizContent = (Map)postRequest.get(ProtocolV2Fields.BIZ_CONTENT);
                    }else if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof String) {
                        bizContent = JsonUtil.jsonStrToObject(postRequest.get(ProtocolV2Fields.BIZ_CONTENT).toString(), Map.class);
                    }
                    List<String> errCodes = AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.stream().collect(Collectors.toList());
                    String outTradeNo = System.currentTimeMillis() + "";
                    return "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"40004\",\n" + 
                            "        \"msg\": \"Business Failed\",\n" + 
                            "        \"sub_code\": \""+ errCodes.get(ThreadLocalRandom.current().nextInt(errCodes.size())) +"\",\n" + 
                            "        \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                            "        \"buyer_pay_amount\": \"0.01\",\n" + 
                            "        \"buyer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"" + outTradeNo +"\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"KNyrfMQOtwI+An56lBpcJevgCSs/3SIum0MQhXl0lBgYILpOnsESVj9ik5XZsphlaHBmKXjGsyaQUgbWUdcn5uqz2GKdRiUn5dPJuZ2dMmtTVJTIGW+EtbWqsmtAu31iZ1wFHBnMJRDbTcszwuHS9B+BijoVnCGq7PLt1+nSFpc=\"\n" + 
                            "}";
                }
            }
        });
        Thread.sleep(6100);
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("test_sync", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_ERROR",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
                );
        
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                return  "{\n" + 
                        "    \"alipay_trade_orderinfo_sync_response\": {\n" + 
                        "        \"code\": \"10000\",\n" + 
                        "        \"msg\": \"Success\",\n" + 
                        "        \"trade_no\": \"20180610121001004680073956707\",\n" + 
                        "        \"out_trade_no\": \"" + authNo + "\",\n" + 
                        "        \"buyer_user_id\": \"" + payerUid + "\"\n" + 
                        "    },\n" + 
                        "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                        "}";
            }
        });
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getLatestTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        PowerMockito.when(gatewaySupportService.getAlipayDepositConsumeTransactionList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(transaction));
        resultString = postPerform("test_sync", "/upay/v2/deposit/sync", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.toString()
        );
    }
}
