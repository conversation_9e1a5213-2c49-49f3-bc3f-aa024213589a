package com.wosai.upay.controller.deposit.prefreeze.weixin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.api.weixin.WeixinV3Client;
import com.wosai.mpay.util.HmacSignature;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SupportUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import static org.junit.Assert.assertNotNull;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

public class WeiXinV3PreFreezeTest extends BaseTestController {
    @MockBean
    WeixinV3Client weixinV3Client;
    
    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_WEIXIN_KEY);
    }

    @Test
    public void testPreFreezeConsumeSuccessWithQuery() throws Exception {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"**********\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "PREFREEZE_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );

        String consumerResult = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_id\":\"500001\",\"service_introduction\":\"某某酒店\",\"state\":\"DONE\",\"state_description\":\"\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":1,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":1}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":4000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"order_id\":\"15646546545165651651\",\"need_collection\":true}";
        String queryResult = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DONE\",\"state_description\":\"MCH_COMPLETE\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":0,\"paid_amount\":3900,\"details\":[{\"seq\":1,\"amount\":3900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(consumerResult, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResult, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "CONSUME_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.toString()
        );
    }
    
    
    /**
     * 
     * 预授权完成失败后，明确返回失败时，将订单状态置位预授权冻结状态
     * 
     * @throws Exception
     */
    @Test
    public void testPreFreezeConsumeFail() throws Exception {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"**********\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "PREFREEZE_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );

        List<String> failList = WeixinConstants.DEPOSIT_CONSUMER_V3_FAIL_LIST.stream().collect(Collectors.toList());
        String code = failList.get(ThreadLocalRandom.current().nextInt(failList.size()));
        String consumerResult = "{\"http_code\":400,\"code\":\""+code+"\"}";
        String queryResult = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DONE\",\"state_description\":\"MCH_COMPLETE\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":0,\"paid_amount\":3900,\"details\":[{\"seq\":1,\"amount\":3900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(consumerResult, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResult, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("testPreFreezeConsumeSuccessWithQuery_with_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "CONSUME_ERROR",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );
    }

    /**
     * 
     * 预授权完成失败后，明确返回失败时，将订单状态置位预授权冻结状态
     * 
     * @throws Exception
     */
    @Test
    public void testSync() throws Exception {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"**********\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("testSync", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "PREFREEZE_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("testSync", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );

        List<String> failList = WeixinConstants.DEPOSIT_CONSUMER_V3_FAIL_LIST.stream().collect(Collectors.toList());
        String code = failList.get(ThreadLocalRandom.current().nextInt(failList.size()));
        String consumerResult = "{\"http_code\":400,\"code\":\""+code+"\"}";
        String queryResult = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DONE\",\"state_description\":\"MCH_COMPLETE\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":0,\"paid_amount\":3900,\"details\":[{\"seq\":1,\"amount\":3900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(consumerResult, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResult, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("testSync", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "CONSUME_ERROR",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );

        String syncResult = "{\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"openid\":\"oUpF8uMuAJO_M2pxb1Q9zNjWeS6o\",\"state\":\"DONE\",\"state_description\":\"MCH_COMPLETE\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":0,\"paid_amount\":3900,\"details\":[{\"amount\":10000,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id \":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult = null;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(syncResult, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        resultString = postPerform("testSync", "/upay/v2/deposit/sync", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.toString()
        );
    }

    @Test
    public void testPreFreezeConsumeSuccessWithReplaceKey() throws Exception {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        Map<String, Object> weixinMinTradeParams = new HashMap<>(SupportUtil.WEIXIN_MIN_TRADE_PARAMS);
        Map<String, Object> tradeParams = MapUtil.getMap(weixinMinTradeParams, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
        tradeParams.put(TransactionParam.SERVICE_MODE, TransactionParam.SERVICE_MODE_PARTNER);
        tradeParams.put(TransactionParam.PRODUCT_CODE, TransactionParam.SQB_PRODUCT_CODE_PAY_SCORE);
        
        Map<String, Object> getAllParams = SupportUtil.buildGetAllParams(weixinMinTradeParams);
        mockGetAllPrams(getAllParams);
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"**********\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("testPreFreezeConsumeSuccessWithReplaceKey_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "PREFREEZE_SUCCESS",
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("testPreFreezeConsumeSuccessWithReplaceKey_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );

        String consumerResult = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_id\":\"500001\",\"service_introduction\":\"某某酒店\",\"state\":\"DONE\",\"state_description\":\"\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":1,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":1}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":4000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"order_id\":\"15646546545165651651\",\"need_collection\":true}";
        String queryResult = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"**********\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DONE\",\"state_description\":\"MCH_COMPLETE\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":0,\"paid_amount\":3900,\"details\":[{\"seq\":1,\"amount\":3900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    String method = invocation.getArgument(1);
                    Map providerResult;
                    if("POST".equals(method)) {
                        providerResult = JsonUtil.jsonStrToObject(consumerResult, Map.class);
                    }else {
                        providerResult = JsonUtil.jsonStrToObject(queryResult, Map.class);
                    }
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("testPreFreezeConsumeSuccessWithReplaceKey_with_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JSON.parseObject(resultString);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        Map<String, Object> consumerTransaction = dataRepository.getTransactionByTsn(MapUtil.getString(getAllParams, TransactionParam.MERCHANT_ID), BeanUtil.getPropString(result, "biz_response.data.tsn"));
        assertNotNull(consumerTransaction);
        String privateKey = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_PRIVATE_KEY_V3);
        Map<String, Object> replaceValue = null;
        
        Map<String, List<Map<String, Objects>>> config = ApolloConfigurationCenterUtil.getTradeParamsReplaceConfig();
        List<Map<String, Object>> replaceTradeParams = (List<Map<String, Object>>) MapUtil.getObject(config, "weixin_mini_trade_params");
        for (Map<String, Object> replaceTradeParam : replaceTradeParams) {
            String matchKey = MapUtil.getString(replaceTradeParam, UpayConstant.TRADE_PARAMS_REPLACE_MATCH_KEY);
            if (TransactionParam.WEIXIN_PRIVATE_KEY_V3.equals(matchKey)) {
                List<String> matchValues = (List<String>) MapUtil.getObject(replaceTradeParam, UpayConstant.TRADE_PARAMS_REPLACE_MATCH_VALUES);
                if (matchValues != null && matchValues.contains(privateKey)) {
                    replaceValue = MapUtil.getMap(replaceTradeParam, UpayConstant.TRADE_PARAMS_REPLACE_REPLACE);
                    break;
                }
            }
        }
        
        if (replaceValue != null) {
            Map<String, Object> consumeTradeParams = MapUtil.getMap(MapUtil.getMap(consumerTransaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
            for (String key : replaceValue.keySet()) {
                String changeValue = MapUtil.getString(replaceValue, key);
                String consumeTrValue = MapUtil.getString(consumeTradeParams, key);
                Assert.assertEquals(changeValue, consumeTrValue);
            }
        }
    }
}
