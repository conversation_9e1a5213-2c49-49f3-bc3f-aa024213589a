package com.wosai.upay.controller.deposit.freeze.alipay;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.SupportUtil;

public class AlipayV2FreezeTest extends BaseTestController {
    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
    }

    /**
     * 
     * 支付宝2.0 线下预授权完成失败（明确失败），单独状态置为deposit_freeze
     * 
     * 期望结果：返回CONSUMER_ERROR，订单状态是DEPOSIT_FREEZED
     * 
     * @throws Exception
     */
    @Test
    public void test_freeze_consume_fail()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));
        
        // mock返回
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_FREEZE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_fund_auth_order_freeze_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else {
                    Map bizContent = null;
                    if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof Map) {
                        bizContent = (Map)postRequest.get(ProtocolV2Fields.BIZ_CONTENT);
                    }else if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof String) {
                        bizContent = JsonUtil.jsonStrToObject(postRequest.get(ProtocolV2Fields.BIZ_CONTENT).toString(), Map.class);
                    }
                    List<String> errCodes = AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.stream().collect(Collectors.toList());
                    String outTradeNo = MapUtil.getString(bizContent, BusinessV2Fields.OUT_TRADE_NO);
                    return "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"40004\",\n" + 
                            "        \"msg\": \"Business Failed\",\n" + 
                            "        \"sub_code\": \""+ errCodes.get(ThreadLocalRandom.current().nextInt(errCodes.size())) +"\",\n" + 
                            "        \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                            "        \"buyer_pay_amount\": \"0.01\",\n" + 
                            "        \"buyer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"" + outTradeNo +"\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"KNyrfMQOtwI+An56lBpcJevgCSs/3SIum0MQhXl0lBgYILpOnsESVj9ik5XZsphlaHBmKXjGsyaQUgbWUdcn5uqz2GKdRiUn5dPJuZ2dMmtTVJTIGW+EtbWqsmtAu31iZ1wFHBnMJRDbTcszwuHS9B+BijoVnCGq7PLt1+nSFpc=\"\n" + 
                            "}";
                }
            }
        });
        
        // 预授权下单
        JSONObject request = SupportUtil.buildPayRequest("284843617877639054");
        String resultString = postPerform("test_freeze_consume_fail", "/upay/v2/deposit/freeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "DEPOSIT_FREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("test_freeze_consume_fail", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_ERROR",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
                );
    }
    
    /**
     * 
     * 支付宝2.0 线下预授权冻结聪哥
     * 
     * 期望结果：返回DEPOSIT_FREEZED
     * 
     * @throws Exception
     */
    @Test
    public void test_pay_success()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));
        
        // mock返回
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_FREEZE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_fund_auth_order_freeze_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        // 预授权下单
        JSONObject request = SupportUtil.buildPayRequest("284843617877639054");
        String resultString = postPerform("test_pay_success", "/upay/v2/deposit/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "DEPOSIT_FREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
    }
}
