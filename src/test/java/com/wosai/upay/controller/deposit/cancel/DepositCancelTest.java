package com.wosai.upay.controller.deposit.cancel;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.weixin.WeixinV3Client;
import com.wosai.mpay.util.HmacSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.SupportUtil;

public class DepositCancelTest extends BaseTestController{
    @MockBean
    UpayOrderService supportService;
    @MockBean
    GatewaySupportService gatewaySupportService;
    @MockBean
    WeixinV3Client weixinV3Client;
    
    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
    }

    /**
     * 
     * 支付宝2.0 历史库线上预授权撤销成功
     * 
     * 期望结果：预授权撤销成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_history_deposit_cancel()throws Exception{
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_alipay_history_deposit_cancel_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_alipay_history_deposit_cancel_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 删除数据
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getFreezeTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        assertNotNull(order);
        assertNotNull(transaction);
        dataRepository.getOrderDao().delete(MapUtil.getString(order, DaoConstants.ID));
        dataRepository.getTransactionDao().delete(MapUtil.getString(transaction, DaoConstants.ID));
        
        // 设置mock
        PowerMockito.when(supportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(order);
        PowerMockito.when(gatewaySupportService.getTransactionByClientTsn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);
        
        AtomicInteger updateCnt = new AtomicInteger();
        PowerMockito.when(supportService.updateOrder(Mockito.anyMap())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) throws Throwable {
                updateCnt.addAndGet(1);
                return true;
            }
        });
        
        AtomicBoolean isRemarkSet = new AtomicBoolean(false);
        String remark = "测试上送解冻说明";
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            Map<String, Object> providerRequest = (Map<String, Object>)invocation.getArgument(4);
            String bizContent = BeanUtil.getPropString(providerRequest, "biz_content");
            isRemarkSet.set(remark.equals(BeanUtil.getPropString(JsonUtil.jsonStrToObject(bizContent, Map.class), "remark")));
            return "{\n" + 
                    "    \"sign\": \"jfAz0Yi0OUvAPqYTzA0DLysx0ri++yf7o/lkHOHaG1Zy2fHBf3j4WM\\n+sJWHZUuyInt6V+wn+6IP9AmwRTKi+GGdWjPrsfBjXqR7H5aBnLhMsAltV7v4cYjhug\\nuAqh4WkaJO6v6CfdybDpzHlxE6Thoucnad+OsjdCXkNd1g3UuU=\\n\",\n" + 
                    "    \"alipay_fund_auth_operation_cancel_response\": {\n" + 
                    "        \"code\": \"10000\",\n" + 
                    "        \"msg\": \"处理成功\",\n" + 
                    "        \"out_order_no\": \"2003259247503667\",\n" + 
                    "        \"out_request_no\": \"2003259247503667\",\n" + 
                    "        \"action\": \"unfreeze\"\n" + 
                    "    }\n" + 
                    "}";
        });
        request.put("extended", CollectionUtil.hashMap("remark", remark));
        
        resultString = postPerform("test_alipay_history_deposit_cancel_with_cancel", "/upay/v2/deposit/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "DEPOSIT_CANCEL_SUCCESS");
        
        Assert.assertEquals("hbase 更新次数", 2, updateCnt.get());
        assertTrue("自定义上送remark", isRemarkSet.get());
    }
    
    /**
     * 
     * 支付宝2.0 历史库线上预授权撤销成功（不上传reason）
     * 
     * 期望结果：预授权撤销成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_history_deposit_cancel_2()throws Exception{
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS));
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_alipay_history_deposit_cancel_2_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn( 
                "{\n" + 
                "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                "        \"code\": \"10000\",\n" + 
                "        \"msg\": \"Success\",\n" + 
                "        \"amount\": \"0.01\",\n" + 
                "        \"auth_no\": \"" + authNo + "\",\n" + 
                "        \"credit_amount\": \"0.01\",\n" + 
                "        \"extra_param\": \"{}\",\n" + 
                "        \"fund_amount\": \"0.00\",\n" + 
                "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                "        \"operation_id\": \"20191129974867636905\",\n" + 
                "        \"operation_type\": \"FREEZE\",\n" + 
                "        \"order_title\": \"mock\",\n" + 
                "        \"out_order_no\": \"7893259287243770\",\n" + 
                "        \"out_request_no\": \"7893259287243770\",\n" + 
                "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                "        \"remark\": \"mock\",\n" + 
                "        \"rest_amount\": \"0.01\",\n" + 
                "        \"rest_credit_amount\": \"0.01\",\n" + 
                "        \"rest_fund_amount\": \"0.00\",\n" + 
                "        \"status\": \"SUCCESS\",\n" + 
                "        \"total_freeze_amount\": \"0.01\",\n" + 
                "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                "        \"total_pay_amount\": \"0.00\",\n" + 
                "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                "}");
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_alipay_history_deposit_cancel_2_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 删除数据
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getFreezeTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        assertNotNull(order);
        assertNotNull(transaction);
        dataRepository.getOrderDao().delete(MapUtil.getString(order, DaoConstants.ID));
        dataRepository.getTransactionDao().delete(MapUtil.getString(transaction, DaoConstants.ID));
        
        // 设置mock
        PowerMockito.when(supportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(order);
        PowerMockito.when(gatewaySupportService.getTransactionByClientTsn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);
        
        AtomicInteger updateCnt = new AtomicInteger();
        PowerMockito.when(supportService.updateOrder(Mockito.anyMap())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) throws Throwable {
                updateCnt.addAndGet(1);
                return true;
            }
        });
        
        AtomicBoolean isRemarkSet = new AtomicBoolean(false);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            Map<String, Object> providerRequest = (Map<String, Object>)invocation.getArgument(4);
            String bizContent = BeanUtil.getPropString(providerRequest, "biz_content");
            isRemarkSet.set(BeanUtil.getPropString(JsonUtil.jsonStrToObject(bizContent, Map.class), "remark").indexOf("解冻") > 0);
            return "{\n" + 
                    "    \"sign\": \"jfAz0Yi0OUvAPqYTzA0DLysx0ri++yf7o/lkHOHaG1Zy2fHBf3j4WM\\n+sJWHZUuyInt6V+wn+6IP9AmwRTKi+GGdWjPrsfBjXqR7H5aBnLhMsAltV7v4cYjhug\\nuAqh4WkaJO6v6CfdybDpzHlxE6Thoucnad+OsjdCXkNd1g3UuU=\\n\",\n" + 
                    "    \"alipay_fund_auth_operation_cancel_response\": {\n" + 
                    "        \"code\": \"10000\",\n" + 
                    "        \"msg\": \"处理成功\",\n" + 
                    "        \"out_order_no\": \"2003259247503667\",\n" + 
                    "        \"out_request_no\": \"2003259247503667\",\n" + 
                    "        \"action\": \"unfreeze\"\n" + 
                    "    }\n" + 
                    "}";
        });
        
        resultString = postPerform("test_alipay_history_deposit_cancel_2_with_cancel", "/upay/v2/deposit/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "DEPOSIT_CANCEL_SUCCESS");
        
        Assert.assertEquals("hbase 更新次数", 2, updateCnt.get());
        assertTrue("未上送remark", isRemarkSet.get());
    }
    
    /**
     * 
     * 微信线上资金授权 线上预授权撤销成功
     * 
     * 期望结果：预授权撤销成功
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_deposit_cancel()throws Exception{
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_WEIXIN_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MIN_TRADE_PARAMS));
        
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"1230000109\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"1230000109\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
              Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
              Mockito.any())).thenAnswer(invocation ->{
                  String method = invocation.getArgument(1);
                  Map result;
                  if("POST".equals(method)) {
                      result = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                  }else {
                      result = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                  }
                  result.put("http_code", 200);
                  return result;
              });
        
        // 预授权下单
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("test_weixin_deposit_cancel_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", "PREFREEZE_SUCCESS",
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("test_weixin_deposit_cancel_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
              "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );
        
        String cancelResult = "{\n" + 
                "    \"mchid\": \"1230000109\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        AtomicBoolean isReasonSet = new AtomicBoolean(false);
        String reason = "测试上送解冻说明";
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    Map<String, Object> providerRequest = (Map<String, Object>)invocation.getArgument(5);
                    isReasonSet.set(reason.equals(BeanUtil.getPropString(providerRequest, "reason")));

                    Map providerResult = JsonUtil.jsonStrToObject(cancelResult, Map.class);;
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        request.put("extended", CollectionUtil.hashMap("reason", reason));
        
        resultString = postPerform("test_weixin_deposit_cancel_with_cancel", "/upay/v2/deposit/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "DEPOSIT_CANCEL_SUCCESS");
        
        assertTrue("自定义上送reason", isReasonSet.get());
        
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        assertNotNull(order);
        assertEquals(order, "status", Order.STATUS_DEPOSIT_CANCELED);
    }
    
    /**
     * 
     * 微信线上资金授权 线上预授权撤销成功(不上送reason)
     * 
     * 期望结果：预授权撤销成功
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_deposit_cancel_2()throws Exception{
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_WEIXIN_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MIN_TRADE_PARAMS));
        
        String prefreezeResultStr = "{\n" + 
                "    \"mchid\": \"1230000109\",\n" + 
                "    \"package\": \"DJIOSQPYWDxsjdldeuwhdodwxasd_dDiodnwjh9we\",\n" + 
                "    \"service_introduction\": \"d3929cea\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"notify_url\": \"https://upay-gateway-mock.iwosai.com/upay/v2/notify/weixinV3/64d04fbd8306d95a177aab7c358ce694/c8f53%262001259247793107%263%2621590000000603250%26f6a2648b-616f-462b-8975-6aaa23e69ed3%264%261586846908857%26871%260%260\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"time_range\": {\n" + 
                "        \"start_time\": \"OnAccept\"\n" + 
                "    },\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"risk_fund\": {\n" + 
                "        \"amount\": 100,\n" + 
                "        \"name\": \"DEPOSIT\"\n" + 
                "    },\n" + 
                "    \"state\": \"CREATED\",\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        String queryResultStr = "{\"http_code\":200,\"appid\":\"wxd678efh567hg6787\",\"mchid\":\"1230000109\",\"service_id\":\"500001\",\"out_order_no\":\"1234323JKHDFE1243252\",\"service_introduction\":\"某某酒店\",\"state\":\"DOING\",\"state_description\":\"USER_CONFIRM\",\"total_amount\":3900,\"post_payments\":[{\"name\":\"就餐费用服务费\",\"amount\":4000,\"description\":\"就餐人均100元服务费：100/小时\",\"count\":1}],\"post_discounts\":[{\"name\":\"满20减1元\",\"description\":\"不与其他优惠叠加\",\"amount\":100}],\"risk_fund\":{\"name\":\"ESTIMATE_ORDER_COST\",\"amount\":10000,\"description\":\"就餐的预估费用\"},\"time_range\":{\"start_time\":\"20091225091010\",\"end_time\":\"20091225121010\"},\"location\":{\"start_location\":\"嗨客时尚主题展餐厅\",\"end_location\":\"嗨客时尚主题展餐厅\"},\"attach\":\"Easdfowealsdkjfnlaksjdlfkwqoi&wl3l2sald\",\"notify_url\":\"https://api.test.com\",\"order_id\":\"15646546545165651651\",\"need_collection\":true,\"collection\":{\"state\":\"USER_PAID\",\"total_amount\":3900,\"paying_amount\":3000,\"paid_amount\":900,\"details\":[{\"seq\":1,\"amount\":900,\"paid_type\":\"NEWTON\",\"paid_time\":\"20091225091210\",\"transaction_id\":\"15646546545165651651\"}]}}";
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
              Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
              Mockito.any())).thenAnswer(invocation ->{
                  String method = invocation.getArgument(1);
                  Map result;
                  if("POST".equals(method)) {
                      result = JsonUtil.jsonStrToObject(prefreezeResultStr, Map.class);
                  }else {
                      result = JsonUtil.jsonStrToObject(queryResultStr, Map.class);
                  }
                  result.put("http_code", 200);
                  return result;
              });
        
        // 预授权下单
        JSONObject request = SupportUtil.buildWeixinPreCreateRequest(Order.PAYWAY_WEIXIN, Order.SUB_PAYWAY_MINI);
        String resultString = postPerform("test_weixin_deposit_cancel_2_with_prefreeze", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map<String,Object> result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", "PREFREEZE_SUCCESS",
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );

        Thread.sleep(6100);
        resultString = postPerform("test_weixin_deposit_cancel_2_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
              "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
        );
        
        String cancelResult = "{\n" + 
                "    \"mchid\": \"1230000109\",\n" + 
                "    \"http_code\": 200,\n" + 
                "    \"appid\": \"wx141c266174366ccf\",\n" + 
                "    \"service_id\": \"00002000000000157865268295736578\",\n" + 
                "    \"out_order_no\": \"2001259247793107\",\n" + 
                "    \"collection\": {\n" + 
                "        \"paying_amount\": 0\n" + 
                "    },\n" + 
                "    \"order_id\": \"1585891580730770509\"\n" + 
                "}";
        AtomicBoolean isReasonSet = new AtomicBoolean(false);
        PowerMockito.when(weixinV3Client.call(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenAnswer(invocation ->{
                    Map<String, Object> providerRequest = (Map<String, Object>)invocation.getArgument(5);
                    isReasonSet.set(BeanUtil.getPropString(providerRequest, "reason", "").indexOf("解冻")>0);

                    Map providerResult = JsonUtil.jsonStrToObject(cancelResult, Map.class);;
                    providerResult.put("http_code", 200);
                    return providerResult;
                });
        
        resultString = postPerform("test_weixin_deposit_cancel_2_with_cancel", "/upay/v2/deposit/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "DEPOSIT_CANCEL_SUCCESS");
        
        assertTrue("未上送reason", isReasonSet.get());
        
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        assertNotNull(order);
        assertEquals(order, "status", Order.STATUS_DEPOSIT_CANCELED);
    }
}
