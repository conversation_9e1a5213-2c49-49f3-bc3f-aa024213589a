package com.wosai.upay.controller.deposit.prefreeze.alipay;

import static org.junit.Assert.assertNotNull;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.BusinessV1Fields;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.Digest;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.SupportUtil;

public class DirectUnionPayAlipayV2PreFreezeTest extends BaseTestController{
    @Before
    public void init() {
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UP_DIRECT_TRADE_PARAMS_ALIPAY));
    }

    /**
     * 
     * 支付宝2.0 线上预授权冻结成功
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_success_with_query()throws Exception{
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String result = "";
            Map postRequest = invocation.getArgument(4);
            String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
            if(AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_APP_FREEZE.equals(method)) {
                result = "{\n" + 
                        "    \"alipay_fund_auth_auth_order_app_freeze_response\": {\n" + 
                        "        \"code\": \"10000\",\n" + 
                        "        \"msg\": \"Success\",\n" + 
                        "        \"order_str\": \"alipay_sdk=alipay-sdk-java-4.8.10.ALL&app_auth_token=201912BB71b9c5e20b61457db987c8b2fafc0X59&app_id=2015102000490218&biz_content=%7B%22amount%22%3A%2299.00%22%2C%22extra_param%22%3A%22%7B%5C%22category%5C%22%3A%5C%22RENT_SHARABLE_CHARGERS%5C%22%2C%5C%22serviceId%5C%22%3A%5C%222019112600000000000003035900%5C%22%7D%22%2C%22order_title%22%3A%22%E7%94%B5%E9%A5%B1%E9%A5%B1%E5%85%B1%E4%BA%AB%E5%85%85%E7%94%B5%E5%AE%9D%22%2C%22out_order_no%22%3A%227895237413026004%22%2C%22out_request_no%22%3A%227895237413026004%22%2C%22pay_timeout%22%3A%224m%22%2C%22product_code%22%3A%22PRE_AUTH_ONLINE%22%7D&charset=UTF-8&format=json&method=alipay.fund.auth.order.app.freeze&notify_url=http%3A%2F%2Fgateway.shouqianba.com%2Fupay%2Fv2%2Fnotify%2Falipaywap%2F59d968d0ec5aad56a81051170f9b114f%2Fe1f80%25267895237413026004%25262%25261580000002596741%252617032ac1-cc98-4a8f-83ec-42e69ad24018%25264%25261596508493700%25269900%25260%25263&sign=Irn5TH5uCMF6kC%2FyB3c9Kw2pM2kr3rbyoiReyvkXyxnbDA3%2FegHCNOtzOaQEXjr0bLdir%2FgZbE5x7a2kLT4YsGXBpzB43rtzkVLWvjy2Du3ID0j%2B5E9QzpveuLn2kIylep2cYjbEuJQf0rZa%2B2jQCBBveKShr7A1j5Oz9tXD%2FCM%3D&sign_type=RSA&timestamp=2020-08-04+10%3A34%3A53&version=1.0}}\"\n" + 
                        "    },\n" + 
                        "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                        "}";

            }else {
                result = "{\n" + 
                            "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
            }
            
            return result;
            
        });
        
        String resultString = postPerform("test_prefreeze_success_with_query", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        
        Thread.sleep(6100);
        resultString = postPerform("test_prefreeze_success_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权冻结成功 - 结果通知方式
     * 
     * 期望结果：预授权冻结成功
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_success_with_notify()throws Exception{
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String result = "";
            Map postRequest = invocation.getArgument(4);
            String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
            if(AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_APP_FREEZE.equals(method)) {
                result = "{\n" + 
                        "    \"alipay_fund_auth_auth_order_app_freeze_response\": {\n" + 
                        "        \"code\": \"10000\",\n" + 
                        "        \"msg\": \"Success\",\n" + 
                        "        \"order_str\": \"alipay_sdk=alipay-sdk-java-4.8.10.ALL&app_auth_token=201912BB71b9c5e20b61457db987c8b2fafc0X59&app_id=2015102000490218&biz_content=%7B%22amount%22%3A%2299.00%22%2C%22extra_param%22%3A%22%7B%5C%22category%5C%22%3A%5C%22RENT_SHARABLE_CHARGERS%5C%22%2C%5C%22serviceId%5C%22%3A%5C%222019112600000000000003035900%5C%22%7D%22%2C%22order_title%22%3A%22%E7%94%B5%E9%A5%B1%E9%A5%B1%E5%85%B1%E4%BA%AB%E5%85%85%E7%94%B5%E5%AE%9D%22%2C%22out_order_no%22%3A%227895237413026004%22%2C%22out_request_no%22%3A%227895237413026004%22%2C%22pay_timeout%22%3A%224m%22%2C%22product_code%22%3A%22PRE_AUTH_ONLINE%22%7D&charset=UTF-8&format=json&method=alipay.fund.auth.order.app.freeze&notify_url=http%3A%2F%2Fgateway.shouqianba.com%2Fupay%2Fv2%2Fnotify%2Falipaywap%2F59d968d0ec5aad56a81051170f9b114f%2Fe1f80%25267895237413026004%25262%25261580000002596741%252617032ac1-cc98-4a8f-83ec-42e69ad24018%25264%25261596508493700%25269900%25260%25263&sign=Irn5TH5uCMF6kC%2FyB3c9Kw2pM2kr3rbyoiReyvkXyxnbDA3%2FegHCNOtzOaQEXjr0bLdir%2FgZbE5x7a2kLT4YsGXBpzB43rtzkVLWvjy2Du3ID0j%2B5E9QzpveuLn2kIylep2cYjbEuJQf0rZa%2B2jQCBBveKShr7A1j5Oz9tXD%2FCM%3D&sign_type=RSA&timestamp=2020-08-04+10%3A34%3A53&version=1.0}}\"\n" + 
                        "    },\n" + 
                        "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                        "}";

            }else {
                // 查单接口返回支付中
                result = "{\n" + 
                        "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                        "        \"code\": \"10000\",\n" + 
                        "        \"msg\": \"Success\",\n" + 
                        "        \"amount\": \"99.00\",\n" + 
                        "        \"auth_no\": \"2020080410002001900565220629\",\n" + 
                        "        \"extra_param\": \"{}\",\n" + 
                        "        \"gmt_create\": \"2020-08-04 11:16:03\",\n" + 
                        "        \"operation_id\": \"20200804598165249005\",\n" + 
                        "        \"operation_type\": \"FREEZE\",\n" + 
                        "        \"order_title\": \"电饱饱共享充电宝\",\n" + 
                        "        \"out_order_no\": \"7895237418430748\",\n" + 
                        "        \"out_request_no\": \"7895237418430748\",\n" + 
                        "        \"payer_logon_id\": \"133****2161\",\n" + 
                        "        \"payer_user_id\": \"2088222094454901\",\n" + 
                        "        \"remark\": \"电饱饱共享充电宝\",\n" + 
                        "        \"rest_amount\": \"0.00\",\n" + 
                        "        \"status\": \"INIT\",\n" + 
                        "        \"total_freeze_amount\": \"0.00\",\n" + 
                        "        \"total_pay_amount\": \"0.00\"\n" + 
                        "    },\n" + 
                        "    \"sign\": \"q6p7FTuAUR3lIERYuvfvz+7nfxjDnLzYXlcdVCZOiScyY0etls4nZRStmPaqMaajJ9vItTrjZAHwm2JrbRwgYtz+/PpJPRltJkUUpl8PX3vV+UWrQKvXQsth88Y0QmvonK6ToG4x+6clWVUxX1KTMsS3VYnp9S9pbVVjEHcoOj0=\"\n" + 
                        "}";
            }
            
            return result;
            
        });
        
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success_with_notify_1", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        // 预授权成功
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        Map<String, String> signResponse = CollectionUtil.hashMap("gmt_trans", DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss"),
                                "status", "SUCCESS",
                                "payer_logon_id", payerLogin,
                                "payer_user_id", payerUid,
                                "amount", "0.30",
                                "auth_no", authNo,
                                "charset", "UTF-8",
                                "out_order_no", BeanUtil.getPropString(result, "biz_response.data.sn")
                );
        
        signResponse.put("sign", AlipaySignature.rsaSign(signResponse, SupportUtil.RSA_PRIVATE_KEY, "utf-8"));
        signResponse.put("sign_type", "RSA");
        Map<String, Object> response = new HashMap<String, Object>(signResponse);
        mockGetRsaKeyDataById(SupportUtil.RSA_PUBLIC_KEY);
        notifyController.alipayUnionpayWapNotify(response, Digest.md5(StringUtils.join(request.getString(UpayService.TERMINAL_SN), "-", request.getString(UpayService.CLIENT_SN)).getBytes()));
        
        Thread.sleep(3000);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        resultString = postPerform("test_prefreeze_success_with_query_3", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权完成成功
     * 
     * 期望结果：预授权完成成功
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_consume_success()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UP_DIRECT_TRADE_PARAMS_ALIPAY));
        
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String requestTradeNo = MapUtil.getString(postRequest, BusinessV1Fields.OUT_TRADE_NO, "123456789");
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_APP_FREEZE.equals(method)) {
                    // 预授权冻结
                    return "{\n" + 
                            "    \"alipay_fund_auth_auth_order_app_freeze_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"order_str\": \"alipay_sdk=alipay-sdk-java-4.8.10.ALL&app_auth_token=201912BB71b9c5e20b61457db987c8b2fafc0X59&app_id=2015102000490218&biz_content=%7B%22amount%22%3A%2299.00%22%2C%22extra_param%22%3A%22%7B%5C%22category%5C%22%3A%5C%22RENT_SHARABLE_CHARGERS%5C%22%2C%5C%22serviceId%5C%22%3A%5C%222019112600000000000003035900%5C%22%7D%22%2C%22order_title%22%3A%22%E7%94%B5%E9%A5%B1%E9%A5%B1%E5%85%B1%E4%BA%AB%E5%85%85%E7%94%B5%E5%AE%9D%22%2C%22out_order_no%22%3A%227895237413026004%22%2C%22out_request_no%22%3A%227895237413026004%22%2C%22pay_timeout%22%3A%224m%22%2C%22product_code%22%3A%22PRE_AUTH_ONLINE%22%7D&charset=UTF-8&format=json&method=alipay.fund.auth.order.app.freeze&notify_url=http%3A%2F%2Fgateway.shouqianba.com%2Fupay%2Fv2%2Fnotify%2Falipaywap%2F59d968d0ec5aad56a81051170f9b114f%2Fe1f80%25267895237413026004%25262%25261580000002596741%252617032ac1-cc98-4a8f-83ec-42e69ad24018%25264%25261596508493700%25269900%25260%25263&sign=Irn5TH5uCMF6kC%2FyB3c9Kw2pM2kr3rbyoiReyvkXyxnbDA3%2FegHCNOtzOaQEXjr0bLdir%2FgZbE5x7a2kLT4YsGXBpzB43rtzkVLWvjy2Du3ID0j%2B5E9QzpveuLn2kIylep2cYjbEuJQf0rZa%2B2jQCBBveKShr7A1j5Oz9tXD%2FCM%3D&sign_type=RSA&timestamp=2020-08-04+10%3A34%3A53&version=1.0}}\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else if(AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY.equals(method)){
                    // 预授权冻结查询
                    return  "{\n" + 
                            "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else {
                    // 预授权完成
                    Map bizContent = null;
                    if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof Map) {
                        bizContent = (Map)postRequest.get(ProtocolV2Fields.BIZ_CONTENT);
                    }else if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof String) {
                        bizContent = JsonUtil.jsonStrToObject(postRequest.get(ProtocolV2Fields.BIZ_CONTENT).toString(), Map.class);
                    }
                    String outTradeNo = MapUtil.getString(bizContent, BusinessV2Fields.OUT_TRADE_NO);
                    return "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                            "        \"buyer_pay_amount\": \"0.01\",\n" + 
                            "        \"buyer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"" + outTradeNo +"\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"KNyrfMQOtwI+An56lBpcJevgCSs/3SIum0MQhXl0lBgYILpOnsESVj9ik5XZsphlaHBmKXjGsyaQUgbWUdcn5uqz2GKdRiUn5dPJuZ2dMmtTVJTIGW+EtbWqsmtAu31iZ1wFHBnMJRDbTcszwuHS9B+BijoVnCGq7PLt1+nSFpc=\"\n" + 
                            "}";
                }
            }
        });
        
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        
        Thread.sleep(6100);
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("test_prefreeze_success_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.toString(),
                        "biz_response.data.channel_finish_time", channelFinishTime/1000*1000 + ""
                );
        
        // 校验预授权完成时上送的商户订单号是否正确
        String sn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Filter<Map<String, Object>> filter = dataRepository.getTransactionDao().filter(
                Criteria.where(Transaction.ORDER_SN).is(sn)
                        .with(Transaction.TYPE).is(Transaction.TYPE_DEPOSIT_CONSUME)
                        .with(Transaction.STATUS).is(Transaction.STATUS_SUCCESS)
        );
        Map<String,Object> consumerTransaction = filter.fetchOne();
        assertNotNull("consumer transaction", consumerTransaction);
        assertEquals(consumerTransaction, Transaction.TRADE_NO, consumerTransaction.get(Transaction.ORDER_SN));
    }
    
    /**
     * 
     * 支付宝2.0 线上预授权完成失败（明确失败），单独状态置为deposit_freeze
     * 
     * 期望结果：返回CONSUMER_ERROR，订单状态是DEPOSIT_FREEZED
     * 
     * @throws Exception
     */
    @Test
    public void test_prefreeze_consume_fail()throws Exception{
        // 预授权完成
        long payTime = System.currentTimeMillis();
        String authNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        long channelFinishTime = System.currentTimeMillis();
        // 冻结成功
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_FUND_AUTH_ORDER_APP_FREEZE.equals(method)) {
                    // 预授权冻结
                    return "{\n" + 
                            "    \"alipay_fund_auth_auth_order_app_freeze_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"order_str\": \"alipay_sdk=alipay-sdk-java-4.8.10.ALL&app_auth_token=201912BB71b9c5e20b61457db987c8b2fafc0X59&app_id=2015102000490218&biz_content=%7B%22amount%22%3A%2299.00%22%2C%22extra_param%22%3A%22%7B%5C%22category%5C%22%3A%5C%22RENT_SHARABLE_CHARGERS%5C%22%2C%5C%22serviceId%5C%22%3A%5C%222019112600000000000003035900%5C%22%7D%22%2C%22order_title%22%3A%22%E7%94%B5%E9%A5%B1%E9%A5%B1%E5%85%B1%E4%BA%AB%E5%85%85%E7%94%B5%E5%AE%9D%22%2C%22out_order_no%22%3A%227895237413026004%22%2C%22out_request_no%22%3A%227895237413026004%22%2C%22pay_timeout%22%3A%224m%22%2C%22product_code%22%3A%22PRE_AUTH_ONLINE%22%7D&charset=UTF-8&format=json&method=alipay.fund.auth.order.app.freeze&notify_url=http%3A%2F%2Fgateway.shouqianba.com%2Fupay%2Fv2%2Fnotify%2Falipaywap%2F59d968d0ec5aad56a81051170f9b114f%2Fe1f80%25267895237413026004%25262%25261580000002596741%252617032ac1-cc98-4a8f-83ec-42e69ad24018%25264%25261596508493700%25269900%25260%25263&sign=Irn5TH5uCMF6kC%2FyB3c9Kw2pM2kr3rbyoiReyvkXyxnbDA3%2FegHCNOtzOaQEXjr0bLdir%2FgZbE5x7a2kLT4YsGXBpzB43rtzkVLWvjy2Du3ID0j%2B5E9QzpveuLn2kIylep2cYjbEuJQf0rZa%2B2jQCBBveKShr7A1j5Oz9tXD%2FCM%3D&sign_type=RSA&timestamp=2020-08-04+10%3A34%3A53&version=1.0}}\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else if(AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_fund_auth_operation_detail_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"amount\": \"0.01\",\n" + 
                            "        \"auth_no\": \"" + authNo + "\",\n" + 
                            "        \"credit_amount\": \"0.01\",\n" + 
                            "        \"extra_param\": \"{}\",\n" + 
                            "        \"fund_amount\": \"0.00\",\n" + 
                            "        \"gmt_create\": \"2019-11-29 09:39:31\",\n" + 
                            "        \"gmt_trans\": \"" + DateUtil.formatDate(new Date(channelFinishTime/1000*1000), "yyyy-MM-dd HH:mm:ss") + "\",\n" + 
                            "        \"operation_id\": \"20191129974867636905\",\n" + 
                            "        \"operation_type\": \"FREEZE\",\n" + 
                            "        \"order_title\": \"mock\",\n" + 
                            "        \"out_order_no\": \"****************\",\n" + 
                            "        \"out_request_no\": \"****************\",\n" + 
                            "        \"payer_logon_id\": \"" + payerLogin + "\",\n" + 
                            "        \"payer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"pre_auth_type\": \"CREDIT_AUTH\",\n" + 
                            "        \"remark\": \"mock\",\n" + 
                            "        \"rest_amount\": \"0.01\",\n" + 
                            "        \"rest_credit_amount\": \"0.01\",\n" + 
                            "        \"rest_fund_amount\": \"0.00\",\n" + 
                            "        \"status\": \"SUCCESS\",\n" + 
                            "        \"total_freeze_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_credit_amount\": \"0.01\",\n" + 
                            "        \"total_freeze_fund_amount\": \"0.00\",\n" + 
                            "        \"total_pay_amount\": \"0.00\",\n" + 
                            "        \"total_pay_credit_amount\": \"0.00\",\n" + 
                            "        \"total_pay_fund_amount\": \"0.00\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"TDVR2LXsegA3xM7rfIasNspwXzUmBgCeDq0jDCfBsQ02lbuP+kf1ejf+qkmtQB4QSc34IvTkEDyZAObOW4TdOegT2Z49+1oFnHcfx5Wa5Yj+DHo7INtZnRWMlouJqOLeCTygGgNUYgbym3T2ujbONNPFUYymjIXgTgRbo1tcvNU=\"\n" + 
                            "}";
                }else {
                    Map bizContent = null;
                    if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof Map) {
                        bizContent = (Map)postRequest.get(ProtocolV2Fields.BIZ_CONTENT);
                    }else if(postRequest.get(ProtocolV2Fields.BIZ_CONTENT) instanceof String) {
                        bizContent = JsonUtil.jsonStrToObject(postRequest.get(ProtocolV2Fields.BIZ_CONTENT).toString(), Map.class);
                    }
                    List<String> errCodes = AlipayConstants.PAY_FAIL_ERR_CODE_LISTS.stream().collect(Collectors.toList());
                    String outTradeNo = MapUtil.getString(bizContent, BusinessV2Fields.OUT_TRADE_NO);
                    return "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"40004\",\n" + 
                            "        \"msg\": \"Business Failed\",\n" + 
                            "        \"sub_code\": \""+ errCodes.get(ThreadLocalRandom.current().nextInt(errCodes.size())) +"\",\n" + 
                            "        \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                            "        \"buyer_pay_amount\": \"0.01\",\n" + 
                            "        \"buyer_user_id\": \"" + payerUid + "\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"" + outTradeNo +"\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"KNyrfMQOtwI+An56lBpcJevgCSs/3SIum0MQhXl0lBgYILpOnsESVj9ik5XZsphlaHBmKXjGsyaQUgbWUdcn5uqz2GKdRiUn5dPJuZ2dMmtTVJTIGW+EtbWqsmtAu31iZ1wFHBnMJRDbTcszwuHS9B+BijoVnCGq7PLt1+nSFpc=\"\n" + 
                            "}";
                }
            }
        });

        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_ALIPAY, Order.SUB_PAYWAY_MINI, "2088602306097418");
        String resultString = postPerform("test_prefreeze_success", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        
        Thread.sleep(6100);
        request.put(DepositService.CONSUME_AMOUNT, "30");
        resultString = postPerform("test_prefreeze_success_fail", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "CONSUME_ERROR",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid,
                        "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.toString()
                );
    }
}
