package com.wosai.upay.controller.deposit.freeze.weixin;


import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.HmacSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class WeinxinFreezeTest extends BaseTestController {
    @Autowired
    DataRepository dataRepository;
    
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById("****weixin_cert_config_key****", null);
    }

    // 冻结成功
    @Test
    public void test_weixin_freeze_success() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payReturnXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1567976611]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[wMpyVamITLMITsYK]]></nonce_str>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <device_info><![CDATA[]]></device_info>\n" + 
                "    <openid><![CDATA[oyBevt7AI_fO-dA-NqBjkfIrjrkE]]></openid>\n" + 
                "    <sub_openid><![CDATA[]]></sub_openid>\n" + 
                "    <trade_type><![CDATA[DEPOSITPAY]]></trade_type>\n" + 
                "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                "    <bank_type><![CDATA[CMBC_DEBIT]]></bank_type>\n" + 
                "    <total_fee>100000</total_fee>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "    <settlement_total_fee>100000</settlement_total_fee>\n" + 
                "    <cash_fee>100000</cash_fee>\n" + 
                "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                "    <transaction_id><![CDATA[4200000597202008049147258399]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                "    <time_end><![CDATA[**************]]></time_end>\n" + 
                "    <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                "    <consume_fee>0</consume_fee>\n" + 
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(payReturnXml);
        String resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/freeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "DEPOSIT_FREEZE_SUCCESS",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.name()
        );
    }
    
    // 冻结异常，勾兑为成功
    @Test
    public void test_weixin_freeze_fix_success() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String freezeReturnXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <result_code><![CDATA[FAIL]]></result_code>\n" + 
                "    <err_code><![CDATA[LACK_PARAMS]]></err_code>\n" + 
                "    <err_code_des><![CDATA[缺少参数]]></err_code_des>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <nonce_str>0</nonce_str>\n" + 
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(freezeReturnXml);
        String resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/freeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "DEPOSIT_FREEZE_FAIL_ERROR",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_FAIL_PROTOCOL_1).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZE_ERROR.name()
        );
        
        freezeReturnXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1567976611]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[wMpyVamITLMITsYK]]></nonce_str>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <device_info><![CDATA[]]></device_info>\n" + 
                "    <openid><![CDATA[oyBevt7AI_fO-dA-NqBjkfIrjrkE]]></openid>\n" + 
                "    <sub_openid><![CDATA[]]></sub_openid>\n" + 
                "    <trade_type><![CDATA[DEPOSITPAY]]></trade_type>\n" + 
                "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                "    <bank_type><![CDATA[CMBC_DEBIT]]></bank_type>\n" + 
                "    <total_fee>100000</total_fee>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "    <settlement_total_fee>100000</settlement_total_fee>\n" + 
                "    <cash_fee>100000</cash_fee>\n" + 
                "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                "    <transaction_id><![CDATA[4200000597202008049147258399]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                "    <time_end><![CDATA[**************]]></time_end>\n" + 
                "    <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                "    <consume_fee>0</consume_fee>\n" + 
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(freezeReturnXml);
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        request.put("sn", orderSn);
        resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/fix", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.name()
        );
    }

    // 冻结异常，勾兑为失败
    @Test
    public void test_weixin_freeze_recon() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String freezeReturnXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <result_code><![CDATA[FAIL]]></result_code>\n" + 
                "    <err_code><![CDATA[LACK_PARAMS]]></err_code>\n" + 
                "    <err_code_des><![CDATA[缺少参数]]></err_code_des>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <nonce_str>0</nonce_str>\n" + 
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(freezeReturnXml);
        String resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/freeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "DEPOSIT_FREEZE_FAIL_ERROR",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_FAIL_PROTOCOL_1).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZE_ERROR.name()
        );
        
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(freezeReturnXml);
        // 勾兑失败，订单未超过1分钟有效期
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        request.put("sn", orderSn);
        resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/reconcileRevoke", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "UPAY_CANCEL_INVALID_ORDER_STATE",
                "biz_response.error_code_standard", "EP48"
        );
        
        // 修改数据库中的ctime，解决无法勾兑的问题
        Map<String, Object> orderUpdate = CollectionUtil.hashMap("id", "o"+ orderSn,
                    "ctime", System.currentTimeMillis() - 1 * 63 * 1000
                );
        dataRepository.getOrderDao().updatePart(orderUpdate);
        resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/reconcileRevoke", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZE_CANCELED.name()
        );
    }
    
    // 完成成功
    @Test
    public void test_weixin_consume_success() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String freezeResponseXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1567976611]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[wMpyVamITLMITsYK]]></nonce_str>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <device_info><![CDATA[]]></device_info>\n" + 
                "    <openid><![CDATA[oyBevt7AI_fO-dA-NqBjkfIrjrkE]]></openid>\n" + 
                "    <sub_openid><![CDATA[]]></sub_openid>\n" + 
                "    <trade_type><![CDATA[DEPOSITPAY]]></trade_type>\n" + 
                "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                "    <bank_type><![CDATA[CMBC_DEBIT]]></bank_type>\n" + 
                "    <total_fee>100000</total_fee>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "    <settlement_total_fee>100000</settlement_total_fee>\n" + 
                "    <cash_fee>100000</cash_fee>\n" + 
                "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                "    <transaction_id><![CDATA[4200000597202008049147258399]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                "    <time_end><![CDATA[**************]]></time_end>\n" + 
                "    <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                "    <consume_fee>0</consume_fee>\n" + 
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(freezeResponseXml);
        String resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/freeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "DEPOSIT_FREEZE_SUCCESS",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.name()
        );
        
        String consumeResponseXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1549895321]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[F6qcbadGSlw7nu19]]></nonce_str>\n" + 
                "    <sign><![CDATA[DB239A0CA27110AE86CBF68DB53387F29786DAB49B4CCE7249AD8C8DFECB5447]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <transaction_id><![CDATA[4200000583202008044838499822]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[7895237471680525]]></out_trade_no>\n" + 
                "    <total_fee>10000</total_fee>\n" + 
                "    <consume_fee>10000</consume_fee>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "</xml>";
        request.put("consume_amount", "1");
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(consumeResponseXml);
        resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "CONSUME_SUCCESS",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_CONSUMED.name()
        );
    }
    
    // 完成失败，订单状态修改为冻结
    @Test
    public void test_weixin_freeze_fail() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HmacSignature.class);
        PowerMockito.when(HmacSignature.sign(Mockito.any(), Mockito.any())).thenReturn("pay_sign");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String freezeResponseXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1567976611]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[wMpyVamITLMITsYK]]></nonce_str>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <device_info><![CDATA[]]></device_info>\n" + 
                "    <openid><![CDATA[oyBevt7AI_fO-dA-NqBjkfIrjrkE]]></openid>\n" + 
                "    <sub_openid><![CDATA[]]></sub_openid>\n" + 
                "    <trade_type><![CDATA[DEPOSITPAY]]></trade_type>\n" + 
                "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                "    <bank_type><![CDATA[CMBC_DEBIT]]></bank_type>\n" + 
                "    <total_fee>100000</total_fee>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "    <settlement_total_fee>100000</settlement_total_fee>\n" + 
                "    <cash_fee>100000</cash_fee>\n" + 
                "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                "    <transaction_id><![CDATA[4200000597202008049147258399]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                "    <time_end><![CDATA[**************]]></time_end>\n" + 
                "    <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                "    <consume_fee>0</consume_fee>\n" + 
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(freezeResponseXml);
        String resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/freeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "DEPOSIT_FREEZE_SUCCESS",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.name()
        );
        
        List<String> failList = WeixinConstants.DEPOSIT_CONSUMER_FAIL_LIST.stream().collect(Collectors.toList());
        String consumeResponseXml = "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <result_code><![CDATA[FAIL]]></result_code>\n" + 
                "    <err_code><![CDATA["+failList.get(ThreadLocalRandom.current().nextInt(failList.size()))+"]]></err_code>\n" + 
                "    <err_code_des><![CDATA[缺少参数]]></err_code_des>\n" + 
                "    <sign><![CDATA[CD0565819F4DAC7FAC58D38E28E800FD3F388850D98B38376AE9AB0117ADC910]]></sign>\n" + 
                "    <nonce_str>0</nonce_str>\n" + 
                "</xml>";
        request.put("consume_amount", "1");
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(consumeResponseXml);
        resultString = postPerform("test_weixin_freeze_success", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "CONSUME_ERROR",
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_CONSUME_ERROR).getName(),
                "biz_response.data.order_status", Order.Status.DEPOSIT_FREEZED.name()
        );
    }
}
