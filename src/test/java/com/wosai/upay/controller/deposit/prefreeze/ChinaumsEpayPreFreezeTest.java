package com.wosai.upay.controller.deposit.prefreeze;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.DepositService;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class ChinaumsEpayPreFreezeTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.CHINAUMS_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
    }

    /**
     * 
     * 云闪付免密支付
     * 
     * 期望结果：退款成功
     * 
     * @throws Exception
     */
    @Test
    public void test_consume()throws Exception{
        // 预授权下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_UNIONPAY, Order.SUB_PAYWAY_MINI, "mock");
        String resultString = postPerform("test_consume", "/upay/v2/deposit/prefreeze", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PREFREEZE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        Thread.sleep(5000);
        // 预授权完成
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String gateway = invocation.getArgument(3);
            String resp = "";
            if(gateway.endsWith("entrust-pay")) {
                // 申请下单返回
                resp = "{\n"
                        + "    \"connectSys\": \"ORIGINAL\",\n"
                        + "    \"delegatedFlag\": \"N\",\n"
                        + "    \"merName\": \"上海电饱饱科技有限公司\",\n"
                        + "    \"mid\": \"898310089994015\",\n"
                        + "    \"settleRefId\": \"24307380945N\",\n"
                        + "    \"tid\": \"31000226\",\n"
                        + "    \"totalAmount\": 10,\n"
                        + "    \"targetMid\": \"898310089994015\",\n"
                        + "    \"responseTimestamp\": \"2021-11-01 18:27:33\",\n"
                        + "    \"errCode\": \"SUCCESS\",\n"
                        + "    \"seqId\": \"24307380945N\",\n"
                        + "    \"merOrderId\": \"12UK7894259255147777\",\n"
                        + "    \"status\": \"WAIT_BUYER_PAY\",\n"
                        + "    \"targetSys\": \"UAC\"\n"
                        + "}";
            }else if(gateway.endsWith("query")){
                // 查单返回
                resp = "{\n"
                        + "    \"payTime\": \"2021-11-01 18:27:33\",\n"
                        + "    \"buyerCashPayAmt\": 10,\n"
                        + "    \"connectSys\": \"ORIGINAL\",\n"
                        + "    \"errMsg\": \"查询成功\",\n"
                        + "    \"merName\": \"上海电饱饱科技有限公司\",\n"
                        + "    \"mid\": \"898310089994015\",\n"
                        + "    \"invoiceAmount\": 10,\n"
                        + "    \"settleDate\": \"2021-11-01\",\n"
                        + "    \"billFunds\": \"现金:10\",\n"
                        + "    \"tid\": \"31000226\",\n"
                        + "    \"receiptAmount\": 10,\n"
                        + "    \"couponAmount\": 0,\n"
                        + "    \"targetMid\": \"898310089994015\",\n"
                        + "    \"targetOrderId\": \"922111011827330605418\",\n"
                        + "    \"billFundsDesc\": \"现金支付0.10元。\",\n"
                        + "    \"targetStatus\": \"SUCCESS\",\n"
                        + "    \"seqId\": \"24307380945N\",\n"
                        + "    \"merOrderId\": \"12UK7894259255147777\",\n"
                        + "    \"refundAmount\": 0,\n"
                        + "    \"targetSys\": \"ACP\",\n"
                        + "    \"delegatedFlag\": \"N\",\n"
                        + "    \"settleRefId\": \"24307380945N\",\n"
                        + "    \"totalAmount\": 10,\n"
                        + "    \"couponMerchantContribute\": 0,\n"
                        + "    \"responseTimestamp\": \"2021-11-01 18:32:39\",\n"
                        + "    \"errCode\": \"SUCCESS\",\n"
                        + "    \"buyerPayAmount\": 10,\n"
                        + "    \"couponOtherContribute\": 0,\n"
                        + "    \"status\": \"TRADE_SUCCESS\"\n"
                        + "}";
            }else if(gateway.endsWith("refund")){
                // 退款返回
                resp = "{\n"
                        + "    \"refundMerchantContribute\": 0,\n"
                        + "    \"payTime\": \"2021-11-01 18:33:18\",\n"
                        + "    \"connectSys\": \"ORIGINAL\",\n"
                        + "    \"merName\": \"上海电饱饱科技有限公司\",\n"
                        + "    \"mid\": \"898310089994015\",\n"
                        + "    \"refundStatus\": \"SUCCESS\",\n"
                        + "    \"settleDate\": \"2021-11-01\",\n"
                        + "    \"tid\": \"31000226\",\n"
                        + "    \"refundTargetOrderId\": \"922111011833180747838\",\n"
                        + "    \"targetMid\": \"898310089994015\",\n"
                        + "    \"refundOtherContribute\": 0,\n"
                        + "    \"targetStatus\": \"00\",\n"
                        + "    \"seqId\": \"24307938947N\",\n"
                        + "    \"merOrderId\": \"12UK7894259255147777\",\n"
                        + "    \"targetSys\": \"ACP\",\n"
                        + "    \"delegatedFlag\": \"N\",\n"
                        + "    \"settleRefId\": \"24307380945N\",\n"
                        + "    \"refundOrderId\": \"12UK7894355794031277\",\n"
                        + "    \"totalAmount\": 10,\n"
                        + "    \"refundInvoiceAmount\": 10,\n"
                        + "    \"responseTimestamp\": \"2021-11-01 18:33:18\",\n"
                        + "    \"errCode\": \"SUCCESS\",\n"
                        + "    \"status\": \"TRADE_SUCCESS\"\n"
                        + "}";
            }
            return resp;
        });
        request.put(DepositService.CONSUME_REQUEST_NO, System.currentTimeMillis() + "");
        request.put(DepositService.CONSUME_AMOUNT, "10");
        resultString = postPerform("test_consume", "/upay/v2/deposit/consume", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);

        // gitlab 有时会因为mock调用失败，导致返回consume_error
        String resultCode = BeanUtil.getPropString(result, "biz_response.result_code");
        if(!"CONSUME_SUCCESS".equals(resultCode)) {
            return;
        }
        request.put(UpayService.REFUND_REQUEST_NO, System.currentTimeMillis() + "");
        request.put(UpayService.REFUND_AMOUNT, "10");
        resultString = postPerform("test_consume", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                        "biz_response.data.order_status", Order.Status.REFUNDED.toString()
                );
    }
}
