package com.wosai.upay.controller.provider;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Map;

import org.junit.Test;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.dao.Order;

public class ProviderMangerControllerTest  extends BaseTestController{
    /**
     * 
     * 正常调用
     * 
     * @throws Exception
     */
    @Test
    public void test_status() throws Exception {
        mockMvc.perform(get("/upay/provider/status"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8_VALUE))
            .andDo(print());
    }
    
    /**
     * 
     * 修改通道限流
     * 
     * @throws Exception
     */
    @Test
    public void test_update() throws Exception {
        String changeKey = "0-1";
        String changeMsg = "fail";
        JSONObject request = new JSONObject();
        request.put(changeKey, CollectionUtil.hashMap("message", changeMsg, "percentage", 80));
        
        String resultString = postPerform("test_update:update", "/upay/provider/update", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStringToObject(resultString, Map.class);
        assertEquals(result, changeKey + ".message", changeMsg);
    }
    
}
