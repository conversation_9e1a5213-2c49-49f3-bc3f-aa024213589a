package com.wosai.upay.controller.upay.pay.weixin;

import java.util.HashMap;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class WeixinHKV3PayTest extends BaseTestController{

    @Before
    public void init() {
        Map basicParams = new HashMap(SupportUtil.buildBasicParams());
        basicParams.put("merchant_country", "HK");
        basicParams.put("currency", "HKD");
        mockGetBasicPrams(basicParams);
        basicParams.putAll(SupportUtil.WEIXIN_HK_V3_TRADE_PARAMS);

        mockGetAllPrams(basicParams);
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById("****weixin_cert_config_key****", SupportUtil.RSA2_PRIVATE_KEY);
    }

    @Test
    public void test_weixin_barcode_pay_success() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        request.put(UpayService.TOTAL_AMOUNT, "10000");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payResult = "{\n"
                + "    \"id\": \"4200001683202302026723115852\",\n"
                + "    \"sp_appid\": \"wx42f6886cbbb3fdbc\",\n"
                + "    \"sub_appid\": \"\",\n"
                + "    \"sp_mchid\": \"136338099\",\n"
                + "    \"sub_mchid\": \"1518449491\",\n"
                + "    \"out_trade_no\": \"7895039260882112\",\n"
                + "    \"payer\": {\n"
                + "        \"sp_openid\": \"oyBevtxIhgvplUoDdbHgrJUbpw_E\",\n"
                + "        \"sub_openid\": \"\"\n"
                + "    },\n"
                + "    \"amount\": {\n"
                + "        \"total\": 10000,\n"
                + "        \"currency\": \"HKD\",\n"
                + "        \"payer_total\": 10000,\n"
                + "        \"payer_currency\": \"HKD\",\n"
                + "        \"exchange_rate\": {\n"
                + "            \"type\": \"SETTLEMENT_RATE\",\n"
                + "            \"rate\": *********\n"
                + "        }\n"
                + "    },\n"
                + "    \"trade_type\": \"MICROPAY\",\n"
                + "    \"trade_state\": \"SUCCESS\",\n"
                + "    \"trade_state_desc\": \"支付成功\",\n"
                + "    \"bank_type\": \"CCB_DEBIT\",\n"
                + "    \"success_time\": \"2023-02-02T18:37:55+08:00\",\n"
                + "    \"http_code\": 200\n"
                + "}";

        PowerMockito.when(HttpClientUtils.doWechatV3Post(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyObject(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(JsonUtil.jsonStringToObject(payResult, Map.class));
        String resultString = postPerform("test_weixin_barcode_pay_success", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PAID.name(),
                "biz_response.data.payer_currency", "HKD",
                "biz_response.data.exchange_rate", "*********",
                "biz_response.data.payer_amount", "10000"
        );
        String sn = (String) BeanUtil.getNestedProperty(result, "biz_response.data.sn");
        Map<String, Object> transaction = dataRepository.getTransactionDao().filter(Criteria.where(Transaction.ORDER_SN).is(sn)).fetchOne();
        assert transaction != null;
        Map<String, Object> tradeParams = MapUtil.getMap(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.WEIXIN_TRADE_PARAMS);
        assert "1.3".equals(MapUtil.getString(tradeParams, TransactionParam.FEE_RATE));
        assert "130".equals(MapUtil.getString(tradeParams, TransactionParam.FEE));
    }

    @Test
    public void test_weixin_barcode_pay_with_query() throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payResult = "{\n"
                + "    \"out_trade_no\": \"7895039264228996\",\n"
                + "    \"payer\": {\n"
                + "        \"sp_openid\": \"\",\n"
                + "        \"sub_openid\": \"\"\n"
                + "    },\n"
                + "    \"sp_appid\": \"\",\n"
                + "    \"sp_mchid\": \"136338099\",\n"
                + "    \"sub_appid\": \"\",\n"
                + "    \"sub_mchid\": \"103906313\",\n"
                + "    \"trade_state\": \"USERPAYING\",\n"
                + "    \"trade_state_desc\": \"需要用户输入支付密码\",\n"
                + "    \"http_code\": 200\n"
                + "}";

        PowerMockito.when(HttpClientUtils.doWechatV3Post(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyObject(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(JsonUtil.jsonStringToObject(payResult, Map.class));

        String queryResult = "{\n"
                + "    \"id\": \"4200001683202302026723115852\",\n"
                + "    \"sp_appid\": \"wx42f6886cbbb3fdbc\",\n"
                + "    \"sub_appid\": \"\",\n"
                + "    \"sp_mchid\": \"136338099\",\n"
                + "    \"sub_mchid\": \"1518449491\",\n"
                + "    \"out_trade_no\": \"7895039260882112\",\n"
                + "    \"payer\": {\n"
                + "        \"sp_openid\": \"oyBevtxIhgvplUoDdbHgrJUbpw_E\",\n"
                + "        \"sub_openid\": \"\"\n"
                + "    },\n"
                + "    \"amount\": {\n"
                + "        \"total\": 30,\n"
                + "        \"currency\": \"HKD\",\n"
                + "        \"payer_total\": 25,\n"
                + "        \"payer_currency\": \"CNY\",\n"
                + "        \"exchange_rate\": {\n"
                + "            \"type\": \"SETTLEMENT_RATE\",\n"
                + "            \"rate\": *********\n"
                + "        }\n"
                + "    },\n"
                + "    \"trade_type\": \"MICROPAY\",\n"
                + "    \"trade_state\": \"SUCCESS\",\n"
                + "    \"trade_state_desc\": \"支付成功\",\n"
                + "    \"bank_type\": \"CCB_DEBIT\",\n"
                + "    \"success_time\": \"2023-02-02T18:37:55+08:00\",\n"
                + "    \"http_code\": 200\n"
                + "}";
        PowerMockito.when(HttpClientUtils.doWechatV3Get(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyObject(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(JsonUtil.jsonStringToObject(queryResult, Map.class));

        String resultString = postPerform("test_weixin_barcode_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_IN_PROG).getName(),
                "biz_response.data.order_status", Order.Status.CREATED.name()
        );
        Thread.sleep(2000);
        resultString = postPerform("test_weixin_barcode_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PAID.name(),
                "biz_response.data.payer_currency", "CNY",
                "biz_response.data.exchange_rate", "*********",
                "biz_response.data.payer_amount", "25"
        );
    }

    @Test
    public void test_weixin_refund() throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doWechatV3Post(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyObject(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String url = invocation.getArgument(3);
            if (url.endsWith("/hk/v3/transactions/micropay")) {
                return JsonUtil.jsonStrToObject("{\n"
                        + "    \"id\": \"4200001683202302026723115852\",\n"
                        + "    \"sp_appid\": \"wx42f6886cbbb3fdbc\",\n"
                        + "    \"sub_appid\": \"\",\n"
                        + "    \"sp_mchid\": \"136338099\",\n"
                        + "    \"sub_mchid\": \"1518449491\",\n"
                        + "    \"out_trade_no\": \"7895039260882112\",\n"
                        + "    \"payer\": {\n"
                        + "        \"sp_openid\": \"oyBevtxIhgvplUoDdbHgrJUbpw_E\",\n"
                        + "        \"sub_openid\": \"\"\n"
                        + "    },\n"
                        + "    \"amount\": {\n"
                        + "        \"total\": 30,\n"
                        + "        \"currency\": \"HKD\",\n"
                        + "        \"payer_total\": 25,\n"
                        + "        \"payer_currency\": \"CNY\",\n"
                        + "        \"exchange_rate\": {\n"
                        + "            \"type\": \"SETTLEMENT_RATE\",\n"
                        + "            \"rate\": *********\n"
                        + "        }\n"
                        + "    },\n"
                        + "    \"trade_type\": \"MICROPAY\",\n"
                        + "    \"trade_state\": \"SUCCESS\",\n"
                        + "    \"trade_state_desc\": \"支付成功\",\n"
                        + "    \"bank_type\": \"CCB_DEBIT\",\n"
                        + "    \"success_time\": \"2023-02-02T18:37:55+08:00\",\n"
                        + "    \"http_code\": 200\n"
                        + "}", Map.class);
            } else if (url.endsWith("/hk/v3/refunds")) {
                return JsonUtil.jsonStrToObject("{\n"
                        + "    \"id\": \"50201404762023020230219780956\",\n"
                        + "    \"out_refund_no\": \"****************\",\n"
                        + "    \"create_time\": \"2023-02-02T18:13:43+08:00\",\n"
                        + "    \"amount\": {\n"
                        + "        \"refund\": 20,\n"
                        + "        \"currency\": \"HKD\",\n"
                        + "        \"payer_refund\": 17,\n"
                        + "        \"payer_currency\": \"CNY\",\n"
                        + "        \"exchange_rate\": {\n"
                        + "            \"type\": \"SETTLEMENT_RATE\",\n"
                        + "            \"rate\": 85711240\n"
                        + "        }\n"
                        + "    },\n"
                        + "    \"http_code\": 200\n"
                        + "}", Map.class);
            }
            return "FAIL";
        });

        String resultString = postPerform("test_weixin_refund", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PAID.name(),
                "biz_response.data.payer_currency", "CNY",
                "biz_response.data.exchange_rate", "*********",
                "biz_response.data.payer_amount", "25"
        );

        request.put(UpayService.REFUND_AMOUNT, "20");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_weixin_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.name(),
                "biz_response.data.payer_currency", "CNY",
                "biz_response.data.exchange_rate", "85711240",
                "biz_response.data.payer_amount", "17"

        );

        resultString = postPerform("test_weixin_refund", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.name(),
                "biz_response.data.payer_currency", "CNY",
                "biz_response.data.exchange_rate", "85711240",
                "biz_response.data.payer_amount", "17"
        );
    }
}
