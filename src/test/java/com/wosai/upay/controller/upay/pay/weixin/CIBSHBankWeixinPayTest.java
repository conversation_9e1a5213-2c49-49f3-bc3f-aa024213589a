package com.wosai.upay.controller.upay.pay.weixin;

import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.cibbank.CIBBankConstants;
import com.wosai.mpay.api.cibbank.ProtocolFields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.XmlUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class CIBSHBankWeixinPayTest extends BaseTestController{
    
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.CIBSHBANK_TRADE_PARAMS));
    }
    
    @Test
    public void test_weixin_barcode() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestStr = invocation.getArgument(5);
                Map<String, Object> postRequest = XmlUtils.parse(postRequestStr);
                String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
                if(CIBBankConstants.SERVICE_UNIFIED_TRADE_MICROPAY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[wx0781c1dea664cd9a]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=滁州华巨百姓缘大药房连锁股份有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" + 
                            "  <cash_fee><![CDATA[30]]></cash_fee>\n" + 
                            "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                            "  <device_info><![CDATA[100009620010183463]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <gmt_payment><![CDATA[**************]]></gmt_payment>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <mdiscount><![CDATA[0]]></mdiscount>\n" + 
                            "  <nonce_str><![CDATA[367500405992040336]]></nonce_str>\n" + 
                            "  <openid><![CDATA[o-Rj7wHG0ojJCS2WI6zQ9vfkOAPA]]></openid>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[4200000682202008256775490017]]></out_transaction_id>\n" + 
                            "  <pay_result><![CDATA[0]]></pay_result>\n" + 
                            "  <pay_scene><![CDATA[micropay]]></pay_scene>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <settlement_total_fee><![CDATA[30]]></settlement_total_fee>\n" + 
                            "  <sign><![CDATA[FB7074DC0DBF9489ABA1B46CEAD2ACE6]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <time_end><![CDATA[**************]]></time_end>\n" + 
                            "  <total_fee><![CDATA[30]]></total_fee>\n" + 
                            "  <trade_type><![CDATA[pay.weixin.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008256689681603]]></transaction_id>\n" + 
                            "  <uuid><![CDATA[6103f463d96c013ae039f0f66c4a05278]]></uuid>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_weixin_barcode_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        AtomicBoolean isQuery = new AtomicBoolean(false);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestStr = invocation.getArgument(5);
                Map<String, Object> postRequest = XmlUtils.parse(postRequestStr);
                String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
                if(CIBBankConstants.SERVICE_UNIFIED_TRADE_MICROPAY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[wx0781c1dea664cd9a]]></appid>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <code><![CDATA[04062_1211037]]></code>\n" + 
                            "  <err_code><![CDATA[USERPAYING]]></err_code>\n" + 
                            "  <err_msg><![CDATA[等待用户输入支付密码]]></err_msg>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <need_query><![CDATA[Y]]></need_query>\n" + 
                            "  <nonce_str><![CDATA[-4212910072226532592]]></nonce_str>\n" + 
                            "  <result_code><![CDATA[1]]></result_code>\n" + 
                            "  <sign><![CDATA[DD8C1AF8222B422AAE50550946857E1F]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <transaction_id><![CDATA[************202008253625815673]]></transaction_id>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }else if(CIBBankConstants.SERVICE_UNIFIED_TRADE_QUERY.equals(method)) {
                    isQuery.set(true);
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[wx0781c1dea664cd9a]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=河南同和堂医药有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <bank_type><![CDATA[CCB_DEBIT]]></bank_type>\n" + 
                            "  <cash_fee><![CDATA[30]]></cash_fee>\n" + 
                            "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                            "  <device_info><![CDATA[100009620010639538]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <gmt_payment><![CDATA[**************]]></gmt_payment>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <mdiscount><![CDATA[0]]></mdiscount>\n" + 
                            "  <nonce_str><![CDATA[702289042258823411]]></nonce_str>\n" + 
                            "  <openid><![CDATA[o-Rj7wFIPIHYAROqx8oGEU_d9RR0]]></openid>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[4200000694202008255160256944]]></out_transaction_id>\n" + 
                            "  <pay_scene><![CDATA[micropay]]></pay_scene>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[D10E005BAC045CCB85076BF6BF7BA045]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <time_end><![CDATA[**************]]></time_end>\n" + 
                            "  <total_fee><![CDATA[30]]></total_fee>\n" + 
                            "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                            "  <trade_type><![CDATA[pay.weixin.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008253625815673]]></transaction_id>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_weixin_barcode_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        while(true) {
            if(isQuery.get()) {
                Thread.sleep(1000);
                break;
            }else {
                Thread.sleep(100);
            }
        }
        resultString = postPerform("test_weixin_barcode_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    @Test
    public void test_weixin_barcode_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestStr = invocation.getArgument(5);
                Map<String, Object> postRequest = XmlUtils.parse(postRequestStr);
                String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
                if(CIBBankConstants.SERVICE_UNIFIED_TRADE_MICROPAY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[wx0781c1dea664cd9a]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=滁州华巨百姓缘大药房连锁股份有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" + 
                            "  <cash_fee><![CDATA[30]]></cash_fee>\n" + 
                            "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                            "  <device_info><![CDATA[100009620010183463]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <gmt_payment><![CDATA[**************]]></gmt_payment>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <mdiscount><![CDATA[0]]></mdiscount>\n" + 
                            "  <nonce_str><![CDATA[367500405992040336]]></nonce_str>\n" + 
                            "  <openid><![CDATA[o-Rj7wHG0ojJCS2WI6zQ9vfkOAPA]]></openid>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[4200000682202008256775490017]]></out_transaction_id>\n" + 
                            "  <pay_result><![CDATA[0]]></pay_result>\n" + 
                            "  <pay_scene><![CDATA[micropay]]></pay_scene>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <settlement_total_fee><![CDATA[30]]></settlement_total_fee>\n" + 
                            "  <sign><![CDATA[FB7074DC0DBF9489ABA1B46CEAD2ACE6]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <time_end><![CDATA[**************]]></time_end>\n" + 
                            "  <total_fee><![CDATA[30]]></total_fee>\n" + 
                            "  <trade_type><![CDATA[pay.weixin.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008256689681603]]></transaction_id>\n" + 
                            "  <uuid><![CDATA[6103f463d96c013ae039f0f66c4a05278]]></uuid>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }else if(CIBBankConstants.SERVICE_UNIFIED_TRADE_REFUND.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <nonce_str><![CDATA[1615545666363425353]]></nonce_str>\n" + 
                            "  <out_refund_no><![CDATA[****************]]></out_refund_no>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[2020082522001405670590517131]]></out_transaction_id>\n" + 
                            "  <refund_channel><![CDATA[ORIGINAL]]></refund_channel>\n" + 
                            "  <refund_fee><![CDATA[30]]></refund_fee>\n" + 
                            "  <refund_id><![CDATA[************202008253625683998]]></refund_id>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[B5AB74B098655FD2EF81D84D888F1AA0]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <trade_type><![CDATA[pay.weixin.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008255697400238]]></transaction_id>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_weixin_barcode_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_weixin_barcode_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_weixin_barcode_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestStr = invocation.getArgument(5);
                Map<String, Object> postRequest = XmlUtils.parse(postRequestStr);
                String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
                if(CIBBankConstants.SERVICE_UNIFIED_TRADE_MICROPAY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[wx0781c1dea664cd9a]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=滁州华巨百姓缘大药房连锁股份有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" + 
                            "  <cash_fee><![CDATA[30]]></cash_fee>\n" + 
                            "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                            "  <device_info><![CDATA[100009620010183463]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <gmt_payment><![CDATA[**************]]></gmt_payment>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <mdiscount><![CDATA[0]]></mdiscount>\n" + 
                            "  <nonce_str><![CDATA[367500405992040336]]></nonce_str>\n" + 
                            "  <openid><![CDATA[o-Rj7wHG0ojJCS2WI6zQ9vfkOAPA]]></openid>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[4200000682202008256775490017]]></out_transaction_id>\n" + 
                            "  <pay_result><![CDATA[0]]></pay_result>\n" + 
                            "  <pay_scene><![CDATA[micropay]]></pay_scene>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <settlement_total_fee><![CDATA[30]]></settlement_total_fee>\n" + 
                            "  <sign><![CDATA[FB7074DC0DBF9489ABA1B46CEAD2ACE6]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <time_end><![CDATA[**************]]></time_end>\n" + 
                            "  <total_fee><![CDATA[30]]></total_fee>\n" + 
                            "  <trade_type><![CDATA[pay.weixin.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008256689681603]]></transaction_id>\n" + 
                            "  <uuid><![CDATA[6103f463d96c013ae039f0f66c4a05278]]></uuid>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }else if(CIBBankConstants.SERVICE_UNIFIED_MICROPAY_REVERSE.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <nonce_str><![CDATA[1615545666363425353]]></nonce_str>\n" + 
                            "  <out_refund_no><![CDATA[****************]]></out_refund_no>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[2020082522001405670590517131]]></out_transaction_id>\n" + 
                            "  <refund_channel><![CDATA[ORIGINAL]]></refund_channel>\n" + 
                            "  <refund_fee><![CDATA[30]]></refund_fee>\n" + 
                            "  <refund_id><![CDATA[************202008253625683998]]></refund_id>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[B5AB74B098655FD2EF81D84D888F1AA0]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <trade_type><![CDATA[pay.weixin.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008255697400238]]></transaction_id>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_weixin_barcode_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        resultString = postPerform("test_weixin_barcode_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
