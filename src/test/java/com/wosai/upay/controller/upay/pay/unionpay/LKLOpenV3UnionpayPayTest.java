package com.wosai.upay.controller.upay.pay.unionpay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class LKLOpenV3UnionpayPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
    }

    @Test
    public void test_unionpay_barcode() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        request.put(UpayService.TOTAL_AMOUNT, "1000");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/api/v3/labs/trans/micropay")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"2024**********\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"need_query\": \"0\",\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"acc_trade_no\": \"**********;685452;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"900\",\n"
                            + "        \"acc_settle_amount\": \"1000\",\n"
                            + "        \"acc_mdiscount_amount\": \"0\",\n"
                            + "        \"acc_discount_amount\": \"100\",\n"
                            + "        \"acc_other_discount_amount\": \"\",\n"
                            + "        \"trade_time\": \"**************\",\n"
                            + "        \"bank_type\": \"UNKNOW\",\n"
                            + "        \"card_type\": \"\",\n"
                            + "        \"remark\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"user_id\": \"\",\n"
                            + "            \"store_id\": \"\",\n"
                            + "            \"alipay_store_id\": \"\",\n"
                            + "            \"open_id\": \"\",\n"
                            + "            \"acc_activity_id\": \"****************,\",\n"
                            + "            \"up_iss_addn_data\": \"\",\n"
                            + "            \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "            \"trade_info\": \"\",\n"
                            + "            \"promotion_detail\": \"\",\n"
                            + "            \"fund_bill_list\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_unionpay_barcode_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        request.put(UpayService.TOTAL_AMOUNT, "1000");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/api/v3/labs/trans/micropay")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"2024**********\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"need_query\": \"1\",\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"acc_trade_no\": \"**********;685452;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"900\",\n"
                            + "        \"acc_settle_amount\": \"1000\",\n"
                            + "        \"acc_mdiscount_amount\": \"0\",\n"
                            + "        \"acc_discount_amount\": \"100\",\n"
                            + "        \"acc_other_discount_amount\": \"\",\n"
                            + "        \"trade_time\": \"**************\",\n"
                            + "        \"bank_type\": \"UNKNOW\",\n"
                            + "        \"card_type\": \"\",\n"
                            + "        \"remark\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"user_id\": \"\",\n"
                            + "            \"store_id\": \"\",\n"
                            + "            \"alipay_store_id\": \"\",\n"
                            + "            \"open_id\": \"\",\n"
                            + "            \"acc_activity_id\": \"****************,\",\n"
                            + "            \"up_iss_addn_data\": \"\",\n"
                            + "            \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "            \"trade_info\": \"\",\n"
                            + "            \"promotion_detail\": \"\",\n"
                            + "            \"fund_bill_list\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                } else if(serviceUrl.contains("/api/v3/labs/query/tradequery")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"20240110160627\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"trade_main_type\": \"MICROPAY\",\n"
                            + "        \"split_attr\": \"\",\n"
                            + "        \"acc_trade_no\": \"**********;685452;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"settle_merchant_no\": \"\",\n"
                            + "        \"settle_term_no\": \"\",\n"
                            + "        \"trade_state\": \"SUCCESS\",\n"
                            + "        \"trade_state_desc\": \"交易成功\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"900\",\n"
                            + "        \"buyer_refund_amount\": \"\",\n"
                            + "        \"acc_settle_amount\": \"1000\",\n"
                            + "        \"acc_mdiscount_amount\": \"0\",\n"
                            + "        \"acc_discount_amount\": \"100\",\n"
                            + "        \"acc_other_discount_amount\": \"\",\n"
                            + "        \"trade_time\": \"**************\",\n"
                            + "        \"user_id1\": \"\",\n"
                            + "        \"user_id2\": \"\",\n"
                            + "        \"bank_type\": \"UNKNOW\",\n"
                            + "        \"card_type\": \"00\",\n"
                            + "        \"acc_activity_id\": \"****************,\",\n"
                            + "        \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "        \"trade_info\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"user_id\": \"\",\n"
                            + "            \"store_id\": \"\",\n"
                            + "            \"alipay_store_id\": \"\",\n"
                            + "            \"open_id\": \"\",\n"
                            + "            \"acc_activity_id\": \"\",\n"
                            + "            \"up_iss_addn_data\": \"\",\n"
                            + "            \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "            \"trade_info\": \"\",\n"
                            + "            \"promotion_detail\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_unionpay_barcode_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_unionpay_barcode_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }

    @Test
    public void test_unionpay_barcode_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        request.put(UpayService.TOTAL_AMOUNT, "1000");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/api/v3/labs/trans/micropay")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"2024**********\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"need_query\": \"0\",\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"acc_trade_no\": \"**********;685452;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"900\",\n"
                            + "        \"acc_settle_amount\": \"1000\",\n"
                            + "        \"acc_mdiscount_amount\": \"0\",\n"
                            + "        \"acc_discount_amount\": \"100\",\n"
                            + "        \"acc_other_discount_amount\": \"\",\n"
                            + "        \"trade_time\": \"**************\",\n"
                            + "        \"bank_type\": \"UNKNOW\",\n"
                            + "        \"card_type\": \"\",\n"
                            + "        \"remark\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"user_id\": \"\",\n"
                            + "            \"store_id\": \"\",\n"
                            + "            \"alipay_store_id\": \"\",\n"
                            + "            \"open_id\": \"\",\n"
                            + "            \"acc_activity_id\": \"****************,\",\n"
                            + "            \"up_iss_addn_data\": \"\",\n"
                            + "            \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "            \"trade_info\": \"\",\n"
                            + "            \"promotion_detail\": \"\",\n"
                            + "            \"fund_bill_list\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                } else if(serviceUrl.contains("/api/v3/labs/relation/refund")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"2024**********\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"acc_trade_no\": \"**********;693576;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"refund_amount\": \"500\",\n"
                            + "        \"payer_amount\": \"500\",\n"
                            + "        \"buyer_refund_amount\": \"\",\n"
                            + "        \"trade_time\": \"2024**********\",\n"
                            + "        \"origin_trade_no\": \"2024010966210311752160\",\n"
                            + "        \"origin_out_trade_no\": \"****************\",\n"
                            + "        \"up_iss_addn_data\": \"\",\n"
                            + "        \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"50\\\"}]\",\n"
                            + "        \"trade_info\": \"\"\n"
                            + "    }\n"
                            + "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );

        request.put(UpayService.REFUND_AMOUNT, "500");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_unionpay_barcode_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.name()
        );
    }
    
    @Test
    public void test_unionpay_barcode_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        request.put(UpayService.TOTAL_AMOUNT, "1000");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/api/v3/labs/trans/micropay")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"2024**********\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"need_query\": \"0\",\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"acc_trade_no\": \"**********;685452;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"900\",\n"
                            + "        \"acc_settle_amount\": \"1000\",\n"
                            + "        \"acc_mdiscount_amount\": \"0\",\n"
                            + "        \"acc_discount_amount\": \"100\",\n"
                            + "        \"acc_other_discount_amount\": \"\",\n"
                            + "        \"trade_time\": \"**************\",\n"
                            + "        \"bank_type\": \"UNKNOW\",\n"
                            + "        \"card_type\": \"\",\n"
                            + "        \"remark\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"user_id\": \"\",\n"
                            + "            \"store_id\": \"\",\n"
                            + "            \"alipay_store_id\": \"\",\n"
                            + "            \"open_id\": \"\",\n"
                            + "            \"acc_activity_id\": \"****************,\",\n"
                            + "            \"up_iss_addn_data\": \"\",\n"
                            + "            \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "            \"trade_info\": \"\",\n"
                            + "            \"promotion_detail\": \"\",\n"
                            + "            \"fund_bill_list\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                } else if(serviceUrl.contains("/api/v3/labs/relation/refund")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"2024**********\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"merchant_no\": \"***************\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"acc_trade_no\": \"**********;693576;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"refund_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"1000\",\n"
                            + "        \"buyer_refund_amount\": \"\",\n"
                            + "        \"trade_time\": \"2024**********\",\n"
                            + "        \"origin_trade_no\": \"2024010966210311752160\",\n"
                            + "        \"origin_out_trade_no\": \"****************\",\n"
                            + "        \"up_iss_addn_data\": \"\",\n"
                            + "        \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "        \"trade_info\": \"\"\n"
                            + "    }\n"
                            + "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_unionpay_barcode_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );

        resultString = postPerform("test_unionpay_barcode_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
    
    
}
