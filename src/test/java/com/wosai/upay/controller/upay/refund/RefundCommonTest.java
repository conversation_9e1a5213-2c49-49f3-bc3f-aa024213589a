package com.wosai.upay.controller.upay.refund;

import static org.junit.Assert.assertNotNull;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.FutureTask;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.NonSqbOrder;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.SupportUtil;

public class RefundCommonTest extends BaseTestController{
    
    @MockBean
    UpayOrderService upayOrderService;
    @MockBean
    GatewaySupportService gatewaySupportService;

    
    @Before
    public void init() {
        Mockito.reset(upayOrderService);
        PowerMockito.when(upayOrderService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V1_TRADE_PARAMS));
    }
    
    /**
     * 
     * 用例名称：跨服务商主体退款
     * 
     * 期望结果：退款成功
     * 
     * @throws Exception
     */
    @Test
    public void test_non_sqb_order_refund()throws Exception{
      PowerMockito.when(gatewaySupportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
      String clientSn = System.currentTimeMillis()+"";
      // 1、插入订单关联关系
      dataRepository.getNonSqbOrderDao().save(CollectionUtil.hashMap(DaoConstants.ID,UUID.randomUUID().toString(),
              NonSqbOrder.CLIENT_SN, clientSn, 
              NonSqbOrder.TRADE_NO, clientSn,
              NonSqbOrder.TERMINAL_SN, SupportUtil.TERMINAL_SN,
              NonSqbOrder.MERCHANT_ID, "0040a3b45204-5ceb-fdb4-c8d5-02c14674",
              NonSqbOrder.AMOUNT, 15000l,
              NonSqbOrder.FINISH_TIME, System.currentTimeMillis()
          ));

      long refundTime = System.currentTimeMillis();
      String tradeNo = clientSn + "T";
      // 2、跨服务商主体退款
      PowerMockito.mockStatic(HttpClientUtils.class);
      PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
              "<alipay>\n" + 
              "    <is_success>T</is_success>\n" + 
              "    <response>\n" + 
              "        <alipay>\n" + 
              "            <result_code>SUCCESS</result_code>\n" + 
              "            <buyer_logon_id>xxxbuyer_logon_idxxxx</buyer_logon_id>\n" + 
              "            <buyer_user_id>xxxbuyer_user_idxxxx</buyer_user_id>\n" + 
              "            <out_trade_no>tl</out_trade_no>\n" + 
              "            <fund_change>Y</fund_change>\n" + 
              "            <trade_no>" + tradeNo + "</trade_no>\n" + 
              "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", refundTime) + "</gmt_payment>\n" + 
              "        </alipay>\n" + 
              "    </response>\n" + 
              "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
              "    <sign_type>MD5</sign_type>\n" + 
              "</alipay>"
      );
      JSONObject refundRequest = SupportUtil.buildRefundRequest(clientSn);
      String resultString = postPerform("test_non_sqb_order_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.toString(),
                "biz_response.data.total_amount", "15000",
                "biz_response.data.net_amount", "5000"
                );
      
      //3、同一笔订单第二次退款
      refundRequest.put(UpayService.REFUND_REQUEST_NO, System.currentTimeMillis() + "");
      refundRequest.put(UpayService.REFUND_AMOUNT, "5000");
      resultString = postPerform("test_non_sqb_order_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
              "biz_response.data.order_status", Order.Status.REFUNDED.toString(),
              "biz_response.data.total_amount", "15000",
              "biz_response.data.net_amount", "0"
              );
    }
    
    /**
     * 
     * 用例名称：勾兑跨服务商主体退款交易为退款失败并再次退款
     * 
     * 期望结果：退款成功
     * 
     * @throws Exception
     */
    @Test
    public void test_non_sqb_order_refund_fix_fail()throws Exception{
      PowerMockito.when(gatewaySupportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
      String clientSn = System.currentTimeMillis()+"";
      // 1、插入订单关联关系
      dataRepository.getNonSqbOrderDao().save(CollectionUtil.hashMap(DaoConstants.ID,UUID.randomUUID().toString(),
              NonSqbOrder.CLIENT_SN, clientSn, 
              NonSqbOrder.TRADE_NO, clientSn,
              NonSqbOrder.TERMINAL_SN, SupportUtil.TERMINAL_SN,
              NonSqbOrder.MERCHANT_ID, "0040a3b45204-5ceb-fdb4-c8d5-02c14674",
              NonSqbOrder.AMOUNT, 15000l,
              NonSqbOrder.FINISH_TIME, System.currentTimeMillis()
          ));
      
      long refundTime = System.currentTimeMillis();
      String tradeNo = clientSn + "T";
      // 2、跨服务商主体退款
      PowerMockito.mockStatic(HttpClientUtils.class);
      PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
              "<alipay>\n" + 
              "    <is_success>T</is_success>\n" + 
              "    <response>\n" + 
              "        <alipay>\n" + 
              "            <result_code>FAIL</result_code>\n" + 
              "            <buyer_logon_id>xxxbuyer_logon_idxxxx</buyer_logon_id>\n" + 
              "            <buyer_user_id>xxxbuyer_user_idxxxx</buyer_user_id>\n" + 
              "            <out_trade_no>tl</out_trade_no>\n" + 
              "            <fund_change>Y</fund_change>\n" + 
              "            <trade_no>" + tradeNo + "</trade_no>\n" + 
              "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", refundTime) + "</gmt_payment>\n" + 
              "        </alipay>\n" + 
              "    </response>\n" + 
              "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
              "    <sign_type>MD5</sign_type>\n" + 
              "</alipay>"
      );
      JSONObject refundRequest = SupportUtil.buildRefundRequest(clientSn);
      String resultString = postPerform("test_non_sqb_order_refund_fix_fail_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_ERROR,
                "biz_response.data.order_status", Order.Status.REFUND_ERROR.toString()
                );
      String sn = (String) BeanUtil.getNestedProperty(result, "biz_response.data.sn");
      
      // 3、订单状态勾兑为支付成功
      refundRequest.put(UpayService.SN, sn);
      resultString = postPerform("test_non_sqb_order_refund_fix_fail", "/upay/v2/fixOrderStatusIfRefundNotSuccess", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", CommonResponse.SUCCESS,
              "biz_response.data.status", Order.STATUS_PAID,
              "biz_response.data.effective_total", 15000
              );
      
      // 4、再次退款
      PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
              "<alipay>\n" + 
              "    <is_success>T</is_success>\n" + 
              "    <response>\n" + 
              "        <alipay>\n" + 
              "            <result_code>SUCCESS</result_code>\n" + 
              "            <buyer_logon_id>xxxbuyer_logon_idxxxx</buyer_logon_id>\n" + 
              "            <buyer_user_id>xxxbuyer_user_idxxxx</buyer_user_id>\n" + 
              "            <out_trade_no>tl</out_trade_no>\n" + 
              "            <fund_change>Y</fund_change>\n" + 
              "            <trade_no>" + tradeNo + "</trade_no>\n" + 
              "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", refundTime) + "</gmt_payment>\n" + 
              "        </alipay>\n" + 
              "    </response>\n" + 
              "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
              "    <sign_type>MD5</sign_type>\n" + 
              "</alipay>"
      );
      
      refundRequest.put(UpayService.REFUND_REQUEST_NO, System.currentTimeMillis() + "");
      refundRequest.put(UpayService.REFUND_AMOUNT, "5000");
      resultString = postPerform("test_non_sqb_order_refund_fix_fail_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.toString(),
                "biz_response.data.total_amount", "15000",
                "biz_response.data.total_amount", "15000"
                );
    }
    
    /**
     * 
     * 用例名称：勾兑跨服务商主体退款交易为退款成功
     * 
     * 期望结果：退款成功
     * 
     * @throws Exception
     */
    @Test
    public void test_non_sqb_order_refund_fix()throws Exception{
      PowerMockito.when(gatewaySupportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
      String clientSn = System.currentTimeMillis()+"";
      // 1、插入订单关联关系
      dataRepository.getNonSqbOrderDao().save(CollectionUtil.hashMap(DaoConstants.ID,UUID.randomUUID().toString(),
              NonSqbOrder.CLIENT_SN, clientSn, 
              NonSqbOrder.TRADE_NO, clientSn,
              NonSqbOrder.TERMINAL_SN, SupportUtil.TERMINAL_SN,
              NonSqbOrder.MERCHANT_ID, "0040a3b45204-5ceb-fdb4-c8d5-02c14674",
              NonSqbOrder.AMOUNT, 15000l,
              NonSqbOrder.FINISH_TIME, System.currentTimeMillis()
          ));
      
      long refundTime = System.currentTimeMillis();
      String tradeNo = clientSn + "T";
      // 2、跨服务商主体退款
      PowerMockito.mockStatic(HttpClientUtils.class);
      PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
              "<alipay>\n" + 
              "    <is_success>T</is_success>\n" + 
              "    <response>\n" + 
              "        <alipay>\n" + 
              "            <result_code>FAIL</result_code>\n" + 
              "            <buyer_logon_id>xxxbuyer_logon_idxxxx</buyer_logon_id>\n" + 
              "            <buyer_user_id>xxxbuyer_user_idxxxx</buyer_user_id>\n" + 
              "            <out_trade_no>tl</out_trade_no>\n" + 
              "            <fund_change>Y</fund_change>\n" + 
              "            <trade_no>" + tradeNo + "</trade_no>\n" + 
              "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", refundTime) + "</gmt_payment>\n" + 
              "        </alipay>\n" + 
              "    </response>\n" + 
              "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
              "    <sign_type>MD5</sign_type>\n" + 
              "</alipay>"
      );
      JSONObject refundRequest = SupportUtil.buildRefundRequest(clientSn);
      String resultString = postPerform("test_non_sqb_order_refund_fix_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_ERROR,
                "biz_response.data.order_status", Order.Status.REFUND_ERROR.toString()
                );
      String sn = (String) BeanUtil.getNestedProperty(result, "biz_response.data.sn");
      Map<String, Object> order = dataRepository.getOrderByOrderSn(null, sn);
      assertNotNull("order is null", order);
      Map<String, Object> transaction = dataRepository.getLatestRefundTransactionByOrderSn((String)order.get(Order.MERCHANT_ID), sn);
      assertNotNull("refund transaction is null", transaction);
      String tsn = BeanUtil.getPropString(transaction, Transaction.TSN);
      
      // 3、订单状态勾兑为退款完成
      refundRequest.put(UpayService.SN, sn);
      refundRequest.put(UpayService.TSN, tsn);
      resultString = postPerform("test_non_sqb_order_refund_fix", "/upay/v2/fixCancelOrRefund", MediaType.APPLICATION_JSON_UTF8_VALUE, refundRequest.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", CommonResponse.SUCCESS,
              "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.toString(),
              "biz_response.data.total_amount", "15000",
              "biz_response.data.net_amount", "5000"
              );
    }
    
    /**
     * 
     * 用例名称：历史交易重复退款失败
     * 
     * 期望结果：重复退款失败
     * 
     * @throws Exception
     */
    @Test
    public void test_history_refund_duplicate()throws Exception{
        // 1、 支付成功
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <request>\n" + 
                "        <param name=\"extend_params\">{\"AGENT_ID\":\"10610050a1\"}</param>\n" + 
                "        <param name=\"_input_charset\">gbk</param>\n" + 
                "        <param name=\"subject\">" + request.getString(UpayService.SUBJECT) +"</param>\n" + 
                "        <param name=\"dynamic_id\">" + request.getString(UpayService.DYNAMIC_ID) + "</param>\n" + 
                "        <param name=\"sign\">727fa1993a14a054c321caa9c53c2892</param>\n" + 
                "        <param name=\"it_b_pay\">1m</param>\n" + 
                "        <param name=\"body\">"+ request.getString(UpayService.SUBJECT) +"</param>\n" + 
                "        <param name=\"notify_url\">http://gateway.shouqianba.com/upay/v2/notify/alipay/82cbc114c99133541602159aeb8d5ef7/af04f%267895238320587535%261%261580000000732951%266edceebc-3bfe-4404-ba98-015a6a996f7c%261%261570801600705%268850%260%260</param>\n" + 
                "        <param name=\"product_code\">BARCODE_PAY_OFFLINE</param>\n" + 
                "        <param name=\"dynamic_id_type\">bar_code</param>\n" + 
                "        <param name=\"out_trade_no\">7895238320587535</param>\n" + 
                "        <param name=\"partner\">2088121609449116</param>\n" + 
                "        <param name=\"service\">alipay.acquire.createandpay</param>\n" + 
                "        <param name=\"total_fee\">" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</param>\n" + 
                "        <param name=\"sign_type\">MD5</param>\n" + 
                "    </request>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                "            <fund_bill_list>\n" + 
                "                <TradeFundBill>\n" + 
                "                    <amount>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</amount>\n" + 
                "                    <force_use_cash>F</force_use_cash>\n" + 
                "                    <fund_channel>90</fund_channel>\n" + 
                "                </TradeFundBill>\n" + 
                "            </fund_bill_list>\n" + 
                "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                "            <out_trade_no>7895238320587535</out_trade_no>\n" + 
                "            <result_code>ORDER_SUCCESS_PAY_SUCCESS</result_code>\n" + 
                "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        String resultString = postPerform("test_history_refund_duplicate_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid
                );
        String sn = (String) BeanUtil.getNestedProperty(result, "biz_response.data.sn");
        String productFlag = BeanUtil.getPropString(dataRepository.getTransactionDao().filter(Criteria.where(Transaction.ORDER_SN).is(sn)).fetchOne(), Transaction.PRODUCT_FLAG);
        Assert.assertEquals(true, productFlag.contains(ProductFlagEnum.DBB.getCode()));

        //2、删除数据，模拟mock返回
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        assertNotNull(order);
        assertNotNull(transaction);
        dataRepository.getOrderDao().delete(MapUtil.getString(order, DaoConstants.ID));
        dataRepository.getTransactionDao().delete(MapUtil.getString(transaction, DaoConstants.ID));
        
        // 设置mock
        PowerMockito.when(gatewaySupportService.getOrderBySn(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(order);
        PowerMockito.when(gatewaySupportService.getPayOrConsumerTransaction(Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(transaction);
        PowerMockito.when(upayOrderService.updateOrder(Mockito.anyMap())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) throws Throwable {
                Thread.sleep(1000);
                return true;
            }
        });
        long refundTime = System.currentTimeMillis();
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <result_code>SUCCESS</result_code>\n" + 
                "            <buyer_logon_id>xxxbuyer_logon_idxxxx</buyer_logon_id>\n" + 
                "            <buyer_user_id>xxxbuyer_user_idxxxx</buyer_user_id>\n" + 
                "            <out_trade_no>tl</out_trade_no>\n" + 
                "            <fund_change>Y</fund_change>\n" + 
                "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", refundTime) + "</gmt_payment>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>"
        );
        
        FutureTask<Map> task1 = new FutureTask<Map>(()-> {
            request.put("refund_request_no", (System.currentTimeMillis() + ThreadLocalRandom.current().nextInt(999)) + "");
            request.put("refund_amount", "1");
            String rs1 = postPerform("test_history_refund_duplicate_refund_1", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
            return JsonUtil.jsonStringToObject(rs1, Map.class);
        });
        
        FutureTask<Map> task2 = new FutureTask<Map>(()-> {
            request.put("refund_request_no", (System.currentTimeMillis() + ThreadLocalRandom.current().nextInt(999)) + "");
            request.put("refund_amount", "1");
            String rs1 = postPerform("test_history_refund_duplicate_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
            return JsonUtil.jsonStringToObject(rs1, Map.class);
        });
        
        new Thread(task1).start();
        new Thread(task2).start();
        
        while(!task1.isDone() || !task2.isDone()) {
            Thread.sleep(500);
        }
        Map successResult = task1.get();
        Map errorResult = task2.get();
        if(null != errorResult
                && null != errorResult.get("biz_response")
                && ((Map)errorResult.get("biz_response")).containsKey("data")) {
            Map tmp = successResult;
            successResult = errorResult;
            errorResult = tmp;
        }
        assertEquals(successResult, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.toString()
                );
        assertEquals(errorResult, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "UPAY_REFUND_ORDER_NOOP"
                );
    
        // 再次退款
        Thread.sleep(2000);
        request.put("refund_request_no", (System.currentTimeMillis() + ThreadLocalRandom.current().nextInt(999)) + "");
        request.put("refund_amount", "1");
        resultString = postPerform("test_history_refund_duplicate_refund_3", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.toString()
                );
    }

    /**
     * 喔噻优惠退款
     * 
     * 1、实付金额金额0.02，优惠金额0.01 优惠服务返回：{result_code=1, pay_amount=2, redeem_result=[{order_sn=7895052764835245, type=11, activity_sn=2600599459, source=3, amount=1}]}
     * 2、第一次退款0.02 优惠服务返回：{result_code=201, refund_amount=2, redeem_result=[{order_sn=7895052764835245, type=11, activity_sn=2600599459, source=3, amount=0}]}
     * 3、第二次退款0.01 优惠服务返回：{result_code=201, refund_amount=0, redeem_result=[{order_sn=7895052764835245, type=11, activity_sn=2600599459, source=3, amount=1}]} 
     * @throws Exception 
     */
    @Test
    public void refundWosaiPromotion() throws Exception {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UP_DIRECT_TRADE_PARAMS_ALIPAY));
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);

        // 1、 实付金额金额0.02，优惠金额0.01 优惠服务返回：{result_code=1, pay_amount=2, redeem_result=[{order_sn=7895052764835245, type=11, activity_sn=2600599459, source=3, amount=1}]}
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        request.put(UpayService.TOTAL_AMOUNT, "3");
        mockGetUpayResult(JsonUtil.jsonStrToObject("{\"result_code\":\"1\",\"pay_amount\":\"2\",\"redeem_result\":[{\"order_sn\":\"7895052764835245\",\"type\":\"11\",\"activity_sn\":\"2600599459\",\"source\":\"3\",\"amount\":\"1\"}]}", Map.class));
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"151******96\",\n" + 
                            "        \"buyer_pay_amount\": \"0.02\",\n" + 
                            "        \"buyer_user_id\": \"2088602024531869\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.02\",\n" + 
                            "            \"fund_channel\": \"PCREDIT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" + 
                            "        \"invoice_amount\": \"3.00\",\n" + 
                            "        \"out_trade_no\": \"7895237191687329\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.02\",\n" + 
                            "        \"total_amount\": \"0.02\",\n" + 
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("refundWosaiPromotion", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );

        // 2、第一次退款0.02 优惠服务返回：{result_code=201, refund_amount=2, redeem_result=[{order_sn=7895052764835245, type=11, activity_sn=2600599459, source=3, amount=0}]}
        request.put(UpayService.REFUND_AMOUNT, "2");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        mockGetUpayRefundResult(JsonUtil.jsonStrToObject("{\"result_code\":\"201\",\"refund_amount\":\"2\",\"redeem_result\":[{\"order_sn\":\"7895052764835245\",\"type\":\"11\",\"activity_sn\":\"2600599459\",\"source\":\"3\",\"amount\":\"0\"}]}", Map.class));
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_REFUND.equals(method)) {
                    return  "{\n"
                            + "    \"alipay_trade_refund_response\": {\n"
                            + "        \"msg\": \"Success\",\n"
                            + "        \"refund_detail_item_list\": [{\n"
                            + "            \"amount\": \"0.02\",\n"
                            + "            \"fund_channel\": \"PCREDIT\"\n"
                            + "        }],\n"
                            + "        \"code\": \"10000\",\n"
                            + "        \"cert_id\": \"4095468502\",\n"
                            + "        \"buyer_user_id\": \"2088042814104862\",\n"
                            + "        \"out_trade_no\": \"7895206547201318\",\n"
                            + "        \"refund_fee\": \"0.02\",\n"
                            + "        \"gmt_refund_pay\": \"2021-12-10 14:55:27\",\n"
                            + "        \"send_back_fee\": \"0.02\",\n"
                            + "        \"trade_no\": \"142021121022001404865722182256\",\n"
                            + "        \"buyer_logon_id\": \"183******59\",\n"
                            + "        \"sign_type\": \"RSA2\",\n"
                            + "        \"fund_change\": \"Y\"\n"
                            + "    },\n"
                            + "    \"sign\": \"hzXxK+3llYfkmUtpcQYXep2qSfV/QmZu8a+/jUWBghxzRqrKTvIoKahM468JQJS7RlB++GHs2cmhk/0a71ghmVZpuudBrIhEv6A8lljIBgfyeTYtAg28mTffzdHUdej5orhrsy/0j3NmiWT2i/6vkyLCx7wQddomiQs4ZUpGt4goXOvrjx9MFh+mKNMU92P9fJOKij7uzowfYdLOojE1iCEDcj9KMKtW5L3RZFtcv0L7aOqVlqzyEpN0g2TB2iVl2fuB5Aq4ZgUOpvbJAt5R1qQ0WWfqNE1NRSzq63MnZ+Qb+NYGXCr8cd0rwV9QwnRETjJCgCxt+nDponXAhAPlAg==\"\n"
                            + "}";
                }
                return "fail";
            }
        });
        resultString = postPerform("refundWosaiPromotion", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_REFUND_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.name()
                );
        // 3、第二次退款0.01 优惠服务返回：{result_code=201, refund_amount=0, redeem_result=[{order_sn=7895052764835245, type=11, activity_sn=2600599459, source=3, amount=1}]}
        request.put(UpayService.REFUND_AMOUNT, "1");
        request.put(UpayService.REFUND_REQUEST_NO, "2");
        mockGetUpayRefundResult(JsonUtil.jsonStrToObject("{\"result_code\":\"201\",\"refund_amount\":\"0\",\"redeem_result\":[{\"order_sn\":\"7895052764835245\",\"type\":\"11\",\"activity_sn\":\"2600599459\",\"source\":\"3\",\"amount\":\"1\"}]}", Map.class));
        resultString = postPerform("refundWosaiPromotion", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_REFUND_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.REFUNDED.name()
                        );
    
    }
    
}
