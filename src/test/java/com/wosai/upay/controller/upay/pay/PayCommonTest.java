package com.wosai.upay.controller.upay.pay;

import static org.junit.Assert.assertNotNull;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.exception.CoreMerchantConfigAbnormalException;
import com.wosai.upay.core.meta.SqbScene;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class PayCommonTest extends BaseTestController{
    @Autowired
    DataRepository dataRepository;

    /**
     * 
     * 错误的payway
     * 
     * @throws Exception
     */
    @Test
    public void test_error_payway()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        request.put(UpayService.PAYWAY, Order.PAYWAY_WEIXIN + "");
        
        String resultString = postPerform("test_error_payway", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "INVALID_BARCODE",
                "biz_response.error_code_standard", "EP04"
        );
    }
    
    /**
     * 
     * 终端对于的服务商没有新业务权限
     * 
     * @throws Exception
     */
    @Test
    public void test_trade_app_no_auth()throws Exception {
        mockGetAllPrams(new CoreMerchantConfigAbnormalException("终端归属服务商无该业务权限"));
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        JSONObject request = SupportUtil.buildPayRequest("134629059992379777");
        
        String resultString = postPerform("test_trade_app_no_auth", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SYSTEM_ERROR,
                        "error_code", "TRADE_APP_NO_AUTH"
                );
    }
    
    /**
     * 
     * 终端没有新业务配置
     * 
     * @throws Exception
     */
    @Test
    public void test_trade_app_no_config()throws Exception {
        mockGetAllPrams(new CoreMerchantConfigAbnormalException("业务方未配置交易参数"));
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        JSONObject request = SupportUtil.buildPayRequest("134629059992379777");
        
        String resultString = postPerform("test_trade_app_no_config", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SYSTEM_ERROR,
                        "error_code", "TRADE_APP_NOT_CONFIG"
                );
    }
    
    /**
     * 
     * 新业务方支付
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_trade_app_pay()throws Exception{
        String tradeApp = ThreadLocalRandom.current().nextInt(999999999) + "";
        Map<String, Object> allParams = SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS);
        allParams.put(TransactionParam.TRADE_APP, tradeApp);
        mockGetAllPrams(allParams);
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><xml>\n" + 
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                "    <sub_appid><![CDATA[wx0fd179d3b11b7b34]]></sub_appid>\n" + 
                "    <mch_id><![CDATA[1502132551]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[210825261]]></sub_mch_id>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <openid><![CDATA[ojjLFjtOHwU0_XvQiwEuVLfa-HmM]]></openid>\n" + 
                "    <sub_openid><![CDATA[okSzXt8h6w4NAlNZy0SjJDLX-d2g]]>\n" + 
                "    </sub_openid><trade_type><![CDATA[MICROPAY]]></trade_type>\n" + 
                "    <bank_type><![CDATA[OTHERS]]></bank_type>\n" + 
                "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "    <total_fee><![CDATA[1300]]></total_fee>\n" + 
                "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                "    <cash_fee><![CDATA[1300]]></cash_fee>\n" + 
                "    <settlement_total_fee><![CDATA[1300]]></settlement_total_fee>\n" + 
                "    <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                "    <transaction_id><![CDATA[4200000540202005214062725738]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                "    <attach><![CDATA[bank_mch_name=江门市蓬江区DoubleCup加杯&bank_mch_id=*************]]></attach>\n" + 
                "    <time_end><![CDATA[**************]]></time_end>\n" + 
                "    <nonce_str><![CDATA[16cb6ad10ec04125a2628f8aeb43c0bd]]></nonce_str>\n" + 
                "    <sign><![CDATA[NQRCCNrh8/e3yJPQVAuj2n4pEmuJv9uK9ifRBUOM8PZspYDigjBUjZIjfdTVf6anqKXpej4WPa/CmJSr0CBpjTSlQHBBuJJB+kcu2kI9BMbttA17OTFqj6o1A4NdHeBH0UOjvb72ZgBV108l2GiUtFFcvz8jyzduCYB3Py50tbSzKIbVqgUb40lJT1D+Fg3FD2ZkWJeznvQtI5dmOroOFWgAr1RM6ggbZd2muVZAUXJAYuulNnZsiBzlc81e6T5zQyzKhhV9thiIgAZOthvBi29m0AiSofYUCXVwxdhPlRpmVsgFMSBrY+y4hnvE8fywpG80BN/V+5MGdBqyPOkbkA==]]></sign>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_weixin_trade_app_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        Map order = dataRepository.getOrderByOrderSn(null, BeanUtil.getPropString(result, "biz_response.data.sn"));
        Map transaction = dataRepository.getPayTransactionByOrderSn(BeanUtil.getPropString(order, Order.MERCHANT_ID), BeanUtil.getPropString(order, Order.SN));
        assertNotNull(transaction);
        Assert.assertEquals("test_weixin_trade_app_pay", tradeApp, BeanUtil.getPropString(transaction, Transaction.CONFIG_SNAPSHOT+"."+ TransactionParam.TRADE_APP));
    }

    /**
     * 
     * mis 相同单号重复交易验证
     * 
     * @throws Exception
     */
    @Test
    public void test_mis_push_trade()throws Exception{
        Map<String, Object> allParams = SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS);
        mockGetAllPrams(allParams);
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());

        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        JSONObject sqbSence = new JSONObject();
        sqbSence.put(Transaction.SQB_SCENE, Arrays.asList(SqbScene.ACQUIRING_BIZ.getCode(), SqbScene.MIS_PUSH_TRADE.getCode(), SqbScene.CAMPUS.getCode()).stream().collect(Collectors.joining(",")));
        request.put(UpayService.EXTENDED, sqbSence);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><xml>\n" + 
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                "    <sub_appid><![CDATA[wx0fd179d3b11b7b34]]></sub_appid>\n" + 
                "    <mch_id><![CDATA[1502132551]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[210825261]]></sub_mch_id>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <openid><![CDATA[ojjLFjtOHwU0_XvQiwEuVLfa-HmM]]></openid>\n" + 
                "    <sub_openid><![CDATA[okSzXt8h6w4NAlNZy0SjJDLX-d2g]]>\n" + 
                "    </sub_openid><trade_type><![CDATA[MICROPAY]]></trade_type>\n" + 
                "    <bank_type><![CDATA[OTHERS]]></bank_type>\n" + 
                "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                "    <total_fee><![CDATA[1300]]></total_fee>\n" + 
                "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                "    <cash_fee><![CDATA[1300]]></cash_fee>\n" + 
                "    <settlement_total_fee><![CDATA[1300]]></settlement_total_fee>\n" + 
                "    <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                "    <transaction_id><![CDATA[4200000540202005214062725738]]></transaction_id>\n" + 
                "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                "    <attach><![CDATA[bank_mch_name=江门市蓬江区DoubleCup加杯&bank_mch_id=*************]]></attach>\n" + 
                "    <time_end><![CDATA[**************]]></time_end>\n" + 
                "    <nonce_str><![CDATA[16cb6ad10ec04125a2628f8aeb43c0bd]]></nonce_str>\n" + 
                "    <sign><![CDATA[NQRCCNrh8/e3yJPQVAuj2n4pEmuJv9uK9ifRBUOM8PZspYDigjBUjZIjfdTVf6anqKXpej4WPa/CmJSr0CBpjTSlQHBBuJJB+kcu2kI9BMbttA17OTFqj6o1A4NdHeBH0UOjvb72ZgBV108l2GiUtFFcvz8jyzduCYB3Py50tbSzKIbVqgUb40lJT1D+Fg3FD2ZkWJeznvQtI5dmOroOFWgAr1RM6ggbZd2muVZAUXJAYuulNnZsiBzlc81e6T5zQyzKhhV9thiIgAZOthvBi29m0AiSofYUCXVwxdhPlRpmVsgFMSBrY+y4hnvE8fywpG80BN/V+5MGdBqyPOkbkA==]]></sign>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_mis_push_trade", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        Map order = dataRepository.getOrderByOrderSn(null, BeanUtil.getPropString(result, "biz_response.data.sn"));
        Map transaction = dataRepository.getPayTransactionByOrderSn(BeanUtil.getPropString(order, Order.MERCHANT_ID), BeanUtil.getPropString(order, Order.SN));

        // 重新下单，因为有支付成功流水，下单失败，提示订单号重复
        resultString = postPerform("test_mis_push_trade", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.error_code", "CLIENT_SN_CONFLICT"
        );
        // 修改订单状态
        dataRepository.getOrderDao().updatePart(MapUtil.hashMap(DaoConstants.ID, MapUtil.getString(order, DaoConstants.ID), Order.STATUS, Order.STATUS_PAY_ERROR));
        dataRepository.getTransactionDao().updatePart(MapUtil.hashMap(DaoConstants.ID, MapUtil.getString(transaction, DaoConstants.ID), Transaction.STATUS, Transaction.STATUS_FAIL_IO_1));

        // 移除场景标识码，重复下单失败，提示订单号重复
        request.remove(UpayService.EXTENDED);
        resultString = postPerform("test_mis_push_trade", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.error_code", "CLIENT_SN_CONFLICT"
        );

        // 添加场景标识码，重新进行下单，下单成功
        request.put(UpayService.EXTENDED, sqbSence);
        resultString = postPerform("test_mis_push_trade", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
}
