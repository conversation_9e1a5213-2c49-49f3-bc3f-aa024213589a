package com.wosai.upay.controller.upay.precreate.alipay;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import com.wosai.upay.core.model.TransactionParam;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.alipay.overseas.AlipayOverseasConstants;
import com.wosai.mpay.api.alipay.overseas.ProtocolFields;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;


@PrepareForTest({WebUtils.class})
public class AlipayOverseasPrecreateTest extends BaseTestController {
    @Before
    public void init() {
        Map<String, Object> basicParams = SupportUtil.buildBasicParams();
        basicParams.put("merchant_country", "HK");
        basicParams.put("currency", "HKD");
        mockGetBasicPrams(basicParams);
        
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        // 随机设置设置签名方式
        Map<String, Object> tradeParams = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(SupportUtil.ALIPAY_V1_TRADE_PARAMS), Map.class);
        List<String> signTypes = Arrays.asList(AlipayOverseasConstants.SIGN_TYPE_RSA, AlipayOverseasConstants.SIGN_TYPE_MD5, AlipayOverseasConstants.SIGN_TYPE_RSA2) ;
        String useSignType = signTypes.get(ThreadLocalRandom.current().nextInt(3));
        Map<String, Object> alipayV1TradeParams = MapUtil.getMap(tradeParams, TransactionParam.ALIPAY_V1_TRADE_PARAMS);
        alipayV1TradeParams.put(TransactionParam.SIGN_TYPE, useSignType);
        if (useSignType.equals(AlipayOverseasConstants.SIGN_TYPE_RSA)) {
            mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        } else if (useSignType.equals(AlipayOverseasConstants.SIGN_TYPE_RSA2)){
            mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        }
        basicParams.putAll(tradeParams);
        mockGetAllPrams(SupportUtil.buildGetAllParams(basicParams));    }

    @Test
    public void test_precreate_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 2, "ov");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            Map postRequest = invocation.getArgument(3);
            String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
            if(AlipayOverseasConstants.SERVICE_NAME_PAY_PRECREATE.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"biz_product\">OVERSEAS_MBARCODE_PAY</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"identity_code_type\">barcode</param>\n" + 
                        "        <param name=\"sign\">9dab5a653742e9642eb544077e1d99bb</param>\n" + 
                        "        <param name=\"alipay_seller_id\">2088331454950965</param>\n" + 
                        "        <param name=\"trans_name\">HM</param>\n" + 
                        "        <param name=\"it_b_pay\">1m</param>\n" + 
                        "        <param name=\"trans_amount\">0.30</param>\n" + 
                        "        <param name=\"extend_info\">{\"secondary_merchant_id\":\"1680001168743\",\"secondary_merchant_name\":\"H &amp; M HENNES &amp; MAURITZ HOLDING ASIA LIMITED\",\"secondary_merchant_industry\":\"5699\",\"store_id\":\"HK0003\",\"store_name\":\"H&amp;M Festival Walk\",\"terminal_id\":\"100000210007743954\"}</param>\n" + 
                        "        <param name=\"partner_trans_id\">7895237622496498</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.spot.pay</param>\n" + 
                        "        <param name=\"currency\">HKD</param>\n" + 
                        "        <param name=\"buyer_identity_code\">288687967128493801</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0405</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088732277143707</alipay_buyer_user_id>\n" + 
                        "            <alipay_precreate_time>20200827205230</alipay_precreate_time>\n" + 
                        "            <alipay_trans_id>2020082722001443701423538469</alipay_trans_id>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <partner_trans_id>7895237622496498</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <qr_code>https://shouqianba.com</qr_code>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>1cfc47b309daefcd0b7cb520b9489f0a</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }else if(AlipayOverseasConstants.SERVICE_NAME_QUERY.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"partner_trans_id\">7895237697341173</param>\n" + 
                        "        <param name=\"partner\">2088431812540583</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.query</param>\n" + 
                        "        <param name=\"sign\">df17e42110ad998ff27cbee3f43ea5a5</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0354</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088802428979111</alipay_buyer_user_id>\n" + 
                        "            <alipay_trans_id>2020082622001479110518403500</alipay_trans_id>\n" + 
                        "            <alipay_trans_status>TRADE_SUCCESS</alipay_trans_status>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <out_trade_no>7895237697341173</out_trade_no>\n" + 
                        "            <partner_trans_id>7895237697341173</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <trans_amount>0.30</trans_amount>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>d1d96bc0975bd37dd34ca31421d91059</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_precreate_with_query", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_precreate_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
}
