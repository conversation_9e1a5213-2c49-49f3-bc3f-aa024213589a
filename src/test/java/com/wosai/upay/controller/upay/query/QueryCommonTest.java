package com.wosai.upay.controller.upay.query;

import com.alibaba.fastjson.JSONObject;
import com.wosai.constant.UpayConstant;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.transaction.service.GatewaySupportService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.SupportUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;


/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2020/3/10.
 */
public class QueryCommonTest extends BaseTestController {
    @MockBean
    private UpayOrderService upayOrderService;
    @MockBean
    private GatewaySupportService gatewaySupportService;

    @Before
    public void init(){
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        Mockito.reset(gatewaySupportService);
    }

    @Test
    public void testPosProxyQuery() throws Exception {
        PowerMockito.mockStatic(ApolloConfigurationCenterUtil.class);
        PowerMockito.when(ApolloConfigurationCenterUtil.getQueryHistoryEnable()).thenReturn(false);
        AtomicBoolean invoked = new AtomicBoolean(false);
        String sn = "test";
        PowerMockito.when(gatewaySupportService.getOrderBySn(Mockito.any(), ArgumentMatchers.eq(sn), Mockito.any(), Mockito.any())).then(new Answer<Map<String,Object>>() {
            @Override
            public Map<String, Object> answer(InvocationOnMock invocation) throws Throwable {
                invoked.set(true);
                return null;
            }
        });
        JSONObject request = SupportUtil.buildQueryRequest(sn, null, UpayConstant.SQB_ORIGIN_POS_PROXY);
        postPerform("test_pos_proxy_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Assert.assertTrue(invoked.get());

    }
}
