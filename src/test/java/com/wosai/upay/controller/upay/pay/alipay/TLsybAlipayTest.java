package com.wosai.upay.controller.upay.pay.alipay;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SM2Util;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class TLsybAlipayTest extends BaseTestController{

    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.SM2_PRIVATE_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.TL_SYB_TRADE_PARAMS));
    }

    @Test
    public void testRefund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        request.put(UpayService.TOTAL_AMOUNT, "400");
        AtomicInteger cnt = new AtomicInteger(0);

        PowerMockito.mockStatic(SM2Util.class);
        PowerMockito.when(SM2Util.unionpaySign(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("sm2 sign");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequest = invocation.getArgument(3);
                if (postRequest.lastIndexOf("apiweb/unitorder/scanqrpay") > 0) {
                    // 下单
                    return  "{\n"
                            + "    \"acct\": \"****************\",\n"
                            + "    \"accttype\": \"99\",\n"
                            + "    \"appid\": \"********\",\n"
                            + "    \"chnldata\": \"{\\\"fund_bill_list\\\":\\\"[{\\\\\\\"amount\\\\\\\":\\\\\\\"8.00\\\\\\\",\\\\\\\"fund_channel\\\\\\\":\\\\\\\"ALIPAYACCOUNT\\\\\\\"}]\\\"}\",\n"
                            + "    \"chnltrxid\": \"2023082222001445051444153972\",\n"
                            + "    \"cmid\": \"****************\",\n"
                            + "    \"cusid\": \"563301054412CJ2\",\n"
                            + "    \"fee\": \"3\",\n"
                            + "    \"fintime\": \"**************\",\n"
                            + "    \"initamt\": \"800\",\n"
                            + "    \"randomstr\": \"************\",\n"
                            + "    \"reqsn\": \"****************\",\n"
                            + "    \"retcode\": \"SUCCESS\",\n"
                            + "    \"sign\": \"cL4oEViocu0MXDbQqVLtx+XRuhs2Jo3TIkjj7Aod3HxLWx+ysForFY00579TINZbLDgwf5vkMS0zYPhJjS2izg==\",\n"
                            + "    \"trxamt\": \"800\",\n"
                            + "    \"trxcode\": \"VSP511\",\n"
                            + "    \"trxid\": \"230822126357852086\",\n"
                            + "    \"trxstatus\": \"0000\"\n"
                            + "}";
                } else if (postRequest.lastIndexOf("/apiweb/tranx/refund") > 0) {
                    // 退款
                    return "{\n"
                            + "    \"appid\": \"********\",\n"
                            + "    \"chnltrxid\": \"2023082222001445051444153972\",\n"
                            + "    \"cusid\": \"563301054412CJ2\",\n"
                            + "    \"errmsg\": \"系统异常\",\n"
                            + "    \"fee\": \"1\",\n"
                            + "    \"randomstr\": \"735883896117\",\n"
                            + "    \"reqsn\": \"7895032682020557\",\n"
                            + "    \"retcode\": \"SUCCESS\",\n"
                            + "    \"sign\": \"KN4bPSOetxeWUbIfgtcQVks+tMcJj+HjdEw9vZPdeK+le44Fu29g06T47+MpFgSlkStsNCCjc+uxR4ONq3eAaA==\",\n"
                            + "    \"trxcode\": \"VSP513\",\n"
                            + "    \"trxid\": \"230822127357851078\",\n"
                            + "    \"trxstatus\": \"2000\"\n"
                            + "}";
                } else if (postRequest.lastIndexOf("/apiweb/tranx/query") > 0) {
                    // 退款查询
                    if (cnt.incrementAndGet() == 1) {
                        return "{\n"
                                + "    \"acct\": \"****************\",\n"
                                + "    \"accttype\": \"99\",\n"
                                + "    \"appid\": \"********\",\n"
                                + "    \"chnltrxid\": \"2023082222001445051444153972\",\n"
                                + "    \"cusid\": \"563301054412CJ2\",\n"
                                + "    \"errmsg\": \"交易处理中\",\n"
                                + "    \"fee\": \"1\",\n"
                                + "    \"initamt\": \"400\",\n"
                                + "    \"randomstr\": \"755511983144\",\n"
                                + "    \"reqsn\": \"7895032682020557\",\n"
                                + "    \"retcode\": \"SUCCESS\",\n"
                                + "    \"sign\": \"PefHcTCppLkOlnRBCDWUV5pUL199FDeeA2fOhh5es5hdr5bTR5IcmVcx7rbwJ+P0QpLzemB9renG5EfPLNnzAQ==\",\n"
                                + "    \"trxamt\": \"400\",\n"
                                + "    \"trxcode\": \"VSP513\",\n"
                                + "    \"trxid\": \"230822127357851078\",\n"
                                + "    \"trxstatus\": \"2000\"\n"
                                + "}";
                    } else {
                        return "{\n"
                                + "    \"acct\": \"****************\",\n"
                                + "    \"accttype\": \"99\",\n"
                                + "    \"appid\": \"********\",\n"
                                + "    \"chnltrxid\": \"2023082222001445051444153972\",\n"
                                + "    \"cusid\": \"563301054412CJ2\",\n"
                                + "    \"errmsg\": \"处理成功\",\n"
                                + "    \"fee\": \"1\",\n"
                                + "    \"initamt\": \"400\",\n"
                                + "    \"randomstr\": \"755511983144\",\n"
                                + "    \"reqsn\": \"7895032682020557\",\n"
                                + "    \"retcode\": \"SUCCESS\",\n"
                                + "    \"sign\": \"PefHcTCppLkOlnRBCDWUV5pUL199FDeeA2fOhh5es5hdr5bTR5IcmVcx7rbwJ+P0QpLzemB9renG5EfPLNnzAQ==\",\n"
                                + "    \"trxamt\": \"400\",\n"
                                + "    \"trxcode\": \"VSP513\",\n"
                                + "    \"trxid\": \"230822127357851078\",\n"
                                + "    \"trxstatus\": \"0000\"\n"
                                + "}"; 
                    }
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        mockGetBasicPrams(SupportUtil.buildTlBasicParams());
        request.put(UpayService.REFUND_AMOUNT, "400");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
        
    }
}
