package com.wosai.upay.controller.upay.precreate.alipay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class AlipayV2PrecreateTest extends BaseTestController{

    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
    }
    
    /**
     * 
     * 支付宝直连c2b
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_csb()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 2, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_PRECREATE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_precreate_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"qr_code\": \"https:\\/\\/qr.alipay.com\\/bax01250g0jvy1ay9qd0509f\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"gQXukJ/a0qQPdCIEUimNkqPGPTJkQ19G21CJtUGskDSjsizwb7sckjwpQk1mIbVKxms9APqwI+GRRlfpAUlh+OSx09MLL6+FF6f3qTrPRRQjmmFfLCSnZ9wMByGqmTL05DB4BZyJBheLp0uA8hyvPR0YlCb7uxdfRDTPkQ/CITY=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_precreate_csb", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );

    }
    
    /**
     * 
     * 支付宝直连wap
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_WAP_V2_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_CREATE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_precreate_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"qr_code\": \"https:\\/\\/qr.alipay.com\\/bax01250g0jvy1ay9qd0509f\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"gQXukJ/a0qQPdCIEUimNkqPGPTJkQ19G21CJtUGskDSjsizwb7sckjwpQk1mIbVKxms9APqwI+GRRlfpAUlh+OSx09MLL6+FF6f3qTrPRRQjmmFfLCSnZ9wMByGqmTL05DB4BZyJBheLp0uA8hyvPR0YlCb7uxdfRDTPkQ/CITY=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_precreate_wap", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 支付宝直连 小程序
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_mini()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_WAP_V2_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 4, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_CREATE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_precreate_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"qr_code\": \"https:\\/\\/qr.alipay.com\\/bax01250g0jvy1ay9qd0509f\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"gQXukJ/a0qQPdCIEUimNkqPGPTJkQ19G21CJtUGskDSjsizwb7sckjwpQk1mIbVKxms9APqwI+GRRlfpAUlh+OSx09MLL6+FF6f3qTrPRRQjmmFfLCSnZ9wMByGqmTL05DB4BZyJBheLp0uA8hyvPR0YlCb7uxdfRDTPkQ/CITY=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_precreate_mini", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 支付宝直连 app
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_app()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_APP_V2_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 5, "zg");
        String resultString = postPerform("test_precreate_app", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 支付宝直连 h5
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_h5()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_H5_V2_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 6, "zg");
        String resultString = postPerform("test_precreate_h5", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 支付宝直连wap交易成功
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_success_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_WAP_V2_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_CREATE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_precreate_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"qr_code\": \"https:\\/\\/qr.alipay.com\\/bax01250g0jvy1ay9qd0509f\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"gQXukJ/a0qQPdCIEUimNkqPGPTJkQ19G21CJtUGskDSjsizwb7sckjwpQk1mIbVKxms9APqwI+GRRlfpAUlh+OSx09MLL6+FF6f3qTrPRRQjmmFfLCSnZ9wMByGqmTL05DB4BZyJBheLp0uA8hyvPR0YlCb7uxdfRDTPkQ/CITY=\"\n" + 
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_QUERY.equals(method)) {
                    return "{\n" + 
                            "    \"alipay_trade_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"she***@thope.com\",\n" + 
                            "        \"buyer_pay_amount\": \"240.00\",\n" + 
                            "        \"buyer_user_id\": \"****************\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"240.00\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"invoice_amount\": \"240.00\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"240.00\",\n" + 
                            "        \"send_pay_date\": \"2020-08-14 16:27:04\",\n" + 
                            "        \"total_amount\": \"240.00\",\n" + 
                            "        \"trade_no\": \"2020081422001465881437631084\",\n" + 
                            "        \"trade_status\": \"TRADE_SUCCESS\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"nJoBgZRgfsRQTA5QVc0l8iLd2SswfRTEE7yWvxDzb300PEBKvkf+klQo0mlwErLeosa+74Uu/Om5Nugvq3Pp9fYEe4/6sN+83OKj0aECEwUG8x4ESN2NEGfNSgCYlTPN+F61Zf0Teh0FITonRc8HJQTcUej1pcrxnO9BaVIWhh4=\"\n" + 
                            "}";
                    
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_precreate_success_wap", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_precreate_success_wap", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
}
