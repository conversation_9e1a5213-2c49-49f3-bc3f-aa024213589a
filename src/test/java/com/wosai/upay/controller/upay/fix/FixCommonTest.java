package com.wosai.upay.controller.upay.fix;

import static org.junit.Assert.assertNotNull;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.SupportUtil;


public class FixCommonTest extends BaseTestController{
    
    @Autowired
    DataRepository dataRepository;
    
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V1_TRADE_PARAMS));
    }
    
    /**
     * 
     * 撤单失败勾兑为支付成功
     * 
     * 期望结果：订单状态为支付成功
     * 
     * @throws Exception
     */
    @Test
    public void test_fix_cancel_to_paid()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        // 1. 生成订单
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                "            <fund_bill_list>\n" + 
                "                <TradeFundBill>\n" + 
                "                    <amount>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</amount>\n" + 
                "                    <force_use_cash>F</force_use_cash>\n" + 
                "                    <fund_channel>90</fund_channel>\n" + 
                "                </TradeFundBill>\n" + 
                "            </fund_bill_list>\n" + 
                "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                "            <out_trade_no>7895238320587535</out_trade_no>\n" + 
                "            <result_code>ORDER_SUCCESS_PAY_SUCCESS</result_code>\n" + 
                "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        String resultString = postPerform("test_fix_cancel_to_paid", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid
                );
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        
        // 2. 订单撤单失败
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <action>refund</action>\n" + 
                "            <out_trade_no>7895231922839437</out_trade_no>\n" + 
                "            <result_code>FAIL</result_code>\n" + 
                "            <retry_flag>N</retry_flag>\n" + 
                "            <trade_no>2020010122001483661405815189</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>fa28a006e7c4a9f01c15ce93b64cccfb</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        resultString = postPerform("test_fix_cancel_to_paid_cancel", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_ERROR
                );
        
        // 3. 勾兑订单为支付成功
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                "            <fund_bill_list>\n" + 
                "                <TradeFundBill>\n" + 
                "                    <amount>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</amount>\n" + 
                "                    <force_use_cash>F</force_use_cash>\n" + 
                "                    <fund_channel>90</fund_channel>\n" + 
                "                </TradeFundBill>\n" + 
                "            </fund_bill_list>\n" + 
                "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                "            <out_trade_no>7895238320587535</out_trade_no>\n" + 
                "            <result_code>ORDER_SUCCESS_PAY_SUCCESS</result_code>\n" + 
                "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        request.put(Order.SN, orderSn);
        resultString = postPerform("test_fix_cancel_to_paid_fix", "/upay/v2/fixOrderStatusToPaidIfCancelNotSuccess", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", CommonResponse.SUCCESS
                );
    }
    
    /**
     * 
     * 撤单失败勾兑为撤单成功
     * 
     * 期望结果：订单状态为撤单成功
     * 
     * @throws Exception
     */
    @Test
    public void test_fix_cancel_to_success()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        // 1. 生成订单
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                "            <fund_bill_list>\n" + 
                "                <TradeFundBill>\n" + 
                "                    <amount>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</amount>\n" + 
                "                    <force_use_cash>F</force_use_cash>\n" + 
                "                    <fund_channel>90</fund_channel>\n" + 
                "                </TradeFundBill>\n" + 
                "            </fund_bill_list>\n" + 
                "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                "            <out_trade_no>7895238320587535</out_trade_no>\n" + 
                "            <result_code>ORDER_SUCCESS_PAY_SUCCESS</result_code>\n" + 
                "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        String resultString = postPerform("test_fix_cancel_to_success_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid
                );
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        
        // 2. 订单撤单失败
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <action>refund</action>\n" + 
                "            <out_trade_no>7895231922839437</out_trade_no>\n" + 
                "            <result_code>FAIL</result_code>\n" + 
                "            <retry_flag>N</retry_flag>\n" + 
                "            <trade_no>2020010122001483661405815189</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>fa28a006e7c4a9f01c15ce93b64cccfb</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        resultString = postPerform("test_fix_cancel_to_success_cancel", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_ERROR
        );
        
        Criteria criteria = Criteria.where(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_CANCEL);
        Map cancelTransaction = dataRepository.getTransactionDao().filter(criteria).fetchOne();
        org.junit.Assert.assertNotNull("query cancel order fail", cancelTransaction);
        String tsn = (String) cancelTransaction.get(Transaction.TSN);
        
        // 3. 勾兑订单为撤单成功
        request.put(Order.SN, orderSn);
        request.put(Transaction.TSN, tsn);
        resultString = postPerform("test_fix_cancel_to_success_fix", "/upay/v2/fixCancelOrRefund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", CommonResponse.SUCCESS
                );
    }
    
    /**
     * 
     * 撤单失败订单勾兑失败
     * 
     * 期望结果：订单状态为撤单失败
     * 
     * @throws Exception
     */
//    @Test
    // TODO 最后一步实现并发处理时不好处理，需要想个方案
    public void test_fix_cancel_to_success_fail()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        // 1. 生成订单
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                "            <fund_bill_list>\n" + 
                "                <TradeFundBill>\n" + 
                "                    <amount>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</amount>\n" + 
                "                    <force_use_cash>F</force_use_cash>\n" + 
                "                    <fund_channel>90</fund_channel>\n" + 
                "                </TradeFundBill>\n" + 
                "            </fund_bill_list>\n" + 
                "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                "            <out_trade_no>7895238320587535</out_trade_no>\n" + 
                "            <result_code>ORDER_SUCCESS_PAY_SUCCESS</result_code>\n" + 
                "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        String resultString = postPerform("test_fix_cancel_to_success_fail_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid
                );
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        
        // 2. 订单撤单失败
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                "<alipay>\n" + 
                "    <is_success>T</is_success>\n" + 
                "    <response>\n" + 
                "        <alipay>\n" + 
                "            <action>refund</action>\n" + 
                "            <out_trade_no>7895231922839437</out_trade_no>\n" + 
                "            <result_code>FAIL</result_code>\n" + 
                "            <retry_flag>N</retry_flag>\n" + 
                "            <trade_no>2020010122001483661405815189</trade_no>\n" + 
                "        </alipay>\n" + 
                "    </response>\n" + 
                "    <sign>fa28a006e7c4a9f01c15ce93b64cccfb</sign>\n" + 
                "    <sign_type>MD5</sign_type>\n" + 
                "</alipay>");
        
        resultString = postPerform("test_fix_cancel_to_success_fail_cancel", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_ERROR
        );
        
        Criteria criteria = Criteria.where(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_CANCEL);
        Map cancelTransaction = dataRepository.getTransactionDao().filter(criteria).fetchOne();
        org.junit.Assert.assertNotNull("query cancel order fail", cancelTransaction);
        String tsn = (String) cancelTransaction.get(Transaction.TSN);
        
        // 3. 勾兑订单为退款成功
        PowerMockito.doThrow(new DaoVersionMismatchException("fail")).when(dataRepository.getOrderDao()).updatePart(Mockito.anyMap());
        request.put(Order.SN, orderSn);
        request.put(Transaction.TSN, tsn);
        resultString = postPerform("test_fix_cancel_to_success_fail_fix", "/upay/v2/fixCancelOrRefund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", CommonResponse.RESULT_CODE_VALIDATE_FAIL
                );
    }
    
    /**
     * 
     * 部分退款失败勾兑为退款成功，设置paid_amount和received_amount（不上送优惠，剩余金额按照比例退）
     * 
     * 期望结果：订单状态为退款成功
     * 
     * @throws Exception
     */
    @Test
    public void test_fix_refund_to_success()throws Exception{
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));

        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        // 1. 生成订单
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        request.put(UpayService.TOTAL_AMOUNT, "5600");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("{\n" + 
                "  \"alipay_trade_pay_response\": {\n" + 
                "    \"code\": \"10000\",\n" + 
                "    \"msg\": \"Success\",\n" + 
                "    \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                "    \"buyer_pay_amount\": \"35.00\",\n" + 
                "    \"buyer_user_id\": \""+ payerUid +"\",\n" + 
                "    \"discount_goods_detail\": \"[{\\\"goods_id\\\":\\\"024003\\\",\\\"goods_name\\\":\\\"招牌叉烧酥\\\",\\\"discount_amount\\\":\\\"12.00\\\",\\\"voucher_id\\\":\\\"2020072200073002963506865DQ2\\\",\\\"goods_num\\\":\\\"1.0\\\"},{\\\"goods_id\\\":\\\"024001\\\",\\\"goods_name\\\":\\\"招牌咖喱酥\\\",\\\"discount_amount\\\":\\\"12.00\\\",\\\"voucher_id\\\":\\\"2020072200073002963506865DQ1\\\",\\\"goods_num\\\":\\\"1.0\\\"}]\",\n" + 
                "    \"fund_bill_list\": [\n" + 
                "      {\n" + 
                "        \"amount\": \"15.00\",\n" + 
                "        \"fund_channel\": \"PCREDIT\"\n" + 
                "      },\n" + 
                "      {\n" + 
                "        \"amount\": \"6.00\",\n" + 
                "        \"fund_channel\": \"MDISCOUNT\"\n" + 
                "      },\n" + 
                "      {\n" + 
                "        \"amount\": \"28.93\",\n" + 
                "        \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                "      },\n" + 
                "      {\n" + 
                "        \"amount\": \"6.07\",\n" + 
                "        \"fund_channel\": \"DISCOUNT\"\n" + 
                "      }\n" + 
                "    ],\n" + 
                "    \"gmt_payment\": \"2020-08-01 10:00:34\",\n" + 
                "    \"invoice_amount\": \"26.93\",\n" + 
                "    \"out_trade_no\": \"****************\",\n" + 
                "    \"point_amount\": \"6.07\",\n" + 
                "    \"receipt_amount\": \"50.00\",\n" + 
                "    \"store_name\": \"红宝石(丰庄店)\",\n" + 
                "    \"total_amount\": \"56.00\",\n" + 
                "    \"trade_no\": \""+tradeNo+"\"\n" + 
                "  },\n" + 
                "  \"sign\": \"RWPQHLoEM7Eaez+QmoIQaAJlxXGlHwkhDikpwFkt5istkHxZ1JY4qFOuZZkuPPSo03ywosNyr5Ob9K4Sb1Eq2I3K53wqnexB5ebMNRpm4TltbVjsepmIahiFvNlwFUAjaFUobQgn2kgYre8BdlGAovp4PGd4XtBSr/nxS80rVEo=\"\n" + 
                "}");
        
        String resultString = postPerform("test_fix_refund_to_success_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid
                );
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        
        // 2. 订单退款失败
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("{\n" + 
                "    \"alipay_trade_refund_response\": {\n" + 
                "        \"code\": \"40004\",\n" + 
                "        \"msg\": \"Business Failed\",\n" + 
                "        \"sub_code\": \"ACQ.SELLER_BALANCE_NOT_ENOUGH\",\n" + 
                "        \"sub_msg\": \"卖家余额不足\",\n" + 
                "        \"out_trade_no\": \"7895237220618053\",\n" + 
                "        \"refund_fee\": \"0.00\",\n" + 
                "        \"send_back_fee\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"kIvor/KNBr6dbulHygxhAEcHoSdFbCXf1CJ9syFbqhKCc7c/ncO6EdrlIDWdJT52ctzkGptE9K6Cqb+R36Vv1e81uvcMXvqPybfy8pp3Qym+TDJteQzqAVpkN7EXq82++IZeeZbwElWS+jv3r/iBh9XnzAbmNDlbffhYrHIOTJA=\"\n" + 
                "}");
        request.put(UpayService.REFUND_AMOUNT, "3900");
        request.put(UpayService.REFUND_REQUEST_NO, "3900");
        resultString = postPerform("test_fix_refund_to_success_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_FAIL
        );
        
        Criteria criteria = Criteria.where(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_REFUND);
        Map refundTransaction = dataRepository.getTransactionDao().filter(criteria).fetchOne();
        org.junit.Assert.assertNotNull("query refund order fail", refundTransaction);
        String tsn = (String) refundTransaction.get(Transaction.TSN);
        
        // 3. 勾兑订单为退款成功
        request.put(Order.SN, orderSn);
        request.put(Transaction.TSN, tsn);
        resultString = postPerform("test_fix_refund_to_success_fix", "/upay/v2/fixCancelOrRefund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", CommonResponse.SUCCESS
                );
        refundTransaction = dataRepository.getTransactionDao().filter(criteria).fetchOne();
        org.junit.Assert.assertNotNull("query refund order fail", refundTransaction);
        assertNotNull(MapUtil.getObject(refundTransaction, Transaction.PAID_AMOUNT));
        assertNotNull(MapUtil.getObject(refundTransaction, Transaction.RECEIVED_AMOUNT));
    }

    /**
     * 
     * 部分退款失败勾兑为退款成功，设置paid_amount和received_amount （上送优惠，剩余金额按照上送金额退）
     * 
     * 期望结果：订单状态为退款成功
     * 
     * @throws Exception
     */
    @Test
    public void test_fix_refund_to_success2()throws Exception{
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        mockGetAlipayV2AppAuthInfo(SupportUtil.ALIPAY_AUTH_INFO);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));

        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        // 1. 生成订单
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        request.put(UpayService.TOTAL_AMOUNT, "5600");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("{\n" + 
                "  \"alipay_trade_pay_response\": {\n" + 
                "    \"code\": \"10000\",\n" + 
                "    \"msg\": \"Success\",\n" + 
                "    \"buyer_logon_id\": \""+ payerLogin +"\",\n" + 
                "    \"buyer_pay_amount\": \"35.00\",\n" + 
                "    \"buyer_user_id\": \""+ payerUid +"\",\n" + 
                "    \"discount_goods_detail\": \"[{\\\"goods_id\\\":\\\"024003\\\",\\\"goods_name\\\":\\\"招牌叉烧酥\\\",\\\"discount_amount\\\":\\\"12.00\\\",\\\"voucher_id\\\":\\\"2020072200073002963506865DQ2\\\",\\\"goods_num\\\":\\\"1.0\\\"},{\\\"goods_id\\\":\\\"024001\\\",\\\"goods_name\\\":\\\"招牌咖喱酥\\\",\\\"discount_amount\\\":\\\"12.00\\\",\\\"voucher_id\\\":\\\"2020072200073002963506865DQ1\\\",\\\"goods_num\\\":\\\"1.0\\\"}]\",\n" + 
                "    \"fund_bill_list\": [\n" + 
                "      {\n" + 
                "        \"amount\": \"15.00\",\n" + 
                "        \"fund_channel\": \"PCREDIT\"\n" + 
                "      },\n" + 
                "      {\n" + 
                "        \"amount\": \"6.00\",\n" + 
                "        \"fund_channel\": \"MDISCOUNT\"\n" + 
                "      },\n" + 
                "      {\n" + 
                "        \"amount\": \"28.93\",\n" + 
                "        \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                "      },\n" + 
                "      {\n" + 
                "        \"amount\": \"6.07\",\n" + 
                "        \"fund_channel\": \"DISCOUNT\"\n" + 
                "      }\n" + 
                "    ],\n" + 
                "    \"gmt_payment\": \"2020-08-01 10:00:34\",\n" + 
                "    \"invoice_amount\": \"26.93\",\n" + 
                "    \"out_trade_no\": \"****************\",\n" + 
                "    \"point_amount\": \"6.07\",\n" + 
                "    \"receipt_amount\": \"50.00\",\n" + 
                "    \"store_name\": \"红宝石(丰庄店)\",\n" + 
                "    \"total_amount\": \"56.00\",\n" + 
                "    \"trade_no\": \""+tradeNo+"\"\n" + 
                "  },\n" + 
                "  \"sign\": \"RWPQHLoEM7Eaez+QmoIQaAJlxXGlHwkhDikpwFkt5istkHxZ1JY4qFOuZZkuPPSo03ywosNyr5Ob9K4Sb1Eq2I3K53wqnexB5ebMNRpm4TltbVjsepmIahiFvNlwFUAjaFUobQgn2kgYre8BdlGAovp4PGd4XtBSr/nxS80rVEo=\"\n" + 
                "}");
        
        String resultString = postPerform("test_fix_refund_to_success_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", payerLogin,
                        "biz_response.data.payer_uid", payerUid
                );
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        
        // 2. 订单退款失败
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("{\n" + 
                "    \"alipay_trade_refund_response\": {\n" + 
                "        \"code\": \"40004\",\n" + 
                "        \"msg\": \"Business Failed\",\n" + 
                "        \"sub_code\": \"ACQ.SELLER_BALANCE_NOT_ENOUGH\",\n" + 
                "        \"sub_msg\": \"卖家余额不足\",\n" + 
                "        \"out_trade_no\": \"7895237220618053\",\n" + 
                "        \"refund_fee\": \"0.00\",\n" + 
                "        \"send_back_fee\": \"0.00\"\n" + 
                "    },\n" + 
                "    \"sign\": \"kIvor/KNBr6dbulHygxhAEcHoSdFbCXf1CJ9syFbqhKCc7c/ncO6EdrlIDWdJT52ctzkGptE9K6Cqb+R36Vv1e81uvcMXvqPybfy8pp3Qym+TDJteQzqAVpkN7EXq82++IZeeZbwElWS+jv3r/iBh9XnzAbmNDlbffhYrHIOTJA=\"\n" + 
                "}");
        request.put(UpayService.REFUND_AMOUNT, "3900");
        request.put(UpayService.REFUND_REQUEST_NO, "3900");
        resultString = postPerform("test_fix_refund_to_success_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_FAIL
        );
        
        Criteria criteria = Criteria.where(Transaction.ORDER_SN).is(orderSn).with(Transaction.TYPE).is(Transaction.TYPE_REFUND);
        Map refundTransaction = dataRepository.getTransactionDao().filter(criteria).fetchOne();
        org.junit.Assert.assertNotNull("query refund order fail", refundTransaction);
        String tsn = (String) refundTransaction.get(Transaction.TSN);
        
        // 3. 勾兑订单为退款成功
        request.put(Order.SN, orderSn);
        request.put(Transaction.TSN, tsn);
        request.put(UpayService.REFUND_CHANNEL_PAYMENTS, null);
        resultString = postPerform("test_fix_refund_to_success_fix", "/upay/v2/fixCancelOrRefund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", CommonResponse.SUCCESS
                );
        refundTransaction = dataRepository.getTransactionDao().filter(criteria).fetchOne();
        org.junit.Assert.assertNotNull("query refund order fail", refundTransaction);
        assertNotNull(MapUtil.getObject(refundTransaction, Transaction.PAID_AMOUNT));
        assertNotNull(MapUtil.getObject(refundTransaction, Transaction.RECEIVED_AMOUNT));
    }
}
