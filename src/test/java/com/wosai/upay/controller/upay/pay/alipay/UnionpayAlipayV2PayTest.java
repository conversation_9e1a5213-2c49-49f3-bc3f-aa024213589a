package com.wosai.upay.controller.upay.pay.alipay;

import static org.junit.Assert.assertNotNull;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.alipay.AlipayV2Methods;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class UnionpayAlipayV2PayTest extends BaseTestController {
    
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
    }

    @Test
    public void test_pay() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"151******96\",\n" + 
                            "        \"buyer_pay_amount\": \"3.00\",\n" + 
                            "        \"buyer_user_id\": \"2088602024531869\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"3.00\",\n" + 
                            "            \"fund_channel\": \"PCREDIT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" + 
                            "        \"invoice_amount\": \"3.00\",\n" + 
                            "        \"out_trade_no\": \"7895237191687329\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"3.00\",\n" + 
                            "        \"total_amount\": \"3.00\",\n" + 
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_pay_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"msg\": \" order success pay inprocess\",\n" + 
                            "        \"code\": \"10003\",\n" + 
                            "        \"cert_id\": \"4095468502\",\n" + 
                            "        \"buyer_user_id\": \"2088112314369486\",\n" + 
                            "        \"invoice_amount\": \"0.00\",\n" + 
                            "        \"out_trade_no\": \"7895237191215868\",\n" + 
                            "        \"total_amount\": \"7.00\",\n" + 
                            "        \"trade_no\": \"222020081422001469481440641717\",\n" + 
                            "        \"buyer_logon_id\": \"183***@qq.com\",\n" + 
                            "        \"receipt_amount\": \"0.00\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"buyer_pay_amount\": \"0.00\",\n" + 
                            "        \"sign_type\": \"RSA2\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"G0oPNVhMgF+yhzxNxyYgyGGE/+P4Xm7QzIMjpLTT1Jpgg11lAshabO6X7f1u+XSPs4VSfSlsDEeiaJZMeSPKN/f7pGR512jDQTHrUWO48GMJRtnB9S9c2UG/LZY/rq6y/D/dujSCLGHlMBODjdEB0FwY9yB4j0KbhtM2M4pxOD0Fc+ITU4lqHvl6JCXTaXchPeiQ2I+jMSqAx3DlvG7IjvV0vJTqam80H5PhaZZfIe71c/3zaaRgx2g6RrSZq74Oed1EthEgFIpHwk07QqlBntjJ60ay0/5P9u9qcokSykTZq7w4aiy++B/TXL/Kqqf27QgKiaPiL4Cy2624TwODPg==\"\n" + 
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_QUERY.equals(method)) {
                    return "{\n" + 
                            "    \"alipay_trade_query_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"she***@thope.com\",\n" + 
                            "        \"buyer_pay_amount\": \"240.00\",\n" + 
                            "        \"buyer_user_id\": \"****************\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"240.00\",\n" + 
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" + 
                            "        }],\n" + 
                            "        \"invoice_amount\": \"240.00\",\n" + 
                            "        \"out_trade_no\": \"****************\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"240.00\",\n" + 
                            "        \"send_pay_date\": \"2020-08-14 16:27:04\",\n" + 
                            "        \"total_amount\": \"240.00\",\n" + 
                            "        \"trade_no\": \"2020081422001465881437631084\",\n" + 
                            "        \"trade_status\": \"TRADE_SUCCESS\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"nJoBgZRgfsRQTA5QVc0l8iLd2SswfRTEE7yWvxDzb300PEBKvkf+klQo0mlwErLeosa+74Uu/Om5Nugvq3Pp9fYEe4/6sN+83OKj0aECEwUG8x4ESN2NEGfNSgCYlTPN+F61Zf0Teh0FITonRc8HJQTcUej1pcrxnO9BaVIWhh4=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_refund()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.NUCC_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"151******96\",\n" + 
                            "        \"buyer_pay_amount\": \"0.30\",\n" + 
                            "        \"buyer_user_id\": \"2088602024531869\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"PCREDIT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"7895237191687329\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" + 
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_REFUND.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_refund_response\": {\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"refund_detail_item_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"PCREDIT\"\n" + 
                            "        }],\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"cert_id\": \"4095468502\",\n" + 
                            "        \"buyer_user_id\": \"2088912756537368\",\n" + 
                            "        \"out_trade_no\": \"7895237705256424\",\n" + 
                            "        \"refund_fee\": \"0.30\",\n" + 
                            "        \"gmt_refund_pay\": \"2020-08-31 14:19:28\",\n" + 
                            "        \"send_back_fee\": \"0.30\",\n" + 
                            "        \"trade_no\": \"362020083122001437361400168652\",\n" + 
                            "        \"buyer_logon_id\": \"157******86\",\n" + 
                            "        \"sign_type\": \"RSA2\",\n" + 
                            "        \"fund_change\": \"Y\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"l0rWEFa6OIZtq0hxA9In5g21SHMAb5wzzGph0ndEq0i4Mgq4MvJ9RTe7fnicn1vxzICjqYeLnO0yWTPBdkeIO3E1bbKw3g+9PbLUOQkFruMoqhDd2EZLVqmkM6OEevoRz9rjzHg/VK+EWUzxd/Bv5rC+pr+5/RWPh/2QlVWK6T+6mehrqGJiS+Qme2k2xsa2yCsXve4w/YD0cTeVYgdAefTlmID8THxutQHrfVAi5pGyCLVqvv3ga/JENNKNl13Sudvgs4pkoHStFyD1GIxhIRjN+q1V+wa7mEqdA1j9wcxVivDZ8vglW+KN7oG5wtzmZhif2rpqwJtpSbULvNL96w==\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    } 
    
    @Test
    public void test_cancel()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.NUCC_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"151******96\",\n" + 
                            "        \"buyer_pay_amount\": \"0.30\",\n" + 
                            "        \"buyer_user_id\": \"2088602024531869\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"0.30\",\n" + 
                            "            \"fund_channel\": \"PCREDIT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" + 
                            "        \"invoice_amount\": \"0.30\",\n" + 
                            "        \"out_trade_no\": \"7895237191687329\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"total_amount\": \"0.30\",\n" + 
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" + 
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_CANCEL.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_cancel_response\": {\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"out_trade_no\": \"7895237703519299\",\n" + 
                            "        \"retry_flag\": \"N\",\n" + 
                            "        \"action\": \"close\",\n" + 
                            "        \"trade_no\": \"842020083122001426571450118467\",\n" + 
                            "        \"cert_id\": \"4095468502\",\n" + 
                            "        \"sign_type\": \"RSA2\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"MaMNxZqaVtNccSSDCYN9YK8oNAq5gtjztTBTAMEeRiXdcsTAf69I4TX37oLx5aXFrVRAJnXwl+AE0Jn3VP25Y49Isgvz6A73UBnMBqk5wJIYd2xYlzrB4fZ/wMeCEDuzqCylxT39Rfzh+CpYs5fRrhp09HIuPunfSLkRlI8NOmMOGrJh/0+cFf+zA1dzZEin7xQfN4VnWVuXka0SS2laIEbfslwx/YTajXtJVFQQIO+1cGrIq2FxR7n6ebAsc6GUCrnNUvaf710A6KfcihCWUTEKJa3fRD2wsybV4HMNMfRNbGbxuG74czm5/PCkWGCEOTszcX/8RdVrqwJx9iBZTQ==\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
    
    @Test
    public void test_pay_provider_response() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" + 
                            "    \"alipay_trade_pay_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"Success\",\n" + 
                            "        \"buyer_logon_id\": \"151******96\",\n" + 
                            "        \"buyer_pay_amount\": \"3.00\",\n" + 
                            "        \"buyer_user_id\": \"2088602024531869\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": \"3.00\",\n" + 
                            "            \"fund_channel\": \"PCREDIT\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" + 
                            "        \"invoice_amount\": \"3.00\",\n" + 
                            "        \"out_trade_no\": \"7895237191687329\",\n" + 
                            "        \"point_amount\": \"0.00\",\n" + 
                            "        \"receipt_amount\": \"3.00\",\n" + 
                            "        \"total_amount\": \"3.00\",\n" + 
                            "        \"trade_no\": \"2020081422001431861444371002\",\n" + 
                            "        \"discount_goods_detail\":\"[{\\\"goods_id\\\":\\\"022007\\\",\\\"goods_name\\\":\\\"优格芝士卷蛋糕\\\",\\\"discount_amount\\\":\\\"22.00\\\",\\\"voucher_id\\\":\\\"2021061600073002765508GQ0PWX\\\",\\\"goods_num\\\":\\\"1.0\\\"}]\"," +
                            "        \"voucher_detail_list\": [{\"id\":\"2015102600073002039000002D5O\",\"name\":\"XX超市5折优惠\",\"type\":\"ALIPAY_FIX_VOUCHER\",\"amount\":10,\"merchant_contribute\":9,\"other_contribute\":1,\"memo\":\"学生专用优惠\",\"template_id\":\"20171030000730015359000EMZP0\",\"purchase_buyer_contribute\":2.01,\"purchase_merchant_contribute\":1.03,\"purchase_ant_contribute\":0.82}]\n" + 
                            "    },\n" + 
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay_provider_response", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        Map<String, Object> providerResponse = (Map<String, Object>) BeanUtil.getNestedProperty(result, "biz_response.data.provider_response");
        assertNotNull(providerResponse.get(QueryResponse.GOODS_DETAILS));
        assertNotNull(providerResponse.get(QueryResponse.VOUCHER_DETAILS));
    }

    @Test
    public void test_pay_new() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" +
                            "    \"alipay_trade_pay_response\": {\n" +
                            "        \"code\": \"ACQ.PAYMENT_AUTH_CODE_INVALID\",\n" +
                            "        \"msg\": \"支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]\",\n" +
                            "        \"buyer_logon_id\": \"151******96\",\n" +
                            "        \"buyer_pay_amount\": \"3.00\",\n" +
                            "        \"buyer_user_id\": \"2088602024531869\",\n" +
                            "        \"fund_bill_list\": [{\n" +
                            "            \"amount\": \"3.00\",\n" +
                            "            \"fund_channel\": \"PCREDIT\"\n" +
                            "        }],\n" +
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" +
                            "        \"invoice_amount\": \"3.00\",\n" +
                            "        \"out_trade_no\": \"7895237191687329\",\n" +
                            "        \"point_amount\": \"0.00\",\n" +
                            "        \"receipt_amount\": \"3.00\",\n" +
                            "        \"total_amount\": \"3.00\",\n" +
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" +
                            "    },\n" +
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" +
                            "}";
                }
                return "fail";
            }
        });

        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction alipay pay {}", transaction);
    }

    @Test
    public void test_pay_with_query_new() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" +
                            "    \"alipay_trade_pay_response\": {\n" +
                            "        \"code\": \"ACQ.PAYMENT_AUTH_CODE_INVALID\",\n" +
                            "        \"msg\": \"支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]\",\n" +
                            "        \"cert_id\": \"4095468502\",\n" +
                            "        \"buyer_user_id\": \"2088112314369486\",\n" +
                            "        \"invoice_amount\": \"0.00\",\n" +
                            "        \"out_trade_no\": \"7895237191215868\",\n" +
                            "        \"total_amount\": \"7.00\",\n" +
                            "        \"trade_no\": \"222020081422001469481440641717\",\n" +
                            "        \"buyer_logon_id\": \"183***@qq.com\",\n" +
                            "        \"receipt_amount\": \"0.00\",\n" +
                            "        \"point_amount\": \"0.00\",\n" +
                            "        \"buyer_pay_amount\": \"0.00\",\n" +
                            "        \"sign_type\": \"RSA2\"\n" +
                            "    },\n" +
                            "    \"sign\": \"G0oPNVhMgF+yhzxNxyYgyGGE/+P4Xm7QzIMjpLTT1Jpgg11lAshabO6X7f1u+XSPs4VSfSlsDEeiaJZMeSPKN/f7pGR512jDQTHrUWO48GMJRtnB9S9c2UG/LZY/rq6y/D/dujSCLGHlMBODjdEB0FwY9yB4j0KbhtM2M4pxOD0Fc+ITU4lqHvl6JCXTaXchPeiQ2I+jMSqAx3DlvG7IjvV0vJTqam80H5PhaZZfIe71c/3zaaRgx2g6RrSZq74Oed1EthEgFIpHwk07QqlBntjJ60ay0/5P9u9qcokSykTZq7w4aiy++B/TXL/Kqqf27QgKiaPiL4Cy2624TwODPg==\"\n" +
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_QUERY.equals(method)) {
                    return "{\n" +
                            "    \"alipay_trade_query_response\": {\n" +
                            "        \"code\": \"ACQ.PAYMENT_AUTH_CODE_INVALID\",\n" +
                            "        \"msg\": \"支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]\",\n" +
                            "        \"buyer_logon_id\": \"she***@thope.com\",\n" +
                            "        \"buyer_pay_amount\": \"240.00\",\n" +
                            "        \"buyer_user_id\": \"****************\",\n" +
                            "        \"fund_bill_list\": [{\n" +
                            "            \"amount\": \"240.00\",\n" +
                            "            \"fund_channel\": \"ALIPAYACCOUNT\"\n" +
                            "        }],\n" +
                            "        \"invoice_amount\": \"240.00\",\n" +
                            "        \"out_trade_no\": \"****************\",\n" +
                            "        \"point_amount\": \"0.00\",\n" +
                            "        \"receipt_amount\": \"240.00\",\n" +
                            "        \"send_pay_date\": \"2020-08-14 16:27:04\",\n" +
                            "        \"total_amount\": \"240.00\",\n" +
                            "        \"trade_no\": \"2020081422001465881437631084\",\n" +
                            "        \"trade_status\": \"TRADE_SUCCESS\"\n" +
                            "    },\n" +
                            "    \"sign\": \"nJoBgZRgfsRQTA5QVc0l8iLd2SswfRTEE7yWvxDzb300PEBKvkf+klQo0mlwErLeosa+74Uu/Om5Nugvq3Pp9fYEe4/6sN+83OKj0aECEwUG8x4ESN2NEGfNSgCYlTPN+F61Zf0Teh0FITonRc8HJQTcUej1pcrxnO9BaVIWhh4=\"\n" +
                            "}";
                }
                return "fail";
            }
        });

        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction alipay query {}", transaction);
    }

    @Test
    public void test_refund_new()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.NUCC_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" +
                            "    \"alipay_trade_pay_response\": {\n" +
                            "        \"code\": \"ACQ.PAYMENT_AUTH_CODE_INVALID\",\n" +
                            "        \"msg\": \"支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]\",\n" +
                            "        \"buyer_logon_id\": \"151******96\",\n" +
                            "        \"buyer_pay_amount\": \"0.30\",\n" +
                            "        \"buyer_user_id\": \"2088602024531869\",\n" +
                            "        \"fund_bill_list\": [{\n" +
                            "            \"amount\": \"0.30\",\n" +
                            "            \"fund_channel\": \"PCREDIT\"\n" +
                            "        }],\n" +
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" +
                            "        \"invoice_amount\": \"0.30\",\n" +
                            "        \"out_trade_no\": \"7895237191687329\",\n" +
                            "        \"point_amount\": \"0.00\",\n" +
                            "        \"receipt_amount\": \"0.30\",\n" +
                            "        \"total_amount\": \"0.30\",\n" +
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" +
                            "    },\n" +
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" +
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_REFUND.equals(method)) {
                    return  "{\n" +
                            "    \"alipay_trade_refund_response\": {\n" +
                            "        \"msg\": \"Success\",\n" +
                            "        \"refund_detail_item_list\": [{\n" +
                            "            \"amount\": \"0.30\",\n" +
                            "            \"fund_channel\": \"PCREDIT\"\n" +
                            "        }],\n" +
                            "        \"code\": \"10000\",\n" +
                            "        \"cert_id\": \"4095468502\",\n" +
                            "        \"buyer_user_id\": \"2088912756537368\",\n" +
                            "        \"out_trade_no\": \"7895237705256424\",\n" +
                            "        \"refund_fee\": \"0.30\",\n" +
                            "        \"gmt_refund_pay\": \"2020-08-31 14:19:28\",\n" +
                            "        \"send_back_fee\": \"0.30\",\n" +
                            "        \"trade_no\": \"362020083122001437361400168652\",\n" +
                            "        \"buyer_logon_id\": \"157******86\",\n" +
                            "        \"sign_type\": \"RSA2\",\n" +
                            "        \"fund_change\": \"Y\"\n" +
                            "    },\n" +
                            "    \"sign\": \"l0rWEFa6OIZtq0hxA9In5g21SHMAb5wzzGph0ndEq0i4Mgq4MvJ9RTe7fnicn1vxzICjqYeLnO0yWTPBdkeIO3E1bbKw3g+9PbLUOQkFruMoqhDd2EZLVqmkM6OEevoRz9rjzHg/VK+EWUzxd/Bv5rC+pr+5/RWPh/2QlVWK6T+6mehrqGJiS+Qme2k2xsa2yCsXve4w/YD0cTeVYgdAefTlmID8THxutQHrfVAi5pGyCLVqvv3ga/JENNKNl13Sudvgs4pkoHStFyD1GIxhIRjN+q1V+wa7mEqdA1j9wcxVivDZ8vglW+KN7oG5wtzmZhif2rpqwJtpSbULvNL96w==\"\n" +
                            "}";
                }
                return "fail";
            }
        });

        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction alipay refund {}", transaction);
    }

    @Test
    public void test_cancel_new()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.NUCC_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(AlipayV2Methods.ALIPAY_TRADE_TRADE.equals(method)) {
                    return  "{\n" +
                            "    \"alipay_trade_pay_response\": {\n" +
                            "        \"code\": \"ACQ.PAYMENT_AUTH_CODE_INVALID\",\n" +
                            "        \"msg\": \"支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]\",\n" +
                            "        \"buyer_logon_id\": \"151******96\",\n" +
                            "        \"buyer_pay_amount\": \"0.30\",\n" +
                            "        \"buyer_user_id\": \"2088602024531869\",\n" +
                            "        \"fund_bill_list\": [{\n" +
                            "            \"amount\": \"0.30\",\n" +
                            "            \"fund_channel\": \"PCREDIT\"\n" +
                            "        }],\n" +
                            "        \"gmt_payment\": \"2020-08-14 17:48:58\",\n" +
                            "        \"invoice_amount\": \"0.30\",\n" +
                            "        \"out_trade_no\": \"7895237191687329\",\n" +
                            "        \"point_amount\": \"0.00\",\n" +
                            "        \"receipt_amount\": \"0.30\",\n" +
                            "        \"total_amount\": \"0.30\",\n" +
                            "        \"trade_no\": \"2020081422001431861444371002\"\n" +
                            "    },\n" +
                            "    \"sign\": \"cZnTiUwY9GrbaE4ONpwbO8/3SFK7ZsbJnj9TXvhYQ4+krtfDHvUOIw3ivdYp7Dzj7FIxcWUeC7SU+CEC5F35ddL9hEA5wm7CZMpR7PoX37v2+3KJ34ZLWC6p+dSK5sF7lHezuvQnZXh1RuWKmXfyXFntRXKgK7DeCciJGEhHxJE=\"\n" +
                            "}";
                }else if(AlipayV2Methods.ALIPAY_TRADE_CANCEL.equals(method)) {
                    return  "{\n" +
                            "    \"alipay_trade_cancel_response\": {\n" +
                            "        \"msg\": \"Success\",\n" +
                            "        \"code\": \"10000\",\n" +
                            "        \"out_trade_no\": \"7895237703519299\",\n" +
                            "        \"retry_flag\": \"N\",\n" +
                            "        \"action\": \"close\",\n" +
                            "        \"trade_no\": \"842020083122001426571450118467\",\n" +
                            "        \"cert_id\": \"4095468502\",\n" +
                            "        \"sign_type\": \"RSA2\"\n" +
                            "    },\n" +
                            "    \"sign\": \"MaMNxZqaVtNccSSDCYN9YK8oNAq5gtjztTBTAMEeRiXdcsTAf69I4TX37oLx5aXFrVRAJnXwl+AE0Jn3VP25Y49Isgvz6A73UBnMBqk5wJIYd2xYlzrB4fZ/wMeCEDuzqCylxT39Rfzh+CpYs5fRrhp09HIuPunfSLkRlI8NOmMOGrJh/0+cFf+zA1dzZEin7xQfN4VnWVuXka0SS2laIEbfslwx/YTajXtJVFQQIO+1cGrIq2FxR7n6ebAsc6GUCrnNUvaf710A6KfcihCWUTEKJa3fRD2wsybV4HMNMfRNbGbxuG74czm5/PCkWGCEOTszcX/8RdVrqwJx9iBZTQ==\"\n" +
                            "}";
                }
                return "fail";
            }
        });

        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);

        logger.info("transaction alipay cancel {}", transaction);
    }
}
