package com.wosai.upay.controller.upay.pay.unionpay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.ChinaumsSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class LKLUnionPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.LAKALA_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
    }
    
    @Test
    public void test_unionpay_barcode() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/thirdpartplatform/scancodpay/8001.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>000000</responseCode>\n" + 
                            "    <reqLogNo>7895237790282633</reqLogNo>\n" + 
                            "    <mercId>822731079940231</mercId>\n" + 
                            "    <txnTm>0831024901</txnTm>\n" + 
                            "    <txnAmt>30</txnAmt>\n" + 
                            "    <payOrderId>41200831101417699501</payOrderId>\n" + 
                            "    <merOrderNo>20083140081319</merOrderNo>\n" + 
                            "    <payChlDesc>银联二维码</payChlDesc>\n" + 
                            "    <mrkInfo>0</mrkInfo>\n" + 
                            "    <message>OK</message>\n" + 
                            "    <MAC>1566140f43ee83a713aab82675879ae4a6115d81</MAC>\n" + 
                            "</ROOT>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_unionpay_barcode_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/thirdpartplatform/scancodpay/8001.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>0100C0</responseCode>\n" + 
                            "    <reqLogNo>7895237799609776</reqLogNo>\n" + 
                            "    <mercId>822603058121697</mercId>\n" + 
                            "    <txnTm>0830200926</txnTm>\n" + 
                            "    <txnAmt>20000</txnAmt>\n" + 
                            "    <merOrderNo>20083042720885</merOrderNo>\n" + 
                            "    <payChlDesc/>\n" + 
                            "    <message>交易状态未明，请查询对账结果[8010001]</message>\n" + 
                            "    <errCode>04</errCode>\n" + 
                            "    <MAC>00000000000000000000000000000000</MAC>\n" + 
                            "</ROOT>";
                }else if(serviceUrl.contains("/thirdpartplatform/scancodpay/8021.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>000000</responseCode>\n" + 
                            "    <reqLogNo>7895237799621437</reqLogNo>\n" + 
                            "    <ornReqLogNo>7895237799609776</ornReqLogNo>\n" + 
                            "    <mercId>822603058121697</mercId>\n" + 
                            "    <txnTm>0830200926</txnTm>\n" + 
                            "    <txnAmt>000000000030</txnAmt>\n" + 
                            "    <payOrderId>28200830725663574481</payOrderId>\n" + 
                            "    <merOrderNo>20083042720885</merOrderNo>\n" + 
                            "    <payChlDesc>银联二维码</payChlDesc>\n" + 
                            "    <mrkInfo>0</mrkInfo>\n" + 
                            "    <tradeState>SUCCESS</tradeState>\n" + 
                            "    <message>OK</message>\n" + 
                            "    <MAC>cf7081adb568bdbea74b89d725fe14d7259dea8b</MAC>\n" + 
                            "</ROOT>";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_unionpay_barcode_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_unionpay_barcode_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    @Test
    public void test_unionpay_barcode_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/thirdpartplatform/scancodpay/8001.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>000000</responseCode>\n" + 
                            "    <reqLogNo>7895237790282633</reqLogNo>\n" + 
                            "    <mercId>822731079940231</mercId>\n" + 
                            "    <txnTm>0831024901</txnTm>\n" + 
                            "    <txnAmt>30</txnAmt>\n" + 
                            "    <payOrderId>41200831101417699501</payOrderId>\n" + 
                            "    <merOrderNo>20083140081319</merOrderNo>\n" + 
                            "    <payChlDesc>银联二维码</payChlDesc>\n" + 
                            "    <mrkInfo>0</mrkInfo>\n" + 
                            "    <message>OK</message>\n" + 
                            "    <MAC>1566140f43ee83a713aab82675879ae4a6115d81</MAC>\n" + 
                            "</ROOT>";
                }else if(serviceUrl.contains("/thirdpartplatform/scancodpay/8031.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>000000</responseCode>\n" + 
                            "    <reqLogNo>7895237669575421</reqLogNo>\n" + 
                            "    <ornReqLogNo>7895237669381598</ornReqLogNo>\n" + 
                            "    <mercId>822100358129490</mercId>\n" + 
                            "    <txnTm>0826125156</txnTm>\n" + 
                            "    <txnAmt>30</txnAmt>\n" + 
                            "    <merOrderNo>20082641074789</merOrderNo>\n" + 
                            "    <message>退款成功</message>\n" + 
                            "    <MAC>00000000000000000000000000000000</MAC>\n" + 
                            "</ROOT>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_unionpay_barcode_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_unionpay_barcode_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_unionpay_barcode_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/thirdpartplatform/scancodpay/8001.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>000000</responseCode>\n" + 
                            "    <reqLogNo>7895237790282633</reqLogNo>\n" + 
                            "    <mercId>822731079940231</mercId>\n" + 
                            "    <txnTm>0831024901</txnTm>\n" + 
                            "    <txnAmt>30</txnAmt>\n" + 
                            "    <payOrderId>41200831101417699501</payOrderId>\n" + 
                            "    <merOrderNo>20083140081319</merOrderNo>\n" + 
                            "    <payChlDesc>银联二维码</payChlDesc>\n" + 
                            "    <mrkInfo>0</mrkInfo>\n" + 
                            "    <message>OK</message>\n" + 
                            "    <MAC>1566140f43ee83a713aab82675879ae4a6115d81</MAC>\n" + 
                            "</ROOT>";
                }else if(serviceUrl.contains("/thirdpartplatform/scancodpay/8002.dor")) {
                    return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<ROOT>\n" + 
                            "    <responseCode>000000</responseCode>\n" + 
                            "    <reqLogNo>7895237669575421</reqLogNo>\n" + 
                            "    <ornReqLogNo>7895237669381598</ornReqLogNo>\n" + 
                            "    <mercId>822100358129490</mercId>\n" + 
                            "    <txnTm>0826125156</txnTm>\n" + 
                            "    <txnAmt>30</txnAmt>\n" + 
                            "    <merOrderNo>20082641074789</merOrderNo>\n" + 
                            "    <message>撤单成功</message>\n" + 
                            "    <MAC>00000000000000000000000000000000</MAC>\n" + 
                            "</ROOT>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_unionpay_barcode_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        resultString = postPerform("test_unionpay_barcode_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
    
    
}
