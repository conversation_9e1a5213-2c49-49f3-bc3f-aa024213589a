package com.wosai.upay.controller.upay.pay.sodexo;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class SodexoPayTest extends BaseTestController{
    
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.SODEXO_TRADE_PARAMS));
    }

    @Test
    public void test_pay() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("775934513156916643");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/OAuth/Token")) {
                return  "{\n" + 
                        "    \"access_token\": \"eDu4Slz-Os0iU38aP7hTCpEjL21gfEGd5kAesjQ20Kyx9k4bmHq-kE-YvTu1X2mULLL7X5h-G6kbI-oHXJ1Kz78FT-pn8oAxCTr6XR_vQEQ5pkVkAeqvzoqS8iQ2L7Cg06VVu1OQrSvFHmcnO20hx3FOuwVsqSAUu5esmeDoRZ5NGfE0feEQmEzMpCmctpgRd9BaW6aQ1H0qXOvkumcW-U0Q2HU127YVGHzM02fb6DrP6OMt\",\n" + 
                        "    \"token_type\": \"bearer\",\n" + 
                        "    \"expires_in\": 86399,\n" + 
                        "    \"refresh_token\": \"OMjKTwTbIgIZOgoTomlXNP9HtUDup6md1spz3s0nZKSoOBj-EdOFLK2vYiluCnQp0IltN-P3h9iiDHozy4x_9ie4c6IbOffncUE9pBoqPI4PL0sNugC38RJFWIkoLmIBRWVqETpfuuTeCwm7v7X4FsnBENly4oce0hRLouQ2at2soXYhkKB6M6lqMWXLbHssZie9JKBsEvmwd3aUB5LJQbsTKT-w-N92UBvAjykiBjlmalz9\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/MealCardBarcodeExpense")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"扣费成功\",\n" + 
                        "    \"transType\": \"MealExpense\",\n" + 
                        "    \"submitTime\": \"20200828100250\",\n" + 
                        "    \"clientTraceNo\": \"7895237746039554\",\n" + 
                        "    \"hostTime\": \"20200828100251\",\n" + 
                        "    \"avaBalance\": \"0.30\",\n" + 
                        "    \"hostTraceNo\": \"CA9040233396831002076059876\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/TransQuery")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"查询成功\",\n" + 
                        "    \"version\": \"1.0\",\n" + 
                        "    \"submitTime\": \"20200828100252\",\n" + 
                        "    \"hostTraceNo\": \"CA9040233396831002076059876\",\n" + 
                        "    \"hostTime\": \"20200828100139\",\n" + 
                        "    \"TransStatus\": \"SUCCESS\",\n" + 
                        "    \"clientTraceNo\": \"7895237746039554\"\n" + 
                        "}";
            }
            return "fail";
        });

        String resultString = postPerform("test_pay_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);
        
        Thread.sleep(5000);
        resultString = postPerform("test_pay_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("775934513156916643");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/OAuth/Token")) {
                return  "{\n" + 
                        "    \"access_token\": \"eDu4Slz-Os0iU38aP7hTCpEjL21gfEGd5kAesjQ20Kyx9k4bmHq-kE-YvTu1X2mULLL7X5h-G6kbI-oHXJ1Kz78FT-pn8oAxCTr6XR_vQEQ5pkVkAeqvzoqS8iQ2L7Cg06VVu1OQrSvFHmcnO20hx3FOuwVsqSAUu5esmeDoRZ5NGfE0feEQmEzMpCmctpgRd9BaW6aQ1H0qXOvkumcW-U0Q2HU127YVGHzM02fb6DrP6OMt\",\n" + 
                        "    \"token_type\": \"bearer\",\n" + 
                        "    \"expires_in\": 86399,\n" + 
                        "    \"refresh_token\": \"OMjKTwTbIgIZOgoTomlXNP9HtUDup6md1spz3s0nZKSoOBj-EdOFLK2vYiluCnQp0IltN-P3h9iiDHozy4x_9ie4c6IbOffncUE9pBoqPI4PL0sNugC38RJFWIkoLmIBRWVqETpfuuTeCwm7v7X4FsnBENly4oce0hRLouQ2at2soXYhkKB6M6lqMWXLbHssZie9JKBsEvmwd3aUB5LJQbsTKT-w-N92UBvAjykiBjlmalz9\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/MealCardBarcodeExpense")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"扣费成功\",\n" + 
                        "    \"transType\": \"MealExpense\",\n" + 
                        "    \"submitTime\": \"20200828100250\",\n" + 
                        "    \"clientTraceNo\": \"7895237746039554\",\n" + 
                        "    \"hostTime\": \"20200828100251\",\n" + 
                        "    \"avaBalance\": \"0.30\",\n" + 
                        "    \"hostTraceNo\": \"CA9040233396831002076059876\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/TransQuery")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"查询成功\",\n" + 
                        "    \"version\": \"1.0\",\n" + 
                        "    \"submitTime\": \"20200828100252\",\n" + 
                        "    \"hostTraceNo\": \"CA9040233396831002076059876\",\n" + 
                        "    \"hostTime\": \"20200828100139\",\n" + 
                        "    \"TransStatus\": \"SUCCESS\",\n" + 
                        "    \"clientTraceNo\": \"7895237746039554\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/MealCardPaymentRefund")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"退货成功\",\n" + 
                        "    \"transType\": \"MealRefund\",\n" + 
                        "    \"submitTime\": \"20200816183332\",\n" + 
                        "    \"clientTraceNo\": \"7895237816333513\",\n" + 
                        "    \"hostTime\": \"20200816183332\",\n" + 
                        "    \"clientTraceOrgNo\": \"7895237811850574\"\n" + 
                        "}";
            }
            return "fail";
        });

        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);
        
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );

        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("775934513156916643");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/OAuth/Token")) {
                return  "{\n" + 
                        "    \"access_token\": \"eDu4Slz-Os0iU38aP7hTCpEjL21gfEGd5kAesjQ20Kyx9k4bmHq-kE-YvTu1X2mULLL7X5h-G6kbI-oHXJ1Kz78FT-pn8oAxCTr6XR_vQEQ5pkVkAeqvzoqS8iQ2L7Cg06VVu1OQrSvFHmcnO20hx3FOuwVsqSAUu5esmeDoRZ5NGfE0feEQmEzMpCmctpgRd9BaW6aQ1H0qXOvkumcW-U0Q2HU127YVGHzM02fb6DrP6OMt\",\n" + 
                        "    \"token_type\": \"bearer\",\n" + 
                        "    \"expires_in\": 86399,\n" + 
                        "    \"refresh_token\": \"OMjKTwTbIgIZOgoTomlXNP9HtUDup6md1spz3s0nZKSoOBj-EdOFLK2vYiluCnQp0IltN-P3h9iiDHozy4x_9ie4c6IbOffncUE9pBoqPI4PL0sNugC38RJFWIkoLmIBRWVqETpfuuTeCwm7v7X4FsnBENly4oce0hRLouQ2at2soXYhkKB6M6lqMWXLbHssZie9JKBsEvmwd3aUB5LJQbsTKT-w-N92UBvAjykiBjlmalz9\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/MealCardBarcodeExpense")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"扣费成功\",\n" + 
                        "    \"transType\": \"MealExpense\",\n" + 
                        "    \"submitTime\": \"20200828100250\",\n" + 
                        "    \"clientTraceNo\": \"7895237746039554\",\n" + 
                        "    \"hostTime\": \"20200828100251\",\n" + 
                        "    \"avaBalance\": \"0.30\",\n" + 
                        "    \"hostTraceNo\": \"CA9040233396831002076059876\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/TransQuery")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"查询成功\",\n" + 
                        "    \"version\": \"1.0\",\n" + 
                        "    \"submitTime\": \"20200828100252\",\n" + 
                        "    \"hostTraceNo\": \"CA9040233396831002076059876\",\n" + 
                        "    \"hostTime\": \"20200828100139\",\n" + 
                        "    \"TransStatus\": \"SUCCESS\",\n" + 
                        "    \"clientTraceNo\": \"7895237746039554\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/MealCardPaymentRefund")) {
                return  "{\n" + 
                        "    \"returnCode\": \"0000\",\n" + 
                        "    \"returnMessage\": \"退款成功\",\n" + 
                        "    \"transType\": \"MealRefund\",\n" + 
                        "    \"submitTime\": \"20200816183332\",\n" + 
                        "    \"clientTraceNo\": \"7895237816333513\",\n" + 
                        "    \"hostTime\": \"20200816183332\",\n" + 
                        "    \"clientTraceOrgNo\": \"7895237811850574\"\n" + 
                        "}";
            }
            return "fail";
        });

        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);
        
        Thread.sleep(5000);
        resultString = postPerform("test_cancel_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );

        resultString = postPerform("test_cancel_3", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
