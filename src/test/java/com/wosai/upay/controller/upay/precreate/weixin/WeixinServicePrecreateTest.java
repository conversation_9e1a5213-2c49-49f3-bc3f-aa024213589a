package com.wosai.upay.controller.upay.precreate.weixin;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class WeixinServicePrecreateTest extends BaseTestController{

    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById("****weixin_cert_config_key****", null);
    }
    
    /**
     * 
     * 微信直连c2b
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_csb()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 2, "zg");
        String resultString = postPerform("test_precreate_csb", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );

    }
    
    /**
     * 
     * 微信直连wap
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_WAP_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1514263421]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[VigBXkHKQ4rqDUOD]]></nonce_str>\n" + 
                "    <sign><![CDATA[0087CF37D621D9D123AB16EEE9477BE7]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <prepay_id><![CDATA[wx14133732516973bc4d220ab145aff80000]]></prepay_id>\n" + 
                "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                "</xml>");
        String resultString = postPerform("test_precreate_wap", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 微信直连 小程序
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_mini()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_MINI_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 4, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1532038281]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[MemcLI24rt7xrh3C]]></nonce_str>\n" + 
                "    <sign><![CDATA[CDCF385C0E7B8D8E7572A76F30679DD0]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <prepay_id><![CDATA[wx14115916925398847fda5c130afdc00000]]></prepay_id>\n" + 
                "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                "    <sub_appid><![CDATA[wx65dca6b170b51b73]]></sub_appid>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_precreate_mini", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 微信直连 app
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_app()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_APP_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 5, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                "    <appid><![CDATA[wxa56c7420fd33309e]]></appid>\n" + 
                "    <mch_id><![CDATA[1253942801]]></mch_id>\n" + 
                "    <nonce_str><![CDATA[orNQYu1mTo6GS7VJ]]></nonce_str>\n" + 
                "    <sign><![CDATA[84AD14D43FBB7CF2E9ABE2E403A9D623]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <prepay_id><![CDATA[wx1412095079279697163e1d1d5dda840000]]></prepay_id>\n" + 
                "    <trade_type><![CDATA[APP]]></trade_type>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_precreate_app", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 微信直连 h5
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_h5()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_H5_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 6, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[1530689411]]></sub_mch_id>\n" + 
                "    <nonce_str><![CDATA[wAKegdOnDg9fUzar]]></nonce_str>\n" + 
                "    <sign><![CDATA[DBD14372708DD8DB83980712D7241C3B]]></sign>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <prepay_id><![CDATA[wx141214145481735af7d43ccd8a11b60000]]></prepay_id>\n" + 
                "    <trade_type><![CDATA[MWEB]]></trade_type>\n" + 
                "    <sub_appid><![CDATA[wx740713193e1bf076]]></sub_appid>\n" + 
                "    <mweb_url><![CDATA[https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx141214145481735af7d43ccd8a11b60000&package=2159922376]]></mweb_url>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_precreate_h5", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 微信直连wap交易成功
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_success_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_WAP_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer((mock)->{
            String url = mock.getArgument(3);
            if(url.endsWith("/pay/unifiedorder")) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                        "<xml>\n" + 
                        "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                        "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                        "    <sub_mch_id><![CDATA[1514263421]]></sub_mch_id>\n" + 
                        "    <nonce_str><![CDATA[VigBXkHKQ4rqDUOD]]></nonce_str>\n" + 
                        "    <sign><![CDATA[0087CF37D621D9D123AB16EEE9477BE7]]></sign>\n" + 
                        "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "    <prepay_id><![CDATA[wx14133732516973bc4d220ab145aff80000]]></prepay_id>\n" + 
                        "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                        "</xml>";
            }else if(url.endsWith("/pay/orderquery")) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                        "<xml>\n" + 
                        "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                        "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                        "    <sub_mch_id><![CDATA[1514263421]]></sub_mch_id>\n" + 
                        "    <nonce_str><![CDATA[jdhTTsWyU3stngRf]]></nonce_str>\n" + 
                        "    <sign><![CDATA[BC1523D5674CC3C01CAB9AA41393D73A]]></sign>\n" + 
                        "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "    <openid><![CDATA[oyBevt8tiv8JCsJmBEI-96vhEukw]]></openid>\n" + 
                        "    <is_subscribe><![CDATA[N]]></is_subscribe>\n" + 
                        "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                        "    <bank_type><![CDATA[CMB_CREDIT]]></bank_type>\n" + 
                        "    <total_fee>2500</total_fee>\n" + 
                        "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                        "    <transaction_id><![CDATA[4200000706202008142384955424]]></transaction_id>\n" + 
                        "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                        "    <attach><![CDATA[]]></attach>\n" + 
                        "    <time_end><![CDATA[**************]]></time_end>\n" + 
                        "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                        "    <cash_fee>2500</cash_fee>\n" + 
                        "    <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                        "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                        "    <version><![CDATA[1.0]]></version>\n" + 
                        "    <promotion_detail><![CDATA[]]></promotion_detail>\n" + 
                        "</xml>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_precreate_wap", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_precreate_wap", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
}
