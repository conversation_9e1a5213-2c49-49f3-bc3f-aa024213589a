package com.wosai.upay.controller.upay.pay.tl;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.workflow.TLSybServiceProvider;
import com.wosai.upay.workflow.TransactionContext;
import jdk.internal.dynalink.linker.LinkerServices;
import lombok.SneakyThrows;
import org.apache.commons.beanutils.BeanUtils;
import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

public class TlSybAlipayTest extends BaseTestController {

    @SneakyThrows
    @Test
    public void resolveAliPayRefundFund() {
        Map<String, Object> order = MapUtil.hashMap(Order.ORIGINAL_TOTAL, 14500l);
        Map<String, Object> transaction = JacksonUtil.toBeanQuietly(" {\n" +
                "      \"subject\": \"自行车服务处含光门店\",\n" +
                "      \"received_amount\": 10000,\n" +
                "      \"buyer_login\": \"****************\",\n" +
                "      \"merchant_id\": \"5856df5b-d99f-4bf7-93b5-587a97d86ef3\",\n" +
                "      \"body\": null,\n" +
                "      \"type\": 11,\n" +
                "      \"mtime\": *************,\n" +
                "      \"extended_params\": null,\n" +
                "      \"tsn\": \"****************\",\n" +
                "      \"product_flag\": null,\n" +
                "      \"operator\": \"001\",\n" +
                "      \"extra_out_fields\": {\n" +
                "        \"payments\": [\n" +
                "          {\n" +
                "            \"type\": \"WALLET_ALIPAY\",\n" +
                "            \"origin_type\": \"ALIPAYACCOUNT\",\n" +
                "            \"amount\": 10000\n" +
                "          }\n" +
                "        ],\n" +
                "        \"trade_no\": \"2024012222001477351433722096\",\n" +
                "        \"combo_id\": \"87\",\n" +
                "        \"order_info\": {\n" +
                "          \"original_total\": 14500,\n" +
                "          \"effective_total\": 14500,\n" +
                "          \"ctime\": *************,\n" +
                "          \"trade_no\": \"240122116991169528\"\n" +
                "        }\n" +
                "      },\n" +
                "      \"reflect\": null,\n" +
                "      \"provider\": 1035,\n" +
                "      \"original_amount\": 10000,\n" +
                "      \"ctime\": *************,\n" +
                "      \"biz_error_code\": null,\n" +
                "      \"id\": \"t****************\",\n" +
                "      \"terminal_id\": \"daac122a-cd3e-43d5-bf63-d9685fee5207\",\n" +
                "      \"store_id\": \"0f8749b8-baab-4135-96d0-643b65de802a\",\n" +
                "      \"client_tsn\": \"1801B81CB6E7A4EC2C0E721199C25253-004873\",\n" +
                "      \"provider_error_info\": {\n" +
                "        \"refund\": {\n" +
                "          \"retcode\": \"SUCCESS\",\n" +
                "          \"retmsg\": \"\",\n" +
                "          \"trxstatus\": \"0000\",\n" +
                "          \"errmsg\": \"\"\n" +
                "        }\n" +
                "      },\n" +
                "      \"extra_params\": null,\n" +
                "      \"payway\": 2,\n" +
                "      \"version\": 2,\n" +
                "      \"finish_time\": 1705911435755,\n" +
                "      \"sub_payway\": 1,\n" +
                "      \"nfc_card\": null,\n" +
                "      \"config_snapshot\": {\n" +
                "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "        \"merchant_id\": \"5856df5b-d99f-4bf7-93b5-587a97d86ef3\",\n" +
                "        \"merchant_sn\": \"1680005534941\",\n" +
                "        \"merchant_name\": \"西安追忆商贸有限责任公司\",\n" +
                "        \"merchant_country\": \"CHN\",\n" +
                "        \"currency\": \"CNY\",\n" +
                "        \"longitude\": \"108.932300\",\n" +
                "        \"latitude\": \"34.251835\",\n" +
                "        \"district_code\": \"610104\",\n" +
                "        \"store_id\": \"0f8749b8-baab-4135-96d0-643b65de802a\",\n" +
                "        \"store_sn\": \"1580000006073726\",\n" +
                "        \"store_client_sn\": null,\n" +
                "        \"store_name\": \"自行车服务处含光门店\",\n" +
                "        \"store_city\": \"西安市\",\n" +
                "        \"terminal_id\": \"daac122a-cd3e-43d5-bf63-d9685fee5207\",\n" +
                "        \"terminal_sn\": \"100029850025565028\",\n" +
                "        \"terminal_name\": \"自行车服务处含光门店\",\n" +
                "        \"terminal_vendor_app_appid\": \"2020080300002985\",\n" +
                "        \"terminal_category\": 102,\n" +
                "        \"clearance_provider\": 5,\n" +
                "        \"pay_status\": 1,\n" +
                "        \"common_switch\": \"00000000000222222222222222222222\",\n" +
                "        \"merchant_daily_max_credit_limit_trans\": null,\n" +
                "        \"merchant_monthly_max_credit_limit_trans\": null,\n" +
                "        \"payway_day_credit_limits\": null,\n" +
                "        \"payway_month_credit_limits\": null,\n" +
                "        \"is_need_refund_fee_flag\": null,\n" +
                "        \"hit_payway\": null,\n" +
                "        \"provider\": 1035,\n" +
                "        \"tl_syb_trade_params\": {\n" +
                "          \"fee_rate\": \"0.38\",\n" +
                "          \"liquidation_next_day\": false,\n" +
                "          \"fee_rate_tag\": {\n" +
                "            \"1\": \"87:\"\n" +
                "          },\n" +
                "          \"fee_rate_original\": \"0.38\",\n" +
                "          \"active\": true,\n" +
                "          \"fee\": 37\n" +
                "        },\n" +
                "        \"term_info\": {\n" +
                "          \"term_id\": \"ST00BsdU\",\n" +
                "          \"term_type\": null,\n" +
                "          \"serial_num\": null\n" +
                "        },\n" +
                "        \"term_id\": \"ST00BsdU\",\n" +
                "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
                "        \"trade_app\": \"1\"\n" +
                "      },\n" +
                "      \"deleted\": false,\n" +
                "      \"effective_amount\": 10000,\n" +
                "      \"trade_no\": \"240122115491185029\",\n" +
                "      \"channel_finish_time\": 1705911435000,\n" +
                "      \"order_id\": \"o****************\",\n" +
                "      \"items\": null,\n" +
                "      \"order_sn\": \"****************\",\n" +
                "      \"buyer_uid\": \"****************\",\n" +
                "      \"status\": 2000\n" +
                "    }", Map.class);

        Map<String, Object> payTransaction = JacksonUtil.toBeanQuietly("{\n" +
                "      \"subject\": \"自行车服务处含光门店\",\n" +
                "      \"received_amount\": 14500,\n" +
                "      \"merchant_id\": \"5856df5b-d99f-4bf7-93b5-587a97d86ef3\",\n" +
                "      \"body\": null,\n" +
                "      \"type\": 30,\n" +
                "      \"mtime\": 1705905901699,\n" +
                "      \"extended_params\": {\n" +
                "        \n" +
                "      },\n" +
                "      \"tsn\": \"****************\",\n" +
                "      \"product_flag\": null,\n" +
                "      \"operator\": \"01\",\n" +
                "      \"extra_out_fields\": {\n" +
                "        \"combo_id\": \"87\",\n" +
                "        \"payments\": [\n" +
                "          {\n" +
                "            \"type\": \"WALLET_ALIPAY\",\n" +
                "            \"origin_type\": \"ALIPAYACCOUNT\",\n" +
                "            \"amount\": 14500\n" +
                "          }\n" +
                "        ]\n" +
                "      },\n" +
                "      \"reflect\": null,\n" +
                "      \"provider\": 1035,\n" +
                "      \"original_amount\": 14500,\n" +
                "      \"ctime\": *************,\n" +
                "      \"biz_error_code\": null,\n" +
                "      \"id\": \"t****************\",\n" +
                "      \"terminal_id\": \"00f9d8ff-b219-4839-bfeb-95f18a4b8d79\",\n" +
                "      \"store_id\": \"0f8749b8-baab-4135-96d0-643b65de802a\",\n" +
                "      \"client_tsn\": \"1801B81CB6E7A4EC2C0E721199C25253\",\n" +
                "      \"provider_error_info\": {\n" +
                "        \"pay\": {\n" +
                "          \"retcode\": \"SUCCESS\",\n" +
                "          \"retmsg\": \"\",\n" +
                "          \"trxstatus\": \"0000\",\n" +
                "          \"errmsg\": \"\"\n" +
                "        }\n" +
                "      },\n" +
                "      \"extra_params\": {\n" +
                "        \"barcode\": \"283752306769655425\",\n" +
                "        \"poi\": {\n" +
                "          \"longitude\": \"108.9322711\",\n" +
                "          \"latitude\": \"34.2518758\"\n" +
                "        },\n" +
                "        \"client_ip\": \"*************\",\n" +
                "        \"sqb_ip\": \"*************\",\n" +
                "        \"sqb_station\": \"460,110,9562,39220272\"\n" +
                "      },\n" +
                "      \"payway\": 2,\n" +
                "      \"version\": 2,\n" +
                "      \"finish_time\": 1705905901695,\n" +
                "      \"sub_payway\": 1,\n" +
                "      \"nfc_card\": null,\n" +
                "      \"config_snapshot\": {\n" +
                "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "        \"merchant_id\": \"5856df5b-d99f-4bf7-93b5-587a97d86ef3\",\n" +
                "        \"merchant_sn\": \"1680005534941\",\n" +
                "        \"merchant_name\": \"西安追忆商贸有限责任公司\",\n" +
                "        \"merchant_country\": \"CHN\",\n" +
                "        \"currency\": \"CNY\",\n" +
                "        \"longitude\": \"108.932300\",\n" +
                "        \"latitude\": \"34.251835\",\n" +
                "        \"district_code\": \"610104\",\n" +
                "        \"store_id\": \"0f8749b8-baab-4135-96d0-643b65de802a\",\n" +
                "        \"store_sn\": \"1580000006073726\",\n" +
                "        \"store_client_sn\": null,\n" +
                "        \"store_name\": \"自行车服务处含光门店\",\n" +
                "        \"store_city\": \"西安市\",\n" +
                "        \"terminal_id\": \"00f9d8ff-b219-4839-bfeb-95f18a4b8d79\",\n" +
                "        \"terminal_sn\": \"100036140025564841\",\n" +
                "        \"terminal_name\": \"自行车服务处含光门店\",\n" +
                "        \"terminal_vendor_app_appid\": \"2021022600003614\",\n" +
                "        \"terminal_category\": 102,\n" +
                "        \"clearance_provider\": 5,\n" +
                "        \"pay_status\": 1,\n" +
                "        \"common_switch\": \"00000000000222222222222222222222\",\n" +
                "        \"merchant_daily_max_credit_limit_trans\": null,\n" +
                "        \"merchant_monthly_max_credit_limit_trans\": null,\n" +
                "        \"payway_day_credit_limits\": null,\n" +
                "        \"payway_month_credit_limits\": null,\n" +
                "        \"is_need_refund_fee_flag\": null,\n" +
                "        \"hit_payway\": null,\n" +
                "        \"provider\": 1035,\n" +
                "        \"tl_syb_trade_params\": {\n" +
                "          \"fee_rate\": \"0.38\",\n" +
                "          \"liquidation_next_day\": false,\n" +
                "          \"fee_rate_tag\": {\n" +
                "            \"1\": \"87:\"\n" +
                "          },\n" +
                "          \"fee_rate_original\": \"0.38\",\n" +
                "          \"active\": true,\n" +
                "          \"fee\": 55\n" +
                "        },\n" +
                "        \"term_info\": {\n" +
                "          \"term_id\": \"ST00BsdU\",\n" +
                "          \"term_type\": null,\n" +
                "          \"serial_num\": null\n" +
                "        },\n" +
                "        \"term_id\": \"ST00BsdU\",\n" +
                "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
                "        \"trade_app\": \"1\"\n" +
                "      },\n" +
                "      \"deleted\": false,\n" +
                "      \"effective_amount\": 14500,\n" +
                "      \"paid_amount\": 14500,\n" +
                "      \"trade_no\": \"240122116991169528\",\n" +
                "      \"channel_finish_time\": *************,\n" +
                "      \"order_id\": \"o****************\",\n" +
                "      \"items\": null,\n" +
                "      \"order_sn\": \"****************\",\n" +
                "      \"buyer_uid\": \"****************\",\n" +
                "      \"status\": 2000\n" +
                "    }", Map.class);
        Map<String, Object> sybResult = JacksonUtil.toBeanQuietly("{\"appid\":\"********\",\"bankcode\":\"ALIPAYACCOUNT\",\"chnldata\":\"{\\\"refund_detail_item_list\\\":\\\"[{\\\\\\\"amount\\\\\\\":\\\\\\\"100.00\\\\\\\",\\\\\\\"fund_channel\\\\\\\":\\\\\\\"ALIPAYACCOUNT\\\\\\\"}]\\\"}\",\"chnltrxid\":\"2024012222001477351433722096\",\"cusid\":\"563791055992CLB\",\"fee\":\"37\",\"fintime\":\"**************\",\"randomstr\":\"************\",\"reqsn\":\"****************\",\"retcode\":\"SUCCESS\",\"sign\":\"2/sf2GKxpfVQA3mYoJOo3BOnvxaBmn3zDYSnw1YfCsEGaH55paLSIkC37slW4pqU4PRX7phcDflTs87FNchzuQ==\",\"trxcode\":\"VSP513\",\"trxid\":\"240122115491185029\",\"trxstatus\":\"0000\"}", Map.class);
        Field resolveRefundFundMap = TLSybServiceProvider.class.getDeclaredField("resolveRefundFundMap");

        resolveRefundFundMap.setAccessible(true);
        Map<Integer, Object> resolveRefundMap = (Map<Integer, Object>) resolveRefundFundMap.get(null);
        Object resolveRefund = resolveRefundMap.get(2);
        Method resolveRefundFund = resolveRefund.getClass().getMethod("resolveRefundFund", Map.class, TransactionContext.class, Map.class);
        resolveRefundFund.setAccessible(true);
        resolveRefundFund.invoke(resolveRefund, sybResult, new TransactionContext(order, transaction), payTransaction);
        assert MapUtil.getLongValue(transaction, "paid_amount") == 10000;
    }


    @SneakyThrows
    @Test
    public void resolveAliPayRefundFundDiscount() {
        Map<String, Object> order = MapUtil.hashMap(Order.ORIGINAL_TOTAL, 214319l);
        Map<String, Object> payTransaction = JacksonUtil.toBeanQuietly("    {\n" +
                "                              \"subject\":  \"合肥阳光消化病医院\",\n" +
                "                              \"received_amount\":  271310,\n" +
                "                              \"buyer_login\":  \"****************\",\n" +
                "                              \"merchant_id\":  \"8c3caa46-3b77-4b0b-af0a-1220ee74b737\",\n" +
                "                              \"body\":  \"合肥阳光消化病医院\",\n" +
                "                              \"type\":  30,\n" +
                "                              \"mtime\":  1708743119010,\n" +
                "                              \"extended_params\":  {\n" +
                "                                        \"fqSignature\":  \"nosign\"\n" +
                "                              },\n" +
                "                              \"tsn\":  \"****************\",\n" +
                "                              \"product_flag\":  null,\n" +
                "                              \"operator\":  \"QRCODE:20120401292219226154\",\n" +
                "                              \"extra_out_fields\":  {\n" +
                "                                        \"combo_id\":  \"4\",\n" +
                "                                        \"wap_pay_request\":  {\n" +
                "                                                  \"tradeNO\":  \"2024022422001442931405634910\"\n" +
                "                                        },\n" +
                "                                        \"trade_no\":  \"2024022422001442931405634910\",\n" +
                "                                        \"payments\":  [\n" +
                "                                                  {\n" +
                "                                                            \"type\":  \"WALLET_ALIPAY\",\n" +
                "                                                            \"origin_type\":  \"ALIPAYACCOUNT\",\n" +
                "                                                            \"amount\":  271296\n" +
                "                                                  },\n" +
                "                                                  {\n" +
                "                                                            \"type\":  \"DISCOUNT_CHANNEL\",\n" +
                "                                                            \"origin_type\":  \"DISCOUNT\",\n" +
                "                                                            \"amount\":  14\n" +
                "                                                  }\n" +
                "                                        ]\n" +
                "                              },\n" +
                "                              \"reflect\":  null,\n" +
                "                              \"provider\":  1035,\n" +
                "                              \"original_amount\":  271310,\n" +
                "                              \"ctime\":  1708743101578,\n" +
                "                              \"biz_error_code\":  {\n" +
                "\n" +
                "                              },\n" +
                "                              \"id\":  \"t****************\",\n" +
                "                              \"terminal_id\":  \"d2c35781-f46d-4433-a216-236e818f5c4a\",\n" +
                "                              \"store_id\":  \"486b4855-b3e3-4bd0-90a4-299cec1dc542\",\n" +
                "                              \"client_tsn\":  \"170874310145892875795\",\n" +
                "                              \"provider_error_info\":  {\n" +
                "                                        \"query\":  {\n" +
                "                                                  \"retcode\":  \"SUCCESS\",\n" +
                "                                                  \"retmsg\":  \"\",\n" +
                "                                                  \"trxstatus\":  \"0000\",\n" +
                "                                                  \"errmsg\":  \"\"\n" +
                "                                        },\n" +
                "                                        \"precreate\":  {\n" +
                "                                                  \"retcode\":  \"SUCCESS\",\n" +
                "                                                  \"retmsg\":  \"\",\n" +
                "                                                  \"trxstatus\":  \"0000\",\n" +
                "                                                  \"errmsg\":  \"\"\n" +
                "                                        }\n" +
                "                              },\n" +
                "                              \"extra_params\":  {\n" +
                "                                        \"payer_uid\":  \"****************\",\n" +
                "                                        \"client_ip\":  \"************\",\n" +
                "                                        \"sqb_wallet_name\":  \"支付宝APP\",\n" +
                "                                        \"sqb_zfb_showcase\":  true,\n" +
                "                                        \"sqb_pay_path\":  null\n" +
                "                              },\n" +
                "                              \"payway\":  2,\n" +
                "                              \"version\":  3,\n" +
                "                              \"finish_time\":  1708743119006,\n" +
                "                              \"sub_payway\":  3,\n" +
                "                              \"nfc_card\":  null,\n" +
                "                              \"config_snapshot\":  {\n" +
                "                                        \"vendor_id\":  \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "                                        \"merchant_id\":  \"8c3caa46-3b77-4b0b-af0a-1220ee74b737\",\n" +
                "                                        \"merchant_sn\":  \"1680004797905\",\n" +
                "                                        \"merchant_name\":  \"合肥阳光消化病医院\",\n" +
                "                                        \"merchant_country\":  \"CHN\",\n" +
                "                                        \"currency\":  \"CNY\",\n" +
                "                                        \"longitude\":  \"117.286755\",\n" +
                "                                        \"latitude\":  \"31.814734\",\n" +
                "                                        \"district_code\":  \"340111\",\n" +
                "                                        \"store_id\":  \"486b4855-b3e3-4bd0-90a4-299cec1dc542\",\n" +
                "                                        \"store_sn\":  \"1580000004833733\",\n" +
                "                                        \"store_client_sn\":  null,\n" +
                "                                        \"store_name\":  \"合肥阳光消化病医院\",\n" +
                "                                        \"store_city\":  \"合肥市\",\n" +
                "                                        \"terminal_id\":  \"d2c35781-f46d-4433-a216-236e818f5c4a\",\n" +
                "                                        \"terminal_sn\":  \"1000018749700\",\n" +
                "                                        \"terminal_name\":  \"合肥阳光消化病医院\",\n" +
                "                                        \"terminal_vendor_app_appid\":  \"2019072500001880\",\n" +
                "                                        \"terminal_category\":  108,\n" +
                "                                        \"clearance_provider\":  5,\n" +
                "                                        \"pay_status\":  1,\n" +
                "                                        \"common_switch\":  \"10000000000222222222222222222222\",\n" +
                "                                        \"merchant_daily_max_credit_limit_trans\":  null,\n" +
                "                                        \"merchant_monthly_max_credit_limit_trans\":  null,\n" +
                "                                        \"payway_day_credit_limits\":  null,\n" +
                "                                        \"payway_month_credit_limits\":  null,\n" +
                "                                        \"is_need_refund_fee_flag\":  null,\n" +
                "                                        \"hit_payway\":  null,\n" +
                "                                        \"provider\":  1035,\n" +
                "                                        \"tl_syb_trade_params\":  {\n" +
                "                                                  \"fee_rate\":  \"0.3\",\n" +
                "                                                  \"liquidation_next_day\":  false,\n" +
                "                                                  \"org_id\":  \"660290000000J8Y\",\n" +
                "                                                  \"app_id\":  \"********\",\n" +
                "                                                  \"public_key\":  \"16e5ebb1-7f39-44b5-85a1-49dac60fa4b7\",\n" +
                "                                                  \"private_key\":  \"1ff747fd-565f-4bd8-9104-2d713b1c193d\",\n" +
                "                                                  \"sys_pid\":  \"2088011691288213\",\n" +
                "                                                  \"alipay_sub_mch_id\":  \"****************\",\n" +
                "                                                  \"cus_id\":  \"563361080711FF3\",\n" +
                "                                                  \"fee_rate_tag\":  {\n" +
                "                                                            \"3\":  \"4:\"\n" +
                "                                                  },\n" +
                "                                                  \"active\":  true,\n" +
                "                                                  \"fee\":  814\n" +
                "                                        },\n" +
                "                                        \"term_info\":  {\n" +
                "                                                  \"term_id\":  \"SS002SRR\",\n" +
                "                                                  \"term_type\":  null,\n" +
                "                                                  \"serial_num\":  null\n" +
                "                                        },\n" +
                "                                        \"term_id\":  \"SS002SRR\",\n" +
                "                                        \"channel_name\":  \"上海收钱吧互联网科技股份有限公司\",\n" +
                "                                        \"trade_app\":  \"1\"\n" +
                "                              },\n" +
                "                              \"deleted\":  false,\n" +
                "                              \"effective_amount\":  271310,\n" +
                "                              \"paid_amount\":  271310,\n" +
                "                              \"trade_no\":  \"240224119994684528\",\n" +
                "                              \"channel_finish_time\":  1708743118000,\n" +
                "                              \"order_id\":  \"o****************\",\n" +
                "                              \"items\":  null,\n" +
                "                              \"order_sn\":  \"****************\",\n" +
                "                              \"buyer_uid\":  \"****************\",\n" +
                "                              \"status\":  2000\n" +
                "                    }", Map.class);

        Map<String, Object> transaction = JacksonUtil.toBeanQuietly("{\n" +
                "                              \"subject\":  \"合肥阳光消化病医院\",\n" +
                "                              \"buyer_login\":  \"****************\",\n" +
                "                              \"merchant_id\":  \"8c3caa46-3b77-4b0b-af0a-1220ee74b737\",\n" +
                "                              \"body\":  \"合肥阳光消化病医院\",\n" +
                "                              \"type\":  11,\n" +
                "                              \"mtime\":  1708746122945,\n" +
                "                              \"extended_params\":  null,\n" +
                "                              \"tsn\":  \"7895036431036760\",\n" +
                "                              \"product_flag\":  null,\n" +
                "                              \"operator\":  \"b13b6481-7bc6-4f2f-8a25-e3220d0f6a70\",\n" +
                "                              \"extra_out_fields\":  {\n" +
                "                                        \"payments\":  [\n" +
                "                                                  {\n" +
                "                                                            \"type\":  \"WALLET_ALIPAY\",\n" +
                "                                                            \"origin_type\":  \"ALIPAYACCOUNT\",\n" +
                "                                                            \"amount\":  214319\n" +
                "                                                  },\n" +
                "                                                  {\n" +
                "                                                            \"type\":  \"DISCOUNT_CHANNEL_MCH\",\n" +
                "                                                            \"origin_type\":  \"DISCOUNT\",\n" +
                "                                                            \"amount\":  11\n" +
                "                                                  }\n" +
                "                                        ],\n" +
                "                                        \"trade_no\":  \"2024022422001442931405634910\",\n" +
                "                                        \"combo_id\":  \"4\",\n" +
                "                                        \"order_info\":  {\n" +
                "                                                  \"original_total\":  271310,\n" +
                "                                                  \"effective_total\":  271310,\n" +
                "                                                  \"ctime\":  1708743101578,\n" +
                "                                                  \"trade_no\":  \"240224119994684528\"\n" +
                "                                        }\n" +
                "                              },\n" +
                "                              \"reflect\":  null,\n" +
                "                              \"provider\":  1035,\n" +
                "                              \"original_amount\":  214330,\n" +
                "                              \"ctime\":  1708746121029,\n" +
                "                              \"biz_error_code\":  null,\n" +
                "                              \"id\":  \"t7895036431036760\",\n" +
                "                              \"terminal_id\":  \"2b593ba4-cef8-4093-af01-8be8e7e6ab8c\",\n" +
                "                              \"store_id\":  \"486b4855-b3e3-4bd0-90a4-299cec1dc542\",\n" +
                "                              \"client_tsn\":  \"170874310145892875795-17087461200212210\",\n" +
                "                              \"provider_error_info\":  {\n" +
                "                                        \"refund\":  {\n" +
                "                                                  \"retcode\":  \"SUCCESS\",\n" +
                "                                                  \"retmsg\":  \"\",\n" +
                "                                                  \"trxstatus\":  \"0000\",\n" +
                "                                                  \"errmsg\":  \"\"\n" +
                "                                        }\n" +
                "                              },\n" +
                "                              \"extra_params\":  null,\n" +
                "                              \"payway\":  2,\n" +
                "                              \"version\":  2,\n" +
                "                              \"finish_time\":  1708746122929,\n" +
                "                              \"sub_payway\":  3,\n" +
                "                              \"nfc_card\":  null,\n" +
                "                              \"config_snapshot\":  {\n" +
                "                                        \"vendor_id\":  \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "                                        \"merchant_id\":  \"8c3caa46-3b77-4b0b-af0a-1220ee74b737\",\n" +
                "                                        \"merchant_sn\":  \"1680004797905\",\n" +
                "                                        \"merchant_name\":  \"合肥阳光消化病医院\",\n" +
                "                                        \"merchant_country\":  \"CHN\",\n" +
                "                                        \"currency\":  \"CNY\",\n" +
                "                                        \"longitude\":  \"117.286755\",\n" +
                "                                        \"latitude\":  \"31.814734\",\n" +
                "                                        \"district_code\":  \"340111\",\n" +
                "                                        \"store_id\":  \"486b4855-b3e3-4bd0-90a4-299cec1dc542\",\n" +
                "                                        \"store_sn\":  \"1580000004833733\",\n" +
                "                                        \"store_client_sn\":  null,\n" +
                "                                        \"store_name\":  \"合肥阳光消化病医院\",\n" +
                "                                        \"store_city\":  \"合肥市\",\n" +
                "                                        \"terminal_id\":  \"2b593ba4-cef8-4093-af01-8be8e7e6ab8c\",\n" +
                "                                        \"terminal_sn\":  \"100000330035338302\",\n" +
                "                                        \"terminal_name\":  \"New Term (activateV2) 1706172509932\",\n" +
                "                                        \"terminal_vendor_app_appid\":  \"2016111100000033\",\n" +
                "                                        \"terminal_category\":  106,\n" +
                "                                        \"clearance_provider\":  5,\n" +
                "                                        \"pay_status\":  1,\n" +
                "                                        \"common_switch\":  \"10000000000222222222222222222222\",\n" +
                "                                        \"merchant_daily_max_credit_limit_trans\":  null,\n" +
                "                                        \"merchant_monthly_max_credit_limit_trans\":  null,\n" +
                "                                        \"payway_day_credit_limits\":  null,\n" +
                "                                        \"payway_month_credit_limits\":  null,\n" +
                "                                        \"is_need_refund_fee_flag\":  null,\n" +
                "                                        \"hit_payway\":  null,\n" +
                "                                        \"provider\":  1035,\n" +
                "                                        \"tl_syb_trade_params\":  {\n" +
                "                                                  \"fee_rate\":  \"0.3\",\n" +
                "                                                  \"liquidation_next_day\":  false,\n" +
                "                                                  \"org_id\":  \"660290000000J8Y\",\n" +
                "                                                  \"app_id\":  \"********\",\n" +
                "                                                  \"public_key\":  \"16e5ebb1-7f39-44b5-85a1-49dac60fa4b7\",\n" +
                "                                                  \"private_key\":  \"1ff747fd-565f-4bd8-9104-2d713b1c193d\",\n" +
                "                                                  \"sys_pid\":  \"2088011691288213\",\n" +
                "                                                  \"alipay_sub_mch_id\":  \"****************\",\n" +
                "                                                  \"cus_id\":  \"563361080711FF3\",\n" +
                "                                                  \"fee_rate_tag\":  {\n" +
                "                                                            \"3\":  \"4:\"\n" +
                "                                                  },\n" +
                "                                                  \"active\":  true,\n" +
                "                                                  \"fee\":  643\n" +
                "                                        },\n" +
                "                                        \"term_info\":  {\n" +
                "                                                  \"term_id\":  \"SS002SRR\",\n" +
                "                                                  \"term_type\":  null,\n" +
                "                                                  \"serial_num\":  null\n" +
                "                                        },\n" +
                "                                        \"term_id\":  \"SS002SRR\",\n" +
                "                                        \"channel_name\":  \"上海收钱吧互联网科技股份有限公司\",\n" +
                "                                        \"trade_app\":  \"1\"\n" +
                "                              },\n" +
                "                              \"deleted\":  false,\n" +
                "                              \"effective_amount\":  214330,\n" +
                "                              \"trade_no\":  \"240224115294724240\",\n" +
                "                              \"channel_finish_time\":  1708746122000,\n" +
                "                              \"order_id\":  \"o****************\",\n" +
                "                              \"items\":  null,\n" +
                "                              \"order_sn\":  \"****************\",\n" +
                "                              \"buyer_uid\":  \"****************\",\n" +
                "                              \"status\":  2000\n" +
                "                    }", Map.class);
        Map<String, Object> sybResult = JacksonUtil.toBeanQuietly("{\n" +
                "          \"acct\":  \"****************\",\n" +
                "          \"accttype\":  \"99\",\n" +
                "          \"appid\":  \"********\",\n" +
                "          \"bankcode\":  \"DISCOUNT\",\n" +
                "          \"chnldata\":  \"{\\\"voucher_detail_list\\\":\\\"[{\\\\\\\"amount\\\\\\\":\\\\\\\"0.14\\\\\\\",\\\\\\\"merchantContribute\\\\\\\":\\\\\\\"0.00\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"实体店通用券包\\\\\\\",\\\\\\\"otherContribute\\\\\\\":\\\\\\\"0.14\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"ALIPAY_CASH_VOUCHER\\\\\\\",\\\\\\\"voucherId\\\\\\\":\\\\\\\"202402240007300293420MVTYCT6\\\\\\\"}]\\\",\\\"discount_amount\\\":\\\"0.14\\\",\\\"fund_bill_list\\\":\\\"[{\\\\\\\"amount\\\\\\\":\\\\\\\"2712.96\\\\\\\",\\\\\\\"fundChannel\\\\\\\":\\\\\\\"ALIPAYACCOUNT\\\\\\\"},{\\\\\\\"amount\\\\\\\":\\\\\\\"0.14\\\\\\\",\\\\\\\"fundChannel\\\\\\\":\\\\\\\"DISCOUNT\\\\\\\"}]\\\"}\",\n" +
                "          \"chnltrxid\":  \"2024022422001442931405634910\",\n" +
                "          \"cmid\":  \"****************\",\n" +
                "          \"cusid\":  \"563361080711FF3\",\n" +
                "          \"fee\":  \"814\",\n" +
                "          \"fintime\":  \"**************\",\n" +
                "          \"initamt\":  \"271310\",\n" +
                "          \"randomstr\":  \"************\",\n" +
                "          \"reqsn\":  \"****************\",\n" +
                "          \"retcode\":  \"SUCCESS\",\n" +
                "          \"sign\":  \"EchZNcLXe937S6FGVo2WTL+rDPsFgr4OiZ4jZrWmpDwzMaVm/IBv06byMvMVcmRUteMXpLiYxWgtkO24UWKuTw==\",\n" +
                "          \"trxamt\":  \"271310\",\n" +
                "          \"trxcode\":  \"VSP511\",\n" +
                "          \"trxid\":  \"240224119994684528\",\n" +
                "          \"trxstatus\":  \"0000\"\n" +
                "}", Map.class);
        Field resolveRefundFundMap = TLSybServiceProvider.class.getDeclaredField("resolveRefundFundMap");

        resolveRefundFundMap.setAccessible(true);
        Map<Integer, Object> resolveRefundMap = (Map<Integer, Object>) resolveRefundFundMap.get(null);
        Object resolveRefund = resolveRefundMap.get(2);
        Method resolveRefundFund = resolveRefund.getClass().getMethod("resolveRefundFund", Map.class, TransactionContext.class, Map.class);
        resolveRefundFund.setAccessible(true);
        resolveRefundFund.invoke(resolveRefund, sybResult, new TransactionContext(order, transaction), payTransaction);
        assert MapUtil.getLongValue(transaction, "paid_amount") == 271310;
    }

    @SneakyThrows
    @Test
    public void resolveWXPayFund() {
        Map<String, Object> order = MapUtil.hashMap(Order.ORIGINAL_TOTAL, 143559l);

        Field resolvePayFundMap = TLSybServiceProvider.class.getDeclaredField("resolvePayFundMap");
        resolvePayFundMap.setAccessible(true);
        Map<Integer, Object> resolveRefundMap = (Map<Integer, Object>) resolvePayFundMap.get(null);
        Object resolvePayFundObj = resolveRefundMap.get(3);
        Method resolvePayFund = resolvePayFundObj.getClass().getMethod("resolvePayFund", Map.class, TransactionContext.class);

        String resultStr = "{\"acct\":\"oGFfks3II00TjNCFCjJC7jZmnX4w\",\"accttype\":\"02\",\"appid\":\"********\",\"bankcode\":\"GDB_CREDIT\",\"chnldata\":\"{\\\"promotion_detail\\\":\\\"[{\\\\\\\"amount\\\\\\\":300,\\\\\\\"scope\\\\\\\":\\\\\\\"GLOBAL\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"广发信用卡专享\\\\\\\",\\\\\\\"activity_id\\\\\\\":\\\\\\\"********\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"COUPON\\\\\\\"}]\\\"}\",\"chnlid\":\"*********\",\"chnltrxid\":\"4200002177202406289608378197\",\"cmid\":\"*********\",\"cusid\":\"5633020581209U0\",\"fee\":\"546\",\"fintime\":\"**************\",\"initamt\":\"143559\",\"randomstr\":\"************\",\"reqsn\":\"****************\",\"retcode\":\"SUCCESS\",\"sign\":\"avUf3NRKqZDCssPskok3x+DmM9Fug3ZWYoJbmeRivF3ASY8OvaHBc3CWKeAWLz0vXk7f0P/0fm1u4V8KnVFT/A==\",\"trxamt\":\"143559\",\"trxcode\":\"VSP501\",\"trxid\":\"240628117409836011\",\"trxstatus\":\"0000\"}";
        String payTransactionStr = "{\"subject\":\"网红鸡蛋灌饼无锡中山路店\",\"received_amount\":143559,\"buyer_login\":\"oGFfks3II00TjNCFCjJC7jZmnX4w\",\"merchant_id\":\"6b87f913-fa36-40ab-9b6c-413d91c21b24\",\"body\":\"网红鸡蛋灌饼无锡中山路店\",\"type\":30,\"mtime\":*************,\"extended_params\":{},\"tsn\":\"****************\",\"product_flag\":null,\"operator\":\"QRCODE:24041602564234558179\",\"extra_out_fields\":{\"combo_id\":\"30\",\"wap_pay_request\":{\"package\":\"prepay_id=wx2801490164244876456915194f8afc0001\",\"appId\":\"wx72534f3638c59073\"},\"trade_no\":\"4200002177202406289608378197\",\"wallet_account_type\":1},\"reflect\":null,\"provider\":1035,\"original_amount\":143559,\"ctime\":*************,\"biz_error_code\":{},\"id\":\"t****************\",\"terminal_id\":\"786a2985-98fc-4ab9-9124-44366b00890b\",\"store_id\":\"********-8a82-42a3-817a-8bf22c7b5f70\",\"client_tsn\":\"171951054119677898065\",\"provider_error_info\":{\"query\":{\"retcode\":\"SUCCESS\",\"retmsg\":\"\",\"trxstatus\":\"0000\",\"errmsg\":\"\"},\"precreate\":{\"retcode\":\"SUCCESS\",\"retmsg\":\"\",\"trxstatus\":\"0000\",\"errmsg\":\"\"}},\"extra_params\":{\"payer_uid\":\"oGFfks3II00TjNCFCjJC7jZmnX4w\",\"client_ip\":\"*************\",\"sqb_wallet_name\":\"微信APP\",\"sqb_pay_path\":null},\"payway\":3,\"version\":3,\"finish_time\":*************,\"sub_payway\":3,\"nfc_card\":null,\"config_snapshot\":{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"6b87f913-fa36-40ab-9b6c-413d91c21b24\",\"merchant_sn\":\"*************\",\"merchant_name\":\"网红鸡蛋灌饼无锡中山路店\",\"merchant_country\":\"CHN\",\"currency\":\"CNY\",\"longitude\":\"120.288475\",\"latitude\":\"31.592158\",\"district_code\":\"320213\",\"store_id\":\"********-8a82-42a3-817a-8bf22c7b5f70\",\"store_sn\":\"1580000006222312\",\"store_client_sn\":null,\"store_name\":\"网红鸡蛋灌饼无锡中山路店\",\"store_city\":\"无锡市\",\"terminal_id\":\"786a2985-98fc-4ab9-9124-44366b00890b\",\"terminal_sn\":\"1000037354811\",\"terminal_name\":\"网红鸡蛋灌饼无锡中山路店\",\"terminal_vendor_app_appid\":\"2019072500001880\",\"terminal_category\":108,\"clearance_provider\":5,\"pay_status\":1,\"common_switch\":\"00000000000202222222222222222222\",\"merchant_daily_max_credit_limit_trans\":null,\"merchant_monthly_max_credit_limit_trans\":null,\"union_over_seas_wallet_single_tran_limit\":500,\"union_over_seas_wallet_day_tran_limit\":10000,\"payway_day_credit_limits\":null,\"payway_month_credit_limits\":null,\"is_need_refund_fee_flag\":null,\"hit_payway\":null,\"provider\":1035,\"tl_syb_trade_params\":{\"fee_rate\":\"0.38\",\"liquidation_next_day\":false,\"channel_id\":\"*********\",\"org_id\":\"660290000000J8Y\",\"app_id\":\"********\",\"public_key\":\"16e5ebb1-7f39-44b5-85a1-49dac60fa4b7\",\"private_key\":\"1ff747fd-565f-4bd8-9104-2d713b1c193d\",\"weixin_sub_appid\":\"wx72534f3638c59073\",\"weixin_sub_appsecret\":\"03dc3555893ef91f82088aedea39313a\",\"weixin_mini_sub_appid\":\"\",\"weixin_mini_sub_appsecret\":\"\",\"weixin_sub_mch_id\":\"*********\",\"cus_id\":\"5633020581209U0\",\"fee_rate_tag\":{\"3\":\"30:\"},\"active\":true,\"fee\":546},\"term_info\":{\"term_id\":\"ST00YFzV\",\"term_type\":null,\"serial_num\":null},\"term_id\":\"ST00YFzV\",\"channel_name\":\"上海收钱吧互联网科技股份有限公司\",\"trade_app\":\"1\"},\"deleted\":false,\"effective_amount\":143559,\"paid_amount\":143559,\"trade_no\":\"240628117409836011\",\"channel_finish_time\":1719510560000,\"order_id\":\"o****************\",\"items\":null,\"order_sn\":\"****************\",\"buyer_uid\":\"oGFfks3II00TjNCFCjJC7jZmnX4w\",\"status\":2000}";

        Map result = JacksonUtil.toBeanQuietly(resultStr, Map.class);
        Map transaction = JacksonUtil.toBeanQuietly(payTransactionStr, Map.class);
        resolvePayFund.setAccessible(true);
        resolvePayFund.invoke(resolvePayFundObj, result, new TransactionContext(order, transaction));
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        assert payments.stream().filter(o -> MapUtil.getLongValue(o, Payment.AMOUNT) == 300).findAny().isPresent();
    }


    @SneakyThrows
    @Test
    public void resolveWXPayFund2() {
        Map<String, Object> order = MapUtil.hashMap(Order.ORIGINAL_TOTAL, 1080l);

        Field resolvePayFundMap = TLSybServiceProvider.class.getDeclaredField("resolvePayFundMap");
        resolvePayFundMap.setAccessible(true);
        Map<Integer, Object> resolveRefundMap = (Map<Integer, Object>) resolvePayFundMap.get(null);
        Object resolvePayFundObj = resolveRefundMap.get(3);
        Method resolvePayFund = resolvePayFundObj.getClass().getMethod("resolvePayFund", Map.class, TransactionContext.class);

        String resultStr = "{\"acct\":\"ofDgL0ZVLfbcfQwnma13VpJUZ05I\",\"accttype\":\"99\",\"appid\":\"********\",\"bankcode\":\"ICBC_DEBIT\",\"chnldata\":\"{\\\"promotion_detail\\\":\\\"[{\\\\\\\"amount\\\\\\\":300,\\\\\\\"scope\\\\\\\":\\\\\\\"GLOBAL\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"宁波工行悦享回归礼\\\\\\\",\\\\\\\"activity_id\\\\\\\":\\\\\\\"********\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"COUPON\\\\\\\"}]\\\"}\",\"chnlid\":\"*********\",\"chnltrxid\":\"4200002212202406287794141193\",\"cmid\":\"*********\",\"cusid\":\"563332058122CHC\",\"fee\":\"4\",\"fintime\":\"**************\",\"initamt\":\"1080\",\"randomstr\":\"************\",\"reqsn\":\"****************\",\"retcode\":\"SUCCESS\",\"sign\":\"b3eHVt8tOzbOoDvvtgSYpDBXbbb/Va/DD1yw4w1KsZQ44t8+F9jQTM50dqavu48S3YkEBo045MRgeSVdfjcDmg==\",\"trxamt\":\"1080\",\"trxcode\":\"VSP501\",\"trxid\":\"240628129490753163\",\"trxstatus\":\"0000\"}";
        String payTransactionStr = "{\"subject\":\"宁波市江北区勤主家饺子馆\",\"received_amount\":1080,\"buyer_login\":\"ofDgL0ZVLfbcfQwnma13VpJUZ05I\",\"merchant_id\":\"07370c43-c491-4c3f-9976-1bd1ba1cb93d\",\"body\":null,\"type\":30,\"mtime\":*************,\"extended_params\":{\"attach\":\"OrderSource=FoodOrder\",\"sub_appid\":\"wxccbcac9a3ece5112\",\"scene_info\":\"{\\\"store_info\\\":{\\\"id\\\":\\\"****************\\\"}}\"},\"tsn\":\"****************\",\"product_flag\":\"a1,ab,d7,a4,a9,aj,c1\",\"operator\":null,\"extra_out_fields\":{\"combo_id\":\"30\",\"wap_pay_request\":{\"package\":\"prepay_id=wx281113319990642640238e371a9d120000\",\"appId\":\"wxccbcac9a3ece5112\"},\"trade_no\":\"4200002212202406287794141193\",\"wallet_account_type\":1},\"reflect\":null,\"provider\":1035,\"original_amount\":1200,\"ctime\":*************,\"biz_error_code\":{},\"id\":\"t****************\",\"terminal_id\":\"9cc368d5-0af8-45c2-b70b-3872b90c6211\",\"store_id\":\"d2e87c83-48fe-4e86-9e9c-4b07a97fd423\",\"client_tsn\":\"2024062811133118945\",\"provider_error_info\":{\"query\":{\"retcode\":\"SUCCESS\",\"retmsg\":\"\",\"trxstatus\":\"0000\",\"errmsg\":\"\"},\"precreate\":{\"retcode\":\"SUCCESS\",\"retmsg\":\"\",\"trxstatus\":\"0000\",\"errmsg\":\"\"}},\"extra_params\":{\"payer_uid\":\"ofDgL0ZVLfbcfQwnma13VpJUZ05I\",\"notify_url\":\"http://upay-acquiring.vpc.shouqianba.com/acquiring/callback/channel/2024062811133118945\",\"client_ip\":\"***************\",\"sqb_scene\":\"ufood,market_program_discount,mini,istore_order,i_store,acquiring_biz\",\"sqb_refund_flag\":\"smart\",\"sqb_voice\":\"2\",\"sqb_inner_biz\":\"acquiring_biz\",\"sqb_activity_biz_ext\":{\"acquiringInfo\":{\"tradeApp\":\"2\",\"acquiringSn\":\"2024062811133102207\",\"acquiringAmount\":1200,\"acquiringAmountComposition\":{\"compositionItems\":[{\"amount\":1200,\"category\":\"default\"}]},\"usingPayTools\":[{\"id\":3,\"payTool\":3,\"amount\":1200,\"amountComposition\":{\"compositionItems\":[{\"amount\":1200,\"category\":\"default\"}]}}]}},\"sqb_biz_model\":\"dine_in\",\"sqb_product_flag\":\"a4,a1\",\"sqb_user_id\":\"19bfca78-9b57-11ec-9700-7cd30ae005e6\",\"profit_sharing\":{\"receivers\":[{\"id\":\"r1800003959\",\"model_id\":\"m1800000793\",\"charge_flag\":\"smart_order\",\"use_charge_config_type\":2,\"receiver_params\":{\"diningHallActivity\":false,\"ufoodFreeFeeActivity\":false,\"chargeType\":0,\"freeOrderAmount\":0,\"merchantMpActivity\":false,\"freeOrderCount\":0,\"ufoodActivity\":false},\"client_sn\":\"207\",\"sharing_amount\":2,\"ratio\":\"0.22%\",\"base_amount\":1080,\"service_fee_id\":\"28\",\"amount\":2}],\"sharing_flag\":\"1\",\"reserve_ratio\":null,\"model_id\":\"m1800000793\",\"sharing_notify_url\":\"http://market-trade.internal.shouqianba.com/hook/profitSharing\",\"sharing_type\":\"3\"}},\"payway\":3,\"version\":4,\"finish_time\":1719544423300,\"sub_payway\":4,\"nfc_card\":null,\"config_snapshot\":{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"07370c43-c491-4c3f-9976-1bd1ba1cb93d\",\"merchant_sn\":\"1680005697503\",\"merchant_name\":\"宁波市江北区勤主家饺子馆\",\"merchant_country\":\"CHN\",\"currency\":\"CNY\",\"longitude\":\"121.610853\",\"latitude\":\"29.913541\",\"district_code\":\"330205\",\"store_id\":\"d2e87c83-48fe-4e86-9e9c-4b07a97fd423\",\"store_sn\":\"****************\",\"store_client_sn\":null,\"store_name\":\"宁波市江北区勤主家饺子馆\",\"store_city\":\"宁波市\",\"terminal_id\":\"9cc368d5-0af8-45c2-b70b-3872b90c6211\",\"terminal_sn\":\"1000025150889\",\"terminal_name\":\"前台\",\"terminal_vendor_app_appid\":\"2021080300004112\",\"terminal_category\":null,\"clearance_provider\":5,\"pay_status\":1,\"sharing_switch\":1,\"common_switch\":\"01000000000202222222222222222222\",\"merchant_daily_max_credit_limit_trans\":null,\"merchant_monthly_max_credit_limit_trans\":null,\"union_over_seas_wallet_single_tran_limit\":500,\"union_over_seas_wallet_day_tran_limit\":10000,\"payway_day_credit_limits\":null,\"payway_month_credit_limits\":null,\"is_need_refund_fee_flag\":null,\"hit_payway\":null,\"provider\":1035,\"tl_syb_trade_params\":{\"fee_rate\":\"0.38\",\"liquidation_next_day\":false,\"channel_id\":\"*********\",\"org_id\":\"660290000000J8Y\",\"app_id\":\"********\",\"public_key\":\"16e5ebb1-7f39-44b5-85a1-49dac60fa4b7\",\"private_key\":\"1ff747fd-565f-4bd8-9104-2d713b1c193d\",\"weixin_sub_appid\":\"wx72534f3638c59073\",\"weixin_sub_appsecret\":\"03dc3555893ef91f82088aedea39313a\",\"weixin_mini_sub_appid\":\"\",\"weixin_mini_sub_appsecret\":\"\",\"weixin_sub_mch_id\":\"*********\",\"cus_id\":\"563332058122CHC\",\"fee_rate_tag\":{\"4\":\"30:\"},\"active\":true,\"fee\":4},\"term_info\":{\"term_id\":\"ST0093pK\",\"term_type\":null,\"serial_num\":null},\"term_id\":\"ST0093pK\",\"channel_name\":\"上海收钱吧互联网科技股份有限公司\",\"trade_app\":\"2\"},\"deleted\":false,\"effective_amount\":1080,\"paid_amount\":1080,\"trade_no\":\"240628129490753163\",\"channel_finish_time\":1719544423000,\"order_id\":\"o****************\",\"items\":{\"payments\":[{\"type\":\"DISCOUNT_WOSAI_MCH\",\"amount\":120,\"source\":\"2601608023\",\"origin_type\":\"item_activity\",\"origin_name\":\"单品优惠\"}]},\"order_sn\":\"****************\",\"buyer_uid\":\"ofDgL0ZVLfbcfQwnma13VpJUZ05I\",\"status\":2000}";

        Map result = JacksonUtil.toBeanQuietly(resultStr, Map.class);
        Map transaction = JacksonUtil.toBeanQuietly(payTransactionStr, Map.class);
        resolvePayFund.setAccessible(true);
        resolvePayFund.invoke(resolvePayFundObj, result, new TransactionContext(order, transaction));
        Map extraOutFields = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        List<Map<String, Object>> payments = (List<Map<String, Object>>) extraOutFields.get(Transaction.PAYMENTS);

        assert payments.stream().filter(o -> MapUtil.getLongValue(o, Payment.AMOUNT) == 300).findAny().isPresent();
    }

}
