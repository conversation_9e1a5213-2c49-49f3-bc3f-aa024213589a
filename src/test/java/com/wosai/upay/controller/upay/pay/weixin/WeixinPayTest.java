package com.wosai.upay.controller.upay.pay.weixin;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.SharingBook;
import com.wosai.profit.sharing.model.SharingTransaction;
import com.wosai.profit.sharing.model.response.BeforeSharingRestituteResult;
import com.wosai.profit.sharing.model.response.SharingRestituteResult;
import com.wosai.profit.sharing.service.SharingService;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.ProfitSharing;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.ProfitUtil;
import com.wosai.upay.util.SupportUtil;

public class WeixinPayTest extends BaseTestController{
    @MockBean
    SharingService sharingService;

    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById("****weixin_cert_config_key****", null);
    }
    
    /**
     * 
     * 微信通道返回trade_error，部分错误码直接置位pay_canceled
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_barcode_pay_canceled()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        Object[] errCodeDescs = WeixinConstants.TRADE_ERROR_FAIL_MESSAGE.toArray();
        int next = new Random().nextInt(errCodeDescs.length -1);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                "    <sub_appid><![CDATA[wxf9114ca5d9b45bd5]]></sub_appid>\n" + 
                "    <mch_id><![CDATA[1461554302]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[239435625]]></sub_mch_id>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                "    <result_code><![CDATA[FAIL]]></result_code>\n" + 
                "    <err_code><![CDATA[TRADE_ERROR]]></err_code>\n" + 
                "    <err_code_des><![CDATA[" + errCodeDescs[next] + "]]></err_code_des>\n" + 
                "    <nonce_str><![CDATA[e8316ba70123462b90b3e07db864fdc7]]></nonce_str>\n" + 
                "    <sign><![CDATA[lArTd9XJSZ8u7OjlYBDVtSCrcrVKzrUxZewvxsKiSAgNd8diB7Ba3y9nRiJDVw7X+yZijPx0ZoehAmQlWutj50cvleZB6qpf8Mdn28SL6CrgptSHqAyt6/M4FoepwRM/DQ35F+qb+cxipkAmzd8Rphh4nmtni8yeZOs2usGl2TPf2FyeuBXa2P6f53oJsgy+sZMqFKhmUb4oi5PeCivW9uWDhU0UUzs0yTHZAhJxxpd4BHVDmr2R2ocXDifIosmca/cnPBLsbisuiky9IfXhdUbn4RCite+a8jEyI4wCFzybCHHLVvNyPTMhGDv8dr9H1MwSZZ4ZnhR3GDzR5RgrIg==]]></sign>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_weixin_barcode_pay_canceled", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_FAIL,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_FAIL_CANCELED).getName(),
                        "biz_response.data.order_status", Order.Status.PAY_CANCELED.name()
                );

    }

    /**
     *
     * 微信通道返回success
     *
     * @throws Exception
     */
    @Test
    public void test_weixin_barcode_pay_success()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payReturnXml = "<xml>\n" +
                "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "  <total_fee>30</total_fee>\n" +
                "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                "  <attach><![CDATA[]]></attach>\n" +
                "  <time_end><![CDATA[**************]]></time_end>\n" +
                "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                "  <cash_fee>30</cash_fee>\n" +
                "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                "  <version><![CDATA[1.0]]></version>\n" +
                "  <promotion_detail><![CDATA[{}]]></promotion_detail>\n" +
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(payReturnXml);
        String resultString = postPerform("test_weixin_barcode_pay_success", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }

    /**
    *
    * 微信通道返回success
    *
    * @throws Exception
    */
   @Test
   public void test_weixin_barcode_pay_success_with_query()throws Exception{
       JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
       PowerMockito.mockStatic(HttpClientUtils.class);
       PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
           String serviceUrl = invocation.getArgument(3);
           if(serviceUrl.contains("/pay/micropay")) {
               return  "<xml>\n" + 
                       "  <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                       "  <return_msg><![CDATA[OK]]></return_msg>\n" + 
                       "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" + 
                       "  <mch_id><![CDATA[**********]]></mch_id>\n" + 
                       "  <sub_mch_id><![CDATA[1526731161]]></sub_mch_id>\n" + 
                       "  <nonce_str><![CDATA[1h3KLlihT64bxkuu]]></nonce_str>\n" + 
                       "  <sign><![CDATA[C46E6820117D5087081DAD164895BC9D]]></sign>\n" + 
                       "  <result_code><![CDATA[FAIL]]></result_code>\n" + 
                       "  <err_code><![CDATA[USERPAYING]]></err_code>\n" + 
                       "  <err_code_des><![CDATA[需要用户输入支付密码]]></err_code_des>\n" + 
                       "</xml>";
           }else if(serviceUrl.contains("/pay/orderquery")) {
               return  "<xml>\n" + 
                       "  <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                       "  <return_msg><![CDATA[OK]]></return_msg>\n" + 
                       "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" + 
                       "  <mch_id><![CDATA[**********]]></mch_id>\n" + 
                       "  <sub_mch_id><![CDATA[1526731161]]></sub_mch_id>\n" + 
                       "  <nonce_str><![CDATA[FpajbxzApRjQ3Cbx]]></nonce_str>\n" + 
                       "  <sign><![CDATA[61DDF4CDC0FFD5E29635CB9FACD7B4B0]]></sign>\n" + 
                       "  <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                       "  <openid><![CDATA[oGFfks_i0MGMLo-SnhjAK1bxy6I0]]></openid>\n" + 
                       "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" + 
                       "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" + 
                       "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" + 
                       "  <total_fee>30</total_fee>\n" + 
                       "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                       "  <transaction_id><![CDATA[4200000681202008318166484286]]></transaction_id>\n" + 
                       "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                       "  <attach><![CDATA[]]></attach>\n" + 
                       "  <time_end><![CDATA[**************]]></time_end>\n" + 
                       "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                       "  <cash_fee>30</cash_fee>\n" + 
                       "  <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                       "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                       "  <version><![CDATA[1.0]]></version>\n" + 
                       "  <promotion_detail><![CDATA[]]></promotion_detail>\n" + 
                       "</xml>";
           }
          
           return "fail";
       });

       String resultString = postPerform("test_weixin_barcode_pay_success_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                       "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                       "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                       "biz_response.data.order_status", Order.Status.CREATED.name()
               );
       
       Thread.sleep(5000);
       resultString = postPerform("test_weixin_barcode_pay_success_with_query_1_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       result = JsonUtil.jsonStrToObject(resultString, Map.class);
       
       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                       "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                       "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                       "biz_response.data.order_status", Order.Status.PAID.name()
               );
   }

    /**
     *
     * 用例名称：分账交易退款上送分账信息
     *
     * 期望结果：上送的分账信息生效
     *
     * @throws Exception
     */
    @Test
    public void testRefundProfitSharing() throws Exception{
        //mock 分账参数校验
        PowerMockito.when(sharingService.validateAndUpdateProfitSharing(Mockito.any())).thenReturn(ProfitUtil.PROFIT_SHARING);
        //mock 退款成功次数为0
        PowerMockito.when(sharingService.getSharingTransactionSuccessCount(Mockito.anyList())).thenAnswer(mi -> 0);
        //mock 退款分账前置校验 可回退
        PowerMockito.when(sharingService.getInfoBeforeSharingRestitute(Mockito.any())).thenAnswer(mi -> {
            BeforeSharingRestituteResult result = new BeforeSharingRestituteResult();
            result.setRestitute(true);
            Map<String,Object> refundProfitSharing = JsonUtil.jsonStringToObject(JsonUtil.objectToJsonString(ProfitUtil.REFUND_PROFIT_SHARING), Map.class);
            ((List<Map<String,Object>>)refundProfitSharing.get(ProfitSharing.RECEIVERS)).forEach(r -> r.put(ProfitSharing.RECEIVER_AMOUNT, MapUtil.getString(r, ProfitSharing.RECEIVER_SHARING_AMOUNT)));
            result.setProfitSharing(refundProfitSharing);
            return result;
        });
        //mock 分账回退 分账成功
        SharingRestituteResult sharingRestituteResult = new SharingRestituteResult();
        sharingRestituteResult.setSharingRestituteStatus(SharingTransaction.STATUS_SUCCESS);
        List<SharingBook> sharingBookInfo = new ArrayList<SharingBook>();
        sharingRestituteResult.setSharingBooks(sharingBookInfo);
        
        ((List<Map<String,Object>>)ProfitUtil.PROFIT_SHARING.get(ProfitSharing.RECEIVERS)).forEach(r -> {
            SharingBook book = new SharingBook();
            book.setId(MapUtil.getString(r, "id"));
            book.setAmount(MapUtil.getLong(r, "sharing_amount"));
            book.setClientSn(MapUtil.getString(r, "client_sn"));
            sharingBookInfo.add(book);
        });
        
        PowerMockito.when(sharingService.sharingRestitute(Mockito.any())).thenReturn(sharingRestituteResult);

        //支付
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        request.put(UpayService.PROFIT_SHARING, ProfitUtil.PROFIT_SHARING);
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payReturnXml = "<xml>\n" +
                "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "  <total_fee>30</total_fee>\n" +
                "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                "  <attach><![CDATA[]]></attach>\n" +
                "  <time_end><![CDATA[**************]]></time_end>\n" +
                "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                "  <cash_fee>30</cash_fee>\n" +
                "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                "  <version><![CDATA[1.0]]></version>\n" +
                "  <promotion_detail><![CDATA[{}]]></promotion_detail>\n" +
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(payReturnXml);
        String resultString = postPerform("test_refund_profit_sharing_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        Assert.assertTrue(UpayProfitSharingUtil.isPayProfitSharingTransaction(transaction));
        //退款
        PowerMockito.mockStatic(HttpClientUtils.class);
        String refundReturnXml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "\n" +
                "<xml>\n" +
                "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                "  <appid><![CDATA[wx42f6886cbxbb3fdbc]]></appid>\n" +
                "  <mch_id><![CDATA[12383x13502]]></mch_id>\n" +
                "  <sub_mch_id><![CDATA[1x522166841]]></sub_mch_id>\n" +
                "  <nonce_str><![CDATA[ouEyYquRuaRiBH7I]]></nonce_str>\n" +
                "  <sign><![CDATA[536AA91AE3E6F2EBE7F6DB453ECBF0F1]]></sign>\n" +
                "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "  <transaction_id><![CDATA[42000005x79202007131403064342]]></transaction_id>\n" +
                "  <out_trade_no><![CDATA[78952x36647434156]]></out_trade_no>\n" +
                "  <out_refund_no><![CDATA[78952x36649348979]]></out_refund_no>\n" +
                "  <refund_id><![CDATA[503006048220x20071301481299457]]></refund_id>\n" +
                "  <refund_channel><![CDATA[]]></refund_channel>\n" +
                "  <refund_fee>1500</refund_fee>\n" +
                "  <coupon_refund_fee>0</coupon_refund_fee>\n" +
                "  <total_fee>1500</total_fee>\n" +
                "  <cash_fee>1500</cash_fee>\n" +
                "  <coupon_refund_count>0</coupon_refund_count>\n" +
                "  <cash_refund_fee>1500</cash_refund_fee>\n" +
                "</xml>";
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(refundReturnXml);
        request.put(UpayService.PROFIT_SHARING, ProfitUtil.REFUND_PROFIT_SHARING);
        request.put("refund_request_no", (System.currentTimeMillis() + ThreadLocalRandom.current().nextInt(999)) + "");
        request.put("refund_amount", "15");
        String refundResultString = postPerform("test_refund_profit_sharing_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map refundResult = JsonUtil.jsonStrToObject(refundResultString, Map.class);
        assertEquals(refundResult, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PARTIAL_REFUNDED.name()
        );
        Map refundTransaction = dataRepository.getLatestTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        Assert.assertTrue(UpayProfitSharingUtil.isPayProfitSharingTransaction(refundTransaction));
        Map<String,Object> extraParams = MapUtil.getMap(refundTransaction, com.wosai.profit.sharing.model.upay.Transaction.EXTRA_PARAMS);
        Map<String,Object> profitSharing = (Map<String, Object>) MapUtil.getMap(extraParams, com.wosai.profit.sharing.model.upay.Transaction.PROFIT_SHARING);
        ((List<Map<String,Object>>)profitSharing.get(ProfitSharing.RECEIVERS)).forEach(r -> {
            Assert.assertNotNull(MapUtil.getString(r, ProfitSharing.RECEIVER_AMOUNT));
        });
    }
    
    @Test
    public void test_refund()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/pay/micropay")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[{}]]></promotion_detail>\n" +
                        "</xml>";
            }else if(serviceUrl.contains("/secapi/pay/refund")) {
                return  "<xml>\n" + 
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" + 
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" + 
                        "  <sub_mch_id><![CDATA[1522166361]]></sub_mch_id>\n" + 
                        "  <nonce_str><![CDATA[qmxTpuXSTmeIeO27]]></nonce_str>\n" + 
                        "  <sign><![CDATA[7C0EB1718D55930E47C30BD8DD95DA25]]></sign>\n" + 
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "  <transaction_id><![CDATA[4200000681202008313611996810]]></transaction_id>\n" + 
                        "  <out_trade_no><![CDATA[7895237703897703]]></out_trade_no>\n" + 
                        "  <out_refund_no><![CDATA[7895237703814329]]></out_refund_no>\n" + 
                        "  <refund_id><![CDATA[50300205452020083102425677690]]></refund_id>\n" + 
                        "  <refund_channel><![CDATA[]]></refund_channel>\n" + 
                        "  <refund_fee>30</refund_fee>\n" + 
                        "  <coupon_refund_fee>0</coupon_refund_fee>\n" + 
                        "  <total_fee>30</total_fee>\n" + 
                        "  <cash_fee>30</cash_fee>\n" + 
                        "  <coupon_refund_count>0</coupon_refund_count>\n" + 
                        "  <cash_refund_fee>30</cash_refund_fee>\n" + 
                        "</xml>";
            }
           
            return "fail";
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    } 
    
    @Test
    public void test_cancel()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/pay/micropay")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[{}]]></promotion_detail>\n" +
                        "</xml>";
            }else if(serviceUrl.contains("/secapi/pay/reverse")) {
                return  "<xml>\n" + 
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" + 
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" + 
                        "  <sub_mch_id><![CDATA[1315093701]]></sub_mch_id>\n" + 
                        "  <nonce_str><![CDATA[5vci9TXqjcCIyqXh]]></nonce_str>\n" + 
                        "  <sign><![CDATA[918B3AE8E7974DD49A841199E2C45C28]]></sign>\n" + 
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "  <recall><![CDATA[N]]></recall>\n" + 
                        "</xml>\n" + 
                        "";
            }
           
            return "fail";
        });
        
        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
    
    /**
     * 
     * 微信直连返回渠道信息
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_provider_response()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        request.put(UpayService.TOTAL_AMOUNT, "6390");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n"
                + "  <return_code><![CDATA[SUCCESS]]></return_code>\n"
                + "  <return_msg><![CDATA[OK]]></return_msg>\n"
                + "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n"
                + "  <mch_id><![CDATA[**********]]></mch_id>\n"
                + "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n"
                + "  <device_info><![CDATA[W029]]></device_info>\n"
                + "  <nonce_str><![CDATA[0ooN2XylMbQ6wH7B]]></nonce_str>\n"
                + "  <sign><![CDATA[0AEF4077D31E6382B08F3EDDBC8F938D]]></sign>\n"
                + "  <result_code><![CDATA[SUCCESS]]></result_code>\n"
                + "  <openid><![CDATA[oGFfks9CQz4Xvc0hydwCikOk14bI]]></openid>\n"
                + "  <is_subscribe><![CDATA[N]]></is_subscribe>\n"
                + "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n"
                + "  <bank_type><![CDATA[ABC_CREDIT]]></bank_type>\n"
                + "  <total_fee>6390</total_fee>\n"
                + "  <fee_type><![CDATA[CNY]]></fee_type>\n"
                + "  <transaction_id><![CDATA[4200001235202107209045509828]]></transaction_id>\n"
                + "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n"
                + "  <attach><![CDATA[]]></attach>\n"
                + "  <time_end><![CDATA[**************]]></time_end>\n"
                + "  <sub_appid><![CDATA[wxdcf49aa3008b7250]]></sub_appid>\n"
                + "  <sub_openid><![CDATA[ojteWjqL1eRcqKvVScL006DE4d-g]]></sub_openid>\n"
                + "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n"
                + "  <cash_fee>5890</cash_fee>\n"
                + "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n"
                + "  <version><![CDATA[1.0]]></version>\n"
                + "  <promotion_detail><![CDATA[{\"promotion_detail\":[{\"promotion_id\":\"***********\",\"name\":\"指定糖果5元券\",\"scope\":\"SINGLE\",\"type\":\"COUPON\",\"amount\":500,\"activity_id\":\"15683171\",\"wxpay_contribute\":0,\"merchant_contribute\":0,\"other_contribute\":500,\"goods_detail\":[{\"goods_id\":\"6923450656181\",\"quantity\":1,\"price\":1200,\"discount_amount\":250},{\"goods_id\":\"6923450657935\",\"quantity\":1,\"price\":1200,\"discount_amount\":250}]}]}]]></promotion_detail>\n"
                + "</xml>");
        
        String resultString = postPerform("test_weixin_provider_response", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        Map<String, Object> providerResponse = (Map<String, Object>) BeanUtil.getNestedProperty(result, "biz_response.data.provider_response");
        assertNotNull(providerResponse.get(QueryResponse.GOODS_DETAILS));
        assertNotNull(providerResponse.get(QueryResponse.VOUCHER_DETAILS));
        assertNotNull(providerResponse.get(QueryResponse.SUB_IS_SUBSCRIBE));
    }
}
