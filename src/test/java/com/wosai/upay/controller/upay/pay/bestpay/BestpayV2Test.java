package com.wosai.upay.controller.upay.pay.bestpay;

import com.alibaba.fastjson.JSONObject;
import com.wosai.constant.ProductFlagEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import java.util.Map;

/**
 * 翼支付3.0 支付测试
 */
public class BestpayV2Test extends BaseTestController {
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.BESTPAY_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetRsaKeyDataById(SupportUtil.getRsaPrivateKey());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
    }

    /**
     *
     * 翼支付3.0 通道下单支付成功
     *
     * @throws Exception
     */
    @Test
    public void test_bestpay_v2_barcode_pay_success_without_query()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
        String payerLogin = "17623800249";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(
                "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"disCountAmt\":0,\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05.396\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":null,\"tradeResultDesc\":null,\"tradeStatus\":\"SUCCESS\",\"tradeprodNo\":\"2020051117TPPIOP1110000000502478\"},\"sign\":\"bPAcSVavAlOMcRIaFZnZkGOn88IxMwGEbzqDdDAh1uYvtrU60G01NO9OC3M8NC9Ywz0KYw1zd6MwzRALs0tGfzVCgWIe0VYfZBiiUb0ZzY6lezeSb7EZMPXfVxt1IFApZYFdLd9WDogzOOe2Now5zQBWldaMJfKb0KFbr6012zA=\",\"success\":true}");

        String resultString = postPerform("test_bestpay_v2_barcode_pay_success_without_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.payer_login", payerLogin
        );
        String sn = (String) BeanUtil.getNestedProperty(result, "biz_response.data.sn");
        String productFlag = BeanUtil.getPropString(dataRepository.getTransactionDao().filter(Criteria.where(Transaction.ORDER_SN).is(sn)).fetchOne(), Transaction.PRODUCT_FLAG);
        Assert.assertEquals(true, productFlag.contains(ProductFlagEnum.MARKET_PROGRAM_DISCOUNT.getCode()));

    }

    /**
     *
     * 翼支付3.0 通道下单成功，通过查询接口查询交易成功
     *
     * @throws Exception
     */
    @Test
    public void test_bestpay_v2_barcode_pay_success_with_query()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestURL = invocation.getArgument(3);
                if(postRequestURL.contains("mapi/uniformReceipt/barCodePay")) {
                    return "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"disCountAmt\":0,\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05.396\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":null,\"tradeResultDesc\":null,\"tradeStatus\":\"WAITFORPAY\",\"tradeprodNo\":\"2020051117TPPIOP1110000000502478\"},\"sign\":\"bPAcSVavAlOMcRIaFZnZkGOn88IxMwGEbzqDdDAh1uYvtrU60G01NO9OC3M8NC9Ywz0KYw1zd6MwzRALs0tGfzVCgWIe0VYfZBiiUb0ZzY6lezeSb7EZMPXfVxt1IFApZYFdLd9WDogzOOe2Now5zQBWldaMJfKb0KFbr6012zA=\",\"success\":true}";
                }else {
                    return "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"closeFlag\":\"FALSE\",\"discountAmt\":0,\"merchantName\":\"刘飞的私人商超\",\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"payFinishedDate\":\"2020-05-11T16:54:05\",\"payResultCode\":\"SUCCESS\",\"payResultDesc\":\"成功\",\"paymentType\":\"BALANCE\",\"refundFlag\":\"FALSE\",\"subject\":\"subject\",\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":\"SUCCESS\",\"tradeResultDesc\":\"交易成功\",\"tradeStatus\":\"SUCCESS\"},\"sign\":\"FUHexdNDaLqFB6hd03kEq1xa+hh502/uJP8gwlnF2ZVoRkGLkqEyiiAGSXGXYpn63OH1dgT069O3mdtpqC5NgTl4oZoO0VE9G2DaRR/sDpPE6FEa4qo6Qmeg5xFhD5EzFoBJGqY7DnIj49WQX/tJ0cDaLcFcXpIWka6VfyhtEVU=\",\"success\":true}";
                }
            }
        });

        String resultString = postPerform("test_bestpay_v2_barcode_pay_success_with_query:pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_IN_PROG).getName(),
                "biz_response.data.order_status", "CREATED"
        );
        Thread.sleep(3000);

        resultString = postPerform("test_bestpay_v2_barcode_pay_success_with_query:query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    /**
    *
    * 翼支付3.0 退款
    *
    * @throws Exception
    */
   @Test
   public void test_refund()throws Exception{
       JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
       PowerMockito.mockStatic(HttpClientUtils.class);
       PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
           String postRequestURL = invocation.getArgument(3);
           if(postRequestURL.contains("mapi/uniformReceipt/barCodePay")) {
               return "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"disCountAmt\":0,\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05.396\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":null,\"tradeResultDesc\":null,\"tradeStatus\":\"SUCCESS\",\"tradeprodNo\":\"2020051117TPPIOP1110000000502478\"},\"sign\":\"bPAcSVavAlOMcRIaFZnZkGOn88IxMwGEbzqDdDAh1uYvtrU60G01NO9OC3M8NC9Ywz0KYw1zd6MwzRALs0tGfzVCgWIe0VYfZBiiUb0ZzY6lezeSb7EZMPXfVxt1IFApZYFdLd9WDogzOOe2Now5zQBWldaMJfKb0KFbr6012zA=\",\"success\":true}";
           }else if(postRequestURL.contains("/mapi/uniformReceipt/tradeRefund")){
               return "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"closeFlag\":\"FALSE\",\"discountAmt\":0,\"merchantName\":\"刘飞的私人商超\",\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"payFinishedDate\":\"2020-05-11T16:54:05\",\"payResultCode\":\"SUCCESS\",\"payResultDesc\":\"成功\",\"paymentType\":\"BALANCE\",\"refundFlag\":\"FALSE\",\"subject\":\"subject\",\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":\"SUCCESS\",\"tradeResultDesc\":\"交易成功\",\"tradeStatus\":\"SUCCESS\"},\"sign\":\"FUHexdNDaLqFB6hd03kEq1xa+hh502/uJP8gwlnF2ZVoRkGLkqEyiiAGSXGXYpn63OH1dgT069O3mdtpqC5NgTl4oZoO0VE9G2DaRR/sDpPE6FEa4qo6Qmeg5xFhD5EzFoBJGqY7DnIj49WQX/tJ0cDaLcFcXpIWka6VfyhtEVU=\",\"success\":true}";
           }
           return "fail";
       });

       String resultString = postPerform("test_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
               "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
       );

       request.put(UpayService.REFUND_AMOUNT, "30");
       request.put(UpayService.REFUND_REQUEST_NO, "1");
       resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       result = JsonUtil.jsonStrToObject(resultString, Map.class);

       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
               "biz_response.data.order_status", Order.Status.REFUNDED.name()
       );
   }
    
   /**
   *
   * 翼支付3.0 退款
   *
   * @throws Exception
   */
  @Test
  public void test_cancel()throws Exception{
      JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
      PowerMockito.mockStatic(HttpClientUtils.class);
      PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
          String postRequestURL = invocation.getArgument(3);
          if(postRequestURL.contains("mapi/uniformReceipt/barCodePay")) {
              return "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"disCountAmt\":0,\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05.396\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":null,\"tradeResultDesc\":null,\"tradeStatus\":\"SUCCESS\",\"tradeprodNo\":\"2020051117TPPIOP1110000000502478\"},\"sign\":\"bPAcSVavAlOMcRIaFZnZkGOn88IxMwGEbzqDdDAh1uYvtrU60G01NO9OC3M8NC9Ywz0KYw1zd6MwzRALs0tGfzVCgWIe0VYfZBiiUb0ZzY6lezeSb7EZMPXfVxt1IFApZYFdLd9WDogzOOe2Now5zQBWldaMJfKb0KFbr6012zA=\",\"success\":true}";
          }else if(postRequestURL.contains("/mapi/uniformReceipt/tradeRefund")){
              return "{\"errorCode\":null,\"errorMsg\":null,\"result\":{\"buyerLoginNo\":\"17623800249\",\"closeFlag\":\"FALSE\",\"discountAmt\":0,\"merchantName\":\"刘飞的私人商超\",\"merchantNo\":\"3178002070426323\",\"merchantOrderNo\":\"20170919148520128231129\",\"outTradeNo\":\"20170919148520128231129\",\"payAmt\":11,\"payFinishedDate\":\"2020-05-11T16:54:05\",\"payResultCode\":\"SUCCESS\",\"payResultDesc\":\"成功\",\"paymentType\":\"BALANCE\",\"refundFlag\":\"FALSE\",\"subject\":\"subject\",\"tradeAmt\":11,\"tradeFinishedDate\":\"2020-05-11T16:54:05\",\"tradeNo\":\"20200511100000210002100256895448\",\"tradeResultCode\":\"SUCCESS\",\"tradeResultDesc\":\"交易成功\",\"tradeStatus\":\"SUCCESS\"},\"sign\":\"FUHexdNDaLqFB6hd03kEq1xa+hh502/uJP8gwlnF2ZVoRkGLkqEyiiAGSXGXYpn63OH1dgT069O3mdtpqC5NgTl4oZoO0VE9G2DaRR/sDpPE6FEa4qo6Qmeg5xFhD5EzFoBJGqY7DnIj49WQX/tJ0cDaLcFcXpIWka6VfyhtEVU=\",\"success\":true}";
          }
          return "fail";
      });

      String resultString = postPerform("test_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
      Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
      );

      resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);
      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
              "biz_response.data.order_status", Order.Status.CANCELED.name()
      );
  }
}
