package com.wosai.upay.controller.upay.precreate.unionpay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class UnionpayOpenPrecreateTest extends BaseTestController{
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UPO_TRADE_PARAMS));
    }

    @Test
    public void test_precreate_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_UNIONPAY, Order.SUB_PAYWAY_WAP, "fg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/gateway/api/pay/unifiedorder")) {
                    return  "{\n" + 
                            "    \"signature\": \"abe/AGKtkO/QpZS1fda0JobmHhk7dMcsRSFoLsN0l1+dCv8v4y97q8R3mQ6w2xiwWJHod2t/P3vHTVJsvuP3H8ESuvNeMOMzZfNmf5++P196e5d+bk+Me+G0UiF+9OQAYQlWdDa2jAJ4ShVBfuQOf40JLsKvmCC88ri+ez7k450=\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"payData\": {\n" + 
                            "        \"redirectUrl\": \"https://qr.95516.com/UP02/qrcGtwWeb-web/front/confirmOrder?sessionId=399b8e0e0e6442e3923080581ae5e9ad\"\n" + 
                            "    },\n" + 
                            "    \"respCode\": \"00\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/gateway/api/pay/queryOrder")) {
                    return  "{\n" + 
                            "    \"orderNo\": \"7895237682533188\",\n" + 
                            "    \"signature\": \"Xtd0Q4FmM8MBKTzvjqEHOOF0L9OdGt8SbcGnN1kY/S9l1yg/wChFIGGgzh+O8Q4ce1Dw0z6GrepZGPnXAxE0rqHW2a1Ww1qQxbPzrWBxTO2gbgj2xIJFBq30DyajsB29zd7O58sGb3PR4z7BNLak6slJvtSUmn2E03n4xmSVGLc=\",\n" + 
                            "    \"couponInfo\": \"[{\\\"id\\\":\\\"000000000000000\\\",\\\"desc\\\":\\\"银联红包\\\",\\\"type\\\":\\\"CP01\\\",\\\"spnsrId\\\":\\\"00010000\\\",\\\"offstAmt\\\":\\\"27\\\"}]\",\n" + 
                            "    \"payTime\": \"20200826100412\",\n" + 
                            "    \"settlementAmt\": 30,\n" + 
                            "    \"payAmt\": 30,\n" + 
                            "    \"payerInfo\": \"\",\n" + 
                            "    \"reqReserved\": \"\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"origRespMsg\": \"成功\",\n" + 
                            "    \"origRespCode\": \"00\",\n" + 
                            "    \"currencyCode\": \"156\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"txnAmt\": 30,\n" + 
                            "    \"transIndex\": \"20082640532768\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_precreate_with_query_1", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_precreate_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
}
