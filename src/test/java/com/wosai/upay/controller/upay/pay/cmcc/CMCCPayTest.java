package com.wosai.upay.controller.upay.pay.cmcc;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.cmcc.BusinessFields;
import com.wosai.mpay.api.cmcc.CMCCWalletConfig;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.SupportUtil;

public class CMCCPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.CMCC_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.BASIC_PARAMS);
        
    }

    @Test
    public void test_cmcc_barcode() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("810086191673401475");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map<String, Object> request = invocation.getArgument(5);
                String type = MapUtil.getString(request, BusinessFields.TYPE);
                if(CMCCWalletConfig.PAY_TYPE.equals(type)) {
                    return  "hmac=390beb2d71f42b1dd7fa8ec6081f2292&signType=MD5&type=CloudQuickPay&version=2.0.1"
                            + "&merchantId=888073115000276&requestId=7895237602578908&orderId=7895237602578908"
                            + "&amount=30&coupAmt=0&vchAmt=0&cashAmt=26500&crdType=&couponAmt=20&poiBonAmt=0"
                            + "&returnCode=000000&message=SUCCESS";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_cmcc_barcode_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("810086191673401475");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map<String, Object> request = invocation.getArgument(5);
                String type = MapUtil.getString(request, BusinessFields.TYPE);
                if(CMCCWalletConfig.PAY_TYPE.equals(type)) {
                    return  "hmac=390beb2d71f42b1dd7fa8ec6081f2292&signType=MD5&type=CloudQuickPay&version=2.0.1"
                            + "&merchantId=888073115000276&requestId=7895237602578908&orderId=7895237602578908"
                            + "&amount=30&coupAmt=0&vchAmt=0&cashAmt=30&crdType=&couponAmt=20&poiBonAmt=0"
                            + "&returnCode=CPS01134&message=SUCCESS";
                }else if(CMCCWalletConfig.OLD_QUERY_TYPE.equals(type)) {
                    return  "hmac=390beb2d71f42b1dd7fa8ec6081f2292&signType=MD5&type=CloudQuickPay&version=2.0.1"
                            + "&merchantId=888073115000276&requestId=7895237602578908&orderId=7895237602578908"
                            + "&amount=30&coupAmt=0&vchAmt=0&cashAmt=30&crdType=&couponAmt=20&poiBonAmt=0"
                            + "&returnCode=000000&message=SUCCESS&status=SUCCESS";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_cmcc_barcode_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_cmcc_barcode_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    @Test
    public void test_cmcc_barcode_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("810086191673401475");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map<String, Object> request = invocation.getArgument(5);
                String type = MapUtil.getString(request, BusinessFields.TYPE);
                if(CMCCWalletConfig.PAY_TYPE.equals(type)) {
                    return  "hmac=390beb2d71f42b1dd7fa8ec6081f2292&signType=MD5&type=CloudQuickPay&version=2.0.1"
                            + "&merchantId=888073115000276&requestId=7895237602578908&orderId=7895237602578908"
                            + "&amount=30&coupAmt=0&vchAmt=0&cashAmt=26500&crdType=&couponAmt=20&poiBonAmt=0"
                            + "&returnCode=000000&message=SUCCESS";
                }else if(CMCCWalletConfig.REFUND_TYPE.equals(type)) {
                    return  "hmac=390beb2d71f42b1dd7fa8ec6081f2292&signType=MD5&type=CloudQuickPay&version=2.0.1"
                            + "&merchantId=888073115000276&requestId=7895237602578908&orderId=7895237602578908"
                            + "&amount=30&coupAmt=0&vchAmt=0&cashAmt=26500&crdType=&couponAmt=20&poiBonAmt=0"
                            + "&returnCode=000000&message=SUCCESS&status=SUCCESS";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_cmcc_barcode_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_cmcc_barcode_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_cmcc_barcode_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("810086191673401475");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map<String, Object> request = invocation.getArgument(5);
                String type = MapUtil.getString(request, BusinessFields.TYPE);
                if(CMCCWalletConfig.PAY_TYPE.equals(type)) {
                    return  "hmac=390beb2d71f42b1dd7fa8ec6081f2292&signType=MD5&type=CloudQuickPay&version=2.0.1"
                            + "&merchantId=888073115000276&requestId=7895237602578908&orderId=7895237602578908"
                            + "&amount=30&coupAmt=0&vchAmt=0&cashAmt=26500&crdType=&couponAmt=20&poiBonAmt=0"
                            + "&returnCode=000000&message=SUCCESS";
                }else if(CMCCWalletConfig.CANCEL_TYPE.equals(type)) {
                    return "merchantId=888073115000276&requestId=78952376607372581598432436323&signType=MD5"
                            + "&type=CloudTxnCancel&version=2.0.0&returnCode=000000"
                            + "&message=%E8%AF%A5%E8%AE%A2%E5%8D%95%E7%8A%B6%E6%80%81%E4%B8%8D%E6%94%AF%E6%8C%81%E6%AD%A4%E6%93%8D%E4%BD%9C"
                            + "&hmac=c94445d1f8b407d204499f90c40d7b31";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_cmcc_barcode_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        resultString = postPerform("test_cmcc_barcode_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
