package com.wosai.upay.controller.upay.precreate.alipay;

import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.cibbank.CIBBankConstants;
import com.wosai.mpay.api.cibbank.ProtocolFields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.XmlUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class CIBSHBankAlipayPrecreateTest extends BaseTestController{
    
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.CIBSHBANK_TRADE_PARAMS));
    }
    
    @Test
    public void test_alipay_c2b() throws Exception {
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 2, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestStr = invocation.getArgument(5);
                Map<String, Object> postRequest = XmlUtils.parse(postRequestStr);
                String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
                if(CIBBankConstants.SERVICE_PAY_ALIPAY_NATIVE.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[****************]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=河南同和堂医药有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <device_info><![CDATA[100009620010639874]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <nonce_str><![CDATA[5823477246740984941]]></nonce_str>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[2020082522001421470546122271]]></out_transaction_id>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[2467EC5535539724084DC61569D13083]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <uuid><![CDATA[6eb23d52db7933a91d3e3936852918f08]]></uuid>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "  <code_url><![CDATA[https://shouqianba.com]]></code_url>\n" +
                            "</xml>";
                }else if(CIBBankConstants.SERVICE_UNIFIED_TRADE_QUERY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[****************]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=开封市百氏康医药连锁有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <bank_type><![CDATA[ALIPAYACCOUNT]]></bank_type>\n" + 
                            "  <buyer_logon_id><![CDATA[138******88]]></buyer_logon_id>\n" + 
                            "  <buyer_pay_amount><![CDATA[19800.00]]></buyer_pay_amount>\n" + 
                            "  <buyer_user_id><![CDATA[****************]]></buyer_user_id>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                            "  <device_info><![CDATA[100009620009001837]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <fund_bill_list><![CDATA[[{\"amount\":\"0.30\",\"fundChannel\":\"ALIPAYACCOUNT\"}]]]></fund_bill_list>\n" + 
                            "  <gmt_payment><![CDATA[**************]]></gmt_payment>\n" + 
                            "  <invoice_amount><![CDATA[0.11]]></invoice_amount>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <mdiscount><![CDATA[0]]></mdiscount>\n" + 
                            "  <nonce_str><![CDATA[-273685190366391118]]></nonce_str>\n" + 
                            "  <openid><![CDATA[****************]]></openid>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[2020082522001466310548445146]]></out_transaction_id>\n" + 
                            "  <point_amount><![CDATA[0.00]]></point_amount>\n" + 
                            "  <receipt_amount><![CDATA[0.30]]></receipt_amount>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[A12617518A67CCC2D09E890A1C23DEBF]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <time_end><![CDATA[**************]]></time_end>\n" + 
                            "  <total_fee><![CDATA[0]]></total_fee>\n" + 
                            "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                            "  <trade_type><![CDATA[pay.alipay.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008257618078706]]></transaction_id>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_alipay_c2b_1", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_alipay_c2b_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    @Test
    public void test_alipay_wap() throws Exception {
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String postRequestStr = invocation.getArgument(5);
                Map<String, Object> postRequest = XmlUtils.parse(postRequestStr);
                String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
                if(CIBBankConstants.SERVICE_PAY_ALIPAY_JSPAY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[****************]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=河南同和堂医药有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <device_info><![CDATA[100009620010639874]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <nonce_str><![CDATA[5823477246740984941]]></nonce_str>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[2020082522001421470546122271]]></out_transaction_id>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[2467EC5535539724084DC61569D13083]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <uuid><![CDATA[6eb23d52db7933a91d3e3936852918f08]]></uuid>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "  <pay_info><![CDATA[{\"tradeNO\":\"2020090222001433341406435086\"}]]></pay_info>\n" +
                            "</xml>";
                }else if(CIBBankConstants.SERVICE_UNIFIED_TRADE_QUERY.equals(method)) {
                    return  "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + 
                            "<xml>\n" + 
                            "  <appid><![CDATA[****************]]></appid>\n" + 
                            "  <attach><![CDATA[bank_mch_name=开封市百氏康医药连锁有限公司&bank_mch_id=*************]]></attach>\n" + 
                            "  <bank_type><![CDATA[ALIPAYACCOUNT]]></bank_type>\n" + 
                            "  <buyer_logon_id><![CDATA[138******88]]></buyer_logon_id>\n" + 
                            "  <buyer_pay_amount><![CDATA[19800.00]]></buyer_pay_amount>\n" + 
                            "  <buyer_user_id><![CDATA[****************]]></buyer_user_id>\n" + 
                            "  <charset><![CDATA[UTF-8]]></charset>\n" + 
                            "  <coupon_fee><![CDATA[0]]></coupon_fee>\n" + 
                            "  <device_info><![CDATA[100009620009001837]]></device_info>\n" + 
                            "  <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                            "  <fund_bill_list><![CDATA[[{\"amount\":\"0.30\",\"fundChannel\":\"ALIPAYACCOUNT\"}]]]></fund_bill_list>\n" + 
                            "  <gmt_payment><![CDATA[**************]]></gmt_payment>\n" + 
                            "  <invoice_amount><![CDATA[0.11]]></invoice_amount>\n" + 
                            "  <mch_id><![CDATA[************]]></mch_id>\n" + 
                            "  <mdiscount><![CDATA[0]]></mdiscount>\n" + 
                            "  <nonce_str><![CDATA[-273685190366391118]]></nonce_str>\n" + 
                            "  <openid><![CDATA[****************]]></openid>\n" + 
                            "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                            "  <out_transaction_id><![CDATA[2020082522001466310548445146]]></out_transaction_id>\n" + 
                            "  <point_amount><![CDATA[0.00]]></point_amount>\n" + 
                            "  <receipt_amount><![CDATA[0.30]]></receipt_amount>\n" + 
                            "  <result_code><![CDATA[0]]></result_code>\n" + 
                            "  <sign><![CDATA[A12617518A67CCC2D09E890A1C23DEBF]]></sign>\n" + 
                            "  <sign_agentno><![CDATA[************]]></sign_agentno>\n" + 
                            "  <sign_type><![CDATA[MD5]]></sign_type>\n" + 
                            "  <status><![CDATA[0]]></status>\n" + 
                            "  <time_end><![CDATA[**************]]></time_end>\n" + 
                            "  <total_fee><![CDATA[0]]></total_fee>\n" + 
                            "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                            "  <trade_type><![CDATA[pay.alipay.micropay]]></trade_type>\n" + 
                            "  <transaction_id><![CDATA[************202008257618078706]]></transaction_id>\n" + 
                            "  <version><![CDATA[1.0]]></version>\n" + 
                            "</xml>";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_alipay_wap_1", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_alipay_wap_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
}
