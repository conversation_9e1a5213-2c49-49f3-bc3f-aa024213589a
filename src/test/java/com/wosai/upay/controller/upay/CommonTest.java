package com.wosai.upay.controller.upay;

import static org.junit.Assert.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.io.IOException;
import java.util.Map;

import org.junit.Test;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.HttpException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.exception.CoreTerminalNotActivatedException;
import com.wosai.upay.core.exception.CoreTerminalNotExistsException;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.util.SupportUtil;


public class CommonTest extends BaseTestController{
    
    @Test
    public void echo() throws Exception {
        mockMvc.perform(get("/upay/v2/echo"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8_VALUE))
            .andExpect(jsonPath("$.message").value("Hi, Upay!"));
    }
    
    /**
     * 必填参数为空
     * 
     * @throws Exception
     */
    @Test
    public void test_invalid_params()throws Exception{
        String resultString = postPerform("test_invalid_params", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, "{}");
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
                CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_VALIDATE_FAIL,
                CommonResponse.ERROR_CODE, CommonResponse.INVALID_PARAMS
        );
    }

    /**
     * 连不上core-b
     * 
     * @throws Exception
     */
    @Test
    public void test_fail_call_coreb()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        mockGetAllPrams(new HttpException("fail to calll core-b", new IOException()));
        String resultString = postPerform("test_fail_call_coreb", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SYSTEM_ERROR,
                "error_code", "EXTERNAL_SERVICE_EXCEPTION",
                "error_code_standard", "EP02"
        );
    }
    
    /**
     * 终端不存在
     * @throws Throwable 
     */
    @Test
    public void test_terminal_not_exists()throws Throwable{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        mockGetAllPrams(new CoreTerminalNotExistsException("终端不存在"));
        String resultString = postPerform("test_terminal_not_exists", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "UPAY_TERMINAL_NOT_EXISTS",
                "biz_response.error_code_standard", "EP11"
        );
    }
    
    /**
     * 终端未激活
     * 
     * @throws Exception
     */
    @Test
    public void test_terminal_not_activated()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        mockGetAllPrams(new CoreTerminalNotActivatedException("终端" + SupportUtil.TERMINAL_SN+ "未激活"));
        String resultString = postPerform("test_terminal_not_activated", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "UPAY_TERMINAL_STATUS_ABNORMAL",
                "biz_response.error_code_standard", "EP55"
        );
    }
    
    /**
     * 终端已禁用
     * 
     * @throws Exception
     */
    @Test
    public void test_terminal_status_disabled()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        mockGetAllPrams(new CoreTerminalNotActivatedException("终端" + SupportUtil.TERMINAL_SN+ "已禁用"));
        String resultString = postPerform("test_terminal_status_disabled", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "UPAY_TERMINAL_STATUS_ABNORMAL",
                "biz_response.error_code_standard", "EP22"
        );
    }
    
    /**
     * 终端状态异常
     * 
     * @throws Exception
     */
    @Test
    public void test_terminal_status_unknown()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        mockGetAllPrams(new CoreTerminalNotActivatedException("终端" + SupportUtil.TERMINAL_SN+ "状态异常"));
        String resultString = postPerform("test_terminal_status_unknown", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "UPAY_TERMINAL_STATUS_ABNORMAL",
                "biz_response.error_code_standard", "EP12"
        );
    }
    
    @Test
    public void test_monitor()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        String resultString = postPerform("test_monitor", "/upay/v2/monitor", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertNotNull(result);
        assertNotNull(result.get("eventBus"));
        assertNotNull(result.get("workflowDriver"));
    }

    @Test
    public void test_updateFlags()throws Exception{
        JSONObject request = new JSONObject();
        request.put("flag_apply_trade_coprocessor", false);
        mockMvc.perform(
                post("/upay/v2/updateFlags")
                    .contentType(MediaType.APPLICATION_JSON_UTF8_VALUE)
                    .content(request.toJSONString()))
        .andExpect(status().isOk());
    }
}
