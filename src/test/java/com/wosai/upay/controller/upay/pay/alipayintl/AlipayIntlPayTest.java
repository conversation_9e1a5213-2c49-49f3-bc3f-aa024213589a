package com.wosai.upay.controller.upay.pay.alipayintl;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class AlipayIntlPayTest extends BaseTestController {
    @Before
    public void init() {
        Map basicParams = SupportUtil.buildBasicParams();
        basicParams.put("merchant_country", "SG");
        basicParams.put("currency", "SGD");
        mockGetBasicPrams(basicParams);
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        basicParams.putAll(SupportUtil.ALIPAY_INTL_TRADE_PARAMS);
        mockGetAllPrams(SupportUtil.buildGetAllParams(basicParams));
    }


    @Test
    public void test_pay() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/api/alipay/intl/acquiring/offline/pay.htm")) {
                return  "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"acquirementId\": \"2020082222001448421446197432\",\n" + 
                        "            \"createTime\": \"2020-08-22T21:37:40+08:00\",\n" + 
                        "            \"merchantTransId\": \"7895237091119535\",\n" + 
                        "            \"orderAmount\": {\n" + 
                        "                \"currency\": \"SGD\",\n" + 
                        "                \"value\": \"30\"\n" + 
                        "            },\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"SUCCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            },\n" + 
                        "            \"userId\": \"2088102003048428\",\n" + 
                        "            \"userLoginId\": \"hep***@126.*\",\n" + 
                        "            \"userSite\": \"ALIPAY_CN\"\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7900\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.offline.pay\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-a4924b72e47c11eaa564f3ae0b06f52c\",\n" + 
                        "            \"respTime\": \"2020-08-22T06:37:41-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"rQGVLASX1pjbYoXC4+NcFVs9lYssNHJgDcF9erX5myZv2FtzW2oHn1sYxkes6ccKdUE9R8MewBW4SF6bzm8qt/n9IflHwrJC6BpHtPckU4JzR+jyzI7PDtA1/9GRYJsoTx1oKqRcpmrBxYGhQ7YjMfWM6IUaQiVX6YyMiNBUXTK0kayeBaQ6RIAsGKQCdwCsQL6VcplXEgAG8ggTCBkFTZUozqJ1lSdzB52gCjX7VV3VOgCAQACjvvnuRWkbUh9ch52iTO0Lk0WKnHBTdnc2hNyAVBrRJ7sCbiFvrtBDA/Ac0oi13tQkTn1q9p6aCcg1t5MrPJrSRbxAtoDMbugArg==\"\n" + 
                        "}";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name(),
                        "biz_response.data.payer_currency", "CNY"
                );
    }
    
    @Test
    public void test_pay_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/api/alipay/intl/acquiring/offline/pay.htm")) {
                return  "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"acquirementId\": \"2020082222001448421446197432\",\n" + 
                        "            \"createTime\": \"2020-08-22T21:37:40+08:00\",\n" + 
                        "            \"merchantTransId\": \"7895237091119535\",\n" + 
                        "            \"orderAmount\": {\n" + 
                        "                \"currency\": \"SGD\",\n" + 
                        "                \"value\": \"30\"\n" + 
                        "            },\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"PAYMENT_IN_PROCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            },\n" + 
                        "            \"userId\": \"2088102003048428\",\n" + 
                        "            \"userLoginId\": \"hep***@126.*\",\n" + 
                        "            \"userSite\": \"ALIPAY_CN\"\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7900\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.offline.pay\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-a4924b72e47c11eaa564f3ae0b06f52c\",\n" + 
                        "            \"respTime\": \"2020-08-22T06:37:41-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"rQGVLASX1pjbYoXC4+NcFVs9lYssNHJgDcF9erX5myZv2FtzW2oHn1sYxkes6ccKdUE9R8MewBW4SF6bzm8qt/n9IflHwrJC6BpHtPckU4JzR+jyzI7PDtA1/9GRYJsoTx1oKqRcpmrBxYGhQ7YjMfWM6IUaQiVX6YyMiNBUXTK0kayeBaQ6RIAsGKQCdwCsQL6VcplXEgAG8ggTCBkFTZUozqJ1lSdzB52gCjX7VV3VOgCAQACjvvnuRWkbUh9ch52iTO0Lk0WKnHBTdnc2hNyAVBrRJ7sCbiFvrtBDA/Ac0oi13tQkTn1q9p6aCcg1t5MrPJrSRbxAtoDMbugArg==\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/alipay/intl/acquiring/offline/payquery.htm")) {
                return "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"payment\": {\n" + 
                        "                \"acquirementId\": \"2020082422001419870538867504\",\n" + 
                        "                \"merchantTransId\": \"7895237642009488\",\n" + 
                        "                \"orderAmount\": {\n" + 
                        "                    \"currency\": \"SGD\",\n" + 
                        "                    \"value\": \"30\"\n" + 
                        "                },\n" + 
                        "                \"payStatus\": \"PAY_SUCCESS\",\n" + 
                        "                \"paymentTime\": \"2020-08-24T11:03:28+08:00\"\n" + 
                        "            },\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"SUCCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            },\n" + 
                        "            \"userSite\": \"ALIPAY_CN\"\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7900\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.offline.payQuery\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-64c3239de5b611eab5e09d7abf7c0779\",\n" + 
                        "            \"respTime\": \"2020-08-23T20:03:34-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"G8+2nBcCa4OtoWS4B8vh5gWEQZ4OserkdnM3JuVpoeHUKseP+m7HiJNbIaUkBhw5TSozvB/uwsSy4j5BrceOBK+nxeuQzldcfvzW4+mSx/mhCo+gj/pV72EW/hx//di7FO82RN6/TtjHfVmkLuRC6pqHjdTL/OKQVGB/vx8yZjclGSg6YCTRIsSAdUSryGrkeNKfI2UhKtzJdvYD7JfJzz2TCjnOYMAZV3sDOyFuakjHgXF7Uner4me/rU3x/aHtxUTIVdju7ZceO3oPmVikUuz74/750s8EZchXtw+PVZFNPThK75CJUYWfv/+8060FWWD6YbrTzRpbD+jtpcWu1w==\"\n" + 
                        "}";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name(),
                        "biz_response.data.payer_currency", "CNY"
                );
    }
    
    @Test
    public void test_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/api/alipay/intl/acquiring/offline/pay.htm")) {
                return  "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"acquirementId\": \"2020082222001448421446197432\",\n" + 
                        "            \"createTime\": \"2020-08-22T21:37:40+08:00\",\n" + 
                        "            \"merchantTransId\": \"7895237091119535\",\n" + 
                        "            \"orderAmount\": {\n" + 
                        "                \"currency\": \"SGD\",\n" + 
                        "                \"value\": \"30\"\n" + 
                        "            },\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"SUCCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            },\n" + 
                        "            \"userId\": \"2088102003048428\",\n" + 
                        "            \"userLoginId\": \"hep***@126.*\",\n" + 
                        "            \"userSite\": \"ALIPAY_CN\"\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7900\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.offline.pay\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-a4924b72e47c11eaa564f3ae0b06f52c\",\n" + 
                        "            \"respTime\": \"2020-08-22T06:37:41-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"rQGVLASX1pjbYoXC4+NcFVs9lYssNHJgDcF9erX5myZv2FtzW2oHn1sYxkes6ccKdUE9R8MewBW4SF6bzm8qt/n9IflHwrJC6BpHtPckU4JzR+jyzI7PDtA1/9GRYJsoTx1oKqRcpmrBxYGhQ7YjMfWM6IUaQiVX6YyMiNBUXTK0kayeBaQ6RIAsGKQCdwCsQL6VcplXEgAG8ggTCBkFTZUozqJ1lSdzB52gCjX7VV3VOgCAQACjvvnuRWkbUh9ch52iTO0Lk0WKnHBTdnc2hNyAVBrRJ7sCbiFvrtBDA/Ac0oi13tQkTn1q9p6aCcg1t5MrPJrSRbxAtoDMbugArg==\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/alipay/intl/acquiring/common/grefund.htm")) {
                return "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"acquirementId\": \"2020072022001456831424381880\",\n" + 
                        "            \"merchantRefundId\": \"7895237886128396\",\n" + 
                        "            \"refundAmount\": {\n" + 
                        "                \"currency\": \"GBP\",\n" + 
                        "                \"value\": \"30\"\n" + 
                        "            },\n" + 
                        "            \"refundTime\": \"2020-08-17T09:25:53-07:00\",\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"SUCCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            }\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7801\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.common.refund\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-50b7d7fae0a611eabbe4f3370cd365b8\",\n" + 
                        "            \"respTime\": \"2020-08-17T09:25:53-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"bNRTVco3g4ZQbJb3a1qBAZ2/c9hnNyl3+h//4jQPcKFsLeB/V2t2PpnC8DjS8po3hCf1w0ZKah4Gp9wL4A77Hg+N5Q+NiqNRj4mW8nHg58u9bUjB0aXcu58GMy2LJRIWL0fW6m6GSm3fh6Q9m5wfEMiQSWhe8crSXkcJHInvHdN/uBzdCn3QvXgSsUV+mOFGzswJPUvjtKi2i/N44+764k4OVy8Yi83Qf9uAK/wPK0E4EPrCVw7/VQIiqU5Gf+3q4hoRMjiIiEhsrCmpJ1CyQ2UM0mMixzW/HIfpOf5+iCKydceE4/tZuL/AtAHfR8aDjY4LVPudXBON67PT9omuHQ==\"\n" + 
                        "}";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_bestpay_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/api/alipay/intl/acquiring/offline/pay.htm")) {
                return  "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"acquirementId\": \"2020082222001448421446197432\",\n" + 
                        "            \"createTime\": \"2020-08-22T21:37:40+08:00\",\n" + 
                        "            \"merchantTransId\": \"7895237091119535\",\n" + 
                        "            \"orderAmount\": {\n" + 
                        "                \"currency\": \"SGD\",\n" + 
                        "                \"value\": \"30\"\n" + 
                        "            },\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"SUCCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            },\n" + 
                        "            \"userId\": \"2088102003048428\",\n" + 
                        "            \"userLoginId\": \"hep***@126.*\",\n" + 
                        "            \"userSite\": \"ALIPAY_CN\"\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7900\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.offline.pay\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-a4924b72e47c11eaa564f3ae0b06f52c\",\n" + 
                        "            \"respTime\": \"2020-08-22T06:37:41-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"rQGVLASX1pjbYoXC4+NcFVs9lYssNHJgDcF9erX5myZv2FtzW2oHn1sYxkes6ccKdUE9R8MewBW4SF6bzm8qt/n9IflHwrJC6BpHtPckU4JzR+jyzI7PDtA1/9GRYJsoTx1oKqRcpmrBxYGhQ7YjMfWM6IUaQiVX6YyMiNBUXTK0kayeBaQ6RIAsGKQCdwCsQL6VcplXEgAG8ggTCBkFTZUozqJ1lSdzB52gCjX7VV3VOgCAQACjvvnuRWkbUh9ch52iTO0Lk0WKnHBTdnc2hNyAVBrRJ7sCbiFvrtBDA/Ac0oi13tQkTn1q9p6aCcg1t5MrPJrSRbxAtoDMbugArg==\"\n" + 
                        "}";
            }else if(serviceUrl.contains("/api/alipay/intl/acquiring/common/paycancel.htm")) {
                return "{\n" + 
                        "    \"response\": {\n" + 
                        "        \"body\": {\n" + 
                        "            \"acquirementId\": \"2020072022001456831424381880\",\n" + 
                        "            \"merchantRefundId\": \"7895237886128396\",\n" + 
                        "            \"refundAmount\": {\n" + 
                        "                \"currency\": \"GBP\",\n" + 
                        "                \"value\": \"30\"\n" + 
                        "            },\n" + 
                        "            \"refundTime\": \"2020-08-17T09:25:53-07:00\",\n" + 
                        "            \"resultInfo\": {\n" + 
                        "                \"resultCode\": \"SUCCESS\",\n" + 
                        "                \"resultCodeId\": \"00000000\",\n" + 
                        "                \"resultMsg\": \"success\",\n" + 
                        "                \"resultStatus\": \"S\"\n" + 
                        "            }\n" + 
                        "        },\n" + 
                        "        \"head\": {\n" + 
                        "            \"clientId\": \"385XPW662Y0V7801\",\n" + 
                        "            \"function\": \"alipay.intl.acquiring.common.payCancel\",\n" + 
                        "            \"reqMsgId\": \"shouqianba-50b7d7fae0a611eabbe4f3370cd365b8\",\n" + 
                        "            \"respTime\": \"2020-08-17T09:25:53-07:00\",\n" + 
                        "            \"version\": \"2.0.0\"\n" + 
                        "        }\n" + 
                        "    },\n" + 
                        "    \"signature\": \"bNRTVco3g4ZQbJb3a1qBAZ2/c9hnNyl3+h//4jQPcKFsLeB/V2t2PpnC8DjS8po3hCf1w0ZKah4Gp9wL4A77Hg+N5Q+NiqNRj4mW8nHg58u9bUjB0aXcu58GMy2LJRIWL0fW6m6GSm3fh6Q9m5wfEMiQSWhe8crSXkcJHInvHdN/uBzdCn3QvXgSsUV+mOFGzswJPUvjtKi2i/N44+764k4OVy8Yi83Qf9uAK/wPK0E4EPrCVw7/VQIiqU5Gf+3q4hoRMjiIiEhsrCmpJ1CyQ2UM0mMixzW/HIfpOf5+iCKydceE4/tZuL/AtAHfR8aDjY4LVPudXBON67PT9omuHQ==\"\n" + 
                        "}";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_bestpay_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        resultString = postPerform("test_bestpay_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
