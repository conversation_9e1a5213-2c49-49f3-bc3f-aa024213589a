package com.wosai.upay.controller.upay.pay.bestpay;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

/**
 * 翼支付v1版本支付测试
 */
public class BestpayTest extends BaseTestController {
    @Before
    public void init() {
        Map<String, Object> tradeParams = SupportUtil.BESTPAY_TRADE_PARAMS;
        Map<String, Object> bestpayParams = (Map<String, Object>) tradeParams.get(TransactionParam.BESTPAY_TRADE_PARAMS);
        bestpayParams.put(TransactionParam.SIGN_TYPE, "MD5");
        mockGetAllPrams(SupportUtil.buildGetAllParams(tradeParams));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
    }

    /**
     *
     * 翼支付 通道下单支付成功
     *
     * @throws Exception
     */
    @Test
    public void test_bestpay_barcode_pay_success_without_query()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
        String payerLogin = "17623800249";
        String tradeNo = StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(
                "{\n" + 
                "    \"success\": true,\n" + 
                "    \"result\": {\n" + 
                "        \"merchantId\": \"02310106010619256\",\n" + 
                "        \"orderNo\": \"****************\",\n" + 
                "        \"orderReqNo\": \"****************\",\n" + 
                "        \"orderDate\": null,\n" + 
                "        \"transStatus\": \"B\",\n" + 
                "        \"transAmt\": \"11\",\n" + 
                "        \"ourTransNo\": \""+ tradeNo +"\",\n" + 
                "        \"encodeType\": \"1\",\n" + 
                "        \"sign\": \"C99152835F7C29081DFE5E95FFA5421F\",\n" + 
                "        \"payerAccount\": null,\n" + 
                "        \"payeeAccount\": null,\n" + 
                "        \"payChannel\": null,\n" + 
                "        \"productDesc\": null,\n" + 
                "        \"refundFlag\": null,\n" + 
                "        \"customerId\": null,\n" + 
                "        \"coupon\": \"0\",\n" + 
                "        \"scValue\": \"0\",\n" + 
                "        \"mchntTmNum\": null,\n" + 
                "        \"deviceTmNum\": null,\n" + 
                "        \"attach\": null,\n" + 
                "        \"transPhone\": \""+payerLogin+"\",\n" + 
                "        \"respCode\": \"SUCCESS\",\n" + 
                "        \"respDesc\": \"交易成功\"\n" + 
                "    },\n" + 
                "    \"errorCode\": null,\n" + 
                "    \"errorMsg\": null\n" + 
                "}");

        String resultString = postPerform("test_bestpay_barcode_pay_success_without_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.payer_login", payerLogin
        );
    }

    /**
     *
     * 翼支付 通道下单成功，通过查询接口查询交易成功
     *
     * @throws Exception
     */
    @Test
    public void test_bestpay_barcode_pay_success_with_query()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
        String payerLogin = "17623800249";
        String tradeNo = StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer((invocation) ->{
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.endsWith("/barcode/placeOrder")) {
                return 
                        "{\n" + 
                        "    \"success\": true,\n" + 
                        "    \"result\": {\n" + 
                        "        \"merchantId\": \"02310106010619256\",\n" + 
                        "        \"orderNo\": \"****************\",\n" + 
                        "        \"orderReqNo\": \"****************\",\n" + 
                        "        \"orderDate\": null,\n" + 
                        "        \"transStatus\": \"A\",\n" + 
                        "        \"transAmt\": \"11\",\n" + 
                        "        \"ourTransNo\": \""+ tradeNo +"\",\n" + 
                        "        \"encodeType\": \"1\",\n" + 
                        "        \"sign\": \"C99152835F7C29081DFE5E95FFA5421F\",\n" + 
                        "        \"payerAccount\": null,\n" + 
                        "        \"payeeAccount\": null,\n" + 
                        "        \"payChannel\": null,\n" + 
                        "        \"productDesc\": null,\n" + 
                        "        \"refundFlag\": null,\n" + 
                        "        \"customerId\": null,\n" + 
                        "        \"coupon\": \"0\",\n" + 
                        "        \"scValue\": \"0\",\n" + 
                        "        \"mchntTmNum\": null,\n" + 
                        "        \"deviceTmNum\": null,\n" + 
                        "        \"attach\": null,\n" + 
                        "        \"transPhone\": \""+payerLogin+"\",\n" + 
                        "        \"respCode\": \"SUCCESS\",\n" + 
                        "        \"respDesc\": \"交易成功\"\n" + 
                        "    },\n" + 
                        "    \"errorCode\": null,\n" + 
                        "    \"errorMsg\": null\n" + 
                        "}";
            }else if(serviceUrl.endsWith("/query/queryOrder")) {
                return 
                        "{\n" + 
                        "    \"success\": true,\n" + 
                        "    \"result\": {\n" + 
                        "        \"merchantId\": \"02310106010619256\",\n" + 
                        "        \"orderNo\": \"****************\",\n" + 
                        "        \"orderReqNo\": \"****************\",\n" + 
                        "        \"orderDate\": null,\n" + 
                        "        \"transStatus\": \"B\",\n" + 
                        "        \"transAmt\": \"11\",\n" + 
                        "        \"ourTransNo\": \""+ tradeNo +"\",\n" + 
                        "        \"encodeType\": \"1\",\n" + 
                        "        \"sign\": \"C99152835F7C29081DFE5E95FFA5421F\",\n" + 
                        "        \"payerAccount\": null,\n" + 
                        "        \"payeeAccount\": null,\n" + 
                        "        \"payChannel\": null,\n" + 
                        "        \"productDesc\": null,\n" + 
                        "        \"refundFlag\": 0,\n" + 
                        "        \"customerId\": null,\n" + 
                        "        \"coupon\": \"0\",\n" + 
                        "        \"scValue\": \"0\",\n" + 
                        "        \"mchntTmNum\": null,\n" + 
                        "        \"deviceTmNum\": null,\n" + 
                        "        \"attach\": null,\n" + 
                        "        \"transPhone\": \""+payerLogin+"\",\n" + 
                        "        \"respCode\": \"SUCCESS\",\n" + 
                        "        \"respDesc\": \"交易成功\"\n" + 
                        "    },\n" + 
                        "    \"errorCode\": null,\n" + 
                        "    \"errorMsg\": null\n" + 
                        "}";
            }
            return null;
        });

        String resultString = postPerform("test_bestpay_barcode_pay_success_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        Thread.currentThread().sleep(5000);
        resultString = postPerform("test_bestpay_barcode_pay_success_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    /**
    *
    * 翼支付 撤单
    *
    * @throws Exception
    */
   @Test
   public void test_bestpay_cancel()throws Exception{
       JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
       String payerLogin = "17623800249";
       String tradeNo = StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
       PowerMockito.mockStatic(HttpClientUtils.class);
       PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer((invocation) ->{
           String serviceUrl = invocation.getArgument(3);
           if(serviceUrl.endsWith("/barcode/placeOrder")) {
               return 
                       "{\n" + 
                       "    \"success\": true,\n" + 
                       "    \"result\": {\n" + 
                       "        \"merchantId\": \"02310106010619256\",\n" + 
                       "        \"orderNo\": \"****************\",\n" + 
                       "        \"orderReqNo\": \"****************\",\n" + 
                       "        \"orderDate\": null,\n" + 
                       "        \"transStatus\": \"B\",\n" + 
                       "        \"transAmt\": \"11\",\n" + 
                       "        \"ourTransNo\": \""+ tradeNo +"\",\n" + 
                       "        \"encodeType\": \"1\",\n" + 
                       "        \"sign\": \"C99152835F7C29081DFE5E95FFA5421F\",\n" + 
                       "        \"payerAccount\": null,\n" + 
                       "        \"payeeAccount\": null,\n" + 
                       "        \"payChannel\": null,\n" + 
                       "        \"productDesc\": null,\n" + 
                       "        \"refundFlag\": null,\n" + 
                       "        \"customerId\": null,\n" + 
                       "        \"coupon\": \"0\",\n" + 
                       "        \"scValue\": \"0\",\n" + 
                       "        \"mchntTmNum\": null,\n" + 
                       "        \"deviceTmNum\": null,\n" + 
                       "        \"attach\": null,\n" + 
                       "        \"transPhone\": \""+payerLogin+"\",\n" + 
                       "        \"respCode\": \"SUCCESS\",\n" + 
                       "        \"respDesc\": \"交易成功\"\n" + 
                       "    },\n" + 
                       "    \"errorCode\": null,\n" + 
                       "    \"errorMsg\": null\n" + 
                       "}";
           }else if(serviceUrl.endsWith("/reverse/reverse")) {
               return 
                       "{\n" + 
                       "    \"success\": true,\n" + 
                       "    \"result\": {\n" + 
                       "        \"sign\": \"3006E6AE9CC9C604C9472109410F8EE9\",\n" + 
                       "        \"refundReqNo\": \"7895237018478303\",\n" + 
                       "        \"oldOrderNo\": \"7895237018724725\",\n" + 
                       "        \"transAmt\": \"11\"\n" + 
                       "    },\n" + 
                       "    \"errorCode\": null,\n" + 
                       "    \"errorMsg\": null\n" + 
                       "}";
           }
           return null;
       });

       String resultString = postPerform("test_bestpay_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
               "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
               "biz_response.data.payer_login", payerLogin
       );
       
       resultString = postPerform("test_bestpay_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       result = JsonUtil.jsonStrToObject(resultString, Map.class);

       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
               "biz_response.data.order_status", Order.Status.CANCELED.name()
       );
   }
   
   /**
   *
   * 翼支付 退款
   *
   * @throws Exception
   */
  @Test
  public void test_bestpay_refund()throws Exception{
      JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
      String payerLogin = "17623800249";
      String tradeNo = StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
      PowerMockito.mockStatic(HttpClientUtils.class);
      PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer((invocation) ->{
          String serviceUrl = invocation.getArgument(3);
          if(serviceUrl.endsWith("/barcode/placeOrder")) {
              return 
                      "{\n" + 
                      "    \"success\": true,\n" + 
                      "    \"result\": {\n" + 
                      "        \"merchantId\": \"02310106010619256\",\n" + 
                      "        \"orderNo\": \"****************\",\n" + 
                      "        \"orderReqNo\": \"****************\",\n" + 
                      "        \"orderDate\": null,\n" + 
                      "        \"transStatus\": \"B\",\n" + 
                      "        \"transAmt\": \"11\",\n" + 
                      "        \"ourTransNo\": \""+ tradeNo +"\",\n" + 
                      "        \"encodeType\": \"1\",\n" + 
                      "        \"sign\": \"C99152835F7C29081DFE5E95FFA5421F\",\n" + 
                      "        \"payerAccount\": null,\n" + 
                      "        \"payeeAccount\": null,\n" + 
                      "        \"payChannel\": null,\n" + 
                      "        \"productDesc\": null,\n" + 
                      "        \"refundFlag\": null,\n" + 
                      "        \"customerId\": null,\n" + 
                      "        \"coupon\": \"0\",\n" + 
                      "        \"scValue\": \"0\",\n" + 
                      "        \"mchntTmNum\": null,\n" + 
                      "        \"deviceTmNum\": null,\n" + 
                      "        \"attach\": null,\n" + 
                      "        \"transPhone\": \""+payerLogin+"\",\n" + 
                      "        \"respCode\": \"SUCCESS\",\n" + 
                      "        \"respDesc\": \"交易成功\"\n" + 
                      "    },\n" + 
                      "    \"errorCode\": null,\n" + 
                      "    \"errorMsg\": null\n" + 
                      "}";
          }else if(serviceUrl.endsWith("/refund/commonRefund")) {
              return 
                      "{\n" + 
                      "    \"success\": true,\n" + 
                      "    \"result\": {\n" + 
                      "        \"sign\": \"0C1F8229A49F673DE16DE49E06D51BD9\",\n" + 
                      "        \"refundReqNo\": \"7895237001372089\",\n" + 
                      "        \"oldOrderNo\": \"7895237004244361\",\n" + 
                      "        \"transAmt\": \"11\"\n" + 
                      "    },\n" + 
                      "    \"errorCode\": null,\n" + 
                      "    \"errorMsg\": null\n" + 
                      "}";
          }
          return null;
      });

      String resultString = postPerform("test_bestpay_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
      Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
              "biz_response.data.payer_login", payerLogin
      );
      
      request.put(UpayService.REFUND_AMOUNT, "30");
      request.put(UpayService.REFUND_REQUEST_NO, "1");
      resultString = postPerform("test_bestpay_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);

      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
              "biz_response.data.order_status", Order.Status.REFUNDED.name()
      );
  }
}
