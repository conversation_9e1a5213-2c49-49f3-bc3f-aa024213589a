package com.wosai.upay.controller.upay.precreate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicBoolean;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class BankcardPrecreateTest extends BaseTestController{
    @Before
    public void init() {
        Map<String, Object> allParams = SupportUtil.buildGetAllParams(SupportUtil.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS);
        allParams.put("deposit", MapUtil.hashMap("bankcard", 1));
        mockGetAllPrams(allParams);
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
    }

    @Test
    public void testPrecreate() throws Exception {
        // 设置返回结果
        String batchBillNo = "000136";
        String sysTraceNo = "00002";
        String authNo = "000162";
        String referNumber = "************";
        String buyerUid = "6221****5811";
        String dccFlag = ThreadLocalRandom.current().nextInt(2) + "";
        PowerMockito.mockStatic(HttpClientUtils.class);
        AtomicBoolean refundDccFlag = new AtomicBoolean(false);
        AtomicBoolean refundQueryDccFlag = new AtomicBoolean(false);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation ->{
            String url = invocation.getArgument(3);
            if (url.contains("/los/checkout_counter/generate_order")) {
                // 下单
                return "{\n"
                        + "    \"code\": \"000000\",\n"
                        + "    \"msg\": \"操作成功\",\n"
                        + "    \"resp_time\": \"20231124114259\",\n"
                        + "    \"resp_data\": {\n"
                        + "        \"merchant_no\": \"8227310723004HM\",\n"
                        + "        \"channel_id\": \"28\",\n"
                        + "        \"out_order_no\": \"7895030003822448\",\n"
                        + "        \"order_create_time\": \"20231124114259\",\n"
                        + "        \"order_efficient_time\": \"20231124114659\",\n"
                        + "        \"pay_order_no\": \"23112411012001101011103721073\",\n"
                        + "        \"counter_url\": \"https://q.lakala.com/r/0000?pageStyle%3DB2B%26txnField%3D%7B%22payOrderNo%22%3A%2223112411012001101011103721073%22%7D\"\n"
                        + "    }\n"
                        + "}";

            } else if (url.contains("/searcher/base_core/trans_query")) {
                // 查单
                return "{\n"
                        + "    \"code\": \"000000\",\n"
                        + "    \"msg\": \"成功\",\n"
                        + "    \"resp_data\": {\n"
                        + "        \"pay_order_no\": null,\n"
                        + "        \"out_order_no\": \"7896259230587900\",\n"
                        + "        \"out_trade_no\": null,\n"
                        + "        \"trans_merchant_no\": \"822731070110B6S\",\n"
                        + "        \"trans_term_no\": \"E1830013\",\n"
                        + "        \"merchant_no\": \"822731070110B6S\",\n"
                        + "        \"term_no\": \"E1830013\",\n"
                        + "        \"total_amount\": \"50000\",\n"
                        + "        \"order_trade_info_list\": [{\n"
                        + "            \"log_no\": \"23************\",\n"
                        + "            \"trade_no\": \"23************\",\n"
                        + "            \"trade_ref_no\": \"" + referNumber + "\",\n"
                        + "            \"trade_type\": \"TRAN_CONSUME\",\n"
                        + "            \"trade_status\": \"SUCCESS\",\n"
                        + "            \"trade_amount\": \"50000\",\n"
                        + "            \"payer_amount\": \"50000\",\n"
                        + "            \"user_id1\": null,\n"
                        + "            \"user_id2\": null,\n"
                        + "            \"trade_time\": \"2023-07-07 00:26:10\",\n"
                        + "            \"acc_trade_no\": null,\n"
                        + "            \"payer_account_no\": \""+ buyerUid +"\",\n"
                        + "            \"payer_name\": null,\n"
                        + "            \"payer_account_bank\": \"********\",\n"
                        + "            \"acc_type\": \"C\",\n"
                        + "            \"pay_mode\": \"INSERT\",\n"
                        + "            \"client_batch_no\": \"" + batchBillNo + "\",\n"
                        + "            \"client_seq_no\": \"" + sysTraceNo + "\",\n"
                        + "            \"settle_merchant_no\": \"822731070110B6S\",\n"
                        + "            \"settle_term_no\": \"E1830013\",\n"
                        + "            \"trade_remark\": \"\",\n"
                        + "            \"tran_acc_issino\": \"********\",\n"
                        + "            \"busi_mode\": \"BS1_OUT_CARD\"\n"
                        + "        }],\n"
                        + "        \"dcc_flg\": " + dccFlag + ",\n"
                        + "        \"dcc_chg_tm\": null,\n"
                        + "        \"aut_cod\": \"" + authNo + "\",\n"
                        + "        \"bil_rat\": null,\n"
                        + "        \"bil_ccy\": null,\n"
                        + "        \"bil_amt\": null\n"
                        + "    }\n"
                        + "}";

            } else if (url.contains("/lams/trade/trade_refund") && !url.contains("trade_refund_query")) {
                // 刷卡交易退款
                String body = invocation.getArgument(5);
                Map<String, Object> bodyMap = JsonUtil.jsonStrToObject(body, Map.class);
                String originBizType = BeanUtil.getPropString(bodyMap, "req_data.origin_biz_type");
                refundDccFlag.set(Objects.equals("2", originBizType));
                return "{\n"
                        + "    \"code\": \"710039\",\n"
                        + "    \"msg\": \"执行成功\",\n"
                        + "    \"resp_data\": {\n"
                        + "        \"log_no\": \"23112310027050\",\n"
                        + "        \"trade_time\": \"20231123075530\",\n"
                        + "        \"out_trade_no\": \"7895030064877640\",\n"
                        + "        \"total_amount\": null,\n"
                        + "        \"payer_amount\": null,\n"
                        + "        \"refund_amount\": \"50000\",\n"
                        + "        \"acc_trade_no\": null,\n"
                        + "        \"origin_log_no\": \"112211173192\",\n"
                        + "        \"origin_trade_no\": null,\n"
                        + "        \"origin_out_trade_no\": null\n"
                        + "    }\n"
                        + "}";

            } else if (url.contains("/lams/trade/trade_refund_query")) {
                // 刷卡交易退款查询
                String body = invocation.getArgument(5);
                Map<String, Object> bodyMap = JsonUtil.jsonStrToObject(body, Map.class);
                String originBizType = BeanUtil.getPropString(bodyMap, "req_data.origin_biz_type");
                refundQueryDccFlag.set(Objects.equals("2", originBizType));

                return "{\n"
                        + "    \"code\": \"000000\",\n"
                        + "    \"msg\": \"执行成功\",\n"
                        + "    \"resp_data\": {\n"
                        + "        \"refund_list\": [{\n"
                        + "            \"out_trade_no\": null,\n"
                        + "            \"trade_time\": \"**************\",\n"
                        + "            \"trade_state\": \"SUCCESS\",\n"
                        + "            \"trade_no\": \"**************\",\n"
                        + "            \"refund_amount\": \"50000\",\n"
                        + "            \"pay_mode\": \"60\",\n"
                        + "            \"crd_no\": \"629152******8662\",\n"
                        + "            \"log_no\": \"**************\",\n"
                        + "            \"account_type\": null,\n"
                        + "            \"open_id\": null,\n"
                        + "            \"bank_type\": null,\n"
                        + "            \"payer_amount\": null,\n"
                        + "            \"acc_settle_amount\": null,\n"
                        + "            \"acc_mdiscount_amount\": null,\n"
                        + "            \"acc_discount_amount\": null\n"
                        + "        }]\n"
                        + "    }\n"
                        + "}";
            }
            
            return "FAIL";
        });

        // 下单
        JSONObject request = SupportUtil.buildPrecreateRequest(Order.PAYWAY_BANKCARD, Order.SUB_PAYWAY_QRCODE, "mock");
        request.put(UpayService.TOTAL_AMOUNT, "50000");
        String resultString = postPerform("testPrecreate", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", "PRECREATE_SUCCESS",
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
                );
        Thread.sleep(8000);
        resultString = postPerform("testPrefreeze_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.payer_login", buyerUid,
                        "biz_response.data.payer_uid", buyerUid,
                        "biz_response.data.order_status", Order.Status.PAID.toString(),
                        "biz_response.data.batch_bill_no", batchBillNo,
                        "biz_response.data.sys_trace_no", sysTraceNo
                );

        Map<String, Object> transaction = dataRepository.getTransactionByTsn(null, BeanUtil.getPropString(result, "biz_response.data.tsn"));
        assert transaction != null;
        // 校验外卡配置
        Map<String, Object> extraOutFileds = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        String wildCardType = MapUtil.getString(extraOutFileds, Transaction.WILD_CARD_TYPE);
        Assert.assertEquals((dccFlag.equals("0") ? "edc" : "dcc"), wildCardType);

        // 校验费率
        Map<String, Object> brankcardFee = (Map<String, Object>) BeanUtil.getNestedProperty(SupportUtil.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS + "." + TransactionParam.PARAMS_BANKCARD_FEE);
        Map<String, Object> feerateInfo = MapUtil.getMap(brankcardFee, (dccFlag.equals("0") ? "edc" : "dcc"));
        String useFeeRate = BeanUtil.getPropString(transaction, Transaction.CONFIG_SNAPSHOT + "." + TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS + "." + TransactionParam.FEE_RATE);
        Assert.assertEquals(MapUtil.getString(feerateInfo, TransactionParam.FEE), useFeeRate);
 
        // 退款
        request.put(UpayService.REFUND_REQUEST_NO, System.currentTimeMillis() + "");
        request.put(UpayService.REFUND_AMOUNT, "50000");
        resultString = postPerform("test_refund", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS);
        Thread.sleep(5000);
        resultString = postPerform("test_refund_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.REFUNDED.toString()
                        );
        assert refundDccFlag.get() && refundQueryDccFlag.get();

        transaction = dataRepository.getTransactionByTsn(null, BeanUtil.getPropString(result, "biz_response.data.tsn"));
        assert transaction != null;
        // 校验外卡配置
        extraOutFileds = MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS);
        wildCardType = MapUtil.getString(extraOutFileds, Transaction.WILD_CARD_TYPE);
        Assert.assertEquals((dccFlag.equals("0") ? "edc" : "dcc"), wildCardType);

    }
}
