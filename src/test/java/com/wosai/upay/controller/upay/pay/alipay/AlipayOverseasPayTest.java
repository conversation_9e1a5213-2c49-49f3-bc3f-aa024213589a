package com.wosai.upay.controller.upay.pay.alipay;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import com.wosai.mpay.util.AlipaySignature;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.mpay.api.alipay.overseas.AlipayOverseasConstants;
import com.wosai.mpay.api.alipay.overseas.ProtocolFields;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;


@PrepareForTest({WebUtils.class})
public class AlipayOverseasPayTest extends BaseTestController {

    @Before
    public void init() {
        Map<String, Object> basicParams = SupportUtil.buildBasicParams();
        basicParams.put("merchant_country", "HK");
        basicParams.put("currency", "HKD");
        mockGetBasicPrams(basicParams);
        
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        // 随机设置设置签名方式
        Map<String, Object> tradeParams = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(SupportUtil.ALIPAY_V1_TRADE_PARAMS), Map.class);
        List<String> signTypes = Arrays.asList(AlipayOverseasConstants.SIGN_TYPE_RSA, AlipayOverseasConstants.SIGN_TYPE_MD5, AlipayOverseasConstants.SIGN_TYPE_RSA2) ;
        String useSignType = signTypes.get(ThreadLocalRandom.current().nextInt(3));
        Map<String, Object> alipayV1TradeParams = MapUtil.getMap(tradeParams, TransactionParam.ALIPAY_V1_TRADE_PARAMS);
        alipayV1TradeParams.put(TransactionParam.SIGN_TYPE, useSignType);
        if (useSignType.equals(AlipayOverseasConstants.SIGN_TYPE_RSA)) {
            mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
        } else if (useSignType.equals(AlipayOverseasConstants.SIGN_TYPE_RSA2)){
            mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        }
        basicParams.putAll(tradeParams);
        mockGetAllPrams(SupportUtil.buildGetAllParams(basicParams));
    }

    @Test
    public void test_pay() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        request.put(UpayService.TOTAL_AMOUNT, "10000");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            Map postRequest = invocation.getArgument(3);
            String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
            if(AlipayOverseasConstants.SERVICE_NAME_PAY_BARCODE.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"biz_product\">OVERSEAS_MBARCODE_PAY</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"identity_code_type\">barcode</param>\n" + 
                        "        <param name=\"sign\">9dab5a653742e9642eb544077e1d99bb</param>\n" + 
                        "        <param name=\"alipay_seller_id\">2088331454950965</param>\n" + 
                        "        <param name=\"trans_name\">HM</param>\n" + 
                        "        <param name=\"it_b_pay\">1m</param>\n" + 
                        "        <param name=\"trans_amount\">0.30</param>\n" + 
                        "        <param name=\"extend_info\">{\"secondary_merchant_id\":\"1680001168743\",\"secondary_merchant_name\":\"H &amp; M HENNES &amp; MAURITZ HOLDING ASIA LIMITED\",\"secondary_merchant_industry\":\"5699\",\"store_id\":\"HK0003\",\"store_name\":\"H&amp;M Festival Walk\",\"terminal_id\":\"100000210007743954\"}</param>\n" + 
                        "        <param name=\"partner_trans_id\">7895237622496498</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.spot.pay</param>\n" + 
                        "        <param name=\"currency\">HKD</param>\n" + 
                        "        <param name=\"buyer_identity_code\">288687967128493801</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0405</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088732277143707</alipay_buyer_user_id>\n" + 
                        "            <alipay_pay_time>20200827205230</alipay_pay_time>\n" + 
                        "            <alipay_trans_id>2020082722001443701423538469</alipay_trans_id>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <partner_trans_id>7895237622496498</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <trans_amount>100.00</trans_amount>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>1cfc47b309daefcd0b7cb520b9489f0a</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name(),
                        "biz_response.data.payer_currency", "HKD"
                );
        
        String sn = (String) BeanUtil.getNestedProperty(result, "biz_response.data.sn");
        Map<String, Object> transaction = dataRepository.getTransactionDao().filter(Criteria.where(Transaction.ORDER_SN).is(sn)).fetchOne();
        assert transaction != null;
        Map<String, Object> tradeParams = MapUtil.getMap(MapUtil.getMap(transaction, Transaction.CONFIG_SNAPSHOT), TransactionParam.ALIPAY_V1_TRADE_PARAMS);
        assert "1.2".equals(MapUtil.getString(tradeParams, TransactionParam.FEE_RATE));
        assert "120".equals(MapUtil.getString(tradeParams, TransactionParam.FEE));
    }

    @Test
    public void test_pay_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            Map postRequest = invocation.getArgument(3);
            String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
            if(AlipayOverseasConstants.SERVICE_NAME_PAY_BARCODE.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"biz_product\">OVERSEAS_MBARCODE_PAY</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"identity_code_type\">barcode</param>\n" + 
                        "        <param name=\"sign\">9dab5a653742e9642eb544077e1d99bb</param>\n" + 
                        "        <param name=\"alipay_seller_id\">2088331454950965</param>\n" + 
                        "        <param name=\"trans_name\">HM</param>\n" + 
                        "        <param name=\"it_b_pay\">1m</param>\n" + 
                        "        <param name=\"trans_amount\">0.30</param>\n" + 
                        "        <param name=\"extend_info\">{\"secondary_merchant_id\":\"1680001168743\",\"secondary_merchant_name\":\"H &amp; M HENNES &amp; MAURITZ HOLDING ASIA LIMITED\",\"secondary_merchant_industry\":\"5699\",\"store_id\":\"HK0003\",\"store_name\":\"H&amp;M Festival Walk\",\"terminal_id\":\"100000210007743954\"}</param>\n" + 
                        "        <param name=\"partner_trans_id\">7895237622496498</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.spot.pay</param>\n" + 
                        "        <param name=\"currency\">HKD</param>\n" + 
                        "        <param name=\"buyer_identity_code\">288687967128493801</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0405</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088732277143707</alipay_buyer_user_id>\n" + 
                        "            <alipay_pay_time>20200827205230</alipay_pay_time>\n" + 
                        "            <alipay_trans_id>2020082722001443701423538469</alipay_trans_id>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <partner_trans_id>7895237622496498</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>UNKNOW</result_code>\n" + 
                        "            <trans_amount>0.30</trans_amount>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>1cfc47b309daefcd0b7cb520b9489f0a</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }else if(AlipayOverseasConstants.SERVICE_NAME_QUERY.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"partner_trans_id\">7895237697341173</param>\n" + 
                        "        <param name=\"partner\">2088431812540583</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.query</param>\n" + 
                        "        <param name=\"sign\">df17e42110ad998ff27cbee3f43ea5a5</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0354</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088802428979111</alipay_buyer_user_id>\n" + 
                        "            <alipay_trans_id>2020082622001479110518403500</alipay_trans_id>\n" + 
                        "            <alipay_trans_status>TRADE_SUCCESS</alipay_trans_status>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <out_trade_no>7895237697341173</out_trade_no>\n" + 
                        "            <partner_trans_id>7895237697341173</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <trans_amount>0.30</trans_amount>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>d1d96bc0975bd37dd34ca31421d91059</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name(),
                        "biz_response.data.payer_currency", "HKD"
                );
    }
    
    @Test
    public void test_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            Map postRequest = invocation.getArgument(3);
            String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
            if(AlipayOverseasConstants.SERVICE_NAME_PAY_BARCODE.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"biz_product\">OVERSEAS_MBARCODE_PAY</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"identity_code_type\">barcode</param>\n" + 
                        "        <param name=\"sign\">9dab5a653742e9642eb544077e1d99bb</param>\n" + 
                        "        <param name=\"alipay_seller_id\">2088331454950965</param>\n" + 
                        "        <param name=\"trans_name\">HM</param>\n" + 
                        "        <param name=\"it_b_pay\">1m</param>\n" + 
                        "        <param name=\"trans_amount\">0.30</param>\n" + 
                        "        <param name=\"extend_info\">{\"secondary_merchant_id\":\"1680001168743\",\"secondary_merchant_name\":\"H &amp; M HENNES &amp; MAURITZ HOLDING ASIA LIMITED\",\"secondary_merchant_industry\":\"5699\",\"store_id\":\"HK0003\",\"store_name\":\"H&amp;M Festival Walk\",\"terminal_id\":\"100000210007743954\"}</param>\n" + 
                        "        <param name=\"partner_trans_id\">7895237622496498</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.spot.pay</param>\n" + 
                        "        <param name=\"currency\">HKD</param>\n" + 
                        "        <param name=\"buyer_identity_code\">288687967128493801</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0405</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088732277143707</alipay_buyer_user_id>\n" + 
                        "            <alipay_pay_time>20200827205230</alipay_pay_time>\n" + 
                        "            <alipay_trans_id>2020082722001443701423538469</alipay_trans_id>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <partner_trans_id>7895237622496498</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <trans_amount>0.30</trans_amount>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>1cfc47b309daefcd0b7cb520b9489f0a</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }else if(AlipayOverseasConstants.SERVICE_NAME_REFUND.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"partner_trans_id\">7895237853008757</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.spot.refund</param>\n" + 
                        "        <param name=\"sign\">0950d1fd04565df9ea310ad8e89da92b</param>\n" + 
                        "        <param name=\"refund_amount\">139.50</param>\n" + 
                        "        <param name=\"currency\">HKD</param>\n" + 
                        "        <param name=\"is_sync\">Y</param>\n" + 
                        "        <param name=\"partner_refund_id\">7895237665811835</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_trans_id>2020081722001476561440749349</alipay_trans_id>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <partner_refund_id>7895237665811835</partner_refund_id>\n" + 
                        "            <partner_trans_id>7895237853008757</partner_trans_id>\n" + 
                        "            <refund_amount>0.30</refund_amount>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>724a3637f7cbff5ef972ecace68258fd</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );

        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            Map postRequest = invocation.getArgument(3);
            String method = MapUtil.getString(postRequest, ProtocolFields.SERVICE);
            if(AlipayOverseasConstants.SERVICE_NAME_PAY_BARCODE.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"biz_product\">OVERSEAS_MBARCODE_PAY</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"identity_code_type\">barcode</param>\n" + 
                        "        <param name=\"sign\">9dab5a653742e9642eb544077e1d99bb</param>\n" + 
                        "        <param name=\"alipay_seller_id\">2088331454950965</param>\n" + 
                        "        <param name=\"trans_name\">HM</param>\n" + 
                        "        <param name=\"it_b_pay\">1m</param>\n" + 
                        "        <param name=\"trans_amount\">0.30</param>\n" + 
                        "        <param name=\"extend_info\">{\"secondary_merchant_id\":\"1680001168743\",\"secondary_merchant_name\":\"H &amp; M HENNES &amp; MAURITZ HOLDING ASIA LIMITED\",\"secondary_merchant_industry\":\"5699\",\"store_id\":\"HK0003\",\"store_name\":\"H&amp;M Festival Walk\",\"terminal_id\":\"100000210007743954\"}</param>\n" + 
                        "        <param name=\"partner_trans_id\">7895237622496498</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.overseas.spot.pay</param>\n" + 
                        "        <param name=\"currency\">HKD</param>\n" + 
                        "        <param name=\"buyer_identity_code\">288687967128493801</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <alipay_buyer_login_id>852-****0405</alipay_buyer_login_id>\n" + 
                        "            <alipay_buyer_user_id>2088732277143707</alipay_buyer_user_id>\n" + 
                        "            <alipay_pay_time>20200827205230</alipay_pay_time>\n" + 
                        "            <alipay_trans_id>2020082722001443701423538469</alipay_trans_id>\n" + 
                        "            <currency>HKD</currency>\n" + 
                        "            <partner_trans_id>7895237622496498</partner_trans_id>\n" + 
                        "            <payment_inst>ALIPAYHK</payment_inst>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <trans_amount>0.30</trans_amount>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>1cfc47b309daefcd0b7cb520b9489f0a</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }else if(AlipayOverseasConstants.SERVICE_NAME_CANCEL.equals(method)) {
                return  "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                        "<alipay>\n" + 
                        "    <is_success>T</is_success>\n" + 
                        "    <request>\n" + 
                        "        <param name=\"out_trade_no\">7895237036835408</param>\n" + 
                        "        <param name=\"partner\">2088331454950965</param>\n" + 
                        "        <param name=\"_input_charset\">GBK</param>\n" + 
                        "        <param name=\"service\">alipay.acquire.cancel</param>\n" + 
                        "        <param name=\"sign\">3f862125f0cd7f9b80786ba36c5ede73</param>\n" + 
                        "        <param name=\"sign_type\">MD5</param>\n" + 
                        "        <param name=\"timestamp\">1597911890692</param>\n" + 
                        "    </request>\n" + 
                        "    <response>\n" + 
                        "        <alipay>\n" + 
                        "            <action>cancel</action>\n" + 
                        "            <out_trade_no>7895237036835408</out_trade_no>\n" + 
                        "            <result_code>SUCCESS</result_code>\n" + 
                        "            <retry_flag>N</retry_flag>\n" + 
                        "            <trade_no>2020082022001456251420658532</trade_no>\n" + 
                        "        </alipay>\n" + 
                        "    </response>\n" + 
                        "    <sign>0fce71b558c825785899d16289f99055</sign>\n" + 
                        "    <sign_type>MD5</sign_type>\n" + 
                        "</alipay>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
