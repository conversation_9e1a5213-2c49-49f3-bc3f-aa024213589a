package com.wosai.upay.controller.upay.precreate.alipay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.alipay.AlipayV1Methods;
import com.wosai.mpay.api.alipay.BusinessV1Fields;
import com.wosai.mpay.api.alipay.ProtocolV1Fields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class AlipayV1PrecreateTest extends BaseTestController{
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
    }
    
    /**
     * 
     * 支付宝1.0 通道下单支付成功
     * 
     * @throws Exception
     */
    @Test
    public void test_alipay_c2b()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V1_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 2, "ov");
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String requestTradeNo = MapUtil.getString(postRequest, BusinessV1Fields.OUT_TRADE_NO, "123456789");
                if(AlipayV1Methods.ALIPAY_ACQUIRE_PRECREATE.equals(MapUtil.getString(postRequest, ProtocolV1Fields.SERVICE))) {
                    return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<alipay>\n" + 
                            "    <is_success>T</is_success>\n" + 
                            "    <request>\n" + 
                            "        <param name=\"extend_params\">{\"AGENT_ID\":\"10610050a1\"}</param>\n" + 
                            "        <param name=\"_input_charset\">gbk</param>\n" + 
                            "        <param name=\"subject\">" + request.getString(UpayService.SUBJECT) +"</param>\n" + 
                            "        <param name=\"dynamic_id\">" + request.getString(UpayService.DYNAMIC_ID) + "</param>\n" + 
                            "        <param name=\"sign\">727fa1993a14a054c321caa9c53c2892</param>\n" + 
                            "        <param name=\"it_b_pay\">1m</param>\n" + 
                            "        <param name=\"body\">"+ request.getString(UpayService.SUBJECT) +"</param>\n" + 
                            "        <param name=\"notify_url\">http://gateway.shouqianba.com/upay/v2/notify/alipay/82cbc114c99133541602159aeb8d5ef7/af04f%267895238320587535%261%261580000000732951%266edceebc-3bfe-4404-ba98-015a6a996f7c%261%261570801600705%268850%260%260</param>\n" + 
                            "        <param name=\"product_code\">BARCODE_PAY_OFFLINE</param>\n" + 
                            "        <param name=\"dynamic_id_type\">bar_code</param>\n" + 
                            "        <param name=\"out_trade_no\">"+ requestTradeNo +"</param>\n" + 
                            "        <param name=\"partner\">2088121609449116</param>\n" + 
                            "        <param name=\"service\">alipay.acquire.createandpay</param>\n" + 
                            "        <param name=\"total_fee\">" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</param>\n" + 
                            "        <param name=\"sign_type\">MD5</param>\n" + 
                            "    </request>\n" + 
                            "    <response>\n" + 
                            "        <alipay>\n" + 
                            "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                            "            <out_trade_no>"+ requestTradeNo +"</out_trade_no>\n" + 
                            "            <result_code>SUCCESS</result_code>\n" + 
                            "            <qr_code>https://shouqianba.com</qr_code>\n" + 
                            "        </alipay>\n" + 
                            "    </response>\n" + 
                            "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                            "    <sign_type>MD5</sign_type>\n" + 
                            "</alipay>";
                }else {
                    return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<alipay>\n" + 
                            "    <is_success>T</is_success>\n" + 
                            "    <request>\n" + 
                            "        <param name=\"out_trade_no\">7895238312021714</param>\n" + 
                            "        <param name=\"partner\">2088021206628513</param>\n" + 
                            "        <param name=\"_input_charset\">gbk</param>\n" + 
                            "        <param name=\"service\">alipay.acquire.query</param>\n" + 
                            "        <param name=\"sign\">9aa783cf0a3185b8a8e133aa23a3a9e4</param>\n" + 
                            "        <param name=\"sign_type\">MD5</param>\n" + 
                            "    </request>\n" + 
                            "    <response>\n" + 
                            "        <alipay>\n" + 
                            "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                            "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                            "            <out_trade_no>7895238312021714</out_trade_no>\n" + 
                            "            <partner>2088021206628513</partner>\n" + 
                            "            <result_code>SUCCESS</result_code>\n" + 
                            "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                            "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                            "            <trade_status>TRADE_SUCCESS</trade_status>\n" + 
                            "        </alipay>\n" + 
                            "    </response>\n" + 
                            "    <sign>be85bb560b541b0ed4f99068da1daaef</sign>\n" + 
                            "    <sign_type>MD5</sign_type>\n" + 
                            "</alipay>";
                }
            }
        });
        
        String resultString = postPerform("test_alipay_c2b_1", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.status", "CREATED",
                        "biz_response.data.order_status", "CREATED"
                );
        Thread.sleep(5000);
        
        resultString = postPerform("test_alipay_c2b_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.payer_login", payerLogin,
                "biz_response.data.payer_uid", payerUid
        );
    }
    
    @Test
    public void test_alipay_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_WAP_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(1, 3, "ov");
        long payTime = System.currentTimeMillis();
        String tradeNo = formatTimeString("yyyyMMddHHmmss", payTime) + payTime;
        String payerLogin = "157******47";
        String payerUid = "2088022753614592";
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String requestTradeNo = MapUtil.getString(postRequest, BusinessV1Fields.OUT_TRADE_NO, "123456789");
                if(AlipayV1Methods.ALIPAY_WAP_CREATE_DIRECT_PAY_BY_USER.equals(MapUtil.getString(postRequest, ProtocolV1Fields.SERVICE))) {
                    return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<alipay>\n" + 
                            "    <is_success>T</is_success>\n" + 
                            "    <request>\n" + 
                            "        <param name=\"extend_params\">{\"AGENT_ID\":\"10610050a1\"}</param>\n" + 
                            "        <param name=\"_input_charset\">gbk</param>\n" + 
                            "        <param name=\"subject\">" + request.getString(UpayService.SUBJECT) +"</param>\n" + 
                            "        <param name=\"dynamic_id\">" + request.getString(UpayService.DYNAMIC_ID) + "</param>\n" + 
                            "        <param name=\"sign\">727fa1993a14a054c321caa9c53c2892</param>\n" + 
                            "        <param name=\"it_b_pay\">1m</param>\n" + 
                            "        <param name=\"body\">"+ request.getString(UpayService.SUBJECT) +"</param>\n" + 
                            "        <param name=\"notify_url\">http://gateway.shouqianba.com/upay/v2/notify/alipay/82cbc114c99133541602159aeb8d5ef7/af04f%267895238320587535%261%261580000000732951%266edceebc-3bfe-4404-ba98-015a6a996f7c%261%261570801600705%268850%260%260</param>\n" + 
                            "        <param name=\"product_code\">BARCODE_PAY_OFFLINE</param>\n" + 
                            "        <param name=\"dynamic_id_type\">bar_code</param>\n" + 
                            "        <param name=\"out_trade_no\">"+ requestTradeNo +"</param>\n" + 
                            "        <param name=\"partner\">2088121609449116</param>\n" + 
                            "        <param name=\"service\">alipay.acquire.createandpay</param>\n" + 
                            "        <param name=\"total_fee\">" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</param>\n" + 
                            "        <param name=\"sign_type\">MD5</param>\n" + 
                            "    </request>\n" + 
                            "    <response>\n" + 
                            "        <alipay>\n" + 
                            "            <gmt_payment>" + formatTimeString("yyyy-MM-dd HH:mm:ss", payTime) + "</gmt_payment>\n" + 
                            "            <out_trade_no>"+ requestTradeNo +"</out_trade_no>\n" + 
                            "            <result_code>SUCCESS</result_code>\n" + 
                            "            <qr_code>https://shouqianba.com</qr_code>\n" + 
                            "        </alipay>\n" + 
                            "    </response>\n" + 
                            "    <sign>3021561b802639bb1483ebb409af486d</sign>\n" + 
                            "    <sign_type>MD5</sign_type>\n" + 
                            "</alipay>";
                }else {
                    return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + 
                            "<alipay>\n" + 
                            "    <is_success>T</is_success>\n" + 
                            "    <request>\n" + 
                            "        <param name=\"out_trade_no\">7895238312021714</param>\n" + 
                            "        <param name=\"partner\">2088021206628513</param>\n" + 
                            "        <param name=\"_input_charset\">gbk</param>\n" + 
                            "        <param name=\"service\">alipay.acquire.query</param>\n" + 
                            "        <param name=\"sign\">9aa783cf0a3185b8a8e133aa23a3a9e4</param>\n" + 
                            "        <param name=\"sign_type\">MD5</param>\n" + 
                            "    </request>\n" + 
                            "    <response>\n" + 
                            "        <alipay>\n" + 
                            "            <buyer_logon_id>" + payerLogin + "</buyer_logon_id>\n" + 
                            "            <buyer_user_id>" + payerUid + "</buyer_user_id>\n" + 
                            "            <out_trade_no>7895238312021714</out_trade_no>\n" + 
                            "            <partner>2088021206628513</partner>\n" + 
                            "            <result_code>SUCCESS</result_code>\n" + 
                            "            <total_fee>" + StringUtils.cents2yuan(request.getLongValue(UpayService.TOTAL_AMOUNT)) + "</total_fee>\n" + 
                            "            <trade_no>" + tradeNo + "</trade_no>\n" + 
                            "            <trade_status>TRADE_SUCCESS</trade_status>\n" + 
                            "        </alipay>\n" + 
                            "    </response>\n" + 
                            "    <sign>be85bb560b541b0ed4f99068da1daaef</sign>\n" + 
                            "    <sign_type>MD5</sign_type>\n" + 
                            "</alipay>";
                }
            }
        });
        
        String resultString = postPerform("test_alipay_wap_1", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.status", "CREATED",
                        "biz_response.data.order_status", "CREATED"
                );
        Thread.sleep(5000);
        
        resultString = postPerform("test_alipay_wap_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
    }
}
