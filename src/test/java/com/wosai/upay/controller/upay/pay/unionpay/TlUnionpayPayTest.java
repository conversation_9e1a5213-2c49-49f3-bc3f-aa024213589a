package com.wosai.upay.controller.upay.pay.unionpay;

import static org.junit.Assert.assertTrue;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.unionqrcode.BusinessFields;
import com.wosai.mpay.api.unionqrcode.UnionPayQRCodeConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;


@PrepareForTest({WebUtils.class}) // 所有需要测试的类列在此处，适用于模拟final类或有final, private, static, native方法的类
public class TlUnionpayPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildTlBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
    }

    @Test
    public void test_pay() throws Exception {
        AtomicBoolean isSetAreaInfo = new AtomicBoolean();
        Map params = SupportUtil.buildTlGetAllParams(SupportUtil.UNION_PAY_TL_TRADE_PARAMS);
        String areaInfo = "156" + StringUtils.leftPad(ThreadLocalRandom.current().nextInt(9999)+ "", 4, "0");
        params.put(TransactionParam.AREA_INFO, areaInfo);
        mockGetAllPrams(params);
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        final String configAreaInfo = areaInfo;
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(3);
                String method = MapUtil.getString(postRequest, BusinessFields.REQ_TYPE);
                String reqAreaInfo = MapUtil.getString(postRequest, BusinessFields.AREA_INFO);
                isSetAreaInfo.set(configAreaInfo.equals(reqAreaInfo));
                if(UnionPayQRCodeConstants.REQ_TYPE_BARCODE.equals(method)) {
                    return  "{\n" + 
                            "    \"payerInfo\": \"e2FjY05vPTYyMjc0MDA4MDcxMzUxMDQmY2FyZEF0dHI9MDImaXNzQ29kZT0wNTI1MzQ1NH0=\",\n" + 
                            "    \"acqCode\": \"48210001\",\n" + 
                            "    \"reqType\": \"0310000903\",\n" + 
                            "    \"sign\": \"iabKsEaNPLPdeTicAHApDIaotllccg+fR8V90fd3LrVXaEnZmboBT8VMayS45R8dN3TfrCQ6ACZ2Vc4BjiiPpxelBNxq7ZFa5YKJtJshKysG6R65fQLgU7TGeLj7DwP3bUoRGOEb70lj8kgW0znbEkBC3MP++Gl3zztEoB675G5875I3OTFrFeDpHZRGG3Zq1G6Hp8sDlc10yfVlp0AlkV3lomd31LLQuPvY15ecSDEgv8wU3h9tvcalra0Jq4MuERxJXtD5OlxZBJZVrbXAJn1a/cydHNxVr2HE8LIXWPr8lC0XPt+g14ID5hfKHHPBHeL+EoVgpJ+YXS/bVLhtOQ==\",\n" + 
                            "    \"respMsg\": \"成功[0000000]\",\n" + 
                            "    \"comInfo\": \"e0YwPTAyMDAmRjI1PTAwJkYzPTAwMDAwMCZGMzc9MjM3ODgzNjY2NjUwJkY2MD0wMzAwMDAwMDAwMDAzMDAxMDAwMDAwMDQwMDUwMDI0MDB9\",\n" + 
                            "    \"settleDate\": \"0818\",\n" + 
                            "    \"voucherNum\": \"08200818326803718181\",\n" + 
                            "    \"version\": \"1.0.0\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"settleKey\": \"48210001   00049992   3718180818090440\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        assertTrue(isSetAreaInfo.get());
    }
    
    @Test
    public void test_pay_with_query() throws Exception {
        mockGetAllPrams(SupportUtil.buildTlGetAllParams(SupportUtil.UNION_PAY_TL_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(3);
                String method = MapUtil.getString(postRequest, BusinessFields.REQ_TYPE);
                if(UnionPayQRCodeConstants.REQ_TYPE_BARCODE.equals(method)) {
                    return  "{\n" + 
                            "    \"payerInfo\": \"e2FjY05vPTYyMjc0MDA4MDcxMzUxMDQmY2FyZEF0dHI9MDImaXNzQ29kZT0wNTI1MzQ1NH0=\",\n" + 
                            "    \"acqCode\": \"48210001\",\n" + 
                            "    \"reqType\": \"0310000903\",\n" + 
                            "    \"sign\": \"iabKsEaNPLPdeTicAHApDIaotllccg+fR8V90fd3LrVXaEnZmboBT8VMayS45R8dN3TfrCQ6ACZ2Vc4BjiiPpxelBNxq7ZFa5YKJtJshKysG6R65fQLgU7TGeLj7DwP3bUoRGOEb70lj8kgW0znbEkBC3MP++Gl3zztEoB675G5875I3OTFrFeDpHZRGG3Zq1G6Hp8sDlc10yfVlp0AlkV3lomd31LLQuPvY15ecSDEgv8wU3h9tvcalra0Jq4MuERxJXtD5OlxZBJZVrbXAJn1a/cydHNxVr2HE8LIXWPr8lC0XPt+g14ID5hfKHHPBHeL+EoVgpJ+YXS/bVLhtOQ==\",\n" + 
                            "    \"respMsg\": \"成功[0000000]\",\n" + 
                            "    \"comInfo\": \"e0YwPTAyMDAmRjI1PTAwJkYzPTAwMDAwMCZGMzc9MjM3ODgzNjY2NjUwJkY2MD0wMzAwMDAwMDAwMDAzMDAxMDAwMDAwMDQwMDUwMDI0MDB9\",\n" + 
                            "    \"settleDate\": \"0818\",\n" + 
                            "    \"voucherNum\": \"08200818326803718181\",\n" + 
                            "    \"version\": \"1.0.0\",\n" + 
                            "    \"respCode\": \"99\",\n" + 
                            "    \"settleKey\": \"48210001   00049992   3718180818090440\"\n" + 
                            "}";
                }else if(UnionPayQRCodeConstants.REQ_TYPE_BARCODE_QUERY.equals(method)) {
                    return "{\n" + 
                            "    \"orderNo\": \"7895237888643680\",\n" + 
                            "    \"couponInfo\": \"W3siaWQiOiIxMjAxMjAyMDA0MjIxNzQ3IiwiZGVzYyI6IumTtuiBlOS6keWNly3lt57luILmu6ExNeWHjzQiLCJ0eXBlIjoiQ1AwMSIsInNwbnNySWQiOiIwMDAxMDAwMCIsIm9mZnN0QW10IjoiNDAwIn1d\",\n" + 
                            "    \"sign\": \"eApZJnWBiRKZamIRdFPcQF67u1Z0N3eDSOIo+A96oZDYvUUuJd4to2X9/rTdV4OGrhX/35jciQZSaKAyNQMyd6oSvnmsURj589Iz0cOhgmOBg2E7VPtKQxZiMUWDrjKqymCwoaLtsv6avyyzZkcVMuG2iiwiV7Y2G+lpScmQkkpky5s91X2clzsfz7by7rsW3/4non0iIk0J/zW1A8JOwa4QFYWl86D4rkHHlxISMNTMoDlYmsAO/oDJ14kCf91w8yYS4sADCVP7MmjPBlqcWDvYLBqkIvcULBO1hmTN4c1YktSLe941sFQ2F3tk6aAcCej292680iFTAbpLXOJDhg==\",\n" + 
                            "    \"settleDate\": \"0818\",\n" + 
                            "    \"origTxnAmt\": \"1500\",\n" + 
                            "    \"voucherNum\": \"95200818357351442102\",\n" + 
                            "    \"version\": \"1.0.0\",\n" + 
                            "    \"settleKey\": \"48210001   00049992   0848300818095545\",\n" + 
                            "    \"orderTime\": \"20200818095535\",\n" + 
                            "    \"payerInfo\": \"e2FjY05vPTYyMTc5MDI3MDAwMDc1NzMyMDUmY2FyZEF0dHI9MDEmaXNzQ29kZT0wMTA0NzMwMCZuYW1lPei1temRq30=\",\n" + 
                            "    \"acqCode\": \"48210001\",\n" + 
                            "    \"reqType\": \"0540000903\",\n" + 
                            "    \"origRespMsg\": \"成功[0000000]\",\n" + 
                            "    \"respMsg\": \"成功[0000000]\",\n" + 
                            "    \"comInfo\": \"e0YwPTAyMDAmRjI1PTAwJkYzPTAwMDAwMCZGMzc9MjM3ODg4NjQzNjgwJkY2MD0wMzAwMDAwMDAwMDA3MDAxMDAwMDAwMDQwMjUwMDExMDB9\",\n" + 
                            "    \"currencyCode\": \"156\",\n" + 
                            "    \"origRespCode\": \"00\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"txnAmt\": \"1500\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
}
