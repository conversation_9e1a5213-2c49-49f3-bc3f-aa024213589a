package com.wosai.upay.controller.upay.pay.giftcard;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.giftCard.GiftCardMethods;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class GiftCardPayTest extends BaseTestController {
    
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.GIFT_CARD_TRADE_PARAMS));
    }

    @Test
    public void test_pay() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("43785412835634638547");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(GiftCardMethods.GIFTCARD_REDEEM.equals(method)) {
                    return  "{\n" + 
                            "    \"giftcard_redeem_response\": {\n" + 
                            "        \"buyer_pay_amount\": 0.30,\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"MDISCOUNT\"\n" + 
                            "        }, {\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"PCARD\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-28 15:43:21\",\n" + 
                            "        \"invoice_amount\": 0.0,\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237734596699\",\n" + 
                            "        \"pay_amount\": \"0.30\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"store_name\": \"精品牛肉面上海御青路店\",\n" + 
                            "        \"total_amount\": 0.30,\n" + 
                            "        \"trade_no\": \"2020082800000707\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"EOkRKx/rEmcmKVfMVhhvNmZZQZehRSxXaDG53rMWRSghoB3tUDPlZIyNiNt/XO3OBRmQj1QQ7J+ONvQ7NwJNROpZlEwq90QglfGM7EdnxW7pHRR6GRfgBnE29MA+rcglpVdmhNs4WH3jHNfvSl6r5AtxLizNJ4FFBnlKllKbT9XHztzL+De5GKgqJOEXgY8boRsn2+OJ4Xev0ZMYS0EIGtrt0dC2E3juViPSV6SIYdvESVutzdw2hJiWZuxnM33Na+1f2TChbYhrFxFDcmwp7EXjm41phI/tFPIomupGmJyGqib0WcGkrPu06kQA0hR6bgj5M7CmwB5KCgHkF+zo2Q==\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_pay_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("43785412835634638547");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(GiftCardMethods.GIFTCARD_REDEEM.equals(method)) {
                    return  "{\n" + 
                            "    \"giftcard_redeem_response\": {\n" + 
                            "        \"buyer_pay_amount\": 0.30,\n" + 
                            "        \"code\": \"10003\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"MDISCOUNT\"\n" + 
                            "        }, {\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"PCARD\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-28 15:43:21\",\n" + 
                            "        \"invoice_amount\": 0.0,\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237734596699\",\n" + 
                            "        \"pay_amount\": \"0.30\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"store_name\": \"精品牛肉面上海御青路店\",\n" + 
                            "        \"total_amount\": 0.30,\n" + 
                            "        \"trade_no\": \"2020082800000707\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"EOkRKx/rEmcmKVfMVhhvNmZZQZehRSxXaDG53rMWRSghoB3tUDPlZIyNiNt/XO3OBRmQj1QQ7J+ONvQ7NwJNROpZlEwq90QglfGM7EdnxW7pHRR6GRfgBnE29MA+rcglpVdmhNs4WH3jHNfvSl6r5AtxLizNJ4FFBnlKllKbT9XHztzL+De5GKgqJOEXgY8boRsn2+OJ4Xev0ZMYS0EIGtrt0dC2E3juViPSV6SIYdvESVutzdw2hJiWZuxnM33Na+1f2TChbYhrFxFDcmwp7EXjm41phI/tFPIomupGmJyGqib0WcGkrPu06kQA0hR6bgj5M7CmwB5KCgHkF+zo2Q==\"\n" + 
                            "}";
                }else if(GiftCardMethods.GIFTCARD_REDEEM_QUERY.equals(method)) {
                    return "{\n" + 
                            "    \"giftcard_redeem_query_response\": {\n" + 
                            "        \"buyer_pay_amount\": 0.0,\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": 0.0,\n" + 
                            "            \"fund_channel\": \"MDISCOUNT\"\n" + 
                            "        }, {\n" + 
                            "            \"amount\": 0.0,\n" + 
                            "            \"fund_channel\": \"PCARD\"\n" + 
                            "        }],\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237683668614\",\n" + 
                            "        \"pay_amount\": 0.30,\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"terminal_id\": \"100013330012187105\",\n" + 
                            "        \"total_amount\": 0.30,\n" + 
                            "        \"trade_no\": \"2020082600000309\",\n" + 
                            "        \"trade_status\": \"TRADE_SUCCESS\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"WpuT07txm/QcpTaYPs2vCl6ORIhlxQ9QDkU6wJ8c9rpNYHvac1iU8+cWxV9uc3euRaqJj2uEOlf5InI+WM1eUvn4+nmQawElH8s0jURXE8xYn1imwh0R/AMqzJYNilhonaMZtNhqzaupQCixM5nK7J39YSqj0C+gsIrWiVBnQ09fHYoHQOoRqjkBcXfleRlIhSC83iP0Z1gzDN/QKzCXkg7yj6w2N5Xj/7l7UIBnWDBcQMwiDcnEl4WEmVm1JlvHzOqrm54WIRqByHz8Py+MNVV4JvWJMMJLMZW775+jr/a8gpYdn9LlyhRTyP3uU+x5A+hlZGEU0f2r6JOVLsuklQ==\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_giftcard_refund()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("43785412835634638547");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(GiftCardMethods.GIFTCARD_REDEEM.equals(method)) {
                    return  "{\n" + 
                            "    \"giftcard_redeem_response\": {\n" + 
                            "        \"buyer_pay_amount\": 0.30,\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": 0,\n" + 
                            "            \"fund_channel\": \"MDISCOUNT\"\n" + 
                            "        }, {\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"PCARD\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-28 15:43:21\",\n" + 
                            "        \"invoice_amount\": 0.0,\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237734596699\",\n" + 
                            "        \"pay_amount\": \"0.30\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"store_name\": \"精品牛肉面上海御青路店\",\n" + 
                            "        \"total_amount\": 0.30,\n" + 
                            "        \"trade_no\": \"2020082800000707\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"EOkRKx/rEmcmKVfMVhhvNmZZQZehRSxXaDG53rMWRSghoB3tUDPlZIyNiNt/XO3OBRmQj1QQ7J+ONvQ7NwJNROpZlEwq90QglfGM7EdnxW7pHRR6GRfgBnE29MA+rcglpVdmhNs4WH3jHNfvSl6r5AtxLizNJ4FFBnlKllKbT9XHztzL+De5GKgqJOEXgY8boRsn2+OJ4Xev0ZMYS0EIGtrt0dC2E3juViPSV6SIYdvESVutzdw2hJiWZuxnM33Na+1f2TChbYhrFxFDcmwp7EXjm41phI/tFPIomupGmJyGqib0WcGkrPu06kQA0hR6bgj5M7CmwB5KCgHkF+zo2Q==\"\n" + 
                            "}";
                }else if(GiftCardMethods.GIFTCARD_REDEEM_REFUND.equals(method)) {
                    return "{\n" + 
                            "    \"giftcard_refund_response\": {\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"fund_change\": \"Y\",\n" + 
                            "        \"gmt_refund_pay\": \"2020-08-28 15:36:46\",\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237734116642\",\n" + 
                            "        \"refund_detail_item_list\": [{\n" + 
                            "            \"amount\": 0,\n" + 
                            "            \"fund_channel\": \"MDISCOUNT\"\n" + 
                            "        }, {\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"PCARD\"\n" + 
                            "        }],\n" + 
                            "        \"refund_fee\": 0.30,\n" + 
                            "        \"trade_no\": \"2020082800000724\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"Yg7bs7bhvGbp3HgS+M4PNGAIR8Ybxo6AE8iFzV0gzSSnuGHpKwX1+3U3Y1G7RX4CIeMWRUouqi76rOOBY6enfxFaMqYJHVZCw+np3R5ZCHx3YSfmK2XhNJYUfVdiR4pWhXfpqYKf3Eb+FATIT65NrkgtEnECLikrCx6VQAvbwmlLnrJQ8axmLljQ8ykbBPmUH/B77Gd/aoxjggSbA9Ks6DJi3S99CYrOEj0mx4V/dEzjYeqSJloS5xQQU3vQqE2PljMr5WVrfudMCUX539yvsleZtsaK8k6mvcBEyzDvISlUuUYimOGGfNwd8RKLgoFF5vmLouvQrqkZBqwXnjlLCg==\"\n" + 
                            "}";
                }
                return "fail";
            }
        });

        String resultString = postPerform("test_giftcard_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_giftcard_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_giftcard_cancel()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("43785412835634638547");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                Map postRequest = invocation.getArgument(4);
                String method = MapUtil.getString(postRequest, ProtocolV2Fields.METHOD);
                if(GiftCardMethods.GIFTCARD_REDEEM.equals(method)) {
                    return  "{\n" + 
                            "    \"giftcard_redeem_response\": {\n" + 
                            "        \"buyer_pay_amount\": 0.30,\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"fund_bill_list\": [{\n" + 
                            "            \"amount\": 0,\n" + 
                            "            \"fund_channel\": \"MDISCOUNT\"\n" + 
                            "        }, {\n" + 
                            "            \"amount\": 0.30,\n" + 
                            "            \"fund_channel\": \"PCARD\"\n" + 
                            "        }],\n" + 
                            "        \"gmt_payment\": \"2020-08-28 15:43:21\",\n" + 
                            "        \"invoice_amount\": 0.0,\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237734596699\",\n" + 
                            "        \"pay_amount\": \"0.30\",\n" + 
                            "        \"receipt_amount\": \"0.30\",\n" + 
                            "        \"store_name\": \"精品牛肉面上海御青路店\",\n" + 
                            "        \"total_amount\": 0.30,\n" + 
                            "        \"trade_no\": \"2020082800000707\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"EOkRKx/rEmcmKVfMVhhvNmZZQZehRSxXaDG53rMWRSghoB3tUDPlZIyNiNt/XO3OBRmQj1QQ7J+ONvQ7NwJNROpZlEwq90QglfGM7EdnxW7pHRR6GRfgBnE29MA+rcglpVdmhNs4WH3jHNfvSl6r5AtxLizNJ4FFBnlKllKbT9XHztzL+De5GKgqJOEXgY8boRsn2+OJ4Xev0ZMYS0EIGtrt0dC2E3juViPSV6SIYdvESVutzdw2hJiWZuxnM33Na+1f2TChbYhrFxFDcmwp7EXjm41phI/tFPIomupGmJyGqib0WcGkrPu06kQA0hR6bgj5M7CmwB5KCgHkF+zo2Q==\"\n" + 
                            "}";
                }else if(GiftCardMethods.GIFTCARD_REDEEM_CANCEL.equals(method)) {
                    return "{\n" + 
                            "    \"giftcard_redeem_cancel_response\": {\n" + 
                            "        \"action\": \"refund\",\n" + 
                            "        \"code\": \"10000\",\n" + 
                            "        \"msg\": \"接口调用成功\",\n" + 
                            "        \"out_trade_no\": \"7895237641415538\",\n" + 
                            "        \"retry_flag\": \"N\",\n" + 
                            "        \"trade_no\": \"2020082400000066\"\n" + 
                            "    },\n" + 
                            "    \"sign\": \"i0BJA6IpdEVTFe9fz/v8v58r5SFSau/Ek6EsmlYddPOctcCu8/ILOIcghs/4zcGPhD/LcYIM55kQrI9EitW+6ksMymQtjtik4ioqfvyKAQHhwwAivcy8R3C2LIIbm9cJq8DtAbVcZ+12Du8wMmaoTMBI5hbImqbDYLj5jdpInPx5BkX9WC0OTKeoj7nX7Z8jgMI81IUGy5Szax5BPbCStkZ3Em9FCTudER45pXjBSgWxEvDHrP5i8nP8lkUyrpW0NzFCXr0ZEwbn5QSyX4yXPere47l6d6fqxS2CFzof/MPqWqnMYnDa8AcBTztfaGUQe4S/VE45kVtX6fIRHGFubA==\"\n" + 
                            "}";
                }
                return "fail";
            }
        });

        String resultString = postPerform("test_giftcard_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        resultString = postPerform("test_giftcard_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
