package com.wosai.upay.controller.upay.precreate.unionpay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class LKLOpenV3UnionpayPrecreateTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA_PRIVATE_KEY);
    }

    @Test
    public void test_unionpay_wap_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPrecreateRequest(Payway.UNIONPAY.getCode(), SubPayway.WAP.getCode(), "6RRQFiUev7nCaxUuGpYSSKpIvDiCXFiz9Yjy+0soyo6yq+gTN4gG//ZPR+Q8NGNF");
        request.put(UpayService.TOTAL_AMOUNT, "1000");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.any(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/api/v3/labs/trans/preorder")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"20240110152449\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"merchant_no\": \"822293590018695\",\n"
                            + "        \"out_trade_no\": \"7894259268283276\",\n"
                            + "        \"trade_no\": \"20240110110113330266215782310718\",\n"
                            + "        \"log_no\": \"66215782310718\",\n"
                            + "        \"settle_merchant_no\": \"\",\n"
                            + "        \"settle_term_no\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"code\": \"\",\n"
                            + "            \"code_image\": \"\",\n"
                            + "            \"prepay_id\": \"\",\n"
                            + "            \"app_id\": \"\",\n"
                            + "            \"pay_sign\": \"\",\n"
                            + "            \"time_stamp\": \"\",\n"
                            + "            \"nonce_str\": \"\",\n"
                            + "            \"package\": \"\",\n"
                            + "            \"sign_type\": \"\",\n"
                            + "            \"redirect_url\": \"https://qr.95516.com/UP04/qrcGtwWeb-web/front/confirmOrder?sessionId=9e77013196184f58824a227cfece39d2\",\n"
                            + "            \"best_pay_info\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                } else if(serviceUrl.contains("/api/v3/labs/query/tradequery")) {
                    return  "{\n"
                            + "    \"code\": \"BBS00000\",\n"
                            + "    \"msg\": \"成功\",\n"
                            + "    \"resp_time\": \"20240110160627\",\n"
                            + "    \"resp_data\": {\n"
                            + "        \"merchant_no\": \"777290058135880\",\n"
                            + "        \"out_trade_no\": \"****************\",\n"
                            + "        \"trade_no\": \"20240109**************\",\n"
                            + "        \"log_no\": \"**************\",\n"
                            + "        \"trade_main_type\": \"MICROPAY\",\n"
                            + "        \"split_attr\": \"\",\n"
                            + "        \"acc_trade_no\": \"**********;685452;********;********\",\n"
                            + "        \"account_type\": \"UQRCODEPAY\",\n"
                            + "        \"settle_merchant_no\": \"\",\n"
                            + "        \"settle_term_no\": \"\",\n"
                            + "        \"trade_state\": \"SUCCESS\",\n"
                            + "        \"trade_state_desc\": \"交易成功\",\n"
                            + "        \"total_amount\": \"1000\",\n"
                            + "        \"payer_amount\": \"900\",\n"
                            + "        \"buyer_refund_amount\": \"\",\n"
                            + "        \"acc_settle_amount\": \"1000\",\n"
                            + "        \"acc_mdiscount_amount\": \"0\",\n"
                            + "        \"acc_discount_amount\": \"100\",\n"
                            + "        \"acc_other_discount_amount\": \"\",\n"
                            + "        \"trade_time\": \"**************\",\n"
                            + "        \"user_id1\": \"\",\n"
                            + "        \"user_id2\": \"\",\n"
                            + "        \"bank_type\": \"UNKNOW\",\n"
                            + "        \"card_type\": \"00\",\n"
                            + "        \"acc_activity_id\": \"****************,\",\n"
                            + "        \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "        \"trade_info\": \"\",\n"
                            + "        \"acc_resp_fields\": {\n"
                            + "            \"user_id\": \"\",\n"
                            + "            \"store_id\": \"\",\n"
                            + "            \"alipay_store_id\": \"\",\n"
                            + "            \"open_id\": \"\",\n"
                            + "            \"acc_activity_id\": \"\",\n"
                            + "            \"up_iss_addn_data\": \"\",\n"
                            + "            \"up_coupon_info\": \"[{\\\"id\\\":\\\"****************\\\",\\\"desc\\\":\\\"yl大于10元随机立减2\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"********\\\",\\\"offstAmt\\\":\\\"100\\\"}]\",\n"
                            + "            \"trade_info\": \"\",\n"
                            + "            \"promotion_detail\": \"\"\n"
                            + "        }\n"
                            + "    }\n"
                            + "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_unionpay_wap_with_query", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_unionpay_wap_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }

}
