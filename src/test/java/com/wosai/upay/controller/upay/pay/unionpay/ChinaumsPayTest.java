package com.wosai.upay.controller.upay.pay.unionpay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.ChinaumsSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class ChinaumsPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.CHINAUMS_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
    }
    
    @Test
    public void test_unionpay_barcode() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        PowerMockito.mockStatic(ChinaumsSignature.class);
        PowerMockito.when(ChinaumsSignature.sign(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("abcdefg");
        
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/v2/poslink/transaction/pay")) {
                    return  "{\n" + 
                            "    \"errCode\": \"00\",\n" + 
                            "    \"errInfo\": \"10000成功响应码\",\n" + 
                            "    \"transactionTime\": \"143324\",\n" + 
                            "    \"transactionDate\": \"0825\",\n" + 
                            "    \"settlementDate\": \"0825\",\n" + 
                            "    \"transactionDateWithYear\": \"20200825\",\n" + 
                            "    \"settlementDateWithYear\": \"20200825\",\n" + 
                            "    \"retrievalRefNum\": \"04767049264W\",\n" + 
                            "    \"transactionAmount\": 30,\n" + 
                            "    \"actualTransactionAmount\": 30,\n" + 
                            "    \"amount\": 15920,\n" + 
                            "    \"orderId\": \"20200825143324104767049264\",\n" + 
                            "    \"thirdPartyDiscountInstrution\": \"\",\n" + 
                            "    \"thirdPartyDiscountInstruction\": \"\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"thirdPartyOrderId\": \"03200825524047953292\",\n" + 
                            "    \"thirdPartyPayInformation\": \"\",\n" + 
                            "    \"totalDiscountAmount\": 0,\n" + 
                            "    \"discountStatus\": 2,\n" + 
                            "    \"promotionList\": [{\n" + 
                            "        \"channelName\": \"ACP\",\n" + 
                            "        \"discountId\": \"1201202008219139\",\n" + 
                            "        \"eventNo\": \"1201202008219139\",\n" + 
                            "        \"eventName\": \"0822全闽乐购满2元享8折，封顶88元（商圈）\",\n" + 
                            "        \"discountScope\": \"GLOBAL\",\n" + 
                            "        \"discountType\": \"CP01\",\n" + 
                            "        \"eventDiscountAmount\": 3980,\n" + 
                            "        \"platformDiscountAmount\": 3980,\n" + 
                            "        \"merchantDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountDetail\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "    }],\n" + 
                            "    \"rawPromotionList\": {\n" + 
                            "        \"channel_info\": {\n" + 
                            "            \"channel_code\": \"ACP\",\n" + 
                            "            \"coupon_info\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "        }\n" + 
                            "    },\n" + 
                            "    \"cardAttr\": \"03\",\n" + 
                            "    \"mchntName\": \"飒拉商业(上海)有限公司\",\n" + 
                            "    \"promotionActivityId\": \"1201202008219139\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_unionpay_barcode_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");

        PowerMockito.mockStatic(ChinaumsSignature.class);
        PowerMockito.when(ChinaumsSignature.sign(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("abcdefg");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/v2/poslink/transaction/pay")) {
                    return  "{\n" + 
                            "    \"errCode\": \"01\",\n" + 
                            "    \"errInfo\": \"10000成功响应码\",\n" + 
                            "    \"transactionTime\": \"143324\",\n" + 
                            "    \"transactionDate\": \"0825\",\n" + 
                            "    \"settlementDate\": \"0825\",\n" + 
                            "    \"transactionDateWithYear\": \"20200825\",\n" + 
                            "    \"settlementDateWithYear\": \"20200825\",\n" + 
                            "    \"retrievalRefNum\": \"04767049264W\",\n" + 
                            "    \"transactionAmount\": 30,\n" + 
                            "    \"actualTransactionAmount\": 30,\n" + 
                            "    \"amount\": 15920,\n" + 
                            "    \"orderId\": \"20200825143324104767049264\",\n" + 
                            "    \"thirdPartyDiscountInstrution\": \"\",\n" + 
                            "    \"thirdPartyDiscountInstruction\": \"\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"thirdPartyOrderId\": \"03200825524047953292\",\n" + 
                            "    \"thirdPartyPayInformation\": \"\",\n" + 
                            "    \"totalDiscountAmount\": 0,\n" + 
                            "    \"discountStatus\": 2,\n" + 
                            "    \"promotionList\": [{\n" + 
                            "        \"channelName\": \"ACP\",\n" + 
                            "        \"discountId\": \"1201202008219139\",\n" + 
                            "        \"eventNo\": \"1201202008219139\",\n" + 
                            "        \"eventName\": \"0822全闽乐购满2元享8折，封顶88元（商圈）\",\n" + 
                            "        \"discountScope\": \"GLOBAL\",\n" + 
                            "        \"discountType\": \"CP01\",\n" + 
                            "        \"eventDiscountAmount\": 3980,\n" + 
                            "        \"platformDiscountAmount\": 3980,\n" + 
                            "        \"merchantDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountDetail\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "    }],\n" + 
                            "    \"rawPromotionList\": {\n" + 
                            "        \"channel_info\": {\n" + 
                            "            \"channel_code\": \"ACP\",\n" + 
                            "            \"coupon_info\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "        }\n" + 
                            "    },\n" + 
                            "    \"cardAttr\": \"03\",\n" + 
                            "    \"mchntName\": \"飒拉商业(上海)有限公司\",\n" + 
                            "    \"promotionActivityId\": \"1201202008219139\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/v2/poslink/transaction/query")) {
                    return  "{\n" + 
                            "    \"errCode\": \"00\",\n" + 
                            "    \"errInfo\": \"00000成功响应码\",\n" + 
                            "    \"originalTransactionTime\": \"20200825143244\",\n" + 
                            "    \"queryResCode\": \"0\",\n" + 
                            "    \"queryResDesc\": \"成功\",\n" + 
                            "    \"originalPayCode\": \"6261118048231762786\",\n" + 
                            "    \"originalBatchNo\": \"000001\",\n" + 
                            "    \"originalSystemTraceNum\": \"004061\",\n" + 
                            "    \"origialRetrievalRefNum\": \"04767055012W\",\n" + 
                            "    \"originalTransactionAmount\": 30,\n" + 
                            "    \"amount\": 30,\n" + 
                            "    \"actualTransactionAmount\": 30,\n" + 
                            "    \"thirdPartyDiscountInstruction\": \"\",\n" + 
                            "    \"thirdPartyDiscountInstrution\": \"\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"thirdPartyOrderId\": \"70200825523647600721\",\n" + 
                            "    \"thirdPartyPayInformation\": \"\",\n" + 
                            "    \"orderId\": \"20200825143244104767055012\",\n" + 
                            "    \"merchantOrderId\": \"7895237672895491\",\n" + 
                            "    \"totalDiscountAmount\": 30,\n" + 
                            "    \"discountStatus\": 2,\n" + 
                            "    \"promotionList\": [{\n" + 
                            "        \"channelName\": \"ACP\",\n" + 
                            "        \"discountId\": \"3102020081933536\",\n" + 
                            "        \"eventNo\": \"3102020081933536\",\n" + 
                            "        \"eventName\": \"全闽乐购券300减88(福州9点场）\",\n" + 
                            "        \"discountScope\": \"GLOBAL\",\n" + 
                            "        \"discountType\": \"CP01\",\n" + 
                            "        \"eventDiscountAmount\": 8800,\n" + 
                            "        \"platformDiscountAmount\": 8800,\n" + 
                            "        \"merchantDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountDetail\": \"W3siaWQiOiIzMTAyMDIwMDgxOTMzNTM2IiwiZGVzYyI6IuWFqOmXveS5kOi0reWIuDMwMOWHjzg4KOemj+W3njnngrnlnLrvvIkiLCJ0eXBlIjoiQ1AwMSIsInNwbnNySWQiOiIwMDAxMDAwMCIsIm9mZnN0QW10IjoiODgwMCJ9XQ==\"\n" + 
                            "    }],\n" + 
                            "    \"rawPromotionList\": {\n" + 
                            "        \"channel_info\": {\n" + 
                            "            \"channel_code\": \"ACP\",\n" + 
                            "            \"coupon_info\": \"W3siaWQiOiIzMTAyMDIwMDgxOTMzNTM2IiwiZGVzYyI6IuWFqOmXveS5kOi0reWIuDMwMOWHjzg4KOemj+W3njnngrnlnLrvvIkiLCJ0eXBlIjoiQ1AwMSIsInNwbnNySWQiOiIwMDAxMDAwMCIsIm9mZnN0QW10IjoiODgwMCJ9XQ==\"\n" + 
                            "        }\n" + 
                            "    },\n" + 
                            "    \"originalSettlementDate\": \"20200825\",\n" + 
                            "    \"mchntName\": \"飒拉商业(上海)有限公司\",\n" + 
                            "    \"promotionActivityId\": \"3102020081933536\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        String resultString = postPerform("test_unionpay_barcode_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_unionpay_barcode_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    @Test
    public void test_unionpay_barcode_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");

        PowerMockito.mockStatic(ChinaumsSignature.class);
        PowerMockito.when(ChinaumsSignature.sign(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("abcdefg");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/v2/poslink/transaction/pay")) {
                    return  "{\n" + 
                            "    \"errCode\": \"00\",\n" + 
                            "    \"errInfo\": \"10000成功响应码\",\n" + 
                            "    \"transactionTime\": \"143324\",\n" + 
                            "    \"transactionDate\": \"0825\",\n" + 
                            "    \"settlementDate\": \"0825\",\n" + 
                            "    \"transactionDateWithYear\": \"20200825\",\n" + 
                            "    \"settlementDateWithYear\": \"20200825\",\n" + 
                            "    \"retrievalRefNum\": \"04767049264W\",\n" + 
                            "    \"transactionAmount\": 30,\n" + 
                            "    \"actualTransactionAmount\": 30,\n" + 
                            "    \"amount\": 15920,\n" + 
                            "    \"orderId\": \"20200825143324104767049264\",\n" + 
                            "    \"thirdPartyDiscountInstrution\": \"\",\n" + 
                            "    \"thirdPartyDiscountInstruction\": \"\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"thirdPartyOrderId\": \"03200825524047953292\",\n" + 
                            "    \"thirdPartyPayInformation\": \"\",\n" + 
                            "    \"totalDiscountAmount\": 0,\n" + 
                            "    \"discountStatus\": 2,\n" + 
                            "    \"promotionList\": [{\n" + 
                            "        \"channelName\": \"ACP\",\n" + 
                            "        \"discountId\": \"1201202008219139\",\n" + 
                            "        \"eventNo\": \"1201202008219139\",\n" + 
                            "        \"eventName\": \"0822全闽乐购满2元享8折，封顶88元（商圈）\",\n" + 
                            "        \"discountScope\": \"GLOBAL\",\n" + 
                            "        \"discountType\": \"CP01\",\n" + 
                            "        \"eventDiscountAmount\": 3980,\n" + 
                            "        \"platformDiscountAmount\": 3980,\n" + 
                            "        \"merchantDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountDetail\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "    }],\n" + 
                            "    \"rawPromotionList\": {\n" + 
                            "        \"channel_info\": {\n" + 
                            "            \"channel_code\": \"ACP\",\n" + 
                            "            \"coupon_info\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "        }\n" + 
                            "    },\n" + 
                            "    \"cardAttr\": \"03\",\n" + 
                            "    \"mchntName\": \"飒拉商业(上海)有限公司\",\n" + 
                            "    \"promotionActivityId\": \"1201202008219139\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/v2/poslink/transaction/refund")) {
                    return  "{\n" + 
                            "    \"errCode\": \"00\",\n" + 
                            "    \"errInfo\": \"10000成功响应码\",\n" + 
                            "    \"transactionTime\": \"113741\",\n" + 
                            "    \"transactionDate\": \"0825\",\n" + 
                            "    \"settlementDate\": \"0825\",\n" + 
                            "    \"transactionDateWithYear\": \"20200825\",\n" + 
                            "    \"settlementDateWithYear\": \"20200825\",\n" + 
                            "    \"retrievalRefNum\": \"04721660611W\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"cardAttr\": \"03\",\n" + 
                            "    \"refundInvoiceAmount\": \"30\",\n" + 
                            "    \"transactionAmount\": 30,\n" + 
                            "    \"refundPromotionList\": \"0|0|0\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_unionpay_barcode_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_unionpay_barcode_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_unionpay_barcode_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");

        PowerMockito.mockStatic(ChinaumsSignature.class);
        PowerMockito.when(ChinaumsSignature.sign(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("abcdefg");

        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/v2/poslink/transaction/pay")) {
                    return  "{\n" + 
                            "    \"errCode\": \"00\",\n" + 
                            "    \"errInfo\": \"10000成功响应码\",\n" + 
                            "    \"transactionTime\": \"143324\",\n" + 
                            "    \"transactionDate\": \"0825\",\n" + 
                            "    \"settlementDate\": \"0825\",\n" + 
                            "    \"transactionDateWithYear\": \"20200825\",\n" + 
                            "    \"settlementDateWithYear\": \"20200825\",\n" + 
                            "    \"retrievalRefNum\": \"04767049264W\",\n" + 
                            "    \"transactionAmount\": 30,\n" + 
                            "    \"actualTransactionAmount\": 30,\n" + 
                            "    \"amount\": 15920,\n" + 
                            "    \"orderId\": \"20200825143324104767049264\",\n" + 
                            "    \"thirdPartyDiscountInstrution\": \"\",\n" + 
                            "    \"thirdPartyDiscountInstruction\": \"\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"thirdPartyOrderId\": \"03200825524047953292\",\n" + 
                            "    \"thirdPartyPayInformation\": \"\",\n" + 
                            "    \"totalDiscountAmount\": 0,\n" + 
                            "    \"discountStatus\": 2,\n" + 
                            "    \"promotionList\": [{\n" + 
                            "        \"channelName\": \"ACP\",\n" + 
                            "        \"discountId\": \"1201202008219139\",\n" + 
                            "        \"eventNo\": \"1201202008219139\",\n" + 
                            "        \"eventName\": \"0822全闽乐购满2元享8折，封顶88元（商圈）\",\n" + 
                            "        \"discountScope\": \"GLOBAL\",\n" + 
                            "        \"discountType\": \"CP01\",\n" + 
                            "        \"eventDiscountAmount\": 3980,\n" + 
                            "        \"platformDiscountAmount\": 3980,\n" + 
                            "        \"merchantDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountAmount\": 0,\n" + 
                            "        \"thirdPartyDiscountDetail\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "    }],\n" + 
                            "    \"rawPromotionList\": {\n" + 
                            "        \"channel_info\": {\n" + 
                            "            \"channel_code\": \"ACP\",\n" + 
                            "            \"coupon_info\": \"W3siaWQiOiIxMjAxMjAyMDA4MjE5MTM5IiwiZGVzYyI6IjA4MjLlhajpl73kuZDotK3mu6Ey5YWD5LqrOOaKmO+8jOWwgemhtjg45YWD77yI5ZWG5ZyI77yJIiwidHlwZSI6IkNQMDEiLCJzcG5zcklkIjoiMDAwMTAwMDAiLCJvZmZzdEFtdCI6IjM5ODAifV0=\"\n" + 
                            "        }\n" + 
                            "    },\n" + 
                            "    \"cardAttr\": \"03\",\n" + 
                            "    \"mchntName\": \"飒拉商业(上海)有限公司\",\n" + 
                            "    \"promotionActivityId\": \"1201202008219139\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/v2/poslink/transaction/voidpayment")) {
                    return  "{\n" + 
                            "    \"errCode\": \"00\",\n" + 
                            "    \"errInfo\": \"10000成功响应码\",\n" + 
                            "    \"transactionTime\": \"113741\",\n" + 
                            "    \"transactionDate\": \"0825\",\n" + 
                            "    \"settlementDate\": \"0825\",\n" + 
                            "    \"transactionDateWithYear\": \"20200825\",\n" + 
                            "    \"settlementDateWithYear\": \"20200825\",\n" + 
                            "    \"retrievalRefNum\": \"04721660611W\",\n" + 
                            "    \"thirdPartyName\": \"银联二维码\",\n" + 
                            "    \"cardAttr\": \"03\",\n" + 
                            "    \"refundInvoiceAmount\": \"30\",\n" + 
                            "    \"transactionAmount\": 30,\n" + 
                            "    \"refundPromotionList\": \"0|0|0\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_unionpay_barcode_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        resultString = postPerform("test_unionpay_barcode_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
    
    
}
