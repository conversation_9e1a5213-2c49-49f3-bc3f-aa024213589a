package com.wosai.upay.controller.upay.pay.weixin;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.util.Map;
import java.util.Random;

import com.wosai.pantheon.util.MapUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class UnionPayWeixinPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(Mockito.anyString(), SupportUtil.RSA2_PRIVATE_KEY);
    }
    
    /**
     * 
     * 微信通道返回trade_error，部分错误码直接置位pay_canceled
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_barcode_pay_canceled()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        Object[] errCodeDescs = WeixinConstants.TRADE_ERROR_FAIL_MESSAGE.toArray();
        int next = new Random().nextInt(errCodeDescs.length -1);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                "    <sub_appid><![CDATA[wxf9114ca5d9b45bd5]]></sub_appid>\n" + 
                "    <mch_id><![CDATA[**********]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[239435625]]></sub_mch_id>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                "    <result_code><![CDATA[FAIL]]></result_code>\n" + 
                "    <err_code><![CDATA[TRADE_ERROR]]></err_code>\n" + 
                "    <err_code_des><![CDATA[" + errCodeDescs[next] + "]]></err_code_des>\n" + 
                "    <nonce_str><![CDATA[e8316ba70123462b90b3e07db864fdc7]]></nonce_str>\n" + 
                "    <sign><![CDATA[lArTd9XJSZ8u7OjlYBDVtSCrcrVKzrUxZewvxsKiSAgNd8diB7Ba3y9nRiJDVw7X+yZijPx0ZoehAmQlWutj50cvleZB6qpf8Mdn28SL6CrgptSHqAyt6/M4FoepwRM/DQ35F+qb+cxipkAmzd8Rphh4nmtni8yeZOs2usGl2TPf2FyeuBXa2P6f53oJsgy+sZMqFKhmUb4oi5PeCivW9uWDhU0UUzs0yTHZAhJxxpd4BHVDmr2R2ocXDifIosmca/cnPBLsbisuiky9IfXhdUbn4RCite+a8jEyI4wCFzybCHHLVvNyPTMhGDv8dr9H1MwSZZ4ZnhR3GDzR5RgrIg==]]></sign>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_weixin_barcode_pay_canceled", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_FAIL,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_FAIL_CANCELED).getName(),
                        "biz_response.data.order_status", Order.Status.PAY_CANCELED.name()
                );

    }

    /**
     *
     * 微信通道返回success
     *
     * @throws Exception
     */
    @Test
    public void test_weixin_barcode_pay_success()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payReturnXml = "<xml>\n" +
                "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "  <total_fee>30</total_fee>\n" +
                "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                "  <attach><![CDATA[]]></attach>\n" +
                "  <time_end><![CDATA[**************]]></time_end>\n" +
                "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                "  <cash_fee>30</cash_fee>\n" +
                "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                "  <version><![CDATA[1.0]]></version>\n" +
                "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(payReturnXml);
        String resultString = postPerform("test_weixin_barcode_pay_success", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    @Test
    public void test_refund()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/wx/v1/pay/micropay")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                        "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                        "</xml>";
            }else if(serviceUrl.contains("/wx/v1/pay/refund")) {
                return  "<xml>\n" + 
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" + 
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" + 
                        "  <sub_mch_id><![CDATA[1522166361]]></sub_mch_id>\n" + 
                        "  <nonce_str><![CDATA[qmxTpuXSTmeIeO27]]></nonce_str>\n" + 
                        "  <sign><![CDATA[7C0EB1718D55930E47C30BD8DD95DA25]]></sign>\n" + 
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "  <transaction_id><![CDATA[4200000681202008313611996810]]></transaction_id>\n" + 
                        "  <out_trade_no><![CDATA[7895237703897703]]></out_trade_no>\n" + 
                        "  <out_refund_no><![CDATA[7895237703814329]]></out_refund_no>\n" + 
                        "  <refund_id><![CDATA[50300205452020083102425677690]]></refund_id>\n" + 
                        "  <refund_channel><![CDATA[]]></refund_channel>\n" + 
                        "  <refund_fee>30</refund_fee>\n" + 
                        "  <coupon_refund_fee>0</coupon_refund_fee>\n" + 
                        "  <total_fee>30</total_fee>\n" + 
                        "  <cash_fee>30</cash_fee>\n" + 
                        "  <coupon_refund_count>0</coupon_refund_count>\n" + 
                        "  <cash_refund_fee>30</cash_refund_fee>\n" + 
                        "</xml>";
            }
           
            return "fail";
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    } 
    
    @Test
    public void test_cancel()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/wx/v1/pay/micropay")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                        "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                        "</xml>";
            }else if(serviceUrl.contains("/wx/v1/pay/order/reverse")) {
                return  "<xml>\n" + 
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" + 
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" + 
                        "  <sub_mch_id><![CDATA[1315093701]]></sub_mch_id>\n" + 
                        "  <nonce_str><![CDATA[5vci9TXqjcCIyqXh]]></nonce_str>\n" + 
                        "  <sign><![CDATA[918B3AE8E7974DD49A841199E2C45C28]]></sign>\n" + 
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "  <recall><![CDATA[N]]></recall>\n" + 
                        "</xml>\n" + 
                        "";
            }
           
            return "fail";
        });
        
        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        
        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
    
    /**
     * 
     * 微信直连返回渠道信息
     * 
     * @throws Exception
     */
    @Test
    public void test_weixin_provider_response()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        request.put(UpayService.TOTAL_AMOUNT, "1500");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n"
                + "  <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n"
                + "  <sub_appid><![CDATA[wx72534f3638c59073]]></sub_appid>\n"
                + "  <mch_id><![CDATA[**********]]></mch_id>\n"
                + "  <sub_mch_id><![CDATA[437971174]]></sub_mch_id>\n"
                + "  <sign_type><![CDATA[RSA2]]></sign_type>\n"
                + "  <cert_id><![CDATA[4097258801]]></cert_id>\n"
                + "  <return_code><![CDATA[SUCCESS]]></return_code>\n"
                + "  <return_msg><![CDATA[成功]]></return_msg>\n"
                + "  <result_code><![CDATA[SUCCESS]]></result_code>\n"
                + "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n"
                + "  <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n"
                + "  <openid><![CDATA[ojjLFjnRfJ8u5Hu7HURI78B5rtck]]></openid>\n"
                + "  <sub_openid><![CDATA[oGFfksw-bn3fWMmyKOD7H-4LQbcQ]]></sub_openid>\n"
                + "  <trade_type><![CDATA[JSAPI]]></trade_type>\n"
                + "  <bank_type><![CDATA[BOC_CREDIT]]></bank_type>\n"
                + "  <fee_type><![CDATA[CNY]]></fee_type>\n"
                + "  <total_fee><![CDATA[1500]]></total_fee>\n"
                + "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n"
                + "  <cash_fee><![CDATA[500]]></cash_fee>\n"
                + "  <settlement_total_fee><![CDATA[1500]]></settlement_total_fee>\n"
                + "  <coupon_fee><![CDATA[1000]]></coupon_fee>\n"
                + "  <transaction_id><![CDATA[4200001163202107276489560308]]></transaction_id>\n"
                + "  <out_trade_no><![CDATA[****************]]></out_trade_no>\n"
                + "  <attach><![CDATA[bank_mch_name=上海市精品牛肉面香亭路店&bank_mch_id=*************]]></attach>\n"
                + "  <time_end><![CDATA[**************]]></time_end>\n"
                + "  <promotion_detail><![CDATA[[{\"promotion_id\":\"***********\",\"name\":\"指定糖果5元券\",\"scope\":\"SINGLE\",\"type\":\"COUPON\",\"amount\":500,\"activity_id\":\"********\",\"wxpay_contribute\":0,\"merchant_contribute\":0,\"other_contribute\":500,\"goods_detail\":[{\"goods_id\":\"*************\",\"quantity\":1,\"price\":1200,\"discount_amount\":250},{\"goods_id\":\"*************\",\"quantity\":1,\"price\":1200,\"discount_amount\":250}]}]]]></promotion_detail>\n"
                + "  <nonce_str><![CDATA[eb34b1a8550e475ca23a1ba5fdf3a627]]></nonce_str>\n"
                + "  <sign><![CDATA[brT3zz2FaR/fzGr0TWfNV9E4HXmM83ntFgabIHNaT+3jUlx7QxwKJm+n4QdKFYoO3a6cpcuByTujvxGdSdg4DPb3TTz2bALnr+1h3LyDikLShrMMThLjrX8NQ1uNJmmYdHsyUp2PdWQZMfdAGx4462FtbRDHq/cIRTE8j13xFExTIR2Dmw2C5yv3pwFeLwgb5RBLRQbMY/efuGZA1iK9abEPQCNKYIaV4A9Uwx7Ld5nafX3S4XJxI0UMuS2T06LmuXpGmD+atK7RfUvIvlvMmbh+i9HIrsCRV/gzgYCYCS5573V9DQ1rh1jzs+eerar6iLOK7I2Cq490/hp2usF5Kw==]]></sign>\n"
                + "</xml>\n"
                + "");
        
        String resultString = postPerform("test_weixin_provider_response", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.status", StateLabel.fromId(Transaction.STATUS_SUCCESS).getName(),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        Map<String, Object> providerResponse = (Map<String, Object>) BeanUtil.getNestedProperty(result, "biz_response.data.provider_response");
        assertNotNull(providerResponse.get(QueryResponse.GOODS_DETAILS));
        assertNotNull(providerResponse.get(QueryResponse.VOUCHER_DETAILS));
    }

    @Test
    public void test_weixin_barcode_pay_success_new()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payReturnXml = "<xml>\n" +
                "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "  <total_fee>30</total_fee>\n" +
                "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                "  <attach><![CDATA[]]></attach>\n" +
                "  <time_end><![CDATA[**************]]></time_end>\n" +
                "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                "  <cash_fee>30</cash_fee>\n" +
                "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                "  <version><![CDATA[1.0]]></version>\n" +
                "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(payReturnXml);
        String resultString = postPerform("test_weixin_barcode_pay_success", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction weixin pay {}", transaction);
    }

    @Test
    public void test_refund_new()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/wx/v1/pay/micropay")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                        "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                        "</xml>";
            }else if(serviceUrl.contains("/wx/v1/pay/refund")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[1522166361]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[qmxTpuXSTmeIeO27]]></nonce_str>\n" +
                        "  <sign><![CDATA[7C0EB1718D55930E47C30BD8DD95DA25]]></sign>\n" +
                        "  <result_code><![CDATA[SUCCESS]]></result_code>\n" +
                        "  <transaction_id><![CDATA[4200000681202008313611996810]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[7895237703897703]]></out_trade_no>\n" +
                        "  <out_refund_no><![CDATA[7895237703814329]]></out_refund_no>\n" +
                        "  <refund_id><![CDATA[50300205452020083102425677690]]></refund_id>\n" +
                        "  <refund_channel><![CDATA[]]></refund_channel>\n" +
                        "  <refund_fee>30</refund_fee>\n" +
                        "  <coupon_refund_fee>0</coupon_refund_fee>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <coupon_refund_count>0</coupon_refund_count>\n" +
                        "  <cash_refund_fee>30</cash_refund_fee>\n" +
                        "</xml>";
            }

            return "fail";
        });

        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);


        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction weixin refund {}", transaction);
    }

    @Test
    public void test_cancel_new() throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if(serviceUrl.contains("/wx/v1/pay/micropay")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                        "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                        "</xml>";
            }else if(serviceUrl.contains("/wx/v1/pay/order/reverse")) {
                return  "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[1315093701]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[5vci9TXqjcCIyqXh]]></nonce_str>\n" +
                        "  <sign><![CDATA[918B3AE8E7974DD49A841199E2C45C28]]></sign>\n" +
                        "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                        "  <recall><![CDATA[N]]></recall>\n" +
                        "</xml>\n" +
                        "";
            }

            return "fail";
        });

        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction weixin cancel {}", transaction);
    }

    @Test
    public void test_query_new() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(invocation -> {
            String serviceUrl = invocation.getArgument(3);
            if (serviceUrl.contains("/wx/v1/pay/micropay")) {
                return "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                        "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[**********]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                        "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                        "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                        "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                        "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                        "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                        "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                        "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                        "  <attach><![CDATA[]]></attach>\n" +
                        "  <time_end><![CDATA[**************]]></time_end>\n" +
                        "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                        "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                        "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                        "  <version><![CDATA[1.0]]></version>\n" +
                        "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                        "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                        "</xml>";
            } else if (serviceUrl.contains("/wx/v1/pay/query")) {
                return "<xml>\n" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                        "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                        "  <appid><![CDATA[wx72534f3638c59073]]></appid>\n" +
                        "  <mch_id><![CDATA[**********]]></mch_id>\n" +
                        "  <sub_mch_id><![CDATA[1522166361]]></sub_mch_id>\n" +
                        "  <nonce_str><![CDATA[qmxTpuXSTmeIeO27]]></nonce_str>\n" +
                        "  <sign><![CDATA[7C0EB1718D55930E47C30BD8DD95DA25]]></sign>\n" +
                        "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                        "  <transaction_id><![CDATA[4200000681202008313611996810]]></transaction_id>\n" +
                        "  <out_trade_no><![CDATA[7895237703897703]]></out_trade_no>\n" +
                        "  <out_refund_no><![CDATA[7895237703814329]]></out_refund_no>\n" +
                        "  <refund_id><![CDATA[50300205452020083102425677690]]></refund_id>\n" +
                        "  <refund_channel><![CDATA[]]></refund_channel>\n" +
                        "  <refund_fee>30</refund_fee>\n" +
                        "  <coupon_refund_fee>0</coupon_refund_fee>\n" +
                        "  <total_fee>30</total_fee>\n" +
                        "  <cash_fee>30</cash_fee>\n" +
                        "  <coupon_refund_count>0</coupon_refund_count>\n" +
                        "  <cash_refund_fee>30</cash_refund_fee>\n" +
                        "</xml>";
            }

            return "fail";
        });

        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);


        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction weixin query {}", transaction);
    }
}
