package com.wosai.upay.controller.upay.pay.unionpay;

import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class UnionpayOpenPayTest extends BaseTestController{
    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UPO_TRADE_PARAMS));
    }

    @Test
    public void test_pay() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/gateway/api/pay/micropay")) {
                    return  "{\n" + 
                            "    \"orderNo\": \"7895237682391065\",\n" + 
                            "    \"signature\": \"kvlJ+mCZWU4Ra2KqrWxI9ohfP55f6QB6PLQ4M6QHmhaX8+Eu91+4SnLAMiDqzdprwFmiQ0ByLD5Q1ofBbFu+3A22ImmcxhuTrgzn2wxjZ9zuutQ/3T+K27dnpWAQb+4m1mX/SEEropPIMB84JFjJtc7vMnFNwBoPICbEj6XSwq8=\",\n" + 
                            "    \"couponInfo\": \"[{\\\"id\\\":\\\"1201202003310373\\\",\\\"desc\\\":\\\"满5元随机立减（小微四川）\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"00010000\\\",\\\"offstAmt\\\":\\\"30\\\"}]\",\n" + 
                            "    \"payTime\": \"20200826095402\",\n" + 
                            "    \"settlementAmt\": 4080,\n" + 
                            "    \"payAmt\": 30,\n" + 
                            "    \"payerInfo\": \"\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"currencyCode\": \"156\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"txnAmt\": 30,\n" + 
                            "    \"transIndex\": \"20082640503173\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_pay_with_query() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/gateway/api/pay/micropay")) {
                    return  "{\n" + 
                            "    \"signature\": \"RoPxA1VdQ7RVqETOAgmUETtjWcH+4xJDXmKkIvV7o84xDZ7vt0yfOzqrruD7M7lX9hZjpddAaW2OZtzy1FSNHVaScGeDFkCxS2mYwYd910X65NhLd4qxmmmLKM58S/vz8tgOmluTiYroAWtVyd11FbXkeoaWRUIJOaL40+n/nPg=\",\n" + 
                            "    \"respMsg\": \"收单机构返回错误：用户支付中，请稍后查询\",\n" + 
                            "    \"respCode\": \"02\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/gateway/api/pay/queryOrder")) {
                    return  "{\n" + 
                            "    \"orderNo\": \"7895237682533188\",\n" + 
                            "    \"signature\": \"Xtd0Q4FmM8MBKTzvjqEHOOF0L9OdGt8SbcGnN1kY/S9l1yg/wChFIGGgzh+O8Q4ce1Dw0z6GrepZGPnXAxE0rqHW2a1Ww1qQxbPzrWBxTO2gbgj2xIJFBq30DyajsB29zd7O58sGb3PR4z7BNLak6slJvtSUmn2E03n4xmSVGLc=\",\n" + 
                            "    \"couponInfo\": \"[{\\\"id\\\":\\\"000000000000000\\\",\\\"desc\\\":\\\"银联红包\\\",\\\"type\\\":\\\"CP01\\\",\\\"spnsrId\\\":\\\"00010000\\\",\\\"offstAmt\\\":\\\"27\\\"}]\",\n" + 
                            "    \"payTime\": \"20200826100412\",\n" + 
                            "    \"settlementAmt\": 30,\n" + 
                            "    \"payAmt\": 30,\n" + 
                            "    \"payerInfo\": \"\",\n" + 
                            "    \"reqReserved\": \"\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"origRespMsg\": \"成功\",\n" + 
                            "    \"origRespCode\": \"00\",\n" + 
                            "    \"currencyCode\": \"156\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"txnAmt\": 30,\n" + 
                            "    \"transIndex\": \"20082640532768\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_pay_with_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_pay_with_query", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }
    
    @Test
    public void test_refund() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/gateway/api/pay/micropay")) {
                    return  "{\n" + 
                            "    \"orderNo\": \"7895237682391065\",\n" + 
                            "    \"signature\": \"kvlJ+mCZWU4Ra2KqrWxI9ohfP55f6QB6PLQ4M6QHmhaX8+Eu91+4SnLAMiDqzdprwFmiQ0ByLD5Q1ofBbFu+3A22ImmcxhuTrgzn2wxjZ9zuutQ/3T+K27dnpWAQb+4m1mX/SEEropPIMB84JFjJtc7vMnFNwBoPICbEj6XSwq8=\",\n" + 
                            "    \"couponInfo\": \"[{\\\"id\\\":\\\"1201202003310373\\\",\\\"desc\\\":\\\"满5元随机立减（小微四川）\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"00010000\\\",\\\"offstAmt\\\":\\\"30\\\"}]\",\n" + 
                            "    \"payTime\": \"20200826095402\",\n" + 
                            "    \"settlementAmt\": 4080,\n" + 
                            "    \"payAmt\": 30,\n" + 
                            "    \"payerInfo\": \"\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"currencyCode\": \"156\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"txnAmt\": 30,\n" + 
                            "    \"transIndex\": \"20082640503173\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/gateway/api/pay/refund")) {
                    return  "{\n" + 
                            "    \"signature\": \"j8MFU+FVTmJRG85PGxGK90n8HbNblc/SEIpYpMzw7U4t6WMo0BPuVJp5Afj526wPp+PH12DQjo587IjZ5RaHDO636q46Iz9dYQ+gLaq/kxfFL2dvlTA+LQsO5KiVtGVT9cFcIif/YiVxQVjAdDh6mCeBFoxfcmqqLlR/hNV4cEQ=\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"respCode\": \"00\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        request.put(UpayService.REFUND_AMOUNT, "30");
        request.put(UpayService.REFUND_REQUEST_NO, "1");
        resultString = postPerform("test_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
                "biz_response.data.order_status", Order.Status.REFUNDED.name()
        );
    }
    
    @Test
    public void test_cancel() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6220288699494506950");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                String serviceUrl = invocation.getArgument(3);
                if(serviceUrl.contains("/gateway/api/pay/micropay")) {
                    return  "{\n" + 
                            "    \"orderNo\": \"7895237682391065\",\n" + 
                            "    \"signature\": \"kvlJ+mCZWU4Ra2KqrWxI9ohfP55f6QB6PLQ4M6QHmhaX8+Eu91+4SnLAMiDqzdprwFmiQ0ByLD5Q1ofBbFu+3A22ImmcxhuTrgzn2wxjZ9zuutQ/3T+K27dnpWAQb+4m1mX/SEEropPIMB84JFjJtc7vMnFNwBoPICbEj6XSwq8=\",\n" + 
                            "    \"couponInfo\": \"[{\\\"id\\\":\\\"1201202003310373\\\",\\\"desc\\\":\\\"满5元随机立减（小微四川）\\\",\\\"type\\\":\\\"DD01\\\",\\\"spnsrId\\\":\\\"00010000\\\",\\\"offstAmt\\\":\\\"30\\\"}]\",\n" + 
                            "    \"payTime\": \"20200826095402\",\n" + 
                            "    \"settlementAmt\": 4080,\n" + 
                            "    \"payAmt\": 30,\n" + 
                            "    \"payerInfo\": \"\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"currencyCode\": \"156\",\n" + 
                            "    \"respCode\": \"00\",\n" + 
                            "    \"txnAmt\": 30,\n" + 
                            "    \"transIndex\": \"20082640503173\"\n" + 
                            "}";
                }else if(serviceUrl.contains("/gateway/api/pay/cancelOrder")) {
                    return  "{\n" + 
                            "    \"signature\": \"j8MFU+FVTmJRG85PGxGK90n8HbNblc/SEIpYpMzw7U4t6WMo0BPuVJp5Afj526wPp+PH12DQjo587IjZ5RaHDO636q46Iz9dYQ+gLaq/kxfFL2dvlTA+LQsO5KiVtGVT9cFcIif/YiVxQVjAdDh6mCeBFoxfcmqqLlR/hNV4cEQ=\",\n" + 
                            "    \"respMsg\": \"成功\",\n" + 
                            "    \"respCode\": \"00\"\n" + 
                            "}";
                }
                return "fail";
            }
        });
        
        String resultString = postPerform("test_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
        
        resultString = postPerform("test_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
                "biz_response.data.order_status", Order.Status.CANCELED.name()
        );
    }
}
