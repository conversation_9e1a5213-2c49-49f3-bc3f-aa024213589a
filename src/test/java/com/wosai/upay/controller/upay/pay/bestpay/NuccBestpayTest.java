package com.wosai.upay.controller.upay.pay.bestpay;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.api.NuccBestPay.NuccBestPayConstants;
import com.wosai.mpay.api.NuccBestPay.ProtocolFields;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CancelResponse;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.api.RefundResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

/**
 * 网联翼支付
 */
@PrepareForTest({WebUtils.class}) // 所有需要测试的类列在此处，适用于模拟final类或有final, private, static, native方法的类
public class NuccBestpayTest extends BaseTestController {
    
    @Before
    public void init() {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.NUCC_TRADE_PARAMS));
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
    }

    /**
     *
     * 翼支付 通道下单支付成功
     *
     * @throws Exception
     */
    @Test
    public void test_bestpay_barcode_pay_success_without_query()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<root>\n" + 
                "    <MsgHeader>\n" + 
                "        <SndDt>2020-08-28T10:23:18</SndDt>\n" + 
                "        <MsgTp>bcs.302.001.01</MsgTp>\n" + 
                "        <SignSN>**********</SignSN>\n" + 
                "    </MsgHeader>\n" + 
                "    <MsgBody>\n" + 
                "        <AcceptPartnerId>S000071********1</AcceptPartnerId>\n" + 
                "        <AccountPartnerId>Z00005000001</AccountPartnerId>\n" + 
                "        <TrxTypeCd>02</TrxTypeCd>\n" + 
                "        <TrxId>20200828102318012010026123210200</TrxId>\n" + 
                "        <TrxOrderNo>****************</TrxOrderNo>\n" + 
                "        <TrxAmt>CNY0.30</TrxAmt>\n" + 
                "        <TrxStatus>02</TrxStatus>\n" + 
                "        <BatchId>B202008280001</BatchId>\n" + 
                "        <SysRtnCd>********</SysRtnCd>\n" + 
                "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                "        <SysRtnTm>2020-08-28T10:23:18</SysRtnTm>\n" + 
                "        <BizStsCd>********</BizStsCd>\n" + 
                "        <BizStsDesc>成功</BizStsDesc>\n" + 
                "    </MsgBody>\n" + 
                "</root>{S:HagNWnLnGOwxhLl4DBIoIH1xlZD9Zeo9YXOTTnvLrRZSGd9E0jcpBlUyyO0fhMcedj+z60eL6bLndFa3KjfQNdG1AlFUPUUgCRe4mzHFI5HAnyFRiyQag9EcISZTeX4bh2ICyTzj9Y+cdH7BW7yOURrZ1XhN9ihrvLLboHR6Y8GtaM5WqxZHl69OjlDRGaLU++ACCwllJruD3pzEaCnuFtJ8mKb679GeEz+z7jTzQylj1WGyrbtlf8srAx3uZaARnlcdBOKxZ7jJfASHoCpJXHHUyxs4S+v8iuDqLsguzgTYisGFZnZmIOPFA1sjAhZBlEtwx4/IfC97yin0u67UPQ==}");

        String resultString = postPerform("test_bestpay_barcode_pay_success_without_query", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
    }

    /**
     *
     * 翼支付 通道下单成功，通过查询接口查询交易成功
     *
     * @throws Exception
     */
    @Test
    public void test_bestpay_barcode_pay_success_with_query()throws Exception{
        JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
        PowerMockito.mockStatic(WebUtils.class);
        PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer((invocation) ->{
            Map header = invocation.getArgument(4);
            String msgTp = MapUtil.getString(header, ProtocolFields.MSG_TP);
            if(msgTp.equals(NuccBestPayConstants.PAY)) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                        "<root>\n" + 
                        "    <MsgHeader>\n" + 
                        "        <SndDt>2020-08-28T10:23:18</SndDt>\n" + 
                        "        <MsgTp>bcs.302.001.01</MsgTp>\n" + 
                        "        <SignSN>**********</SignSN>\n" + 
                        "    </MsgHeader>\n" + 
                        "    <MsgBody>\n" + 
                        "        <AcceptPartnerId>S000071********1</AcceptPartnerId>\n" + 
                        "        <AccountPartnerId>Z00005000001</AccountPartnerId>\n" + 
                        "        <TrxTypeCd>02</TrxTypeCd>\n" + 
                        "        <TrxId>20200828102318012010026123210200</TrxId>\n" + 
                        "        <TrxOrderNo>****************</TrxOrderNo>\n" + 
                        "        <TrxAmt>CNY0.30</TrxAmt>\n" + 
                        "        <TrxStatus>02</TrxStatus>\n" + 
                        "        <BatchId>B202008280001</BatchId>\n" + 
                        "        <SysRtnCd>********</SysRtnCd>\n" + 
                        "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                        "        <SysRtnTm>2020-08-28T10:23:18</SysRtnTm>\n" + 
                        "        <BizStsCd>BB000001</BizStsCd>\n" + 
                        "        <BizStsDesc>等待用户支付</BizStsDesc>\n" + 
                        "    </MsgBody>\n" + 
                        "</root>{S:HagNWnLnGOwxhLl4DBIoIH1xlZD9Zeo9YXOTTnvLrRZSGd9E0jcpBlUyyO0fhMcedj+z60eL6bLndFa3KjfQNdG1AlFUPUUgCRe4mzHFI5HAnyFRiyQag9EcISZTeX4bh2ICyTzj9Y+cdH7BW7yOURrZ1XhN9ihrvLLboHR6Y8GtaM5WqxZHl69OjlDRGaLU++ACCwllJruD3pzEaCnuFtJ8mKb679GeEz+z7jTzQylj1WGyrbtlf8srAx3uZaARnlcdBOKxZ7jJfASHoCpJXHHUyxs4S+v8iuDqLsguzgTYisGFZnZmIOPFA1sjAhZBlEtwx4/IfC97yin0u67UPQ==}";
            }else if(msgTp.equals(NuccBestPayConstants.NORMAL_QUERY)) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                        "<root>\n" + 
                        "    <MsgHeader>\n" + 
                        "        <SndDt>2020-08-28T10:23:20</SndDt>\n" + 
                        "        <MsgTp>bcs.308.001.01</MsgTp>\n" + 
                        "        <SignSN>**********</SignSN>\n" + 
                        "    </MsgHeader>\n" + 
                        "    <MsgBody>\n" + 
                        "        <AcceptPartnerId>S000071********1</AcceptPartnerId>\n" + 
                        "        <AccountPartnerId>Z00005000001</AccountPartnerId>\n" + 
                        "        <OriBizStsCd>********</OriBizStsCd>\n" + 
                        "        <OriBizStsDesc>成功</OriBizStsDesc>\n" + 
                        "        <OriSysRtnCd>********</OriSysRtnCd>\n" + 
                        "        <OriSysRtnDesc>成功</OriSysRtnDesc>\n" + 
                        "        <OriTrxTypeCd>02</OriTrxTypeCd>\n" + 
                        "        <OriTrxUserTypeCd>02</OriTrxUserTypeCd>\n" + 
                        "        <OriTrxId>20200828102318012010026123210200</OriTrxId>\n" + 
                        "        <OriTrxOrderNo>****************</OriTrxOrderNo>\n" + 
                        "        <OriTrxAmt>CNY0.30</OriTrxAmt>\n" + 
                        "        <OriTrxSettleAmt>CNY0.30</OriTrxSettleAmt>\n" + 
                        "        <OriTrxPayerAmt>CNY0.30</OriTrxPayerAmt>\n" + 
                        "        <OriTrxStatus>00</OriTrxStatus>\n" + 
                        "        <OriTrxFinishTm>2020-08-28T10:23:19</OriTrxFinishTm>\n" + 
                        "        <OriBatchId>B202008280001</OriBatchId>\n" + 
                        "        <SysRtnCd>********</SysRtnCd>\n" + 
                        "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                        "        <SysRtnTm>2020-08-28T10:23:20</SysRtnTm>\n" + 
                        "        <BizStsCd>********</BizStsCd>\n" + 
                        "        <BizStsDesc>成功</BizStsDesc>\n" + 
                        "    </MsgBody>\n" + 
                        "</root>{S:r5o3l/AD+1Jlf7+nha46hMb8m+8kpbGD/6FLrFyE/HknqRrenDbvGqsKlSe4y7V70IRAw00jjLX3ybt9bgBp6xbWUk6NanE1A3YBGCPGs4AAqHCn1Gn/nqfttRJ6J3NYgCQnb4GJl3mXdRLrbNc/ZgHT83NoCcB/70RnAdPvGWhY28wbPsAJVr/sea+K5ckRPcWlEOFJOwDVggE8O9ts830sZvnwjtSHOTPrRlnEYlIgxPxzinzRj64IqhUgGu2DNRaV/10hoND/X/hc4MhkpoyBD25weK/v9zh4OaMDRkz6y3PJPrU7HQQRiCiMArAEDXy8BpMX/YaTqAp3TvBY3A==}";
            }
            return null;
        });

        String resultString = postPerform("test_bestpay_barcode_pay_success_with_query_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", PayResponse.RESULT_CODE_PAY_IN_PROGRESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
        );
        Thread.currentThread().sleep(5000);
        resultString = postPerform("test_bestpay_barcode_pay_success_with_query_2", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                "biz_response.data.order_status", Order.Status.PAID.name()
        );
    }
    
    /**
    *
    * 翼支付 撤单
    *
    * @throws Exception
    */
   @Test
   public void test_bestpay_cancel()throws Exception{
       JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
       PowerMockito.mockStatic(WebUtils.class);
       PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer((invocation) ->{
           Map header = invocation.getArgument(4);
           String msgTp = MapUtil.getString(header, ProtocolFields.MSG_TP);
           if(msgTp.equals(NuccBestPayConstants.PAY)) {
               return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                       "<root>\n" + 
                       "    <MsgHeader>\n" + 
                       "        <SndDt>2020-08-28T10:23:18</SndDt>\n" + 
                       "        <MsgTp>bcs.302.001.01</MsgTp>\n" + 
                       "        <SignSN>**********</SignSN>\n" + 
                       "    </MsgHeader>\n" + 
                       "    <MsgBody>\n" + 
                       "        <AcceptPartnerId>S000071********1</AcceptPartnerId>\n" + 
                       "        <AccountPartnerId>Z00005000001</AccountPartnerId>\n" + 
                       "        <TrxTypeCd>02</TrxTypeCd>\n" + 
                       "        <TrxId>20200828102318012010026123210200</TrxId>\n" + 
                       "        <TrxOrderNo>****************</TrxOrderNo>\n" + 
                       "        <TrxAmt>CNY0.30</TrxAmt>\n" + 
                       "        <TrxStatus>02</TrxStatus>\n" + 
                       "        <BatchId>B202008280001</BatchId>\n" + 
                       "        <SysRtnCd>********</SysRtnCd>\n" + 
                       "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                       "        <SysRtnTm>2020-08-28T10:23:18</SysRtnTm>\n" + 
                       "        <BizStsCd>********</BizStsCd>\n" + 
                       "        <BizStsDesc>成功</BizStsDesc>\n" + 
                       "    </MsgBody>\n" + 
                       "</root>{S:HagNWnLnGOwxhLl4DBIoIH1xlZD9Zeo9YXOTTnvLrRZSGd9E0jcpBlUyyO0fhMcedj+z60eL6bLndFa3KjfQNdG1AlFUPUUgCRe4mzHFI5HAnyFRiyQag9EcISZTeX4bh2ICyTzj9Y+cdH7BW7yOURrZ1XhN9ihrvLLboHR6Y8GtaM5WqxZHl69OjlDRGaLU++ACCwllJruD3pzEaCnuFtJ8mKb679GeEz+z7jTzQylj1WGyrbtlf8srAx3uZaARnlcdBOKxZ7jJfASHoCpJXHHUyxs4S+v8iuDqLsguzgTYisGFZnZmIOPFA1sjAhZBlEtwx4/IfC97yin0u67UPQ==}";
           }else if(msgTp.equals(NuccBestPayConstants.REFUND)) {
               return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                       "<root>\n" + 
                       "    <MsgHeader>\n" + 
                       "        <SndDt>2020-08-26T21:23:56</SndDt>\n" + 
                       "        <MsgTp>bcs.402.001.01</MsgTp>\n" + 
                       "        <SignSN>**********</SignSN>\n" + 
                       "    </MsgHeader>\n" + 
                       "    <MsgBody>\n" + 
                       "        <TrxId>20200826212355040000026119210104</TrxId>\n" + 
                       "        <TrxOrderNo>7895237694690362</TrxOrderNo>\n" + 
                       "        <TrxAmt>CNY0.30</TrxAmt>\n" + 
                       "        <OriRefTrxAmt>CNY25.50</OriRefTrxAmt>\n" + 
                       "        <OriTrxAmt>CNY0.30</OriTrxAmt>\n" + 
                       "        <TrxPayerAmt>CNY0.30</TrxPayerAmt>\n" + 
                       "        <TrxStatus>00</TrxStatus>\n" + 
                       "        <TrxFinishTm>2020-08-26T21:23:55</TrxFinishTm>\n" + 
                       "        <BatchId>B202008260001</BatchId>\n" + 
                       "        <SysRtnCd>********</SysRtnCd>\n" + 
                       "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                       "        <SysRtnTm>2020-08-26T21:23:56</SysRtnTm>\n" + 
                       "        <BizStsCd>********</BizStsCd>\n" + 
                       "        <BizStsDesc>成功</BizStsDesc>\n" + 
                       "    </MsgBody>\n" + 
                       "</root>{S:XJjjS7V/c7v85lemBcusniamj3dRljSQ8H5/hkLFZpe8uNZWrsqOpUrFBBc/rLjHAFswdOS4i12s+nKVMoGgdlIifNo3wAM6tMYrMNxLHZ9gF6iStmfQAbN0FoHhzTaUNPXP9bAFdN4DMYe5IHdE/igFKekDlQdbnNYgS/M/9A5PqlPXl+S+8qMfa7e8bHHHj32elu3mYEqF+54IZ0Sp4bfJ9gxxn83DRkFQVUR5N6wIVOV8xmgnE4qgSHVt/gLPxJNDeYRuOxYlIgDeyojhHWLWJwFw9w8qgCeGFWklji6dCDPSN4oNgGlIFvP4yc/hN3Zy2unyT6oIN45Mf7UyLg==}";
           }
           return null;
       });

       String resultString = postPerform("test_bestpay_cancel_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
               "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
       );
       
       resultString = postPerform("test_bestpay_cancel_2", "/upay/v2/cancel", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       result = JsonUtil.jsonStrToObject(resultString, Map.class);

       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", CancelResponse.RESULT_CODE_CANCEL_SUCCESS,
               "biz_response.data.order_status", Order.Status.CANCELED.name()
       );
   }
   
   /**
   *
   * 翼支付 退款
   *
   * @throws Exception
   */
  @Test
  public void test_bestpay_refund()throws Exception{
      JSONObject request = SupportUtil.buildPayRequest("510471948860389764");
      PowerMockito.mockStatic(WebUtils.class);
      PowerMockito.when(WebUtils.doPost(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenAnswer((invocation) ->{
          Map header = invocation.getArgument(4);
          String msgTp = MapUtil.getString(header, ProtocolFields.MSG_TP);
          if(msgTp.equals(NuccBestPayConstants.PAY)) {
              return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                      "<root>\n" + 
                      "    <MsgHeader>\n" + 
                      "        <SndDt>2020-08-28T10:23:18</SndDt>\n" + 
                      "        <MsgTp>bcs.302.001.01</MsgTp>\n" + 
                      "        <SignSN>**********</SignSN>\n" + 
                      "    </MsgHeader>\n" + 
                      "    <MsgBody>\n" + 
                      "        <AcceptPartnerId>S000071********1</AcceptPartnerId>\n" + 
                      "        <AccountPartnerId>Z00005000001</AccountPartnerId>\n" + 
                      "        <TrxTypeCd>02</TrxTypeCd>\n" + 
                      "        <TrxId>20200828102318012010026123210200</TrxId>\n" + 
                      "        <TrxOrderNo>****************</TrxOrderNo>\n" + 
                      "        <TrxAmt>CNY0.30</TrxAmt>\n" + 
                      "        <TrxStatus>02</TrxStatus>\n" + 
                      "        <BatchId>B202008280001</BatchId>\n" + 
                      "        <SysRtnCd>********</SysRtnCd>\n" + 
                      "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                      "        <SysRtnTm>2020-08-28T10:23:18</SysRtnTm>\n" + 
                      "        <BizStsCd>********</BizStsCd>\n" + 
                      "        <BizStsDesc>成功</BizStsDesc>\n" + 
                      "    </MsgBody>\n" + 
                      "</root>{S:HagNWnLnGOwxhLl4DBIoIH1xlZD9Zeo9YXOTTnvLrRZSGd9E0jcpBlUyyO0fhMcedj+z60eL6bLndFa3KjfQNdG1AlFUPUUgCRe4mzHFI5HAnyFRiyQag9EcISZTeX4bh2ICyTzj9Y+cdH7BW7yOURrZ1XhN9ihrvLLboHR6Y8GtaM5WqxZHl69OjlDRGaLU++ACCwllJruD3pzEaCnuFtJ8mKb679GeEz+z7jTzQylj1WGyrbtlf8srAx3uZaARnlcdBOKxZ7jJfASHoCpJXHHUyxs4S+v8iuDqLsguzgTYisGFZnZmIOPFA1sjAhZBlEtwx4/IfC97yin0u67UPQ==}";
          }else if(msgTp.equals(NuccBestPayConstants.REFUND)) {
              return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                      "<root>\n" + 
                      "    <MsgHeader>\n" + 
                      "        <SndDt>2020-08-26T21:23:56</SndDt>\n" + 
                      "        <MsgTp>bcs.402.001.01</MsgTp>\n" + 
                      "        <SignSN>**********</SignSN>\n" + 
                      "    </MsgHeader>\n" + 
                      "    <MsgBody>\n" + 
                      "        <TrxId>20200826212355040000026119210104</TrxId>\n" + 
                      "        <TrxOrderNo>7895237694690362</TrxOrderNo>\n" + 
                      "        <TrxAmt>CNY0.30</TrxAmt>\n" + 
                      "        <OriRefTrxAmt>CNY25.50</OriRefTrxAmt>\n" + 
                      "        <OriTrxAmt>CNY0.30</OriTrxAmt>\n" + 
                      "        <TrxPayerAmt>CNY0.30</TrxPayerAmt>\n" + 
                      "        <TrxStatus>00</TrxStatus>\n" + 
                      "        <TrxFinishTm>2020-08-26T21:23:55</TrxFinishTm>\n" + 
                      "        <BatchId>B202008260001</BatchId>\n" + 
                      "        <SysRtnCd>********</SysRtnCd>\n" + 
                      "        <SysRtnDesc>成功</SysRtnDesc>\n" + 
                      "        <SysRtnTm>2020-08-26T21:23:56</SysRtnTm>\n" + 
                      "        <BizStsCd>********</BizStsCd>\n" + 
                      "        <BizStsDesc>成功</BizStsDesc>\n" + 
                      "    </MsgBody>\n" + 
                      "</root>{S:XJjjS7V/c7v85lemBcusniamj3dRljSQ8H5/hkLFZpe8uNZWrsqOpUrFBBc/rLjHAFswdOS4i12s+nKVMoGgdlIifNo3wAM6tMYrMNxLHZ9gF6iStmfQAbN0FoHhzTaUNPXP9bAFdN4DMYe5IHdE/igFKekDlQdbnNYgS/M/9A5PqlPXl+S+8qMfa7e8bHHHj32elu3mYEqF+54IZ0Sp4bfJ9gxxn83DRkFQVUR5N6wIVOV8xmgnE4qgSHVt/gLPxJNDeYRuOxYlIgDeyojhHWLWJwFw9w8qgCeGFWklji6dCDPSN4oNgGlIFvP4yc/hN3Zy2unyT6oIN45Mf7UyLg==}";
          }
          return null;
      });

      String resultString = postPerform("test_bestpay_refund_1", "/upay/v2/pay", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
      Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", PayResponse.RESULT_CODE_PAY_SUCCESS,
              "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN)
      );
      
      request.put(UpayService.REFUND_AMOUNT, "30");
      request.put(UpayService.REFUND_REQUEST_NO, "1");
      resultString = postPerform("test_bestpay_refund_2", "/upay/v2/refund", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
      result = JsonUtil.jsonStrToObject(resultString, Map.class);

      assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
              "biz_response.result_code", RefundResponse.RESULT_CODE_REFUND_SUCCESS,
              "biz_response.data.order_status", Order.Status.REFUNDED.name()
      );
  }
}
