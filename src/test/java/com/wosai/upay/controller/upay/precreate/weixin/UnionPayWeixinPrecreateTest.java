package com.wosai.upay.controller.upay.precreate.weixin;

import java.util.Map;

import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.model.api.PayResponse;
import com.wosai.upay.model.api.QueryResponse;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.ActivityUtil;
import com.wosai.upay.util.SupportUtil;

public class UnionPayWeixinPrecreateTest extends BaseTestController{

    @Before
    public void init() {
        mockGetBasicPrams(SupportUtil.buildBasicParams());
        mockGetUpayResult(ActivityUtil.buildGetUpayResultV2Result());
        mockGetRsaKeyDataById("*****private_key***", SupportUtil.RSA2_PRIVATE_KEY);
    }
    
    /**
     * 
     * 微信直连c2b
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_csb()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 2, "zg");
        String resultString = postPerform("test_precreate_csb", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );

    }
    
    /**
     * 
     * 微信直连wap
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                "    <sub_appid><![CDATA[wx72534f3638c59073]]></sub_appid>\n" + 
                "    <mch_id><![CDATA[1514637271]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[326440908]]></sub_mch_id>\n" + 
                "    <sign_type><![CDATA[RSA2]]></sign_type><cert_id>\n" + 
                "    <![CDATA[4097258801]]></cert_id>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                "    <prepay_id><![CDATA[wx14142549153606a262f64ade5471b10000]]></prepay_id>\n" + 
                "    <wc_pay_data><![CDATA[{\"appId\":\"wx72534f3638c59073\",\"timeStamp\":\"1597386349\",\"nonceStr\":\"b6bf484b04c44e179ef46c935729a5e4\",\"package\":\"prepay_id=wx14142549153606a262f64ade5471b10000\",\"signType\":\"RSA\",\"paySign\":\"m7K56X3ATLCTASINHBINTlfj4+xXq/xCEfuB7a4bSmGXeY8/6g6N3IDm/Df3vcGgVuo6GeLlrpCYhEUAcgD6XJ9M/WdmJHDz9rXNyzGsbm3gLap0Jq8totT8rhHCA141gWO3hA50+xbcqKJJYNJPpTn/p1Knn+KKsq/oCqSfkWo1HDJO8zUGD3s+ViPKfn2GDTNY6BXct+aQueJeEPgkVvw+llPJcs5JF4ImfDZJamQnmZ0DT89WVUQxe2+NOWnIeDALVP8iEYYPRQsjvWYdmtrgxq+oCrQV6g0dFhJHOsBam9+/iGcEjLxTTNbz7W1ZakKaFqa7LaCbD8dwk2jBEA==\"}]]></wc_pay_data>\n" + 
                "    <nonce_str><![CDATA[95de8f1db0e24711b274348297f627b1]]></nonce_str>\n" + 
                "    <sign><![CDATA[qHFeociWQ7sjKojJZrsryPTg9JgkhwpHMa5P6GG76nN2MGOs1vEJL7l2lpRM3mLqOdS1Pm+RufSNWTz2Fa6N6V/La2gYTuq4f0VStTLTjpHXntmbtm8PfzZJWFPe8HmRwYFVXocI0C7pS4F5Gmsux0JxPye1L3h7jzRKpO6EOwxmawWq8yW6VLOUpGG/kn0+8jLf0c8e99U/elehweC3VaPJN+RQww/9488kpTnWQnU1BdWi6QPHHVOdTtTMC4DnbbNcoyzSh7s47w8IOc8eH3DRctFvKJof3aG2KOFLu8P+8pAkXfjTE/3K6HUcjoI5g44vDOZ1ifWMxWXjzz0gWg==]]></sign>\n" + 
                "</xml>");
        String resultString = postPerform("test_precreate_wap", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 微信直连 小程序
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_mini()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 4, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                "<xml>\n" + 
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                "    <sub_appid><![CDATA[wx72534f3638c59073]]></sub_appid>\n" + 
                "    <mch_id><![CDATA[1514637271]]></mch_id>\n" + 
                "    <sub_mch_id><![CDATA[326440908]]></sub_mch_id>\n" + 
                "    <sign_type><![CDATA[RSA2]]></sign_type><cert_id>\n" + 
                "    <![CDATA[4097258801]]></cert_id>\n" + 
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                "    <prepay_id><![CDATA[wx14142549153606a262f64ade5471b10000]]></prepay_id>\n" + 
                "    <wc_pay_data><![CDATA[{\"appId\":\"wx72534f3638c59073\",\"timeStamp\":\"1597386349\",\"nonceStr\":\"b6bf484b04c44e179ef46c935729a5e4\",\"package\":\"prepay_id=wx14142549153606a262f64ade5471b10000\",\"signType\":\"RSA\",\"paySign\":\"m7K56X3ATLCTASINHBINTlfj4+xXq/xCEfuB7a4bSmGXeY8/6g6N3IDm/Df3vcGgVuo6GeLlrpCYhEUAcgD6XJ9M/WdmJHDz9rXNyzGsbm3gLap0Jq8totT8rhHCA141gWO3hA50+xbcqKJJYNJPpTn/p1Knn+KKsq/oCqSfkWo1HDJO8zUGD3s+ViPKfn2GDTNY6BXct+aQueJeEPgkVvw+llPJcs5JF4ImfDZJamQnmZ0DT89WVUQxe2+NOWnIeDALVP8iEYYPRQsjvWYdmtrgxq+oCrQV6g0dFhJHOsBam9+/iGcEjLxTTNbz7W1ZakKaFqa7LaCbD8dwk2jBEA==\"}]]></wc_pay_data>\n" + 
                "    <nonce_str><![CDATA[95de8f1db0e24711b274348297f627b1]]></nonce_str>\n" + 
                "    <sign><![CDATA[qHFeociWQ7sjKojJZrsryPTg9JgkhwpHMa5P6GG76nN2MGOs1vEJL7l2lpRM3mLqOdS1Pm+RufSNWTz2Fa6N6V/La2gYTuq4f0VStTLTjpHXntmbtm8PfzZJWFPe8HmRwYFVXocI0C7pS4F5Gmsux0JxPye1L3h7jzRKpO6EOwxmawWq8yW6VLOUpGG/kn0+8jLf0c8e99U/elehweC3VaPJN+RQww/9488kpTnWQnU1BdWi6QPHHVOdTtTMC4DnbbNcoyzSh7s47w8IOc8eH3DRctFvKJof3aG2KOFLu8P+8pAkXfjTE/3K6HUcjoI5g44vDOZ1ifWMxWXjzz0gWg==]]></sign>\n" + 
                "</xml>");
        
        String resultString = postPerform("test_precreate_mini", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
    }
    
    /**
     * 
     * 微信直连wap交易成功
     * 
     * @throws Exception
     */
    @Test
    public void test_precreate_success_wap()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenAnswer((mock)->{
            String url = mock.getArgument(3);
            if(url.endsWith("/wx/v1/pay/prepay")) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                        "<xml>\n" + 
                        "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" + 
                        "    <sub_appid><![CDATA[wx72534f3638c59073]]></sub_appid>\n" + 
                        "    <mch_id><![CDATA[1514637271]]></mch_id>\n" + 
                        "    <sub_mch_id><![CDATA[326440908]]></sub_mch_id>\n" + 
                        "    <sign_type><![CDATA[RSA2]]></sign_type><cert_id>\n" + 
                        "    <![CDATA[4097258801]]></cert_id>\n" + 
                        "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "    <return_msg><![CDATA[成功]]></return_msg>\n" + 
                        "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                        "    <prepay_id><![CDATA[wx14142549153606a262f64ade5471b10000]]></prepay_id>\n" + 
                        "    <wc_pay_data><![CDATA[{\"appId\":\"wx72534f3638c59073\",\"timeStamp\":\"1597386349\",\"nonceStr\":\"b6bf484b04c44e179ef46c935729a5e4\",\"package\":\"prepay_id=wx14142549153606a262f64ade5471b10000\",\"signType\":\"RSA\",\"paySign\":\"m7K56X3ATLCTASINHBINTlfj4+xXq/xCEfuB7a4bSmGXeY8/6g6N3IDm/Df3vcGgVuo6GeLlrpCYhEUAcgD6XJ9M/WdmJHDz9rXNyzGsbm3gLap0Jq8totT8rhHCA141gWO3hA50+xbcqKJJYNJPpTn/p1Knn+KKsq/oCqSfkWo1HDJO8zUGD3s+ViPKfn2GDTNY6BXct+aQueJeEPgkVvw+llPJcs5JF4ImfDZJamQnmZ0DT89WVUQxe2+NOWnIeDALVP8iEYYPRQsjvWYdmtrgxq+oCrQV6g0dFhJHOsBam9+/iGcEjLxTTNbz7W1ZakKaFqa7LaCbD8dwk2jBEA==\"}]]></wc_pay_data>\n" + 
                        "    <nonce_str><![CDATA[95de8f1db0e24711b274348297f627b1]]></nonce_str>\n" + 
                        "    <sign><![CDATA[qHFeociWQ7sjKojJZrsryPTg9JgkhwpHMa5P6GG76nN2MGOs1vEJL7l2lpRM3mLqOdS1Pm+RufSNWTz2Fa6N6V/La2gYTuq4f0VStTLTjpHXntmbtm8PfzZJWFPe8HmRwYFVXocI0C7pS4F5Gmsux0JxPye1L3h7jzRKpO6EOwxmawWq8yW6VLOUpGG/kn0+8jLf0c8e99U/elehweC3VaPJN+RQww/9488kpTnWQnU1BdWi6QPHHVOdTtTMC4DnbbNcoyzSh7s47w8IOc8eH3DRctFvKJof3aG2KOFLu8P+8pAkXfjTE/3K6HUcjoI5g44vDOZ1ifWMxWXjzz0gWg==]]></sign>\n" + 
                        "</xml>";
            }else if(url.endsWith("/wx/v1/pay/order/qry")) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" + 
                        "<xml>\n" + 
                        "    <return_code><![CDATA[SUCCESS]]></return_code>\n" + 
                        "    <return_msg><![CDATA[OK]]></return_msg>\n" + 
                        "    <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" + 
                        "    <mch_id><![CDATA[1238313502]]></mch_id>\n" + 
                        "    <sub_mch_id><![CDATA[1514263421]]></sub_mch_id>\n" + 
                        "    <nonce_str><![CDATA[jdhTTsWyU3stngRf]]></nonce_str>\n" + 
                        "    <sign><![CDATA[BC1523D5674CC3C01CAB9AA41393D73A]]></sign>\n" + 
                        "    <result_code><![CDATA[SUCCESS]]></result_code>\n" + 
                        "    <openid><![CDATA[oyBevt8tiv8JCsJmBEI-96vhEukw]]></openid>\n" + 
                        "    <is_subscribe><![CDATA[N]]></is_subscribe>\n" + 
                        "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" + 
                        "    <bank_type><![CDATA[CMB_CREDIT]]></bank_type>\n" + 
                        "    <total_fee>2500</total_fee>\n" + 
                        "    <fee_type><![CDATA[CNY]]></fee_type>\n" + 
                        "    <transaction_id><![CDATA[4200000706202008142384955424]]></transaction_id>\n" + 
                        "    <out_trade_no><![CDATA[****************]]></out_trade_no>\n" + 
                        "    <attach><![CDATA[]]></attach>\n" + 
                        "    <time_end><![CDATA[**************]]></time_end>\n" + 
                        "    <trade_state><![CDATA[SUCCESS]]></trade_state>\n" + 
                        "    <cash_fee>2500</cash_fee>\n" + 
                        "    <trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n" + 
                        "    <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" + 
                        "    <version><![CDATA[1.0]]></version>\n" + 
                        "    <promotion_detail><![CDATA[]]></promotion_detail>\n" + 
                        "</xml>";
            }
            return "fail";
        });
        
        String resultString = postPerform("test_precreate_wap", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", PayResponse.RESULT_CODE_PRECREATE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.CREATED.name()
                );
        Thread.sleep(5000);
        resultString = postPerform("test_precreate_wap", "/upay/v2/query", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        result = JsonUtil.jsonStrToObject(resultString, Map.class);
        
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                        "biz_response.result_code", QueryResponse.RESULT_CODE_SUCCESS,
                        "biz_response.data.client_sn", request.getString(UpayService.CLIENT_SN),
                        "biz_response.data.order_status", Order.Status.PAID.name()
                );
    }

    @Test
    public void test_precreate_new () throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("134618243148626213");
        PowerMockito.mockStatic(HttpClientUtils.class);
        String payReturnXml = "<xml>\n" +
                "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "  <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                "  <appid><![CDATA[wx42f6886cbbb3fdbc]]></appid>\n" +
                "  <mch_id><![CDATA[1238313502]]></mch_id>\n" +
                "  <sub_mch_id><![CDATA[1522166191]]></sub_mch_id>\n" +
                "  <nonce_str><![CDATA[NNihd45njF2NnPXK]]></nonce_str>\n" +
                "  <sign><![CDATA[B96BEE3A9592011EFFB5192821DBEA4A]]></sign>\n" +
                "  <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                "  <openid><![CDATA[oyBevt50TTTMcvXEQhmOrLFMLBnA]]></openid>\n" +
                "  <is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "  <trade_type><![CDATA[MICROPAY]]></trade_type>\n" +
                "  <bank_type><![CDATA[OTHERS]]></bank_type>\n" +
                "  <total_fee>30</total_fee>\n" +
                "  <fee_type><![CDATA[CNY]]></fee_type>\n" +
                "  <transaction_id><![CDATA[42000005737202007137723052413]]></transaction_id>\n" +
                "  <out_trade_no><![CDATA[789523x6647687722]]></out_trade_no>\n" +
                "  <attach><![CDATA[]]></attach>\n" +
                "  <time_end><![CDATA[**************]]></time_end>\n" +
                "  <sub_appid><![CDATA[wx5d5dbc67b184e1a3d]]></sub_appid>\n" +
                "  <sub_openid><![CDATA[oUlSyjgyqFH2DnGtDudzmcdboYtn8]]></sub_openid>\n" +
                "  <sub_is_subscribe><![CDATA[N]]></sub_is_subscribe>\n" +
                "  <cash_fee>30</cash_fee>\n" +
                "  <cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                "  <version><![CDATA[1.0]]></version>\n" +
                "  <promotion_detail><![CDATA[[{\"promotion_id\":\"2000000100188362099\",\"name\":\"微信支付到店红包\",\"scope\":\"GLOBAL\",\"type\":\"COUPON\",\"amount\":9,\"activity_id\":\"10838200\",\"wxpay_contribute\":9,\"merchant_contribute\":0,\"other_contribute\":0}]]]></promotion_detail>\n" +
                "  <trade_state><![CDATA[SUCCESS]]></trade_state>\n" +
                "</xml>";

        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn(payReturnXml);
        String resultString = postPerform("test_weixin_barcode__precreate", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction weixin precreate {}", transaction);
    }

    @Test
    public void test_precreate_csb_new()throws Exception{
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.UNION_PAY_TRADE_PARAMS));
        JSONObject request = SupportUtil.buildPrecreateRequest(3, 3, "zg");
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),  Mockito.anyInt(), Mockito.anyInt())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" +
                "<xml>\n" +
                "    <appid><![CDATA[wx3819a8bec7f2861b]]></appid>\n" +
                "    <sub_appid><![CDATA[wx72534f3638c59073]]></sub_appid>\n" +
                "    <mch_id><![CDATA[1514637271]]></mch_id>\n" +
                "    <sub_mch_id><![CDATA[326440908]]></sub_mch_id>\n" +
                "    <sign_type><![CDATA[RSA2]]></sign_type><cert_id>\n" +
                "    <![CDATA[4097258801]]></cert_id>\n" +
                "    <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "    <return_msg><![CDATA[支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]]]></return_msg>\n" +
                "    <result_code><![CDATA[ACQ.PAYMENT_AUTH_CODE_INVALID]]></result_code>\n" +
                "    <trade_type><![CDATA[JSAPI]]></trade_type>\n" +
                "    <prepay_id><![CDATA[wx14142549153606a262f64ade5471b10000]]></prepay_id>\n" +
                "    <wc_pay_data><![CDATA[{\"appId\":\"wx72534f3638c59073\",\"timeStamp\":\"1597386349\",\"nonceStr\":\"b6bf484b04c44e179ef46c935729a5e4\",\"package\":\"prepay_id=wx14142549153606a262f64ade5471b10000\",\"signType\":\"RSA\",\"paySign\":\"m7K56X3ATLCTASINHBINTlfj4+xXq/xCEfuB7a4bSmGXeY8/6g6N3IDm/Df3vcGgVuo6GeLlrpCYhEUAcgD6XJ9M/WdmJHDz9rXNyzGsbm3gLap0Jq8totT8rhHCA141gWO3hA50+xbcqKJJYNJPpTn/p1Knn+KKsq/oCqSfkWo1HDJO8zUGD3s+ViPKfn2GDTNY6BXct+aQueJeEPgkVvw+llPJcs5JF4ImfDZJamQnmZ0DT89WVUQxe2+NOWnIeDALVP8iEYYPRQsjvWYdmtrgxq+oCrQV6g0dFhJHOsBam9+/iGcEjLxTTNbz7W1ZakKaFqa7LaCbD8dwk2jBEA==\"}]]></wc_pay_data>\n" +
                "    <nonce_str><![CDATA[95de8f1db0e24711b274348297f627b1]]></nonce_str>\n" +
                "    <sign><![CDATA[qHFeociWQ7sjKojJZrsryPTg9JgkhwpHMa5P6GG76nN2MGOs1vEJL7l2lpRM3mLqOdS1Pm+RufSNWTz2Fa6N6V/La2gYTuq4f0VStTLTjpHXntmbtm8PfzZJWFPe8HmRwYFVXocI0C7pS4F5Gmsux0JxPye1L3h7jzRKpO6EOwxmawWq8yW6VLOUpGG/kn0+8jLf0c8e99U/elehweC3VaPJN+RQww/9488kpTnWQnU1BdWi6QPHHVOdTtTMC4DnbbNcoyzSh7s47w8IOc8eH3DRctFvKJof3aG2KOFLu8P+8pAkXfjTE/3K6HUcjoI5g44vDOZ1ifWMxWXjzz0gWg==]]></sign>\n" +
                "</xml>");
        String resultString = postPerform("test_precreate_wap_new", "/upay/v2/precreate", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);

        String orderSn = BeanUtil.getPropString(result, "biz_response.data.sn");
        Map order = dataRepository.getOrderByOrderSn(null, orderSn);
        Map transaction = dataRepository.getPayTransactionByOrderSn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn);
        logger.info("transaction weixin precreate {}", transaction);
    }
}
