package com.wosai.upay.controller.support;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.controller.BaseTestController;
import com.wosai.upay.model.api.CommonResponse;
import com.wosai.upay.service.UpayService;
import com.wosai.upay.util.SupportUtil;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.http.MediaType;

import java.util.Map;

public class SupportControllerTest  extends BaseTestController{
    
    /**
     * 
     * 使用不支持的payway来调用接口
     * 
     * @throws Exception
     */
    @Test
    public void test_query_user_id_with_fail_payway() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("6227716222497164012");
        
        String resultString = postPerform("test_query_user_id_with_fail_payway", "/upay/support/queryUserId", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "INVALID_BARCODE",
                "biz_response.error_code_standard", "EP03"
        );
    }
    
    /**
     * 
     * 
     * 交易参数不支持
     * 
     * @throws Exception
     */
    @Test
    public void test_query_user_id_not_support() throws Exception {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V1_TRADE_PARAMS));
        
        JSONObject request = SupportUtil.buildPayRequest("134635185071552411");
        String resultString = postPerform("test_query_user_id_not_support", "/upay/support/queryUserId", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "INVALID_BARCODE",
                "biz_response.error_code_standard", "EP05"
        );
    }
    
    /**
     * 
     * 
     * 微信查询返回成功
     * 
     * @throws Exception
     */
    @Test
    public void test_query_user_id_with_weixin_success() throws Exception {
        String openId = "o24xJs8oZ4Bb7bnobHXoSpn5Z9tw";
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<xml>\n" + 
                "    <return_code>SUCCESS</return_code>\n" + 
                "    <result_code>SUCCESS</result_code>\n" + 
                "    <sub_openid>"+ openId +"</sub_openid>\n" + 
                "</xml>");
        JSONObject request = SupportUtil.buildPayRequest("134535087903881803");
        
        String resultString = postPerform("test_query_user_id_with_weixin_success", "/upay/support/queryUserId", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "QUERY_SUCCESS",
                "biz_response.data.sub_openid", openId
        );
    }
    
    /**
     * 
     * 
     * 微信查询返回失败
     * 
     * @throws Exception
     */
    @Test
    public void test_query_user_id_with_weixin_fail() throws Exception {
        String returnCode = "FAIL";
        String returnMsg = "参数错误";
        
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<xml>\n" + 
                "    <err_code>" + returnCode +"</err_code>\n" + 
                "    <err_code_des>"+ returnMsg +"</err_code_des>\n" + 
                "</xml>");
        JSONObject request = SupportUtil.buildPayRequest("134535087903881803");
        
        String resultString = postPerform("test_query_user_id_with_weixin_fail", "/upay/support/queryUserId", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.error_code", returnCode,
                "biz_response.error_message", returnMsg
        );
    }
    
    /**
     * 
     * 
     * 支付宝查询返回成功
     * 
     * @throws Exception
     */
    @Test
    public void test_query_user_id_with_alipay_success() throws Exception {
        String buyerId = "2088202283953664";
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("{\n" + 
                "    \"alipay_mobile_shake_user_query_response\": {\n" + 
                "        \"user_id\": \""+ buyerId +"\",\n" + 
                "        \"bizdata\": \"{\\\"UTDID\\\":\\\"W5SzKQZAXHcDAMQzEhVmTDZq\\\"}\"\n" + 
                "    },\n" + 
                "    \"sign\": \"W6R48KPJStxlkikjRBN/6CMJXoHq7OsFjVJDhRMMh7Q5E8V32it5FfiKAIRBBXDUEUe/CLlHXXeCicPWdg3r25hnco743FaqoJ3BPO/1ioSTUhLY/lKTvsLpGgoEy8gEql71/WSo/6yZ9K3+8hui+81Q8qVFEmO+R8/U3W4J76o=\"\n" + 
                "}");
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        
        String resultString = postPerform("test_query_user_id_with_alipay_success", "/upay/support/queryUserId", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "QUERY_SUCCESS",
                "biz_response.data.openid", buyerId
        );
    }
    
    /**
     * 
     * 
     * 支付宝查询返回失败
     * 
     * @throws Exception
     */
    @Test
    public void test_query_user_id_with_alipay_fail() throws Exception {
        String code = "10001";
        String subMsg = "参数错误";
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));
        mockGetRsaKeyDataById(SupportUtil.RSA2_PRIVATE_KEY);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("{\n" + 
                "    \"alipay_mobile_shake_user_query_response\": {\n" + 
                "        \"code\": \""+ code +"\",\n" + 
                "        \"sub_msg\": \""+subMsg +"\"\n" + 
                "    },\n" + 
                "    \"sign\": \"W6R48KPJStxlkikjRBN/6CMJXoHq7OsFjVJDhRMMh7Q5E8V32it5FfiKAIRBBXDUEUe/CLlHXXeCicPWdg3r25hnco743FaqoJ3BPO/1ioSTUhLY/lKTvsLpGgoEy8gEql71/WSo/6yZ9K3+8hui+81Q8qVFEmO+R8/U3W4J76o=\"\n" + 
                "}");
        
        JSONObject request = SupportUtil.buildPayRequest("281206934530305376");
        
        String resultString = postPerform("test_query_user_id_with_alipay_fail", "/upay/support/queryUserId", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.error_code", code,
                "biz_response.error_message", subMsg
        );
    }
    
    /**
     * 
     * 
     * 清理商户缓存接口
     * 
     * @throws Exception
     */
    @Test
    public void test_clear_basic_cache() throws Exception {
        JSONObject request = SupportUtil.buildPayRequest("");
        String result = postPerform("test_clear_basic_cache", "/upay/support/clearBasicCache", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        org.junit.Assert.assertEquals("result", "\"success\"", result);
    }


    /**
     *
     * 交易参数不支持
     *
     * @throws Exception
     */
    @Test
    public void test_getWxAuthInfo_not_support() throws Exception {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.ALIPAY_V2_TRADE_PARAMS));

        JSONObject request = SupportUtil.buildWxAuthInfoRequest("adbasdsjhbvcndx");
        String resultString = postPerform("test_getWxAuthInfo_not_support", "/upay/support/getWxpayfaceAuthinfo", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "FAIL",
                "biz_response.error_code", "NOT_SUPPORT",
                "biz_response.error_code_standard", "EP142"
        );
    }

    /**
     *
     * 微信查询返回成功
     *
     * @throws Exception
     */
    @Test
    public void test_getWxAuthInfo_with_weixin_success() throws Exception {
        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<xml>\n" +
                "    <return_code>SUCCESS</return_code>\n" +
                "    <return_msg>请求成功</return_msg>\n" +
                "    <nonce_str>nonce_str</nonce_str>\n" +
                "    <appid>appid</appid>\n" +
                "    <mch_id>mch_id</mch_id>\n" +
                "    <authinfo>authinfo</authinfo>\n" +
                "    <expires_in>0</expires_in>\n" +
                "</xml>");
        JSONObject request = SupportUtil.buildWxAuthInfoRequest("adbasdsjhbvcndx");

        String resultString = postPerform("test_getWxAuthInfo_with_weixin_success", "/upay/support/getWxpayfaceAuthinfo", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", "QUERY_SUCCESS",
                "biz_response.data.expires_in", 0,
                "biz_response.data.mch_id", "mch_id",
                "biz_response.data.appid", "appid",
                "biz_response.data.authinfo", "authinfo"
        );
    }

    /**
     *
     * 微信查询返回失败
     *
     * @throws Exception
     */
    @Test
    public void test_getWxAuthInfo_with_weixin_fail() throws Exception {
        String returnCode = "QUERY_FAIL";
        String returnMsg = "请求失败";

        mockGetAllPrams(SupportUtil.buildGetAllParams(SupportUtil.WEIXIN_TRADE_PARAMS));
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.when(HttpClientUtils.doPost(Mockito.anyString(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn("<xml>\n" +
                "    <return_code>" + returnCode +"</return_code>\n" +
                "    <return_msg>"+ returnMsg +"</return_msg>\n" +
                "</xml>");
        JSONObject request = SupportUtil.buildWxAuthInfoRequest("adbasdsjhbvcndx");

        String resultString = postPerform("test_getWxAuthInfo_with_weixin_fail", "/upay/support/getWxpayfaceAuthinfo", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", returnCode,
                "biz_response.error_message", returnMsg
        );
    }
    
    /**
    *
    * 生成订单号接口
    *
    * @throws Exception
    */
   @Test
   public void test_gen_sn() throws Exception {
       String returnCode = "FAIL";
       String returnMsg = "暂无权限生成单号";

       JSONObject request = SupportUtil.buildPayRequest("123");

       String resultString = postPerform("test_gen_sn", "/upay/support/genSn", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
       Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
       assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
               "biz_response.result_code", returnCode,
               "biz_response.error_message", returnMsg
       );
   }

    /**
     * 支付宝-花呗分期吱口令码申领
     *
     * @throws Exception
     */
    @Test
   public void test_hbfq_createShareCode() throws Exception {
        String returnCode = "FAIL";
        String returnMsg = "获取吱口令失败";

        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(UpayService.EXTENDED,  CollectionUtil.hashMap(
                "biz_link", "https://xxx.com"
        ));
        String resultString = postPerform("test_hbfq_createShareCode", "/upay/support/alipay/hbfq/createShareCode", MediaType.APPLICATION_JSON_UTF8_VALUE, request.toJSONString());
        Map result = JsonUtil.jsonStrToObject(resultString, Map.class);
        assertEquals(result, CommonResponse.RESULT_CODE, CommonResponse.RESULT_CODE_SUCCESS,
                "biz_response.result_code", returnCode,
                "biz_response.error_message", returnMsg
        );
   }

}
