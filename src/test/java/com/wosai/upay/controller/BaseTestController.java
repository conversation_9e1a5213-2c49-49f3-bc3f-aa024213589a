package com.wosai.upay.controller;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.weixin.WeixinV3Client;
import com.wosai.mpay.util.*;
import com.wosai.upay.activity.service.ActivityUpayService;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.Jaxb2RootElementHttpMessageConverter;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@PowerMockIgnore({"javax.management.*", "sun.security.*", "javax.net.*", "javax.net.ssl.*"}) //为了解决使用powermock后，提示classloader错误
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath:spring/business-config.xml", "classpath:spring/tools-config.xml", "classpath:spring/mvc-core-config.xml"})
@PrepareForTest({HttpClientUtils.class, WeixinV3Client.class, ApolloConfigurationCenterUtil.class, HmacSignature.class, ChinaumsSignature.class, SM2Util.class}) // 所有需要测试的类列在此处，适用于模拟final类或有final, private, static, native方法的类
public class BaseTestController{
    protected static final Logger logger = LoggerFactory.getLogger(BaseTestController.class);
    protected MockMvc mockMvc;
    
    @Autowired
    WebApplicationContext context;
    
    @Autowired
    protected UpayController upayController;
    
    @Autowired
    protected NotifyController notifyController;

    @Autowired
    AdminController adminController;
    
    @Autowired
    DepositController depositController;
    
    @Autowired
    QrcodeController qrcodeController;
    
    @Autowired
    SupportController supportController;

    @Autowired
    protected DataRepository dataRepository;

    @Autowired
    ProviderMangerController providerMangerController;
    @MockBean
    SupportService supportService;
    @MockBean(name = "activityUpayService")
    ActivityUpayService activityUpayService;
    
    @MockBean
    TerminalService terminalService;
    private static final String BP_CACHE_KEY = "BP-Cache";
    private static final String CACHE_KEY_REG = "-";
    private static final String GEN_SN_KEY = "Gen-Sn-";

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(context, upayController, adminController, depositController, qrcodeController, supportController, providerMangerController, notifyController)
                    .setMessageConverters(new MappingJackson2HttpMessageConverter(), new Jaxb2RootElementHttpMessageConverter())
                    .build();
    }
    
    /**
     * 
     * mock接口，返回响应内容
     * 
     * @param testcaseName          测试用例名称
     * @param url                   服务地址
     * @param mediaType             请求类型
     * @param requestString         请求内容
     * @return String               返回内容
     * @throws Exception
     */
    protected String postPerform(String testcaseName, String url, String mediaType, String requestString) throws Exception{
        return postPerform(testcaseName, url, mediaType, requestString, false);
    }
    
    /**
     * 
     * mock接口，返回响应内容
     * 
     * @param testcaseName          测试用例名称
     * @param url                   服务地址
     * @param mediaType             请求类型
     * @param requestString         请求内容
     * @param isFake                是否fake请求
     * @return String               返回内容
     * @throws Exception
     */
    protected String postPerform(String testcaseName, String url, String mediaType, String requestString, boolean isFake) throws Exception{
        MockHttpServletRequestBuilder request = post(url)
            .contentType(mediaType)
            .content(requestString).accept(MediaType.ALL);
        if(isFake) {
            request.header("fake", "1");
        }
        MvcResult mvcResult = mockMvc.perform(request)
            .andExpect(status().isOk())
            .andReturn();
        
        String result = null;
        if(null != mvcResult.getResponse().getContentType()) {
            result = mvcResult.getResponse().getContentAsString();
        } else if(null != mvcResult.getAsyncResult()){
            // servlet异步接口的content-type=null，无法使用getContentAsString获取
            if(mvcResult.getAsyncResult() instanceof String) {
                result = (String)mvcResult.getAsyncResult();
            }else {
                result = JsonUtil.toJsonStr(mvcResult.getAsyncResult());
            }
        }
        
        logger.info("{} request:: {}, response:: {}", testcaseName, requestString, result);
        return result;
    }
    
    protected String formatTimeString(String format, long time) {
        return new SafeSimpleDateFormat(format).format(new Date(time));
    }
    
    protected void assertEquals(Map result, Object ... params) {
        for (int i = 0; i < params.length / 2; ++i) {
            org.junit.Assert.assertEquals(String.valueOf(params[2*i]), params[2 * i + 1], BeanUtil.getNestedProperty(result, String.valueOf(params[2*i])));
        }
    }
    
    protected void mockGetAllPrams(Map result) {
        PowerMockito.when(supportService.getAllParams(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        PowerMockito.when(supportService.getAllParamsWithTradeApp(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        PowerMockito.when(terminalService.getSecret(Mockito.anyString())).thenReturn(CollectionUtil.hashMap(Terminal.CURRENT_SECRET, "VGZ4QWxAWP1BhwzG"));

    }
    
    protected void mockGetAllPrams(Throwable throwable) {
        // core-b 会根据终端缓存报错信息，需要清理缓存
        ExternalServiceFacade.TERMINAL_GET_PARAMS_EXCEPTION_CACHE.invalidateAll();
        PowerMockito.when(supportService.getAllParams(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(throwable);
        PowerMockito.when(supportService.getAllParamsWithTradeApp(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(throwable);
    }

    protected void mockGetRsaKeyDataById(String result) {
        PowerMockito.when(supportService.getRsaKeyDataById(Mockito.anyString())).thenReturn(result);
    }

    protected void mockGetRsaKeyDataById(String id, String result) {
        PowerMockito.when(supportService.getRsaKeyDataById(id)).thenReturn(result);
    }
    
    protected void mockGetAlipayV2AppAuthInfo(Map result) {
        PowerMockito.when(supportService.getAlipayV2AppAuthInfo(Mockito.any(), Mockito.any())).thenReturn(result);
    }
    
    protected void mockGetBasicPrams(Map result) {
        PowerMockito.when(supportService.getBasicParams(Mockito.any(), Mockito.any())).thenReturn(result);
    }

    protected void mockGetUpayResult(Map result) {
        PowerMockito.when(activityUpayService.getUpayResultV2(Mockito.any())).thenReturn(result);
    }

    protected void mockGetUpayRefundResult(Map result) {
        PowerMockito.when(activityUpayService.getUpayRefundResultV2(Mockito.any())).thenReturn(result);
    }
}
