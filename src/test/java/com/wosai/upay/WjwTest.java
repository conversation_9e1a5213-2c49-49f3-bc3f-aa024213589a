package com.wosai.upay;

import com.ccb.mis.CcbMisSdk;
import com.ccb.mis.entity.ParamEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.profit.sharing.util.UpayConfigCryptoUtil;
import com.wosai.upay.model.TerminalInfo;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.kafka.Transaction;
import com.wosai.upay.repository.DataRepository;
import com.wosai.upay.service.AmqpFacade;
import com.wosai.upay.service.ExternalServiceFacade;
//import com.wosai.upay.service.SceneConfigFacade;
import com.wosai.upay.service.TsnGenerator;
//import com.wosai.upay.service.paynet.PaynetQrCodeNotifyProcess;
//import com.wosai.upay.service.paynet.PaynetQrCodeTransactionEnquiryProcess;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.util.AvroBeanHelper;
import com.wosai.upay.workflow.*;
import lombok.SneakyThrows;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.nio.ByteBuffer;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@ContextConfiguration(locations = {"classpath:spring/business-config.xml", "classpath:spring/tools-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class WjwTest {
    @Autowired
    TsnGenerator generator;
    @Autowired
    ExternalServiceFacade externalServiceFacade;
//    @Autowired
//    PaynetQrCodeNotifyProcess process;


    @BeforeClass
    public static void setup() {

    }

    @AfterClass
    public static void tearDown() {

    }

    @Autowired
    DataRepository dataRepository;
    @Autowired
    WorkflowManager workflowManager;
    @Autowired
//    SceneConfigFacade sceneConfigFacade;
    @Test
    public void testTsnGenerator() throws MpayException {
//        Map<String, Object> order = dataRepository.getOrderByOrderSn(null, "7894259268945135");
//        Map<String, Object> transaction = dataRepository.getTransactionByTsn(BeanUtil.getPropString(order, Order.MERCHANT_ID), "7894259268945135");
//        TransactionContext context = new TransactionContext(order, transaction);
//        MpayServiceProvider mpayServiceProvider = workflowManager.matchServiceProvider(transaction);
//        Map<String,Object> result = JsonUtil.jsonStrToObject("{\"retCode\":\"-2\",\"retMsg\":\"**********,等待用户付款，@@10003~~ order success pay inprocess@@\",\"transData\":{\"merchantCode\":\"105000654226491\",\"termNo\":\"10378631\",\"systemTraceNo\":\"1010149001642660448107145\"},\"validDate\":\"20711208\",\"traceNo\":\"1300261300301642660448317\"}", Map.class);
//        ((CcbServiceProvider)mpayServiceProvider).setTransactionContextErrorInfo(result, context , "pay");
//        System.out.println(transaction.get(Transaction.PROVIDER_ERROR_INFO));
//
//
//        long providerQueryExpireTime = ApolloConfigurationCenterUtil.getProviderQueryExpireTime("1033");
//        System.out.println(providerQueryExpireTime);
//        mpayServiceProvider.depositQuery(context);
//        mpayServiceProvider.depositCancel(context);
//        mpayServiceProvider.depositConsume(context);
//        mpayServiceProvider.refund(context);
//        TerminalInfo terminalInfo = ((AbstractServiceProvider) mpayServiceProvider).genTerminalInfo(transaction);
//        System.out.println(terminalInfo);
//        Map bizErrorInfo = SceneConfigFacade.getBizErrorInfo("pay", 2, "xxx", "复合域[terminal_info]缺少[device_id]字段(UP01A5100)");
//        System.out.println(bizErrorInfo);
        String s = "{\n" +
                "      \"BusMsg\": {\n" +
                "        \"AppHdr\": {\n" +
                "          \"Fr\": {\n" +
                "            \"FIId\": {\n" +
                "              \"FinInstnId\": {\n" +
                "                \"Othr\": {\n" +
                "                  \"Id\": \"RPPEMYKL\"\n" +
                "                }\n" +
                "              }\n" +
                "            }\n" +
                "          },\n" +
                "          \"To\": {\n" +
                "            \"FIId\": {\n" +
                "              \"FinInstnId\": {\n" +
                "                \"Othr\": {\n" +
                "                  \"Id\": \"BEEZMYNB\"\n" +
                "                }\n" +
                "              }\n" +
                "            }\n" +
                "          },\n" +
                "          \"BizMsgIdr\": \"20240821RPPEMYKL520HQR14650371\",\n" +
                "          \"MsgDefIdr\": \"pacs.008.001.06.01\",\n" +
                "          \"BizSvc\": \"RPP\",\n" +
                "          \"CreDt\": \"2024-08-21T01:30:52Z\",\n" +
                "          \"PssblDplct\": false,\n" +
                "          \"RPPSgntr\": {\n" +
                "            \"KeyNbr\": \"6c2a4b6c39e458fdea321c42e8c8a91a\",\n" +
                "            \"Signature\": \"qmY7L34nMgjrmQxOyYIvL2+1p0YZdijBK6+pk6zYgl6Er25Pg7kRETpJkXjlcDVMkNMNWyl/HfUeGD2KQIMr5bsSLlUK24U0g6ndoYFhdYDd9MJw9qOb6X0AQs5y+j0X26c1Ldye0fOryBNNSyJkZZvUZgIsBLk74wV714N770aHIrZoXH+n1KXJhXzSx/L+P/i+5mhB0i7EAotnXMgxiNHEJQQ/YX9QXpg81LAEhWBJjOQOXr8wZ9tUMv3AORm7QeCeyirq/ryMCLpr3TljkBPqFoqGEeEBl6GB+FfugjWlnJk1arjjxU3XuQGNcTgWWJQvc+oUR+7gCYOfRK2UMw==\"\n" +
                "          }\n" +
                "        },\n" +
                "        \"Document\": {\n" +
                "          \"FIToFICstmrCdtTrfInf\": {\n" +
                "            \"GrpHdr\": {\n" +
                "              \"MsgId\": \"20240821GXSPMYKL52044712044\",\n" +
                "              \"CreDtTm\": \"2024-08-21T09:30:52.552\",\n" +
                "              \"NbOfTxs\": 1,\n" +
                "              \"SttlmInf\": {\n" +
                "                \"SttlmMtd\": \"CLRG\"\n" +
                "              }\n" +
                "            },\n" +
                "            \"CdtTrfTxInf\": [\n" +
                "              {\n" +
                "                \"PmtId\": {\n" +
                "                  \"EndToEndId\": \"20240821GXSPMYKL520OQR44712044\",\n" +
                "                  \"TxId\": \"20240821GXSPMYKL52044712044\",\n" +
                "                  \"ClrSysRef\": \"001\"\n" +
                "                },\n" +
                "                \"PmtTpInf\": {\n" +
                "                  \"CtgyPurp\": {\n" +
                "                    \"Prtry\": \"EPAY\"\n" +
                "                  }\n" +
                "                },\n" +
                "                \"IntrBkSttlmAmt\": \"1\",\n" +
                "                \"IntrBkSttlmAmtCcy\": \"MYR\",\n" +
                "                \"IntrBkSttlmDt\": \"2024-08-21\",\n" +
                "                \"ChrgBr\": \"DEBT\",\n" +
                "                \"Dbtr\": {\n" +
                "                  \"Nm\": \"John Doe\"\n" +
                "                },\n" +
                "                \"DbtrAcct\": {\n" +
                "                  \"Id\": {\n" +
                "                    \"Othr\": {\n" +
                "                      \"Id\": \"9087654321\"\n" +
                "                    }\n" +
                "                  },\n" +
                "                  \"Tp\": {\n" +
                "                    \"Prtry\": \"DFLT\"\n" +
                "                  }\n" +
                "                },\n" +
                "                \"DbtrAgt\": {\n" +
                "                  \"FinInstnId\": {\n" +
                "                    \"Othr\": {\n" +
                "                      \"Id\": \"GXSPMYKL\"\n" +
                "                    }\n" +
                "                  }\n" +
                "                },\n" +
                "                \"CdtrAgt\": {\n" +
                "                  \"FinInstnId\": {\n" +
                "                    \"Othr\": {\n" +
                "                      \"Id\": \"BEEZMYNB\"\n" +
                "                    }\n" +
                "                  }\n" +
                "                },\n" +
                "                \"Cdtr\": {\n" +
                "                  \"Nm\": \"John Doe\"\n" +
                "                },\n" +
                "                \"CdtrAcct\": {\n" +
                "                  \"Id\": {\n" +
                "                    \"Othr\": {\n" +
                "                      \"Id\": \"100224082080001161906762\"\n" +
                "                    }\n" +
                "                  },\n" +
                "                  \"Tp\": {\n" +
                "                    \"Prtry\": \"DFLT\"\n" +
                "                  }\n" +
                "                },\n" +
                "                \"SplmtryData\": [\n" +
                "                  {\n" +
                "                    \"Envlp\": {\n" +
                "                      \"InstrForCdtrAgt\": {\n" +
                "                        \"RecptRef\": \"QR PAYMENT\",\n" +
                "                        \"PaymntDesc\": \"QR PAYMENT\",\n" +
                "                        \"ScndValInd\": \"N\"\n" +
                "                      },\n" +
                "                      \"InstrForDbtrAcct\": {\n" +
                "                        \"RsdntSts\": \"1\",\n" +
                "                        \"PrdTp\": \"C\",\n" +
                "                        \"ShariaCmpl\": \"N\",\n" +
                "                        \"Dtls\": \"1\"\n" +
                "                      },\n" +
                "                      \"Dbtr\": {\n" +
                "                        \"IPAddr\": \"127.0.0.1\",\n" +
                "                        \"Id\": \"********1234\"\n" +
                "                      },\n" +
                "                      \"QRTxInfo\": {\n" +
                "                        \"QRCd\": \"00020201021126560014A0000006150001010689023602241002240820800011619067625204000053034585802MY5911TIM TESTING6013Petaling Jaya623703169580000000631509071390000306524906304D333\",\n" +
                "                        \"QRCategory\": \"03\",\n" +
                "                        \"AcceptedPymtType\": [\n" +
                "                          \"04\"\n" +
                "                        ]\n" +
                "                      }\n" +
                "                    }\n" +
                "                  }\n" +
                "                ]\n" +
                "              }\n" +
                "            ]\n" +
                "          }\n" +
                "        }\n" +
                "      }\n" +
                "    }";
//        process.process((Map<String, Object>) JsonUtil.jsonStringToObject(s, Map.class));
    }

@Autowired
AmqpFacade amqpFacade;
    @SneakyThrows
    @Test
    public  void fun1(){
        String s = " {\n" +
                "      \"subject\": \"F牛肉饭邬建伟测试环境测试\",\n" +
                "      \"received_amount\": 1500,\n" +
                "      \"buyer_login\": \"oVxsc1TAGML8lMASrlCqVq6WOGHI\",\n" +
                "      \"merchant_id\": \"c685200e-9178-4e3c-917b-3e8e5000206a\",\n" +
                "      \"body\": null,\n" +
                "      \"type\": 30,\n" +
                "      \"mtime\": 1729051703917,\n" +
                "      \"extended_params\": {},\n" +
                "      \"tsn\": \"7895220220193937\",\n" +
                "      \"product_flag\": null,\n" +
                "      \"operator\": \"01\",\n" +
                "      \"extra_out_fields\": {\n" +
                "        \"combo_id\": \"1\",\n" +
                "        \"is_offset\": true,\n" +
                "        \"is_default_poi\": false,\n" +
                "        \"weixin_appid\": \"wx72534f3638c59073\",\n" +
                "        \"payments\": [\n" +
                "          {\n" +
                "            \"type\": \"WALLET_WEIXIN\",\n" +
                "            \"origin_type\": \"OTHERS\",\n" +
                "            \"amount\": 1500\n" +
                "          }\n" +
                "        ],\n" +
                "        \"wallet_account_type\": 1\n" +
                "      },\n" +
                "      \"reflect\": null,\n" +
                "      \"provider\": 1033,\n" +
                "      \"original_amount\": 1500,\n" +
                "      \"ctime\": *************,\n" +
                "      \"biz_error_code\": null,\n" +
                "      \"id\": \"t7895220220193937\",\n" +
                "      \"terminal_id\": \"f8cc4bce-19eb-4be0-9058-77187db300c0\",\n" +
                "      \"store_id\": \"a0673488-6689-42e9-85a1-213464eb03fd\",\n" +
                "      \"client_tsn\": \"0E7504DF85B0AA55B35EE01D954BAE98\",\n" +
                "      \"provider_error_info\": {\n" +
                "        \"pay\": {\n" +
                "          \"return_code\": \"SUCCESS\",\n" +
                "          \"return_msg\": \"成功\",\n" +
                "          \"result_code\": \"SUCCESS\"\n" +
                "        }\n" +
                "      },\n" +
                "      \"extra_params\": {\n" +
                "        \"barcode\": \"131235635883149070\",\n" +
                "        \"poi\": {\n" +
                "          \"longitude\": \"121.5730754\",\n" +
                "          \"latitude\": \"29.8090123\"\n" +
                "        },\n" +
                "        \"client_ip\": \"**************\",\n" +
                "        \"sqb_ip\": \"**************\",\n" +
                "        \"sqb_station\": \"460,0,22477,209814849\"\n" +
                "      },\n" +
                "      \"payway\": 3,\n" +
                "      \"version\": 2,\n" +
                "      \"finish_time\": 1729051703909,\n" +
                "      \"sub_payway\": 1,\n" +
                "      \"nfc_card\": null,\n" +
                "      \"config_snapshot\": {\n" +
                "        \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "        \"merchant_id\": \"c685200e-9178-4e3c-917b-3e8e5000206a\",\n" +
                "        \"merchant_sn\": \"1680006670274\",\n" +
                "        \"merchant_name\": \"杭州中泰餐饮服务有限公司（宁波城市学院）\",\n" +
                "        \"merchant_country\": \"CHN\",\n" +
                "        \"currency\": \"CNY\",\n" +
                "        \"longitude\": \"121.569918\",\n" +
                "        \"latitude\": \"29.808124\",\n" +
                "        \"district_code\": \"330212\",\n" +
                "        \"store_id\": \"a0673488-6689-42e9-85a1-213464eb03fd\",\n" +
                "        \"store_sn\": \"1580000007406911\",\n" +
                "        \"store_client_sn\": null,\n" +
                "        \"store_name\": \"F牛肉饭\",\n" +
                "        \"store_city\": \"宁波市\",\n" +
                "        \"extra\": {\n" +
                "          \"offsetInfo\": \"{\\\"street_address\\\":\\\"府山西路431号\\\",\\\"province\\\":\\\"浙江省\\\",\\\"city\\\":\\\"绍兴市\\\",\\\"complete_address\\\":\\\"府山西路431号\\\",\\\"district\\\":\\\"越城区\\\",\\\"latitude\\\":\\\"30.002439\\\",\\\"longitude\\\":\\\"120.570676\\\"}\"\n" +
                "        },\n" +
                "        \"terminal_id\": \"f8cc4bce-19eb-4be0-9058-77187db300c0\",\n" +
                "        \"terminal_sn\": \"100036140040622525\",\n" +
                "        \"terminal_name\": \"F牛肉饭\",\n" +
                "        \"terminal_vendor_app_appid\": \"2021022600003614\",\n" +
                "        \"terminal_category\": 102,\n" +
                "        \"clearance_provider\": 2,\n" +
                "        \"pay_status\": 1,\n" +
                "        \"common_switch\": \"00000000000200002222222222222222\",\n" +
                "        \"merchant_daily_max_credit_limit_trans\": null,\n" +
                "        \"merchant_monthly_max_credit_limit_trans\": null,\n" +
                "        \"union_over_seas_wallet_single_tran_limit\": **********,\n" +
                "        \"union_over_seas_wallet_day_tran_limit\": **********,\n" +
                "        \"payway_day_credit_limits\": null,\n" +
                "        \"payway_month_credit_limits\": null,\n" +
                "        \"is_need_refund_fee_flag\": null,\n" +
                "        \"hit_payway\": null,\n" +
                "        \"provider\": 1033,\n" +
                "        \"lkl_up_trade_params\": {\n" +
                "          \"cert_id\": \"**********\",\n" +
                "          \"fee_rate\": \"0.25\",\n" +
                "          \"sign_type\": \"SM2\",\n" +
                "          \"channel_id\": \"32631798\",\n" +
                "          \"public_key\": \"6de82fb2-497f-47a2-8df9-e9c9715fb65f\",\n" +
                "          \"weixin_appid\": \"wxd23604aba7ed0487\",\n" +
                "          \"merchant_name\": \"杭州中泰餐饮服务有限公司\",\n" +
                "          \"weixin_appkey\": \"c4b3764f913442937c5f12cc21de6acb\",\n" +
                "          \"weixin_mch_id\": \"**********\",\n" +
                "          \"up_private_key\": \"e60fb433-61db-4fa7-b871-1bc893d25e4f\",\n" +
                "          \"provider_mch_id\": \"822331058120UPY\",\n" +
                "          \"weixin_sub_appid\": \"wx72534f3638c59073\",\n" +
                "          \"weixin_sub_mch_id\": \"595280122\",\n" +
                "          \"liquidation_next_day\": true,\n" +
                "          \"weixin_sub_appsecret\": \"03dc3555893ef91f82088aedea393131111a\",\n" +
                "          \"weixin_mini_sub_appid\": \"wxccbcac9a3ece5112\",\n" +
                "          \"weixin_mini_sub_appsecret\": \"\",\n" +
                "          \"is_affiliated\": false,\n" +
                "          \"original_provider_mch_id\": \"\",\n" +
                "          \"fee_rate_tag\": {\n" +
                "            \"1\": \"1:\"\n" +
                "          },\n" +
                "          \"active\": true,\n" +
                "          \"fee\": 4\n" +
                "        },\n" +
                "        \"term_info\": {\n" +
                "          \"term_id\": \"K0885163\",\n" +
                "          \"term_type\": null,\n" +
                "          \"serial_num\": null\n" +
                "        },\n" +
                "        \"term_id\": \"K0885163\",\n" +
                "        \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
                "        \"trade_app\": \"1\"\n" +
                "      },\n" +
                "      \"deleted\": false,\n" +
                "      \"effective_amount\": 1500,\n" +
                "      \"paid_amount\": 1500,\n" +
                "      \"trade_no\": \"4200059286202410167571329267\",\n" +
                "      \"channel_finish_time\": 1729051703000,\n" +
                "      \"order_id\": \"o7895220220193937\",\n" +
                "      \"items\": null,\n" +
                "      \"order_sn\": \"7895220220193937\",\n" +
                "      \"buyer_uid\": \"oGFfks_ATIq3h3YhFtJ8kbYZRpQo\",\n" +
                "      \"status\": 0\n" +
                "    }";
//        for (int i = 0; i < 20000; i++) {
//            Map map = JsonUtil.jsonStringToObject(s, Map.class);
//            amqpFacade.notifyScoreService(new HashMap<>(), Collections.synchronizedMap(map));
//        }
//        Thread.sleep(100000l);
        Transaction transaction = AvroBeanHelper.getTransactionBeanFromMap(JsonUtil.jsonStringToObject(s, Map.class));

        System.out.println(transaction.toByteBuffer().array().length);


    }
}
