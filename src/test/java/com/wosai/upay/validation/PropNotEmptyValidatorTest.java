package com.wosai.upay.validation;

import static org.junit.Assert.assertEquals;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.validation.Payload;

import org.junit.Test;

import com.wosai.data.util.CollectionUtil;

public class PropNotEmptyValidatorTest {

    @Test
    public void test() {
        PropNotEmptyValidator v = new PropNotEmptyValidator();
        v.initialize(new PropNotEmpty() {
            @Override
            public Class<? extends Annotation> annotationType() {
                return null;
            }

            @Override
            public String message() {
                return null;
            }

            @Override
            public Class<?>[] groups() {
                return null;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return null;
            }

            @Override
            public String value() {
                return "test";
            }
            
        });
        
        Map<String, Object> info = new HashMap<>();
        // 字符串校验
        info.put("test", "test");
        assertEquals(true, v.isValid(info, null));
        info.put("test", "");
        assertEquals(false, v.isValid(info, null));
        
        // 集合校验
        info.put("test", Arrays.asList("a", "b", "c"));
        assertEquals(true, v.isValid(info, null));
        info.put("test", new ArrayList<>());
        assertEquals(false, v.isValid(info, null));

        // map校验
        info.put("test", CollectionUtil.hashMap("b","b"));
        assertEquals(true, v.isValid(info, null));
        info.put("test", new HashMap<>());
        assertEquals(false, v.isValid(info, null));
        
        // 数组校验
        info.put("test", new String[] {"a", "b", "c"});
        assertEquals(true, v.isValid(info, null));
        info.put("test", new String[] {});
        assertEquals(false, v.isValid(info, null));

        // 未知类型校验
        info.put("test", new Integer(10));
        assertEquals(false, v.isValid(info, null));
        
        // null值校验
        info.put("test", null);
        assertEquals(false, v.isValid(info, null));


    }

}
