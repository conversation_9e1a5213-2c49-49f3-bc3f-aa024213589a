package com.wosai.upay.validation;

import static org.junit.Assert.*;

import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import javax.validation.Payload;

import org.junit.Test;

public class PropPatternValidatorTest {

    @Test
    public void test() {
        boolean nullable = ThreadLocalRandom.current().nextBoolean();
        PropPatternValidator v = new PropPatternValidator();
        v.initialize(new PropPattern() {
            
            @Override
            public Class<? extends Annotation> annotationType() {
                // TODO Auto-generated method stub
                return null;
            }
            
            @Override
            public String value() {
                return "test";
            }
            
            @Override
            public String regex() {
                return "[1-9]\\d{0,9}";
            }
            
            @Override
            public Class<? extends Payload>[] payload() {
                return null;
            }
            
            @Override
            public boolean nullable() {
                return nullable;
            }
            
            @Override
            public String message() {
                return null;
            }
            
            @Override
            public Class<?>[] groups() {
                return null;
            }
        });
        
        Map<String, Object> info = new HashMap<>();
        assertEquals(nullable, v.isValid(info, null));
        
        info.put("test", "1");
        assertEquals(true, v.isValid(info, null));
        
        info.put("test", "abc");
        assertEquals(false, v.isValid(info, null));

        info.put("test", 1);
        assertEquals(false, v.isValid(info, null));
    }

}
