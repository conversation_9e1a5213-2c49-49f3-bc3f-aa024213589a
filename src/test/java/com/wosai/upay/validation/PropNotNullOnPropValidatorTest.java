package com.wosai.upay.validation;

import static org.junit.Assert.assertEquals;

import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.Map;

import javax.validation.Payload;

import org.junit.Test;

public class PropNotNullOnPropValidatorTest {

    @Test
    public void test() {
        PropNotNullOnPropValidator v = new PropNotNullOnPropValidator();
        v.initialize(new PropNotNullOnProp() {
            
            @Override
            public Class<? extends Annotation> annotationType() {
                return null;
            }
            
            @Override
            public String value2() {
                return "test2";
            }
            
            @Override
            public String value() {
                return "test1";
            }
            
            @Override
            public Class<? extends Payload>[] payload() {
                return null;
            }
            
            @Override
            public String message() {
                return null;
            }
            
            @Override
            public Class<?>[] groups() {
                return null;
            }
        });
        
        Map<String, Object> info = new HashMap<>();
        assertEquals(true, v.isValid(info, null));
        
        info.put("test1", "1");
        assertEquals(true, v.isValid(info, null));
        
        info.clear();
        info.put("test2", "2");
        assertEquals(false, v.isValid(info, null));

        info.put("test1", "1");
        info.put("test2", "2");
        assertEquals(true, v.isValid(info, null));

    }

}
