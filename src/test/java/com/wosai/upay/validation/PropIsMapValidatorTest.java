package com.wosai.upay.validation;

import static org.junit.Assert.*;

import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.concurrent.ThreadLocalRandom;

import javax.validation.Payload;

import org.junit.Test;

import com.wosai.data.util.CollectionUtil;

public class PropIsMapValidatorTest {

    @Test
    public void test() {
        boolean nullable = ThreadLocalRandom.current().nextBoolean();
        PropIsMapValidator v = new PropIsMapValidator();
        v.initialize(new PropIsMap() {

            @Override
            public Class<? extends Annotation> annotationType() {
                // TODO Auto-generated method stub
                return null;
            }

            @Override
            public String message() {
                return null;
            }

            @Override
            public Class<?>[] groups() {
                // TODO Auto-generated method stub
                return null;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                // TODO Auto-generated method stub
                return null;
            }

            @Override
            public String value() {
                return "test";
            }

            @Override
            public boolean nullable() {
                return nullable;
            }

            @Override
            public boolean emptyable() {
                return false;
            }
            
        });
        
        assertEquals(nullable, v.isValid(new HashMap<>(), null));
        assertEquals(false, v.isValid(CollectionUtil.hashMap("test", new HashMap<>()), null));
        assertEquals(true, v.isValid(CollectionUtil.hashMap("test", CollectionUtil.hashMap("a","a")), null));
        
        // 类型不是map
        assertEquals(false, v.isValid(CollectionUtil.hashMap("test","test"), null));


    }

}
