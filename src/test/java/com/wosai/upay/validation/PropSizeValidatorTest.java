package com.wosai.upay.validation;

import static org.junit.Assert.assertEquals;

import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.validation.Payload;

import org.junit.Test;

import com.wosai.data.util.CollectionUtil;

public class PropSizeValidatorTest {

    @Test
    public void test() {
        PropSizeValidator v = new PropSizeValidator();
        v.initialize(new PropSize() {

            @Override
            public Class<? extends Annotation> annotationType() {
                return null;
            }

            @Override
            public String message() {
                return null;
            }

            @Override
            public Class<?>[] groups() {
                return null;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return null;
            }

            @Override
            public String value() {
                return "test";
            }

            @Override
            public int min() {
                return 0;
            }

            @Override
            public int max() {
                return 3;
            }

            @Override
            public boolean nullable() {
                return false;
            }
            
        });
        
        Map<String, Object> info = new HashMap<>();
        // 字符串校验
        info.put("test", "tes");
        assertEquals(true, v.isValid(info, null));
        info.put("test", "test");
        assertEquals(false, v.isValid(info, null));
        
        // 集合校验
        info.put("test", Arrays.asList("a", "b", "c"));
        assertEquals(true, v.isValid(info, null));
        info.put("test", Arrays.asList("a", "b", "c", "d"));
        assertEquals(false, v.isValid(info, null));


        // map校验
        info.put("test", CollectionUtil.hashMap("b","b"));
        assertEquals(true, v.isValid(info, null));
        info.put("test", CollectionUtil.hashMap("b","b", "c", "c", "d", "d", "e", "e"));
        assertEquals(false, v.isValid(info, null));

        // 数组校验
        info.put("test", new String[] {"a", "b", "c"});
        assertEquals(true, v.isValid(info, null));
        info.put("test", new String[] {"a", "b", "c", "d"});
        assertEquals(false, v.isValid(info, null));


        // 未知类型校验
        info.put("test", new Integer(10));
        assertEquals(false, v.isValid(info, null));
        
        // null值校验
        info.put("test", null);
        assertEquals(false, v.isValid(info, null));
    }

}
