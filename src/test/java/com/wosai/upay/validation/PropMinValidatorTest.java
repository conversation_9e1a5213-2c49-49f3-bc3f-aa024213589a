package com.wosai.upay.validation;

import static org.junit.Assert.*;

import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.validation.Payload;

import org.junit.Test;

import com.wosai.data.util.CollectionUtil;

public class PropMinValidatorTest {

    @Test
    public void test() {
        PropMinValidator v = new PropMinValidator();
        v.initialize(new PropMin() {

            @Override
            public Class<? extends Annotation> annotationType() {
                return null;
            }

            @Override
            public String message() {
                return null;
            }

            @Override
            public Class<?>[] groups() {
                return null;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return null;
            }

            @Override
            public String value() {
                return "test";
            }

            @Override
            public long min() {
                return 10;
            }

            @Override
            public boolean nullable() {
                return false;
            }});
        
        Map<String, Object> info = new HashMap<>();
        // 字符串校验
        info.put("test", "9");
        assertEquals(false, v.isValid(info, null));
        info.put("test", "10");
        assertEquals(true, v.isValid(info, null));

        // 集合校验
        info.put("test", Arrays.asList("a", "b", "c", "d"));
        assertEquals(false, v.isValid(info, null));

        // map校验
        info.put("test", CollectionUtil.hashMap("b","b", "c", "c", "d", "d", "e", "e"));
        assertEquals(false, v.isValid(info, null));

        // 数组校验
        info.put("test", new String[] {"a", "b", "c", "d"});
        assertEquals(false, v.isValid(info, null));

        // 数字类型校验
        info.put("test", 9);
        assertEquals(false, v.isValid(info, null));
        info.put("test", 11);
        assertEquals(true, v.isValid(info, null));

        
        // null值校验
        info.put("test", null);
        assertEquals(false, v.isValid(info, null));
    }

}
