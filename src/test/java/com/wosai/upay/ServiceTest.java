package com.wosai.upay;

import java.util.HashSet;
import java.util.concurrent.ConcurrentLinkedQueue;
import com.wosai.upay.service.ExternalServiceFacade;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.wosai.upay.service.TsnGenerator;
@ContextConfiguration(locations = {"classpath:spring/business-config.xml", "classpath:spring/tools-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class ServiceTest {
    @Autowired
    TsnGenerator generator;
    @Autowired
    ExternalServiceFacade externalServiceFacade;

    @BeforeClass
    public static void setup() {

    }

    @AfterClass
    public static void tearDown() {

    }

    @Test
    public void testTsnGenerator() {
        final ConcurrentLinkedQueue<String> observedSns = new ConcurrentLinkedQueue<String>();

        Runnable r = new Runnable() {

            @Override
            public void run() {
                for(int i=0; i<10; ++i) {
                    String sn = generator.nextSn();
                    observedSns.add(sn);
                }
            }
        };

        Thread[] threads = new Thread[3];

        for (int i=0; i<3; ++i) {
            threads[i] = new Thread(r);
            threads[i].start();
        }
        for(Thread t: threads) {
            try {
                t.join();
            }
            catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        HashSet<String> distinctSns = new HashSet<String>();
        for (String sn: observedSns) {
            distinctSns.add(sn);

        }
        Assert.assertEquals(observedSns.size(), distinctSns.size());
    }
}
