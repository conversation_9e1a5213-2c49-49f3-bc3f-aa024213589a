package com.wosai.upay;

import java.io.UnsupportedEncodingException;
import java.util.concurrent.atomic.AtomicInteger;

import com.wosai.mpay.util.Base64;
import org.bouncycastle.crypto.RuntimeCryptoException;
import org.junit.Test;

import com.wosai.pantheon.util.RetryUtil;
import com.wosai.pantheon.util.RetryUtil.TimingStrategy;

public class RetryTest {

    @Test
    public void testRetry() {
        TimingStrategy.Builder builder = new TimingStrategy.Builder()
                .setRetry(3, 1000, 1.0)
                .setJitter(true, 10);
        AtomicInteger idx = new AtomicInteger();
        RetryUtil<Boolean> retryUtil = new RetryUtil<Boolean>()
                .retry(builder.build())
                .method(() -> {
                    System.out.println("in method，idx=" + idx.getAndIncrement());
                    throw new RuntimeCryptoException("test");
                })
                .on(throwable -> !com.wosai.profit.sharing.constant.ErrorMessageConstant.UPAY_TRANSACTION_NOT_NEED_SHARING.equals(throwable.getMessage()))
                .until(succeeded -> succeeded != null && succeeded);
        retryUtil.execute();
        assert idx.get() != 3;
        
        
        AtomicInteger idx2 = new AtomicInteger();
        Exception e = null;
        try {
            retryUtil = new RetryUtil<Boolean>()
                    .retry(builder.build())
                    .method(() -> {
                        System.out.println("in method，idx=" + idx2.getAndIncrement());
                        throw new RuntimeCryptoException(com.wosai.profit.sharing.constant.ErrorMessageConstant.UPAY_TRANSACTION_NOT_NEED_SHARING);
                    })
                    .on(throwable -> !com.wosai.profit.sharing.constant.ErrorMessageConstant.UPAY_TRANSACTION_NOT_NEED_SHARING.equals(throwable.getMessage()))
                    .until(succeeded -> succeeded != null && succeeded);
            retryUtil.execute();
        }catch (Exception ex) {
            e = ex;
        }
        assert idx2.get() != 0 || e != null;
    }
}
