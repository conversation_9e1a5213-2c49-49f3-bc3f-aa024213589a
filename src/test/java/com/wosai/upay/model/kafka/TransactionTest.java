package com.wosai.upay.model.kafka;

import java.util.Map;

import org.junit.Test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.util.AvroBeanHelper;

public class TransactionTest {

    @Test
    public void test() throws JsonProcessingException {
        Map<String, Object> transactionMap = CollectionUtil.hashMap(null, null);
        Transaction transaction = AvroBeanHelper.getTransactionBeanFromMap(transactionMap);
    }

}
