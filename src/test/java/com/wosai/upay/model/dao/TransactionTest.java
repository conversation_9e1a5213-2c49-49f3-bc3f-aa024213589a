package com.wosai.upay.model.dao;

import static org.junit.Assert.*;

import org.junit.Test;

public class TransactionTest {

    @Test
    public void test() {
        new Transaction();
        assertEquals(Transaction.ProductFlag.APP, Transaction.ProductFlag.fromCode(Transaction.PRODUCT_APP));
        assertEquals(Transaction.ProductFlag.APP, Transaction.ProductFlag.fromCode(999));
        
        int [] successStatus = {Transaction.STATUS_CREATED, Transaction.STATUS_SUCCESS, Transaction.STATUS_IN_PROG, Transaction.STATUS_PRE_SUCCESS};
        
        for (int i = 0; i < successStatus.length; i++) {
            assertEquals(true, Transaction.notFailed(successStatus[i]));
        }
        assertEquals(false, Transaction.notFailed(Transaction.STATUS_FAIL_IO_1));
    }

}
