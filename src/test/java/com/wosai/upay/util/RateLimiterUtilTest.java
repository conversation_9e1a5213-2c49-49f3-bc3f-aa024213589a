package com.wosai.upay.util;

import org.junit.Test;

import com.wosai.upay.workflow.MpayServiceProvider;

public class RateLimiterUtilTest {

    @Test
    public void test() {
        RateLimiterUtil.init();
        RateLimiterUtil.verification(SupportUtil.TERMINAL_SN, MpayServiceProvider.OP_CANCEL);
        RateLimiterUtil.verification(SupportUtil.TERMINAL_SN, MpayServiceProvider.OP_CANCEL, false);
        RateLimiterUtil.getProviderRateLimiter();
        RateLimiterUtil.verifyGenSnRateLimit();
    }

}
