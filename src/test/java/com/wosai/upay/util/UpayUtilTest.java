package com.wosai.upay.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.wosai.upay.exception.InvalidBarcodeException;
import com.wosai.upay.model.dao.Order;

public class UpayUtilTest {

    @Test
    public void test() {
        assertEquals("商家扫码", UpayUtil.getSubPaywayName(Order.SUB_PAYWAY_BARCODE));
        assertEquals("二维码", UpayUtil.getSubPaywayName(Order.SUB_PAYWAY_QRCODE));
        assertEquals("顾客扫码", UpayUtil.getSubPaywayName(Order.SUB_PAYWAY_WAP));
        assertEquals("小程序", UpayUtil.getSubPaywayName(Order.SUB_PAYWAY_MINI));
        assertEquals("APP支付", UpayUtil.getSubPaywayName(Order.SUB_PAYWAY_APP));
        assertEquals("H5支付", UpayUtil.getSubPaywayName(Order.SUB_PAYWAY_H5));
    }

    @Test
    public void testDcepResolvePayway() {
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100101111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100201111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100301111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100401111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100501111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100601111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100701111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100801111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100901111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0101001111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0101101111111111111", "cc"));
        UpayUtil.changeDcepPattern("010(10|0[1-9])\\d{14}");
        Exception ex = null;
        try {
            UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0101101111111111111", "cc");
        }catch (InvalidBarcodeException e) {
            ex = e;
        }
        assertTrue(ex != null);
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100101111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100201111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100301111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100401111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100501111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100601111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100701111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100801111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100901111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0101001111111111111", "cc"));
        assertTrue(Order.PAYWAY_DCEP == UpayUtil.resolvePayway(Order.PAYWAY_DCEP + "", "0100514826029250607", "cc"));

    }
}
