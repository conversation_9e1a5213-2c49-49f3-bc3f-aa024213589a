package com.wosai.upay.util;

import com.google.common.collect.Lists;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.service.ExternalServiceFacade.TcpPayResult.TcpPay;
import com.wosai.upay.service.ExternalServiceFacade.TcpRefundResult.TcpRefund;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class PaymentUtilTest {

    @Test
    public void test_buildTransactionPaymentsForRefundOrCancel() {
        ExternalServiceFacade.TcpRefundResult tcpRefundResult = new ExternalServiceFacade.TcpRefundResult(false, 30);
        tcpRefundResult.setHongbaoWosaiAmount(1L);
        TcpRefund hongbaoWosai = new TcpRefund(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpRefundResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpRefundResult.setHongbaoWosaiMchAmount(2L);
        TcpRefund hongbaoWosaiMch = new TcpRefund(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpRefundResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpRefundResult.setDiscountWosaiAmount(3L);
        TcpRefund discountWosai = new TcpRefund(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpRefundResult.getDiscountWosai().add(discountWosai);
        
        tcpRefundResult.setDiscountWosaiMchAmount(4L);
        TcpRefund discountWosaiMch = new TcpRefund(4L, "wosai-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpRefundResult.getDiscountWosaiMch().add(discountWosaiMch);


        List<Map<String, Object>> transactionPayments = PaymentUtil.buildTransactionPaymentsForRefundOrCancel(tcpRefundResult);
        
        assertNotNull(transactionPayments);
        assertEquals(4, transactionPayments.size());
        
        int matchCnt = 4;
        for (Map<String, Object> map : transactionPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            TcpRefund refund = null;
            switch(type){
                case Payment.TYPE_HONGBAO_WOSAI:
                    refund = hongbaoWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_HONGBAO_WOSAI_MCH:
                    refund = hongbaoWosaiMch;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI:
                    refund = discountWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                    refund = discountWosaiMch;
                    matchCnt--;
                    break;
                default:
                    break;
            }
            if(null == refund) {
                continue;
            }
            Long amount = MapUtil.getLong(map, Payment.AMOUNT);
            String source = MapUtil.getString(map, Payment.SOURCE);
            String originType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
            assertEquals(amount, refund.getAmount());
            assertEquals(source, refund.getSource());
            assertEquals(originType, refund.getOriginalType());
        }
        assertEquals(0, matchCnt);
    }

    @Test
    public void test_buildOrderPaymentsForPay() {
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosai = new TcpPay(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpPayResult.setHongbaoWosaiMchAmount(2L);
        TcpPay hongbaoWosaiMch = new TcpPay(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpPayResult.setDiscountWosaiAmount(3L);
        TcpPay discountWosai = new TcpPay(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpPayResult.getDiscountWosai().add(discountWosai);
        
        tcpPayResult.setDiscountWosaiMchAmount(4L);
        TcpPay discountWosaiMch = new TcpPay(4L, "wosai-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpPayResult.getDiscountWosaiMch().add(discountWosaiMch);


        List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult));
        
        assertNotNull(orderPayments);
        assertEquals(4, orderPayments.size());
        
        int matchCnt = 4;
        for (Map<String, Object> map : orderPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            TcpPay pay = null;
            switch(type){
                case Payment.TYPE_HONGBAO_WOSAI:
                    pay = hongbaoWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_HONGBAO_WOSAI_MCH:
                    pay = hongbaoWosaiMch;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI:
                    pay = discountWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                    pay = discountWosaiMch;
                    matchCnt--;
                    break;
                default:
                    break;
            }
            if(null == pay) {
                continue;
            }
            Long amountTotal = MapUtil.getLong(map, Payment.AMOUNT_TOTAL);
            Long netAmount = MapUtil.getLong(map, Payment.NET_AMOUNT);
            String source = MapUtil.getString(map, Payment.SOURCE);
            String originType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
            assertEquals(amountTotal, pay.getAmount());
            assertEquals(netAmount, pay.getAmount());
            assertEquals(source, pay.getSource());
            assertEquals(originType, pay.getOriginalType());
        }
        assertEquals(0, matchCnt);
    }

    
    @Test
    public void test_buildTransactionPaymentsForPay() {
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosai = new TcpPay(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpPayResult.setHongbaoWosaiMchAmount(2L);
        TcpPay hongbaoWosaiMch = new TcpPay(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpPayResult.setDiscountWosaiAmount(3L);
        TcpPay discountWosai = new TcpPay(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpPayResult.getDiscountWosai().add(discountWosai);
        
        tcpPayResult.setDiscountWosaiMchAmount(4L);
        TcpPay discountWosaiMch = new TcpPay(4L, "wosai-mch-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpPayResult.getDiscountWosaiMch().add(discountWosaiMch);


        List<Map<String, Object>> transactionPayments = PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult);
        
        assertNotNull(transactionPayments);
        assertEquals(4, transactionPayments.size());
        
        int matchCnt = 4;
        for (Map<String, Object> map : transactionPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            TcpPay pay = null;
            switch(type){
                case Payment.TYPE_HONGBAO_WOSAI:
                    pay = hongbaoWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_HONGBAO_WOSAI_MCH:
                    pay = hongbaoWosaiMch;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI:
                    pay = discountWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                    pay = discountWosaiMch;
                    matchCnt--;
                    break;
                default:
                    break;
            }
            if(null == pay) {
                continue;
            }
            Long amount = MapUtil.getLong(map, Payment.AMOUNT);
            String source = MapUtil.getString(map, Payment.SOURCE);
            String originType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
            assertEquals(amount, pay.getAmount());
            assertEquals(source, pay.getSource());
            assertEquals(originType, pay.getOriginalType());
        }
        assertEquals(0, matchCnt);
    }

    
    @Test
    public void test_updateOrderPaymentsNetAmountForRefundSuccess() {
        // 支付
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosaiPay = new TcpPay(1L, "wosai");
        hongbaoWosaiPay.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosaiPay);
        
        tcpPayResult.setHongbaoWosaiMchAmount(2L);
        TcpPay hongbaoWosaiMchPay = new TcpPay(2L, "wosai-mch");
        hongbaoWosaiMchPay.setSource("wosai_2");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMchPay);
        
        tcpPayResult.setDiscountWosaiAmount(3L);
        TcpPay discountWosaiPay = new TcpPay(3L, "wosai-discount");
        discountWosaiPay.setSource("wosai_3");
        tcpPayResult.getDiscountWosai().add(discountWosaiPay);
        
        tcpPayResult.setDiscountWosaiMchAmount(4L);
        TcpPay discountWosaiMchPay = new TcpPay(4L, "wosai-discount");
        discountWosaiMchPay.setSource("wosai_4");
        tcpPayResult.getDiscountWosaiMch().add(discountWosaiMchPay);
        List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult));
        
        // 退款
        ExternalServiceFacade.TcpRefundResult tcpRefundResult = new ExternalServiceFacade.TcpRefundResult(false, 30);
        tcpRefundResult.setHongbaoWosaiAmount(1L);
        TcpRefund hongbaoWosai = new TcpRefund(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpRefundResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpRefundResult.setHongbaoWosaiMchAmount(2L);
        TcpRefund hongbaoWosaiMch = new TcpRefund(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpRefundResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpRefundResult.setDiscountWosaiAmount(3L);
        TcpRefund discountWosai = new TcpRefund(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpRefundResult.getDiscountWosai().add(discountWosai);
        
        tcpRefundResult.setDiscountWosaiMchAmount(4L);
        TcpRefund discountWosaiMch = new TcpRefund(4L, "wosai-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpRefundResult.getDiscountWosaiMch().add(discountWosaiMch);
        List<Map<String, Object>> transactionPayments = PaymentUtil.buildTransactionPaymentsForRefundOrCancel(tcpRefundResult);
        
        PaymentUtil.updateOrderPaymentsNetAmountForRefundSuccess(orderPayments, transactionPayments);
        for (Map<String, Object> map : orderPayments) {
            assertEquals(0L, MapUtil.getLongValue(map, Payment.NET_AMOUNT));
        }
    }

    @Test
    public void test_updateOrderPaymentsNetAmountForRefundSuccessWhenSameType() {
        // 支付
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosaiPay = new TcpPay(1L, "wosai");
        hongbaoWosaiPay.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosaiPay);

        tcpPayResult.setHongbaoWosaiMchAmount(7L);
        TcpPay hongbaoWosaiMchPay1 = new TcpPay(3L, "wosai-mch");
        hongbaoWosaiMchPay1.setSource("wosai_21");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMchPay1);

        TcpPay hongbaoWosaiMchPay2 = new TcpPay(4L, "wosai-mch");
        hongbaoWosaiMchPay2.setSource("wosai_22");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMchPay2);


        List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult));

        // 退款
        ExternalServiceFacade.TcpRefundResult tcpRefundResult = new ExternalServiceFacade.TcpRefundResult(false, 30);
        tcpRefundResult.setHongbaoWosaiAmount(1L);
        TcpRefund hongbaoWosai = new TcpRefund(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpRefundResult.getHongbaoWosai().add(hongbaoWosai);

        tcpRefundResult.setHongbaoWosaiMchAmount(5L);
        TcpRefund hongbaoWosaiMch = new TcpRefund(5L, "wosai-mch");
        tcpRefundResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);

        List<Map<String, Object>> transactionPayments = PaymentUtil.buildTransactionPaymentsForRefundOrCancel(tcpRefundResult);

        PaymentUtil.updateOrderPaymentsNetAmountForRefundSuccess(orderPayments, transactionPayments);
        long hongboWosaiMchNetAmount = 0l;
        for (Map<String, Object> map : orderPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            if(Payment.TYPE_HONGBAO_WOSAI.equals(type)){
                assertEquals(0L, MapUtil.getLongValue(map, Payment.NET_AMOUNT));
            }else if(Payment.TYPE_HONGBAO_WOSAI_MCH.equals(type)){
                hongboWosaiMchNetAmount = hongboWosaiMchNetAmount + MapUtil.getLongValue(map, Payment.NET_AMOUNT);
            }
        }
        assertEquals(2L, hongboWosaiMchNetAmount);
    }

    @Test
    public void test_updateOrderPaymentsNetAmountForRevokeRefundSuccess() {
        // 支付
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosaiPay = new TcpPay(1L, "wosai");
        hongbaoWosaiPay.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosaiPay);
        
        tcpPayResult.setHongbaoWosaiMchAmount(2L);
        TcpPay hongbaoWosaiMchPay = new TcpPay(2L, "wosai-mch");
        hongbaoWosaiMchPay.setSource("wosai_2");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMchPay);
        
        tcpPayResult.setDiscountWosaiAmount(3L);
        TcpPay discountWosaiPay = new TcpPay(3L, "wosai-discount");
        discountWosaiPay.setSource("wosai_3");
        tcpPayResult.getDiscountWosai().add(discountWosaiPay);
        
        tcpPayResult.setDiscountWosaiMchAmount(4L);
        TcpPay discountWosaiMchPay = new TcpPay(4L, "wosai-discount");
        discountWosaiMchPay.setSource("wosai_4");
        tcpPayResult.getDiscountWosaiMch().add(discountWosaiMchPay);
        List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult));
        
        // 退款
        ExternalServiceFacade.TcpRefundResult tcpRefundResult = new ExternalServiceFacade.TcpRefundResult(false, 30);
        tcpRefundResult.setHongbaoWosaiAmount(1L);
        TcpRefund hongbaoWosai = new TcpRefund(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpRefundResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpRefundResult.setHongbaoWosaiMchAmount(2L);
        TcpRefund hongbaoWosaiMch = new TcpRefund(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpRefundResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpRefundResult.setDiscountWosaiAmount(3L);
        TcpRefund discountWosai = new TcpRefund(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpRefundResult.getDiscountWosai().add(discountWosai);
        
        tcpRefundResult.setDiscountWosaiMchAmount(4L);
        TcpRefund discountWosaiMch = new TcpRefund(4L, "wosai-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpRefundResult.getDiscountWosaiMch().add(discountWosaiMch);
        List<Map<String, Object>> transactionPayments = PaymentUtil.buildTransactionPaymentsForRefundOrCancel(tcpRefundResult);
        
        PaymentUtil.updateOrderPaymentsNetAmountForRefundSuccess(orderPayments, transactionPayments);
        for (Map<String, Object> map : orderPayments) {
            assertEquals(0L, MapUtil.getLongValue(map, Payment.NET_AMOUNT));
        }
        
        // 退款撤销
        PaymentUtil.updateOrderPaymentsNetAmountForRevokeRefundSuccess(orderPayments, transactionPayments);
        int matchCnt = 4;
        for (Map<String, Object> map : orderPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            TcpPay pay = null;
            switch(type){
                case Payment.TYPE_HONGBAO_WOSAI:
                    pay = hongbaoWosaiPay;
                    matchCnt--;
                    break;
                case Payment.TYPE_HONGBAO_WOSAI_MCH:
                    pay = hongbaoWosaiMchPay;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI:
                    pay = discountWosaiPay;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                    pay = discountWosaiMchPay;
                    matchCnt--;
                    break;
                default:
                    break;
            }
            if(null == pay) {
                continue;
            }
            Long amountTotal = MapUtil.getLong(map, Payment.AMOUNT_TOTAL);
            Long netAmount = MapUtil.getLong(map, Payment.NET_AMOUNT);
            String source = MapUtil.getString(map, Payment.SOURCE);
            String originType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
            assertEquals(amountTotal, pay.getAmount());
            assertEquals(netAmount, pay.getAmount());
            assertEquals(source, pay.getSource());
            assertEquals(originType, pay.getOriginalType());
        }
        assertEquals(0, matchCnt);
    }
    
    @Test
    public void test_updateOrderPaymentsNetAmountToZero() {
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosai = new TcpPay(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpPayResult.setHongbaoWosaiMchAmount(2L);
        TcpPay hongbaoWosaiMch = new TcpPay(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpPayResult.setDiscountWosaiAmount(3L);
        TcpPay discountWosai = new TcpPay(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpPayResult.getDiscountWosai().add(discountWosai);
        
        tcpPayResult.setDiscountWosaiMchAmount(4L);
        TcpPay discountWosaiMch = new TcpPay(4L, "wosai-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpPayResult.getDiscountWosaiMch().add(discountWosaiMch);


        List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult));
        assertNotNull(orderPayments);
        assertEquals(4, orderPayments.size());
        PaymentUtil.updateOrderPaymentsNetAmountToZero(orderPayments);
        
        int matchCnt = 4;
        for (Map<String, Object> map : orderPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            TcpPay pay = null;
            switch(type){
                case Payment.TYPE_HONGBAO_WOSAI:
                    pay = hongbaoWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_HONGBAO_WOSAI_MCH:
                    pay = hongbaoWosaiMch;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI:
                    pay = discountWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                    pay = discountWosaiMch;
                    matchCnt--;
                    break;
                default:
                    break;
            }
            if(null == pay) {
                continue;
            }
            Long amountTotal = MapUtil.getLong(map, Payment.AMOUNT_TOTAL);
            long netAmount = MapUtil.getLongValue(map, Payment.NET_AMOUNT);
            String source = MapUtil.getString(map, Payment.SOURCE);
            String originType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
            assertEquals(amountTotal, pay.getAmount());
            assertEquals(0L, netAmount);
            assertEquals(source, pay.getSource());
            assertEquals(originType, pay.getOriginalType());
        }
        assertEquals(0, matchCnt);
    }
    
    @Test
    public void test_updateOrderPaymentsNetAmountToAmountTotal() {
        ExternalServiceFacade.TcpPayResult tcpPayResult = new ExternalServiceFacade.TcpPayResult(false, 30);
        tcpPayResult.setHongbaoWosaiAmount(1L);
        TcpPay hongbaoWosai = new TcpPay(1L, "wosai");
        hongbaoWosai.setSource("wosai_1");
        tcpPayResult.getHongbaoWosai().add(hongbaoWosai);
        
        tcpPayResult.setHongbaoWosaiMchAmount(2L);
        TcpPay hongbaoWosaiMch = new TcpPay(2L, "wosai-mch");
        hongbaoWosai.setSource("wosai_2");
        tcpPayResult.getHongbaoWosaiMch().add(hongbaoWosaiMch);
        
        tcpPayResult.setDiscountWosaiAmount(3L);
        TcpPay discountWosai = new TcpPay(3L, "wosai-discount");
        discountWosai.setSource("wosai_3");
        tcpPayResult.getDiscountWosai().add(discountWosai);
        
        tcpPayResult.setDiscountWosaiMchAmount(4L);
        TcpPay discountWosaiMch = new TcpPay(4L, "wosai-discount");
        discountWosaiMch.setSource("wosai_4");
        tcpPayResult.getDiscountWosaiMch().add(discountWosaiMch);


        List<Map<String, Object>> orderPayments = PaymentUtil.buildOrderPaymentsForPay(PaymentUtil.buildTransactionPaymentsForPay(tcpPayResult));
        assertNotNull(orderPayments);
        assertEquals(4, orderPayments.size());
        PaymentUtil.updateOrderPaymentsNetAmountToZero(orderPayments);
        
        int matchCnt = 4;
        for (Map<String, Object> map : orderPayments) {
            String type = MapUtil.getString(map, Payment.TYPE);
            TcpPay pay = null;
            switch(type){
                case Payment.TYPE_HONGBAO_WOSAI:
                    pay = hongbaoWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_HONGBAO_WOSAI_MCH:
                    pay = hongbaoWosaiMch;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI:
                    pay = discountWosai;
                    matchCnt--;
                    break;
                case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                    pay = discountWosaiMch;
                    matchCnt--;
                    break;
                default:
                    break;
            }
            if(null == pay) {
                continue;
            }
            Long amountTotal = MapUtil.getLong(map, Payment.AMOUNT_TOTAL);
            long netAmount = MapUtil.getLongValue(map, Payment.NET_AMOUNT);
            String source = MapUtil.getString(map, Payment.SOURCE);
            String originType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
            assertEquals(amountTotal, pay.getAmount());
            assertEquals(0L, netAmount);
            assertEquals(source, pay.getSource());
            assertEquals(originType, pay.getOriginalType());
        }
        assertEquals(0, matchCnt);
        
        PaymentUtil.updateOrderPaymentsNetAmountToAmountTotal(orderPayments);
        for (Map<String, Object> map : orderPayments) {
            long amountTotal = MapUtil.getLong(map, Payment.AMOUNT_TOTAL);
            long netAmount = MapUtil.getLongValue(map, Payment.NET_AMOUNT);
            assertEquals(amountTotal, netAmount);
        }
    }
    
    
    @Test
    public void test_isNewTradeCoprocessorOrder() {
        Map<String, Object> order = new ConcurrentHashMap<String, Object>();
        assertEquals(false, PaymentUtil.isNewTradeCoprocessorOrder(order));

        Map<String, Object> items = new ConcurrentHashMap<String, Object>();
        order.put(Order.ITEMS, items);
        assertEquals(false, PaymentUtil.isNewTradeCoprocessorOrder(order));

        items.put(Order.PAYMENTS, new ArrayList<>());
        assertEquals(true, PaymentUtil.isNewTradeCoprocessorOrder(order));
    }

    @Test
    public void updateSqbDepositPaymentsAmountForRefundSuccess() {
        List<Map<String, Object>> freezePayments = buildFreezePayments();
        List<Map<String, Object>> refundPayments = buildRefundPayments();
        PaymentUtil.updateSqbDepositPaymentsAmountForRefundSuccess(freezePayments, refundPayments);
        System.out.println(freezePayments);



    }

    private List<Map<String, Object>> buildFreezePayments() {
        List<Map<String, Object>> result = Lists.newArrayList();
        result.add(MapUtil.hashMap("type", Payment.TYPE_CARD_CHANNEL_MCH_PRE, "origin_type","PCARD", "amount",2));
        result.add(MapUtil.hashMap("type", Payment.TYPE_HONGBAO_CHANNEL_MCH, "origin_type","MDISCOUNT", "amount",21));
        result.add(MapUtil.hashMap("type", Payment.TYPE_DISCOUNT_CHANNEL_MCH, "origin_type","MDISCOUNT", "amount",51));
        return result;
    }
    private List<Map<String, Object>> buildRefundPayments() {
        List<Map<String, Object>> result = Lists.newArrayList();
        result.add(MapUtil.hashMap("type", Payment.TYPE_CARD_CHANNEL_MCH_PRE, "origin_type","PCARD", "amount",2));
        result.add(MapUtil.hashMap("type", Payment.TYPE_DISCOUNT_CHANNEL_MCH, "origin_type","MDISCOUNT", "amount",20));
        result.add(MapUtil.hashMap("type", Payment.TYPE_DISCOUNT_CHANNEL_MCH, "origin_type","MDISCOUNT", "amount",50));
        return result;
    }
}
