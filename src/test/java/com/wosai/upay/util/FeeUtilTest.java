package com.wosai.upay.util;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;
import org.junit.Test;

import java.util.Map;

import static com.wosai.upay.util.FeeUtil.ZERO_FEE_RATE;
import static org.junit.Assert.assertEquals;

public class FeeUtilTest {

    @Test
    public void test() {
        assertEquals(0, FeeUtil.applyRefundRate(12345, null));
        assertEquals(46, FeeUtil.applyRefundRate(12345, "0.38"));
        assertEquals(47, FeeUtil.applyRate(12345, "0.38"));

        Map<String, Object> freezeMap = MapUtil.hashMap(Transaction.ORIGINAL_AMOUNT, 90000,
                Transaction.CONFIG_SNAPSHOT, MapUtil.hashMap(TransactionParam.WEIXIN_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.FEE, FeeUtil.applyRefundRate(90000, "0.35"), TransactionParam.ACTIVE, true))
        );
        Map<String, Object> consumeMap = MapUtil.hashMap(Transaction.ORIGINAL_AMOUNT, 51000,
                Transaction.CONFIG_SNAPSHOT, MapUtil.hashMap(TransactionParam.WEIXIN_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.ACTIVE, true, TransactionParam.FEE_RATE, "0.35"))
        );
        assertEquals(178, FeeUtil.calculateWeixinDepositConsumeFee(freezeMap, consumeMap));
    }

    @Test
    public void getActualFeeRate() {
        assertEquals(ZERO_FEE_RATE, FeeUtil.getActualFeeRate(null, null));
        assertEquals(ZERO_FEE_RATE, FeeUtil.getActualFeeRate("", null));
        assertEquals("0", FeeUtil.getActualFeeRate("0", null));
        assertEquals("0.0", FeeUtil.getActualFeeRate("0.0", null));
        assertEquals("0.1", FeeUtil.getActualFeeRate("0.1", null));
        assertEquals("0.1", FeeUtil.getActualFeeRate("0.1", ""));
        assertEquals("0.28", FeeUtil.getActualFeeRate("0.38", "0.1"));
        assertEquals(ZERO_FEE_RATE, FeeUtil.getActualFeeRate("0.38", "0.38"));
        assertEquals(ZERO_FEE_RATE, FeeUtil.getActualFeeRate("0.38", "0.39"));
    }

}
