package com.wosai.upay.util;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.service.UpayService;

public class SupportUtil {
    public static final String TERMINAL_SN = "tsn-1000001525774";                 // mock 终端sn
    public static final String STORE_SN = "st-1580000000144406";                    // mock 门店sn

    public static final String RSA2_PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCnFeIfdInE7MM4gjMY6M/hCHGdJNQjucwJbiexdKG5/qz78AZygMiZSl8MZg7OnONivttJqqCk+sK6VLtuK3U6nxAJZ77oPx1NP0DQPKPsqEGbx2YLF+Rzvz0eGezGB3F5yDvnUYRXz1LxVfdhmjQcDrjoqJkAlQID8CfkOAxG7XrDCQHLGTc8WHsJejxRJNbEizqcYDRUl54ttbYQA+tv44BEhHE9f5ofxdW+XcwmDxzvG2uH5uLdN10S6xG4XADIBpc3JflNoJgejYkVzXlTyDFhblF1QEKlJ52/jpK2/EAoUVQ8bzOuJwvADQDYLG4UggnIIAULaT0lD7IG+a4TAgMBAAECggEBAJ35P7kzywpIPG+SElOr0tfVzu2fo38/rJ0B7D+IoVwkYVM2ZjIedwsP2Pv1BmzswsRa5SQqUgaKHFBlymxoZEcPmu75ivM0sTSW6GW539biYl2x0lYMue+B0XxDUK0O3ogg/BpB5VnMaIRLHkDGthwNA2Dd/wMg3y3wAxdwspLfb4A33MAxGzIEAAmgiyjAXV3Lm3ZovmomCwIy6sG/ZqJYLRdMqQi9NCZdiJeP67PGovZ8m5EYIfhqwBGs6YhqRZDrzN6o4jMOsqoC6XrbRZ2e3Qavb9rVaVfEqc1AWitTD/gY5Ha5wsytaAP1M6VHnu4w/xb5XNg/TQ3OMS55QnkCgYEA3GrXQxBkFjij6g1RgGX6DY0pH0AC9lFu+L30e2Na3TOVX04IEsn/c/n7CfJktn6MMZCd7Dul3uy6I+L1j0a5BTwo8PaABEh7PKqF2XejoNI/OvsHtcEtmMUP/YGG5TTmZZg+DAEyyURd6eKW7KLntb8+n3v5TFeoOOPh0GiL040CgYEAwg8A2l959L/FMxTrwQg+ETT/3Oi0yyUOSm9naA9yrexTBdVdWBqB060rlsfvNIBY+CF7b3eaVo4A8AqimQiYGJ9/R1NJVnhQ5Fj1RAjaJryBizXM0siORvqcwtgNNUZORsMN8BRGaWdAkR/z4nq40YLc7IsFw+mbgWjdiUBrUB8CgYBxj2YqejZmsHXa71OvSFrlQnYOUKd2CoyXAR8FtzGoR5xMxuIn9AR11zQ8crLqxxYpry19+VlDGknolVUYpUJL7SvWwdlA26sKnXxqh1Y5VDkZhR36yXVZgSX04RgD3RZa/23wN2nYAi40gvZpIqEopUh5WKEfcQYNkXLVMhfgPQKBgQC0Vw0L4z6K3Mx+NCsDMI29n+tHeFrLjpxBQFZwtip0xvJAsZAVmEyeUof3j8YUnFISBcuYQyYC2eS1Lqz8Wqg9+btmpVe6LAXAr4r+tttEtFoO7Ohz2j2eaLfVusJLiBKmYA5L1gu5WGMAexlCl5m52ktE581ry2TgVnvKWTthMQKBgQDGOL65M+BR+R1lWOEzsu2P6+qRAmfnEVXxiDawrrukOKgs6vj7zYigbYd1gdPk4zsfV6xKFnFKeamU5k/NVhzOvh1knZAKGt03L/jNyvBwTj3CmAUhXYYLWP+LmyRY5bdHouV/yi+PNr+9QcrvsqkvTGPF+ATJ1rrQ5xBUuyettQ==";
    public static final String RSA2_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApxXiH3SJxOzDOIIzGOjP4QhxnSTUI7nMCW4nsXShuf6s+/AGcoDImUpfDGYOzpzjYr7bSaqgpPrCulS7bit1Op8QCWe+6D8dTT9A0Dyj7KhBm8dmCxfkc789Hhnsxgdxecg751GEV89S8VX3YZo0HA646KiZAJUCA/An5DgMRu16wwkByxk3PFh7CXo8USTWxIs6nGA0VJeeLbW2EAPrb+OARIRxPX+aH8XVvl3MJg8c7xtrh+bi3TddEusRuFwAyAaXNyX5TaCYHo2JFc15U8gxYW5RdUBCpSedv46StvxAKFFUPG8zricLwA0A2CxuFIIJyCAFC2k9JQ+yBvmuEwIDAQAB";
    
    public static final String RSA_PRIVATE_KEY = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKB7HSppBgB5iT2JXNaDy0Uxorhldn/xdRs48mGNfTRhWHd/r/xQb1zOMDq7dl6L/K46vMGeohpSxqUhfJ9HfmPl9w6MHHqEK1NykxVQfDCzkvnAXA5rZv+Qq9OFBzyB0q8oNL17x7q+x8VEuVWZWEtleL4m+0uvrqG6wz//SvuBAgMBAAECgYBH9m0hs26pj9Zm7c3fs8HXCWb6YSU+QjxNNMAb1qKqdmyBSZTVBAysBRnDfISb6RIp3tT9zfDK9Qt2yQllM14NH7wbFoUASHsSfWbOaHpIUcq2zxO/hXqXQkuwk0mLo4KPFhPpixUNf/pcniCwq2BsEk0569y+FJWJKzyOjXWfMQJBANldqKNPD9AL4l4xK2bayvmy3xIeSYJHxCJDCuRCTN+NGB0jZ7s4dWxBQLS/DtZDM4+cxeJj9RYVk7gt6QlI+P8CQQC9ASOuwFNqMGt3MG/KQ8OhY201+Mo7KV2JNrAJdR4SaoCQLl0LGoDB7GWd1Y+nyjfDhRnz51u+0JS+5qZ+G4t/AkBhkSnGDBHz97GnR/jMVT8wr7pzXobuZJpEKsWzyZLJ8YI2ahMAtYn3ip6pwuy8/jMhrmQgh2+Wdx70WB83HLsvAkBnK34M4BMoffHLh/cd08uwBxQQSBQsIzJZui5xmyqlJtmy2nwTmY37TU1S0Mav8qRp5/f4uavNBxKK+mCbqfGFAkAQ5UwYfq2Z1beIxLJs+SaivhJtvg17f/ruMc75Fa1AZfD7Z/v+rhkqw6np3Ys5KMiiZiFdbUTjXREP2HTy8XEg";
    public static final String RSA_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgex0qaQYAeYk9iVzWg8tFMaK4ZXZ/8XUbOPJhjX00YVh3f6/8UG9czjA6u3Zei/yuOrzBnqIaUsalIXyfR35j5fcOjBx6hCtTcpMVUHwws5L5wFwOa2b/kKvThQc8gdKvKDS9e8e6vsfFRLlVmVhLZXi+JvtLr66husM//0r7gQIDAQAB";

    public static final String SM2_PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgn7/InqcvIPCi1KcYm2wFhtPEZq70DIloKNyLWcc+AbigCgYIKoEcz1UBgi2hRANCAAQfTDcdMScyaW2cdYiY6BUTfHb1BdFMm7eBO9L6da16GQzQfjWJ79h9Za7GbmgewpyUFfdz+fRHITk2gc9LH49o";
    public static final String SM2_PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEH0w3HTEnMmltnHWImOgVE3x29QXRTJu3gTvS+nWtehkM0H41ie/YfWWuxm5oHsKclBX3c/n0RyE5NoHPSx+PaA==";

    public static final String RSA2_PRIVATE_WEIXIN_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDhrORP6LLUpgqvK1nHLDRDlW6ntfJYw5aCOzjO569C8Q1kfM9Mkm53qYWdzvR64ZUtys1fWlWhMD1AmsgREbCAfT3yr4pciUjD7tkjqI1CIo/2+FA1xQQmxa5OmWAYpvYNtglMyMzbavKpXkJyZRoPg+dGirZX/C0rj5i5fP45Kb9ejyC0dcPry7D6GGhtYVPaCgtYTdE436YRttomCQmj0LzFrHQXLPINTxUddHkfU9sf3WjyX9bGwWe8auFGbmQEAOPG9oxB839c53PdAKn6xKT6FlK2ryMW5Zoq6JZtfiQc7tPJgFzMlW3kU9HkNPLTepMu5vdO3m3cj4WFWizjAgMBAAECggEAN8frWxESettb0rZtZ3y2omniQxQHBge6QRBwVzwPJwW8ILV06+FZ6rv9KeeIqILvw7TYFJSYi9IHodtgTk/IB8ptMHpRQdQxJzT3eQfleo5edVYlRVUgVJESJ51jklRah0MSg2cys514w4Jn/CyfR2x8EwSgU5kidRz2TAroL6p6CxwhsKiv47JIrr+G/Wki6IC/FMUjzXjuRIY4/hmhgKN/vN+wS+pCfR2AqVfGp0VH7s5ajjP/9028mROUyPztx7uZN2NdRQZR2qkEM2KaFIV8aN+xBaleaq06WGmBACfaFvmygU6wwF05oXHe0nIaNx1Rx0Flqm30F4d07V0aiQKBgQD8WyWl/3l3xwTGAWN6I5IPxmBKaK0X2Z4IW1HDwkDXtTPcfEFZeKgoZRkIkzusRMwGanRppPvRyfi7lCnLfXxLI3nUsti6hGPx3lFeVuIpxAem0p6bi+fiaGhkNcrdD4QNEzhxPNEU4H/yrvH3itOz5h+8VXz0+Y1X7JYEPo2+1QKBgQDk7x4dqNu70g8eKIpcSAGMJW5/T94ITPXLlSsUpXOGxsnAgAtNAcFrTfk9qNOEGAXYJwQ+ni9YC6jcmQZYi6YN/mv08JzeXHyXKp/mdHQ6J7tZrpnufUgtCYXGoL5wSBNfIs/KD+G9i993FstueEbcAo37G+2S9A10WviY05dI1wKBgQCIC49RXVgbpZRCNs9tMi31CSoyExp1yCD1Ol3rHhcc8xghXm9Qj8Z/+Hi1ccJyzaf8MXnO1l3XrH3Gikq55E2UqGxuqXhDnUnQQ90z8bz9bSv+2H8fNh5tpYHZvFJAOLQk5F7CVVoWWi1AJOZXYGVfD8m+G4xA+SQk6Wu6oAqEfQKBgDJpEOmZCCGYhKmwZZ8iX+zbRTSVgp+zSqB0jVLzh7fSyBQyXcqsK0oUlmBkb4bzvz20gbPEI0qw7pL8wy/2OC92qZD69p/kH68tvJ4DYKB5pUB/OPgR8DWylhmBxc11oYZeZQDqdFvHlAJmK498vqWtjlQP90VXpirL6VfFp9drAoGAXl70pNPDhEzAakeXx1g6e9Lnx7tqH2VtD2Ze5F2/p1mMS5zcTfYTtUV6c/6lDqVAgKlqeFu+hA/lRMyzS0wXtykizP/sLjl9gp4qh1lo3cfTstHxc7ocfznHNoZseESkhLXCTGVX4jtuwSufGHrZfRnQzBChkNJk+pig1qdL8cY=";


    public static final Map<String, Object> ALIPAY_AUTH_INFO = CollectionUtil.hashMap(
                TransactionParam.APP_AUTH_TOKEN, "*******appAuthToken**********",
                TransactionParam.APP_AUTH_STORE_ID, "*************authStoreId************",
                TransactionParam.APP_AUTH_SHOP_ID, "*************authShopId************");
    
    public static final Map<String,Object> ALIPAY_V1_TRADE_PARAMS = CollectionUtil.hashMap("alipay_v1_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
                "liquidation_next_day", false,
                "partner", "*****partner****",
                "app_key", "*****app_key****",
                "agent_id", "10610050a1",
                "merchant_alipay_industry", null,
                TransactionParam.CHANNEL_CURRENCY_FEE_RATES, CollectionUtil.hashMap(
                            "CNY", "1",
                            "HKD", "1.2"
                        )
    ));

    public static final Map<String,Object> ALIPAY_WAP_TRADE_PARAMS = CollectionUtil.hashMap("alipay_wap_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "partner", "*****partner****",
            "app_key", "*****app_key****",
            "agent_id", "10610050a1",
            "merchant_alipay_industry", null
    ));

    public static final Map<String,Object> BESTPAY_TRADE_PARAMS = CollectionUtil.hashMap("bestpay_trade_params", CollectionUtil.hashMap("fee_rate", "0.38",
            "ledger_detail", 0,
            "liquidation_next_day", false,
            "merchant_id", "3178002070426323",
            "merchant_key", "349b29fe-2f14-40c0-9039-429a90e64786",
            "merchant_pwd", "397271",
            "store_id", "2",
            "sub_merchant_id", "02430109020620052",
            "sign_type", "RSA",
            "active", true,
            "fee", 0
    ));
    // 预授权支付宝wap、小程序交易参数
    public static final Map<String,Object> DEPOSIT_MINI_ALIPAY_WAP_V2_TRADE_PARAMS = CollectionUtil.hashMap("alipay_wap_v2_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
                "liquidation_next_day", false,
                "pid", "*****pid****",
                "app_id", "*****2015102000490218****",
                "app_key", "akhq4bltbpiw9ocvr35a1oyfvmj04fhw",
                "partner", "****************",
                "private_key", "d9df7ab0-4b23-4d2f-a9c9-fc191a70a2cc",
                "public_key", "698a0f73-43a6-11e9-9807-7cd30ae435b2",
                "app_auth_token", "*************app_auth_token*************",
                "auth_app_id", "***********auth_app_id*********",
                "seller_id", "*************seller_id**********",
                "category", "**************category*************",
                "service_id", "**************service_id************",
                "app_auth_store_id", "**************app_auth_store_id**********",
                "app_auth_shop_id", "***************app_auth_shop_id***********"
    ));
    
    public static final Map<String,Object> UP_DIRECT_TRADE_PARAMS_WEIXIN = CollectionUtil.hashMap("up_direct_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
                "liquidation_next_day", true,
                "channel_id", "*******channel_id******",
                "rece_org_no", "******rece_org_no*****",
                "up_private_key", "*****up_private_key***",
                "weixin_appid", "*****weixin_appid***",
                "weixin_sub_appid", "*****weixin_sub_appid***",
                "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
                "weixin_mch_id", "*****weixin_mch_id***",
                "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
                "provider_mch_id", "*****provider_mch_id***",
                "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
                "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
                "active", true
    ));
    
    public static final Map<String,Object> UNION_PAY_TRADE_PARAMS = CollectionUtil.hashMap("up_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", true,
            "channel_id", "*******channel_id******",
            "rece_org_no", "******rece_org_no*****",
            "up_private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "app_id", "*****app_id***",
            "sys_pid", "*****sys_pid***",
            "alipay_sub_mch_id", "*****alipay_sub_mch_id***",
            "rece_org_no", "*****rece_org_no***",
            "service_id", "*****service_id***",
            "category", "*****category***",
            "cert_id", "*****cert_id***",
            "provider_mch_id", "*****provider_mch_id***",
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "active", true
    ));
    
    public static final Map<String,Object> NUCC_TRADE_PARAMS = CollectionUtil.hashMap("nucc_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", true,
            "idc_flag", "*****8idc_flag*******",
            "channel_id", "*******channel_id******",
            "rece_org_no", "******rece_org_no*****",
            "nucc_private_key", "*****nucc_private_key***",
            "nucc_cert", "*******nucc_cert*******",
            "sp_mch_id", "**********nucc_cert*******",
            "public_key", "*****public_key***",
            "app_id", "*****app_id***",
            "sys_pid", "*****sys_pid***",
            "alipay_sub_mch_id", "*****alipay_sub_mch_id***",
            "rece_org_no", "*****rece_org_no***",
            "service_id", "*****service_id***",
            "category", "*****category***",
            "cert_id", "*****cert_id***",
            "provider_mch_id", "*****provider_mch_id***",
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "bestpay_sign_sn", "******bestpay_sign_sn*****",
            "parent_merchant_id", "*******parent_merchant_id******",
            "active", true
    ));
    
    public static final Map<String,Object> UP_DIRECT_TRADE_PARAMS_ALIPAY = CollectionUtil.hashMap("up_direct_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", true,
            "channel_id", "*******channel_id******",
            "rece_org_no", "******rece_org_no*****",
            "up_private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "app_id", "*****app_id***",
            "sys_pid", "*****sys_pid***",
            "alipay_sub_mch_id", "*****alipay_sub_mch_id***",
            "rece_org_no", "*****rece_org_no***",
            "service_id", "*****service_id***",
            "category", "*****category***",
            "cert_id", "*****cert_id***",
            "provider_mch_id", "*****provider_mch_id***",
            "active", true
    ));

    public static final Map<String,Object> WEIXIN_TRADE_PARAMS = CollectionUtil.hashMap("weixin_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
                "liquidation_next_day", false,
                "weixin_appid", "*****weixin_appid***",
                "weixin_sub_appid", "*****weixin_sub_appid***",
                "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
                "weixin_mch_id", "*****weixin_mch_id***",
                "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
                "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
                "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
                "weixin_cert_config_key", "****weixin_cert_config_key****",
                "weixin_cert_password", "****weixin_cert_password****",
                "active", true
    ));
    
    public static final Map<String,Object> WEIXIN_WAP_TRADE_PARAMS = CollectionUtil.hashMap("weixin_wap_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "active", true
    ));
    
    public static final Map<String,Object> WEIXIN_MINI_TRADE_PARAMS = CollectionUtil.hashMap("weixin_mini_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "active", true
    ));
    
    public static final Map<String,Object> WEIXIN_APP_TRADE_PARAMS = CollectionUtil.hashMap("weixin_app_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "active", true
    ));

    public static final Map<String,Object> WEIXIN_H5_TRADE_PARAMS = CollectionUtil.hashMap("weixin_h5_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "active", true
    ));

    public static final Map<String,Object> ALIPAY_V2_TRADE_PARAMS = CollectionUtil.hashMap("alipay_v2_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "pid", "*****pid***",
            "app_id", "*****app_id***",
            "private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "app_auth_token", "*****app_auth_token***",
            "auth_app_id", "*****auth_app_id*****",
            "mch_id", "*****mch_id*****",
            "category", "****category****",
            "seller_id", "****seller_id****",
            "app_auth_store_id", "****app_auth_store_id****",
            "app_auth_shop_id", "****app_auth_shop_id****",
            "active", true
    ));

    public static final Map<String,Object> ALIPAY_WAP_V2_TRADE_PARAMS = CollectionUtil.hashMap("alipay_wap_v2_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "pid", "*****pid***",
            "app_id", "*****app_id***",
            "private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "app_auth_token", "*****app_auth_token***",
            "auth_app_id", "*****auth_app_id*****",
            "mch_id", "*****mch_id*****",
            "category", "****category****",
            "seller_id", "****seller_id****",
            "app_auth_store_id", "****app_auth_store_id****",
            "app_auth_shop_id", "****app_auth_shop_id****",
            "active", true
    ));
    
    public static final Map<String,Object> ALIPAY_APP_V2_TRADE_PARAMS = CollectionUtil.hashMap("alipay_app_v2_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "pid", "*****pid***",
            "app_id", "*****app_id***",
            "private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "app_auth_token", "*****app_auth_token***",
            "auth_app_id", "*****auth_app_id*****",
            "mch_id", "*****mch_id*****",
            "category", "****category****",
            "seller_id", "****seller_id****",
            "app_auth_store_id", "****app_auth_store_id****",
            "app_auth_shop_id", "****app_auth_shop_id****",
            "active", true
    ));
    
    public static final Map<String,Object> ALIPAY_H5_V2_TRADE_PARAMS = CollectionUtil.hashMap("alipay_h5_v2_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "pid", "*****pid***",
            "app_id", "*****app_id***",
            "private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "app_auth_token", "*****app_auth_token***",
            "auth_app_id", "*****auth_app_id*****",
            "mch_id", "*****mch_id*****",
            "category", "****category****",
            "seller_id", "****seller_id****",
            "app_auth_store_id", "****app_auth_store_id****",
            "app_auth_shop_id", "****app_auth_shop_id****",
            "active", true
    ));
    
    public static final Map<String,Object> WEIXIN_MIN_TRADE_PARAMS = CollectionUtil.hashMap("weixin_mini_trade_params",CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "weixin_appid", "wxeb7a1cb7b4610e2f",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "1440558602",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "active", true,
            "service_id","00002000000000157865268295736578",
            "serial_no","76C51D1C0A2644AB4F899CF5261BD11365178F3A",
            "weixin_appkey_v3","nCk8DZOHfKYEsvqe05p5Ft2zWH2FxZGF",
            "private_key_v3","330d03e2-2d02-47c4-a728-ae3458514e59",
            "public_key_v3","028fe0a141611b524b0ceb0e978df2ba"
    ));

    public static final Map<String,Object> UNION_PAY_TL_TRADE_PARAMS = CollectionUtil.hashMap("up_tl_trade_params",CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", true,
            "org_id", "*********org_id********",
            "up_private_key", "*****up_private_key***",
            "channel_id", "******channel_id*******",
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "1440558602",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "app_id", "****app_id****",
            "sys_pid", "****sys_pid****",
            "alipay_sub_mch_id", "****alipay_sub_mch_id****",
            "provider_mch_id", "****provider_mch_id****",
            "cert_id", "****cert_id****",
            "mch_id", "****mch_id****",
            "mer_cat_code", "****mer_cat_code****",
            "mer_name", "****mer_name****",
            "term_id", "****term_id****",
            "active", true
    ));

    public static final Map<String,Object> CIBSHBANK_TRADE_PARAMS = CollectionUtil.hashMap("cibshbank_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "pid", "*****pid***",
            "channel_fee_rate", "0.2",
            "sp_mch_id", "*****sp_mch_id***",
            "sp_sign_agent_key", "*****sp_sign_agent_key***",
            "sp_sign_agent_no", "*****sp_sign_agent_no***",
            "active", true
    ));

    public static final Map<String,Object> CHINAUMS_TRADE_PARAMS = CollectionUtil.hashMap("chinaums_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "app_id", "*****app_id***",
            "app_key", "****app_key****",
            "mch_code", "*****mch_code***",
            "term_code", "*****term_code***",
            "active", true
    ));
    
    public static final Map<String,Object> UPO_TRADE_PARAMS = CollectionUtil.hashMap("upo_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "upo_pid", "*****upo_pid***",
            "upo_mch_id", "****upo_mch_id****",
            "lakala_term_id", "*****lakala_term_id***",
            "private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "active", true
    ));

    public static final Map<String,Object> CMCC_TRADE_PARAMS = CollectionUtil.hashMap("cmcc_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "cmcc_merchant_id", "*****cmcc_merchant_id***",
            "cmcc_merchant_key", "****cmcc_merchant_key****",
            "sign_type", "MD5",
            "active", true
    ));

    public static final Map<String,Object> SODEXO_TRADE_PARAMS = CollectionUtil.hashMap("sodexo_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "mid", "*****mid***",
            "tid", "****tid****",
            "client_id", "****client_id****",
            "cient_secret", "****cient_secret****",
            "active", true
    ));

    public static final Map<String,Object> ALIPAY_INTL_TRADE_PARAMS = CollectionUtil.hashMap("alipay_intl_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "client_id", "*****client_id***",
            "private_key", "****private_key****",
            "merchant_id", "****merchant_id****",
            "merchant_alipay_industry", "5699",
            "active", true
    ));

    public static final Map<String,Object> GIFT_CARD_TRADE_PARAMS = CollectionUtil.hashMap("gift_card_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "app_id", "*****app_id***",
            "private_key", "****private_key****",
            "sign_type", "RSA",
            "active", true
    ));

    public static final Map<String,Object> LAKALA_TRADE_PARAMS = CollectionUtil.hashMap("lakala_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "channel_fee_rate", "0.2",
            "lakala_comport_code", "****lakala_comport_code****",
            "lakala_secret", "****lakala_secret****",
            "lakala_weixin_sub_appid", "****lakala_weixin_sub_appid****",
            "weixin_sub_appid", "****weixin_sub_appid****",
            "weixin_sub_appsecret", "****weixin_sub_appsecret****",
            "lakala_merc_id", "****lakala_merc_id****",
            "lakala_term_id", "****lakala_term_id****",
            "sign_type", "RSA",
            "active", true
    ));

    public static final Map<String,Object> WEIXIN_HK_V3_TRADE_PARAMS = CollectionUtil.hashMap("weixin_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", false,
            "weixin_appid", "*****weixin_appid***",
            "weixin_sub_appid", "*****weixin_sub_appid***",
            "weixin_sub_appsecret", "*****weixin_sub_appsecret***",
            "weixin_mch_id", "*****weixin_mch_id***",
            "weixin_sub_mch_id", "*****weixin_sub_mch_id***",
            "weixin_mini_sub_appid", "*****weixin_mini_sub_appid*****",
            "weixin_mini_sub_appsecret", "*****weixin_mini_sub_appsecret*****",
            "weixin_cert_config_key", "****weixin_cert_config_key****",
            "weixin_cert_password", "****weixin_cert_password****",
            "merchant_wechat_industry", 5331,
            "serial_no", "703DA83CED3AAB16DAB6DD2E7832E7491BDDAE7E",
            "active", true,
            "version", "v3",
            TransactionParam.CHANNEL_CURRENCY_FEE_RATES, CollectionUtil.hashMap("CNY", "1", "HKD", "1.3")
    ));

    public static final Map<String,Object> LAKALA_UNION_PAY_OPEN_TRADE_PARAMS = CollectionUtil.hashMap("lakala_open_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", true,
            "version", "v3",
            "app_id", "OP00000003",
            "seria_no", "OP00000003",
            "private_key", "d9df7ab0-4b23-4d2f-a9c9-fc191a70a2cc",
            "channel_id", "28",
            "merc_id", "****merc_id****",
            "term_id", "****term_id****",
            "term_no", "****term_no****",
            "bankcard_fee", CollectionUtil.hashMap("credit", CollectionUtil.hashMap("fee", "0.57"), "debit", CollectionUtil.hashMap("fee", "0.75", "max", 4500),
                    "edc", CollectionUtil.hashMap("fee", "1.57", "max", 5500), "dcc", CollectionUtil.hashMap("fee", "1.75", "max", 6500)),
            "active", true
    ));

    public static final Map<String,Object> TL_SYB_TRADE_PARAMS = CollectionUtil.hashMap("tl_syb_trade_params", CollectionUtil.hashMap("fee_rate", "0.6",
            "liquidation_next_day", true,
            "org_id", "660290000000J8Y",
            "app_id", "********",
            "seria_no", "OP00000003",
            "private_key", "*****private_key***",
            "public_key", "*****public_key***",
            "sys_pid", "****************",
            "alipay_sub_mch_id", "****alipay_sub_mch_id****",
            "cus_id", "563290058122AJA",
            "active", true
    ));

    public static final Map<String,Object> BASIC_PARAMS = CollectionUtil.hashMap("vendor_id", "b52600e36100-3ce9-5e11-99fa-f5f9d958",
                    "merchant_id", "0040a3b45204-5ceb-fdb4-c8d5-02c14674",
                    "merchant_sn", "mch-1680000810456",
                    "merchant_name", "testcase测试商户",
                    "merchant_country", "CN",
                    "currency", "CNY",
                    "store_id", "1ce384ece391-7d6b-4f34-52a8-0f20bb8f",
                    "store_sn", STORE_SN,
                    "store_client_sn", "88297761728900",
                    "store_name", "just a test002",
                    "store_city", "苏州市",
                    "terminal_id", "620585fcd3a9-6eeb-f274-cb95-39fad5e5",
                    "terminal_sn", TERMINAL_SN,
                    "terminal_name", "麻辣E族",
                    "merchant_daily_max_sum_of_trans", "2500000",
                    "is_sent_store_id", true,
                    TransactionParam.REFUND_NON_SQB_ORDER, true,
                    "deposit", CollectionUtil.hashMap("weixin", 1, "alipay", 1),
                    TransactionParam.COMMON_SWITCH, "11111111111111111"
    );

    public static final Map<String,Object> BESTPAY_BASIC_PARAMS = CollectionUtil.hashMap("vendor_id", "b52600e36100-3ce9-5e11-99fa-f5f9d958",
            "merchant_id", "c9c94ba03dc7-8008-7e11-0012-e8701662",
            "merchant_sn", "mch-1680000730061",
            "merchant_name", "testcase测试商户",
            "merchant_country", "CN",
            "currency", "CNY",
            "store_id", "9a489947830f-c769-3e94-92dd-f50b733d",
            "store_sn", STORE_SN,
            "store_client_sn", "1162",
            "store_name", "just a test003",
            "store_city", "长沙市",
            "terminal_id", "e7b165bb2726-e53a-f8a4-9466-5fbc5927",
            "terminal_sn", TERMINAL_SN,
            "terminal_name", "老百姓大药房",
            TransactionParam.REFUND_NON_SQB_ORDER, true
    );

    public static Map buildBasicParams() {
        Map newMap = new HashMap();
        newMap.putAll(BASIC_PARAMS);
        newMap.put(TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        return newMap;
    }

    public static Map buildTlBasicParams() {
        Map newMap = new HashMap();
        newMap.putAll(BASIC_PARAMS);
        newMap.put(TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL);
        return newMap;
    }

    public static Map buildGetAllParams(Map tradeParams) {
        Map newMap = new HashMap();
        newMap.putAll(BASIC_PARAMS);
        newMap.put(TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
        newMap.putAll(tradeParams);
        return newMap;
    }

    public static Map buildTlGetAllParams(Map tradeParams) {
        Map newMap = new HashMap();
        newMap.putAll(BASIC_PARAMS);
        newMap.put(TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL);
        newMap.putAll(tradeParams);
        return newMap;
    }

    public static String getRsaPrivateKey() {
        String key = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIxj8tBs0Ag/ia/lQiq0MzrW9GlhSTFyxMEvHNYWc0l7Y+li2iVaEKcOJ/0PU1ke5Q/UZLVu9p7E42THFvzeLqZ20ZJfwyXAOiMb82xsJWBLMWMpKiCBODISiUQsOjlgzhdfx5q5Dzc9iiBmrxbwZ8olXN3tYj41HEXCgr6Cs9O3AgMBAAECgYA/YCtP/Z8X6h7s4bCx+HEcAceJ/tn80ZOSpgL9flsC9AnRZMeXjwXvdAuHSLDWjYTYVYg1A2efmsAQUXPGKUKjQuviclFxjfOeNwNZvh+VW05F+7jGa+bu4OFsWFvRX2gRzanGNgSl9IPTJGg1tDf2VtF/PYF/EGStQsqHCuS92QJBAOQemCBv4ptkv56i/WpLj0PhL8OhE44LicPXm2rhY0q9dICTsXrI5/eKE+HUrF+tVJS0B9/fa9/xqpVTIC+/3M0CQQCdjHxToE9byZfVvBIhT3nvzmKtRWZhvWYNf9my3nwVhBm5Pt3vf6dO2HJqQmYYv49Ipn/TRQ0jwE5/eDOYCDKTAkAlKu7yaghr9TBbcocDqKgBEVGs+DOtcyfRJvIFqg95gADZajcCoHVbGb4/j10+gYlEaUjXKxnydPesOM0HuqUJAkAgJLh861kllfS3c76TMz7ikU4Kof0oddbrbClDWCci0Kfa5purMHU3HewCB/gClpqqLOYpeY4bvDGHykNkgk+nAkBwNntjK40mVZnjZKEVIuEWlsiHb2ruapxr/FO03cvEzY6+huVSR2atpvZmfhPB5/VvGiHP1WHAFUrrhgJR0/F0";
        return key;
    }

    public static JSONObject buildPayRequest(String dynamicId) {
        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(UpayService.DYNAMIC_ID, dynamicId);
        request.put(UpayService.TOTAL_AMOUNT, "30");
        request.put(UpayService.CLIENT_SN, System.currentTimeMillis() + StringUtils.leftPad(ThreadLocalRandom.current().nextInt(9999) + "", 4, "0"));
        request.put(UpayService.SUBJECT, "auto test case");
        request.put(UpayService.EXTENDED, CollectionUtil.hashMap(
                Transaction.SQB_SCENE, "dbb"
        ));
        return request;
    }

    public static JSONObject buildPrecreateRequest(Integer payway, Integer subPayway, String payerUid) {
        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(UpayService.PAYWAY, String.valueOf(payway));
        request.put(UpayService.SUB_PAYWAY, String.valueOf(subPayway));
        request.put(UpayService.PAYER_UID, payerUid);
        request.put(UpayService.TOTAL_AMOUNT, "30");
        request.put(UpayService.CLIENT_SN, System.currentTimeMillis() + StringUtils.leftPad(ThreadLocalRandom.current().nextInt(9999) + "", 4, "0"));
        request.put(UpayService.SUBJECT, "auto test case");
        return request;
    }

    public static JSONObject buildQueryRequest(String sn, String clientSn, String sqbOrigin){
        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(UpayService.SN, sn);
        request.put(UpayService.CLIENT_SN, clientSn);
        request.put(UpayService.SQB_ORIGIN, sqbOrigin);

        return request;
    }

    public static JSONObject buildWxAuthInfoRequest(String rawdata) {
        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(BusinessFields.RAWDATA, rawdata);
        request.put(UpayService.TOTAL_AMOUNT, "30");
        request.put(UpayService.CLIENT_SN, System.currentTimeMillis() + "");
        request.put(UpayService.SUBJECT, "auto test case");
        return request;
    }

    public static JSONObject buildWeixinPreCreateRequest(Integer payway, Integer subPayway) {
        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(UpayService.PAYWAY, String.valueOf(payway));
        request.put(UpayService.SUB_PAYWAY, String.valueOf(subPayway));
        request.put(UpayService.TOTAL_AMOUNT, "30");
        request.put(UpayService.CLIENT_SN, System.currentTimeMillis() + "");
        request.put(UpayService.SUBJECT, "auto test case");
        JSONObject extended = new JSONObject();
        JSONObject timeRange = new JSONObject();
        timeRange.put("start_time","OnAccept");
        extended.put("time_range",timeRange);
        JSONObject riskFund = new JSONObject();
        riskFund.put("name","DEPOSIT");
        riskFund.put("amount",100);
        extended.put("risk_fund",riskFund);
        extended.put("need_user_confirm",true);
        request.put("extended",extended);
        return request;
    }


    public static JSONObject buildRefundRequest(String clientSn) {
        JSONObject request = new JSONObject();
        request.put(UpayService.TERMINAL_SN, SupportUtil.TERMINAL_SN);
        request.put(UpayService.REFUND_REQUEST_NO, System.currentTimeMillis()+"");
        request.put(UpayService.CLIENT_SN, clientSn);
        request.put(UpayService.OPERATOR, "auto test case");
        request.put(UpayService.REFUND_AMOUNT, "10000");
        return request;
    }
}
