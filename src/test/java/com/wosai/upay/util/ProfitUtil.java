package com.wosai.upay.util;

import com.wosai.data.util.CollectionUtil;

import java.util.Arrays;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/13.
 */
public class ProfitUtil {

    public static final Map<String,Object> PROFIT_SHARING = CollectionUtil.hashMap(
            "sharing_flag", "1",
            "sharing_type", "3",
            "model_id", "m1",
            "receivers", Arrays.asList(
                    CollectionUtil.hashMap(

                            "id", "r1",
                            "sharing_amount", "4",
                            "client_sn", "c1"
                    ),
                    CollectionUtil.hashMap(

                            "id", "r2",
                            "sharing_amount", "5",
                            "client_sn", "c2"
                    )
            )
    );

    public static final Map<String,Object> REFUND_PROFIT_SHARING = CollectionUtil.hashMap(
            "sharing_flag", "1",
            "receivers", Arrays.asList(
                    CollectionUtil.hashMap(

                            "id", "r1",
                            "sharing_amount", "2"
                    ),
                    CollectionUtil.hashMap(

                            "id", "r2",
                            "sharing_amount", "3"
                    )
            )
    );
}
