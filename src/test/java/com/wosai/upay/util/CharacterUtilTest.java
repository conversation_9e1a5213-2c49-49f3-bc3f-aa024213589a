package com.wosai.upay.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import static org.junit.Assert.*;


/**
 * <AUTHOR>
 * @description
 * @date 2024-03-13
 */
@Slf4j
public class CharacterUtilTest {

    @Test
    public void filterSpecialCharacter() {
        assertEquals("", CharacterUtil.filterSpecialCharacter(""));
        assertNull(CharacterUtil.filterSpecialCharacter(null));

        String str = "收钱吧sq8 \t\n\"\r\f\\\b()[]{}$`~!@^&*+-=/|<>,.?~！#¥%；“”。？";
        assertEquals("收钱吧sq8", CharacterUtil.filterSpecialCharacter(str));

        String str1 = "\t\n\"\r\f\\\b() []{}$`~!@^&*+-=/|<>,.?~！#¥%；“”。？";
        assertEquals("", CharacterUtil.filterSpecialCharacter(str1));
    }

    @Test
    public void isChineseCharacter() {
        assertTrue(CharacterUtil.isChineseCharacter('中'));
        assertTrue(CharacterUtil.isChineseCharacter('国'));
        assertTrue(CharacterUtil.isChineseCharacter('上'));
        assertTrue(CharacterUtil.isChineseCharacter('海'));
        assertFalse(CharacterUtil.isChineseCharacter('8'));
        assertFalse(CharacterUtil.isChineseCharacter('b'));
        assertFalse(CharacterUtil.isChineseCharacter('*'));
        assertFalse(CharacterUtil.isChineseCharacter('#'));
        assertFalse(CharacterUtil.isChineseCharacter('$'));
        assertFalse(CharacterUtil.isChineseCharacter('@'));
        assertFalse(CharacterUtil.isChineseCharacter('!'));
        assertFalse(CharacterUtil.isChineseCharacter('~'));
    }
}