package com.wosai.upay.util;

import static org.junit.Assert.assertNotNull;

import java.util.Map;

import org.junit.Test;

import com.wosai.data.util.CollectionUtil;

/**
 *  注意：由于配置都在配置中心中，所以此处的单元测试用例只做基础的接口调用
 *
 */
public class ApolloConfigurationCenterUtilTest {

    @Test
    public void test() {
        Map<String, String> defaultGateway = CollectionUtil.hashMap("1016.concurrency", "");
        Map<String, Integer> defaultThreadPoolConfig = CollectionUtil.hashMap("1016.concurrency", 100);
        String notifyPrivateNetworkConfigStr = "{\"local1\":\"https://shouqianba.com/\"}";
        ApolloConfigurationCenterUtil util = new ApolloConfigurationCenterUtil(defaultGateway, defaultThreadPoolConfig, notifyPrivateNetworkConfigStr,"hangzhou");
        assertNotNull(util.getProviderThreadPoolConfig("1016"));
        
        assertNotNull(util.getCsbToWapConfig());
        assertNotNull(util.getFakeDatabaseFastFail());
        assertNotNull(util.getFakeRequestEnable());
        assertNotNull(util.getHistoryRefundInfoCacheTimeout());
        assertNotNull(util.getMaxRefundCount());
        assertNotNull(util.getQueryHistoryEnable());
        assertNotNull(util.getRateLimiterEnable());
        assertNotNull(util.getTradeCacheTimeout());
        assertNotNull(util.getWorkflowDiscardThreshold());
        assertNotNull(util.getNotifyHost("local1", "/sqb_notify"));
        util.getProviderGateway("provider.tl.unionpay.weixin.wapOrMini", "pay");
        util.getProviderGatewayConfig("provider.tl.unionpay.weixin.wapOrMini", "pay");
        util.getUpstreamConfigKey("provider.tl.unionpay.weixin.wapOrMini", false, "pay");
        util.getUpstreamConfigKey("provider.tl.unionpay.weixin.wapOrMini", true, "pay");

    }

}
