package com.wosai.upay;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.RateLimiter;
import com.wosai.net.Peer;
import com.wosai.net.Upstream;
import com.wosai.net.UpstreamPeerContextHolder;
import com.wosai.upay.service.ExternalServiceFacade;
import com.wosai.upay.service.TsnGenerator;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2019/8/2.
 */

@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class ProviderGatewayTest {
    public static final Logger logger = LoggerFactory.getLogger(ProviderGatewayTest.class);
    @Autowired
    TsnGenerator generator;
    @Autowired
    ExternalServiceFacade externalServiceFacade;

    @BeforeClass
    public static void setup() {

    }

    @AfterClass
    public static void tearDown() {

    }


    @Test
    public void testChooseServer(){
        int api1Weight = 60;
        int api2Weight = 30;
        int api3Weight = 20;
        String serverConfig = "{\"servers\":[{\"server\":\"https://api.mch.weixin.qq.com\",\"weight\":"+api1Weight+"},{\"server\":\"https://api2.mch.weixin.qq.com\",\"weight\":"+api2Weight+"},{\"server\":\"https://api3.mch.weixin.qq.com\",\"weight\":"+api3Weight+"}]}";
        Upstream upstream = JSON.parseObject(serverConfig.replace("servers", "peers"), Upstream.class);
        long count = (api1Weight + api2Weight + api3Weight) * 1000;
        long api1Count = 0;
        long api2count = 0;
        long api3count = 0;
        for (int i = 0; i < count; i++) {
            String url = upstream.getPeer().getServer();
            if("https://api.mch.weixin.qq.com".equals(url)){
                api1Count++;
            }else if("https://api2.mch.weixin.qq.com".equals(url)){
                api2count++;
            }else if("https://api3.mch.weixin.qq.com".equals(url)){
                api3count++;
            }
        }
        Assert.assertEquals(count , api1Count + api2count + api3count);
        Assert.assertEquals(api1Count * 1.0/api2count, api1Weight/api2Weight, 0.000000001);
        Assert.assertEquals(api1Count * 1.0/api3count, api1Weight/api3Weight, 0.000000001);

    }

    @Test
    public void testConcurrentUpstream(){
        ExecutorService service = Executors.newFixedThreadPool(100);
        int count = 50000;
        CountDownLatch latch = new CountDownLatch(count);
        //server weight down maxFails failTimeout slowStart
        Peer peer1 = getPeer("api1", 60, false, 10, 5);
        Peer peer2 = getPeer("api2", 20, false, 10, 5);
        Peer peer3 = getPeer("api3", 20, false, 10, 5);
        Upstream upstream = new Upstream();
        upstream.setPeers(Arrays.asList(peer1, peer2, peer3));
        AtomicInteger api1Count = new AtomicInteger(0);
        AtomicInteger api2Count = new AtomicInteger(0);
        AtomicInteger api3Count = new AtomicInteger(0);
        AtomicBoolean urlIsNull = new AtomicBoolean(false);
        for (int i = 0; i < count; i++) {
            service.submit(()->{
                String url = upstream.getPeer().getServer();
                if("api1".equals(url)){
                    api1Count.incrementAndGet();
                }else if("api2".equals(url)){
                    api2Count.incrementAndGet();
                }else if("api3".equals(url)){
                    api3Count.incrementAndGet();
                }
                if(url == null){
                    urlIsNull.set(true);
                }
                UpstreamPeerContextHolder.finish(System.currentTimeMillis()%4 == 0? true : false );
                latch.countDown();
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {

        }
        Assert.assertFalse(urlIsNull.get());
        Assert.assertEquals(3, api1Count.get() * 1.0 /api2Count.get(), 0.00000001);
        service.shutdown();
    }

    @Test
    public void testAutoUpstream(){
        ExecutorService service = Executors.newFixedThreadPool(100);
        int count = 10000;
        CountDownLatch latch = new CountDownLatch(count);
        //server weight down maxFails failTimeout slowStart
        Peer peer1 = getPeer("api1", 50, false, 16, 8);
        Peer peer2 = getPeer("api2", 50, false, 16, 8);
        Upstream upstream = new Upstream();
        upstream.setPeers(Arrays.asList(peer1, peer2));
        AtomicInteger api1Count = new AtomicInteger(0);
        AtomicInteger api2Count = new AtomicInteger(0);
        long start = System.currentTimeMillis();
        List<FailConfig> fails = Arrays.asList(
                new FailConfig(5, 10, 5),
                new FailConfig(13, 17, 35),
                new FailConfig(22, 23, 5),
                new FailConfig(25, 31, 35),
                new FailConfig(40, 48, 5),
                new FailConfig(70, 100000, 35)

        );
        RateLimiter rateLimiter = RateLimiter.create(90);
        service.submit(()->{
            while (true){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                logger.info("upstream info: {}", JSON.toJSONString(upstream));
            }
        });
        Random random = new Random();
        for (int i = 0; i < count; i++) {
            service.submit(()->{
                rateLimiter.acquire();
                Peer peer = upstream.getPeer();
                String url = peer.getServer();
                UpstreamPeerContextHolder.setContextHolder(upstream, peer);
                if("api1".equals(url)){
                    api1Count.incrementAndGet();
                }else if("api2".equals(url)){
                    api2Count.incrementAndGet();
                }
                logger.info("peer: {}", url);
                long seconds = (System.currentTimeMillis() - start)/1000;
                boolean success = true;
                if("api1".equals(url)){
                    for (int j = 0; j < fails.size(); j++) {
                        FailConfig failConfig = fails.get(j);
                        if(failConfig.min <= seconds && failConfig.max >= seconds){
                            success = random.nextInt(100) > failConfig.percent;
                        }
                    }

                }
                logger.info("finish info: {} {}", url, success);
                UpstreamPeerContextHolder.finish(success);
                latch.countDown();
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
        }
        service.shutdown();
    }



    private Peer getPeer(String server, int weight, boolean down, int maxFails, int failTimeout){
        Peer peer = new Peer();
        peer.setServer(server);
        peer.setWeight(weight);
        peer.setDown(down);
        peer.setMaxFails(maxFails);
        peer.setFailTimeout(failTimeout);
        return peer;
    }

    class FailConfig{
        int min;
        int max;
        int percent; //百分比

        public FailConfig(int min, int max, int percent) {
            this.min = min;
            this.max = max;
            this.percent = percent;
        }
    }


//    @Test
//    public void testConfigProviderGateway(){
//        for (int i = 0; i < 100000; i++) {
//            String url = ApolloConfigurationCenterUtil.getProviderGateway(SodexoServiceProvider.NAME, "pay") ;
//            System.out.println(url);
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//
//        }
//    }
}
