package com.wosai.upay.workflow;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.model.Payment;
import com.wosai.upay.model.dao.Transaction;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Map;

import static org.junit.Assert.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class GuotongServiceProviderTest {

    @InjectMocks
    private GuotongServiceProvider guotongServiceProvider;

    @Before
    public void setup() {
        guotongServiceProvider = new GuotongServiceProvider();
    }

    @Test
    public void test_convertPromotionToPayment_discount() {
        // Given
        Map<String, Object> promotion = CollectionUtil.hashMap(
                "promotion_id", "DISCOUNT_001",
                "name", "满100减10",
                "scope", "GLOBAL",
                "type", "DISCOUNT",
                "amount", "1000",
                "activity_id", "ACT001",
                "wxpay_contribu", "500",
                "merchant_contribute", "500",
                "other_contribute", "0",
                "goods_detail", Arrays.asList(
                        CollectionUtil.hashMap(
                                "goods_id", "GOODS001",
                                "goods_remark", "测试商品",
                                "discount_amount", "1000",
                                "quantity", "1",
                                "price", "10000"
                        )
                )
        );

        // When
        Map<String, Object> result = guotongServiceProvider.convertPromotionToPayment(promotion);

        // Then
        assertNotNull(result);
        assertEquals(Payment.TYPE_DISCOUNT_CHANNEL_MCH, result.get(Transaction.PAYMENT_TYPE));
        assertEquals("DISCOUNT", result.get(Transaction.PAYMENT_ORIGIN_TYPE));
        assertEquals(500L, result.get(Transaction.PAYMENT_AMOUNT));
        assertEquals("DISCOUNT_001", result.get(Transaction.PAYMENT_SOURCE));
    }

    @Test
    public void test_convertPromotionToPayment_coupon() {
        // Given
        Map<String, Object> promotion = CollectionUtil.hashMap(
                "promotion_id", "COUPON_001",
                "name", "新人优惠券",
                "scope", "GLOBAL",
                "type", "COUPON",
                "amount", "2000",
                "activity_id", "ACT002",
                "wxpay_contribu", "2000",
                "merchant_contribute", "0",
                "other_contribute", "0",
                "goods_detail", Arrays.asList(
                        CollectionUtil.hashMap(
                                "goods_id", "GOODS002",
                                "goods_remark", "测试商品2",
                                "discount_amount", "2000",
                                "quantity", "1",
                                "price", "20000"
                        )
                )
        );

        // When
        Map<String, Object> result = guotongServiceProvider.convertPromotionToPayment(promotion);

        // Then
        assertNotNull(result);
        assertEquals(Payment.TYPE_DISCOUNT_CHANNEL, result.get(Transaction.PAYMENT_TYPE));
        assertEquals("COUPON", result.get(Transaction.PAYMENT_ORIGIN_TYPE));
        assertEquals(2000L, result.get(Transaction.PAYMENT_AMOUNT));
        assertEquals("COUPON_001", result.get(Transaction.PAYMENT_SOURCE));
    }

    @Test
    public void test_convertPromotionToPayment_invalid_type() {
        // Given
        Map<String, Object> promotion = CollectionUtil.hashMap(
                "promotion_id", "INVALID_001",
                "name", "无效类型优惠",
                "scope", "GLOBAL",
                "type", "INVALID_TYPE",
                "amount", "1000",
                "activity_id", "ACT003",
                "wxpay_contribu", "1000",
                "merchant_contribute", "0",
                "other_contribute", "0"
        );

        // When
        Map<String, Object> result = guotongServiceProvider.convertPromotionToPayment(promotion);

        // Then
        assertNull(result);
    }

    @Test
    public void test_convertPromotionToPayment_null_values() {
        // Given
        Map<String, Object> promotion = CollectionUtil.hashMap(
                "promotion_id", null,
                "type", null,
                "wxpay_contribu", null,
                "merchant_contribute", null,
                "other_contribute", null
        );

        // When
        Map<String, Object> result = guotongServiceProvider.convertPromotionToPayment(promotion);

        // Then
        assertNull(result);
    }
}