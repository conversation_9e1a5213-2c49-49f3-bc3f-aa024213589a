package com.wosai.upay.workflow;

import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @description
 * @date 2024-03-14
 */
@SuppressWarnings("ALL")
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest(ApolloConfigurationCenterUtil.class)
public class AbstractServiceProviderTest {
    @InjectMocks
    private AbstractServiceProvider serviceProvider = new HXBankServiceProvider();

    //上送poi信息
    private static String UPLOAD_LONGITUDE = "113.6833675";
    private static String UPLOAD_LATITUDE = "34.5307783";
    //门店poi信息
    private static String STORE_LONGITUDE = "117.1358377";
    private static String STORE_LATITUDE = "39.1187318";


    @Before
    public void setUp() {
        PowerMockito.mockStatic(ApolloConfigurationCenterUtil.class);
    }

    /**
     * 测试poi信息生成逻辑
     */
    @Test
    public void genTerminalInfoTestForPoi() {
        AbstractServiceProvider serviceProvider = new HXBankServiceProvider();
        when(ApolloConfigurationCenterUtil.isSendMerchantPoi()).thenReturn(true);

        Map<String, Object> transaction = initTransactionWithPoi();

        //上送poi有效，返回上送poi
        assertTrue(("+" + UPLOAD_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //上送poi值为null，返回门店poi
        Map uploadPoi = (Map) ((Map)transaction.get(Transaction.EXTRA_PARAMS)).get(Transaction.POI);
        uploadPoi.put(Transaction.LONGITUDE, null);
        assertTrue(("+" + STORE_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //上送poi值为0，返回门店poi
        uploadPoi.put(Transaction.LONGITUDE, "0");
        assertTrue(("+" + STORE_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //上送poi值为0.0，返回门店poi
        uploadPoi.put(Transaction.LONGITUDE, "0.0");
        assertTrue(("+" + STORE_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //上送poi值为0.0000000，返回门店poi
        uploadPoi.put(Transaction.LONGITUDE, "0.0000000");
        assertTrue(("+" + STORE_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //上送poi值含有字母，返回门店poi
        uploadPoi.put(Transaction.LONGITUDE, "4.9E-324");
        assertTrue(("+" + STORE_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //未上送poi，返回门店poi
        uploadPoi.remove(Transaction.LONGITUDE);
        uploadPoi.remove(Transaction.LATITUDE);
        assertTrue(("+" + STORE_LONGITUDE).startsWith(serviceProvider.genTerminalInfo(transaction).getFormatLongitude()));

        //上送poi无效，且门店poi也无效，返回Null
        Map config = (Map) ((Map)transaction.get(Transaction.CONFIG_SNAPSHOT));
        config.put(TransactionParam.LONGITUDE, "0.0000000");
        assertNull(serviceProvider.genTerminalInfo(transaction).getFormatLongitude());
    }

    @Test
    public void isPoiInfoInvalid() {
        assertTrue(serviceProvider.isPoiInfoInvalid(null));
        assertTrue(serviceProvider.isPoiInfoInvalid(""));
        assertTrue(serviceProvider.isPoiInfoInvalid("0"));
        assertTrue(serviceProvider.isPoiInfoInvalid("0."));
        assertTrue(serviceProvider.isPoiInfoInvalid("0.0"));
        assertTrue(serviceProvider.isPoiInfoInvalid("0.00000"));
        assertTrue(serviceProvider.isPoiInfoInvalid("0.0000000"));
        assertTrue(serviceProvider.isPoiInfoInvalid("0.000000000"));
        assertTrue(serviceProvider.isPoiInfoInvalid("4.9E"));

        assertFalse(serviceProvider.isPoiInfoInvalid("4.09"));
        assertFalse(serviceProvider.isPoiInfoInvalid("+9.53"));
        assertFalse(serviceProvider.isPoiInfoInvalid("-7.29"));
        assertFalse(serviceProvider.isPoiInfoInvalid("35.2086382"));
        assertFalse(serviceProvider.isPoiInfoInvalid("18.39281508266021"));
    }

    /**
     * 初始化携带poi信息的Transaction
     *
     * @return
     */
    private Map<String, Object> initTransactionWithPoi() {
        //初始化数据
        Map uploadPoi = new HashMap(4);
        Map extraParams = new HashMap(4);
        extraParams.put(Transaction.POI, uploadPoi);

        Map config = new HashMap(4);
        Map<String, Object> transaction = new HashMap<>(4);
        transaction.put(Transaction.CONFIG_SNAPSHOT, config);
        transaction.put(Transaction.EXTRA_PARAMS, extraParams);

        //初始化上送poi
        uploadPoi.put(Transaction.LONGITUDE, UPLOAD_LONGITUDE);
        uploadPoi.put(Transaction.LATITUDE, UPLOAD_LATITUDE);

        //初始化兜底的门店poi
        config.put(TransactionParam.LONGITUDE, STORE_LONGITUDE);
        config.put(TransactionParam.LATITUDE, STORE_LATITUDE);
        return transaction;
    }
}