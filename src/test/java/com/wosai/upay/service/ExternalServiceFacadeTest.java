package com.wosai.upay.service;

import com.wosai.upay.core.service.SupportService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;


/**
 * <AUTHOR> Date: 2020/2/14 Time: 9:52 上午
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ExternalServiceFacadeTest {

    @InjectMocks
    private ExternalServiceFacade facade;
    @Mock
    private SupportService supportService;
    @Mock
    private RedisTemplate redisTemplate;
    @Mock
    private ValueOperations valueOperations;


    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void isAllowAcrossStoreRefund() {
        PowerMockito.when(supportService.queryStatus(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        boolean result = facade.isAllowAcrossStoreRefund("123");
        Assert.assertTrue("isAllowAcrossStoreRefund用例失败", result);

        PowerMockito.when(supportService.queryStatus(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(0);
        result = facade.isAllowAcrossStoreRefund("123");
        Assert.assertFalse("isAllowAcrossStoreRefund用例失败", result);

        PowerMockito.when(supportService.queryStatus(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        result = facade.isAllowAcrossStoreRefund("123");
        Assert.assertTrue("isAllowAcrossStoreRefund用例失败", result);
    }

    @Test
    public void isAllowGenOrderSn() {
        PowerMockito.when(supportService.queryStatus(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        boolean result = facade.isAllowGenOrderSn("123");
        Assert.assertFalse("isAllowGenOrderSn用例失败", result);

        PowerMockito.when(supportService.queryStatus(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(0);
        result = facade.isAllowGenOrderSn("123");
        Assert.assertFalse("isAllowGenOrderSn用例失败", result);

        PowerMockito.when(supportService.queryStatus(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        result = facade.isAllowGenOrderSn("123");
        Assert.assertTrue("isAllowGenOrderSn用例失败", result);
    }

    @Test
    public void saveGeneratedOrderSn() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.doNothing().when(valueOperations).set(Mockito.anyString(), Mockito.any(), Mockito.anyLong(), Mockito.any());
        facade.saveGeneratedOrderSn("123");
    }

    @Test
    public void isContainsOrderSn() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.get(Mockito.anyString())).thenReturn("123");
        boolean result = facade.isContainsOrderSn("123");
        Assert.assertTrue("isContainsOrderSn用例失败", result);

        PowerMockito.when(valueOperations.get(Mockito.anyString())).thenReturn(null);
        result = facade.isContainsOrderSn("123");
        Assert.assertFalse("isContainsOrderSn用例失败", result);
    }
}