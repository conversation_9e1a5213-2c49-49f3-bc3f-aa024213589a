package com.wosai.upay.service;


import com.wosai.constant.UpayConstant;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.Workflow;
import com.wosai.upay.workflow.WorkflowManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import sun.swing.StringUIClientPropertyKey;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class SupportServiceWithBusinessConfigTest {
    @Autowired
    SupportService supportService;
    @Autowired
    UpayService upayService;
    @Autowired
    UpayOrderService upayOrderService;
    @Autowired
    WorkflowManager workflowManager;

    public static String feeRate = randomFeeRate();


    @Test
    public void testChangeTradeFeePayment() {
        //7894259268277709
        Map<String, Object> request = MapUtils.hashMap(
                SupportService.TERMINAL_SN, "21010290527248",
                SupportService.SN, "7894259268207390",
                UpayService.TSN, "7894259268207390",
                TransactionParam.FEE_RATE, feeRate);
        doTestChangeTradeFeePayment(request);
    }

    @Test
    public void testChangeTradeFeePaymentHbase() {
        Map<String, Object> request = MapUtils.hashMap(
                SupportService.TERMINAL_SN, "21010290497811",
                SupportService.SN, "7894259268999560",
                UpayService.TSN, "7894259268999560",
                TransactionParam.FEE_RATE, feeRate,
                UpayService.SCOPE, UpayConstant.SCOPE_HBASE);
        doTestChangeTradeFeePayment(request);
    }

    private void doTestChangeTradeFeePayment(Map request) {
        Map<String, Object> transaction = supportService.changeTradeFee(request);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException interruptedException) {
        }
        List<Map<String, Object>> transactions = upayOrderService.getOriginalTransactionListByOrderSn(MapUtil.getString(request, Order.SN));
        Optional<Map<String, Object>> any = transactions.stream().filter(o -> MapUtil.getString(o, DaoConstants.ID).equals(MapUtil.getString(transaction, DaoConstants.ID))).findAny();
        assert any.isPresent();

        Map<String, Object> updateTransaction = any.get();
        MpayServiceProvider mpayServiceProvider = workflowManager.matchServiceProvider(updateTransaction);
        String updateFeeRate = MapUtil.getString(mpayServiceProvider.getTradeParams(updateTransaction), TransactionParam.FEE_RATE);
        assert updateFeeRate.equals(feeRate);
    }


    @Test
    public void testChangeTradeFeeRefund() {
        Map<String, Object> request = MapUtils.hashMap(
                SupportService.TERMINAL_SN, "2101000330290533371",
                SupportService.SN, "7894259268207390",
                UpayService.TSN, "7894259268207577",
                TransactionParam.FEE_RATE, feeRate);
        doTestChangeTradeFeeRefund(request);
    }

    @Test
    public void testChangeTradeFeeRefundHbase() {
        Map<String, Object> request = MapUtils.hashMap(
                SupportService.TERMINAL_SN, "21010290497811",
                SupportService.SN, "7894259268999560",
                UpayService.TSN, "7894259268999567",
                TransactionParam.FEE_RATE, feeRate,
                UpayService.SCOPE, UpayConstant.SCOPE_HBASE);
        doTestChangeTradeFeeRefund(request);
    }

    private void doTestChangeTradeFeeRefund(Map request) {
        Map<String, Object> transaction = supportService.changeTradeFee(request);

        try {
            Thread.sleep(10000);
        } catch (InterruptedException interruptedException) {
        }
        List<Map<String, Object>> transactions = upayOrderService.getOriginalTransactionListByOrderSn(MapUtil.getString(request, Order.SN));
        Optional<Map<String, Object>> any = transactions.stream().filter(o -> MapUtil.getString(o, DaoConstants.ID).equals(MapUtil.getString(transaction, DaoConstants.ID))).findAny();
        assert any.isPresent();

        Map<String, Object> updateTransaction = any.get();
        MpayServiceProvider mpayServiceProvider = workflowManager.matchServiceProvider(updateTransaction);
        String updateFeeRate = MapUtil.getString(mpayServiceProvider.getTradeParams(updateTransaction), TransactionParam.FEE_RATE);
        assert updateFeeRate.equals(feeRate);
    }

    @Test
    public void TestChangeAll() {
        testChangeTradeFeePayment();
        testChangeTradeFeeRefund();

        testChangeTradeFeePaymentHbase();
        testChangeTradeFeeRefundHbase();
    }

    private static String randomFeeRate() {
        Random random = new Random(System.currentTimeMillis());
        int i = random.nextInt(5000);
        return StringUtils.cents2yuan(i);
    }
}
