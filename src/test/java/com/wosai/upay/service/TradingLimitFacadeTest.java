package com.wosai.upay.service;

import com.wosai.constant.UpayErrorScenesConstant;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.exception.UpaySingleTradeOverLimitError;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.workflow.MpayServiceProvider;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;

@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class TradingLimitFacadeTest {
    @Autowired
    TradingLimitFacade tradingLimitFacade;
    @SneakyThrows
    @Test
    public void checkTradeAppIdTradingLimit(){
        Method checkTradeAppIdTradingLimit = TradingLimitFacade.class.getDeclaredMethod("checkTradeAppIdTradingLimit", Map.class, MpayServiceProvider.class);
        checkTradeAppIdTradingLimit.setAccessible(true);
        Map<String, String> transaction = MapUtil.hashMap("merchant_id", "xxx", "config_snapshot", JsonUtil.jsonStringToObject("{\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"merchant_id\":\"05d4bedb-f6c2-4517-a601-ce2f1a04496c\",\"merchant_sn\":\"1680007021601\",\"merchant_name\":\"青岛0900汉堡工厂\",\"merchant_country\":\"CHN\",\"currency\":\"CNY\",\"longitude\":\"120.387946\",\"latitude\":\"36.321062\",\"district_code\":\"370214\",\"store_id\":\"728cd5d4-9f5f-489d-b940-00cb0f214bcb\",\"store_sn\":\"1580000007974799\",\"store_client_sn\":\"0900汉堡工厂\",\"store_name\":\"0900汉堡工厂\",\"store_city\":\"青岛市\",\"terminal_id\":\"46f11ad7-de88-495a-bc12-7342afb368ba\",\"terminal_sn\":\"100000330039712119\",\"terminal_name\":\"New Term (activateV2) 1723454019835\",\"terminal_vendor_app_appid\":\"2016111100000033\",\"terminal_category\":106,\"clearance_provider\":2,\"pay_status\":1,\"common_switch\":\"01000000000200002222222222222222\",\"sharing_switch\":1,\"merchant_daily_max_credit_limit_trans\":null,\"merchant_monthly_max_credit_limit_trans\":null,\"union_over_seas_wallet_single_tran_limit\":500,\"union_over_seas_wallet_day_tran_limit\":10000,\"payway_day_credit_limits\":null,\"payway_month_credit_limits\":null,\"is_need_refund_fee_flag\":null,\"hit_payway\":null,\"phone_pos_single_tran_limit\":900,\"phone_pos_day_tran_limit\":2000,\"provider\":1034,\"lakala_open_trade_params\":{\"liquidation_next_day\":true,\"version\":\"v3\",\"app_id\":\"***************\",\"seria_no\":\"017d509e7315\",\"private_key\":\"bcff2a24-4fd3-4d6a-af48-f8e6435e3d30\",\"channel_id\":\"28\",\"fee_rate\":\"0.00\",\"merc_id\":\"822452058120UQT\",\"term_id\":\"887027813524234240\",\"term_no\":\"L0578883\",\"bankcard_fee\":{\"edc\":{\"fee\":\"0\"}},\"fee_rate_tag\":{\"2\":\"485:\"},\"active\":true,\"fee\":0},\"term_info\":{\"term_id\":\"F1274793\",\"term_type\":null,\"serial_num\":null},\"term_id\":\"F1274793\",\"channel_name\":\"上海收钱吧互联网科技股份有限公司\",\"trade_app\":\"28\"}",Map.class), Transaction.ORIGINAL_AMOUNT, 90001);
        Exception e = null;
        try {
            checkTradeAppIdTradingLimit.invoke(tradingLimitFacade, transaction, null);
        } catch (Exception e1) {
            Field target = InvocationTargetException.class.getDeclaredField("target");
            target.setAccessible(true);
            e = (UpaySingleTradeOverLimitError) target.get(e1);
        }
        assert e!=null && e instanceof UpaySingleTradeOverLimitError;

    }
}
