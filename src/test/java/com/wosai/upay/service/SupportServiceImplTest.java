package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.util.RateLimiterUtil;
import com.wosai.upay.util.UpayUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;


/**
 * <AUTHOR> Date: 2020/3/9 Time: 4:43 下午
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({BeanUtil.class, RateLimiterUtil.class, UpayUtil.class})
public class SupportServiceImplTest {

    @InjectMocks
    private SupportServiceImpl supportService;
    @Mock
    private ExternalServiceFacade facade;
    @Mock
    private TsnGenerator tsnGenerator;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(BeanUtil.class);
        PowerMockito.mockStatic(RateLimiterUtil.class);
        PowerMockito.mockStatic(UpayUtil.class);
    }

    @Test
    public void genSn() {
        PowerMockito.when(facade.getBasicParams(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new HashMap(){{
                    put(MerchantConfig.MERCHANT_ID, "123");
                }});
        PowerMockito.when(facade.isAllowGenOrderSn(Mockito.any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(tsnGenerator.nextSn()).thenReturn("23333");
        PowerMockito.doNothing().when(facade).saveGeneratedOrderSn(Mockito.anyString());
        supportService.genSn(new HashMap<>());
    }

    @Test(expected = UpayBizException.class)
    public void genSnBizException() {
        PowerMockito.when(facade.getBasicParams(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new HashMap(){{
                    put(MerchantConfig.MERCHANT_ID, "123");
                }});
        PowerMockito.when(facade.isAllowGenOrderSn(Mockito.any())).thenReturn(Boolean.FALSE);
        PowerMockito.when(tsnGenerator.nextSn()).thenReturn("23333");
        PowerMockito.doNothing().when(facade).saveGeneratedOrderSn(Mockito.anyString());
        supportService.genSn(new HashMap<>());
    }
}