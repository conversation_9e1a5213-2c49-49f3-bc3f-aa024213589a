package com.wosai.upay.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.exception.UpayBizException;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.DateTimeUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> Date: 2020/2/12 Time: 3:25 下午
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LocalDateTime.class})
public class RefundProcessorTest {

    //@InjectMocks
    private RefundProcessor refundProcessor;
    @InjectMocks
    private ExternalServiceFacade facade;

    @Before
    public void setUp() throws Exception {
        //PowerMockito.when(facade.isAllowAcrossStoreRefund(Mockito.anyString())).thenReturn(false);
    }

    @Test
    public void acrossStoreRefundProcess() {
        refundProcessor.acrossStoreRefundProcess("223", "123", new HashMap<String, Object>(){{
            put("store_id", "123");
        }});
    }

    @Test(expected = UpayBizException.class)
    public void acrossStoreRefundProcessThrowErr() {
        refundProcessor.acrossStoreRefundProcess("223", "123", new HashMap<String, Object>(){{
            put("store_id", "223");
        }});
    }

    @Test
    public void fuyouTradingTimeAllowed() {
        Map<String, Object> transaction = MapUtil.hashMap(
                Transaction.PROVIDER, Order.PROVIDER_FUYOU,
                Transaction.CHANNEL_FINISH_TIME, LocalDateTimeUtil.toEpochMilli(LocalDateTime.of(LocalDate.now(), LocalTime.of(1, 0, 0)))
        );
        boolean result = refundProcessor.fuyouTradingTimeAllowed(transaction);
        System.out.println(result);
    }
    @Test
    public void setLiquidationNextDayConfig() throws Exception {
        Map<String, Object> transaction = MapUtil.hashMap(
                Transaction.PROVIDER, Order.PROVIDER_FUYOU,
                DaoConstants.CTIME, DateTimeUtil.valueOf("2024-06-22 00:00:00").getTime(),
                Transaction.CONFIG_SNAPSHOT, MapUtil.hashMap(TransactionParam.FUYOU_TRADE_PARAMS, MapUtil.hashMap(
                        TransactionParam.ACTIVE, true,
                        TransactionParam.LIQUIDATION_NEXT_DAY, false))
        );
        facade.setLiquidationNextDayConfig(transaction);
        String key = String.format("%s.%s.%s", Transaction.CONFIG_SNAPSHOT, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.LIQUIDATION_NEXT_DAY);
        Assert.assertTrue(BeanUtil.getPropBoolean(transaction, key));
    }
}