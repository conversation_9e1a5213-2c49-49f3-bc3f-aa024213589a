package com.wosai.upay.service;

import java.util.Map;
import java.util.concurrent.Executors;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.fsm.StateLabel;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.SupportUtil;
import com.wosai.upay.workflow.TransactionContext;

/**
 * 
 * 注意：该测试用例只能验证推送无报错，不能验证是否真正推送
 *
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "sun.security.*", "javax.net.*", "javax.net.ssl.*"}) //为了解决使用powermock后，提示classloader错误
public class ClientNotifierTest {
    public static final StateLabel SUCCESS = new StateLabel(Transaction.STATUS_SUCCESS, "SUCCESS");

    @Test
    public void test() throws InterruptedException {
        ClientNotifier clientNotifier = new ClientNotifier();
        ExternalServiceFacade serviceFacade = Mockito.mock(ExternalServiceFacade.class);
        Whitebox.setInternalState(clientNotifier, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(clientNotifier, "om", new ObjectMapper());
        Whitebox.setInternalState(clientNotifier, "executor", Executors.newScheduledThreadPool(10));
        PowerMockito.when(serviceFacade.getRsaKeyDataById(Mockito.anyString())).thenReturn(SupportUtil.RSA2_PRIVATE_KEY);
       
        Map<String, Object> order = JsonUtil.jsonStrToObject("{\n" + 
                "    \"id\": \"o7894259242124572\",\n" + 
                "    \"ctime\": 1599039246743,\n" + 
                "    \"sn\": \"7894259242124572\",\n" + 
                "    \"client_sn\": \"1599039243065\",\n" + 
                "    \"subject\": \"auto test case\",\n" + 
                "    \"body\": null,\n" + 
                "    \"status\": 1200,\n" + 
                "    \"tcp_modified\": false,\n" + 
                "    \"original_total\": 30,\n" + 
                "    \"net_original\": 30,\n" + 
                "    \"effective_total\": 30,\n" + 
                "    \"net_effective\": 30,\n" + 
                "    \"payway\": 1,\n" + 
                "    \"sub_payway\": 3,\n" + 
                "    \"merchant_id\": \"0040a3b45204-5ceb-fdb4-c8d5-02c14674\",\n" + 
                "    \"store_id\": \"1ce384ece391-7d6b-4f34-52a8-0f20bb8f\",\n" + 
                "    \"terminal_id\": \"620585fcd3a9-6eeb-f274-cb95-39fad5e5\",\n" + 
                "    \"operator\": null,\n" + 
                "    \"reflect\": null\n" + 
                "}", Map.class);
        Map<String, Object> transaction = JsonUtil.jsonStrToObject("{\n" + 
                "    \"id\": \"t7894259242124572\",\n" + 
                "    \"tsn\": \"7894259242124572\",\n" + 
                "    \"client_tsn\": \"1599039243065\",\n" + 
                "    \"type\": 30,\n" + 
                "    \"subject\": \"auto test case\",\n" + 
                "    \"body\": null,\n" + 
                "    \"status\": 2000,\n" + 
                "    \"original_amount\": 30,\n" + 
                "    \"effective_amount\": 30,\n" + 
                "    \"merchant_id\": \"0040a3b45204-5ceb-fdb4-c8d5-02c14674\",\n" + 
                "    \"store_id\": \"1ce384ece391-7d6b-4f34-52a8-0f20bb8f\",\n" + 
                "    \"terminal_id\": \"620585fcd3a9-6eeb-f274-cb95-39fad5e5\",\n" + 
                "    \"operator\": null,\n" + 
                "    \"payway\": 1,\n" + 
                "    \"sub_payway\": 3,\n" + 
                "    \"order_id\": \"o7894259242124572\",\n" + 
                "    \"order_sn\": \"7894259242124572\",\n" + 
                "    \"extra_params\": {\n" + 
                "        \"payer_uid\": \"ov\"\n" + 
                "    },\n" + 
                "    \"extended_params\": null,\n" + 
                "    \"reflect\": null,\n" + 
                "    \"config_snapshot\": {\n" + 
                "        \"store_id\": \"1ce384ece391-7d6b-4f34-52a8-0f20bb8f\",\n" + 
                "        \"store_sn\": \"st-1580000000144406\",\n" + 
                "        \"store_city\": \"苏州市\",\n" + 
                "        \"is_sent_store_id\": true,\n" + 
                "        \"merchant_name\": \"testcase测试商户\",\n" + 
                "        \"store_client_sn\": \"88297761728900\",\n" + 
                "        \"merchant_id\": \"0040a3b45204-5ceb-fdb4-c8d5-02c14674\",\n" + 
                "        \"merchant_sn\": \"mch-1680000810456\",\n" + 
                "        \"merchant_daily_max_sum_of_trans\": \"2500000\",\n" + 
                "        \"alipay_wap_trade_params\": {\n" + 
                "            \"fee_rate\": \"0.6\",\n" + 
                "            \"liquidation_next_day\": false,\n" + 
                "            \"partner\": \"*****partner****\",\n" + 
                "            \"app_key\": \"*****app_key****\",\n" + 
                "            \"agent_id\": \"10610050a1\",\n" + 
                "            \"merchant_alipay_industry\": null\n" + 
                "        },\n" + 
                "        \"refund_non_sqb_order\": true,\n" + 
                "        \"terminal_name\": \"麻辣E族\",\n" + 
                "        \"vendor_id\": \"b52600e36100-3ce9-5e11-99fa-f5f9d958\",\n" + 
                "        \"merchant_country\": \"CN\",\n" + 
                "        \"store_name\": \"just a test002\",\n" + 
                "        \"deposit\": {\n" + 
                "            \"weixin\": 1,\n" + 
                "            \"alipay\": 1\n" + 
                "        },\n" + 
                "        \"currency\": \"CNY\",\n" + 
                "        \"terminal_id\": \"620585fcd3a9-6eeb-f274-cb95-39fad5e5\",\n" + 
                "        \"terminal_sn\": \"tsn-1000001525774\"\n" + 
                "    }\n" + 
                "}", Map.class);

        
        TransactionContext context = new TransactionContext(order, transaction);
        clientNotifier.notify("https://upay-gateway.beta.iwosai.com", context);
        
        context.setApiVer(1);
        clientNotifier.notify("https://upay-gateway.beta.iwosai.com", context);
        
        Thread.sleep(2000);
        
    }

}
