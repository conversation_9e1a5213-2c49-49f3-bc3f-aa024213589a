package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.model.dao.Order;
import com.wosai.upay.model.dao.Transaction;
import com.wosai.upay.util.RateLimiterUtil;
import com.wosai.upay.util.UpayUtil;
import com.wosai.upay.workflow.MpayServiceProvider;
import com.wosai.upay.workflow.TransactionContext;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: This is a description of the class.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/4/3
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({BeanUtil.class, RateLimiterUtil.class, UpayUtil.class})
public class UpayServiceImplTest {

    @InjectMocks
    private UpayServiceImpl upayService;

    @Test
    public void testDirect() throws Exception {
        int paywayCode = 3;
        Map<String, Object> transaction = MapUtil.hashMap(
                Transaction.PAYWAY, paywayCode,
                Transaction.EXTRA_OUT_FIELDS, new HashMap<>(),
                Transaction.CONFIG_SNAPSHOT, MapUtil.hashMap(
                        TransactionParam.DEPOSIT, MapUtil.hashMap(TransactionParam.DEPOSIT_WX, 1, TransactionParam.DEPOSIT_SQB, 1, TransactionParam.DEPOSIT_ALI, 1)
                )
        );
        String depositType = getDepositType(paywayCode, transaction, new MockServiceProvider());
        Assert.assertEquals(depositType, TransactionParam.DEPOSIT_WX);
    }

    @Test
    public void testLkl() throws Exception {
        int paywayCode = 2;
        Map<String, Object> transaction = MapUtil.hashMap(
                Transaction.PAYWAY, paywayCode,
                Transaction.EXTRA_OUT_FIELDS, new HashMap<>(),
                Transaction.CONFIG_SNAPSHOT, MapUtil.hashMap(
                        TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS, new HashMap<>(),
                        TransactionParam.DEPOSIT, MapUtil.hashMap(TransactionParam.DEPOSIT_WX, 1, TransactionParam.DEPOSIT_SQB, 1, TransactionParam.DEPOSIT_ALI, 1)
                )
        );
        String depositType = getDepositType(paywayCode, transaction, new LklServiceProvider());
        Assert.assertEquals(depositType, TransactionParam.DEPOSIT_ALI);
    }

    @Test
    public void testSqb() throws Exception {
        int paywayCode = 2;
        Map<String, Object> transaction = MapUtil.hashMap(
                Transaction.PAYWAY, paywayCode,
                Transaction.EXTRA_OUT_FIELDS, new HashMap<>(),
                Transaction.CONFIG_SNAPSHOT, MapUtil.hashMap(
                        TransactionParam.DEPOSIT, MapUtil.hashMap(TransactionParam.DEPOSIT_WX, 1, TransactionParam.DEPOSIT_SQB, 1, TransactionParam.DEPOSIT_ALI, 1)
                )
        );
        String depositType = getDepositType(paywayCode, transaction, new LklServiceProvider());
        Assert.assertEquals(depositType, TransactionParam.DEPOSIT_SQB);
    }

    private String getDepositType(int paywayCode, Map<String, Object> transaction, MpayServiceProvider serviceProvider) throws Exception {
        // 使用反射获取私有方法
        Method getDepositTypeMethod = UpayServiceImpl.class.getDeclaredMethod("getDepositType", int.class, Map.class, MpayServiceProvider.class);
        getDepositTypeMethod.setAccessible(true);
        // 调用私有方法
        return (String) getDepositTypeMethod.invoke(upayService, paywayCode, transaction, serviceProvider);
    }

    public static class LklServiceProvider extends MockServiceProvider {
        @Override
        public Integer getProvider() {
            return Order.PROVIDER_LAKALA_UNION_PAY;
        }
    }

    public static class MockServiceProvider implements MpayServiceProvider {
        @Override
        public String getName() {
            return null;
        }

        @Override
        public Integer getProvider() {
            return null;
        }

        @Override
        public boolean canHandle(Map<String, Object> transaction) {
            return false;
        }

        @Override
        public Map<String, Object> getTradeParams(Map<String, Object> transaction) {
            return null;
        }

        @Override
        public String pay(TransactionContext context, boolean resume) {
            return null;
        }

        @Override
        public String cancel(TransactionContext context) {
            return null;
        }

        @Override
        public String query(TransactionContext context) {
            return null;
        }

        @Override
        public String refund(TransactionContext context) {
            return null;
        }

        @Override
        public String precreate(TransactionContext context, boolean resume) {
            return null;
        }

        @Override
        public String explainNotification(Map<String, Object> providerNotification) {
            return null;
        }

        @Override
        public String depositQuery(TransactionContext context) {
            return null;
        }

        @Override
        public String depositFreeze(TransactionContext context, boolean resume) {
            return null;
        }

        @Override
        public String depositPreFreeze(TransactionContext context, boolean resume) {
            return null;
        }

        @Override
        public String depositCancel(TransactionContext context) {
            return null;
        }

        @Override
        public String depositConsume(TransactionContext context) {
            return null;
        }
    }
}
