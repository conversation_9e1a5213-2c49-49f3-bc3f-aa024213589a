package com.wosai.fsm;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class StateTest {

    @Test
    public void test() {
        StateLabel start = new StateLabel(1, "start");
        StateLabel next = new StateLabel(2, "next");
        StateLabel end = new StateLabel(3, "end");

        
        State other = new State(start , new Action() {
            
            @Override
            public String invoke(MachineContext context, boolean resume) {
                return "glb";
            }
        });
        State state = new State(other);
        state.setTransition("start", start);
        state.setTransition("next", next);
        state.setTransition("end",end);
        assertNotNull(state.targetStateLabels());

        
        state.nextState("next");
        state.getTransitionMap();
        state.toString();
        state.setTransitionMap(null);
        assertEquals(start, state.getLabel());
        
        assertEquals(false, state.isEnd());
        state.setEnd(true);
        assertEquals(true, state.isEnd());
        
        assertEquals("glb", state.getAction().invoke(null, false));
        state.setAction((context, resume) -> "new-glb");
        assertEquals("new-glb", state.getAction().invoke(null, false));

        assertEquals(false, state.isDelayed());
        long delays[] = {1L, 2L, 3L};
        state.setDelays(delays);
        assertNotNull(state.isDelayed());
        assertEquals(true, state.isDelayed());
        assertNotNull(state.getDelays());

        
        assertNull(state.getGiveUpResult());
        state.setGiveUpResult("fail");
        assertEquals("fail", state.getGiveUpResult());

        assertEquals(false, state.isInproc());
        state.setInproc(true);
        assertEquals(true, state.isInproc());

    }

}
