package com.wosai.fsm;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class StateLabelTest {

    @Test
    public void test() {
        StateLabel test = new StateLabel(99991, "test");
        assertEquals(StateLabel.fromId(99991), test);
        test.setId(99992);
        assertNull(StateLabel.fromId(99992));
        test.setName("new-test");
        assertEquals(false, test.equals(null));
        test.toString();
        test.hashCode();
        test.getName();
        
        StateLabel test2 = new StateLabel(99993, "test");
        test2.setId(99992);
        assertEquals(true, StateLabel.fromId(99991).equals(test2));

        test2.setId(99991);
        assertEquals(false, StateLabel.fromId(99991).equals(test2));

        assertEquals(false, StateLabel.fromId(99991).equals(Integer.valueOf(10)));

    }

}
