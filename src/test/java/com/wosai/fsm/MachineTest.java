package com.wosai.fsm;

import static org.junit.Assert.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import org.junit.Test;

public class MachineTest {

    @Test
    public void test() throws InterruptedException {
        final Random random = new Random();
        long[] delays = {1000, 1000, 2000, 2000, 3000};
        MachineBuilder builder = new MachineBuilder();
        final StateLabel CREATED = new StateLabel(1, "CREATED");
        final StateLabel SUCCESS = new StateLabel(2, "SUCCESS");
        final StateLabel UNKNOWN = new StateLabel(3, "UNKNOWN");
        final StateLabel ERROR_RECOVERY = new StateLabel(4, "ERROR_RECOVERY");
        final StateLabel ERROR_CANCELED = new StateLabel(5, "ERROR_CANCELED");
        final StateLabel ERROR_UNKNOWN = new StateLabel(6, "ERROR_UNKNOWN");
        final StateLabel IN_PROG = new StateLabel(7, "IN_PROG");
        
        builder.on(CREATED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                System.out.println("submit order");
                int dice = random.nextInt(4);
                String[] outcomes = {"success", "error", "inprog", "ioex" };
                return outcomes[dice];
            }
        }).transition("success", SUCCESS)
          .transition("ioex", UNKNOWN)
          .transition("error", ERROR_RECOVERY)
          .transition("inprog", IN_PROG)
        .on(SUCCESS).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                System.out.println("mark order success");
                return null;
            }
        }).end()
        .on(UNKNOWN).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                System.out.println("mark order unknown");
                return null;
            }
        }).end()
        .on(ERROR_RECOVERY).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                System.out.println("cancel order with error encountered");
                int dice = random.nextInt(2);
                String[] outcomes = {"success", "ioex" };
                return outcomes[dice];
            }
        }).transition("success", ERROR_CANCELED)
          .transition("ioex", ERROR_UNKNOWN)
        .on(ERROR_CANCELED).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                System.out.println("mark order ERROR_CANCELED");
                return null;
            }
        }).end()
        .on(ERROR_UNKNOWN).invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                System.out.println("mark order ERROR_UNKNOWN");
                return null;
            }
        }).end()
        .on(IN_PROG).delay(delays,  "timeout").invoke(new Action() {
            @Override
            public String invoke(MachineContext context, boolean resume) {
                int dice = random.nextInt(10);
                switch(dice) {
                case 0:
                    return "success";
                case 1:
                    return "ioex";
                case 2:
                    return "error";
                default:
                    return "inprog";
                }
            }
        }).transition("success", SUCCESS)
          .transition("ioex", UNKNOWN)
          .transition("error", ERROR_RECOVERY)
          .transition("timeout", ERROR_RECOVERY)
          .transition("inprog", IN_PROG);


        Machine machine = builder.build();
        StateLabel start = CREATED;
        MachineContext context = new MachineContext(machine, start);
        State startState = machine.getState(start);
        String outcome = startState.getAction().invoke(context, false);
        StateLabel currentLabel = start;
        Map<StateLabel, Integer> stateCount = new HashMap<StateLabel, Integer>();
        while(true) {
            State newState = machine.transition(currentLabel, outcome);
            currentLabel = newState.getLabel();
            
            context.setCurrentStateLabel(currentLabel);
            System.out.println("entering state " + currentLabel);
            if (newState.getAction()!=null) {
                if (newState.isDelayed()) {
                    Integer iter = stateCount.get(currentLabel);
                    if (iter == null) {
                        iter = 0;
                    }
                    stateCount.put(currentLabel, iter+1);

                    if (iter < newState.getDelays().length) {
                        Thread.sleep(newState.getDelays()[iter]);
                        System.out.println("delayed iter " + iter);
                    }else{
                        outcome = newState.getGiveUpResult();
                        System.out.println("leaving state with giveup result " + outcome);
                        continue;
                    }
                }
                outcome = newState.getAction().invoke(context, false);
                System.out.println("levaing state with outcome " + outcome);
            }
            
            if (newState.isEnd()) {
                break;
            }

        }

        System.out.println("FINAL: " + currentLabel);
    }

}
