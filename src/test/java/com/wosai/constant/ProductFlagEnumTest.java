package com.wosai.constant;

import static org.junit.Assert.assertEquals;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;

import org.junit.Test;

public class ProductFlagEnumTest {

    @Test
    public void test() throws ClassNotFoundException, NoSuchMethodException, SecurityException, InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException, NoSuchFieldException {
        ProductFlagEnum.DBB.getDesc();
        ProductFlagEnum.DBB.getCode();
        ProductFlagEnum.DBB.getKey();
        assertEquals(false, ProductFlagEnum.DBB.contain(null));
        assertEquals(true, ProductFlagEnum.DBB.contain("dbb"));
        
        ProductFlagEnum flag = ProductFlagEnum.JJZ_WM;
        Class<?> clazz = ProductFlagEnum.class;  
        Field  field = clazz.getDeclaredField("bizFlags");  
        field.setAccessible(true);
        field.set(flag, null);
        assertEquals(false, flag.contain("jjz"));
        field.set(flag, new HashSet<>(Arrays.asList(
                "jjz"
                )));
        assertEquals(true, flag.contain("jjz"));
        field.set(flag, Collections.emptySet());
        assertEquals(false, flag.contain("jjz"));

    }

}
