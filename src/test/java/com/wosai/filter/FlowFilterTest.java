package com.wosai.filter;

import static org.junit.Assert.assertEquals;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import com.google.common.util.concurrent.RateLimiter;
import com.wosai.upay.util.ApolloConfigurationCenterUtil;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({ApolloConfigurationCenterUtil.class, }) // 所有需要测试的类列在此处，适用于模拟final类或有final, private, static, native方法的类
public class FlowFilterTest {

    @Test
    public void test() throws IOException, ServletException {
        PowerMockito.mockStatic(ApolloConfigurationCenterUtil.class);
        FlowFilter flowFilter = new FlowFilter();
        flowFilter.init(null);
        // fake请求开关关闭，不允许有fake请求
        MockHttpServletRequest servletRequest = new MockHttpServletRequest();
        servletRequest.addHeader("fake", "1");
        MockHttpServletResponse servletResponse = new MockHttpServletResponse();
        flowFilter.doFilter(servletRequest, servletResponse, new FilterChain() {
            
            @Override
            public void doFilter(ServletRequest request, ServletResponse response) throws IOException, ServletException {
            }
        });
        assertEquals("{\"result_code\":\"200\",\"biz_response\":{\"result_code\":\"REQUEST_FAIL\",\"error_message\":\"禁止fake数据进入\"}}", servletResponse.getContentAsString());
        
        // fake请求打开，限流
        servletResponse = new MockHttpServletResponse();
        PowerMockito.when(ApolloConfigurationCenterUtil.getFakeRequestEnable()).thenReturn(true);
        PowerMockito.when(ApolloConfigurationCenterUtil.getRateLimiterEnable()).thenReturn(true);
        ApolloConfigurationCenterUtil.rateLimiter = RateLimiter.create(0.001);
        ApolloConfigurationCenterUtil.rateLimiter.tryAcquire();

        flowFilter.doFilter(servletRequest, servletResponse, new FilterChain() {
            
            @Override
            public void doFilter(ServletRequest request, ServletResponse response) throws IOException, ServletException {
            }
        });
        assertEquals("{\"result_code\":\"200\",\"biz_response\":{\"result_code\":\"REQUEST_FAIL\",\"error_message\":\"服务器正忙,请稍后重试\"}}", servletResponse.getContentAsString());
        
        // fake请求打开，无限流
        servletResponse = new MockHttpServletResponse();
        ApolloConfigurationCenterUtil.rateLimiter = RateLimiter.create(500);
        flowFilter.doFilter(servletRequest, servletResponse, new FilterChain() {
            
            @Override
            public void doFilter(ServletRequest request, ServletResponse response) throws IOException, ServletException {
                PrintWriter out = response.getWriter();
                out.print("success");
                out.flush();
            }
        });
        assertEquals("success", servletResponse.getContentAsString());
        
        flowFilter.destroy();
        flowFilter.destroy();
        flowFilter.destroy();
    }

}
