### 勾兑-预授权完成
POST {{host}}/upay/v2/deposit/fixConsumeToSuccess
Content-Type: application/json

{
  "sn": "7894355726069970",
  "tsn": "7894259294360572",
  "terminal_sn": "{{terminal_sn}}"
}

### 勾兑-退款
POST {{host}}/upay/v2/fixCancelOrRefund
Content-Type: application/json

{
  "sn": "7894259294728978",
  "tsn": "7894259294728960",
  "terminal_sn": "{{terminal_sn}}"
}

### 支付
POST {{host}}/upay/v2/pay
Content-Type: application/json

{
  "subject": "丸辣BBQ啦",
  "client_ip": "*************",
  "client_sn": "24C135A1FDE2A60005",
  "dynamic_id": "288846961743051438",
  "terminal_sn": "{{terminal_sn}}",
  "total_amount": "1"
}

### 退款
POST {{host}}/upay/v2/refund
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "client_sn": "24C135A1FDE2A60005",
  "refund_request_no": "123",
  "refund_amount": "1"
}
### 撤单
POST {{host}}/upay/v2/cancel
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "sn": "7894259299794540"
}

### 微信预下单
POST {{host}}/upay/v2/precreate
Content-Type: application/json

{
  "payway": "1",
  "sub_payway": "3",
  "client_sn": "173137411723067889384",
  "total_amount": "1",
  "terminal_sn": "{{terminal_sn}}",
  "subject": "丸辣BBQ啦",
  "payer_uid": "2088902686835971"
}

### 支付宝预下单
POST {{host}}/upay/v2/precreate
Content-Type: application/json

{
  "payway": "1",
  "sub_payway": "3",
  "client_sn": "17313741172306788002",
  "total_amount": "1",
  "terminal_sn": "{{terminal_sn}}",
  "subject": "丸辣BBQ啦",
  "payer_uid": "2088902686835971"
}

#### 查单
POST {{host}}/upay/v2/query
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "sn": "7894259299779570",
  "refund_request_no": ""
}

### 回调通知
POST http://pay-apisix.beta.iwosai.com/upay/v2/notify/jsb/9fea76e75de97116fb2c486095e2b0f3/6ba51
Content-Type: application/json

{
  "sn": "7894259299794540"
}


### 清除商户交易参数缓存
POST http://core-business.beta.iwosai.com/rpc/support
Content-Type: application/json

{
  "method": "removeCachedParams",
  "params": [
    "21690003845640"
  ],
  "id": "1"
}

###

