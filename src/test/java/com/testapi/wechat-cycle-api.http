### 清除商户交易参数缓存
POST http://core-business.beta.iwosai.com/rpc/support
Content-Type: application/json

{
  "method": "removeCachedParams",
  "params": [
    "**************"
  ],
  "id": "1"
}

### 请求签约-小程序
POST {{host}}/upay/support/auth
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "payway": 3,
  "sub_payway": 4,
  "sqb_product_code": "WEIXIN_CYCLE",
  "extended": {
    "sub_appid": "{{sub_appid}}",
    "plan_id": "{{plan_id}}",
    "contract_code": "{{contract_code}}",
    "request_serial": {{$timestamp}},
    "contract_display_account": "小明",
    "notify_url": "https://test-callback-kaci.shouqianba.com/trade/autopay/wx/contracts/notify"
  }
}

### 签约查询
POST {{host}}/upay/support/authQuery
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "payway": 3,
  "sub_payway": 4,
  "sqb_product_code": "WEIXIN_CYCLE",
  "extended": {
    "sub_appid": "{{sub_appid}}",
    "plan_id": "{{plan_id}}",
    "contract_code": "{{contract_code}}"
  }
}

### 申请解约
POST {{host}}/upay/support/authTerminate
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "payway": 3,
  "sub_payway": 4,
  "sqb_product_code": "WEIXIN_CYCLE",
  "extended": {
    "sub_appid": "{{sub_appid}}",
    "plan_id": "{{plan_id}}",
    "contract_code": "{{contract_code}}",
    "contract_termination_remark": "签约信息有误，须重新签约"
  }
}


### 预授权接口
POST {{host}}/upay/v2/deposit/precreate
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "client_sn": "{{$timestamp}}",
  "payway": "3",
  "sub_payway": "4",
  "total_amount": "10",
  "subject": "预授权测试",
  "operator": "system",
  "extended": {
    "sqb_product_code": "WEIXIN_CYCLE",
    "delay_trade": false,
    "sub_appid": "{{sub_appid}}",
    "contract_id": "{{contract_id}}",
    "deduct_duration": {
      "count": 7,
      "unit": "DAY"
    },
    "estimated_amount": {
      "amount": 10,
      "currency": "CNY"
    }
  }
}

### 扣款
POST {{host}}/upay/v2/deposit/consume
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "sn": "7894259294392079",
  "client_sn": "{{$timestamp}}",
  "payway": "3",
  "sub_payway": "4",
  "operator": "system",
  "consume_amount": "10",
  "extended": {
    "sqb_product_code": "WEIXIN_CYCLE",
    "sub_appid": "{{sub_appid}}",
    "contract_id": "{{contract_id}}",
    "client_ip": "************",
    "notify_url": "https://test-callback-kaci.shouqianba.com/trade/autopay/wx/contracts/notify"
  }
}

### 退款
POST {{host}}/upay/v2/refund
Content-Type: application/json

{
  "terminal_sn": "{{terminal_sn}}",
  "client_sn": "1747276213",
  "refund_request_no": "123",
  "refund_amount": "10",
  "extended": {
    "sqb_product_code": "WEIXIN_CYCLE",
    "sub_appid": "{{sub_appid}}"
  }
}

