### upay-gateway testcase列表

#### /upay/admin 管理类接口

##### AdminControllerTest

| 用例名称            | 用例说明 | 测试接口                        |
| ------------------- | -------- | :------------------------------ |
| health              |          | /upay/admin/health              |
| backupHealth        |          | /upay/admin/backupHealth        |
| getServiceConfig    |          | /upay/admin/getServiceConfig    |
| changeServiceConfig |          | /upay/admin/changeServiceConfig |



#### /upay/qrcode 生成二维码接口

##### QrcodeControllerTest

| 用例名称 | 用例说明 | 测试接口     |
| -------- | -------- | :----------- |
| qrcode   | 正常调用 | /upay/qrcode |



#### /upay/support 配置相关接口

##### SupportControllerTest

| 用例名称                               | 用例说明           | 测试接口                      |
| -------------------------------------- | ------------------ | :---------------------------- |
| test_query_user_id_with_fail_payway    | 不支持的payway     | /upay/support/queryUserId     |
| test_query_user_id_not_support         | 交易参数不支持     | /upay/support/queryUserId     |
| test_query_user_id_with_weixin_success | 微信查询返回成功   | /upay/support/queryUserId     |
| test_query_user_id_with_weixin_fail    | 微信查询返回失败   | /upay/support/queryUserId     |
| test_query_user_id_with_alipay_success | 支付宝查询返回成功 | /upay/support/queryUserId     |
| test_query_user_id_with_alipay_fail    | 支付宝查询返回失败 | /upay/support/queryUserId     |
| test_clear_basic_cache                 | 清理商户缓存接口   | /upay/support/clearBasicCache |



#### /upay/v2 交易相关接口

##### 公用测试用例 CommonTest

| 用例名称                      | 用例说明           | 测试接口      |
| ----------------------------- | ------------------ | :------------ |
| echo                          |                    | /upay/v2/echo |
| test_invalid_params           | 必填参数不能为空   | /upay/v2/pay  |
| test_fail_call_coreb          | 交易时连不上core-b | /upay/v2/pay  |
| test_terminal_not_exists      | 终端不存在         | /upay/v2/pay  |
| test_terminal_not_activated   | 终端未激活         | /upay/v2/pay  |
| test_terminal_status_disabled | 终端已禁用         | /upay/v2/pay  |
| test_terminal_status_unknown  | 终端状态异常       | /upay/v2/pay  |

##### 下单公用测试用例 PayCommonTest

| 用例名称          | 用例说明         | 测试接口     |
| ----------------- | ---------------- | :----------- |
| test_error_payway | 上送错误的payway | /upay/v2/pay |
|                   |                  |              |

##### 支付宝直连1.0 AlipayV1PayTest

| 用例名称                               | 用例说明                                  | 测试接口     |
| -------------------------------------- | ----------------------------------------- | :----------- |
| test_barcode_pay_success_without_query | 支付宝1.0 B2C下单支付成功                 | /upay/v2/pay |
| test_barcode_pay_success_with_query    | 支付宝1.0 B2C下单返回支付中，查单返回成功 | /upay/v2/pay |
|                                        |                                           |              |

##### 退款公用测试用例 RefundCommonTest

| 用例名称                           | 用例说明                                     | 测试接口                                                     |
| ---------------------------------- | -------------------------------------------- | :----------------------------------------------------------- |
| test_non_sqb_order_refund          | 跨服务商主体退款                             | /upay/v2/refund                                              |
| test_non_sqb_order_refund_fix_fail | 勾兑跨服务商主体退款交易为退款失败并再次退款 | /upay/v2/refund<br />/upay/v2/fixOrderStatusIfRefundNotSuccess |
| test_non_sqb_order_refund_fix      | 勾兑跨服务商主体退款交易为退款成功           | /upay/v2/refund<br />/upay/v2/fixCancelOrRefund              |

##### 

#### /upay/v2/deposit 预授权相关接口

##### 支付宝直连2.0 AlipayV2PreFreezeTest

| 用例名称                           | 用例说明                          | 测试接口                   |
| ---------------------------------- | --------------------------------- | :------------------------- |
| test_prefreeze_success_with_query  | 线上预授权冻结成功 - 自动查询方式 | /upay/v2/deposit/prefreeze |
| test_prefreeze_success_with_notify | 线上预授权冻结成功 - 结果通知方式 | /upay/v2/deposit/prefreeze |
| test_prefreeze_consume_success     | 线上预授权完成成功                | /upay/v2/deposit/prefreeze |

