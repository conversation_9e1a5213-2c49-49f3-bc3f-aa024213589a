# upay-gateway

## 部署

* 下载jetty8版本 <http://download.eclipse.org/jetty/8.1.17.v20150415/dist/jetty-distribution-8.1.17.v20150415.zip>， 并解压。
* 新增upay.xml文件，文件内容如下,并把此文件放入到contexts目录下

```
<?xml version="1.0"  encoding="ISO-8859-1"?>
<!DOCTYPE Configure PUBLIC "-//Jetty//Configure//EN" "http://www.eclipse.org/jetty/configure.dtd">


<Configure class="org.eclipse.jetty.webapp.WebAppContext">

  <Set name="contextPath">/</Set>
  <Set name="war"><SystemProperty name="jetty.home" default="."/>/webapps/upay.war</Set>

</Configure>


```

* 打包项目war包，把war包改名为upay.war放入到webapps下面

* 启动项目

```
java  -Djetty.port=11112  -jar /app/bin/upay/jetty/start.jar

```

* 按照需要，配置相应的supervisor,以及nginx


## 紧急关闭支付通道

* 获取所有支付通道的状态

  ```

  # 1: 支付宝，2:支付宝2.0 3: 微信，4: 百付宝，5: 京东，6: QQ钱包 1001:兴业银行 1002: 拉卡拉

  $ curl http://localhost:9966/upay-gateway/upay/provider/status

  {"1":{"message":null,"percentage":100},"1001":{"message":null,"percentage":100},"1002":{"message":null,"percentage":100},"2":{"message":null,"percentage":100},"3":{"message":null,"percentage":100},"4":{"message":null,"percentage":100},"5":{"message":null,"percentage":100},"6":{"message":null,"percentage":100}}

  ```

* 紧急关闭支付通道

  ```
  # 关闭线上的兴业银行通道

  $ curl -X POST -H "Content-Type: application/json" -v http://localhost:9966/upay-gateway/upay/provider/update  -d @- <<EOF

      {
      "1001":{
      "percentage":"0",
      "message":"由于系统服务不稳定, 支付受理暂停30分钟。"
      }
    }
  EOF


  {"1":{"message":null,"percentage":100},"1001":{"percentage":"0","message":"由于系统服务不稳定, 支付受理暂停30分钟。"},"1002":{"message":null,"percentage":100},"2":{"message":null,"percentage":100},"3":{"message":null,"percentage":100},"4":{"message":null,"percentage":100},"5":{"message":null,"percentage":100},"6":{"message":null,"percentage":100}}

  ```

* 随机拒绝支付请求

  ```
  # 随机拒绝70%的支付宝线上交易请求（30%会被接受）

  $ curl -X POST -H "Content-Type: application/json" http://localhost:9966/upay-gateway/upay/provider/update -d @- <<EOF
  {"1":{"message":"系统繁忙，请稍后再试","percentage":30}}
  EOF

  {"1":{"message":"系统繁忙，请稍后再试","percentage":30},"1001":{"percentage":"0","message":"由于系统服务不稳定, 支付受理暂停30分钟。"},"1002":{"message":null,"percentage":100},"2":{"message":null,"percentage":100},"3":{"message":null,"percentage":100},"4":{"message":null,"percentage":100},"5":{"message":null,"percentage":100},"6":{"message":null,"percentage":100}}

  ```

  ```
  # 支付失败
  $ curl -X POST -H "Content-Type: application/json" http://s1-upay-001:11112/upay/v2/precreate -d @- <<EOF
  {
    "wosai_store_id": "00261081001200200015837",
    "client_sn": "12345701",
    "subject": "测试1分钱",
    "payway": "1",
    "sub_payway": "3",
    "total_amount": "1"
  }
  EOF

  {"result_code":"200","biz_response":{"result_code":"FAIL","error_code":"UPAY_PROVIDER_STATUS","error_message":"支付通道负荷过高。部分交易请求会被随机拒绝。, provider=1"}}
  ```

* 打开关闭优惠立减红包核销活动服务

```
# 打开
$ curl -X POST -H "Content-Type: application/json" http://localhost:9966/upay-gateway/upay/v2/updateFlags -d @- <<EOF
  {"flag_apply_trade_coprocessor":true}
EOF
# 关闭
$ curl -X POST -H "Content-Type: application/json" http://localhost:9966/upay-gateway/upay/v2/updateFlags -d @- <<EOF
  {"flag_apply_trade_coprocessor":false}
EOF

```

* 打开关闭威富通退款验证逻辑

```
# 打开
$ curl -X POST -H "Content-Type: application/json" http://localhost:9966/upay-gateway/upay/admin -d @- <<EOF
  {"merchant_balance_split_enable":true}
EOF
# 关闭
$ curl -X POST -H "Content-Type: application/json" http://localhost:9966/upay-gateway/upay/admin -d @- <<EOF
  {"merchant_balance_split_enable":false}
EOF

```
