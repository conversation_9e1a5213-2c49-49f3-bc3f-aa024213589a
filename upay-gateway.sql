CREATE DATABASE `upay` /*!40100 DEFAULT CHARACTER SET utf8 */;

DROP TABLE IF EXISTS  `log`;
CREATE TABLE `log` (
  `id` varchar(37) NOT NULL COMMENT '自增长ID',
  `type` int(11) NOT NULL COMMENT 'type字段决定payload如何解析',
  `payload` blob COMMENT 'payload存放以JSON格式编码的系统事件，例如订单成功支付。',
  `processed` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已经处理（下游业务逻辑完成运算并更新数据或者同步到持久化的队列）',
  `partition` int(10) unsigned NOT NULL COMMENT '分区ID(0-999)。多线程方式处理日志记录的时候，防止同一条记录被重复处理。',
  `action_id` varchar(37) DEFAULT NULL COMMENT '外部业务id 比如提现记录id',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `log_processed` (`processed`),
  KEY `log_ctime` (`ctime`),
  KEY `log_action_id` (`action_id`)
) ENGINE=InnoDB  COMMENT='日志记录用于驱动下游逻辑（余额更新）和外部系统的数据同步（积分）。';


DROP TABLE IF EXISTS  `event_log`;
CREATE TABLE `event_log` (
  `id` varchar(37) NOT NULL COMMENT '自增长ID',
  `merchant_id` varchar(37) DEFAULT NULL COMMENT '商户id',
  `type` int(11) NOT NULL COMMENT 'type字段决定payload如何解析',
  `payload` blob COMMENT 'payload存放以JSON格式编码的系统事件，例如订单成功支付。',
  `processed` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已经处理（下游业务逻辑完成运算并更新数据或者同步到持久化的队列）',
  `partition` int(10) unsigned NOT NULL COMMENT '分区ID(0-999)。多线程方式处理日志记录的时候，防止同一条记录被重复处理。',
  `action_id` varchar(37) DEFAULT NULL COMMENT '外部业务id 比如提现记录id',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `log_processed` (`processed`),
  KEY `log_ctime` (`ctime`),
  KEY `log_merchant_id_ctime` (`merchant_id`, `ctime`),
  KEY `log_action_id` (`action_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='日志记录用于驱动下游逻辑（余额更新）和外部系统的数据同步（积分）。'


DROP TABLE IF EXISTS  `order`;
CREATE TABLE `order` (
  `id` varchar(37) NOT NULL COMMENT 'UUID',
  `sn` varchar(32) NOT NULL COMMENT '按照规则生成的订单号。订单号sn等于支付流水的流水号。',
  `client_sn` varchar(32) DEFAULT NULL COMMENT '商户订单号',
  `subject` varchar(64) NOT NULL COMMENT '订单标题',
  `body` varchar(255) DEFAULT NULL COMMENT '订单详情',
  `items` blob COMMENT '明细（JSON格式）',
  `net_items` blob COMMENT '如果退款是针对商品的，这个字段记录扣除退掉的商品后的明细。',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态 \n0: CREATED\n1200: PAID\n1500: PAY_ERROR\n2200: REFUNDED\n2201: PARTIAL_REFUNDED\n2500: REFUND_ERROR\n',
  `tcp_modified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单是否触发trade-coprocessor处理逻辑',
  `original_total` bigint(20) DEFAULT NULL,
  `net_original` bigint(20) DEFAULT NULL COMMENT '原始总金额',
  `effective_total` bigint(20) NOT NULL COMMENT '向支付通道请求的支付总金额 BIGINT (trade-coprocessor可能会返回不等于original_total的值）',
  `net_effective` bigint(20) DEFAULT NULL COMMENT '扣除退款后的向支付通道请求的支付金额的净值 ',
  `total_discount` bigint(20) DEFAULT NULL COMMENT '总金额中的折扣部分，包括商户补贴和服务商补贴',
  `net_discount` bigint(20) DEFAULT NULL COMMENT '如果没有退款操作，net_discount 等于total_discount。否则为退款之后剩余的折扣。有些服务商规定折扣部分优先退。',
  `buyer_uid` varchar(45) DEFAULT NULL COMMENT '付款人在支付服务商的用户ID',
  `buyer_login` varchar(45) DEFAULT NULL COMMENT '付款人在支付服务商登录帐号',
  `merchant_id` varchar(37) NOT NULL COMMENT '商户ID 商户记录UUID',
  `store_id` varchar(37) NOT NULL COMMENT '商户ID 商户记录UUID',
  `terminal_id` varchar(37) DEFAULT NULL COMMENT '终端ID 终端记录的UUID',
  `operator` varchar(45) DEFAULT NULL COMMENT '原始支付交易（不管撤单、退款、补单的交易）的操作员（姓名或其它自定义ID）',
  `provider` int(11) NULL DEFAULT NULL COMMENT '支付通道 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉',
  `payway` int(11) DEFAULT NULL COMMENT '支付服务商\n1: alipay\n3: weixin\n4: baifubao\n5: jdwallet\n',
  `sub_payway` int(11) DEFAULT NULL COMMENT '支付方式：\n1: BARCODE\n2: QRCODE\n3: WAP',
  `trade_no` varchar(128) DEFAULT NULL COMMENT '服务商返回的交易凭证号',
  `reflect` blob COMMENT '商户上传的附加字段，保存在订单中。终端查询的时候原样返回。',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL,
  `nfc_card` varchar(30) DEFAULT NULL COMMENT 'nfc交易，银行卡号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  UNIQUE KEY `idx_sn` (`sn`),
  UNIQUE KEY `idx_store_id_client_sn` (`store_id`,`client_sn`),
  KEY `idx_operator_id` (`operator`),
  KEY `nfc_card` (`nfc_card`),
  KEY `idx_ctime_payway_sub_payway_status` (`ctime`,`payway`,`sub_payway`,`status`),
  KEY `idx_merchant_id_ctime_status` (`merchant_id`,`ctime`,`status`),
  KEY `idx_store_id_ctime_status` (`store_id`,`ctime`,`status`),
  KEY `idx_ctime_status` (`ctime`,`status`)
) ENGINE=InnoDB  COMMENT='订单';

DROP TABLE IF EXISTS  `transaction`;
CREATE TABLE `transaction` (
  `id` varchar(37) NOT NULL,
  `tsn` varchar(32) NOT NULL COMMENT '交易流水号。',
  `client_tsn` varchar(64) NOT NULL COMMENT '商户的流水号。支付流水的client_tsn等于订单的client_sn。退款流水的client_tsn等于client_sn加商户的退款请求编号。',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '交易类型\n30: 付款\n10: 取消\n11: 退款\n',
  `subject` varchar(64) DEFAULT NULL COMMENT '标题',
  `body` varchar(255) DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '状态 CREATED, IN_PROG, SUCESS, UNKNOWN, ERROR_RECOVERY, ERROR_CANCELED, ERROR_UNKNOWN',
  `effective_amount` bigint(20) DEFAULT NULL COMMENT '向支付通道请求的金额 BIGINT',
  `original_amount` bigint(20) DEFAULT NULL,
  `paid_amount` bigint(20) DEFAULT NULL COMMENT '消费者实际支付金额',
  `received_amount` bigint(20) DEFAULT NULL COMMENT '商户实际收款金额',
  `items` blob COMMENT '如果type是支付，这个字段记录购买的商品明细。如果type是退款，这个字段记录退掉的商品明细。这个字段可以用于统计一段时间内商品的净销量。',
  `buyer_uid` varchar(45) DEFAULT NULL COMMENT '付款人在支付服务商的用户ID',
  `buyer_login` varchar(45) DEFAULT NULL COMMENT '付款人在支付服务商的登录账号',
  `merchant_id` varchar(37) DEFAULT NULL,
  `store_id` varchar(37) NOT NULL COMMENT '商户ID 商户记录UUID',
  `terminal_id` varchar(37) DEFAULT NULL COMMENT '终端ID 终端记录UUID',
  `operator` varchar(45) DEFAULT NULL COMMENT '操作员姓名或其它自定义ID',
  `order_sn` varchar(32) DEFAULT NULL COMMENT '原始订单号',
  `order_id` varchar(37) DEFAULT NULL COMMENT '原始订单ID（uuid）',
  `provider` int(11) NULL DEFAULT NULL COMMENT '支付通道 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉',
  `payway` int(10) unsigned DEFAULT NULL COMMENT '支付服务商\n1: alipay\n3: weixin\n4: baifubao\n5: jdwallet\n',
  `sub_payway` int(10) unsigned DEFAULT NULL COMMENT '支付方式\n1: BARCODE\n2: QRCODE\n3: WAP',
  `trade_no` varchar(128) DEFAULT NULL COMMENT '服务商返回的交易凭证号',
  `product_flag` varchar(45) DEFAULT NULL COMMENT '产品来源：APP、SDK、POS',
  `extra_params` blob COMMENT '可选参数，包括\noperator － 操作员\npoi － 发生地点\nnotify_url － 回调URL\nremark － 备注\nbarcode - 条码\n',
  `extra_out_fields` blob COMMENT '可选的交易流水的返回字段（例如qrcode)',
  `extended_params` blob COMMENT '透传到支付通道的参数(operator, goods_details)，由商户和支付通道约定，我们不做解析',
  `reflect` blob COMMENT '商户上传的附加字段，保存在订单中。终端查询的时候原样返回。',
  `config_snapshot` blob COMMENT '配置参数快照，包括\nfee_rate 费率\nliquidation_next_day 是否二清\nweixin_trade_params\n     sub_mch_id 微信子商户号\n     goods_tag 参与优惠活动标识\nalipay_v1_trade_params\n     partner 收款商户\n     app_key\nalipay_v2_trade_params\n     app_id 收款商户\n     private_key\n     auth_token\nalipay_wap_tr /* comment truncated */ /*ade_params\n     partner\n     app_key\n     app_id\n     private_key\n     app_auth_token\nweixin_wap_trade_params\n*/',
  `finish_time` bigint(20) DEFAULT NULL COMMENT '交易完成时间（收钱吧系统时间）',
  `channel_finish_time` bigint(20) DEFAULT NULL COMMENT '交易实际完成时间（从支付通道获得）',
  `biz_error_code` blob COMMENT '业务错误码。收钱吧统一定义的业务错误码。',
  `provider_error_info` blob COMMENT '调用支付通道返回的错误信息\nprotocol_error_code  接入错误码\nsystem_error_code 系统错误码\nnetwork_error_message 网络异常消息文本\nbiz_error_code 业务错误码\nbiz_error_message 业务错误消息文本\n',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL,
  `nfc_card` varchar(30) DEFAULT NULL COMMENT 'nfc交易，银行卡号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  UNIQUE KEY `idx_tsn` (`tsn`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_operator_id` (`operator`),
  KEY `transaction_order_sn` (`order_sn`),
  KEY `transaction_ctime` (`ctime`),
  KEY `nfc_card` (`nfc_card`),
  KEY `idx_client_tsn` (`client_tsn`),
  KEY `idx_ctime_payway_sub_payway_status` (`ctime`,`payway`,`sub_payway`,`status`),
  KEY `idx_merchant_id_ctime_status` (`merchant_id`,`ctime`,`status`),
  KEY `idx_store_id_ctime_status` (`store_id`,`ctime`,`status`),
  KEY `idx_ctime_status` (`ctime`,`status`)
) ENGINE=InnoDB  COMMENT='交易流水';

DROP TABLE IF EXISTS  `wallet`;
CREATE TABLE `wallet` (
  `id` varchar(37) NOT NULL COMMENT '商户UUID',
  `balance` bigint(20) NOT NULL DEFAULT '0' COMMENT '余额（分）',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB ;

DROP TABLE IF EXISTS  `wallet_change`;
CREATE TABLE `wallet_change` (
  `id` varchar(37) NOT NULL COMMENT 'ID',
  `type` int(11) unsigned NOT NULL COMMENT '变动类别 1:收款 11:退款 12：转出 2：转入 13:提现 3:提现退回 14:手续费扣减 4：手续费退回',
  `amount` int(11) NOT NULL COMMENT '变动金额为正',
  `sign` smallint(6) NOT NULL COMMENT '1: 正向金额变更（余额增加）-1: 反向金额变更（余额减少）',
  `balance` int(11) NOT NULL COMMENT '变更后余额',
  `remark` text COMMENT '备注（操作员添加的备忘文字记录）',
  `detail` blob COMMENT 'JSON编码的提现记录详情，展示的时候可以避免关联交易流水或提现记录',
  `merchant_id` varchar(37) NOT NULL COMMENT '商户id',
  `action_id` varchar(37) DEFAULT NULL COMMENT '产生变更记录的动作id,比如提现id,订单id',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `wallet_change_index_merchant_id` (`merchant_id`)
) ENGINE=InnoDB  COMMENT='余额变动记录';

DROP TABLE IF EXISTS  `system_config`;
CREATE TABLE `system_config` (
  `id` varchar(37)  NOT NULL COMMENT 'UUID',
  `name` varchar(64) NOT NULL COMMENT '名字',
  `desc` varchar(100) NOT NULL COMMENT '描述',
  `content` blob COMMENT '内容（JSON，非JSON）',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB  COMMENT='系统配置表,存放系统的元数据，参数等';

/* VIEWS */;
DROP VIEW IF EXISTS `order_view`;
CREATE VIEW `order_view` AS select `order`.`id` AS `id`,`order`.`sn` AS `sn`,`order`.`client_sn` AS `client_sn`,`order`.`subject` AS `subject`,`order`.`body` AS `body`,`order`.`items` AS `items`,`order`.`net_items` AS `net_items`,`order`.`status` AS `status`,`order`.`tcp_modified` AS `tcp_modified`,`order`.`original_total` AS `original_total`,`order`.`net_original` AS `net_original`,`order`.`effective_total` AS `effective_total`,`order`.`net_effective` AS `net_effective`,`order`.`total_discount` AS `total_discount`,`order`.`net_discount` AS `net_discount`,`order`.`buyer_uid` AS `buyer_uid`,`order`.`buyer_login` AS `buyer_login`,`order`.`merchant_id` AS `merchant_id`,`order`.`store_id` AS `store_id`,`order`.`terminal_id` AS `terminal_id`,`order`.`operator` AS `operator`,`order`.`payway` AS `payway`,`order`.`sub_payway` AS `sub_payway`,`order`.`trade_no` AS `trade_no`,`order`.`reflect` AS `reflect`,`order`.`ctime` AS `ctime`,`order`.`mtime` AS `mtime`,`order`.`deleted` AS `deleted`,`order`.`version` AS `version`,`order`.`nfc_card` AS `nfc_card` from `order`;

